# 项目基本信息
name: qubic_exchange
description: "Qubic交易所移动端应用"

# 防止意外发布到pub.dev，私有项目建议保留此设置
publish_to: 'none'

# 版本号定义：主版本.次版本.修订版本+构建号
version: 1.0.0+1

# 环境要求
environment:
  sdk: ^3.7.2

# 项目依赖包
dependencies:
  flutter:
    sdk: flutter
  # Flutter官方国际化支持
  flutter_localizations:
    sdk: flutter

  # iOS风格图标字体包
  cupertino_icons: ^1.0.8
  # 状态管理
  provider: ^6.1.2
  # 本地存储
  shared_preferences: ^2.2.3
  # 网络请求
  http: ^1.4.0
  # Lottie动画
  lottie: ^3.3.1
  # 动画
  flutter_animate: ^4.5.2
  # 骨架屏
  shimmer: ^3.0.0
  # 轮播图
  carousel_slider: ^5.1.1
  # 国际化和数字格式化
  intl: ^0.20.2
  # 图标字体
  remixicon: ^1.4.1
  # 滑块
  syncfusion_flutter_sliders: ^30.1.38
  # Syncfusion核心包
  syncfusion_flutter_core: ^30.1.38
  # Toast
  toastification: ^3.0.3
  # 刷新
  easy_refresh: ^3.4.0
  # 扩展嵌套滚动视图
  extended_nested_scroll_view: ^6.0.1
  # WebView
  webview_flutter: ^4.13.0
  # 验证码输入
  pinput: ^5.0.1
  # 扩展复制
  copy_with_extension: ^6.0.1
  # 数字格式化
  flexi_formatter: ^1.5.0  
  # JSON序列化
  json_annotation: ^4.9.0
  # 大数字处理
  decimal: ^2.3.3
  # WebSocket
  web_socket_channel: ^2.4.5
  # 网络请求
  dio: ^5.4.3
  # ToolTip 弹窗
  super_tooltip: ^2.1.0
  # 图表
  syncfusion_flutter_charts: ^30.1.40
  
  #极验验证
  gt4_flutter_plugin:
    git:
      url: https://github.com/GeeTeam/gt4_flutter_plugin.git
      ref: master
  # Google登录
  google_sign_in: ^6.2.1
  # Apple登录
  sign_in_with_apple: ^6.1.1
  # Hive数据库
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # 二进制数据解析
  msgpack_dart: ^1.0.1

# 开发依赖包
dev_dependencies:
  flutter_test:
    sdk: flutter

  # Flutter代码规范检查工具
  # 规则配置在analysis_options.yaml文件中
  flutter_lints: ^5.0.0

  # Hive代码生成
  hive_generator: ^2.0.1
  build_runner: ^2.4.7

# Flutter特定配置
flutter:
  # 启用Material Design图标字体
  uses-material-design: true

  # 启用代码生成（用于l10n国际化）
  generate: true

  # 资源文件配置
  assets:
    - assets/images/
    - assets/images/base/
    - assets/images/okx/
    - assets/crypto/
    - assets/lottie/
    - assets/lottie/main_tab/
    - assets/fonts/

  # 自定义字体配置
  fonts:
    - family: Switzer
      fonts:
        - asset: assets/fonts/Switzer-Regular.ttf
          weight: 400
        - asset: assets/fonts/Switzer-Medium.ttf
          weight: 600
        - asset: assets/fonts/Switzer-SemiBold.ttf
          weight: 800
        - asset: assets/fonts/Switzer-Bold.ttf
          weight: 900

    - family: IBMPlexSans
      fonts:
        - asset: assets/fonts/IBMPlexSans-Regular.ttf
          weight: 400
        - asset: assets/fonts/IBMPlexSans-Medium.ttf
          weight: 500
        - asset: assets/fonts/IBMPlexSans-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/IBMPlexSans-Bold.ttf
          weight: 700
