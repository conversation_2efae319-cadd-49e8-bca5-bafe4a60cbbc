# 🚀 CPX Exchange Mobile

<div align="center">

![Flutter](https://img.shields.io/badge/Flutter-3.7.2+-02569B?style=for-the-badge&logo=flutter&logoColor=white)
![Dart](https://img.shields.io/badge/Dart-3.0+-0175C2?style=for-the-badge&logo=dart&logoColor=white)
![Platform](https://img.shields.io/badge/Platform-iOS%20%7C%20Android-lightgrey?style=for-the-badge)
![License](https://img.shields.io/badge/License-Private-red?style=for-the-badge)

**🌟 专业级加密货币交易所移动端应用**

*基于Flutter构建的高性能、多主题、国际化交易平台*

[📱 功能特性](#-功能特性) • [🏗️ 项目架构](#️-项目架构) • [🚀 快速开始](#-快速开始) • [📖 开发文档](#-开发文档)

</div>

---

## 📱 功能特性

### 💹 核心交易功能
- **🔥 现货交易** - 支持限价单、市价单等多种订单类型
- **⚡ 合约交易** - 杠杆交易和期货合约
- **🔄 跟单交易** - 智能跟单和策略复制
- **💱 杠杆交易** - 保证金交易和风险管理
- **⚡ 闪兑交易** - 快速币币兑换
- **🔗 链上交易** - 去中心化交易支持

### 📊 市场数据
- **📈 实时行情** - 实时价格更新和深度数据
- **📋 市场列表** - 全面的加密货币市场信息
- **⭐ 自选管理** - 个性化收藏和关注列表
- **🔍 智能搜索** - 快速查找交易对和币种
- **📊 K线图表** - 专业级图表分析工具
- **💡 市场洞察** - 专业分析和投资机会

### 💰 资产管理
- **💳 账户总览** - 实时资产统计和分析
- **📊 持仓管理** - 详细的持仓信息和盈亏
- **📋 订单管理** - 当前委托和历史订单
- **💸 资金流水** - 完整的资金变动记录
- **🔄 资产转换** - 多币种资产管理

### 👤 用户系统
- **🔐 安全登录** - 主账户和子账户登录支持
- **📝 用户注册** - 完整的注册流程和验证
- **🔒 密码管理** - 安全的密码重置功能
- **🌍 国际化** - 多语言和地区支持
- **⚙️ 个人设置** - 个性化配置和偏好设置

### 🎨 主题系统
- **🔄 动态主题** - 支持多种交易所主题模板（Base、OKX等）
- **🌙 深色模式** - 完整的深色/浅色模式支持
- **⚙️ 远程配置** - 后端驱动的主题配置管理
- **💾 智能缓存** - 24小时配置缓存机制
- **🎯 权限控制** - 灵活的主题切换权限管理

### 🛠️ 技术特性
- **🏗️ 模块化架构** - 清晰的分层架构，易于维护和扩展
- **🎯 状态管理** - 使用Provider进行状态管理
- **🌐 网络请求** - 完整的HTTP客户端和错误处理
- **💾 本地存储** - SharedPreferences本地数据持久化
- **🔧 配置管理** - 统一的应用配置管理系统
- **🌍 国际化支持** - 完整的多语言本地化系统

---

## 🏗️ 项目架构

### 📁 目录结构

```
CPX-EXCHANGE-MOBILE/
├── 📱 android/                    # Android平台配置
├── 🍎 ios/                        # iOS平台配置
├── 🎨 assets/                     # 资源文件
│   ├── fonts/                     # 字体文件 (Switzer, IBMPlexSans)
│   ├── icons/                     # 图标资源
│   ├── images/                    # 图片资源 (bitget/, okx/)
│   ├── crypto/                    # 加密货币图标
│   ├── lottie/                    # Lottie动画文件
│   └── languages/                 # 语言资源文件
├── 📚 lib/                        # 主要代码目录
│   ├── 🚀 main.dart              # 应用入口文件
│   ├── 🎨 core/                  # 核心模块
│   │   ├── config/               # 配置管理
│   │   ├── constants/            # 常量定义
│   │   ├── managers/             # 核心管理器
│   │   ├── models/               # 数据模型
│   │   ├── providers/            # 状态管理
│   │   ├── storage/              # 存储服务
│   │   ├── templates/            # 主题模板系统
│   │   │   ├── base/             # 基础主题
│   │   │   └── okx/              # OKX主题
│   │   └── theme/                # 主题系统
│   │       ├── colors/           # 主题颜色
│   │       ├── constants/        # 主题常量
│   │       ├── extensions/       # 主题扩展
│   │       ├── managers/         # 主题管理器
│   │       └── styles/           # 主题样式
│   ├── 📊 data/                  # 数据层
│   ├── 🌍 localization/          # 国际化
│   ├── 📱 pages/                 # 页面组件
│   │   ├── assets/               # 资产页面
│   │   ├── auth/                 # 认证页面 (登录/注册)
│   │   ├── country_code/         # 国家代码选择
│   │   ├── home/                 # 首页
│   │   ├── main_tab/             # 主标签页
│   │   ├── market/               # 市场页面
│   │   ├── search_crypto/        # 加密货币搜索
│   │   ├── splash/               # 启动页
│   │   ├── tools/                # 工具页面
│   │   ├── trade/                # 交易页面
│   │   │   ├── spot_trade/       # 现货交易
│   │   │   ├── contract_trade/   # 合约交易
│   │   │   └── copy_order_trade/ # 跟单交易
│   │   └── wealth/               # 财富管理
│   ├── 🛣️ routes/                # 路由管理
│   ├── 🌐 services/              # 服务层
│   ├── 🛠️ utils/                 # 工具类
│   └── 🧩 widgets/               # 通用组件
│       ├── common/               # 公共组件
│       ├── dialogs/              # 对话框组件
│       └── trade_common/         # 交易通用组件
├── 🧪 test/                      # 测试文件
├── 📄 pubspec.yaml               # 项目配置文件
├── 📋 analysis_options.yaml      # 代码分析配置
└── 📖 README.md                  # 项目说明文档
```

### 🏛️ 架构设计

#### 分层架构
```
┌─────────────────────────────────────┐
│            🖥️ UI Layer             │  ← Pages & Widgets
├─────────────────────────────────────┤
│         🔄 State Management        │  ← Providers & Notifiers
├─────────────────────────────────────┤
│          🌐 Service Layer          │  ← API & Business Logic
├─────────────────────────────────────┤
│          📊 Data Layer             │  ← Models & Repository
├─────────────────────────────────────┤
│          🎨 Theme System           │  ← Dynamic Theme Management
└─────────────────────────────────────┘
```

#### 核心模块

1. **🎨 主题系统 (Theme System)**
   - 动态主题切换和管理
   - 多模板支持（Base、OKX等）
   - 深色/浅色模式切换
   - 后端配置驱动

2. **🌐 服务层 (Service Layer)**
   - 配置服务：从后端获取应用配置
   - 初始化服务：管理应用启动流程
   - 货币服务：多币种支持和汇率管理
   - 网络服务：HTTP请求和响应处理

3. **📊 数据层 (Data Layer)**
   - 数据模型定义
   - 本地存储管理
   - 缓存策略实现

4. **🧩 组件系统 (Widget System)**
   - 可复用的UI组件
   - 主题感知组件
   - 业务逻辑组件

5. **🛠️ 工具类系统 (Utils System)**
   - 应用信息获取：版本、平台、运行环境
   - 屏幕信息获取：尺寸、密度、方向、安全区域
   - 元素信息获取：Widget尺寸、位置、可见性
   - 数字格式化：货币、百分比、数量格式化

---

## 🚀 快速开始

### 📋 环境要求

- **Flutter SDK**: 3.7.2+
- **Dart SDK**: 3.0+
- **iOS**: iOS 12.0+
- **Android**: API Level 21+ (Android 5.0+)

### 🛠️ 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/herotrade/CPX-EXCHANGE-MOBILE.git
   cd CPX-EXCHANGE-MOBILE
   ```

2. **安装依赖**
   ```bash
   flutter pub get
   ```

3. **配置环境**
   ```bash
   # iOS (需要macOS)
   cd ios && pod install && cd ..

   # Android
   flutter doctor  # 检查Android环境
   ```

4. **运行应用**
   ```bash
   # 调试模式
   flutter run

   # 发布模式
   flutter run --release

   # 指定设备
   flutter run -d <device_id>
   ```

### 🔧 开发配置

#### 主题配置
应用启动时会从后端获取主题配置，开发模式下使用模拟数据：

```dart
// 在 lib/core/config/app_config_models.dart 中修改默认配置
factory AppConfigModel.defaultConfig() {
  return AppConfigModel(
    themeConfig: ThemeConfig.defaultConfig(),
    apiBaseUrl: 'https://api.cpx-exchange.com',
    appVersion: '1.0.0',
    debugMode: false,
  );
}
```

#### 后端API格式
```json
{
  "data": {
    "themeConfig": {
      "templateName": "base",              // 主题模板名称
      "defaultDarkMode": false,           // 默认深色模式
      "allowThemeSwitch": true,           // 允许主题切换
      "allowDarkModeSwitch": true         // 允许深色模式切换
    },
    "apiBaseUrl": "https://api.cpx-exchange.com",
    "appVersion": "1.0.0",
    "debugMode": false
  }
}
```

---

## 📖 开发文档

### 🎨 主题系统使用

#### 基础用法
```dart
import 'package:qubic_exchange/core/index.dart';

// 在Widget中使用主题颜色
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = ThemeColors.instance;
    
    return Container(
      color: theme.surface(context),
      child: Text(
        'Hello World',
        style: TextStyle(color: theme.textPrimary(context)),
      ),
    );
  }
}
```

#### 主题切换
```dart
// 获取主题管理器
final themeProvider = ThemeProvider.instance;

// 切换主题模式
await themeProvider.setLightMode();    // 浅色模式
await themeProvider.setDarkMode();     // 深色模式
await themeProvider.setSystemMode();   // 跟随系统
await themeProvider.toggleThemeMode(); // 切换模式

// 切换主题模板
await themeProvider.setTemplate('okx');  // 切换到OKX主题
await themeProvider.setTemplate('base'); // 切换到基础主题
```

### 🛠️ 工具类使用

#### 应用信息工具类
```dart
import 'package:qubic_exchange/utils/index.dart';

// 获取应用基本信息
final appName = AppInfoUtil.appName;           // 'Qubic Exchange'
final appVersion = AppInfoUtil.fullVersion;    // '1.0.0+1'
final platform = AppInfoUtil.platformName;    // 'iOS' 或 'Android'
final isDebug = AppInfoUtil.isDebugMode;       // true/false

// 获取平台信息
final isIOS = AppInfoUtil.isIOS;
final isAndroid = AppInfoUtil.isAndroid;
final isMobile = AppInfoUtil.isMobile;

// 获取运行时长
final runtime = AppInfoUtil.appRuntimeFormatted; // '5m 30s'

// 获取完整应用信息
final appInfo = AppInfoUtil.getAppInfo();

// 打印应用信息（调试用）
AppInfoUtil.printAppInfo();

// 复制应用信息到剪贴板
await AppInfoUtil.copyAppInfoToClipboard();
```

#### 屏幕信息工具类
```dart
// 获取屏幕基本信息
final screenSize = ScreenUtil.screenSize;         // Size(width, height)
final screenWidth = ScreenUtil.screenWidth;       // 屏幕宽度
final screenHeight = ScreenUtil.screenHeight;     // 屏幕高度
final pixelRatio = ScreenUtil.pixelRatio;         // 像素密度

// 获取安全区域信息
final safeAreaTop = ScreenUtil.safeAreaTop;       // 顶部安全区域
final safeAreaBottom = ScreenUtil.safeAreaBottom; // 底部安全区域
final safeAreaLeft = ScreenUtil.safeAreaLeft;     // 左侧安全区域
final safeAreaRight = ScreenUtil.safeAreaRight;   // 右侧安全区域

// 获取状态栏和导航栏信息
final statusBarHeight = ScreenUtil.statusBarHeight;
final navigationBarHeight = ScreenUtil.navigationBarHeight;

// 获取屏幕方向
final orientation = ScreenUtil.orientation;       // Orientation.portrait/landscape
final isPortrait = ScreenUtil.isPortrait;         // 是否竖屏
final isLandscape = ScreenUtil.isLandscape;       // 是否横屏

// 获取完整屏幕信息
final screenInfo = ScreenUtil.getScreenInfo();

// 打印屏幕信息（调试用）
ScreenUtil.printScreenInfo();
```

#### Widget信息工具类
```dart
// 创建GlobalKey
final GlobalKey widgetKey = GlobalKey();

// 在Widget中使用
Widget build(BuildContext context) {
  return Container(
    key: widgetKey,
    child: Text('Hello'),
  );
}

// 获取Widget尺寸
final size = WidgetUtil.getWidgetSize(widgetKey);

// 获取Widget位置
final position = WidgetUtil.getWidgetPosition(widgetKey);

// 检查Widget是否可见
final isVisible = WidgetUtil.isWidgetVisible(widgetKey, context);

// 获取完整Widget信息
final widgetInfo = WidgetUtil.getWidgetInfo(widgetKey, context);

// 打印Widget信息（调试用）
WidgetUtil.printWidgetInfo(widgetKey, context, widgetName: 'MyWidget');
```

### 🌐 服务层开发

#### 配置服务
```dart
import 'package:qubic_exchange/services/index.dart';

// 获取配置服务实例
final configService = ConfigService.instance;

// 初始化配置
await configService.initializeConfig();

// 获取当前配置
final config = configService.currentConfig;

// 获取主题配置
final themeConfig = configService.themeConfig;

// 刷新配置
await configService.refreshConfig();

// 清除缓存
await configService.clearCache();
```

#### 应用初始化服务
```dart
// 获取初始化服务实例
final initService = AppInitializationService.instance;

// 初始化应用
await initService.initializeApp();

// 重新初始化主题
await initService.reinitializeTheme();

// 获取初始化状态
final isInitialized = initService.isInitialized;
final appConfig = initService.appConfig;

// 打印初始化信息
initService.printInitializationInfo();
```

#### 货币服务
```dart
// 获取货币服务实例
final currencyService = CurrencyService.instance;

// 初始化货币服务
await currencyService.initialize();

// 获取当前货币
final currentCurrency = currencyService.currentCurrency;

// 切换货币
await currencyService.changeCurrency('USD');

// 获取支持的货币列表
final supportedCurrencies = currencyService.supportedCurrencies;

// 格式化金额
final formattedAmount = currencyService.formatAmount(1234.56);
```

### 📱 页面开发

#### 页面结构
```dart
import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class MyPage extends StatefulWidget {
  const MyPage({super.key});

  @override
  State<MyPage> createState() => _MyPageState();
}

class _MyPageState extends State<MyPage> {
  @override
  Widget build(BuildContext context) {
    final theme = ThemeColors.instance;

    return Scaffold(
      backgroundColor: theme.surface(context),
      appBar: CustomAppBar(
        title: '页面标题',
        backgroundColor: theme.surface(context),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Container(
      // 页面内容
    );
  }
}
```

#### 路由导航
```dart
import 'package:qubic_exchange/routes/index.dart';

// 导航到指定页面
NavigationService.pushNamed(AppRoutes.login);

// 带参数导航
NavigationService.pushNamed(
  AppRoutes.searchCrypto,
  arguments: {'symbol': 'BTC'},
);

// 替换当前页面
NavigationService.pushReplacementNamed(AppRoutes.mainTabbarScreen);

// 返回上一页
NavigationService.pop();

// 返回到指定页面
NavigationService.popUntil(AppRoutes.mainTabbarScreen);
```

### 🧩 组件开发

#### 自定义组件
```dart
import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ThemeColors.instance;

    return ButtonWidget(
      text: text,
      onPressed: isLoading ? null : onPressed,
      backgroundColor: theme.primary(context),
      textColor: theme.buttonText(context),
      isLoading: isLoading,
    );
  }
}
```

#### 使用通用组件
```dart
// 按钮组件
ButtonWidget(
  text: '确认',
  onPressed: () {},
  backgroundColor: theme.primary(context),
)

// 输入框组件
InputWidget(
  hintText: '请输入内容',
  controller: _controller,
  onChanged: (value) {},
)

// 标签栏组件
TabBarWidget(
  tabs: ['标签1', '标签2', '标签3'],
  onTabChanged: (index) {},
)

// 底部弹窗组件
BottomSheetWidget.show(
  context: context,
  title: '选择选项',
  child: _buildContent(),
);
```

---

## 🔧 构建和部署

### 📱 Android构建

```bash
# 构建APK
flutter build apk --release

# 构建App Bundle
flutter build appbundle --release

# 构建指定架构
flutter build apk --target-platform android-arm64 --release
```

### 🍎 iOS构建

```bash
# 构建iOS应用
flutter build ios --release

# 构建IPA文件
flutter build ipa --release

# 指定配置文件
flutter build ios --release --flavor production
```

### 🚀 发布配置

#### Android发布配置
1. 在 `android/app/build.gradle` 中配置签名
2. 创建 `android/key.properties` 文件
3. 配置混淆规则

#### iOS发布配置
1. 在Xcode中配置证书和描述文件
2. 设置App Store Connect配置
3. 配置Info.plist权限

---

## 🧪 测试

### 单元测试
```bash
# 运行所有测试
flutter test

# 运行指定测试文件
flutter test test/widget_test.dart

# 生成测试覆盖率报告
flutter test --coverage
```

### 集成测试
```bash
# 运行集成测试
flutter drive --target=test_driver/app.dart
```

---

## 🐛 故障排除

### 常见问题

#### 1. 主题切换不生效
```bash
# 清理缓存
flutter clean
flutter pub get

# 重启应用
flutter run --hot-restart
```

#### 2. 配置加载失败
- 检查网络连接
- 验证API端点地址
- 查看控制台错误日志
- 清除应用缓存重试

#### 3. 构建失败
```bash
# 清理构建缓存
flutter clean

# 重新获取依赖
flutter pub get

# 重新构建
flutter build apk
```

### 调试工具

#### 主题调试
```dart
// 打印主题信息
ThemeProvider.instance.printThemeInfo();

// 打印配置信息
ConfigService.instance.printConfigInfo();

// 打印初始化状态
AppInitializationService.instance.printInitializationInfo();
```

#### 网络调试
```bash
# 启用网络日志
flutter run --verbose

# 使用代理工具
# Charles、Fiddler等抓包工具
```

---

## 🤝 贡献指南

### 开发流程

1. **Fork项目** - 创建个人分支
2. **创建功能分支** - `git checkout -b feature/your-feature`
3. **提交代码** - 遵循提交规范
4. **推送分支** - `git push origin feature/your-feature`
5. **创建PR** - 提交Pull Request

### 代码规范

#### Flutter代码规范
- 遵循 [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- 使用 `flutter analyze` 检查代码质量
- 使用 `dart format` 格式化代码
- 添加必要的注释和文档

#### 文件命名规范
```
# 文件命名使用下划线分隔
my_widget.dart
user_service.dart
app_constants.dart

# 类名使用大驼峰命名
class MyWidget extends StatelessWidget {}
class UserService {}

# 变量和方法使用小驼峰命名
String userName = '';
void getUserInfo() {}
```

#### 提交规范
```bash
# 功能添加
git commit -m "feat: 添加新的主题模板支持"

# 问题修复
git commit -m "fix: 修复主题切换时的内存泄漏"

# 文档更新
git commit -m "docs: 更新README文档"

# 样式调整
git commit -m "style: 调整代码格式"

# 重构代码
git commit -m "refactor: 重构主题管理器"

# 性能优化
git commit -m "perf: 优化列表滚动性能"

# 测试相关
git commit -m "test: 添加主题切换单元测试"
```

### 代码审查

#### 审查要点
- **功能完整性** - 确保功能按预期工作
- **代码质量** - 遵循编码规范和最佳实践
- **性能考虑** - 避免不必要的重建和内存泄漏
- **测试覆盖** - 包含必要的单元测试和集成测试
- **文档完整** - 添加适当的注释和文档

#### 审查流程
1. 自动化检查通过（CI/CD）
2. 至少一位团队成员审查
3. 解决所有审查意见
4. 合并到主分支

---

## 📊 性能优化

### 应用性能

#### 启动优化
- 延迟加载非关键模块
- 优化资源文件大小
- 使用启动画面减少感知延迟

#### 内存优化
- 及时释放不需要的资源
- 使用对象池减少GC压力
- 监控内存使用情况

#### 渲染优化
- 减少不必要的Widget重建
- 使用const构造函数
- 合理使用RepaintBoundary

### 网络优化

#### 请求优化
- 实现请求缓存机制
- 使用连接池复用连接
- 压缩请求和响应数据

#### 数据优化
- 分页加载大量数据
- 预加载关键数据
- 实现离线缓存

---

## 📈 监控和分析

### 性能监控

#### 关键指标
- 应用启动时间
- 页面加载时间
- 内存使用情况
- 网络请求延迟
- 崩溃率和错误率

#### 监控工具
- Firebase Performance Monitoring
- Flutter Inspector
- Dart DevTools

### 用户行为分析

#### 分析指标
- 用户活跃度
- 功能使用率
- 用户路径分析
- 转化率统计

---

## 🔒 安全考虑

### 数据安全

#### 敏感数据保护
- 使用加密存储敏感信息
- 避免在日志中输出敏感数据
- 实现安全的网络传输

#### 认证和授权
- 实现安全的登录机制
- 使用JWT令牌管理会话
- 定期刷新访问令牌

### 代码安全

#### 代码混淆
- 启用代码混淆保护
- 移除调试信息
- 保护关键业务逻辑

---

## 📄 许可证

本项目为私有项目，版权归 **CPX Exchange** 所有。

未经授权，禁止复制、分发或修改本项目的任何部分。

---

## 📞 联系我们

- **项目仓库**: [GitHub](https://github.com/herotrade/CPX-EXCHANGE-MOBILE.git)
- **技术支持**: [技术团队邮箱]
- **问题反馈**: [GitHub Issues](https://github.com/herotrade/CPX-EXCHANGE-MOBILE/issues)

---

## 🙏 致谢

感谢所有为本项目做出贡献的开发者和设计师。

特别感谢：
- **Flutter团队** - 提供优秀的跨平台框架
- **Material Design** - 提供设计规范和组件
- **开源社区** - 提供各种优秀的第三方包

---

<div align="center">

**🚀 让我们一起构建更好的数字资产交易体验！**

Made with ❤️ by CPX Exchange Team

</div>
