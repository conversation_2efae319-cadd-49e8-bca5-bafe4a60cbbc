import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'core/index.dart';
import 'core/managers/focus_manager.dart';
import 'l10n/index.dart';
import 'providers/auth_provider.dart';
import 'services/pages/auth/index.dart';
import 'services/pages/auth/session_manager.dart';
import 'services/core/first_launch_service.dart';
import 'services/storage/storage_service.dart';
import 'services/market/market_service.dart';
import 'routes/index.dart';

/// 应用程序入口点
///
/// 负责初始化应用程序的核心服务，包括：
/// - 语言管理器初始化
/// - 主题管理器初始化
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化核心服务
  await _initializeServices();

  runApp(const MyApp());

  // 异步初始化市场服务（不阻塞应用启动）
  _initializeMarketServiceAsync();
}

/// 初始化应用程序核心服务
Future<void> _initializeServices() async {
  try {
    _setupGlobalErrorHandling();

    // 初始化Hive数据库
    await Hive.initFlutter();

    // 初始化存储服务
    await StorageService.instance.initialize();

    await Future.wait([LanguageManager().initialize(), ThemeProvider.instance.initialize()]);
  } catch (e) {
    // 初始化失败时的处理
  }
}

/// 设置全局错误处理
void _setupGlobalErrorHandling() {
  FlutterError.onError = (FlutterErrorDetails details) {
    final errorMessage = details.exception.toString();

    // 忽略 SliverGeometry 相关错误
    final ignoredErrors = ['SliverGeometry is not valid', 'layoutExtent', 'paintExtent'];

    if (ignoredErrors.any(errorMessage.contains)) return;

    FlutterError.presentError(details);
  };
}

/// 异步初始化市场服务
///
/// 在应用启动后异步执行，不阻塞UI显示
void _initializeMarketServiceAsync() {
  Future.microtask(() async {
    try {
      debugPrint('🚀 开始异步初始化市场服务...');

      // 初始化MarketService
      await MarketService.instance.initialize();

      // 检查是否需要更新数据
      final marketService = MarketService.instance;
      final shouldUpdate = marketService.shouldUpdate(threshold: const Duration(minutes: 30));

      if (shouldUpdate) {
        // 如果币种数据为空，获取完整数据；否则执行增量更新
        if (marketService.currencyModels.isEmpty) {
          await marketService.refreshAllData();
        } else {
          // 并行执行增量更新和其他数据获取
          await Future.wait([
            marketService.updateCurrencyData(),
            marketService.fetchCurrencyCateData(),
            marketService.fetchMarketTypeData(),
            marketService.fetchMainTabData(),
          ]);
        }
      } else {
        debugPrint('✅ 数据较新，跳过更新');

        // 如果栏目或市场数据为空，仍需获取
        final futures = <Future<bool>>[];

        if (marketService.categoryModels.isEmpty) {
          futures.add(marketService.fetchCurrencyCateData());
        }

        if (marketService.marketTypeModels.isEmpty) {
          futures.add(marketService.fetchMarketTypeData());
        }

        if (marketService.mainTabModels.isEmpty) {
          futures.add(marketService.fetchMainTabData());
        }

        if (futures.isNotEmpty) {
          await Future.wait(futures);
        }
      }
    } catch (e) {
      debugPrint('❌ 市场服务异步初始化失败: $e');
      // 初始化失败不影响应用正常运行
    }
  });
}

/// 应用程序根组件
class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

/// MyApp 状态管理类
class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  String? _initialRoute;
  bool _isRouteInitialized = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _determineInitialRoute();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  /// 确定初始路由
  ///
  /// 启动逻辑：
  /// - 首次启动：进入引导页
  /// - 后续启动：无论登录状态如何都进入主页面
  Future<void> _determineInitialRoute() async {
    try {
      // 初始化首次启动检测服务
      await FirstLaunchService.instance.initialize();

      // 检查是否为首次启动
      if (FirstLaunchService.instance.isFirstLaunch) {
        // 首次启动，显示引导页
        _setInitialRoute(AppRoutes.splash);
        return;
      }

      // 非首次启动，直接进入主页面
      _setInitialRoute(AppRoutes.mainTabbarScreen);
    } catch (e) {
      // 发生错误时，默认显示启动页
      _setInitialRoute(AppRoutes.splash);
    }
  }

  /// 设置初始路由
  void _setInitialRoute(String route) {
    if (mounted) {
      setState(() {
        _initialRoute = route;
        _isRouteInitialized = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(providers: _buildProviders(), child: Consumer3<ThemeProvider, LanguageManager, AuthProvider>(builder: _buildApp));
  }

  /// 全局 AuthProvider 实例
  static final AuthProvider _globalAuthProvider = AuthProvider();

  /// 构建状态管理提供者列表
  List<ChangeNotifierProvider> _buildProviders() {
    return [
      ChangeNotifierProvider<ThemeProvider>.value(value: ThemeProvider.instance),
      ChangeNotifierProvider<LanguageManager>.value(value: LanguageManager()),
      ChangeNotifierProvider<CaptchaService>.value(value: CaptchaService()),
      ChangeNotifierProvider<AuthProvider>.value(value: _globalAuthProvider),
    ];
  }

  /// 构建应用程序主体
  Widget _buildApp(
    BuildContext context,
    ThemeProvider themeProvider,
    LanguageManager languageManager,
    AuthProvider authProvider,
    Widget? child,
  ) {
    // 初始化会话管理器
    if (themeProvider.isInitialized && _isRouteInitialized && authProvider.isInitialized) {
      SessionManager.instance.initialize(authProvider);
      debugPrint('🔧 SessionManager 已初始化');
    }
    // 检查主题提供者和路由是否已初始化
    if (!themeProvider.isInitialized || !_isRouteInitialized) {
      return MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(_isRouteInitialized ? '加载主题中...' : '初始化应用中...', style: const TextStyle(fontSize: 16)),
              ],
            ),
          ),
        ),
      );
    }

    final themeDataGenerator = ThemeDataGenerator.instance;

    return GlobalFocusWrapper(
      enableGlobalUnfocus: true,
      child: MaterialApp(
        title: 'Qubic Exchange',
        debugShowCheckedModeBanner: false,

        locale: languageManager.currentLocale,
        supportedLocales: AppLocalizations.supportedLocales,
        localizationsDelegates: AppLocalizations.localizationsDelegates,

        navigatorKey: NavigationService.navigatorKey,
        navigatorObservers: [NavigationService.routeObserver],
        onGenerateRoute: RouteGenerator.generateRoute,
        initialRoute: _initialRoute ?? AppRoutes.splash,

        theme: themeDataGenerator.generateLightTheme(themeProvider.templateName),
        darkTheme: themeDataGenerator.generateDarkTheme(themeProvider.templateName),
        themeMode: themeProvider.flutterThemeMode,

        builder:
            (context, child) => MediaQuery(
              data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1.0)),
              child: child ?? const SizedBox.shrink(),
            ),
      ),
    );
  }
}
