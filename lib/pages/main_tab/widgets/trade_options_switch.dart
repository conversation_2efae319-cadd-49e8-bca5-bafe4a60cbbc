import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

// 交易选项数据模型
class TradeOption {
  final String key;
  final String title;
  final String icon;

  const TradeOption({
    required this.key,
    required this.title,
    required this.icon,
  });
}

class TradeOptionsSwitch {
  static Future<String?> show(
    BuildContext context, {
    String? currentOption,
    Function(String)? onOptionSelected,
  }) async {
    return await showModalBottomSheet<String>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => _TradeOptionsContent(
            currentOption: currentOption,
            onOptionSelected: onOptionSelected,
          ),
    );
  }
}

class _TradeOptionsContent extends StatefulWidget {
  final String? currentOption;
  final Function(String)? onOptionSelected;

  const _TradeOptionsContent({this.currentOption, this.onOptionSelected});

  @override
  State<_TradeOptionsContent> createState() => _TradeOptionsContentState();
}

class _TradeOptionsContentState extends State<_TradeOptionsContent>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _opacityAnimation;
  late Animation<Offset> _slideAnimation;

  // 关闭按钮动画控制器
  late AnimationController _closeButtonController;
  late Animation<double> _closeButtonOpacity;
  late Animation<double> _closeButtonRotation;

  // 交易选项列表
  final List<TradeOption> _tradeOptions = [
    TradeOption(key: 'spot', title: '现货交易', icon: 'spot_menu'),
    TradeOption(key: 'contract_trade', title: '合约交易', icon: 'contract_menu'),
    TradeOption(key: 'copy_trade', title: '跟单交易', icon: 'copy_order_menu'),
  ];

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startAnimations();
  }

  // 初始化动画
  void _initAnimations() {
    // 主动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // 关闭按钮动画控制器
    _closeButtonController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _closeButtonOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _closeButtonController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _closeButtonRotation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _closeButtonController,
        curve: const Interval(0.2, 1.0, curve: Curves.elasticOut),
      ),
    );
  }

  // 开始动画
  void _startAnimations() {
    _animationController.forward();

    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _closeButtonController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _closeButtonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击空白区域关闭弹窗
        _closeWithAnimation();
      },
      child: Container(
        color: Colors.transparent,
        child: Align(
          alignment: Alignment.bottomCenter,
          child: Padding(
            padding: const EdgeInsets.only(bottom: 115), // 距离底部的间距
            child: GestureDetector(
              onTap: () {
                // 阻止事件冒泡，点击弹窗内容区域不关闭
              },
              child: AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return SlideTransition(
                    position: _slideAnimation,
                    child: Opacity(
                      opacity: _opacityAnimation.value,
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(
                          horizontal: UiConstants.spacing18,
                        ),
                        child: Stack(
                          clipBehavior: Clip.none,
                          children: [
                            // 自定义形状背景
                            CustomPaint(
                              painter: WaterdropPopupPainter(
                                color: context.templateColors.popupBackground,
                              ),
                              child: Container(
                                width: double.infinity,
                                padding: EdgeInsets.all(UiConstants.spacing14),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // 交易选项列表
                                    ..._tradeOptions.map(
                                      (option) => _buildTradeOption(option),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            // 关闭按钮 - 浮动在水滴圆形位置，延迟出现并带旋转动画
                            Positioned(
                              bottom: -45, // 位于水滴圆形部分
                              left: 0,
                              right: 0,
                              child: Center(
                                child: AnimatedBuilder(
                                  animation: _closeButtonController,
                                  builder: (context, child) {
                                    return Opacity(
                                      opacity: _closeButtonOpacity.value,
                                      child: Transform.rotate(
                                        angle:
                                            _closeButtonRotation.value *
                                            2 *
                                            3.14159,
                                        child: GestureDetector(
                                          onTap: () {
                                            _closeWithAnimation();
                                          },
                                          child: Container(
                                            width: 43,
                                            height: 43,
                                            decoration: BoxDecoration(
                                              color:
                                                  context
                                                      .templateColors
                                                      .primary,
                                              borderRadius:
                                                  BorderRadius.circular(
                                                    UiConstants
                                                        .borderRadiusCircle,
                                                  ),
                                            ),
                                            child: Icon(
                                              Icons.close,
                                              size: 24,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 带动画的关闭方法
  Future<void> _closeWithAnimation() async {
    await _animationController.reverse();
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  // 选择选项
  Future<void> _selectOption(String optionKey) async {
    widget.onOptionSelected?.call(optionKey);
    await _closeWithAnimation();
    // 移除重复的 Navigator.pop 调用，_closeWithAnimation 已经处理了弹窗关闭
  }

  // 构建交易选项项目
  Widget _buildTradeOption(TradeOption option) {
    final isSelected = option.key == widget.currentOption;

    return InkWellWidget(
      onTap: () => _selectOption(option.key),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing10,
          vertical: UiConstants.spacing14,
        ),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? context.templateColors.tabbarBackground
                  : context.templateColors.popupBackground,
          borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
        ),
        child: Row(
          children: [
            // 图标
            ThemedImage.asset(
              option.icon,
              width: 26,
              height: 26,
              followTheme: true,
            ),
            const SizedBox(width: 12),

            // 文本内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    option.title,
                    style: context.templateStyle.text.bodyLargeMedium,
                  ),
                ],
              ),
            ),

            // 选中状态指示器
            if (isSelected)
              Icon(
                RemixIcons.check_line,
                size: 20,
                color: context.templateColors.textPrimary,
              ),
          ],
        ),
      ),
    );
  }
}

class WaterdropPopupPainter extends CustomPainter {
  final Color color;
  final double borderRadius;
  final double waterdropRadius;

  WaterdropPopupPainter({
    required this.color,
    this.borderRadius = 12,
    this.waterdropRadius = 28.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final double centerX = size.width / 2;
    final double bottomY = size.height;
    final double dropCenterY = bottomY + waterdropRadius * 0.8;

    // 1. 绘制圆角矩形主体
    _drawRoundedRectangle(canvas, size, paint);

    // 2. 绘制左侧连接曲线
    _drawLeftCurve(canvas, size, paint, centerX, bottomY, dropCenterY);

    // 3. 绘制右侧连接曲线
    _drawRightCurve(canvas, size, paint, centerX, bottomY, dropCenterY);

    // 4. 绘制底部圆形
    _drawBottomCircle(canvas, paint, centerX, dropCenterY);
  }

  // 绘制圆角矩形主体
  void _drawRoundedRectangle(Canvas canvas, Size size, Paint paint) {
    final rect = RRect.fromLTRBR(
      0,
      0,
      size.width,
      size.height,
      Radius.circular(borderRadius),
    );
    canvas.drawRRect(rect, paint);
  }

  // 绘制左侧连接曲线
  void _drawLeftCurve(
    Canvas canvas,
    Size size,
    Paint paint,
    double centerX,
    double bottomY,
    double dropCenterY,
  ) {
    final path = Path();

    // 从矩形底部左侧连接点开始
    final leftConnectionX = centerX - waterdropRadius * 2.2;
    path.moveTo(leftConnectionX, bottomY);

    // 第一段三次贝塞尔曲线：从矩形底部平滑过渡到中间点（右侧的水平翻转）
    final midPointX = centerX - waterdropRadius * 1.3;
    final midPointY = bottomY + waterdropRadius * 0.35;

    path.cubicTo(
      centerX - waterdropRadius * 2.1, // 第一段控制点1X（接近起始点，保持水平）
      bottomY + waterdropRadius * -0.01, // 第一段控制点1Y（几乎水平）
      centerX - waterdropRadius * 1.5, // 第一段控制点2X（逐渐向内）
      bottomY + waterdropRadius * 0.05, // 第一段控制点2Y（轻微下降）
      midPointX, // 中间点X
      midPointY, // 中间点Y
    );

    // 第二段三次贝塞尔曲线：从中间点平滑过渡到圆形连接点（右侧的水平翻转）
    path.cubicTo(
      centerX - waterdropRadius * 1.1, // 第二段控制点1X（继续向内）
      bottomY + waterdropRadius * 0.55, // 第二段控制点1Y（加速下降）
      centerX - waterdropRadius * 1.1, // 第二段控制点2X（接近圆形）
      dropCenterY + waterdropRadius * 0.3, // 第二段控制点2Y（圆形附近）
      centerX - waterdropRadius * 0.7, // 终点X（圆形左下角外侧）
      dropCenterY + waterdropRadius * 0.7, // 终点Y（圆形下半圆外侧）
    );

    // 连接到圆形底部
    path.arcToPoint(
      Offset(centerX, dropCenterY + waterdropRadius),
      radius: Radius.circular(waterdropRadius),
      clockwise: false,
    );

    // 回到矩形底部中心左侧
    path.lineTo(centerX - waterdropRadius * 0.5, bottomY);
    path.lineTo(leftConnectionX, bottomY);

    path.close();
    canvas.drawPath(path, paint);
  }

  // 绘制右侧连接曲线
  void _drawRightCurve(
    Canvas canvas,
    Size size,
    Paint paint,
    double centerX,
    double bottomY,
    double dropCenterY,
  ) {
    final path = Path();

    // 从矩形底部右侧连接点开始，先创建一个平滑的起始过渡
    final rightConnectionX = centerX + waterdropRadius * 2.2;
    path.moveTo(rightConnectionX, bottomY);

    // 第一段三次贝塞尔曲线：从矩形底部平滑过渡到中间点
    final midPointX = centerX + waterdropRadius * 1.3;
    final midPointY = bottomY + waterdropRadius * 0.35;

    path.cubicTo(
      centerX + waterdropRadius * 2.1, // 第一段控制点1X（接近起始点，保持水平）
      bottomY + waterdropRadius * -0.01, // 第一段控制点1Y（几乎水平）
      centerX + waterdropRadius * 1.5, // 第一段控制点2X（逐渐向内）
      bottomY + waterdropRadius * 0.05, // 第一段控制点2Y（轻微下降）
      midPointX, // 中间点X
      midPointY, // 中间点Y
    );

    // 第二段三次贝塞尔曲线：从中间点平滑过渡到圆形连接点
    path.cubicTo(
      centerX + waterdropRadius * 1.1, // 第二段控制点1X（继续向内）
      bottomY + waterdropRadius * 0.55, // 第二段控制点1Y（加速下降）
      centerX + waterdropRadius * 1.1, // 第二段控制点2X（接近圆形）
      dropCenterY + waterdropRadius * 0.3, // 第二段控制点2Y（圆形附近）
      centerX + waterdropRadius * 0.7, // 终点X（圆形右下角外侧）
      dropCenterY + waterdropRadius * 0.7, // 终点Y（圆形下半圆外侧）
    );

    // 连接到圆形底部
    path.arcToPoint(
      Offset(centerX, dropCenterY + waterdropRadius),
      radius: Radius.circular(waterdropRadius),
      clockwise: true,
    );

    // 回到矩形底部中心右侧
    path.lineTo(centerX + waterdropRadius * 0.5, bottomY);
    path.lineTo(rightConnectionX, bottomY);

    path.close();
    canvas.drawPath(path, paint);
  }

  // 绘制底部圆形
  void _drawBottomCircle(
    Canvas canvas,
    Paint paint,
    double centerX,
    double dropCenterY,
  ) {
    canvas.drawCircle(Offset(centerX, dropCenterY), waterdropRadius, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
