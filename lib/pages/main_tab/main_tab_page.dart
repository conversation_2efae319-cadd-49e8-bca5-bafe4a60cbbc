/*
   === 底部导航主容器页面 ===

   主要功能：
   - 管理底部导航栏的5个主要页面
   - 处理交易页面的特殊逻辑（凸起模式、弹窗切换）
   - 管理页面状态和动画效果
   - 处理用户登录状态检查（资产页面需要登录）
*/

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/widgets/common/lazy_tab_view.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:qubic_exchange/routes/index.dart';
import 'package:qubic_exchange/services/pages/auth/index.dart';
import './widgets/trade_options_switch.dart';
import '../trade/spot_trade/spot_trade_page.dart';
import '../trade/contract_trade/contract_trade_page.dart';
import '../trade/copy_order_trade/copy_order_trade.dart';

/// 主标签页索引枚举
enum MainTabIndex {
  home(0),
  market(1),
  trade(2),
  wealth(3),
  assets(4);

  const MainTabIndex(this.value);
  final int value;
}

/// 交易页面类型枚举
enum TradePageType {
  spot('spot'),
  contractTrade('contract_trade'),
  copyTrade('copy_trade');

  const TradePageType(this.key);
  final String key;
}

/// 主标签页配置常量
class _MainTabConfig {
  static Duration get animationDuration => UiConstants.animationDurationLong;
  static Duration get iconAnimationDuration =>
      UiConstants.animationDurationShort;
  static double get iconSize => UiConstants.iconSize28;
  static double get elevatedIconSize => UiConstants.iconSize48;
  static double get elevatedIconOffset => -26.0;
  static double get tradeIconSize => UiConstants.iconSize20;

  static const List<String> iconNames = [
    'home_tabbar_home',
    'home_tabbar_market',
    'home_tabbar_trade',
    'home_tabbar_wealth',
    'home_tabbar_assets',
  ];

  static const List<String> translationKeys = [
    'home',
    'market',
    'trade',
    'wealth',
    'assets',
  ];
}

/// 主标签页组件
///
/// 管理底部导航栏和页面切换逻辑
class MainTabPage extends StatefulWidget {
  /// 是否启用凸起模式（交易按钮凸起显示）
  final bool isElevatedMode;

  /// 全局Key，用于外部访问
  static final GlobalKey globalKey = GlobalKey();

  const MainTabPage({super.key, this.isElevatedMode = true});

  @override
  State<MainTabPage> createState() => _MainTabPageState();
}

class _MainTabPageState extends State<MainTabPage>
    with TickerProviderStateMixin {
  // ==================== 状态变量 ====================

  /// 当前选中的标签页索引
  int _currentIndex = MainTabIndex.home.value;

  /// 底部导航图标动画控制器列表
  late List<AnimationController> _animationControllers;

  /// 当前凸起模式状态
  late bool _isElevatedMode;

  /// 交易按钮是否已经点击过（用于区分第一次和第二次点击）
  static bool _hasTradeButtonBeenClicked = false;

  /// 当前选中的交易选项（静态变量保持状态）
  static TradePageType _currentTradeOption = TradePageType.spot;

  /// 交易选项缓存键
  static const String _tradeOptionCacheKey = 'selected_trade_option';

  // ==================== 交易页面管理 ====================

  /// 获取交易页面实例（每次创建新实例，确保实时更新）
  Widget _createTradePage(TradePageType type) {
    switch (type) {
      case TradePageType.spot:
        return const SpotTradePage();
      case TradePageType.contractTrade:
        return const ContractTradePage();
      case TradePageType.copyTrade:
        return const CopyOrderTrade();
    }
  }

  // ==================== 公共方法 ====================

  /// 测试方法：直接切换到合约交易页面（用于调试）
  void switchToContractTrade() {
    setState(() {
      _currentTradeOption = TradePageType.contractTrade;
      _updateTradePageInMainPages();
    });
    _saveTradeOptionToCache();
  }

  /// 测试方法：直接切换到跟单交易页面（用于调试）
  void switchToCopyTrade() {
    setState(() {
      _currentTradeOption = TradePageType.copyTrade;
      _updateTradePageInMainPages();
    });
    _saveTradeOptionToCache();
  }

  /// 切换到行情tab
  void switchToMarketTab() {
    _switchToTab(1); // 行情是第2个tab，索引为1
  }

  /// 获取页面实例（每次创建新实例，确保实时更新）
  Widget _getPage(int index) {
    switch (index) {
      case 0:
        return const HomePage();
      case 1:
        return const MarketPage();
      case 2:
        // 交易页面使用懒加载，只有切换到该tab时才初始化
        return LazyTabView(
          builder: () => _getCurrentTradePage(),
        );
      case 3:
        return const WealthPage();
      case 4:
        return const AssetsPage();
      default:
        return const HomePage();
    }
  }

  // ==================== 私有方法 ====================

  /// 获取当前交易页面（每次创建新实例，确保实时更新）
  Widget _getCurrentTradePage() {
    return _createTradePage(_currentTradeOption);
  }

  /// 更新交易页面（由于不再使用缓存，此方法仅用于状态更新）
  void _updateTradePageInMainPages() {
    // 不再需要更新缓存，页面会在下次访问时自动创建新实例
    setState(() {
      // 触发重建以显示新的交易页面
    });
  }

  /// 从缓存加载交易选项
  void _loadTradeOptionFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedOption = prefs.getString(_tradeOptionCacheKey);
      if (cachedOption != null) {
        switch (cachedOption) {
          case 'spot':
            _currentTradeOption = TradePageType.spot;
            break;
          case 'contract_trade':
            _currentTradeOption = TradePageType.contractTrade;
            break;
          case 'copy_trade':
            _currentTradeOption = TradePageType.copyTrade;
            break;
          default:
            _currentTradeOption = TradePageType.spot;
        }
      }
    } catch (e) {
      // 如果加载失败，使用默认值
      _currentTradeOption = TradePageType.spot;
    }
  }

  /// 保存交易选项到缓存
  void _saveTradeOptionToCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_tradeOptionCacheKey, _currentTradeOption.key);
    } catch (e) {
      // 忽略保存失败
    }
  }

  /// 将字符串转换为交易页面类型枚举
  TradePageType _stringToTradePageType(String key) {
    switch (key) {
      case 'spot':
        return TradePageType.spot;
      case 'contract_trade':
        return TradePageType.contractTrade;
      case 'copy_trade':
        return TradePageType.copyTrade;
      default:
        return TradePageType.spot;
    }
  }

  // ==================== 生命周期方法 ====================

  @override
  void initState() {
    super.initState();
    _initializeState();
    _initializeAnimationControllers();
    _initializeMainPages();
  }

  /// 初始化状态
  void _initializeState() {
    _isElevatedMode = widget.isElevatedMode;
    _loadTradeOptionFromCache();
  }

  /// 初始化动画控制器
  void _initializeAnimationControllers() {
    _animationControllers = List.generate(
      _MainTabConfig.iconNames.length,
      (index) => AnimationController(
        duration: _MainTabConfig.animationDuration,
        vsync: this,
      ),
    );

    // 设置初始动画状态
    for (int i = 0; i < _animationControllers.length; i++) {
      _animationControllers[i].value = i == _currentIndex ? 1.0 : 0.0;
    }
  }

  /// 初始化主页面列表（不再需要缓存初始化）
  void _initializeMainPages() {
    // 不再需要初始化页面缓存，页面会在需要时动态创建
  }

  @override
  void dispose() {
    _disposeAnimationControllers();
    super.dispose();
  }

  /// 销毁动画控制器
  void _disposeAnimationControllers() {
    for (var controller in _animationControllers) {
      controller.dispose();
    }
  }

  // ==================== UI构建方法 ====================

  /// 设置凸起模式（通过参数控制）
  void setElevatedMode(bool isElevated) {
    setState(() {
      _isElevatedMode = isElevated;
    });
  }

  /// 构建主体内容
  Widget _buildBody() {
    return IndexedStack(
      index: _currentIndex,
      children: List.generate(5, (index) => _getPage(index)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.templateColors.surface,
      body: _buildBody(),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  /// 构建底部导航栏
  Widget _buildBottomNavigationBar() {
    return Theme(
      data: Theme.of(context).copyWith(
        // 移除底部导航项点击水波纹效果
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
      ),
      child: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
        items: _buildBottomNavItems(),
        selectedLabelStyle: context.templateStyle.text.hintTextMedium,
        unselectedLabelStyle: context.templateStyle.text.hintText,
        selectedItemColor: context.templateColors.textPrimary,
        unselectedItemColor: context.templateColors.textSecondary,
        backgroundColor: context.templateColors.surface,
        elevation: 0,
        enableFeedback: true,
        showSelectedLabels: true,
        showUnselectedLabels: true,
        iconSize: _MainTabConfig.iconSize,
      ),
    );
  }

  /// 构建底部导航项
  List<BottomNavigationBarItem> _buildBottomNavItems() {
    final labels = ['首页', '行情', '交易', '理财', '资产'];

    return List.generate(_MainTabConfig.translationKeys.length, (index) {
      return BottomNavigationBarItem(
        icon: _buildAnimatedLottieIcon(index),
        label: labels[index],
      );
    });
  }

  /// 构建动画Lottie图标
  Widget _buildAnimatedLottieIcon(int index) {
    final isTradeTab = index == MainTabIndex.trade.value;
    final shouldElevate = _isElevatedMode && isTradeTab;

    return Container(
      padding: const EdgeInsets.only(bottom: 4),
      child: AnimatedBuilder(
        animation: _animationControllers[index],
        builder: (context, child) {
          final isSelected = _currentIndex == index;
          final animationValue = _animationControllers[index].value;

          if (shouldElevate) {
            return _buildElevatedIcon(index, isSelected, animationValue);
          } else {
            return SizedBox(
              height: _MainTabConfig.iconSize,
              child: AnimatedContainer(
                duration: _MainTabConfig.iconAnimationDuration,
                curve: Curves.easeInOut,
                padding: const EdgeInsets.all(2),
                child: _buildLottieIcon(index),
              ),
            );
          }
        },
      ),
    );
  }

  /// 构建凸起图标
  Widget _buildElevatedIcon(int index, bool isSelected, double animationValue) {
    return SizedBox(
      height: _MainTabConfig.iconSize,
      child: Stack(
        alignment: Alignment.center,
        clipBehavior: Clip.none,
        children: [
          Positioned(
            top: _MainTabConfig.elevatedIconOffset,
            child: Container(
              width: _MainTabConfig.elevatedIconSize,
              height: _MainTabConfig.elevatedIconSize,
              decoration: BoxDecoration(
                color: context.templateColors.primary,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: ThemedImage.asset(
                  'trade_icon',
                  width: _MainTabConfig.tradeIconSize,
                  height: _MainTabConfig.tradeIconSize,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建Lottie图标
  Widget _buildLottieIcon(int index) {
    final iconName = _MainTabConfig.iconNames[index];
    final isSelected = _currentIndex == index;

    // 根据选中状态决定使用哪个动画文件
    final finalIconName = isSelected ? iconName : '${iconName}_resume';

    return ThemedLottie(
      path: finalIconName,
      width: _MainTabConfig.iconSize,
      height: _MainTabConfig.iconSize,
      followTheme: true,
      template: TemplateType.base, // 使用默认模板
    );
  }

  // ==================== 事件处理方法 ====================

  /// 标签页点击处理
  void _onTabTapped(int index) {
    // 交易按钮特殊处理
    if (index == MainTabIndex.trade.value) {
      _handleTradeButtonTap();
      return;
    }

    // 资产页面登录检查
    if (index == MainTabIndex.assets.value) {
      _handleAssetsTabTap();
      return;
    }

    // 重置交易按钮状态
    _resetTradeButtonState();

    // 切换到其他标签页
    _switchToTab(index);
  }

  /// 重置交易按钮状态
  void _resetTradeButtonState() {
    if (_hasTradeButtonBeenClicked) {
      _hasTradeButtonBeenClicked = false;
    }
  }

  /// 处理交易按钮点击
  void _handleTradeButtonTap() {
    HapticFeedback.heavyImpact();

    if (!_isElevatedMode) {
      // 普通模式：直接切换到交易页面
      _switchToTab(MainTabIndex.trade.value);
      return;
    }

    // 凸起模式：智能弹窗逻辑
    if (_currentIndex == MainTabIndex.trade.value) {
      // 当前已经在交易页面：直接显示交易选项弹窗
      _showTradeOptionsSwitch();
    } else {
      // 当前不在交易页面：跳转到交易页面
      _hasTradeButtonBeenClicked = true;
      _navigateToCurrentTradePage();
    }
  }

  /// 处理资产页面点击
  void _handleAssetsTabTap() {
    // 检查用户是否已登录
    if (!LoginStatusService.isLoggedIn(context)) {
      // 未登录，跳转到登录页面
      _navigateToLogin();
      return;
    }

    // 已登录，正常切换到资产页面
    _switchToTab(MainTabIndex.assets.value);
  }

  /// 导航到登录页面
  void _navigateToLogin() {
    NavigationService().navigateTo(
      AppRoutes.login,
      arguments: {'transitionType': RouteTransitionType.slideUp},
    );
  }

  /// 切换到指定标签页
  void _switchToTab(int index) {
    if (_currentIndex == index) return;

    HapticFeedback.heavyImpact();
    final oldIndex = _currentIndex;

    setState(() {
      _currentIndex = index;
    });

    // 播放切换动画
    _playTabSwitchAnimation(oldIndex, index);
  }

  /// 播放标签切换动画
  void _playTabSwitchAnimation(int oldIndex, int newIndex) {
    _animationControllers[oldIndex].reset();
    _animationControllers[oldIndex].forward();
    _animationControllers[newIndex].reset();
    _animationControllers[newIndex].forward();
  }

  /// 导航到当前选中的交易页面
  void _navigateToCurrentTradePage() {
    _switchToTab(MainTabIndex.trade.value);
  }

  /// 显示交易选项切换弹窗
  void _showTradeOptionsSwitch() {
    TradeOptionsSwitch.show(
      context,
      currentOption: _currentTradeOption.key,
      onOptionSelected: (String option) {
        // 处理选项选择
        _handleTradeOptionSelected(option);
      },
    );
  }

  /// 处理交易选项选择
  void _handleTradeOptionSelected(String option) {
    final newTradeOption = _stringToTradePageType(option);
    // 如果选择了不同的选项，重置点击状态并更新页面
    if (_currentTradeOption != newTradeOption) {
      setState(() {
        _hasTradeButtonBeenClicked = false;
        _currentTradeOption = newTradeOption;
        // 更新主页面列表中的交易页面
        _updateTradePageInMainPages();
      });
      // 保存选择到缓存
      _saveTradeOptionToCache();
    }
  }
}
