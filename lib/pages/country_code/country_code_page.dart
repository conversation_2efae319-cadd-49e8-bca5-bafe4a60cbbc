/*
*  区号选择界面
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/utils/index.dart';
import 'package:qubic_exchange/l10n/index.dart';
import 'package:remixicon/remixicon.dart';
import 'package:qubic_exchange/data/country_code_data.dart'
    as country_data; // 国家区号

class CountryCodePage extends StatefulWidget {
  const CountryCodePage({super.key});

  @override
  State<CountryCodePage> createState() => _CountryCodePageState();
}

class _CountryCodePageState extends State<CountryCodePage>
    with DynamicScrollPhysicsMixin {
  late final ScrollController _scrollController = ScrollController();

  // 搜索相关状态
  final TextEditingController _searchController = TextEditingController();
  List<country_data.CountryCode> _filteredCountries =
      country_data.CountryCodes.countries;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    initDynamicScrollPhysics(_scrollController);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    disposeDynamicScrollPhysics();
    super.dispose();
  }

  // 搜索变化处理
  void _onSearchChanged() {
    final languageManager = context.read<LanguageManager>();
    setState(() {
      _searchQuery = _searchController.text;
      if (_searchQuery.isEmpty) {
        _filteredCountries = country_data.CountryCodes.countries;
      } else {
        _filteredCountries = country_data.CountryCodes.searchCountries(
          _searchQuery,
          languageManager.currentLocale.languageCode,
          languageManager.currentLocale.countryCode,
        );
      }
    });
  }

  // 选择国家区号并返回
  void _selectCountryCode(country_data.CountryCode country) {
    Navigator.pop(context, country);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(showBackButton: true),
      backgroundColor: context.templateColors.surface,
      body: SafeArea(
        bottom: false,
        child: CustomScrollView(
          controller: _scrollController,
          physics: currentScrollPhysics,
          slivers: [
            // 搜索框 - 吸顶效果
            SliverPersistentHeader(
              pinned: true,
              delegate: _SearchBarDelegate(
                child: _buildSearch(),
                height: UiConstants.spacing64,
              ),
            ),
            // 区号列表或空状态
            _filteredCountries.isEmpty
                ? _buildEmptyState()
                : _buildCountryCodeList(),
            // 底部安全区域间距
            SliverToBoxAdapter(
              child: SizedBox(height: MediaQuery.of(context).padding.bottom),
            ),
          ],
        ),
      ),
    );
  }

  // 构建搜索框
  Widget _buildSearch() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing16,
        vertical: UiConstants.spacing12,
      ),
      child: TextFieldWidget(
        controller: _searchController,
        height: 40,
        hintText: '搜索国家或区号',
        prefixIcon: Padding(
          padding: EdgeInsets.only(
            left: UiConstants.spacing12,
            right: UiConstants.spacing4,
          ),
          child: Icon(
            RemixIcons.search_2_line,
            size: 16,
            color: context.templateColors.textTertiary,
          ),
        ),
        suffixIcon:
            _searchQuery.isNotEmpty
                ? GestureDetector(
                  onTap: () {
                    _searchController.clear();
                  },
                  child: Padding(
                    padding: EdgeInsets.only(right: UiConstants.spacing12),
                    child: Icon(
                      RemixIcons.close_circle_fill,
                      size: 16,
                      color: context.templateColors.textTertiary,
                    ),
                  ),
                )
                : null,
        onChanged: (value) {
          // 搜索逻辑已在 _onSearchChanged 中处理
        },
      ),
    );
  }

  // 构建国家区号列表
  Widget _buildCountryCodeList() {
    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        final country = _filteredCountries[index];
        return _buildCountryCodeItem(country);
      }, childCount: _filteredCountries.length),
    );
  }

  // 构建国家区号项
  Widget _buildCountryCodeItem(country_data.CountryCode country) {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        // 根据当前语言获取国家名称
        final countryName = country.getNameByLanguage(
          languageManager.currentLocale.languageCode,
          languageManager.currentLocale.countryCode,
        );

        return InkWellWidget(
          onTap: () => _selectCountryCode(country),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: UiConstants.spacing16,
              vertical: UiConstants.spacing16,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 主要名称 - 根据当前语言显示
                      Text(
                        countryName,
                        style: context.templateStyle.text.bodyText,
                      ),
                    ],
                  ),
                ),
                SizedBox(width: UiConstants.spacing12),
                // 区号
                Text(
                  country.dialCode,
                  style: context.templateStyle.text.descriptionText,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 构建无搜索结果
  Widget _buildEmptyState() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: EdgeInsets.only(top: UiConstants.spacing32),
        child: EmptyWidget(
          imageName: 'icon_no_data_search',
          imageSize: 150,
          text: _searchQuery.isEmpty ? '暂无数据' : '无搜索结果',
        ),
      ),
    );
  }
}

/// 搜索框吸顶代理
///
/// 用于实现搜索框的吸顶效果，当用户滚动列表时，
/// 搜索框会固定在顶部，方便随时搜索。
class _SearchBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;

  _SearchBarDelegate({required this.child, this.height = 64.0});

  @override
  double get minExtent => height;

  @override
  double get maxExtent => height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(
      height: height,
      color: context.templateColors.surface,
      child: child,
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return oldDelegate != this;
  }
}
