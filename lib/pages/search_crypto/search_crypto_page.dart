/*
  搜索货币
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/services/market/index.dart';
import 'package:qubic_exchange/pages/market/models/crypto_item_data.dart';

class SearchCryptoPage extends StatefulWidget {
  const SearchCryptoPage({super.key});

  @override
  State<SearchCryptoPage> createState() => _SearchCryptoPageState();
}

class _SearchCryptoPageState extends State<SearchCryptoPage> with TickerProviderStateMixin {
  final preferredSize = Size.fromHeight(56);
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  // 市场类型数据
  final List<Map<String, dynamic>> _marketTypes = [];

  // 币种数据按市场类型分组
  Map<String, List<CryptoItemData>> _currencyDataByMarket = {};

  // 搜索结果按市场类型分组
  Map<String, List<CryptoItemData>> _filteredDataByMarket = {};

  String _searchQuery = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    _loadMarketData();
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  // 加载市场数据
  Future<void> _loadMarketData() async {
    try {
      // 获取市场类型数据
      final marketTypeModels = MarketService.instance.marketTypeModels;

      for (final marketType in marketTypeModels) {
        _marketTypes.add({'id': marketType.id.toString(), 'title': marketType.name});
      }

      // 获取所有币种数据并按市场类型分组
      final currencyModels = MarketService.instance.currencyModels;
      _currencyDataByMarket.clear();

      for (final currency in currencyModels) {
        final marketType = currency.marketType;
        if (marketType > 0) {
          // 为每个市场类型创建数据
          final marketIds = _getMarketIdsByType(marketType);

          for (final marketId in marketIds) {
            if (!_currencyDataByMarket.containsKey(marketId)) {
              _currencyDataByMarket[marketId] = [];
            }

            // 获取ticker数据
            final tickerData = _getTickerDataForMarket(currency, marketId);

            // 创建CryptoItemData
            final cryptoItem = CryptoItemData(
              symbol: currency.baseAsset,
              quoteCurrency: currency.quoteAsset,
              currentPrice: tickerData?.lastPrice ?? 0.0,
              cnyPrice: 0.0,
              changePercentage: tickerData?.priceChangeP ?? 0.0,
              volume24h: tickerData?.volume ?? 0.0,
              volumeUnit: '万',
              precision: currency.mPricePrecision,
              logo: currency.logo,
            );

            _currencyDataByMarket[marketId]!.add(cryptoItem);
          }
        }
      }

      // 初始化TabController
      if (_marketTypes.isNotEmpty) {
        _tabController = TabController(length: _marketTypes.length, vsync: this);
      }

      // 初始化搜索结果
      _initializeFilteredData();
    } catch (e) {
      debugPrint('加载市场数据失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 获取ticker数据
  TickerModel? _getTickerDataForMarket(CurrencyModel currency, String marketId) {
    // 根据市场类型选择对应的ticker数据
    String tickerKey;
    if (marketId == '6') {
      // 杠杆栏目使用现货的ticker数据
      tickerKey = '1';
    } else {
      // 其他栏目使用对应栏目ID的ticker数据
      tickerKey = marketId;
    }

    return currency.tickers[tickerKey];
  }

  // 根据market_type获取市场ID列表
  List<String> _getMarketIdsByType(int marketType) {
    switch (marketType) {
      case 1:
        return ['1', '5', '6']; // 现货数据用于现货、合约、杠杆市场
      case 5:
        return ['5']; // 合约市场使用market_type=5的数据
      default:
        return [marketType.toString()]; // 其他市场使用对应的market_type
    }
  }

  // 初始化过滤数据
  void _initializeFilteredData() {
    _filteredDataByMarket.clear();
    for (final entry in _currencyDataByMarket.entries) {
      _filteredDataByMarket[entry.key] = List.from(entry.value);
    }
  }

  // 搜索变化处理
  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
      _filterData();
    });
  }

  // 过滤数据
  void _filterData() {
    if (_searchQuery.isEmpty) {
      _initializeFilteredData();
      return;
    }

    _filteredDataByMarket.clear();
    for (final entry in _currencyDataByMarket.entries) {
      final filteredList = entry.value.where((item) => _matchesSearch(item)).toList();
      if (filteredList.isNotEmpty) {
        _filteredDataByMarket[entry.key] = filteredList;
      }
    }
  }

  // 检查是否匹配搜索条件
  bool _matchesSearch(CryptoItemData item) {
    final query = _searchQuery.toLowerCase();
    final symbol = item.symbol.toLowerCase();
    final quoteCurrency = item.quoteCurrency.toLowerCase();

    return symbol.contains(query) || quoteCurrency.contains(query) || '$symbol$quoteCurrency'.contains(query);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.templateColors.surface,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
          child: Column(children: [_buildSearchBar(), Expanded(child: _buildContent())]),
        ),
      ),
    );
  }

  // 顶部搜索栏
  Widget _buildSearchBar() {
    return Container(
      color: context.templateColors.surface,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: TextFieldWidget(
              controller: _searchController,
              height: 36,
              radius: BorderRadius.circular(UiConstants.borderRadius8),
              hintText: '搜索币种',
              fillColor: context.templateColors.inputBackground,
              autofocus: true,
              textInputAction: TextInputAction.search,
              prefixIcon: Padding(
                padding: EdgeInsets.all(UiConstants.spacing8),
                child: ThemedImage.asset('icon_search', width: 16, height: 16, followTheme: true),
              ),
              suffixIcon:
                  _searchQuery.isNotEmpty
                      ? Padding(
                        padding: EdgeInsets.all(UiConstants.spacing8),
                        child: InkWellWidget(
                          onTap: () {
                            _searchController.clear();
                          },
                          child: ThemedImage.asset('icon_close', width: 16, height: 16, followTheme: true),
                        ),
                      )
                      : null,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: UiConstants.spacing14),
            child: InkWellWidget(
              onTap: () => Navigator.of(context).pop(),
              child: Text('取消', style: context.templateStyle.text.bodyLarge.copyWith(color: context.templateColors.primary)),
            ),
          ),
        ],
      ),
    );
  }

  // 内容区域
  Widget _buildContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return NestedScrollView(
      headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
        return <Widget>[_buildStickyTabBar()];
      },
      body: TabBarView(
        controller: _tabController,
        children:
            _marketTypes.map((marketType) {
              final marketId = marketType['id'] as String;
              final filteredData = _filteredDataByMarket[marketId] ?? [];
              return _buildCryptoList(filteredData);
            }).toList(),
      ),
    );
  }

  // 构建货币列表
  Widget _buildCryptoList(List<CryptoItemData> data) {
    if (data.isEmpty) {
      return EmptyWidget(text: _searchQuery.isNotEmpty ? '未找到相关币种' : '暂无币种数据', imageName: 'noData');
    }

    return ListView.builder(
      itemCount: data.length,
      itemBuilder: (context, index) {
        return InkWellWidget(onTap: () => _onCryptoItemTap(data[index]), child: _buildCryptoItem(data[index]));
      },
    );
  }

  // 处理币种点击
  void _onCryptoItemTap(CryptoItemData data) {
    // 这里可以添加导航到交易页面的逻辑
    Navigator.pop(context, data);
  }

  // 吸顶的选项卡
  Widget _buildStickyTabBar() {
    return SliverPersistentHeader(
      pinned: true, // 设置为吸顶
      floating: true,
      delegate: _StickyTabBarDelegate(
        child: Container(
          color: context.templateColors.surface,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(top: UiConstants.spacing20, bottom: UiConstants.spacing12),
                child: Text('热搜', style: context.templateStyle.text.h2),
              ),
              TabbarWidget(
                controller: _tabController,
                tabs: _marketTypes.map((marketType) => TabItem(title: marketType['title'] as String)).toList(),
                indicatorStyle: TabBarIndicatorStyle.filled,
                showIndicator: true,
                labelStyle: context.templateStyle.text.bodyTextMedium,
                selectedColor: context.templateColors.textPrimary,
                unselectedColor: context.templateColors.textTertiary,
                height: 26.0,
                labelPadding: EdgeInsets.symmetric(horizontal: UiConstants.spacing12),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 列表项
  Widget _buildCryptoItem(CryptoItemData data) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing12, horizontal: UiConstants.spacing16),
      child: Row(
        children: [
          // 货币图标
          data.logo != null && data.logo!.isNotEmpty
              ? ThemedImage.network(data.logo!, width: 24, height: 24)
              : ThemedImage.crypto('btc', width: 24, height: 24, followTheme: true),
          SizedBox(width: UiConstants.spacing12),
          // 货币信息
          Expanded(child: Text('${data.symbol}/${data.quoteCurrency}', style: context.templateStyle.text.bodyLargeMedium)),
          // 价格信息
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(data.formattedPrice, style: context.templateStyle.text.bodyLargeMedium),
              Text(
                data.formattedChangePercentage,
                style: context.templateStyle.text.bodySmall.copyWith(
                  color: data.changePercentage >= 0 ? context.templateColors.success : context.templateColors.error,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// 吸顶选项卡委托类
class _StickyTabBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  _StickyTabBarDelegate({required this.child});

  @override
  double get minExtent => 110.0; // 最小高度

  @override
  double get maxExtent => 110.0; // 最大高度

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
