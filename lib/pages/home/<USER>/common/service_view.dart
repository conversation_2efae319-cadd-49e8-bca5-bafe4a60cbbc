/*

  保障与服务
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class ServiceView extends StatefulWidget {
  const ServiceView({super.key});

  @override
  State<ServiceView> createState() => _ServiceViewState();
}

class _ServiceViewState extends State<ServiceView> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing16),
      child: _buildServiceList(context),
    );
  }

  // 服务项
  Widget _buildServiceItem(String title, String imagePath, String buttonText) {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing32),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: context.templateStyle.text.bodyTextMedium,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: UiConstants.spacing12),
                GestureDetector(
                  onTap: () {},
                  child: Text(
                    buttonText,
                    style: context.templateStyle.text.bodyText.copyWith(
                      color: context.templateColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: UiConstants.spacing20),
          ThemedImage.asset(
            imagePath,
            width: 75,
            height: 75,
            followTheme: true,
          ),
        ],
      ),
    );
  }

  // 服务列表
  Widget _buildServiceList(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题 - 淡入 + 从上滑入
          Padding(
            padding: EdgeInsets.only(bottom: UiConstants.spacing20),
            child: TextWidget(
              text: '安全服务',
              style: context.templateStyle.text.h3,
            ),
          ),

          // 第一个服务项 - 从左滑入，延迟200ms
          _buildServiceItem('安全基金保障您的资产安全', 'icon_overview_safe_coin', '查看详情'),

          // 第二个服务项 - 从右滑入，延迟400ms
          _buildServiceItem(
            '帮助中心为您提供全方位支持',
            'icon_overview_safe_shield',
            '立即前往',
          ),
        ],
      ),
    );
  }
}
