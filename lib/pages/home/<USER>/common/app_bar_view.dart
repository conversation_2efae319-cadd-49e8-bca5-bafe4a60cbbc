/*
  首页AppBar组件
*/

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:remixicon/remixicon.dart';
import 'package:qubic_exchange/widgets/index.dart';
import '../../../../core/index.dart';
import '../../../../routes/index.dart';
import '../../../../providers/auth_provider.dart';

class AppBarView extends StatelessWidget implements PreferredSizeWidget {
  final String? hintText;
  final VoidCallback? onSearchTap;
  final VoidCallback? onAvatarTap;
  final VoidCallback? onSupportTap;
  final double height;
  final Color? backgroundColor;
  final SystemUiOverlayStyle? systemOverlayStyle;

  const AppBarView({
    super.key,
    this.hintText = 'BTC/USDT',
    this.onSearchTap,
    this.onAvatarTap,
    this.onSupportTap,
    this.height = 56.0,
    this.backgroundColor,
    this.systemOverlayStyle,
  });

  @override
  Size get preferredSize => Size.fromHeight(height);

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final isDark = Theme.of(context).brightness == Brightness.dark;

        return AppBar(
          backgroundColor: backgroundColor ?? context.templateColors.surface,
          elevation: 0,
          scrolledUnderElevation: 0,
          toolbarHeight: height,
          automaticallyImplyLeading: false,
          systemOverlayStyle:
              systemOverlayStyle ??
              SystemUiOverlayStyle(
                statusBarColor: Colors.transparent,
                statusBarIconBrightness:
                    isDark ? Brightness.light : Brightness.dark,
                statusBarBrightness:
                    isDark ? Brightness.dark : Brightness.light,
              ),
          title: Row(
            children: [
              // 左侧头像图标
              InkWellWidget(
                onTap: onAvatarTap ?? () => _navigateToUserCenter(context),
                child: ThemedLottie(
                  path: 'home_avatar',
                  width: UiConstants.iconSize24,
                  height: UiConstants.iconSize24,
                  followTheme: true,
                  template: TemplateType.base, // 使用默认模板
                ),
              ),

              SizedBox(width: UiConstants.spacing14),

              // 搜索框
              Expanded(
                child: InkWellWidget(
                  onTap: onSearchTap ?? () => _navigateToSearchCrypto(context),
                  child: Container(
                    height: UiConstants.spacing36,
                    decoration: BoxDecoration(
                      color: context.templateColors.inputBackground,
                      borderRadius: BorderRadius.circular(
                        UiConstants.borderRadius8,
                      ),
                    ),
                    child: Row(
                      children: [
                        SizedBox(width: UiConstants.spacing16),
                        Icon(
                          RemixIcons.search_line,
                          size: UiConstants.iconSize16,
                          color: context.templateColors.textTertiary,
                        ),
                        SizedBox(width: UiConstants.spacing8),
                        Text(
                          hintText ?? 'BTC/USDT',
                          style: context.templateStyle.text.descriptionText,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              SizedBox(width: UiConstants.spacing14),

              // 右侧客服图标
              InkWellWidget(
                onTap: onSupportTap,
                child: ThemedImage.asset(
                  'support',
                  width: UiConstants.spacing24,
                  height: UiConstants.spacing24,
                  followTheme: true,
                ),
              ),

              // 右侧消息通知 - 仅在登录时显示
              Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  // 只有在用户登录且令牌有效时才显示消息通知
                  if (authProvider.isLoggedIn && !authProvider.isTokenExpired) {
                    return InkWellWidget(
                      onTap: () => {},
                      child: ThemedImage.asset(
                        'icon_message',
                        width: UiConstants.spacing24,
                        height: UiConstants.spacing24,
                        followTheme: true,
                        margin: EdgeInsets.only(left: UiConstants.spacing8),
                      ),
                    );
                  }
                  // 未登录时不显示消息通知图标
                  return const SizedBox.shrink();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // 跳转到用户中心
  void _navigateToUserCenter(BuildContext context) {
    NavigationService().navigateTo(AppRoutes.profilePage);
  }

  // 跳转至货币搜索
  void _navigateToSearchCrypto(BuildContext context) {
    NavigationService().navigateTo(AppRoutes.searchCrypto);
  }

  // 跳转到消息中心
  void _navigateToMessages(BuildContext context) {
    // TODO: 添加消息中心路由
    // NavigationService().navigateTo(AppRoutes.messages);

    // 临时显示提示
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('消息中心功能开发中')));
  }
}
