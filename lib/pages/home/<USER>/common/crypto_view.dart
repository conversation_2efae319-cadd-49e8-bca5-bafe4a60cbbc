/*
  首页币类激励轮播
*/

import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:shimmer/shimmer.dart';

// 右侧内容显示类型枚举
enum RightContentType {
  price, // 显示价格和变化百分比
  countdown, // 显示倒计时
  none, // 不显示
}

// 币类激励数据模型
class CoinPromotionData {
  final String title;
  final List<String> coinIcons;
  final double? price;
  final String? changePercent;
  final bool? isPositive;
  final String description;
  final RightContentType rightContentType;
  final DateTime? endTime; // 倒计时结束时间

  CoinPromotionData({
    required this.title,
    required this.coinIcons,
    required this.description,
    this.price,
    this.changePercent,
    this.isPositive,
    this.rightContentType = RightContentType.none,
    this.endTime,
  });
}

class CryptoView extends StatefulWidget {
  final bool showSkeleton;
  final List<CoinPromotionData>? data; // 允许外部传入数据
  final VoidCallback? onItemTap; // 点击回调

  const CryptoView({
    super.key,
    this.showSkeleton = false,
    this.data,
    this.onItemTap,
  });

  @override
  State<CryptoView> createState() => _CryptoViewState();
}

class _CryptoViewState extends State<CryptoView> with TickerProviderStateMixin {
  // 常量定义
  static const double _coinIconSize = 16.0;
  static const double _coinIconOverlap = 10.0;
  static const Duration _autoPlayInterval = Duration(seconds: 4);
  static const Duration _autoPlayAnimationDuration = Duration(
    milliseconds: 800,
  );

  int _currentIndex = 0;
  late final AnimationController _animationController;

  // 获取数据源
  List<CoinPromotionData> _getPromotionData(BuildContext context) =>
      widget.data ?? _getDefaultData(context);

  // 获取默认数据
  List<CoinPromotionData> _getDefaultData(BuildContext context) {
    return [
      CoinPromotionData(
        title: '双币投资',
        coinIcons: ['btc', 'eth'],
        description: '稳健收益，灵活投资',
        rightContentType: RightContentType.price,
        price: 2452.96,
        changePercent: '+2.53%',
        isPositive: true,
      ),
      CoinPromotionData(
        title: '限时活动',
        coinIcons: ['bnb'],
        description: '新用户专享福利',
        rightContentType: RightContentType.countdown,
        endTime: DateTime.now().add(const Duration(hours: 2, minutes: 30)),
      ),
      CoinPromotionData(
        title: '新币上线',
        coinIcons: ['sol'],
        description: '抢先体验新项目',
        rightContentType: RightContentType.none,
      ),
    ];
  }

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // 初始化动画
  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  // 页面变化回调
  void _onPageChanged(int index, dynamic reason) {
    if (mounted) {
      setState(() {
        _currentIndex = index;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: context.templateColors.buttonSecondary.withValues(
              alpha: 0.2,
            ),
            width: 1,
          ),
        ),
      ),
      child:
          widget.showSkeleton
              ? _buildSkeletonView()
              : Stack(
                children: [
                  CarouselSlider(
                    items:
                        _getPromotionData(context).asMap().entries.map((entry) {
                          return _buildSwiperItem(entry.value, entry.key);
                        }).toList(),
                    options: CarouselOptions(
                      height: 50,
                      autoPlay: false,
                      autoPlayInterval: _autoPlayInterval,
                      autoPlayAnimationDuration: _autoPlayAnimationDuration,
                      enlargeCenterPage: false,
                      viewportFraction: 1.0,
                      onPageChanged: _onPageChanged,
                    ),
                  ),
                  _buildIndexIndicator(context),
                ],
              ),
    );
  }

  // 轮播项
  Widget _buildSwiperItem(CoinPromotionData data, int index) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing16),
      child: Column(
        children: [
          // 顶部行
          _buildTopRow(data),

          const Spacer(),

          // 描述文本
          _buildDescription(data),
        ],
      ),
    );
  }

  // 构建顶部行
  Widget _buildTopRow(CoinPromotionData data) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildTitle(data.title),
        SizedBox(width: UiConstants.spacing4),
        _buildCoinIcons(data.coinIcons),
        const Spacer(),
        _buildRightContent(data),
      ],
    );
  }

  // 构建标题
  Widget _buildTitle(String title) {
    return Text(title, style: context.templateStyle.text.bodySmallMedium);
  }

  // 构建币种图标组
  Widget _buildCoinIcons(List<String> coinIcons) {
    return Expanded(
      child: SizedBox(
        height: _coinIconSize,
        child: Stack(
          children:
              coinIcons.asMap().entries.map((entry) {
                return Positioned(
                  left: entry.key * _coinIconOverlap,
                  child: _buildCoinIcon(entry.value),
                );
              }).toList(),
        ),
      ),
    );
  }

  // 构建单个币种图标
  Widget _buildCoinIcon(String coinName) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(UiConstants.borderRadiusCircle),
      child: ThemedImage(
        name: coinName,
        width: _coinIconSize,
        height: _coinIconSize,
        folder: ThemedAssetFolder.crypto,
      ),
    );
  }

  // 构建描述文本
  Widget _buildDescription(CoinPromotionData data) {
    return Padding(
      padding: EdgeInsets.only(right: UiConstants.spacing16),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          data.description,
          style: context.templateStyle.text.descriptionText,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  // 构建右侧内容
  Widget _buildRightContent(CoinPromotionData data) {
    switch (data.rightContentType) {
      case RightContentType.price:
        return _buildPriceContent(data);
      case RightContentType.countdown:
        return _buildCountdownContent(data);
      case RightContentType.none:
        return SizedBox.shrink();
    }
  }

  // 构建价格内容
  Widget _buildPriceContent(CoinPromotionData data) {
    if (data.price == null ||
        data.changePercent == null ||
        data.isPositive == null) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          data.price!.toStringAsFixed(2),
          style: context.templateStyle.text.bodySmallMedium,
        ),
        SizedBox(width: UiConstants.spacing8),
        Text(
          data.changePercent!,
          style: context.templateStyle.text.bodySmallMedium.copyWith(
            color:
                data.isPositive!
                    ? context.templateColors.tradeBuy
                    : context.templateColors.tradeSell,
          ),
        ),
      ],
    );
  }

  // 构建倒计时内容
  Widget _buildCountdownContent(CoinPromotionData data) {
    if (data.endTime == null) {
      return const SizedBox.shrink();
    }

    final now = DateTime.now();
    final difference = data.endTime!.difference(now);

    // 如果倒计时已结束，显示结束文本
    if (difference.isNegative) {
      return Text(
        '已结束',
        style: context.templateStyle.text.descriptionTextMedium,
      );
    }

    return CountdownWidget(
      targetTime: data.endTime,
      textStyle: context.templateStyle.text.bodySmallMedium,
      format: CountdownFormat.dayHourMinuteSecond,
      showLabels: true,
      labelTexts: {'days': '天'},
      labelStyle: context.templateStyle.text.bodySmallMedium,
    );
  }

  // 构建索引指示器
  Widget _buildIndexIndicator(BuildContext context) {
    return Positioned(
      right: UiConstants.spacing16,
      bottom: -2,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
        decoration: BoxDecoration(
          color: context.templateColors.surface,
          borderRadius: BorderRadius.circular(UiConstants.borderRadius4),
        ),
        child: Row(
          children: [
            Text.rich(
              TextSpan(
                style: context.templateStyle.text.descriptionSmall,
                children: [
                  TextSpan(
                    text: '${_currentIndex + 1}',
                    style: context.templateStyle.text.bodySmallMedium,
                  ),
                  TextSpan(text: '/${_getPromotionData(context).length}'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建骨架屏视图
  Widget _buildSkeletonView() {
    return Shimmer.fromColors(
      baseColor: context.templateColors.skeletonBase,
      highlightColor: context.templateColors.skeletonHighlight,
      child: Container(
        height: 80,
        margin: EdgeInsets.symmetric(horizontal: UiConstants.spacing16),
        decoration: BoxDecoration(
          color: context.templateColors.skeletonBase,
          borderRadius: BorderRadius.circular(UiConstants.borderRadius14),
        ),
      ),
    );
  }
}
