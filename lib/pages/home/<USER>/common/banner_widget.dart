/*
  首页横幅
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

import 'package:qubic_exchange/routes/index.dart';

class BannerWidget extends StatefulWidget {
  // 是否显示骨架屏
  final bool showSkeleton;
  const BannerWidget({super.key, this.showSkeleton = false});

  @override
  State<BannerWidget> createState() => _BannerWidgetState();
}

class _BannerWidgetState extends State<BannerWidget> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(UiConstants.spacing16),
      child: _buildUnauthenticatedView(),
    );
  }

  // 未登录状态视图
  Widget _buildUnauthenticatedView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildLottieAnimation(),
        SizedBox(height: UiConstants.spacing16),
        _buildTitleText(),
        Si<PERSON><PERSON>ox(height: UiConstants.spacing16),
        _buildLoginButton(),
      ],
    );
  }

  // 横幅图片
  Widget _buildLottieAnimation() {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(color: context.templateColors.surface),
      child: ThemedImage.asset(
        'banner_promotion',
        width: 200,
        height: 200,
        followTheme: true,
        showSkeleton: widget.showSkeleton,
      ),
    );
  }

  // 标题文本
  Widget _buildTitleText() {
    return Text(
      '6200 USDT 大礼包，注册即刻领取！',
      style: context.templateStyle.text.h2,
      textAlign: TextAlign.left,
    );
  }

  // 登录按钮
  Widget _buildLoginButton() {
    return CommonButton.primary(
      '注册/登录',
      height: 40,
      width: double.infinity,
      borderRadius: UiConstants.borderRadius8,
      onPressed: () {
        NavigationService().navigateTo(AppRoutes.register);
      },
    );
  }
}
