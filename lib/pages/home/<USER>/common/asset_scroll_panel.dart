/*
*  资产滚动面板
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class AssetScrollPanel extends StatefulWidget {
  // 是否有资产
  final bool hasAssets;

  const AssetScrollPanel({super.key, this.hasAssets = false});

  @override
  State<AssetScrollPanel> createState() => _AssetScrollPanelState();
}

class _AssetScrollPanelState extends State<AssetScrollPanel> {
  // 当前步骤
  int _currentStep = 1;

  // 构建菜单列表
  final List<Map<String, dynamic>> _menuItems = [
    {'imageName': 'icon_rewards_center', 'text': '福利中心'},
    {'imageName': 'icon_kcgi', 'text': 'KCGI'},
    {'imageName': 'icon_earn', 'text': '理财'},
    {'imageName': 'icon_menu_copy_trade', 'text': '跟单'},
    {'imageName': 'icon_onchain_trade', 'text': '链上交易'},
    {'imageName': 'icon_menu_more', 'text': '更多'},
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          // 资产部分
          _buildAssetSection(),

          // 横向滚动菜单
          _buildHorizontalMenu(),
        ],
      ),
    );
  }

  // 构建资产部分
  Widget _buildAssetSection() {
    /// 构建无资产
    Widget buildEmptyAsset() {
      return Container(
        padding: EdgeInsets.all(UiConstants.spacing18),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// 进度
            StepProgressWidget(
              steps: ['身份认证', '交易', '开始交易'],
              currentStep: _currentStep,
            ),

            /// 标题
            Padding(
              padding: EdgeInsets.symmetric(vertical: UiConstants.spacing18),
              child: Text(
                _currentStep == 1 ? '交易前需完成身份认证' : '充值资产，开启交易',
                style: context.templateStyle.text.h2,
              ),
            ),

            // 身份认证倒计时
            if (_currentStep == 1)
              Container(
                margin: EdgeInsets.only(bottom: UiConstants.spacing10),
                padding: EdgeInsets.all(UiConstants.spacing8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                    context.templateStyles.borderRadiusMedium,
                  ),
                  border: Border.all(
                    width: UiConstants.borderWidth0_5,
                    color: context.templateColors.divider,
                  ),
                ),
                child: Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text.rich(
                          TextSpan(
                            style: context.templateStyle.text.bodySmallMedium,
                            children: [
                              TextSpan(text: '身份认证可得'),
                              TextSpan(
                                text: '10000',
                                style:
                                    context.templateStyle.text.infoTextMedium,
                              ),
                              TextSpan(text: '福利金'),
                            ],
                          ),
                        ),

                        CountdownWidget(
                          totalSeconds: 300,
                          format: CountdownFormat.dayHourMinuteSecond,
                          textStyle: context.templateStyle.text.bodySmallMedium,
                          onFinished: () {
                            // TODO 倒计时完成执行事件
                          },
                        ),
                      ],
                    ),
                    Spacer(),
                    Icon(
                      RemixIcons.arrow_right_s_line,
                      size: UiConstants.spacing14,
                      color: context.templateColors.textSecondary,
                    ),
                  ],
                ),
              ),

            /// 按钮
            CommonButton.primary(
              _currentStep == 1 ? '去认证' : '去充值',
              height: 40,
              width: double.infinity,
              borderRadius: UiConstants.borderRadius8,
              onPressed: () {},
            ),
          ],
        ),
      );
    }

    /// 构建有资产
    Widget buildAssetContent() {
      return Container(
        padding: EdgeInsets.all(UiConstants.spacing18),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  '总资产估值',
                  style: context.templateStyle.text.descriptionLarge,
                ),
                SizedBox(width: UiConstants.spacing4),
                Icon(
                  RemixIcons.eye_fill,
                  size: UiConstants.iconSize16,
                  color: context.templateColors.textTertiary,
                ),
              ],
            ),
            SizedBox(height: UiConstants.spacing8),
            // 余额 + 充值
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            '1,234.56',
                            style: context.templateStyle.text.h1,
                          ),
                          SizedBox(width: UiConstants.spacing8),
                          Padding(
                            padding: EdgeInsets.only(top: UiConstants.spacing8),
                            child: InkWellWidget(
                              child: Row(
                                children: [
                                  Text(
                                    'BTC',
                                    style:
                                        context
                                            .templateStyle
                                            .text
                                            .bodyTextMedium,
                                  ),
                                  ThemedImage(
                                    name: 'arrow_triangle_down',
                                    size: UiConstants.iconSize12,
                                    followTheme: true,
                                    margin: EdgeInsets.only(
                                      left: UiConstants.spacing4,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      Text(
                        '≈ 1,234.56 CNY',
                        style: context.templateStyle.text.descriptionSmall,
                      ),
                    ],
                  ),
                ),
                CommonButton(
                  text: '去充值',
                  height: 36,
                  padding: EdgeInsetsGeometry.symmetric(
                    horizontal: UiConstants.spacing18,
                    vertical: 0.0,
                  ),
                  borderRadius: UiConstants.borderRadius8,
                  onPressed: () {},
                ),
              ],
            ),
          ],
        ),
      );
    }

    return widget.hasAssets ? buildAssetContent() : buildEmptyAsset();
  }

  // 构建横向滚动菜单
  Widget _buildHorizontalMenu() {
    /// 构建菜单项
    Widget buildMenuItem({required String imageName, required String text}) {
      return Container(
        margin: EdgeInsets.only(
          left: UiConstants.spacing18,
          right: UiConstants.spacing10,
        ),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(UiConstants.spacing12),
              decoration: BoxDecoration(
                color: context.templateColors.inputBackground,
                borderRadius: BorderRadius.circular(
                  context.templateStyles.borderRadiusMedium,
                ),
              ),
              child: ThemedImage(
                name: imageName,
                size: UiConstants.iconSize24,
                followTheme: true,
                showSkeleton: false,
              ),
            ),
            SizedBox(height: UiConstants.spacing8),
            Text(text, style: context.templateStyle.text.bodySmall),
          ],
        ),
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing10),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children:
              _menuItems
                  .map(
                    (key) => buildMenuItem(
                      imageName: key['imageName'],
                      text: key['text'],
                    ),
                  )
                  .toList(),
        ),
      ),
    );
  }
}
