/*
  首页活动类轮播
*/

import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:shimmer/shimmer.dart';

// 首页活动类轮播
class PromotionView extends StatefulWidget {
  final bool showSkeleton;

  const PromotionView({super.key, this.showSkeleton = false});

  @override
  State<PromotionView> createState() => _PromotionViewState();
}

class _PromotionViewState extends State<PromotionView> {
  late double imageSize = 80.0;
  int _currentIndex = 0;

  // 获取轮播数据
  List<dynamic> _getBannerData(BuildContext context) {
    return [
      {'image': '', 'title': '新人专享活动', 'subtitle': '注册即送体验金'},
      {'image': '', 'title': '限时优惠', 'subtitle': '交易手续费减免'},
      {'image': '', 'title': '节日特惠', 'subtitle': '瓜分百万奖池'},
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: context.templateColors.divider,
            width: UiConstants.borderWidth0_5,
          ),
        ),
      ),
      child:
          widget.showSkeleton
              ? _buildSkeletonView()
              : Stack(
                children: [
                  CarouselSlider(
                    items:
                        _getBannerData(context).asMap().entries.map((entry) {
                          return _buildSwiperItem(entry.value, entry.key);
                        }).toList(),
                    options: CarouselOptions(
                      height: imageSize + UiConstants.spacing8,
                      autoPlay: _getBannerData(context).length > 1,
                      autoPlayInterval: const Duration(seconds: 5),
                      autoPlayAnimationDuration: const Duration(
                        milliseconds: 800,
                      ),
                      enlargeCenterPage: true,
                      viewportFraction: 1.0,
                      onPageChanged: (index, reason) {
                        setState(() {
                          _currentIndex = index;
                        });
                      },
                    ),
                  ),
                  _buildIndexIndicator(context),
                ],
              ),
    );
  }

  // 轮播项
  Widget _buildSwiperItem(dynamic data, int index) {
    final containerSize = imageSize;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing16),
      child: ClipRRect(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: imageSize,
              height: imageSize,
              child: ThemedImage.asset(
                data['image'],
                width: imageSize,
                height: imageSize,
                showSkeleton: false,
              ),
            ),
            SizedBox(width: UiConstants.spacing16),
            Expanded(
              child: SizedBox(
                height: containerSize,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TextWidget(
                      text: data['title'],
                      style: context.templateStyle.text.descriptionSmall,
                    ),
                    SizedBox(height: UiConstants.spacing4),
                    TextWidget(
                      text: data['subtitle'],
                      style: context.templateStyle.text.bodyTextMedium,
                    ),
                    const Spacer(),
                    ViewMoreButton(
                      text: '查看详情',
                      style: context.templateStyle.text.bodySmallMedium,
                      showArrow: true,
                      onPressed: () => {},
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建索引指示器
  Widget _buildIndexIndicator(BuildContext context) {
    return Positioned(
      right: 0,
      bottom: -2,
      child: Container(
        margin: EdgeInsets.only(right: UiConstants.spacing16),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
          decoration: BoxDecoration(
            color: context.templateColors.surface,
            borderRadius: BorderRadius.circular(UiConstants.borderRadius4),
          ),
          child: Row(
            children: [
              Text.rich(
                TextSpan(
                  style: context.templateStyle.text.descriptionSmall,
                  children: [
                    TextSpan(
                      text: '${_currentIndex + 1}',
                      style: context.templateStyle.text.bodySmallMedium,
                    ),
                    TextSpan(text: '/${_getBannerData(context).length}'),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建骨架屏视图
  Widget _buildSkeletonView() {
    return Shimmer.fromColors(
      baseColor: context.templateColors.skeletonBase,
      highlightColor: context.templateColors.skeletonHighlight,
      child: Container(
        height: 100,
        decoration: BoxDecoration(color: context.templateColors.skeletonBase),
      ),
    );
  }
}
