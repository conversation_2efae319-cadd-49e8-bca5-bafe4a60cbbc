/*
*  首页
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:qubic_exchange/pages/main_tab/main_tab_page.dart';
import 'package:qubic_exchange/utils/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/models/index.dart';
import 'package:qubic_exchange/services/index.dart';
import 'widgets/common/index.dart';
import './widgets/content_board/index.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  // 最大下拉距离
  final maxOffset = 44.0;

  // 标签栏高度
  final consultationTabHeight = 44.0;

  // 构建资讯标签控制器
  late TabController _consultationTabController;

  // 首页数据服务
  final HomeDataService _homeDataService = HomeDataService();

  // 首页数据状态
  HomeDataModel _homeData = const HomeDataModel();

  // 构建资讯标签内容
  static const List<TabItem> _consultationTabKeys = [
    TabItem(title: '社区'),
    TabItem(title: '公告'),
    TabItem(title: '要闻'),
    TabItem(title: '活动'),
    TabItem(title: '快讯'),
  ];

  @override
  void initState() {
    super.initState();
    _consultationTabController = TabController(length: _consultationTabKeys.length, vsync: this);
    _initializeHomeData();
  }

  @override
  void dispose() {
    _consultationTabController.dispose();
    _homeDataService.stopAutoRefresh();
    super.dispose();
  }

  /// 初始化首页数据
  void _initializeHomeData() {
    // 监听数据变化
    _homeDataService.dataStream.listen((data) {
      if (mounted) {
        setState(() {
          _homeData = data;
        });
      }
    });

    // 检查服务是否已经初始化过
    if (_homeDataService.currentData.status == HomeDataStatus.initial) {
      // 首次初始化服务
      _homeDataService.initialize();
    } else {
      // 服务已初始化，直接使用现有数据并恢复自动刷新
      setState(() {
        _homeData = _homeDataService.currentData;
      });
      // 恢复自动刷新（如果之前被停止了）
      _homeDataService.initialize();
    }
  }

  // 获取用户登录情况
  bool get isUserLoggedIn => context.read<AuthProvider>().isLoggedIn;

  // 保持页面状态
  @override
  bool get wantKeepAlive => true;

  // 下拉刷新
  Future<void> _onRefresh() async {
    try {
      // 检查组件是否已销毁
      if (!mounted) {
        debugPrint('⚠️ 组件已销毁，跳过刷新操作');
        return;
      }

      // 检查TabController是否正在动画中，避免冲突
      if (_consultationTabController.indexIsChanging) {
        debugPrint('⚠️ TabController正在切换中，跳过刷新操作');
        return;
      }

      // 根据登录状态执行不同的刷新逻辑
      if (isUserLoggedIn) {
        debugPrint('🔄 用户已登录，执行完整数据刷新');
        await _homeDataService.refreshData();
      } else {
        debugPrint('🔄 用户未登录，执行基础数据刷新');
        // 模拟刷新操作
        await Future.delayed(const Duration(seconds: 1));
      }
    } catch (e) {
      debugPrint('❌ 刷新失败: $e');
      // 确保刷新操作有明确的结束
    }
  }

  // 处理查看更多点击
  void _handleViewMore() {
    // 通过globalKey切换到行情tab
    final mainTabState = MainTabPage.globalKey.currentState;
    if (mainTabState != null) {
      (mainTabState as dynamic).switchToMarketTab();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 调用父类的 build 方法
    return Scaffold(backgroundColor: context.templateColors.surface, appBar: AppBarView(), body: SafeArea(child: _buildPageView()));
  }

  // 构建页面视图
  Widget _buildPageView() {
    return EasyRefresh.builder(
      header: ClassicHeader(
        clamping: true,
        position: IndicatorPosition.locator,
        triggerOffset: maxOffset,
        processedDuration: const Duration(seconds: 1),
        safeArea: false,
        showMessage: false,
        showText: false,
        maxOverOffset: maxOffset,
      ),
      onRefresh: _onRefresh,
      childBuilder: (context, physics) {
        return isUserLoggedIn
            ? ScrollConfiguration(
              behavior: const ERScrollBehavior(),
              child: ExtendedNestedScrollView(
                physics: physics,
                onlyOneScrollInBody: true,
                pinnedHeaderSliverHeightBuilder: () {
                  return consultationTabHeight;
                },
                headerSliverBuilder: (context, innerBoxIsScrolled) {
                  return <Widget>[
                    // 刷新指示器定位器
                    const HeaderLocator.sliver(clearExtent: false),
                    SliverToBoxAdapter(child: _buildHeader()),
                    // 资讯标签栏
                    SliverPersistentHeader(
                      pinned: true,
                      delegate: StickyDelegate(
                        maxHeight: consultationTabHeight,
                        child: _buildConsultationTabBar(),
                        backgroundColor: context.templateColors.surface,
                      ),
                    ),
                  ];
                },
                body: _buildConsultationContent(physics),
              ),
            )
            : ScrollConfiguration(
              behavior: const ERScrollBehavior(),
              child: ExtendedNestedScrollView(
                physics: physics,
                onlyOneScrollInBody: true,
                pinnedHeaderSliverHeightBuilder: () {
                  return consultationTabHeight;
                },
                headerSliverBuilder: (context, innerBoxIsScrolled) {
                  return <Widget>[
                    // 刷新指示器定位器
                    const HeaderLocator.sliver(clearExtent: false),
                  ];
                },
                body: SingleChildScrollView(
                  physics: physics,
                  child: Column(children: [_buildHeader(), SecurityGuard(), SizedBox(height: ScreenUtil.screenHeight(context) * 0.1)]),
                ),
              ),
            );
      },
    );
  }

  // 构建头部
  Widget _buildHeader() {
    return Column(
      children: [
        // 轮播图 | 资产
        Builder(
          builder: (context) {
            final isLoggedIn = context.read<AuthProvider>().isLoggedIn;
            return isLoggedIn ? const AssetScrollPanel(hasAssets: false) : const BannerWidget();
          },
        ),
        // 促销活动
        const PromotionView(),
        // 币类激励
        const CryptoView(),
        // 货币看板
        if (isUserLoggedIn && _homeData.hasData)
          TokenBoardSection(tokenBoardData: _homeData.tokenBoardData!, onTokenTap: (token) {}, onViewMore: _handleViewMore),
      ],
    );
  }

  // 构建资讯标签栏
  Widget _buildConsultationTabBar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(width: context.templateStyles.borderWidthThin, color: context.templateColors.divider)),
      ),
      child: TabbarWidget(
        height: consultationTabHeight,
        controller: _consultationTabController,
        tabs: _consultationTabKeys,
        labelStyle: context.templateStyle.text.tabText,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: true,
        indicatorSize: TabBarIndicatorSize.label,
        labelPadding: EdgeInsets.only(right: UiConstants.spacing14),
      ),
    );
  }

  // 构建资讯内容
  Widget _buildConsultationContent(ScrollPhysics physics) {
    return TabBarView(
      controller: _consultationTabController,
      children: [
        // 社区
        _AutomaticKeepAlive(child: CommunityHomePage(physics: physics)),
        // 公告
        _AutomaticKeepAlive(child: AnnouncementSection(physics: physics)),
        // 要闻
        _AutomaticKeepAlive(child: HeadlineSection(physics: physics)),
        // 活动
        _AutomaticKeepAlive(child: ActivitySection(physics: physics)),
        // 快讯
        _AutomaticKeepAlive(child: FlashNewsSection(physics: physics)),
      ],
    );
  }
}

/// 自动保持状态的包装组件
class _AutomaticKeepAlive extends StatefulWidget {
  final Widget child;

  const _AutomaticKeepAlive({required this.child});

  @override
  State<_AutomaticKeepAlive> createState() => _AutomaticKeepAliveState();
}

class _AutomaticKeepAliveState extends State<_AutomaticKeepAlive> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }
}
