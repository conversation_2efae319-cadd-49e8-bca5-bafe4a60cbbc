/*
* 代币数据转换工具
* 将不同数据源转换为 TokenBoardSection 需要的数据格式
*/

import '../../../pages/market/models/crypto_item_data.dart';

class TokenDataConverter {
  /// 将 CryptoItemData 列表转换为 TokenBoardSection 数据格式
  static Map<String, Map<String, List<Map<String, dynamic>>>>
  convertFromCryptoItemData(List<CryptoItemData> cryptoData) {
    // 将 CryptoItemData 转换为通用格式
    final List<Map<String, dynamic>> convertedData =
        cryptoData.map((item) {
          return {
            'symbol': item.symbol,
            'name': item.fullSymbolSpot,
            'price': '\$${item.formattedPrice}',
            'changeAmount': _calculateChangeAmount(
              item.changePercentage,
              item.currentPrice,
            ),
            'changePercentage': item.changePercentage,
            'volume': item.formattedVolume,
            'isUp': item.isPriceIncreasing,
            'tradingTag': item.tradingTag,
            'quoteCurrency': item.quoteCurrency,
            'currentPrice': item.currentPrice,
            'cnyPrice': item.cnyPrice,
            'volume24h': item.volume24h,
            'volumeUnit': item.volumeUnit,
            'lastUpdated': item.lastUpdated.toIso8601String(),
          };
        }).toList();

    // 按照不同标签分类数据
    return {
      '自选': {
        '现货': _filterForFavorites(convertedData),
        '合约': _filterForContracts(convertedData),
      },
      '热门': {
        '现货': _filterForSpot(convertedData),
        '合约': _filterForContracts(convertedData),
        '链上交易': _filterForOnChain(convertedData),
      },
      '涨幅榜': {
        '现货': _filterForGainers(convertedData),
        '合约': _filterForGainers(_filterForContracts(convertedData)),
        '链上交易': _filterForGainers(_filterForOnChain(convertedData)),
      },
      '跌幅榜': {
        '现货': _filterForLosers(convertedData),
        '合约': _filterForLosers(_filterForContracts(convertedData)),
        '链上交易': _filterForLosers(_filterForOnChain(convertedData)),
      },
      '新币榜': {
        '现货': _filterForNewCoins(convertedData),
        '合约': _filterForNewCoins(_filterForContracts(convertedData)),
        '链上交易': _filterForNewCoins(_filterForOnChain(convertedData)),
      },
      '成交额榜': {
        '现货': _filterByVolume(convertedData),
        '合约': _filterByVolume(_filterForContracts(convertedData)),
        '链上交易': _filterByVolume(_filterForOnChain(convertedData)),
      },
    };
  }

  /// 计算涨跌额
  static double _calculateChangeAmount(
    double changePercentage,
    double currentPrice,
  ) {
    return currentPrice * changePercentage / 100;
  }

  /// 过滤自选数据（取前几个作为示例）
  static List<Map<String, dynamic>> _filterForFavorites(
    List<Map<String, dynamic>> data,
  ) {
    return data.take(3).toList();
  }

  /// 过滤现货数据
  static List<Map<String, dynamic>> _filterForSpot(
    List<Map<String, dynamic>> data,
  ) {
    return data
        .where(
          (item) => item['tradingTag'] == null || item['tradingTag'] != '永续',
        )
        .toList();
  }

  /// 过滤合约数据
  static List<Map<String, dynamic>> _filterForContracts(
    List<Map<String, dynamic>> data,
  ) {
    return data.where((item) => item['tradingTag'] == '永续').toList();
  }

  /// 过滤链上交易数据（模拟，实际应该有专门的标识）
  static List<Map<String, dynamic>> _filterForOnChain(
    List<Map<String, dynamic>> data,
  ) {
    // 这里模拟链上交易数据，实际应该根据具体的标识字段过滤
    final onChainSymbols = ['UNI', 'AAVE', 'COMP', 'MKR', 'SNX'];
    return data
        .where((item) => onChainSymbols.contains(item['symbol']))
        .toList();
  }

  /// 过滤涨幅榜数据
  static List<Map<String, dynamic>> _filterForGainers(
    List<Map<String, dynamic>> data,
  ) {
    return data.where((item) => item['isUp'] == true).toList()..sort(
      (a, b) => (b['changePercent'] as String)
          .replaceAll('%', '')
          .replaceAll('+', '')
          .compareTo(
            (a['changePercent'] as String)
                .replaceAll('%', '')
                .replaceAll('+', ''),
          ),
    );
  }

  /// 过滤跌幅榜数据
  static List<Map<String, dynamic>> _filterForLosers(
    List<Map<String, dynamic>> data,
  ) {
    return data.where((item) => item['isUp'] == false).toList()..sort(
      (a, b) => (a['changePercent'] as String)
          .replaceAll('%', '')
          .replaceAll('-', '')
          .compareTo(
            (b['changePercent'] as String)
                .replaceAll('%', '')
                .replaceAll('-', ''),
          ),
    );
  }

  /// 过滤新币榜数据（按上市时间，这里模拟取最新的几个）
  static List<Map<String, dynamic>> _filterForNewCoins(
    List<Map<String, dynamic>> data,
  ) {
    return data.take(5).toList();
  }

  /// 按成交额排序
  static List<Map<String, dynamic>> _filterByVolume(
    List<Map<String, dynamic>> data,
  ) {
    return data.toList()..sort((a, b) {
      final aVolume = _parseVolume(a['volume'] as String);
      final bVolume = _parseVolume(b['volume'] as String);
      return bVolume.compareTo(aVolume);
    });
  }

  /// 解析成交量字符串为数值（用于排序）
  static double _parseVolume(String volumeStr) {
    final numStr = volumeStr.replaceAll(RegExp(r'[^\d.]'), '');
    final num = double.tryParse(numStr) ?? 0;

    if (volumeStr.contains('亿')) {
      return num * 100000000;
    } else if (volumeStr.contains('万')) {
      return num * 10000;
    }
    return num;
  }

  /// 创建示例数据（用于测试）
  static Map<String, Map<String, List<Map<String, dynamic>>>>
  createSampleData() {
    final sampleCryptoData = [
      CryptoItemData(
        symbol: 'BTC',
        quoteCurrency: 'USDT',
        currentPrice: 43250.50,
        cnyPrice: 311244.60,
        changePercentage: 2.45,
        volume24h: 1.2,
        volumeUnit: '亿',
        tradingTag: '永续',
      ),
      CryptoItemData(
        symbol: 'ETH',
        quoteCurrency: 'USDT',
        currentPrice: 2650.30,
        cnyPrice: 19082.16,
        changePercentage: -1.23,
        volume24h: 8.5,
        volumeUnit: '万',
      ),
      CryptoItemData(
        symbol: 'BNB',
        quoteCurrency: 'USDT',
        currentPrice: 315.80,
        cnyPrice: 2273.76,
        changePercentage: 0.85,
        volume24h: 2.3,
        volumeUnit: '万',
      ),
      CryptoItemData(
        symbol: 'ADA',
        quoteCurrency: 'USDT',
        currentPrice: 0.4520,
        cnyPrice: 3.2544,
        changePercentage: 3.21,
        volume24h: 1.8,
        volumeUnit: '万',
      ),
      CryptoItemData(
        symbol: 'SOL',
        quoteCurrency: 'USDT',
        currentPrice: 98.75,
        cnyPrice: 711.00,
        changePercentage: -2.15,
        volume24h: 5.2,
        volumeUnit: '万',
      ),
      CryptoItemData(
        symbol: 'UNI',
        quoteCurrency: 'USDT',
        currentPrice: 8.45,
        cnyPrice: 60.84,
        changePercentage: 5.67,
        volume24h: 3.1,
        volumeUnit: '万',
      ),
    ];

    return convertFromCryptoItemData(sampleCryptoData);
  }
}
