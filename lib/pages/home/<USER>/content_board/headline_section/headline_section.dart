/*
*  要闻标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';

import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/services/clients/dio_request.dart';
import 'package:qubic_exchange/services/config/api_route.dart';
import 'package:qubic_exchange/models/article_model.dart';
import 'package:qubic_exchange/services/common/common_service.dart';

class HeadlineSection extends StatefulWidget {
  final ScrollPhysics? physics;

  const HeadlineSection({super.key, this.physics});

  @override
  State<HeadlineSection> createState() => _HeadlineSectionState();
}

class _HeadlineSectionState extends State<HeadlineSection> {
  // 文章数据
  List<ArticleModel> _articles = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _fetchArticles();
  }

  // 获取文章数据
  Future<void> _fetchArticles() async {
    try {
      final response = await DioRequest.instance.get(
        ApiRoute.articleList,
        queryParams: {'category_id': 19, 'page': 1, 'page_size': 20},
        requireAuth: true,
      );

      if (response.data != null) {
        final responseData = response.data;
        if (responseData is Map<String, dynamic> && responseData.containsKey('list')) {
          final listData = responseData['list'];
          if (listData is List) {
            final articles = <ArticleModel>[];

            for (final item in listData) {
              try {
                if (item is Map<String, dynamic>) {
                  articles.add(ArticleModel.fromJson(item));
                }
              } catch (e) {
                debugPrint('解析单条文章数据失败: $e, 数据: $item');
              }
            }

            _articles = articles;
          }
        }
      }
    } catch (e) {
      debugPrint('获取要闻数据失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_articles.isEmpty) {
      return const Center(child: Padding(padding: EdgeInsets.all(32.0), child: Text('暂无要闻数据')));
    }

    return ListView.builder(
      physics: widget.physics,
      itemCount: _articles.length,
      itemBuilder: (context, index) {
        final article = _articles[index];
        return _buildItem(
          title: article.localizedTitle,
          time: CommonService.formatTimeAgo(article.createdAt),
          imagePath: index % 3 == 0 ? 'icon_contract_list_hot' : '',
        );
      },
    );
  }

  // 构建要闻项
  Widget _buildItem({required String title, required String time, String imagePath = ''}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18, vertical: UiConstants.spacing10),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: context.templateColors.divider, width: context.templateStyles.borderWidthThin)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: context.templateStyle.text.bodyLargeMedium, maxLines: 2, overflow: TextOverflow.ellipsis),
                SizedBox(height: UiConstants.spacing8),
                Text(time, style: context.templateStyle.text.descriptionSmall),
              ],
            ),
          ),
          if (imagePath.isNotEmpty)
            ThemedImage(name: imagePath, width: 80, height: 60, followTheme: true, margin: EdgeInsets.only(left: UiConstants.spacing16)),
        ],
      ),
    );
  }
}
