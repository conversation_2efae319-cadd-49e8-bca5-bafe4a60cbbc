/*
*  热门话题
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/services/clients/dio_request.dart';
import 'package:qubic_exchange/services/config/api_route.dart';

import 'package:remixicon/remixicon.dart';

// 热门话题数据模型
class HotTopicModel {
  final int id;
  final List<int> currency;
  final String title;
  final String content;
  final int lookNum;
  final int createdBy;
  final int updatedBy;
  final String createdAt;
  final String updatedAt;

  HotTopicModel({
    required this.id,
    required this.currency,
    required this.title,
    required this.content,
    required this.lookNum,
    required this.createdBy,
    required this.updatedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory HotTopicModel.fromJson(Map<String, dynamic> json) {
    return HotTopicModel(
      id: json['id'] ?? 0,
      currency: List<int>.from(json['currency'] ?? []),
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      lookNum: json['look_num'] ?? 0,
      createdBy: json['created_by'] ?? 0,
      updatedBy: json['updated_by'] ?? 0,
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
    );
  }
}

class HotTopics extends StatefulWidget {
  const HotTopics({super.key});

  @override
  State<HotTopics> createState() => _HotTopicsState();
}

class _HotTopicsState extends State<HotTopics> {
  List<HotTopicModel> _hotTopics = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _fetchHotTopics();
  }

  // 获取热门话题数据
  Future<void> _fetchHotTopics() async {
    try {
      final response = await DioRequest.instance.get(ApiRoute.hotTopicList, requireAuth: true);
      if (response.data != null && response.data['list'] != null) {
        final List<dynamic> list = response.data['list'];
        _hotTopics = list.map((item) => HotTopicModel.fromJson(item)).toList();
      }
    } catch (e) {
      debugPrint('获取热门话题失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18, vertical: UiConstants.spacing10),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(width: context.templateStyles.borderWidthThin, color: context.templateColors.divider)),
      ),
      child: Column(
        children: [
          _buildTitle(),
          if (_isLoading) const Center(child: CircularProgressIndicator()) else ..._hotTopics.map((topic) => _buildItem(topic)),
        ],
      ),
    );
  }

  // 构建标题部分
  Widget _buildTitle() {
    return Padding(
      padding: EdgeInsets.only(bottom: UiConstants.spacing10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text('热门话题', style: context.templateStyle.text.bodyLargeMedium),
          Icon(RemixIcons.arrow_right_s_line, size: UiConstants.iconSize20, color: context.templateColors.textSecondary),
        ],
      ),
    );
  }

  // 构建话题项
  Widget _buildItem(HotTopicModel topic) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing4),
      child: Row(
        children: [
          Icon(RemixIcons.hashtag, color: context.templateColors.primary, size: UiConstants.iconSize16, weight: 800),
          SizedBox(width: UiConstants.spacing4),
          Flexible(
            child: Text(topic.title, style: context.templateStyle.text.bodyTextMedium, overflow: TextOverflow.ellipsis, maxLines: 1),
          ),
          SizedBox(width: UiConstants.spacing8),
          ThemedImage.asset('icon_contract_list_hot', size: UiConstants.iconSize14, format: ImageFormat.webp),
        ],
      ),
    );
  }
}
