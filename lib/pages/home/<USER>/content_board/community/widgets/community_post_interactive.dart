/*
*  社区动态项
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/models/pages/home/<USER>';

class CommunityPostInteractive extends StatefulWidget {
  final CommunityPostModel? postModel;
  final String? userAvatar;
  final String userName;
  final String publishTime;
  final String content;
  final List<String>? images;
  final int likeCount;
  final int commentCount;
  final int shareCount;
  final bool isLiked;
  final VoidCallback? onLike;
  final VoidCallback? onComment;
  final VoidCallback? onShare;
  final VoidCallback? onUserTap;

  const CommunityPostInteractive({
    super.key,
    this.postModel,
    this.userAvatar,
    this.userName = 'Bitget_Insights',
    this.publishTime = '19小时前',
    this.content = '',
    this.images,
    this.likeCount = 56,
    this.commentCount = 54,
    this.shareCount = 1,
    this.isLiked = false,
    this.onLike,
    this.onComment,
    this.onShare,
    this.onUserTap,
  });

  // 使用数据模型的构造函数
  CommunityPostInteractive.fromModel({
    super.key,
    required CommunityPostModel postModel,
    this.onLike,
    this.onComment,
    this.onShare,
    this.onUserTap,
  }) : postModel = postModel,
       userAvatar = postModel.userAvatar,
       userName = postModel.userName,
       publishTime = postModel.publishTime,
       content = postModel.content,
       images = postModel.images,
       likeCount = postModel.likeCount,
       commentCount = postModel.commentCount,
       shareCount = postModel.shareCount,
       isLiked = postModel.isLiked;

  @override
  State<CommunityPostInteractive> createState() =>
      _CommunityPostInteractiveState();
}

class _CommunityPostInteractiveState extends State<CommunityPostInteractive> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing16,
        vertical: UiConstants.spacing12,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: context.templateColors.divider,
            width: context.templateStyles.borderWidthThin,
          ),
        ),
      ),
      child: Column(
        children: [_buildHeader(), _buildContent(), _buildToggle()],
      ),
    );
  }

  // 构建头部
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing12),
      child: Row(
        children: [
          // 用户头像
          GestureDetector(
            onTap: widget.onUserTap,
            child: Container(
              width: UiConstants.iconSize40,
              height: UiConstants.iconSize40,
              margin: EdgeInsets.only(right: UiConstants.spacing12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
                color: context.templateColors.primary,
              ),
              child:
                  widget.userAvatar != null
                      ? ThemedImage(
                        name: widget.userAvatar!,
                        size: UiConstants.iconSize40,
                        borderRadius: BorderRadius.circular(
                          UiConstants.borderRadius8,
                        ),
                      )
                      : Icon(
                        Icons.person,
                        color: Colors.white,
                        size: UiConstants.iconSize24,
                      ),
            ),
          ),
          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.userName,
                  style: context.templateStyle.text.bodyTextMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: UiConstants.spacing2),
                Text(
                  widget.publishTime,
                  style: context.templateStyle.text.descriptionSmall.copyWith(
                    color: context.templateColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建内容
  Widget _buildContent() {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 文本内容
          if (widget.content.isNotEmpty)
            Text(
              widget.content,
              style: context.templateStyle.text.bodyText.copyWith(height: 1.4),
            ),

          // 图片内容
          if (widget.images != null && widget.images!.isNotEmpty)
            Container(
              margin: EdgeInsets.only(top: UiConstants.spacing12),
              child: _buildImageGrid(),
            ),
        ],
      ),
    );
  }

  // 构建图片网格
  Widget _buildImageGrid() {
    if (widget.images == null || widget.images!.isEmpty) {
      return const SizedBox.shrink();
    }

    final images = widget.images!;

    // 单张图片
    if (images.length == 1) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
        child: ThemedImage(
          name: images[0],
          width: double.infinity,
          height: 200,
          fit: BoxFit.cover,
        ),
      );
    }

    // 多张图片网格布局
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: images.length == 2 ? 2 : 3,
        crossAxisSpacing: UiConstants.spacing8,
        mainAxisSpacing: UiConstants.spacing8,
        childAspectRatio: 1.0,
      ),
      itemCount: images.length > 9 ? 9 : images.length,
      itemBuilder: (context, index) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
          child: ThemedImage(name: images[index], fit: BoxFit.cover),
        );
      },
    );
  }

  // 构建底部操作
  Widget _buildToggle() {
    return Row(
      children: [
        // 点赞按钮
        _buildActionButton(
          iconName: 'thumb_up',
          count: widget.likeCount,
          isActive: widget.isLiked,
          onTap: widget.onLike,
        ),

        // 评论按钮
        _buildActionButton(
          iconName: 'chat_bubble_outline',
          count: widget.commentCount,
          onTap: widget.onComment,
        ),

        const Spacer(),

        // 分享按钮
        _buildActionButton(
          iconName: 'share',
          count: widget.shareCount,
          onTap: widget.onShare,
          alignment: MainAxisAlignment.end,
        ),
      ],
    );
  }

  // 构建操作按钮
  Widget _buildActionButton({
    required String iconName,
    required int count,
    bool isActive = false,
    VoidCallback? onTap,
    MainAxisAlignment alignment = MainAxisAlignment.start,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing8,
          vertical: UiConstants.spacing4,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: alignment,
          children: [
            Icon(
              _getIconData(iconName),
              size: UiConstants.iconSize16,
              color:
                  isActive
                      ? context.templateColors.primary
                      : context.templateColors.textSecondary,
            ),
            if (count > 0) ...[
              SizedBox(width: UiConstants.spacing4),
              Text(
                _formatCount(count),
                style: context.templateStyle.text.descriptionSmall.copyWith(
                  color:
                      isActive
                          ? context.templateColors.primary
                          : context.templateColors.textSecondary,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // 获取图标数据
  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'thumb_up':
        return Icons.thumb_up_outlined;
      case 'chat_bubble_outline':
        return Icons.chat_bubble_outline;
      case 'share':
        return Icons.share_outlined;
      default:
        return Icons.help_outline;
    }
  }

  // 格式化数量显示
  String _formatCount(int count) {
    if (count < 1000) {
      return count.toString();
    } else if (count < 10000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    } else {
      return '${(count / 10000).toStringAsFixed(1)}w';
    }
  }
}
