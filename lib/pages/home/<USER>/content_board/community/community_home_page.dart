/*
*  社区版块
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/utils/index.dart';
import 'package:qubic_exchange/services/clients/dio_request.dart';
import 'package:qubic_exchange/services/config/api_route.dart';
import 'package:qubic_exchange/models/dynamics_model.dart';
import './widgets/hot_topics.dart';
import './widgets/community_post_interactive.dart';

class CommunityHomePage extends StatefulWidget {
  final ScrollPhysics? physics;

  const CommunityHomePage({super.key, this.physics});

  @override
  State<CommunityHomePage> createState() => _CommunityHomePageState();
}

class _CommunityHomePageState extends State<CommunityHomePage> {
  final ScrollController _scrollController = ScrollController();
  List<DynamicsModel> _dynamicsList = [];
  int _currentPage = 1;
  static const int _pageSize = 10;
  bool _isLoading = false;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _fetchDynamics();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // 滚动监听
  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading && _hasMore) {
        debugPrint('触发加载更多，当前页: $_currentPage');
        _loadMore();
      }
    }
  }

  // 获取动态数据
  Future<void> _fetchDynamics({bool isRefresh = false}) async {
    if (_isLoading) return;

    debugPrint('开始获取动态数据，页码: ${isRefresh ? 1 : _currentPage}, 刷新: $isRefresh');

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await DioRequest.instance.get(
        ApiRoute.dynamicsList,
        queryParams: {'page': isRefresh ? 1 : _currentPage, 'page_size': _pageSize, 'type': 2},
        requireAuth: true,
      );
      if (response.data != null) {
        final responseData = response.data;
        if (responseData is Map<String, dynamic> && responseData.containsKey('list')) {
          final listData = responseData['list'];
          if (listData is List) {
            final newDynamics = <DynamicsModel>[];

            for (final item in listData) {
              try {
                if (item is Map<String, dynamic>) {
                  newDynamics.add(DynamicsModel.fromJson(item));
                }
              } catch (e) {
                debugPrint('解析单条动态数据失败: $e, 数据: $item');
              }
            }

            if (isRefresh) {
              _dynamicsList = newDynamics;
              _currentPage = 2;
              debugPrint('刷新完成，获取到 ${newDynamics.length} 条数据，下一页: $_currentPage');
            } else {
              _dynamicsList.addAll(newDynamics);
              _currentPage++;
              debugPrint('加载更多完成，新增 ${newDynamics.length} 条数据，总数: ${_dynamicsList.length}，下一页: $_currentPage');
            }

            _hasMore = newDynamics.length == _pageSize;
            debugPrint('是否还有更多数据: $_hasMore');
          }
        }
      }
    } catch (e, stackTrace) {
      debugPrint('获取动态数据失败: $e');
      debugPrint('堆栈信息: $stackTrace');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 加载更多
  Future<void> _loadMore() async {
    await _fetchDynamics();
  }

  @override
  Widget build(BuildContext context) {
    return ListView(
      controller: _scrollController,
      physics: widget.physics,
      children: [
        // 热门话题
        const HotTopics(),

        // 社区动态列表
        ..._buildCommunityPosts(),

        // 加载更多指示器
        if (_isLoading && _dynamicsList.isNotEmpty)
          const Padding(padding: EdgeInsets.all(16.0), child: Center(child: CircularProgressIndicator())),

        // 底部安全区域
        SizedBox(height: ScreenUtil.screenHeight(context) * 0.1),
      ],
    );
  }

  // 构建社区动态列表
  List<Widget> _buildCommunityPosts() {
    if (_dynamicsList.isEmpty && _isLoading) {
      return [const Padding(padding: EdgeInsets.all(32.0), child: Center(child: CircularProgressIndicator()))];
    }

    if (_dynamicsList.isEmpty && !_isLoading) {
      return [const Padding(padding: EdgeInsets.all(32.0), child: Center(child: Text('暂无动态数据')))];
    }

    return _dynamicsList.asMap().entries.map((entry) {
      final index = entry.key;
      final dynamic = entry.value;

      return CommunityPostInteractive(
        userName: dynamic.userName,
        publishTime: _formatTime(dynamic.publishTime),
        content: dynamic.formattedContent,
        images: dynamic.images,
        likeCount: dynamic.likeCount,
        commentCount: dynamic.lookNum, // 使用look_num作为评论数
        shareCount: 0, // API中没有分享数，暂时设为0
        isLiked: dynamic.isLiked,
        onLike: () => _handleLike(index),
        onComment: () => _handleComment(index),
        onShare: () => _handleShare(index),
        onUserTap: () => _handleUserTap(dynamic.userName),
      );
    }).toList();
  }

  // 格式化时间显示
  String _formatTime(String timeStr) {
    try {
      final dateTime = DateTime.parse(timeStr);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays > 0) {
        return '${difference.inDays}天前';
      } else if (difference.inHours > 0) {
        return '${difference.inHours}小时前';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes}分钟前';
      } else {
        return '刚刚';
      }
    } catch (e) {
      return timeStr;
    }
  }

  // 处理点赞
  void _handleLike(int postIndex) {
    print('点赞动态 $postIndex');
    // TODO: 实现点赞逻辑
  }

  // 处理评论
  void _handleComment(int postIndex) {
    print('评论动态 $postIndex');
    // TODO: 实现评论逻辑
  }

  // 处理分享
  void _handleShare(int postIndex) {
    print('分享动态 $postIndex');
    // TODO: 实现分享逻辑
  }

  // 处理用户点击
  void _handleUserTap(String userName) {
    print('点击用户: $userName');
    // TODO: 跳转到用户主页
  }
}
