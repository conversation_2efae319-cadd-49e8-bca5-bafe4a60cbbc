/*
*  公告标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/services/clients/dio_request.dart';
import 'package:qubic_exchange/services/config/api_route.dart';
import 'package:qubic_exchange/models/category_model.dart';
import 'package:qubic_exchange/models/notice_model.dart';

class AnnouncementSection extends StatefulWidget {
  final ScrollPhysics? physics;

  const AnnouncementSection({super.key, this.physics});

  @override
  State<AnnouncementSection> createState() => _AnnouncementSectionState();
}

class _AnnouncementSectionState extends State<AnnouncementSection> with TickerProviderStateMixin {
  // 构建标签控制器
  late TabController _tabController;

  // 创建标签项
  List<TabItem> _tabKeys = [TabItem(title: '全部')];

  // 分类数据
  List<CategoryModel> _categories = [];
  bool _isLoading = true;

  // 公告数据
  List<NoticeModel> _notices = [];
  bool _isLoadingNotices = false;

  // 初始化
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 1, vsync: this);
    _fetchCategories();
  }

  // 获取分类数据
  Future<void> _fetchCategories() async {
    try {
      final response = await DioRequest.instance.get(ApiRoute.categoryList, queryParams: {"key": "notice"}, requireAuth: true);

      if (response.data != null && response.data is List) {
        final List<dynamic> list = response.data;
        _categories = list.map((item) => CategoryModel.fromJson(item)).toList();

        // 更新标签列表
        _updateTabKeys();
      }
    } catch (e) {
      debugPrint('获取分类数据失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 更新标签列表
  void _updateTabKeys() {
    final newTabKeys = <TabItem>[TabItem(title: '全部')];

    // 添加分类标签
    for (final category in _categories) {
      newTabKeys.add(TabItem(title: category.displayName));
    }
    // 重新创建TabController
    _tabController.dispose();
    _tabController = TabController(length: newTabKeys.length, vsync: this);

    // 添加标签切换监听器
    _tabController.addListener(_onTabChanged);

    _tabKeys = newTabKeys;

    // 获取初始公告数据（全部）
    _fetchNotices();

    // 强制重建整个Widget
    if (mounted) {
      setState(() {});
    }
  }

  // 标签切换监听
  void _onTabChanged() {
    if (!_tabController.indexIsChanging) {
      _fetchNotices();
    }
  }

  // 获取公告数据
  Future<void> _fetchNotices() async {
    if (_isLoadingNotices) return;

    setState(() {
      _isLoadingNotices = true;
    });

    try {
      // 获取当前选中的分类ID
      int? categoryId;
      if (_tabController.index > 0 && _tabController.index <= _categories.length) {
        categoryId = _categories[_tabController.index - 1].id;
      }

      final response = await DioRequest.instance.get(
        ApiRoute.noticeList,
        queryParams: {if (categoryId != null) 'category_id': categoryId, 'page_size': 20},
        requireAuth: true,
      );

      if (response.data != null) {
        final responseData = response.data;
        if (responseData is Map<String, dynamic> && responseData.containsKey('list')) {
          final listData = responseData['list'];
          if (listData is List) {
            final notices = <NoticeModel>[];

            for (final item in listData) {
              try {
                if (item is Map<String, dynamic>) {
                  notices.add(NoticeModel.fromJson(item));
                }
              } catch (e) {
                debugPrint('解析单条公告数据失败: $e, 数据: $item');
              }
            }

            _notices = notices;
          }
        }
      }
    } catch (e) {
      debugPrint('获取公告数据失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingNotices = false;
        });
      }
    }
  }

  // 销毁
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // 构建 UI 内容
  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // TabBar
        _buildTabbar(),
        // TabBarView
        Expanded(child: TabBarView(controller: _tabController, children: _tabKeys.map((tab) => _buildAnnouncementList()).toList())),
      ],
    );
  }

  // 创建标签栏
  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18, vertical: UiConstants.spacing4),
      child: TabbarWidget(
        height: 24,
        controller: _tabController,
        tabs: _tabKeys,
        labelStyle: context.templateStyle.text.bodySmallMedium,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textTertiary,
        showIndicator: true,
        indicatorColor: context.templateColors.tabbarActive,
        indicatorStyle: TabBarIndicatorStyle.filled,
        tabPadding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
      ),
    );
  }

  // 构建公告列表
  Widget _buildAnnouncementList() {
    if (_isLoadingNotices) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_notices.isEmpty) {
      return const Center(child: Padding(padding: EdgeInsets.all(32.0), child: Text('暂无公告数据')));
    }

    return ListView.builder(
      physics: widget.physics,
      itemCount: _notices.length,
      itemBuilder: (context, index) {
        final notice = _notices[index];
        return _buildItem(
          title: notice.localizedTitle,
          content: _stripHtmlTags(notice.localizedContent),
          time: _formatTime(notice.createdAt),
          imagePath: notice.lookNum > 10 ? 'icon_contract_list_hot' : '',
        );
      },
    );
  }

  // 移除HTML标签
  String _stripHtmlTags(String htmlString) {
    final RegExp exp = RegExp(r"<[^>]*>", multiLine: true, caseSensitive: true);
    return htmlString.replaceAll(exp, '');
  }

  // 格式化时间显示
  String _formatTime(String timeStr) {
    try {
      final dateTime = DateTime.parse(timeStr);
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
    } catch (e) {
      return timeStr;
    }
  }

  // 创建公告项
  Widget _buildItem({required String title, required String content, required String time, String imagePath = ''}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18, vertical: UiConstants.spacing10),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: context.templateColors.divider, width: context.templateStyles.borderWidthThin)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: context.templateStyle.text.bodyLargeMedium, maxLines: 2, overflow: TextOverflow.ellipsis),
                SizedBox(height: UiConstants.spacing8),
                Text(content, style: context.templateStyle.text.descriptionText, maxLines: 1, overflow: TextOverflow.ellipsis),
                SizedBox(height: UiConstants.spacing8),
                Text(time, style: context.templateStyle.text.descriptionSmall),
              ],
            ),
          ),
          if (imagePath.isNotEmpty)
            ThemedImage(name: imagePath, width: 80, height: 60, followTheme: true, margin: EdgeInsets.only(left: UiConstants.spacing16)),
        ],
      ),
    );
  }
}
