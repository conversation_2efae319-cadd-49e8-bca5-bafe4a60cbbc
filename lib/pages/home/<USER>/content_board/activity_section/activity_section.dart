/*
*  活动标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class ActivitySection extends StatefulWidget {
  final ScrollPhysics? physics;

  const ActivitySection({super.key, this.physics});

  @override
  State<ActivitySection> createState() => _ActivitySectionState();
}

class _ActivitySectionState extends State<ActivitySection> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: ListView(
        physics: widget.physics,
        children: [
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
          _buildItem(),
        ],
      ),
    );
  }

  // 活动项
  Widget _buildItem() {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing14),
      margin: EdgeInsets.only(top: UiConstants.spacing14),
      decoration: BoxDecoration(
        color: context.templateColors.surface,
        borderRadius: BorderRadius.circular(
          context.templateStyles.borderRadiusMedium,
        ),
        border: Border.all(
          width: context.templateStyles.borderWidthThin,
          color: context.templateColors.border,
        ),
      ),
      child: Row(
        children: [
          ThemedImage(
            name: '',
            size: UiConstants.iconSize40,
            followTheme: true,
            margin: EdgeInsets.only(right: UiConstants.spacing14),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '这里是活动的标题',
                style: context.templateStyle.text.bodyTextMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: UiConstants.spacing4),
              TagWidget(
                text: '福利中心',
                borderColor: context.templateColors.primary,
                borderWidth: context.templateStyles.borderWidthThin,
                padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
                textStyle: context.templateStyle.text.bodySmallMedium.copyWith(
                  color: context.templateColors.primary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
