/*
*  快讯标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/services/clients/dio_request.dart';
import 'package:qubic_exchange/services/config/api_route.dart';
import 'package:qubic_exchange/models/article_model.dart';
import 'package:qubic_exchange/services/market/market_service.dart';

class FlashNewsSection extends StatefulWidget {
  final ScrollPhysics? physics;

  const FlashNewsSection({super.key, this.physics});

  @override
  State<FlashNewsSection> createState() => _FlashNewsSectionState();
}

class _FlashNewsSectionState extends State<FlashNewsSection> {
  // 文章数据
  List<ArticleModel> _articles = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();

    // 获取文章数据
    _fetchArticles();
  }

  // 获取文章数据
  Future<void> _fetchArticles() async {
    try {
      final response = await DioRequest.instance.get(
        ApiRoute.articleList,
        queryParams: {'category_id': 18, 'page': 1, 'page_size': 20},
        requireAuth: true,
      );

      if (response.data != null) {
        final responseData = response.data;
        if (responseData is Map<String, dynamic> && responseData.containsKey('list')) {
          final listData = responseData['list'];
          if (listData is List) {
            final articles = <ArticleModel>[];

            for (final item in listData) {
              try {
                if (item is Map<String, dynamic>) {
                  articles.add(ArticleModel.fromJson(item));
                }
              } catch (e) {
                debugPrint('解析单条文章数据失败: $e, 数据: $item');
              }
            }

            _articles = articles;
          }
        }
      }
    } catch (e) {
      debugPrint('获取文章数据失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_articles.isEmpty) {
      return const Center(child: Padding(padding: EdgeInsets.all(32.0), child: Text('暂无快讯数据')));
    }

    return ListView.builder(
      physics: widget.physics,
      itemCount: _articles.length,
      itemBuilder: (context, index) {
        return _buildFlashNewsItem(index);
      },
    );
  }

  /// 构建快讯项目
  Widget _buildFlashNewsItem(int index) {
    if (index >= _articles.length) return const SizedBox.shrink();

    final article = _articles[index];
    return _buildNewsItem(article);
  }

  /// 构建快讯项
  Widget _buildNewsItem(ArticleModel article) {
    // 获取相关代币的涨跌幅数据
    final tokenTags = _getTokenTags(article);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing16, vertical: UiConstants.spacing12),
      decoration: BoxDecoration(color: context.templateColors.background),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 时间
          Row(
            children: [
              Container(
                width: 4,
                height: 4,
                decoration: BoxDecoration(
                  color: context.templateColors.border,
                  borderRadius: BorderRadius.circular(UiConstants.borderRadius10),
                ),
                margin: EdgeInsets.only(right: UiConstants.spacing10),
              ),
              Text(_formatTime(article.createdAt), style: context.templateStyle.text.descriptionSmall),
            ],
          ),
          SizedBox(height: UiConstants.spacing8),
          // 快讯内容
          IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 虚线分割线
                Container(
                  width: 4,
                  margin: EdgeInsets.only(right: UiConstants.spacing10),
                  child: CustomPaint(painter: _DashedLinePainter(color: context.templateColors.border)),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(article.localizedTitle, style: context.templateStyle.text.bodyTextMedium),
                      // 相关代币标签
                      if (tokenTags.isNotEmpty) ...[
                        SizedBox(height: UiConstants.spacing12),
                        Wrap(
                          spacing: UiConstants.spacing8,
                          runSpacing: UiConstants.spacing6,
                          children: tokenTags.map((token) => _buildTokenTag(token)).toList(),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 获取代币标签数据
  List<Map<String, dynamic>> _getTokenTags(ArticleModel article) {
    final tokenTags = <Map<String, dynamic>>[];

    // 遍历文章中的currency字段
    for (final currencyId in article.currency) {
      // 从MarketService获取ticker数据
      final tickerData = MarketService.getTickerData(currencyId, marketType: 1);

      if (tickerData != null) {
        // 直接从ticker数据中获取symbol
        final tickerSymbol = tickerData['symbol']?.toString() ?? '';
        final symbol = tickerSymbol.replaceAll('USDT', '');

        // 过滤掉空的或无效的symbol
        if (symbol.isNotEmpty && symbol.toUpperCase() != 'UNKNOWN') {
          final priceChangeP = (tickerData['price_changeP'] as num?)?.toDouble() ?? 0.0;

          tokenTags.add({
            'symbol': symbol,
            'change': '${priceChangeP >= 0 ? '+' : ''}${priceChangeP.toStringAsFixed(2)}%',
            'isUp': priceChangeP >= 0,
          });
        }
      }
    }

    return tokenTags;
  }

  /// 格式化时间显示
  String _formatTime(String timeStr) {
    try {
      final dateTime = DateTime.parse(timeStr);
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return timeStr;
    }
  }

  /// 构建代币标签
  Widget _buildTokenTag(Map<String, dynamic> token) {
    final isUp = token['isUp'] as bool;
    final changeColor = isUp ? context.templateColors.success : context.templateColors.error;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing8, vertical: UiConstants.spacing4),
      decoration: BoxDecoration(
        color: context.templateColors.surface,
        borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
        border: Border.all(color: context.templateColors.divider, width: 0.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(token['symbol'], style: context.templateStyle.text.bodySmallMedium),
          SizedBox(width: UiConstants.spacing4),
          Text(token['change'], style: context.templateStyle.text.bodySmallMedium.copyWith(color: changeColor)),
        ],
      ),
    );
  }
}

/// 虚线绘制器
class _DashedLinePainter extends CustomPainter {
  final Color color;

  _DashedLinePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke;

    const double dashWidth = 2.0;
    const double dashSpace = 2.0;

    double startY = 0;
    while (startY < size.height) {
      canvas.drawLine(Offset(size.width / 2, startY), Offset(size.width / 2, startY + dashWidth), paint);
      startY += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
