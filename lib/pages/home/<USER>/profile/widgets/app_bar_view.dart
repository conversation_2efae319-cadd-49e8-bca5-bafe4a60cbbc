/*

  个人中心AppBar组件
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/routes/index.dart';

class AppBarView extends StatelessWidget implements PreferredSizeWidget {
  const AppBarView({super.key});

  @override
  Size get preferredSize => const Size.fromHeight(56.0);

  @override
  Widget build(BuildContext context) {
    return AppBarWidget(
      showBackButton: true,
      actions: [
        Padding(
          padding: EdgeInsets.only(right: UiConstants.spacing8),
          child: InkWellWidget(
            onTap: () {
              NavigationService().navigateTo(AppRoutes.setting);
            },
            child: ThemedImage.asset(
              'ds_icon_ds3setting_personal',
              width: 23,
              height: 23,
              followTheme: true,
            ),
          ),
        ),
      ],
    );
  }
}
