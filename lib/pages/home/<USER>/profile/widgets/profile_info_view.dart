/*

  用户信息组件
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

import 'package:qubic_exchange/routes/index.dart';

class ProfileInfoView extends StatefulWidget {
  final bool isAuthenticated;

  const ProfileInfoView({super.key, this.isAuthenticated = false});

  @override
  State<ProfileInfoView> createState() => _ProfileInfoViewState();
}

class _ProfileInfoViewState extends State<ProfileInfoView> {
  // 使用新的主题系统

  @override
  Widget build(BuildContext context) {
    return widget.isAuthenticated
        ? const _AuthenticatedView()
        : const _UnauthenticatedView();
  }
}

// 已登陆状态
class _AuthenticatedView extends StatelessWidget {
  const _AuthenticatedView();

  @override
  Widget build(BuildContext context) {
    return Container();
  }
}

// 未登陆状态
class _UnauthenticatedView extends StatelessWidget {
  const _UnauthenticatedView();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: UiConstants.spacing32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(text: '欢迎', style: context.templateStyle.text.h1),
          SizedBox(height: UiConstants.spacing8),
          TextWidget(
            text: '欢迎使用 CPX Exchange',
            style: context.templateStyle.text.descriptionText,
          ),
          SizedBox(height: UiConstants.spacing20),
          CommonButton.primary(
            '注册/登录',
            height: 40,
            width: double.infinity,
            borderRadius: UiConstants.borderRadius8,
            onPressed: () {
              NavigationService().navigateTo(AppRoutes.register);
            },
          ),
        ],
      ),
    );
  }
}
