/*

  用户中心菜单功能
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/routes/index.dart';

class MenuListView extends StatefulWidget {
  const MenuListView({super.key});

  @override
  State<MenuListView> createState() => _MenuListViewState();
}

class _MenuListViewState extends State<MenuListView> {
  // 使用新的主题系统

  @override
  Widget build(BuildContext context) {
    return _buildMenuOptions();
  }

  // 菜单列表
  Widget _buildMenuOptions() {
    final menuItems = [
      {'name': '帮助中心', 'icon': 'strategy_guide', 'route': null},
      {'name': '客服中心', 'icon': 'support_menu', 'route': null},
      {'name': '学院', 'icon': 'learning_help_center', 'route': null},
      {'name': '保护基金', 'icon': 'protectionFund', 'route': null},
    ];

    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(bottom: UiConstants.spacing32),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          TextWidget(
            text: '快速访问',
            style: context.templateStyle.text.bodyLargeMedium,
          ),
          SizedBox(height: UiConstants.spacing32),
          Wrap(
            crossAxisAlignment: WrapCrossAlignment.start,
            spacing: UiConstants.spacing16,
            runSpacing: UiConstants.spacing16,
            alignment: WrapAlignment.start,
            runAlignment: WrapAlignment.start,
            children:
                menuItems
                    .map(
                      (item) => SizedBox(
                        width:
                            (MediaQuery.of(context).size.width -
                                UiConstants.spacing32 -
                                UiConstants.spacing16 * 3) /
                            4,
                        child: _buildMenuItem(
                          item['name']!,
                          item['icon']!,
                          item['route'],
                        ),
                      ),
                    )
                    .toList(),
          ),
        ],
      ),
    );
  }

  // 菜单项
  Widget _buildMenuItem(String name, String iconName, String? route) {
    return InkWellWidget(
      onTap: () {
        if (route != null) {
          NavigationService().navigateTo(route);
        }
      },
      child: SizedBox(
        child: Column(
          children: [
            ThemedImage.asset(
              iconName,
              followTheme: true,
              width: 26,
              height: 26,
            ),
            SizedBox(height: UiConstants.spacing8),
            TextWidget(
              text: name,
              style: context.templateStyle.text.bodyText.copyWith(fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }
}
