/*
 * 个人中心 + 功能菜单页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import './widgets/index.dart';

class UserCenterScreen extends StatefulWidget {
  const UserCenterScreen({super.key});

  @override
  State<UserCenterScreen> createState() => _UserCenterScreenState();
}

class _UserCenterScreenState extends State<UserCenterScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.templateColors.surface,
      appBar: AppBarView(),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(UiConstants.spacing16),
          child: Column(
            children: [
              // 个人信息
              ProfileInfoView(),
              // 菜单功能
              MenuListView(),
            ],
          ),
        ),
      ),
    );
  }
}
