/*
*  货币看板
*/

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/widgets/common/dynamic_height_tab_view.dart';
import 'package:qubic_exchange/models/index.dart';
import 'package:qubic_exchange/services/market/index.dart';
import 'package:qubic_exchange/services/clients/dio_request.dart';
import 'package:qubic_exchange/services/config/api_route.dart';
import 'package:qubic_exchange/services/storage/storage_service.dart';
import 'package:qubic_exchange/services/network/websocket_service.dart';
import 'widgets/token_currency_item.dart';

class TokenBoardSection extends StatefulWidget {
  /// 代币数据（新模型）
  final TokenBoardDataModel? tokenBoardData;

  /// 代币数据（旧格式，用于向后兼容）
  final Map<String, Map<String, List<Map<String, dynamic>>>>? tokenData;

  /// 点击代币回调
  final Function(TokenModel token)? onTokenTap;

  /// 点击查看更多回调
  final VoidCallback? onViewMore;

  const TokenBoardSection({super.key, this.tokenBoardData, this.tokenData, this.onTokenTap, this.onViewMore});

  @override
  State<TokenBoardSection> createState() => _TokenBoardSectionState();
}

class _TokenBoardSectionState extends State<TokenBoardSection> with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  // 构建标签控制器
  late TabController _mainTabController;
  late TabController _subTabController;

  // 主标签数据
  List<Map<String, dynamic>> _mainTabData = [];

  // 数据加载状态
  bool _isLoading = true;

  // 自选数据缓存 {'1': [866, 123, ...], '5': [456, 789, ...]}
  final Map<String, List<int>> _favoritesByMarket = {};

  // WebSocket相关
  StreamSubscription<Map<String, dynamic>>? _tickerSubscription;

  // TokenModel数据缓存 {currency_id: TokenModel}
  final Map<int, TokenModel> _tokenDataCache = {};

  // 保持页面状态
  @override
  bool get wantKeepAlive => true;

  /// 获取主标签列表（从后端数据中获取）
  List<TabItem> get _mainTabKeys {
    if (_mainTabData.isEmpty) {
      // 数据加载中或加载失败时显示默认标签（自选始终在第一位）
      return const [
        TabItem(title: '自选'),
        TabItem(title: '热门'),
        TabItem(title: '涨幅榜'),
        TabItem(title: '新币榜'),
        TabItem(title: '股票'),
        TabItem(title: '外汇'),
        TabItem(title: '期货'),
      ];
    }

    // 使用后端数据生成标签（自选已经在第一位）
    return _mainTabData.map((tabData) {
      return TabItem(title: tabData['title']?.toString() ?? '');
    }).toList();
  }

  // 初始化
  @override
  void initState() {
    super.initState();
    _fetchMainTabData();
    _loadFavoritesData();
    _waitForWebSocketAndSubscribe();
  }

  /// 获取主标签数据
  Future<void> _fetchMainTabData() async {
    try {
      final response = await DioRequest.instance.get(ApiRoute.homeMainTab, requireAuth: false);

      if (response.success && response.data != null) {
        final List<dynamic> data = response.data as List<dynamic>;
        final List<Map<String, dynamic>> backendData = data.cast<Map<String, dynamic>>();

        // 将自选合并到第一个位置
        _mainTabData = [
          {
            'title': '自选',
            'sub': [
              {'title': '现货'},
              {'title': '合约'},
            ],
          },
          ...backendData,
        ];

        setState(() {
          _isLoading = false;
        });

        // 数据加载完成后初始化TabController
        _initTabControllers();
      } else {
        // 请求失败时也要添加自选标签
        _mainTabData = [
          {
            'title': '自选',
            'sub': [
              {'title': '现货'},
              {'title': '合约'},
            ],
          },
        ];
        setState(() {
          _isLoading = false;
        });
        _initTabControllers();
      }
    } catch (e) {
      debugPrint('获取主标签数据失败: $e');
      // 异常时也要添加自选标签
      _mainTabData = [
        {
          'title': '自选',
          'sub': [
            {'title': '现货'},
            {'title': '合约'},
          ],
        },
      ];
      setState(() {
        _isLoading = false;
      });
      _initTabControllers();
    }
  }

  /// 初始化TabController
  void _initTabControllers() {
    _mainTabController = TabController(length: _mainTabKeys.length, vsync: this);

    // 初始化子标签控制器（基于第一个主标签）
    final initialSubTabs = _getSubTabKeys(_mainTabKeys[0].title);
    _subTabController = TabController(length: initialSubTabs.length, vsync: this);

    // 监听主标签变化，更新子标签控制器
    _mainTabController.addListener(_onMainTabChanged);

    // 监听子标签变化，确保状态同步
    _subTabController.addListener(_onSubTabChanged);
  }

  /// 处理主标签变化
  void _onMainTabChanged() {
    if (_mainTabController.indexIsChanging) {
      final currentMainTab = _mainTabKeys[_mainTabController.index].title;
      final newSubTabs = _getSubTabKeys(currentMainTab);

      // 如果子标签数量发生变化，重新创建子标签控制器
      if (newSubTabs.length != _subTabController.length) {
        _subTabController.dispose();
        _subTabController = TabController(
          length: newSubTabs.length,
          vsync: this,
          initialIndex: 0, // 确保从第一个子标签开始
        );
        // 重新添加监听器
        _subTabController.addListener(_onSubTabChanged);
      } else {
        // 即使子标签数量相同，也要重置到第一个子标签
        if (_subTabController.index != 0) {
          _subTabController.animateTo(0);
        }
      }

      // 无论子标签数量是否变化，都触发重建以确保内容更新
      if (mounted) {
        // 清除缓存中的ticker数据，避免显示错误的数据
        _clearTickerCache();

        setState(() {
          // 触发重建以更新子标签内容
        });
      }
    }
  }

  /// 处理子标签变化
  void _onSubTabChanged() {
    if (mounted) {
      // 获取当前子标签
      final currentMainTab = _mainTabKeys[_mainTabController.index].title;
      final subTabKeys = _getSubTabKeys(currentMainTab);
      if (_subTabController.index < subTabKeys.length) {
        final currentSubTab = subTabKeys[_subTabController.index];

        // 子标签切换完成后，清除缓存并触发重建
        _clearTickerCache();
        setState(() {
          // 触发重建
        });
      }
    }
  }

  // 销毁
  @override
  void dispose() {
    // 清理WebSocket相关资源
    _tickerSubscription?.cancel();

    // 移除监听器
    _mainTabController.removeListener(_onMainTabChanged);
    _subTabController.removeListener(_onSubTabChanged);

    // 销毁控制器
    _mainTabController.dispose();
    _subTabController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    // 数据加载中显示加载状态
    if (_isLoading) {
      return Container(
        height: 200,
        decoration: BoxDecoration(
          border: Border(bottom: BorderSide(color: context.templateColors.divider, width: UiConstants.borderWidth0_5)),
        ),
        child: const Center(child: CircularProgressIndicator()),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: context.templateColors.divider, width: UiConstants.borderWidth0_5)),
      ),
      child: Column(
        children: [
          // 主标签栏 - 始终显示
          _buildMainTabbar(),
          // 主标签内容 - 使用动态高度 TabView 或空状态
          _buildMainTabContent(),
          // 查看更多 - 始终显示
          SizedBox(
            width: double.infinity,
            height: 44,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: widget.onViewMore,
                  child: ViewMoreButton(
                    text: '查看更多',
                    style: context.templateStyle.text.descriptionTextMedium,
                    iconColor: context.templateColors.textSecondary,
                    showArrow: true,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建主标签栏
  Widget _buildMainTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      decoration: BoxDecoration(border: Border(top: BorderSide(width: UiConstants.borderWidth0_5, color: context.templateColors.divider))),
      child: TabbarWidget(
        height: 44,
        controller: _mainTabController,
        tabs: _mainTabKeys,
        labelStyle: context.templateStyle.text.tabText,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: true,
        indicatorSize: TabBarIndicatorSize.label,
        labelPadding: EdgeInsets.only(right: UiConstants.spacing14),
      ),
    );
  }

  // 构建主标签内容区 - 使用动态高度 TabView
  Widget _buildMainTabContent() {
    final mainTabs = _mainTabKeys;

    return DynamicHeightTabView(
      controller: _mainTabController,
      enableSwipe: true,
      transitionType: TabTransitionType.none,
      animationDuration: const Duration(milliseconds: 300),
      children: mainTabs.map((tab) => _buildTabPage(tab.title)).toList(),
    );
  }

  /// 构建标签页内容
  Widget _buildTabPage(String tabName) {
    final subTabKeys = _getSubTabKeys(tabName);

    return Column(
      children: [
        // 子标签栏（如果有子标签才显示）
        if (subTabKeys.isNotEmpty) _buildSubTabbar(tabName),
        // 子标签内容
        _buildSubTabContent(tabName),
      ],
    );
  }

  /// 获取子标签列表（从后端数据中获取）
  List<String> _getSubTabKeys(String mainTabName) {
    // 查找对应的主标签数据
    final mainTabData = _mainTabData.firstWhere((tab) => tab['title'] == mainTabName, orElse: () => <String, dynamic>{});

    if (mainTabData.isEmpty) {
      // 如果没有找到数据，使用默认配置
      switch (mainTabName) {
        case '自选':
        case '热门':
        case '涨幅榜':
        case '新币榜':
          return ['现货', '合约'];
        case '股票':
          return ['美股', '港股'];
        case '外汇':
          return []; // 外汇子标题为空
        case '期货':
          return ['贵金属', '原油'];
        default:
          return ['现货', '合约'];
      }
    }

    // 从后端数据中提取子标签
    final subData = mainTabData['sub'];
    if (subData == null) return [];

    if (subData is List) {
      // sub是数组，提取每个子项的title
      return subData.map((item) => item['title']?.toString() ?? '').where((title) => title.isNotEmpty).toList();
    } else if (subData is Map) {
      // sub是对象（如外汇），检查是否有title
      final title = subData['title']?.toString() ?? '';
      return title.isEmpty ? [] : [title];
    }

    return [];
  }

  // 构建子标签栏
  Widget _buildSubTabbar(String mainTabName) {
    final subTabKeys = _getSubTabKeys(mainTabName);
    final subTabs = subTabKeys.map((key) => TabItem(title: key)).toList();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18, vertical: UiConstants.spacing10),
      child: TabbarWidget(
        height: 22,
        controller: _subTabController,
        tabs: subTabs,
        labelStyle: context.templateStyle.text.bodySmallMedium,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textTertiary,
        showIndicator: true,
        indicatorColor: context.templateColors.tabbarActive,
        indicatorStyle: TabBarIndicatorStyle.filled,
        tabPadding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
      ),
    );
  }

  // 构建子标签内容 - 使用动态高度 TabView
  Widget _buildSubTabContent(String mainTabName) {
    final subTabKeys = _getSubTabKeys(mainTabName);

    // 如果没有子标签，直接显示内容
    if (subTabKeys.isEmpty) {
      return _buildTokenList(mainTabName, '');
    }

    return DynamicHeightTabView(
      controller: _subTabController,
      enableSwipe: true,
      transitionType: TabTransitionType.none,
      animationDuration: const Duration(milliseconds: 250),
      children: subTabKeys.map((subTab) => _buildTokenList(mainTabName, subTab)).toList(),
    );
  }

  /// 构建代币列表
  Widget _buildTokenList(String mainTab, String subTab) {
    // 获取数据（优先使用传入数据）
    final tokenData = _getTokenData(mainTab, subTab);

    // 如果没有数据，显示空状态
    if (tokenData.isEmpty) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
        child: Column(
          children: [
            // 列表头部
            _buildListHeader(mainTab, subTab),
            // 固定高度的空状态组件
            SizedBox(
              height: 200, // 固定高度，确保空状态有足够的占位空间
              child: Center(child: EmptyWidget(text: '暂无数据', imageName: 'noData', imageSize: 50)),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: Column(
        children: [
          // 列表头部
          _buildListHeader(mainTab, subTab),
          // 代币列表
          ...tokenData.map(
            (token) => TokenCurrencyItem(
              symbol: token.symbol,
              name: token.name,
              price: token.price,
              change: token.formattedChangeAmount,
              changePercent: token.formattedChangePercentage,
              volume: token.volume,
              isUp: token.isUp,
              isContract: subTab == '合约',
              quoteCurrency: token.quoteCurrency,
              displayMode: _getDisplayMode(mainTab),
              extraData: token.toJson(), // 转换为Map用于兼容
              onTap: () => _handleTokenTap(token),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建列表头部
  Widget _buildListHeader(String mainTab, String subTab) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing12),
      child: Row(children: _getHeaderColumns(mainTab, subTab)),
    );
  }

  /// 获取头部列配置
  List<Widget> _getHeaderColumns(String mainTab, String subTab) {
    switch (mainTab) {
      case '新币榜':
        return [
          Expanded(
            flex: 3,
            child: Text('名称', style: context.templateStyle.text.bodySmall.copyWith(color: context.templateColors.textSecondary)),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '已上线',
              textAlign: TextAlign.right,
              style: context.templateStyle.text.bodySmall.copyWith(color: context.templateColors.textSecondary),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '开盘涨幅/价格',
              textAlign: TextAlign.right,
              style: context.templateStyle.text.bodySmall.copyWith(color: context.templateColors.textSecondary),
            ),
          ),
        ];

      case '成交额榜':
        return [
          Expanded(
            flex: 3,
            child: Text(
              subTab == '链上交易' ? '币种/链上成交额' : '币种',
              style: context.templateStyle.text.bodySmall.copyWith(color: context.templateColors.textSecondary),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '总市值',
              textAlign: TextAlign.right,
              style: context.templateStyle.text.bodySmall.copyWith(color: context.templateColors.textSecondary),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '涨跌',
              textAlign: TextAlign.right,
              style: context.templateStyle.text.bodySmall.copyWith(color: context.templateColors.textSecondary),
            ),
          ),
        ];

      default:
        // 默认头部：名称、最新价、24h涨跌幅
        return [
          Expanded(
            flex: 3,
            child: Text('名称', style: context.templateStyle.text.bodySmall.copyWith(color: context.templateColors.textSecondary)),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '最新价',
              textAlign: TextAlign.right,
              style: context.templateStyle.text.bodySmall.copyWith(color: context.templateColors.textSecondary),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '24h涨跌幅',
              textAlign: TextAlign.right,
              style: context.templateStyle.text.bodySmall.copyWith(color: context.templateColors.textSecondary),
            ),
          ),
        ];
    }
  }

  /// 加载自选数据
  Future<void> _loadFavoritesData() async {
    try {
      final storage = StorageService.instance;

      // 加载现货市场自选数据 (market_type = 1)
      final spotKey = 'favorites_market_1';
      final spotList = storage.getList(spotKey);
      if (spotList != null) {
        try {
          final ids = spotList.cast<int>();
          _favoritesByMarket['1'] = ids;
        } catch (e) {
          try {
            final stringList = spotList.cast<String>();
            final ids = stringList.map((s) => int.parse(s)).toList();
            _favoritesByMarket['1'] = ids;
          } catch (e2) {
            _favoritesByMarket['1'] = [];
          }
        }
      }

      // 加载合约市场自选数据 (market_type = 5)
      final contractKey = 'favorites_market_5';
      final contractList = storage.getList(contractKey);
      if (contractList != null) {
        try {
          final ids = contractList.cast<int>();
          _favoritesByMarket['5'] = ids;
        } catch (e) {
          try {
            final stringList = contractList.cast<String>();
            final ids = stringList.map((s) => int.parse(s)).toList();
            _favoritesByMarket['5'] = ids;
          } catch (e2) {
            _favoritesByMarket['5'] = [];
          }
        }
      }
    } catch (e) {
      debugPrint('加载自选数据失败: $e');
    }
  }

  /// 等待WebSocket连接并订阅
  void _waitForWebSocketAndSubscribe() {
    WebSocketService.instance.connectionStream.listen((isConnected) {
      if (isConnected && mounted) {
        _subscribeToTickers();
      }
    });

    if (WebSocketService.instance.isConnected) {
      _subscribeToTickers();
    }
  }

  /// 订阅ticker数据
  void _subscribeToTickers() {
    // 订阅market_type 1的ticker数据（现货）
    final subscribeMessage1 = {"action": "subscribe", "type": "ticker", "market_type": 1};

    // 订阅market_type 5的ticker数据（合约）
    final subscribeMessage5 = {"action": "subscribe", "type": "ticker", "market_type": 5};

    // 发送订阅消息
    WebSocketService.instance.sendMessage(subscribeMessage1);
    WebSocketService.instance.sendMessage(subscribeMessage5);

    _tickerSubscription = WebSocketService.instance.subscribe('ticker', {}).listen(_handleTickerData);
  }

  /// 处理ticker数据
  void _handleTickerData(Map<String, dynamic> message) {
    if (message['type'] == 'ticker' && message['data'] != null) {
      final List<dynamic> tickerList = message['data'];
      final int marketype = message['market_type'] as int;
      _updateTokenDataWithTicker(tickerList, marketype);
    }
  }

  /// 使用ticker数据更新TokenModel
  void _updateTokenDataWithTicker(List<dynamic> tickerList, int marketype) {
    // 检查组件是否还在挂载状态
    if (!mounted) return;

    // 获取当前实际显示的主标签和子标签
    final currentMainTab = _mainTabKeys[_mainTabController.index].title;
    final subTabKeys = _getSubTabKeys(currentMainTab);
    String currentSubTab = '';
    if (subTabKeys.isNotEmpty && _subTabController.index < subTabKeys.length) {
      currentSubTab = subTabKeys[_subTabController.index];
    }

    // 根据当前显示的子标题确定需要处理的market_type
    int targetMarketType = 1; // 默认现货
    if (currentSubTab == '合约') {
      targetMarketType = 5;
    }

    // 只处理匹配当前子标题的ticker数据
    if (marketype != targetMarketType) {
      return;
    }

    // 检查是否正在切换标签（防止切换过程中的数据更新）
    if (_mainTabController.indexIsChanging || _subTabController.indexIsChanging) {
      return;
    }

    bool shouldUpdate = false;

    for (final ticker in tickerList) {
      final currencyId = ticker['currency_id'] as int;

      if (_tokenDataCache.containsKey(currencyId)) {
        final existingToken = _tokenDataCache[currencyId]!;
        final lastPrice = (ticker['last_price'] as num?)?.toDouble() ?? 0.0;
        final priceChangeP = (ticker['price_changeP'] as num?)?.toDouble() ?? 0.0;
        final volume = (ticker['volume'] as num?)?.toDouble() ?? 0.0;
        final priceChange = (ticker['price_change'] as num?)?.toDouble() ?? 0.0;

        // 直接更新数据，不需要比较
        _tokenDataCache[currencyId] = TokenModel(
          symbol: existingToken.symbol,
          name: existingToken.name,
          price: lastPrice.toStringAsFixed(lastPrice >= 1 ? 2 : 6),
          changeAmount: priceChange,
          changePercentage: priceChangeP,
          volume: volume.toStringAsFixed(2),
          quoteCurrency: existingToken.quoteCurrency,
          isUp: priceChangeP >= 0,
        );

        shouldUpdate = true;
      }
    }

    // 直接更新UI，不使用定时器
    if (shouldUpdate && mounted) {
      setState(() {});
    }
  }

  /// 获取代币数据
  List<TokenModel> _getTokenData(String mainTab, String subTab) {
    // 处理自选数据
    if (mainTab == '自选') {
      return _getFavoritesTokenDataWithCache(subTab);
    }

    // 新币榜暂不处理
    if (mainTab == '新币榜') {
      return [];
    }

    // 获取MarketService中的币种模型数据
    final currencyModels = MarketService.instance.currencyModels;
    if (currencyModels.isEmpty) {
      return [];
    }

    // 确定market_type：现货=1，合约=5
    final marketType = subTab == '合约' ? 5 : 1;

    // 过滤出对应market_type的币种模型
    List<CurrencyModel> filteredCurrencies =
        currencyModels.where((currency) {
          return currency.marketType == 1;
        }).toList();

    // 根据主标题进行不同的处理
    switch (mainTab) {
      case '热门':
        // 热门：直接使用原始币种列表，取前5条
        filteredCurrencies = filteredCurrencies.take(5).toList();
        break;

      case '涨幅榜':
        // 涨幅榜：根据price_changeP排序（从大到小），取前5条
        filteredCurrencies.sort((a, b) {
          final aChangeP = _getPriceChangeP(a, marketType);
          final bChangeP = _getPriceChangeP(b, marketType);
          return bChangeP.compareTo(aChangeP); // 从大到小排序
        });
        filteredCurrencies = filteredCurrencies.take(5).toList();
        break;

      default:
        filteredCurrencies = filteredCurrencies.take(5).toList();
        break;
    }

    // 转换为TokenModel列表并更新缓存
    final tokens = filteredCurrencies.map((currency) => _convertToTokenModel(currency, marketType)).toList();

    // 更新缓存
    for (final currency in filteredCurrencies) {
      final currencyId = currency.id;
      final token = _convertToTokenModel(currency, marketType);
      _tokenDataCache[currencyId] = token;
    }

    return tokens;
  }

  /// 获取自选代币数据（带缓存）
  List<TokenModel> _getFavoritesTokenDataWithCache(String subTab) {
    // 确定market_type：现货=1，合约=5
    final marketType = subTab == '合约' ? 5 : 1;
    final marketTypeKey = marketType.toString();

    // 获取自选ID列表
    final favoriteIds = _favoritesByMarket[marketTypeKey] ?? [];
    if (favoriteIds.isEmpty) {
      return [];
    }

    // 获取MarketService中的币种模型数据
    final currencyModels = MarketService.instance.currencyModels;
    if (currencyModels.isEmpty) {
      return [];
    }

    // 根据ID筛选出自选币种
    List<CurrencyModel> favoriteCurrencies = [];
    for (final id in favoriteIds) {
      final currency = currencyModels.firstWhere((c) => c.id == id && c.marketType == 1, orElse: () => throw StateError('Not found'));
      try {
        favoriteCurrencies.add(currency);
      } catch (e) {
        // 如果没找到就跳过
      }
    }

    // 转换为TokenModel列表并更新缓存
    final tokens = <TokenModel>[];
    for (final currency in favoriteCurrencies) {
      final currencyId = currency.id;

      // 优先使用缓存中的数据（包含最新ticker信息）
      if (_tokenDataCache.containsKey(currencyId)) {
        tokens.add(_tokenDataCache[currencyId]!);
      } else {
        // 如果缓存中没有，创建新的TokenModel并加入缓存
        final token = _convertToTokenModel(currency, marketType);
        _tokenDataCache[currencyId] = token;
        tokens.add(token);
      }
    }

    return tokens;
  }

  /// 获取币种的price_changeP值
  double _getPriceChangeP(CurrencyModel currency, int marketType) {
    try {
      final ticker = currency.tickers[marketType.toString()];
      if (ticker == null) return 0.0;

      return ticker.priceChangeP;
    } catch (e) {
      debugPrint('获取price_changeP失败: $e');
      return 0.0;
    }
  }

  /// 将币种数据转换为TokenModel
  TokenModel _convertToTokenModel(CurrencyModel currency, int marketType) {
    try {
      final ticker = currency.tickers[marketType.toString()];

      final symbol = currency.baseAsset;
      final quoteCurrency = currency.quoteAsset;
      final lastPrice = ticker?.lastPrice ?? 0.0;
      final priceChangeP = ticker?.priceChangeP ?? 0.0;
      final volume = ticker?.volume ?? 0.0;
      final priceChange = ticker?.priceChange ?? 0.0;

      return TokenModel(
        symbol: symbol,
        name: symbol,
        price: lastPrice.toStringAsFixed(lastPrice >= 1 ? 2 : 6),
        changeAmount: priceChange,
        changePercentage: priceChangeP,
        volume: volume.toStringAsFixed(2),
        quoteCurrency: quoteCurrency,
        isUp: priceChangeP >= 0,
      );
    } catch (e) {
      debugPrint('转换TokenModel失败: $e');
      // 返回默认的TokenModel
      return TokenModel(
        symbol: currency.baseAsset,
        name: currency.baseAsset,
        price: '0.00',
        changeAmount: 0.0,
        changePercentage: 0.0,
        volume: '0.00',
        quoteCurrency: currency.quoteAsset,
        isUp: false,
      );
    }
  }

  /// 获取显示模式
  TokenDisplayMode _getDisplayMode(String mainTab) {
    switch (mainTab) {
      case '新币榜':
        return TokenDisplayMode.newCoin;
      case '成交额榜':
        return TokenDisplayMode.volume;
      default:
        return TokenDisplayMode.normal;
    }
  }

  /// 处理代币点击
  void _handleTokenTap(TokenModel token) {
    if (widget.onTokenTap != null) {
      widget.onTokenTap!(token);
    }
  }

  /// 清除ticker缓存数据
  void _clearTickerCache() {
    _tokenDataCache.clear();
  }
}
