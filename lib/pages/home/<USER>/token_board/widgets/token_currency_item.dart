/*
*  货币项组件
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/utils/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';

enum TokenDisplayMode {
  normal, // 默认模式：名称、最新价、24h涨跌幅
  newCoin, // 新币榜模式：名称、已上线、开盘涨幅/价格
  volume, // 成交额榜模式：币种、总市值、涨跌
}

class TokenCurrencyItem extends StatefulWidget {
  final String symbol;
  final String name;
  final String price;
  final String change;
  final String changePercent;
  final String volume;
  final bool isUp;
  final bool isContract;
  final String? quoteCurrency; // 报价货币，如 USDT
  final TokenDisplayMode displayMode; // 显示模式
  final Map<String, dynamic>? extraData; // 额外数据（用于新币榜和成交额榜）
  final VoidCallback? onTap;

  const TokenCurrencyItem({
    super.key,
    required this.symbol,
    required this.name,
    required this.price,
    required this.change,
    required this.changePercent,
    required this.volume,
    this.isUp = true,
    this.isContract = false,
    this.quoteCurrency,
    this.displayMode = TokenDisplayMode.normal,
    this.extraData,
    this.onTap,
  });

  @override
  State<TokenCurrencyItem> createState() => _TokenCurrencyItemState();
}

class _TokenCurrencyItemState extends State<TokenCurrencyItem> {
  /// 获取交易对货币
  String _getQuoteCurrency() {
    // 优先使用传入的 quoteCurrency 参数
    if (widget.quoteCurrency != null && widget.quoteCurrency!.isNotEmpty) {
      return widget.quoteCurrency!;
    }

    // 如果名称包含 "/"，提取后面的部分
    if (widget.name.contains('/')) {
      return widget.name.split('/').last.trim();
    }

    // 默认返回 USDT
    return 'USDT';
  }

  /// 获取人民币价格显示
  String _getCnyPrice() {
    try {
      // 获取AuthProvider实例
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // 提取价格数值
      final priceValue = widget.price.replaceAll(RegExp(r'[^\d.]'), '');
      final numericPrice = double.tryParse(priceValue) ?? 0.0;

      // 获取汇率和货币符号
      final exchangeRate = authProvider.currentRate;
      final currencySymbol = authProvider.selectedCurrency?.symbol ?? '\$';

      // 计算转换后的价格
      final convertedPrice = numericPrice * exchangeRate;

      // 格式化并返回带货币符号的价格
      return '$currencySymbol${NumberFormatUtil.formatWithComma(convertedPrice)}';
    } catch (e) {
      // 出错时返回原始成交量信息
      return widget.volume;
    }
  }

  /// 获取带计价符号的价格
  String _getPriceWithSymbol() {
    final priceValue = widget.price.replaceAll(RegExp(r'[^\d.]'), '');
    final numericPrice = double.tryParse(priceValue) ?? 0.0;
    return NumberFormatUtil.formatWithComma(numericPrice);
  }

  /// 构建右侧内容
  List<Widget> _buildRightContent() {
    switch (widget.displayMode) {
      case TokenDisplayMode.newCoin:
        return _buildNewCoinContent();
      case TokenDisplayMode.volume:
        return _buildVolumeContent();
      default:
        return _buildNormalContent();
    }
  }

  /// 构建默认内容（名称、最新价、24h涨跌幅）
  List<Widget> _buildNormalContent() {
    return [
      // 价格信息
      Expanded(
        flex: 2,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(_getPriceWithSymbol(), style: context.templateStyle.text.bodyLargeMedium),
            Text(_getCnyPrice(), style: context.templateStyle.text.descriptionSmall),
          ],
        ),
      ),
      // 涨跌幅
      Expanded(
        flex: 2,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing12, vertical: UiConstants.spacing4),
              decoration: BoxDecoration(
                color: widget.isUp ? context.templateColors.tradeBuy : context.templateColors.tradeSell,
                borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
              ),
              child: Text(
                widget.changePercent,
                style: context.templateStyle.text.bodyTextMedium.copyWith(color: context.templateColors.white),
              ),
            ),
          ],
        ),
      ),
    ];
  }

  /// 构建新币榜内容（名称、已上线、开盘涨幅/价格）
  List<Widget> _buildNewCoinContent() {
    final onlineTime = widget.extraData?['onlineTime'] ?? '未知';
    final openingGain = widget.extraData?['openingGain'] ?? '0%';

    return [
      // 已上线时间
      Expanded(
        flex: 2,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [Text(onlineTime, style: context.templateStyle.text.bodyLargeMedium)],
        ),
      ),
      // 开盘涨幅/价格
      Expanded(
        flex: 2,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(_getPriceWithSymbol(), style: context.templateStyle.text.bodyLargeMedium),
            Text(
              openingGain,
              style: context.templateStyle.text.bodyTextMedium.copyWith(
                color: openingGain.startsWith('+') ? context.templateColors.tradeBuy : context.templateColors.tradeSell,
              ),
            ),
          ],
        ),
      ),
    ];
  }

  /// 构建成交额榜内容（币种、总市值、涨跌）
  List<Widget> _buildVolumeContent() {
    final marketCap = widget.extraData?['marketCap'] ?? '未知';

    return [
      // 总市值
      Expanded(
        flex: 2,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [Text(marketCap, style: context.templateStyle.text.bodyLargeMedium)],
        ),
      ),
      // 涨跌
      Expanded(
        flex: 2,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              widget.changePercent,
              style: context.templateStyle.text.bodyLargeMedium.copyWith(
                color: widget.isUp ? context.templateColors.tradeBuy : context.templateColors.tradeSell,
              ),
            ),
          ],
        ),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return InkWellWidget(
      onTap: widget.onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: UiConstants.spacing10),
        child: Row(
          children: [
            // 代币信息
            Expanded(
              flex: 3,
              child:
                  widget.isContract
                      ? Text('${widget.symbol}USDT', style: context.templateStyle.text.bodyLargeMedium)
                      : Text.rich(
                        TextSpan(
                          style: context.templateStyle.text.bodyLargeMedium,
                          children: [
                            TextSpan(text: widget.symbol),
                            TextSpan(text: ' / ${_getQuoteCurrency()}', style: context.templateStyle.text.descriptionSmall),
                          ],
                        ),
                      ),
            ),

            // 右侧内容 - 根据显示模式构建
            ..._buildRightContent(),
          ],
        ),
      ),
    );
  }
}
