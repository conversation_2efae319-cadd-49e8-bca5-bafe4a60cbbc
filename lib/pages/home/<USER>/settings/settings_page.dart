/*


  偏好设置界面
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';
import 'package:qubic_exchange/services/data/currency_service.dart';
import 'package:qubic_exchange/utils/formatting/currency_utils.dart';
import 'package:qubic_exchange/utils/index.dart';

import 'package:remixicon/remixicon.dart';
import './widgets/index.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  final CurrencyService _currencyService = CurrencyService.instance;

  @override
  void initState() {
    super.initState();
  }

  // 设置项
  List<dynamic> _getSettingOptions() => [
    {'text': '语言', 'value': '中文'},
    {'text': '主题', 'value': _getCurrentThemeName()},
    {'text': '计价货币', 'value': _getCurrentCurrencyName()},
    {'text': '涨跌幅基准', 'value': '24小时'},
  ];

  // 获取当前主题显示名称
  String _getCurrentThemeName() {
    final themeProvider = context.read<ThemeProvider>();
    final isDark = themeProvider.isDark;

    final mode = isDark ? '夜晚模式' : '白天模式';
    return mode;
  }

  // 获取当前货币显示名称
  String _getCurrentCurrencyName() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final selectedCurrency = authProvider.selectedCurrency;
    if (selectedCurrency != null) {
      return '${selectedCurrency.symbol} ${selectedCurrency.name.toUpperCase()}';
    }
    return CurrencyUtils.getCurrentCode();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.templateColors.background,
      appBar: AppBarWidget(title: '偏好设置', showBackButton: true),
      body: Consumer2<ThemeProvider, AuthProvider>(
        builder: (context, themeProvider, authProvider, child) {
          return Padding(padding: EdgeInsets.all(UiConstants.spacing16), child: _buildSettingOptions());
        },
      ),
    );
  }

  // 设置列表
  Widget _buildSettingOptions() {
    final settingOptions = _getSettingOptions();

    return SizedBox(
      child: Column(
        children:
            settingOptions.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              return _buildSettingItem(
                title: item['text'],
                value: item['value'],
                imageName: item['imageName'],
                onTap: () => _handleSettingTap(index, item['text']),
              );
            }).toList(),
      ),
    );
  }

  // 处理设置项点击事件
  void _handleSettingTap(int index, String title) {
    switch (index) {
      case 0: // 语言
        // TODO: 实现语言切换
        break;
      case 1: // 主题
        ThemeSwitcher.show(context);
        break;
      case 2: // 计价币种
        _handleCurrencySelection(context);
        break;
      case 3: // 涨跌幅基准
        // 实现涨跌幅基准选择
        break;
    }
  }

  // 处理货币选择
  Future<void> _handleCurrencySelection(BuildContext context) async {
    final selectedCurrencyCode = await CurrencySwitcher.show(context, selectedCurrency: _currencyService.currentCurrencyCode);

    if (selectedCurrencyCode != null && mounted) {
      final success = await _currencyService.changeCurrency(selectedCurrencyCode);
      if (success && mounted) {
        setState(() {
          // 刷新界面以显示新的货币设置
        });
      }
    }
  }

  // 设置项
  Widget _buildSettingItem({required String title, String? value, String? imageName, required VoidCallback onTap}) {
    return InkWellWidget(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(bottom: UiConstants.spacing16),
        padding: EdgeInsets.symmetric(vertical: UiConstants.spacing8),
        child: Row(
          children: [
            Text(title, style: context.templateStyle.text.bodyTextMedium),
            Spacer(),
            if (value != null) Text(value, style: context.templateStyle.text.descriptionText),
            if (imageName != null) ThemedImage.asset(imageName, width: 20, height: 20),
            SizedBox(width: UiConstants.spacing4),
            Icon(RemixIcons.arrow_right_s_line, size: 20, color: context.templateColors.textTertiary),
          ],
        ),
      ),
    );
  }
}
