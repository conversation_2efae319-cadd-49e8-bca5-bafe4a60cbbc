/*
  主题切换底部弹窗
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/utils/index.dart';

import 'package:qubic_exchange/core/index.dart';
import 'package:remixicon/remixicon.dart';

class ThemeSwitcher {
  // 显示主题选择底部弹窗
  static Future<void> show(BuildContext context) async {
    await BottomSheetWidget.show(
      context: context,
      title: '主题',
      showCloseButton: true,
      showBottomButton: false,
      showHeader: false,
      showHeaderBorder: false,
      child: _ThemeSelectionContent(),
    );
  }
}

class _ThemeSelectionContent extends StatefulWidget {
  @override
  _ThemeSelectionContentState createState() => _ThemeSelectionContentState();
}

class _ThemeSelectionContentState extends State<_ThemeSelectionContent> {
  final ThemeProvider _themeProvider = ThemeProvider.instance;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: UiConstants.spacing20,
        bottom: ScreenUtil.bottomSafeHeight(context),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildThemeModeItem(AppThemeMode.light, '白天模式'),
          _buildThemeModeItem(AppThemeMode.dark, '夜晚模式'),
          _buildThemeModeItem(AppThemeMode.system, '跟随系统'),

          SizedBox(height: UiConstants.spacing16),
        ],
      ),
    );
  }

  // 构建主题模式选项
  Widget _buildThemeModeItem(AppThemeMode mode, String displayName) {
    final isSelected = _themeProvider.themeMode == mode;

    return InkWellWidget(
      onTap: () async {
        if (!isSelected) {
          await _themeProvider.setThemeMode(mode);
          if (mounted) {
            Navigator.pop(context);
          }
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: UiConstants.spacing16),
        child: Row(
          children: [
            // 模式名称
            Expanded(
              child: Text(
                displayName,
                style: context.templateStyle.text.bodyText,
              ),
            ),

            // 选中状态图标
            if (isSelected)
              Icon(
                RemixIcons.check_line,
                size: UiConstants.iconSize20,
                color: context.templateColors.primary,
              ),
          ],
        ),
      ),
    );
  }
}
