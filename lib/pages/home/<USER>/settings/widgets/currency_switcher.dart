/*
 * 货币切换底部弹窗
 *
 * 功能：
 * - 支持搜索货币
 * - 显示货币列表
 * - 选择货币状态管理
 * - 无搜索结果提示
 */

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:qubic_exchange/utils/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/services/data/currency_service.dart';
import 'package:qubic_exchange/core/models/currency_model.dart';
import 'package:qubic_exchange/models/currency/index.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';
import 'package:remixicon/remixicon.dart';

/// 货币切换器
class CurrencySwitcher {
  /// 显示货币选择底部弹窗
  static Future<String?> show(
    BuildContext context, {
    String? selectedCurrency,
    bool showHeader = true,
    double? headerHeight,
    bool showHeaderBorder = false,
    bool showDragHandle = false,
    double? maxHeight,
  }) async {
    final currencyService = CurrencyService.instance;

    return await BottomSheetWidget.show<String>(
      context: context,
      title: '计价货币',
      showCloseButton: true,
      showHeader: showHeader,
      showDragHandle: showDragHandle,
      headerHeight: headerHeight ?? 58,
      showHeaderBorder: showHeaderBorder,
      showBottomButton: false,
      maxHeight: maxHeight ?? ScreenUtil.getScreenInfo(context)['screenHeight'] * 0.8,
      useSafeArea: false,
      child: _CurrencySelectionContent(selectedCurrency: selectedCurrency ?? currencyService.currentCurrencyCode),
    );
  }
}

/// 货币选择内容组件
class _CurrencySelectionContent extends StatefulWidget {
  final String selectedCurrency;

  const _CurrencySelectionContent({required this.selectedCurrency});

  @override
  State<_CurrencySelectionContent> createState() => _CurrencySelectionContentState();
}

class _CurrencySelectionContentState extends State<_CurrencySelectionContent> {
  /// 列表高度占屏幕高度的比例
  static const double _listHeightRatio = 0.6;

  late String _selectedCurrency;
  final TextEditingController _searchController = TextEditingController();
  List<SupportedCurrency> _filteredCurrencies = [];

  @override
  void initState() {
    super.initState();
    _selectedCurrency = widget.selectedCurrency;
    _searchController.addListener(_onSearchChanged);
    _loadCurrencies();
  }

  void _loadCurrencies() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (authProvider.supportedCurrencies.isEmpty) {
      // 如果数据为空，触发重新加载
      authProvider.initializeCurrency().then((_) {
        if (mounted) {
          setState(() {
            _filteredCurrencies = authProvider.supportedCurrencies;
          });
        }
      });
    } else {
      _filteredCurrencies = authProvider.supportedCurrencies;
    }
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  /// 搜索变化处理
  void _onSearchChanged() {
    final query = _searchController.text.trim();
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    setState(() {
      if (query.isEmpty) {
        _filteredCurrencies = authProvider.supportedCurrencies;
      } else {
        _filteredCurrencies =
            authProvider.supportedCurrencies
                .where(
                  (currency) =>
                      currency.name.toLowerCase().contains(query.toLowerCase()) ||
                      currency.symbol.toLowerCase().contains(query.toLowerCase()),
                )
                .toList();
      }
    });
  }

  /// 处理货币选择
  void _onCurrencySelected(SupportedCurrency currency) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    authProvider.updateSelectedCurrency(currency);
    Navigator.of(context).pop(currency.name);
  }

  @override
  Widget build(BuildContext context) {
    final listHeight = MediaQuery.of(context).size.height * _listHeightRatio;

    return Padding(
      padding: EdgeInsets.only(top: UiConstants.spacing20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [_buildSearchBox(), _buildCurrencyList(listHeight), SizedBox(height: UiConstants.spacing16)],
      ),
    );
  }

  /// 构建搜索框
  Widget _buildSearchBox() {
    return Padding(
      padding: EdgeInsets.only(bottom: UiConstants.spacing8),
      child: TextFieldWidget(
        height: 38,
        hintText: '搜索',
        controller: _searchController,
        prefixIcon: Padding(
          padding: EdgeInsets.only(left: UiConstants.spacing12),
          child: ThemedImage.asset('icon_search', width: 18, height: 18),
        ),
      ),
    );
  }

  /// 构建货币列表
  Widget _buildCurrencyList(double height) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // 如果数据为空且AuthProvider有数据，更新本地数据
        if (_filteredCurrencies.isEmpty && authProvider.supportedCurrencies.isNotEmpty) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              _filteredCurrencies = authProvider.supportedCurrencies;
            });
          });
        }

        return SizedBox(
          height: height,
          child:
              _filteredCurrencies.isEmpty
                  ? Center(child: CircularProgressIndicator())
                  : ListView.builder(
                    itemCount: _filteredCurrencies.length,
                    itemBuilder: (context, index) {
                      return _buildCurrencyItem(_filteredCurrencies[index]);
                    },
                  ),
        );
      },
    );
  }

  /// 是否显示空状态
  bool _shouldShowEmptyState() {
    return _filteredCurrencies.isEmpty && _searchController.text.isNotEmpty;
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return const EmptyWidget(imageName: 'notData', text: '未找到相关货币');
  }

  /// 构建货币选项
  Widget _buildCurrencyItem(SupportedCurrency currency) {
    final isSelected = _selectedCurrency == currency.name;

    return InkWellWidget(
      onTap: () => _onCurrencySelected(currency),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: UiConstants.spacing16),
        decoration: BoxDecoration(border: Border(bottom: BorderSide(color: context.templateColors.divider, width: 0.5))),
        child: Row(
          children: [
            // 货币信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 货币名称和符号
                  Row(
                    children: [
                      Text(currency.symbol, style: context.templateStyle.text.bodyText),
                      SizedBox(width: UiConstants.spacing8),
                      Text(currency.name.toUpperCase(), style: context.templateStyle.text.bodyText),
                    ],
                  ),
                ],
              ),
            ),

            // 选中状态图标
            if (isSelected) Icon(RemixIcons.check_line, size: 20, color: context.templateColors.primary),
          ],
        ),
      ),
    );
  }
}
