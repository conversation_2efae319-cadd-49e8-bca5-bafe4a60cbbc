/*
*  涨跌颜色切换
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/utils/index.dart';
import 'package:qubic_exchange/core/index.dart';

class RiseColorSwitcher {
  /// 显示货币选择底部弹窗
  static Future<String?> show(
    BuildContext context, {
    String? selectedCurrency,
    bool showHeader = true,
    double? headerHeight,
    bool showHeaderBorder = false,
    bool showDragHandle = false,
    double? maxHeight,
  }) async {
    return await BottomSheetWidget.show<String>(
      context: context,
      title: '涨跌颜色',
      titleAlign: TextAlign.start,
      showCloseButton: false,
      showHeader: showHeader,
      showDragHandle: showDragHandle,
      headerHeight: headerHeight ?? 58,
      showHeaderBorder: showHeaderBorder,
      showBottomButton: false,
      maxHeight:
          maxHeight ?? ScreenUtil.getScreenInfo(context)['screenHeight'] * 0.8,
      useSafeArea: false,
      child: _CurrencySelectionContent(
        selectedCurrency: selectedCurrency ?? '',
      ),
    );
  }
}

class _CurrencySelectionContent extends StatefulWidget {
  final String selectedCurrency;

  const _CurrencySelectionContent({required this.selectedCurrency});

  @override
  State<_CurrencySelectionContent> createState() =>
      _CurrencySelectionContentState();
}

class _CurrencySelectionContentState extends State<_CurrencySelectionContent> {
  // 选项列表
  final List<Map<String, dynamic>> _options = [
    {'text': '绿涨红跌', 'imageName': 'ic_green_up'},
    {'text': '红涨绿跌', 'imageName': 'ic_red_up'},
  ];

  // 当前选中的选项
  late String _selectedOption;

  @override
  void initState() {
    super.initState();
    // 如果没有传入选中项或传入的选中项为空，则默认选中第一项
    _selectedOption =
        widget.selectedCurrency.isEmpty
            ? _options.first['text']
            : widget.selectedCurrency;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: ScreenUtil.bottomSafeHeight(context)),
      child: Row(
        children: [
          ..._options.expand(
            (key) => [
              _buildRadioItem(
                text: key['text'],
                isSelected: _selectedOption == key['text'],
                imageName: key['imageName'],
                onTap: () => _onOptionSelected(key['text']),
              ),
              if (key != _options.last) SizedBox(width: UiConstants.spacing8),
            ],
          ),
        ],
      ),
    );
  }

  // 处理选项选择
  void _onOptionSelected(String option) {
    setState(() {
      _selectedOption = option;
    });
    // 延迟关闭弹窗，让用户看到选中效果
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        Navigator.of(context).pop(option);
      }
    });
  }

  // 构建单选项
  Widget _buildRadioItem({
    required String text,
    bool isSelected = false,
    required String imageName,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: InkWellWidget(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.all(UiConstants.spacing14),
          decoration: BoxDecoration(
            color:
                isSelected
                    ? context.templateColors.primary.withValues(alpha: 0.1)
                    : context.templateColors.surface,
            border: Border.all(
              width: isSelected ? 1.0 : 0.5,
              color:
                  isSelected
                      ? context.templateColors.primary
                      : context.templateColors.divider,
            ),
            borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  ThemedImage.asset(imageName, width: 20, height: 20),
                  Spacer(),
                  Container(
                    width: 14,
                    height: 14,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color:
                          isSelected
                              ? context.templateColors.primary
                              : context.templateColors.surface,
                      border: Border.all(
                        color: context.templateColors.primary,
                        width: 1,
                      ),
                    ),
                    child:
                        isSelected
                            ? Icon(Icons.check, size: 10, color: Colors.white)
                            : null,
                  ),
                ],
              ),
              SizedBox(height: UiConstants.spacing10),
              Text(text, style: context.templateStyle.text.bodyTextMedium),
            ],
          ),
        ),
      ),
    );
  }
}
