/*
*  钱包盈亏概览组件
*
*  支持外部传入盈亏和余额数据
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';

/// 钱包盈亏数据模型
class WalletProfitData {
  final String title; // 标题
  final String value; // 主要数值
  final String unit; // 主要单位
  final String equivalentValue; // 等值数值
  final String equivalentUnit; // 等值单位
  final bool showDashed; // 是否显示虚线下划线

  const WalletProfitData({
    required this.title,
    required this.value,
    required this.unit,
    this.equivalentValue = '',
    this.equivalentUnit = '',
    this.showDashed = false,
  });

  /// 创建默认的总盈亏数据
  static WalletProfitData defaultTotalProfit() {
    return const WalletProfitData(
      title: '总盈亏',
      value: '0.00',
      unit: 'USDT',
      equivalentValue: '0.00',
      equivalentUnit: 'CNY',
      showDashed: true,
    );
  }

  /// 创建默认的可用余额数据
  static WalletProfitData defaultAvailableBalance() {
    return const WalletProfitData(
      title: '可用余额',
      value: '0.00',
      unit: 'USDT',
      equivalentValue: '0.00',
      equivalentUnit: 'CNY',
    );
  }
}

class WalletProfitOverview extends StatefulWidget {
  /// 外边距
  final EdgeInsets? padding;

  /// 总盈亏数据
  final WalletProfitData? totalProfitData;

  /// 可用余额数据
  final WalletProfitData? availableBalanceData;

  /// 点击总盈亏回调
  final VoidCallback? onTotalProfitTap;

  /// 点击可用余额回调
  final VoidCallback? onAvailableBalanceTap;

  const WalletProfitOverview({
    super.key,
    this.padding,
    this.totalProfitData,
    this.availableBalanceData,
    this.onTotalProfitTap,
    this.onAvailableBalanceTap,
  });

  @override
  State<WalletProfitOverview> createState() => _WalletProfitOverviewState();
}

class _WalletProfitOverviewState extends State<WalletProfitOverview> {
  /// 获取总盈亏数据
  WalletProfitData get _totalProfitData =>
      widget.totalProfitData ?? WalletProfitData.defaultTotalProfit();

  /// 获取可用余额数据
  WalletProfitData get _availableBalanceData =>
      widget.availableBalanceData ?? WalletProfitData.defaultAvailableBalance();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding,
      child: Row(
        children: [
          _buildItem(data: _totalProfitData, onTap: widget.onTotalProfitTap),
          _buildItem(
            data: _availableBalanceData,
            onTap: widget.onAvailableBalanceTap,
          ),
        ],
      ),
    );
  }

  /// 构建单项
  Widget _buildItem({required WalletProfitData data, VoidCallback? onTap}) {
    Widget content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          data.title,
          style: context.templateStyle.text.descriptionText.copyWith(
            decorationStyle:
                data.showDashed ? TextDecorationStyle.dashed : null,
            decoration: data.showDashed ? TextDecoration.underline : null,
            decorationColor: context.templateColors.textTertiary,
          ),
        ),
        SizedBox(height: UiConstants.spacing4),
        Text(
          '${data.value} ${data.unit}',
          style: context.templateStyle.text.bodyTextMedium,
        ),
        if (data.equivalentValue.isNotEmpty)
          Text(
            '${data.equivalentValue}  ${data.equivalentUnit}',
            style: context.templateStyle.text.descriptionSmall,
          ),
      ],
    );

    return Expanded(
      child:
          onTap != null
              ? GestureDetector(
                onTap: onTap,
                behavior: HitTestBehavior.opaque,
                child: content,
              )
              : content,
    );
  }
}
