/*
*  资产头部组件
*
*  功能：
*  - 支持自定义标题文本
*  - 可控制今日盈亏显示/隐藏
*  - 支持自定义菜单项配置
*  - 支持货币切换
*  - 自动适配主题模板
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/pages/assets/widgets/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

/// 总资产数据模型
class TotalAssetsData {
  final String usdtValue;
  final String fiatValue;
  final String fiatUnit;

  const TotalAssetsData({
    required this.usdtValue,
    required this.fiatValue,
    required this.fiatUnit,
  });
}

/// 菜单项数据模型
class HeaderMenuItem {
  final String iconName;
  final String title;
  final VoidCallback? onTap;

  const HeaderMenuItem({
    required this.iconName,
    required this.title,
    this.onTap,
  });
}

class HeaderSection extends StatefulWidget {
  /// 标题文本，默认为"现货估值"
  final String titleText;

  /// 是否显示今日盈亏，默认显示
  final bool showDailyPnL;

  /// 自定义菜单项列表，最多支持4个
  final List<HeaderMenuItem> menuItems;

  /// 余额数值
  final String balanceValue;

  /// 余额单位
  final String balanceUnit;

  /// 等值货币显示
  final String equivalentValue;

  /// 总资产数据
  final TotalAssetsData? totalAssetsData;

  /// 插入内容
  final Widget? insertWidget;

  const HeaderSection({
    super.key,
    this.titleText = '现货估值',
    this.showDailyPnL = true,
    this.menuItems = const [],
    this.balanceValue = '1,000,000.001',
    this.balanceUnit = 'BTC',
    this.equivalentValue = '≈ 1,000,000.00 CNY',
    this.totalAssetsData,
    this.insertWidget,
  });

  @override
  State<HeaderSection> createState() => _HeaderSectionState();
}

class _HeaderSectionState extends State<HeaderSection> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          _buildTitle(),
          SizedBox(height: UiConstants.spacing12),
          // 余额
          _buildBalance(),

          // 插入内容
          if (widget.insertWidget != null) ...[
            SizedBox(height: UiConstants.spacing20),
            widget.insertWidget!,
          ],

          // 菜单（仅在有菜单项时显示）
          if (widget.menuItems.isNotEmpty) _buildMenu(),
        ],
      ),
    );
  }

  /// 构建标题部分
  Widget _buildTitle() {
    return Row(
      children: [
        Text(
          widget.titleText,
          style: context.templateStyle.text.descriptionText,
        ),
        SizedBox(width: UiConstants.spacing4),
        InkWellWidget(
          onTap:
              () => {
                // TODO: 余额是否可见
              },
          child: Icon(
            RemixIcons.eye_fill,
            size: UiConstants.iconSize14,
            color: context.templateColors.textTertiary,
          ),
        ),
        Spacer(),
        ThemedImage(
          name: 'orderHistory',
          size: UiConstants.iconSize24,
          followTheme: true,
        ),
      ],
    );
  }

  /// 构建余额部分
  Widget _buildBalance() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 主要余额显示
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              widget.totalAssetsData?.usdtValue ?? widget.balanceValue,
              style: context.templateStyle.text.h2
            ),
            Padding(
              padding: EdgeInsets.only(
                bottom: UiConstants.spacing4,
                left: UiConstants.spacing8,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'USDT',
                    style: context.templateStyle.text.bodyLargeMedium,
                  ),
                  ThemedImage(
                    name: 'arrow_triangle_down',
                    size: UiConstants.iconSize10,
                    margin: EdgeInsets.only(left: UiConstants.spacing4),
                    followTheme: true,
                  ),
                ],
              ),
            ),
          ],
        ),
        // 等值货币显示
        Text(
          widget.totalAssetsData != null
            ? '≈ ${widget.totalAssetsData!.fiatValue} ${widget.totalAssetsData!.fiatUnit}'
            : widget.equivalentValue,
          style: context.templateStyle.text.descriptionText,
        ),
        // 今日盈亏（可选显示）
        if (widget.showDailyPnL) ...[
          SizedBox(height: UiConstants.spacing10),
          AssetsProfit(title: '今日盈亏', value: '0.00', unit: 'USDT'),
        ],
      ],
    );
  }

  /// 构建菜单部分
  Widget _buildMenu() {
    final displayItems = widget.menuItems.take(4).toList();

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing24,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: _buildMenuItems(displayItems),
      ),
    );
  }

  /// 构建菜单项列表
  List<Widget> _buildMenuItems(List<HeaderMenuItem> items) {
    final List<Widget> widgets = [];

    for (int i = 0; i < items.length; i++) {
      final item = items[i];

      // 添加菜单项
      widgets.add(
        Expanded(
          child: InkWellWidget(
            onTap: item.onTap,
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.all(UiConstants.spacing10),
                  margin: EdgeInsets.only(bottom: UiConstants.spacing4),
                  decoration: BoxDecoration(
                    color: context.templateColors.inputBackground,
                    borderRadius: BorderRadius.circular(
                      context.templateStyles.borderRadiusLarge,
                    ),
                  ),
                  child: ThemedImage(
                    name: item.iconName,
                    size: UiConstants.iconSize28,
                    followTheme: true,
                    fit: BoxFit.cover,
                  ),
                ),
                Text(item.title, style: context.templateStyle.text.bodySmall),
              ],
            ),
          ),
        ),
      );

      // 添加间距（除了最后一个）
      if (i < items.length - 1) {
        widgets.add(SizedBox(width: UiConstants.spacing36));
      }
    }

    return widgets;
  }
}
