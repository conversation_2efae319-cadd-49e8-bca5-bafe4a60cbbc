/*
*  小额转换
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class SmallBalanceConvert extends StatefulWidget {
  const SmallBalanceConvert({super.key});

  @override
  State<SmallBalanceConvert> createState() => _SmallBalanceConvertState();
}

class _SmallBalanceConvertState extends State<SmallBalanceConvert> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: Container(
        padding: EdgeInsets.all(UiConstants.spacing10),
        decoration: BoxDecoration(
          border: Border.all(
            width: UiConstants.borderWidth0_5,
            color: context.templateColors.divider,
          ),
          borderRadius: BorderRadius.circular(
            context.templateStyles.borderRadiusMedium,
          ),
        ),
        child: Row(
          children: [
            ThemedImage(
              name: '',
              width: 18,
              height: 18,
              followTheme: true,
              margin: EdgeInsets.only(right: UiConstants.spacing8),
            ),
            Text('小额兑换 BGB', style: context.templateStyle.text.bodyTextMedium),
            Spacer(),
            Icon(
              RemixIcons.arrow_right_s_line,
              size: UiConstants.iconSize20,
              color: context.templateColors.textPrimary,
            ),
          ],
        ),
      ),
    );
  }
}
