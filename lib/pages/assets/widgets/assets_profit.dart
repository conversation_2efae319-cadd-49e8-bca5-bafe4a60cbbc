/*
*  收益
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:remixicon/remixicon.dart';

class AssetsProfit extends StatefulWidget {
  final String? title;
  final String? value;
  final String? unit;
  final bool showArrow;

  const AssetsProfit({
    super.key,
    this.title,
    this.value,
    this.unit,
    this.showArrow = true,
  });

  @override
  State<AssetsProfit> createState() => _AssetsProfitState();
}

class _AssetsProfitState extends State<AssetsProfit> {
  Color _getColorByValue() {
    // 清理字符串，移除可能的符号和空格
    final cleanValue = widget.value?.replaceAll(RegExp(r'[^\d.-]'), '') ?? '0';
    final numValue = double.tryParse(cleanValue) ?? 0;

    if (numValue > 0) {
      return context.templateColors.tradeBuy;
    } else if (numValue < 0) {
      return context.templateColors.tradeSell;
    } else {
      return context.templateColors.textSecondary;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: '${widget.title} ',
                  style: context.templateStyle.text.bodyText.copyWith(
                    decorationStyle: TextDecorationStyle.dashed,
                    decoration: TextDecoration.underline,
                    decorationColor: context.templateColors.textTertiary,
                  ),
                ),
                TextSpan(
                  text: '${widget.value ?? '0'} ${widget.unit}',
                  style: context.templateStyle.text.descriptionText.copyWith(
                    color: _getColorByValue(),
                  ),
                ),
              ],
            ),
          ),
          if (widget.showArrow)
            Icon(
              RemixIcons.arrow_right_s_line,
              size: UiConstants.fontSize20,
              color: context.templateColors.textTertiary,
            ),
        ],
      ),
    );
  }
}
