/*
*  数字货币项组件
*
*  功能：
*  - 显示数字货币基本信息（图标、名称、简称）
*  - 显示持仓数量和价值
*  - 显示今日盈亏和成本价格
*  - 支持自定义数据传入
*  - 自动适配主题模板
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

/// 数字货币数据模型
class CryptoItemData {
  final String symbol; // 币种符号，如 'TRX'
  final String name; // 币种名称，如 'TRON'
  final String iconName; // 图标名称
  final String balance; // 持仓数量
  final String balanceValue; // 持仓价值（计价货币）
  final String? dailyPnL; // 今日盈亏（计价货币，可选）
  final String? costPrice; // 成本价格（USDT，可选）
  final bool isPositivePnL; // 盈亏是否为正
  final String quoteCurrency; // 计价货币符号，如 'CNY'

  const CryptoItemData({
    required this.symbol,
    required this.name,
    required this.iconName,
    required this.balance,
    required this.balanceValue,
    this.dailyPnL,
    this.costPrice,
    this.isPositivePnL = false,
    this.quoteCurrency = 'CNY',
  });

  /// 是否有盈亏数据
  bool get hasPnLData => dailyPnL != null && dailyPnL!.isNotEmpty;

  /// 是否有成本价格数据
  bool get hasCostPriceData => costPrice != null && costPrice!.isNotEmpty;

  /// 是否有任何额外数据（盈亏或成本价格）
  bool get hasExtraData => hasPnLData || hasCostPriceData;

  /// 格式化的今日盈亏显示文本
  String get formattedDailyPnL =>
      dailyPnL != null ? '$dailyPnL $quoteCurrency' : '';

  /// 格式化的成本价格显示文本
  String get formattedCostPrice => costPrice != null ? '$costPrice USDT' : '';
}

class CryptoItem extends StatefulWidget {
  /// 数字货币数据
  final CryptoItemData? data;

  /// 点击回调
  final VoidCallback? onTap;

  const CryptoItem({super.key, this.data, this.onTap});

  @override
  State<CryptoItem> createState() => _CryptoItemState();
}

class _CryptoItemState extends State<CryptoItem> {
  /// 获取默认数据
  CryptoItemData get _defaultData => const CryptoItemData(
    symbol: 'TRX',
    name: 'TRON',
    iconName: 'btc',
    balance: '0.000000',
    balanceValue: '0.00',
    quoteCurrency: 'CNY',
    // 默认不显示盈亏和成本价格
    dailyPnL: null,
    costPrice: null,
    isPositivePnL: false,
  );

  /// 获取当前数据
  CryptoItemData get _currentData => widget.data ?? _defaultData;

  @override
  Widget build(BuildContext context) {
    return InkWellWidget(
      onTap: widget.onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing18,
          vertical: UiConstants.spacing10,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 币种图标
            _buildCryptoIcon(),
            SizedBox(width: UiConstants.spacing12),
            // 主要内容区域
            Expanded(child: _buildMainContent()),
          ],
        ),
      ),
    );
  }

  /// 构建币种图标
  Widget _buildCryptoIcon() {
    return Padding(
      padding: EdgeInsets.only(top: UiConstants.spacing4),
      child: ThemedImage(
        name: _currentData.iconName,
        size: UiConstants.iconSize24,
        borderRadius: BorderRadius.circular(UiConstants.borderRadiusCircle),
        folder: ThemedAssetFolder.crypto,
      ),
    );
  }

  /// 构建主要内容
  Widget _buildMainContent() {
    return SizedBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 基本信息和余额
          _buildBasicInfoRow(),
          // 盈亏和成本价格（仅在有数据时显示）
          if (_currentData.hasExtraData) ...[
            SizedBox(height: UiConstants.spacing8),
            _buildPnLAndCostRow(),
          ],
        ],
      ),
    );
  }

  /// 构建基本信息行
  Widget _buildBasicInfoRow() {
    return Row(
      children: [
        // 币种信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _currentData.symbol,
                style: context.templateStyle.text.bodyLargeMedium,
              ),
              Text(
                _currentData.name,
                style: context.templateStyle.text.descriptionSmall,
              ),
            ],
          ),
        ),
        // 余额信息
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              _currentData.balance,
              style: context.templateStyle.text.bodyLargeMedium,
            ),
            Text(
              '${_currentData.balanceValue} ${_currentData.quoteCurrency}',
              style: context.templateStyle.text.descriptionSmall,
            ),
          ],
        ),
      ],
    );
  }

  /// 构建盈亏和成本价格行
  Widget _buildPnLAndCostRow() {
    final List<Widget> rows = [];

    // 今日盈亏行（仅在有数据时显示）
    if (_currentData.hasPnLData) {
      rows.add(
        Row(
          children: [
            Text('今日盈亏', style: context.templateStyle.text.descriptionSmall),
            Spacer(),
            Text(
              _currentData.formattedDailyPnL,
              style: context.templateStyle.text.descriptionSmall.copyWith(
                color:
                    _currentData.isPositivePnL
                        ? context.templateColors.tradeBuy
                        : context.templateColors.tradeSell,
              ),
            ),
          ],
        ),
      );
    }

    // 成本价格行（仅在有数据时显示）
    if (_currentData.hasCostPriceData) {
      rows.add(
        Row(
          children: [
            Text(
              '成本价格',
              style: context.templateStyle.text.descriptionSmall.copyWith(
                decorationStyle: TextDecorationStyle.dashed,
                decoration: TextDecoration.underline,
                decorationColor: context.templateColors.textTertiary,
              ),
            ),
            Spacer(),
            Text(
              _currentData.formattedCostPrice,
              style: context.templateStyle.text.descriptionSmall.copyWith(
                color: context.templateColors.textPrimary,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing14),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(width: 0.5, color: context.templateColors.divider),
        ),
      ),
      child: Column(children: rows),
    );
  }
}
