/*
*  法币项组件
*
*  功能：
*  - 显示法币基本信息（图标、代码、名称）
*  - 显示法币余额和等值
*  - 支持自定义数据传入
*  - 支持点击交互
*  - 自动适配主题模板
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

/// 法币数据模型
class FiatItemData {
  final String code; // 法币代码，如 'VND'
  final String name; // 法币名称，如 'Vietnam Dong'
  final String iconName; // 图标名称
  final String balance; // 余额
  final String balanceValue; // 等值显示（计价货币）
  final String quoteCurrency; // 计价货币符号，如 'CNY'

  const FiatItemData({
    required this.code,
    required this.name,
    required this.iconName,
    required this.balance,
    required this.balanceValue,
    this.quoteCurrency = 'CNY',
  });

  /// 格式化的等值显示文本
  String get formattedBalanceValue => '$balanceValue $quoteCurrency';
}

class FiatItem extends StatefulWidget {
  /// 法币数据
  final FiatItemData? data;

  /// 点击回调
  final VoidCallback? onTap;

  const FiatItem({super.key, this.data, this.onTap});

  @override
  State<FiatItem> createState() => _FiatItemState();
}

class _FiatItemState extends State<FiatItem> {
  /// 获取默认数据
  FiatItemData get _defaultData => const FiatItemData(
    code: 'VND',
    name: 'Vietnam Dong',
    iconName: 'vnd',
    balance: '0.00',
    balanceValue: '0.00',
    quoteCurrency: 'CNY',
  );

  /// 获取当前数据
  FiatItemData get _currentData => widget.data ?? _defaultData;

  @override
  Widget build(BuildContext context) {
    return InkWellWidget(
      onTap: widget.onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing18,
          vertical: UiConstants.spacing12,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 法币图标
            _buildFiatIcon(),
            SizedBox(width: UiConstants.spacing12),
            // 主要内容区域
            Expanded(child: _buildMainContent()),
          ],
        ),
      ),
    );
  }

  /// 构建法币图标
  Widget _buildFiatIcon() {
    return Padding(
      padding: EdgeInsets.only(top: UiConstants.spacing4),
      child: ThemedImage(
        name: _currentData.iconName,
        width: 24,
        height: 24,
        borderRadius: BorderRadius.circular(UiConstants.borderRadiusCircle),
        followTheme: true,
      ),
    );
  }

  /// 构建主要内容
  Widget _buildMainContent() {
    return SizedBox(
      child: Row(
        children: [
          // 法币信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _currentData.code,
                  style: context.templateStyle.text.bodyLargeMedium,
                ),
                Text(
                  _currentData.name,
                  style: context.templateStyle.text.descriptionSmall,
                ),
              ],
            ),
          ),
          // 余额信息
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                _currentData.balance,
                style: context.templateStyle.text.bodyLargeMedium,
              ),
              Text(
                _currentData.formattedBalanceValue,
                style: context.templateStyle.text.descriptionSmall,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
