/*
*  现货标签页
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';
import 'package:qubic_exchange/services/assets/assets_service.dart';
import 'package:qubic_exchange/services/assets/models/asset_balance_model.dart';
import 'package:qubic_exchange/services/market/market_service.dart';
import 'package:qubic_exchange/utils/formatting/number_format_util.dart';
import '../../widgets/index.dart';
import './widgets/crypto_item.dart';
import './widgets/fiat_item.dart';
import '../../../../models/pages/assets/common/menu_item_data.dart';

class SpotTabPage extends StatefulWidget {
  /// 加密货币数据列表
  final List<CryptoItemData>? cryptoDataList;

  /// 法币数据列表
  final List<FiatItemData>? fiatDataList;

  /// 数据刷新回调
  final Future<void> Function()? onRefresh;

  /// 加密货币项点击回调
  final void Function(CryptoItemData data)? onCryptoItemTap;

  /// 法币项点击回调
  final void Function(FiatItemData data)? onFiatItemTap;

  const SpotTabPage({
    super.key,
    this.cryptoDataList,
    this.fiatDataList,
    this.onRefresh,
    this.onCryptoItemTap,
    this.onFiatItemTap,
  });

  @override
  State<SpotTabPage> createState() => _SpotTabPageState();
}

class _SpotTabPageState extends State<SpotTabPage>
    with TickerProviderStateMixin {
  // 构建标签控制器
  late TabController _tabController;

  // 构建标签内容
  static const List<TabItem> _tabKeys = [
    TabItem(title: '加密货币'),
    TabItem(title: '法币'),
  ];

  /// 菜单项列表
  late final List<MenuItemData> _menuItems;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabKeys.length, vsync: this);

    // 初始化菜单项列表
    _menuItems = MenuItemBuilder.buildSpotMenuItems(
      onDeposit: _handleDeposit,
      onWithdraw: _handleWithdraw,
      onTransfer: _handleTransfer,
      onPnL: _handlePnL,
    );
  }

  // 销毁
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 获取默认加密货币数据
  List<CryptoItemData> get _defaultCryptoData => [
    const CryptoItemData(
      symbol: 'BTC',
      name: 'Bitcoin',
      iconName: 'btc',
      balance: '0.12345678',
      balanceValue: '17,500.00', // 持仓价值（CNY）
      dailyPnL: '+125.50', // 今日盈亏（CNY）
      costPrice: '45,000.00', // 成本价格（USDT）
      isPositivePnL: true,
      quoteCurrency: 'CNY',
    )
  ];

  /// 获取默认法币数据
  List<FiatItemData> get _defaultFiatData => [
    const FiatItemData(
      code: 'USD',
      name: 'US Dollar',
      iconName: 'usd',
      balance: '1,250.00',
      balanceValue: '8,750.00', // 等值（CNY）
      quoteCurrency: 'CNY',
    )
  ];

  /// 获取当前加密货币数据
  List<CryptoItemData> get _currentCryptoData =>
      widget.cryptoDataList ?? _defaultCryptoData;

  /// 获取当前法币数据
  List<FiatItemData> get _currentFiatData =>
      widget.fiatDataList ?? _defaultFiatData;

  // 下拉刷新
  Future<void> _onRefresh() async {
    if (widget.onRefresh != null) {
      // 使用外部传入的刷新回调
      await widget.onRefresh!();
    } else {
      // 默认刷新逻辑
      await Future.delayed(const Duration(seconds: 2));
      debugPrint('🔄 现货资产数据刷新完成');
    }
  }

  // 处理充值点击
  void _handleDeposit() {
    // TODO: 导航到充值页面
  }

  // 处理提现点击
  void _handleWithdraw() {
    // TODO: 导航到提现页面
  }

  // 处理划转点击
  void _handleTransfer() {
    // TODO: 导航到划转页面
  }

  // 处理盈亏点击
  void _handlePnL() {
    // TODO: 导航到盈亏页面
  }

  /// 计算现货账户总USDT价值
  double _calculateTotalUsdtValue() {
    final assetsService = AssetsService.instance;
    final spotAccount = assetsService.getAccountBalance(AccountType.spot);

    if (spotAccount == null) return 0.0;

    double totalUsdt = 0.0;

    for (final currency in spotAccount.currencies) {
      if (currency.totalAmount <= 0) continue;

      final symbol = currency.symbol;

      if (symbol == 'USDT') {
        totalUsdt += currency.totalAmount;
      } else {
        final currencyModel = MarketService.getCurrencyModel(currency.currencyId);

        if (currencyModel != null) {
          dynamic ticker;
          for (final key in ['1', '5', '0']) {
            ticker = currencyModel.tickers[key];
            if (ticker != null && ticker.lastPrice > 0) {
              break;
            }
          }

          if (ticker != null && ticker.lastPrice > 0) {
            final usdtValue = currency.totalAmount * ticker.lastPrice;
            totalUsdt += usdtValue;
          }
        }
      }
    }

    return totalUsdt;
  }

  /// 构建菜单项列表
  List<HeaderMenuItem> _buildMenuItems() {
    return _menuItems.map((menuData) {
      return HeaderMenuItem(
        iconName: menuData.iconName,
        title: menuData.title,
        onTap: menuData.onTap,
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final maxOffset = 44.0;
    return EasyRefresh.builder(
      header: ClassicHeader(
        clamping: true,
        position: IndicatorPosition.locator,
        triggerOffset: maxOffset,
        processedDuration: const Duration(seconds: 1),
        safeArea: false,
        showMessage: false,
        showText: false,
        maxOverOffset: maxOffset,
      ),
      onRefresh: _onRefresh,
      childBuilder: (context, physics) {
        return ScrollConfiguration(
          behavior: const ERScrollBehavior(),
          child: ExtendedNestedScrollView(
            physics: physics,
            onlyOneScrollInBody: true,
            pinnedHeaderSliverHeightBuilder: () {
              return 60.0;
            },
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return <Widget>[
                // 刷新指示器定位器
                const HeaderLocator.sliver(clearExtent: false),

                // 顶部区域
                SliverToBoxAdapter(
                  child: Consumer<AuthProvider>(
                    builder: (context, authProvider, child) {
                      final totalUsdt = _calculateTotalUsdtValue();
                      final fiatValue = totalUsdt * authProvider.currentRate;
                      final fiatSymbol = authProvider.selectedCurrency?.symbol ?? 'USD';

                      return Column(
                        children: [
                          HeaderSection(
                            titleText: '现货估值',
                            showDailyPnL: true,
                            menuItems: _buildMenuItems(),
                            balanceValue: NumberFormatUtil.formatWithComma(totalUsdt, decimalDigits: 8),
                            balanceUnit: 'USDT',
                            equivalentValue: '≈ ${NumberFormatUtil.formatWithComma(fiatValue, decimalDigits: 2)} $fiatSymbol',
                          ),
                          SmallBalanceConvert(),
                        ],
                      );
                    },
                  ),
                ),

                // 标签栏
                SliverPersistentHeader(
                  pinned: true,
                  delegate: StickyDelegate(
                    maxHeight: 60,
                    child: _buildTabbar(),
                    backgroundColor: context.templateColors.surface,
                  ),
                ),
              ];
            },
            body: _buildTabview(physics),
          ),
        );
      },
    );
  }

  // 构建标签栏
  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: Row(
        children: [
          Expanded(
            child: TabbarWidget(
              height: 50,
              controller: _tabController,
              tabs: _tabKeys,
              labelStyle: context.templateStyle.text.tabText,
              selectedColor: context.templateColors.textPrimary,
              unselectedColor: context.templateColors.textSecondary,
              showIndicator: true,
              indicatorSize: TabBarIndicatorSize.label,
              labelPadding: EdgeInsets.only(right: UiConstants.spacing18),
            ),
          ),
          ThemedImage(
            name: 'icon_search',
            width: 20,
            height: 20,
            margin: EdgeInsets.only(right: UiConstants.spacing18),
          ),
          ThemedImage(name: '', width: 24, height: 24, followTheme: true),
        ],
      ),
    );
  }

  // 构建标签内容
  Widget _buildTabview(ScrollPhysics physics) {
    return TabBarView(
      controller: _tabController,
      children: [
        // 加密货币
        _buildCryptoList(physics),

        // 法币
        _buildFiatList(physics),
      ],
    );
  }

  // 构建加密数字货币列表
  Widget _buildCryptoList(ScrollPhysics physics) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final assetsService = AssetsService.instance;
        final spotAccount = assetsService.getAccountBalance(AccountType.spot);

        if (spotAccount == null || spotAccount.currencies.isEmpty) {
          return _buildEmptyState('暂无现货资产');
        }

        // 过滤有余额的币种
        final currenciesWithBalance = spotAccount.currenciesWithBalance;

        if (currenciesWithBalance.isEmpty) {
          return _buildEmptyState('暂无加密货币资产');
        }

        return ListView.builder(
          physics: physics,
          itemCount: currenciesWithBalance.length,
          itemBuilder: (context, index) {
            final currency = currenciesWithBalance[index];

            // 计算USDT价值
            double usdtValue = 0.0;
            final symbol = currency.symbol;

            if (symbol == 'USDT') {
              usdtValue = currency.totalAmount;
            } else {
              // 通过currency_id获取币种模型和ticker数据
              final currencyModel = MarketService.getCurrencyModel(currency.currencyId);
              if (currencyModel != null) {
                // 尝试获取ticker数据
                dynamic ticker;
                for (final key in ['1', '5', '0']) {
                  ticker = currencyModel.tickers[key];
                  if (ticker != null && ticker.lastPrice > 0) {
                    break;
                  }
                }
                if (ticker != null && ticker.lastPrice > 0) {
                  usdtValue = currency.totalAmount * ticker.lastPrice;
                }
              }
            }

            // 计算法币价值
            final fiatValue = usdtValue * authProvider.currentRate;
            final fiatSymbol = authProvider.selectedCurrency?.symbol ?? 'USD';

            // 构建CryptoItemData
            final cryptoData = CryptoItemData(
              symbol: currency.symbol,
              name: currency.symbol,
              iconName: currency.symbol.toLowerCase(),
              balance: NumberFormatUtil.formatWithComma(currency.totalAmount, decimalDigits: 8),
              balanceValue: NumberFormatUtil.formatWithComma(fiatValue, decimalDigits: 2),
              quoteCurrency: fiatSymbol,
              dailyPnL: '0.00',
              costPrice: '0.00',
              isPositivePnL: true,
            );

            return CryptoItem(
              data: cryptoData,
              onTap: () => widget.onCryptoItemTap?.call(cryptoData),
            );
          },
        );
      },
    );
  }

  // 构建法币列表
  Widget _buildFiatList(ScrollPhysics physics) {
    final fiatData = _currentFiatData;

    if (fiatData.isEmpty) {
      return _buildEmptyState('暂无法币资产');
    }

    return ListView.builder(
      physics: physics,
      itemCount: fiatData.length,
      itemBuilder: (context, index) {
        final data = fiatData[index];
        return FiatItem(
          data: data,
          onTap: () => widget.onFiatItemTap?.call(data),
        );
      },
    );
  }

  /// 构建空状态
  Widget _buildEmptyState(String message) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(UiConstants.spacing32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_wallet_outlined,
              size: 64,
              color: context.templateColors.textTertiary,
            ),
            SizedBox(height: UiConstants.spacing16),
            Text(
              message,
              style: context.templateStyle.text.bodyText.copyWith(
                color: context.templateColors.textTertiary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
