/*
*  合约跟单项组件
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/services/assets/models/asset_balance_model.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';
import 'package:qubic_exchange/utils/formatting/number_format_util.dart';


class CopyItem extends StatefulWidget {
  final CurrencyBalance? currency;
  final AuthProvider? authProvider;
  final VoidCallback? onTap;

  const CopyItem({
    super.key,
    this.currency,
    this.authProvider,
    this.onTap,
  });

  @override
  State<CopyItem> createState() => _CopyItemState();
}

class _CopyItemState extends State<CopyItem> {
  /// 格式化数字，去掉末尾的0，至少保留2位小数，添加千分位分隔符
  String _formatBalance(double amount) {
    // 先格式化为8位小数
    String formatted = amount.toStringAsFixed(8);

    // 去掉末尾的0
    formatted = formatted.replaceAll(RegExp(r'0*$'), '');

    // 如果末尾是小数点，去掉小数点
    if (formatted.endsWith('.')) {
      formatted = formatted.substring(0, formatted.length - 1);
    }

    // 确保至少有2位小数
    if (!formatted.contains('.')) {
      formatted += '.00';
    } else {
      final parts = formatted.split('.');
      if (parts[1].length < 2) {
        formatted += '0' * (2 - parts[1].length);
      }
    }

    // 分离整数部分和小数部分
    final parts = formatted.split('.');
    String integerPart = parts[0];
    String decimalPart = parts.length > 1 ? parts[1] : '';

    // 为整数部分添加千分位分隔符
    String formattedInteger = '';
    for (int i = 0; i < integerPart.length; i++) {
      if (i > 0 && (integerPart.length - i) % 3 == 0) {
        formattedInteger += ',';
      }
      formattedInteger += integerPart[i];
    }

    // 组合整数部分和小数部分
    return decimalPart.isNotEmpty ? '$formattedInteger.$decimalPart' : formattedInteger;
  }

  @override
  Widget build(BuildContext context) {
    final currency = widget.currency;
    final authProvider = widget.authProvider;

    // 如果没有数据，显示默认内容
    if (currency == null || authProvider == null) {
      return _buildDefaultContent();
    }

    // 计算法币价值
    final fiatValue = currency.totalAmount * authProvider.currentRate;
    final fiatSymbol = authProvider.selectedCurrency?.symbol ?? 'USD';

    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing18,
          vertical: UiConstants.spacing14,
        ),
        child: Row(
          children: [
            ThemedImage(
              name: currency.symbol.toLowerCase(),
              size: 24,
              folder: ThemedAssetFolder.crypto,
              borderRadius: BorderRadius.circular(UiConstants.borderRadiusCircle),
            ),
            SizedBox(width: UiConstants.spacing12),
            Expanded(
              child: Row(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        currency.symbol,
                        style: context.templateStyle.text.bodyTextMedium,
                      ),
                      Text(
                        '合约跟单',
                        style: context.templateStyle.text.descriptionSmall.copyWith(
                          color: context.templateColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  Spacer(),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${_formatBalance(currency.totalAmount)} ${currency.symbol}',
                        style: context.templateStyle.text.bodyTextMedium,
                      ),
                      Text(
                        '$fiatSymbol ${NumberFormatUtil.formatWithComma(fiatValue, decimalDigits: 2)}',
                        style: context.templateStyle.text.descriptionSmall.copyWith(
                          color: context.templateColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建默认内容（当没有数据时）
  Widget _buildDefaultContent() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing14,
      ),
      child: Row(
        children: [
          ThemedImage(name: '', size: 24, folder: ThemedAssetFolder.crypto),
          SizedBox(width: UiConstants.spacing12),
          Expanded(
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'USDT',
                      style: context.templateStyle.text.bodyTextMedium,
                    ),
                    Text(
                      '合约跟单',
                      style: context.templateStyle.text.descriptionSmall.copyWith(
                        color: context.templateColors.textSecondary,
                      ),
                    ),
                  ],
                ),
                Spacer(),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '0.00 USDT',
                      style: context.templateStyle.text.bodyTextMedium,
                    ),
                    Text(
                      '¥ 0.00',
                      style: context.templateStyle.text.descriptionSmall.copyWith(
                        color: context.templateColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
