/*
*  合约标签页
*
*  支持USDT-M、币本位、USDC-M三种合约类型
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import './tabviews/usdt_margin_contract_page.dart';
import './tabviews/coin_margin_contract_page.dart';
import './tabviews/usdc_margin_contract_page.dart';

/// 合约类型枚举
enum ContractType {
  usdtMargin('USDT-M'),
  coinMargin('币本位'),
  usdcMargin('USDC-M');

  const ContractType(this.displayName);
  final String displayName;
}

class FuturesTabPage extends StatefulWidget {
  /// 初始选中的合约类型
  final ContractType initialContractType;

  /// 合约类型切换回调
  final void Function(ContractType contractType)? onContractTypeChanged;

  const FuturesTabPage({
    super.key,
    this.initialContractType = ContractType.usdtMargin,
    this.onContractTypeChanged,
  });

  @override
  State<FuturesTabPage> createState() => _FuturesTabPageState();
}

class _FuturesTabPageState extends State<FuturesTabPage>
    with TickerProviderStateMixin {
  /// 子标签控制器
  late TabController _subTabController;

  /// 当前选中的合约类型
  late ContractType _currentContractType;

  /// 合约类型列表
  static const List<ContractType> _contractTypes = ContractType.values;

  @override
  void initState() {
    super.initState();
    _currentContractType = widget.initialContractType;

    // 初始化子标签控制器
    _subTabController = TabController(
      length: _contractTypes.length,
      vsync: this,
      initialIndex: _contractTypes.indexOf(_currentContractType),
    );

    // 监听标签切换
    _subTabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    _subTabController.removeListener(_onTabChanged);
    _subTabController.dispose();
    super.dispose();
  }

  /// 处理标签切换
  void _onTabChanged() {
    if (!_subTabController.indexIsChanging) {
      final newContractType = _contractTypes[_subTabController.index];
      if (newContractType != _currentContractType) {
        setState(() {
          _currentContractType = newContractType;
        });
        widget.onContractTypeChanged?.call(newContractType);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 子标签
        _buildSubTabbar(),

        // 子标签内容
        Expanded(child: _buildSubTabview()),
      ],
    );
  }

  // 构建子标签
  Widget _buildSubTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing10,
      ),
      child: TabbarWidget(
        height: 24,
        controller: _subTabController,
        tabs:
            _contractTypes
                .map((type) => TabItem(title: type.displayName))
                .toList(),
        labelStyle: context.templateStyle.text.tabTextSmall,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: true,
        indicatorSize: TabBarIndicatorSize.label,
        indicatorStyle: TabBarIndicatorStyle.filled,
        indicatorColor: context.templateColors.inputBackground,
        tabPadding: EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
        labelPadding: EdgeInsets.only(right: UiConstants.spacing10),
      ),
    );
  }

  /// 构建子标签内容
  Widget _buildSubTabview() {
    return TabBarView(
      controller: _subTabController,
      physics: const NeverScrollableScrollPhysics(),
      children:
          _contractTypes.map((contractType) {
            return _buildContractPage(contractType);
          }).toList(),
    );
  }

  /// 根据合约类型构建对应的页面
  Widget _buildContractPage(ContractType contractType) {
    switch (contractType) {
      case ContractType.usdtMargin:
        return const UsdtMarginContractPage();
      case ContractType.coinMargin:
        return const CoinMarginContractPage();
      case ContractType.usdcMargin:
        return const UsdcMarginContractPage();
    }
  }
}
