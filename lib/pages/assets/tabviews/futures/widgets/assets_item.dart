/*
*  合约资产项组件
*
*  支持外部数据传入和点击事件
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

/// 合约资产数据模型
class ContractAssetData {
  final String symbol; // 币种符号
  final String name; // 币种名称
  final String iconName; // 图标名称
  final String balance; // 余额
  final String balanceValue; // 余额价值
  final String quoteCurrency; // 计价货币

  const ContractAssetData({
    required this.symbol,
    required this.name,
    required this.iconName,
    required this.balance,
    required this.balanceValue,
    this.quoteCurrency = 'CNY',
  });

  /// 格式化的余额价值显示
  String get formattedBalanceValue => '$balanceValue $quoteCurrency';

  /// 创建默认数据
  static ContractAssetData defaultData() {
    return const ContractAssetData(
      symbol: 'USDT',
      name: 'USDT 永续',
      iconName: 'usdt',
      balance: '0.00000000',
      balanceValue: '0.00',
    );
  }
}

class AssetsItem extends StatefulWidget {
  /// 资产数据
  final ContractAssetData? data;

  /// 点击回调
  final VoidCallback? onTap;

  /// 外边距
  final EdgeInsets? margin;

  const AssetsItem({super.key, this.data, this.onTap, this.margin});

  @override
  State<AssetsItem> createState() => _AssetsItemState();
}

class _AssetsItemState extends State<AssetsItem> {
  /// 获取当前数据
  ContractAssetData get _currentData =>
      widget.data ?? ContractAssetData.defaultData();

  @override
  Widget build(BuildContext context) {
    Widget content = Container(
      margin: widget.margin,
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing14,
      ),
      child: Row(
        children: [
          // 货币图标
          ThemedImage(
            name: _currentData.iconName,
            size: 24,
            borderRadius: BorderRadius.circular(UiConstants.borderRadiusCircle),
            folder: ThemedAssetFolder.crypto,
          ),
          SizedBox(width: UiConstants.spacing12),

          // 货币信息
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 左侧：币种信息
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _currentData.symbol,
                      style: context.templateStyle.text.bodyLargeMedium,
                    ),
                    Text(
                      _currentData.name,
                      style: context.templateStyle.text.descriptionSmall,
                    ),
                  ],
                ),

                // 右侧：余额信息
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      _currentData.balance,
                      style: context.templateStyle.text.bodyLargeMedium,
                    ),
                    Text(
                      _currentData.formattedBalanceValue,
                      style: context.templateStyle.text.descriptionSmall,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );

    // 如果有点击回调，包装为可点击组件
    return widget.onTap != null
        ? GestureDetector(
          onTap: widget.onTap,
          behavior: HitTestBehavior.opaque,
          child: content,
        )
        : content;
  }
}
