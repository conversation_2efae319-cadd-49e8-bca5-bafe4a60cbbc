/*
*  USDT-M 合约标签页
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';
import 'package:qubic_exchange/services/assets/assets_service.dart';
import 'package:qubic_exchange/services/assets/models/asset_balance_model.dart';
import 'package:qubic_exchange/services/market/market_service.dart';
import 'package:qubic_exchange/utils/formatting/number_format_util.dart';
import '../../../widgets/index.dart';
import './../widgets/assets_item.dart';

class UsdtMarginContractPage extends StatefulWidget {
  const UsdtMarginContractPage({super.key});

  @override
  State<UsdtMarginContractPage> createState() => _UsdtMarginContractPageState();
}

class _UsdtMarginContractPageState extends State<UsdtMarginContractPage>
    with TickerProviderStateMixin {
  // 标签控制器
  late TabController _tabController;
  late TabController _subTabController;

  // 标签键
  final List<TabItem> _subTabKeys = [
    TabItem(title: 'USDT-M'),
    TabItem(title: '币本位'),
    TabItem(title: 'USDC-M'),
  ];

  final List<TabItem> _tabKeys = [TabItem(title: '资产')];

  // 初始化
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabKeys.length, vsync: this);
    _subTabController = TabController(length: _subTabKeys.length, vsync: this);
  }

  // 销毁
  @override
  void dispose() {
    _tabController.dispose();
    _subTabController.dispose();
    super.dispose();
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    // 模拟刷新操作
    await Future.delayed(const Duration(seconds: 2));
    // 这里可以添加实际的数据刷新逻辑
  }

  // 处理充值点击
  void _handleDeposit() {
    // TODO: 导航到充值页面
  }

  // 处理划转点击
  void _handleTransfer() {
    // TODO: 导航到划转页面
  }

  // 处理盈亏点击
  void _handlePnL() {
    // TODO: 导航到盈亏页面
  }

  // 处理资产项点击
  void _handleAssetTap(ContractAssetData assetData) {
    // TODO: 导航到资产详情页面
    debugPrint('点击了资产: ${assetData.symbol}');
  }

  /// 计算合约账户总USDT价值
  double _calculateTotalUsdtValue() {
    final assetsService = AssetsService.instance;
    final futuresAccount = assetsService.getAccountBalance(AccountType.futures);

    if (futuresAccount == null) return 0.0;

    double totalUsdt = 0.0;

    for (final currency in futuresAccount.currencies) {
      if (currency.totalAmount <= 0) continue;

      final symbol = currency.symbol;

      if (symbol == 'USDT') {
        totalUsdt += currency.totalAmount;
      } else {
        final currencyModel = MarketService.getCurrencyModel(currency.currencyId);

        if (currencyModel != null) {
          dynamic ticker;
          for (final key in ['1', '5', '0']) {
            ticker = currencyModel.tickers[key];
            if (ticker != null && ticker.lastPrice > 0) {
              break;
            }
          }

          if (ticker != null && ticker.lastPrice > 0) {
            final usdtValue = currency.totalAmount * ticker.lastPrice;
            totalUsdt += usdtValue;
          }
        }
      }
    }

    return totalUsdt;
  }

  @override
  Widget build(BuildContext context) {
    final maxOffset = 44.0;
    return EasyRefresh.builder(
      header: ClassicHeader(
        clamping: true,
        position: IndicatorPosition.locator,
        triggerOffset: maxOffset,
        processedDuration: const Duration(seconds: 1),
        safeArea: false,
        showMessage: false,
        showText: false,
        maxOverOffset: maxOffset,
      ),
      onRefresh: _onRefresh,
      childBuilder: (context, physics) {
        return ScrollConfiguration(
          behavior: const ERScrollBehavior(),
          child: ExtendedNestedScrollView(
            physics: physics,
            onlyOneScrollInBody: true,
            pinnedHeaderSliverHeightBuilder: () {
              return 60.0;
            },
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return <Widget>[
                // 刷新指示器定位器
                const HeaderLocator.sliver(clearExtent: false),

                // 顶部区域
                SliverToBoxAdapter(
                  child: Consumer<AuthProvider>(
                    builder: (context, authProvider, child) {
                      final totalUsdt = _calculateTotalUsdtValue();
                      final fiatValue = totalUsdt * authProvider.currentRate;
                      final fiatSymbol = authProvider.selectedCurrency?.symbol ?? 'USD';

                      // 计算总价值（资产累积 + 未实现盈亏）
                      final unrealizedPnL = 0.0; // 未实现盈亏，暂时为0
                      final totalValue = totalUsdt + unrealizedPnL;
                      final totalFiatValue = totalValue * authProvider.currentRate;

                      return Column(
                        children: [
                          HeaderSection(
                            titleText: '总资产',
                            showDailyPnL: true,
                            menuItems: [
                              HeaderMenuItem(
                                iconName: 'deposit',
                                title: '去交易',
                                onTap: () => _handleDeposit(),
                              ),
                              HeaderMenuItem(
                                iconName: 'transfer',
                                title: '划转',
                                onTap: () => _handleTransfer(),
                              ),
                              HeaderMenuItem(
                                iconName: 'pnl',
                                title: '盈亏',
                                onTap: () => _handlePnL(),
                              ),
                            ],
                            balanceValue: NumberFormatUtil.formatWithComma(totalValue, decimalDigits: 8),
                            balanceUnit: 'USDT',
                            equivalentValue: '≈ ${NumberFormatUtil.formatWithComma(totalFiatValue, decimalDigits: 2)} $fiatSymbol',
                            insertWidget: WalletProfitOverview(
                              totalProfitData: WalletProfitData(
                                title: '钱包余额',
                                value: NumberFormatUtil.formatWithComma(totalUsdt, decimalDigits: 8),
                                unit: 'USDT',
                                equivalentValue: NumberFormatUtil.formatWithComma(fiatValue, decimalDigits: 2),
                                equivalentUnit: fiatSymbol,
                                showDashed: true,
                              ),
                              availableBalanceData: WalletProfitData(
                                title: '未实现盈亏',
                                value: NumberFormatUtil.formatWithComma(unrealizedPnL, decimalDigits: 2),
                                unit: 'USDT',
                                equivalentValue: NumberFormatUtil.formatWithComma(unrealizedPnL * authProvider.currentRate, decimalDigits: 2),
                                equivalentUnit: fiatSymbol,
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),

                // 标签栏
                SliverPersistentHeader(
                  pinned: true,
                  delegate: StickyDelegate(
                    maxHeight: 60,
                    child: _buildTabbar(),
                    backgroundColor: context.templateColors.surface,
                  ),
                ),
              ];
            },
            body: _buildTabview(physics),
          ),
        );
      },
    );
  }

  // 构建标签栏
  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing14,
      ),
      child: Row(
        children: [
          Expanded(
            child: TabbarWidget(
              height: 50,
              controller: _tabController,
              tabs: _tabKeys,
              labelStyle: context.templateStyle.text.tabText,
              selectedColor: context.templateColors.textPrimary,
              unselectedColor: context.templateColors.textSecondary,
              showIndicator: false,
              labelPadding: EdgeInsets.only(right: UiConstants.spacing18),
            ),
          ),
          ThemedImage(
            name: 'icon_search',
            width: 20,
            height: 20,
            margin: EdgeInsets.only(right: UiConstants.spacing18),
          ),
          ThemedImage(name: '', width: 24, height: 24, followTheme: true),
        ],
      ),
    );
  }

  // 构建标签内容
  Widget _buildTabview(ScrollPhysics physics) {
    return TabBarView(
      controller: _tabController,
      physics: NeverScrollableScrollPhysics(),
      children: [
        // 资产列表
        _buildAssetsList(physics),
      ],
    );
  }

  // 构建资产列表
  Widget _buildAssetsList(ScrollPhysics physics) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final assetsService = AssetsService.instance;
        final futuresAccount = assetsService.getAccountBalance(AccountType.futures);

        if (futuresAccount == null || futuresAccount.currencies.isEmpty) {
          return Center(
            child: Padding(
              padding: EdgeInsets.all(UiConstants.spacing32),
              child: Text(
                '暂无合约账户资产',
                style: context.templateStyle.text.bodyText.copyWith(
                  color: context.templateColors.textTertiary,
                ),
              ),
            ),
          );
        }

        // 过滤有余额的币种
        final currenciesWithBalance = futuresAccount.currenciesWithBalance;

        if (currenciesWithBalance.isEmpty) {
          return Center(
            child: Padding(
              padding: EdgeInsets.all(UiConstants.spacing32),
              child: Text(
                '暂无合约资产',
                style: context.templateStyle.text.bodyText.copyWith(
                  color: context.templateColors.textTertiary,
                ),
              ),
            ),
          );
        }

        return ListView.builder(
          physics: physics,
          itemCount: currenciesWithBalance.length,
          itemBuilder: (context, index) {
            final currency = currenciesWithBalance[index];

            // 计算USDT价值
            double usdtValue = 0.0;
            final symbol = currency.symbol;

            if (symbol == 'USDT') {
              usdtValue = currency.totalAmount;
            } else {
              final currencyModel = MarketService.getCurrencyModel(currency.currencyId);
              if (currencyModel != null) {
                dynamic ticker;
                for (final key in ['1', '5', '0']) {
                  ticker = currencyModel.tickers[key];
                  if (ticker != null && ticker.lastPrice > 0) {
                    break;
                  }
                }
                if (ticker != null && ticker.lastPrice > 0) {
                  usdtValue = currency.totalAmount * ticker.lastPrice;
                }
              }
            }

            // 计算法币价值
            final fiatValue = usdtValue * authProvider.currentRate;
            final fiatSymbol = authProvider.selectedCurrency?.symbol ?? 'USD';

            // 构建ContractAssetData
            final assetData = ContractAssetData(
              symbol: currency.symbol,
              name: '${currency.symbol} 永续',
              iconName: currency.symbol.toLowerCase(),
              balance: NumberFormatUtil.formatWithComma(currency.totalAmount, decimalDigits: 8),
              balanceValue: NumberFormatUtil.formatWithComma(fiatValue, decimalDigits: 2),
              quoteCurrency: fiatSymbol,
            );

            return AssetsItem(
              data: assetData,
              onTap: () => _handleAssetTap(assetData),
            );
          },
        );
      },
    );
  }
}
