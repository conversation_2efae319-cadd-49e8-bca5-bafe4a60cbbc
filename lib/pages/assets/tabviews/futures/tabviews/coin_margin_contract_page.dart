/*
*  币本位合约标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import '../../../widgets/index.dart';
import './../widgets/assets_item.dart';

class CoinMarginContractPage extends StatefulWidget {
  const CoinMarginContractPage({super.key});

  @override
  State<CoinMarginContractPage> createState() => _CoinMarginContractPageState();
}

class _CoinMarginContractPageState extends State<CoinMarginContractPage>
    with TickerProviderStateMixin {
  // 标签控制器
  late TabController _tabController;
  late TabController _subTabController;

  // 标签键
  final List<TabItem> _subTabKeys = [
    TabItem(title: 'USDT-M'),
    TabItem(title: '币本位'),
    TabItem(title: 'USDC-M'),
  ];

  final List<TabItem> _tabKeys = [TabItem(title: '资产')];

  // 初始化
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabKeys.length, vsync: this);
    _subTabController = TabController(length: _subTabKeys.length, vsync: this);
  }

  // 销毁
  @override
  void dispose() {
    _tabController.dispose();
    _subTabController.dispose();
    super.dispose();
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    // 模拟刷新操作
    await Future.delayed(const Duration(seconds: 2));
    // 这里可以添加实际的数据刷新逻辑
  }

  // 处理充值点击
  void _handleDeposit() {
    // TODO: 导航到充值页面
  }

  // 处理划转点击
  void _handleTransfer() {
    // TODO: 导航到划转页面
  }

  // 处理盈亏点击
  void _handlePnL() {
    // TODO: 导航到盈亏页面
  }

  @override
  Widget build(BuildContext context) {
    final maxOffset = 44.0;
    return EasyRefresh.builder(
      header: ClassicHeader(
        clamping: true,
        position: IndicatorPosition.locator,
        triggerOffset: maxOffset,
        processedDuration: const Duration(seconds: 1),
        safeArea: false,
        showMessage: false,
        showText: false,
        maxOverOffset: maxOffset,
      ),
      onRefresh: _onRefresh,
      childBuilder: (context, physics) {
        return ScrollConfiguration(
          behavior: const ERScrollBehavior(),
          child: ExtendedNestedScrollView(
            physics: physics,
            onlyOneScrollInBody: true,
            pinnedHeaderSliverHeightBuilder: () {
              return 60.0;
            },
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return <Widget>[
                // 刷新指示器定位器
                const HeaderLocator.sliver(clearExtent: false),

                // 顶部区域
                SliverToBoxAdapter(
                  child: Column(
                    children: [
                      HeaderSection(
                        titleText: '总资产',
                        menuItems: [
                          HeaderMenuItem(
                            iconName: 'deposit',
                            title: '去交易',
                            onTap: () => _handleDeposit(),
                          ),
                          HeaderMenuItem(
                            iconName: 'transfer',
                            title: '划转',
                            onTap: () => _handleTransfer(),
                          ),
                          HeaderMenuItem(
                            iconName: 'pnl',
                            title: '盈亏',
                            onTap: () => _handlePnL(),
                          ),
                        ],
                        insertWidget: WalletProfitOverview(
                          totalProfitData: WalletProfitData(
                            title: '钱包余额',
                            value: '1,250.50',
                            unit: 'USDT',
                            equivalentValue: '8,753.50',
                            equivalentUnit: 'CNY',
                            showDashed: true,
                          ),
                          availableBalanceData: WalletProfitData(
                            title: '未实现盈亏',
                            value: '5,680.25',
                            unit: 'USDT',
                            equivalentValue: '39,761.75',
                            equivalentUnit: 'CNY',
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // 标签栏
                SliverPersistentHeader(
                  pinned: true,
                  delegate: StickyDelegate(
                    maxHeight: 60,
                    child: _buildTabbar(),
                    backgroundColor: context.templateColors.surface,
                  ),
                ),
              ];
            },
            body: _buildTabview(physics),
          ),
        );
      },
    );
  }

  // 构建标签栏
  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing14,
      ),
      child: Row(
        children: [
          Expanded(
            child: TabbarWidget(
              height: 50,
              controller: _tabController,
              tabs: _tabKeys,
              labelStyle: context.templateStyle.text.tabText,
              selectedColor: context.templateColors.textPrimary,
              unselectedColor: context.templateColors.textSecondary,
              showIndicator: false,
              labelPadding: EdgeInsets.only(right: UiConstants.spacing18),
            ),
          ),
          ThemedImage(
            name: 'icon_search',
            width: 20,
            height: 20,
            margin: EdgeInsets.only(right: UiConstants.spacing18),
          ),
          ThemedImage(name: '', width: 24, height: 24, followTheme: true),
        ],
      ),
    );
  }

  // 构建标签内容
  Widget _buildTabview(ScrollPhysics physics) {
    return TabBarView(
      controller: _tabController,
      physics: NeverScrollableScrollPhysics(),
      children: [
        // 资产
        ListView.builder(
          physics: physics,
          itemCount: 10,
          itemBuilder: (context, index) {
            return AssetsItem();
          },
        ),
      ],
    );
  }
}
