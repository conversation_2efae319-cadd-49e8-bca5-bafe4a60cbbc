/*
*  资产总览界面
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';
import 'package:qubic_exchange/services/assets/assets_service.dart';
import 'tabviews/has_assets.dart';
import 'tabviews/empty_asset.dart';

class OverviewPage extends StatefulWidget {
  const OverviewPage({super.key});

  @override
  State<OverviewPage> createState() => _OverviewPageState();
}

class _OverviewPageState extends State<OverviewPage> {
  // 初始化
  @override
  void initState() {
    super.initState();
  }

  // 销毁
  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // 判断是否有资产
        final hasAssets = _hasAssets(authProvider);

        // 根据是否有资产显示不同界面
        return hasAssets ? HasAssetsPage() : EmptyAsset();
      },
    );
  }

  /// 判断用户是否有资产
  bool _hasAssets(AuthProvider authProvider) {
    // 检查是否登录
    if (!authProvider.isLoggedIn) return false;

    // 检查账户数量是否为0
    final assetsService = AssetsService.instance;
    final accounts = assetsService.currentAssets?.accounts ?? [];
    return accounts.isNotEmpty;
  }
}
