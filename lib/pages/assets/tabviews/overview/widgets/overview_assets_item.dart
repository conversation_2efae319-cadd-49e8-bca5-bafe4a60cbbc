/*
*  资产项组件
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

/// 账户余额数据模型
class AccountBalance {
  final String accountType;
  final String balance;
  final bool hasBalance;

  const AccountBalance({
    required this.accountType,
    required this.balance,
    required this.hasBalance,
  });

  /// 创建有余额的账户
  factory AccountBalance.withBalance(String accountType, String balance) {
    return AccountBalance(
      accountType: accountType,
      balance: balance,
      hasBalance: balance != '0' && balance.isNotEmpty,
    );
  }

  /// 创建无余额的账户
  factory AccountBalance.empty(String accountType) {
    return AccountBalance(
      accountType: accountType,
      balance: '0',
      hasBalance: false,
    );
  }
}

class OverviewAssetsItem extends StatefulWidget {
  final String? coinSymbol;
  final String? coinName;
  final String? totalBalance;
  final String? equivalentValue;
  final String? equivalentUnit;
  final List<AccountBalance>? accountBalances;
  final VoidCallback? onTap;

  const OverviewAssetsItem({
    super.key,
    this.coinSymbol,
    this.coinName,
    this.totalBalance,
    this.equivalentValue,
    this.equivalentUnit,
    this.accountBalances,
    this.onTap,
  });

  @override
  State<OverviewAssetsItem> createState() => _OverviewAssetsItemState();
}

class _OverviewAssetsItemState extends State<OverviewAssetsItem> {
  bool _isExpanded = false;

  /// 获取有余额的账户列表
  List<AccountBalance> get _accountsWithBalance {
    return widget.accountBalances
            ?.where((account) => account.hasBalance)
            .toList() ??
        [];
  }

  /// 是否有账户详情可显示
  bool get _hasAccountDetails => _accountsWithBalance.isNotEmpty;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(color: context.templateColors.surface),
      child: Column(
        children: [
          _buildMainContent(),
          if (_isExpanded && _hasAccountDetails) _buildAccountDetails(),
        ],
      ),
    );
  }

  /// 构建主要内容
  Widget _buildMainContent() {
    return InkWellWidget(
      onTap: () {
        if (_hasAccountDetails) {
          setState(() {
            _isExpanded = !_isExpanded;
          });
        } else {
          widget.onTap?.call();
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing18,
          vertical: UiConstants.spacing16,
        ),
        child: Row(
          children: [
            // 币种图标
            ThemedImage.crypto(
              widget.coinSymbol?.toLowerCase() ?? 'usdt',
              size: 26,
              borderRadius: BorderRadius.circular(
                UiConstants.borderRadiusCircle,
              ),
            ),

            SizedBox(width: UiConstants.spacing12),

            // 币种信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.coinSymbol ?? 'USDT',
                    style: context.templateStyle.text.bodyLargeMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 2),
                  Text(
                    widget.coinName ?? 'Tether',
                    style: context.templateStyle.text.bodySmall.copyWith(
                      color: context.templateColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),

            // 余额信息
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  widget.totalBalance ?? '0.00',
                  style: context.templateStyle.text.bodyLargeMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  '≈ ${widget.equivalentValue ?? '0.00'} ${widget.equivalentUnit ?? 'CNY'}',
                  style: context.templateStyle.text.bodySmall.copyWith(
                    color: context.templateColors.textSecondary,
                  ),
                ),
              ],
            ),

            // 展开按钮（只在有账户详情时显示）
            if (_hasAccountDetails) ...[
              Container(
                padding: EdgeInsets.only(left: UiConstants.spacing10),
                child: AnimatedRotation(
                  turns: _isExpanded ? 0.5 : 0,
                  duration: const Duration(milliseconds: 200),
                  child: ThemedImage(
                    name: 'arrow_triangle_down_gray',
                    size: UiConstants.iconSize12,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建账户详情列表
  Widget _buildAccountDetails() {
    final accounts = _accountsWithBalance;

    return Container(
      padding: EdgeInsets.only(
        left: UiConstants.spacing56,
        right: UiConstants.spacing18,
      ),
      child: Column(
        children:
            accounts.asMap().entries.map((entry) {
              final index = entry.key;
              final account = entry.value;
              final isLast = index == accounts.length - 1;

              return _buildAccountItem(
                accountType: account.accountType,
                balance: account.balance,
                showBottomBorder: !isLast,
              );
            }).toList(),
      ),
    );
  }

  /// 构建单个账户信息项
  Widget _buildAccountItem({
    required String accountType,
    required String balance,
    required bool showBottomBorder,
    VoidCallback? onTap,
  }) {
    return InkWellWidget(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: UiConstants.spacing10),
        decoration: BoxDecoration(
          border:
              showBottomBorder
                  ? Border(
                    bottom: BorderSide(
                      width: 0.5,
                      color: context.templateColors.divider,
                    ),
                  )
                  : null,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              accountType,
              style: context.templateStyle.text.bodySmallMedium,
            ),
            Text(balance, style: context.templateStyle.text.bodySmallMedium),
          ],
        ),
      ),
    );
  }
}
