/*
* 账户项
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';

/// 账户数据模型
class AccountItemData {
  final String accountType;
  final String balance;
  final String coinSymbol;
  final String equivalentValue;
  final String equivalentUnit;
  final Color indicatorColor;

  const AccountItemData({
    required this.accountType,
    required this.balance,
    required this.coinSymbol,
    required this.equivalentValue,
    this.equivalentUnit = 'USD',
    required this.indicatorColor,
  });
}

class OverviewAccountItem extends StatefulWidget {
  final AccountItemData? accountData;
  final VoidCallback? onTap;

  const OverviewAccountItem({super.key, this.accountData, this.onTap});

  @override
  State<OverviewAccountItem> createState() => _OverviewAccountItemState();
}

class _OverviewAccountItemState extends State<OverviewAccountItem> {
  /// 获取默认数据
  AccountItemData get _defaultData => const AccountItemData(
    accountType: '现货',
    balance: '0.000129',
    coinSymbol: 'BTC',
    equivalentValue: '0.00',
    indicatorColor: Colors.blue,
  );

  @override
  Widget build(BuildContext context) {
    final data = widget.accountData ?? _defaultData;

    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing18,
          vertical: UiConstants.spacing12,
        ),
        child: Row(
          children: [
            Container(
              width: 10,
              height: 10,
              decoration: BoxDecoration(color: data.indicatorColor),
            ),
            SizedBox(width: UiConstants.spacing12),
            Text(
              data.accountType,
              style: context.templateStyle.text.bodyLargeMedium,
            ),
            Spacer(),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${data.balance} ${data.coinSymbol}',
                  style: context.templateStyle.text.bodyTextMedium,
                ),
                Text(
                  '≈ ${data.equivalentValue} ${data.equivalentUnit}',
                  style: context.templateStyle.text.bodySmall,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
