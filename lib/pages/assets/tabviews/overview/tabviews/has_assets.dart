/*
*  有资产
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/common/dynamic_height_tab_view.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:qubic_exchange/models/pages/assets/common/menu_item_data.dart';
import 'package:qubic_exchange/services/assets/index.dart' as assets;
import 'package:qubic_exchange/services/market/market_service.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';
import 'package:qubic_exchange/utils/index.dart';
import '../../../widgets/index.dart';
import '../widgets/overview_assets_item.dart';
import '../widgets/overview_account_item.dart';

class HasAssetsPage extends StatefulWidget {
  const HasAssetsPage({super.key});

  @override
  State<HasAssetsPage> createState() => _HasAssetsPageState();
}

class _HasAssetsPageState extends State<HasAssetsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  static const List<TabItem> _tabKeys = [
    TabItem(title: '资产'),
    TabItem(title: '账户'),
  ];

  /// 菜单项列表
  late final List<MenuItemData> _menuItems;

  /// AuthProvider引用，避免在dispose中访问context
  AuthProvider? _authProvider;

  /// 计算总资产
  TotalAssetsData _calculateTotalAssets(AuthProvider authProvider) {
    try {
      final assetsResponse = assets.AssetsService.instance.currentAssets;
      if (assetsResponse == null) {
        return TotalAssetsData(
          usdtValue: '0.00',
          fiatValue: '0.00',
          fiatUnit: _getFiatSymbol(authProvider)
        );
      }

      double totalUsdtValue = 0.0;

      for (final account in assetsResponse.accounts) {
        for (final currency in account.currencies) {
          if (!currency.hasBalance) continue;

          // 币种数量 × ticker价格 = USDT价值
          final tickerPrice = _getCurrencyUsdtPriceById(currency.currencyId, currency.symbol);
          final usdtValue = currency.totalAmount * tickerPrice;
          totalUsdtValue += usdtValue;


        }
      }

      final fiatValue = _convertUsdtToFiat(totalUsdtValue, authProvider);

      return TotalAssetsData(
        usdtValue: NumberFormatUtil.formatWithComma(totalUsdtValue),
        fiatValue: NumberFormatUtil.formatWithComma(fiatValue),
        fiatUnit: _getFiatSymbol(authProvider),
      );
    } catch (e) {
      debugPrint('❌ 计算总资产失败: $e');
      return TotalAssetsData(
        usdtValue: '0.00',
        fiatValue: '0.00',
        fiatUnit: _getFiatSymbol(authProvider)
      );
    }
  }

  List<SecurityGuardItem> get _securityGuardItems => [
    SecurityGuardItem(
      title: '7.651亿美元的保护基金，您的资产安全是我们的立身之本',
      imagePath: 'security_fund',
      onTap: _handleSecurityFundTap,
    ),
    SecurityGuardItem(
      title: '银行级安全防护，多重加密保障您的数字资产',
      imagePath: 'security_protection',
      onTap: _handleSecurityProtectionTap,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabKeys.length, vsync: this);

    // 初始化菜单项列表
    _menuItems = [
      // 充值
      MenuItemData.deposit(() {
        // TODO: 导航到充值页面
      }),
      // 提现
      MenuItemData.withdraw(() {
        // TODO: 导航到提现页面
      }),
      // 划转
      MenuItemData.transfer(() {
        // TODO: 导航到划转页面
      }),
      // 盈亏
      MenuItemData.pnl(() {
        // TODO: 导航到盈亏页面
      }),
    ];

    // 监听AuthProvider变化，当法币切换时刷新界面
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _authProvider = Provider.of<AuthProvider>(context, listen: false);
      _authProvider?.addListener(_onAuthProviderChanged);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    // 移除监听器，使用保存的引用避免访问已销毁的context
    _authProvider?.removeListener(_onAuthProviderChanged);
    super.dispose();
  }

  /// AuthProvider变化回调
  void _onAuthProviderChanged() {
    if (mounted) {
      setState(() {
        // 触发界面重新构建，重新计算法币价值
      });
    }
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    try {
      // 刷新资产数据
      await assets.AssetsService.instance.refreshAssets();
      debugPrint('✅ 市场和资产数据刷新完成');
    } catch (e) {
      debugPrint('❌ 数据刷新失败: $e');
    }
  }

  /// 构建菜单项列表
  List<HeaderMenuItem> _buildMenuItems() {
    return _menuItems.map((menuData) {
      return HeaderMenuItem(
        iconName: menuData.iconName,
        title: menuData.title,
        onTap: menuData.onTap,
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final maxOffset = 44.0;
    return EasyRefresh.builder(
      header: ClassicHeader(
        clamping: true,
        position: IndicatorPosition.locator,
        triggerOffset: maxOffset,
        processedDuration: const Duration(seconds: 1),
        safeArea: false,
        showMessage: false,
        showText: false,
        maxOverOffset: maxOffset,
      ),
      onRefresh: _onRefresh,
      childBuilder: (context, physics) {
        return ScrollConfiguration(
          behavior: const ERScrollBehavior(),
          child: ExtendedNestedScrollView(
            physics: physics,
            onlyOneScrollInBody: true,
            pinnedHeaderSliverHeightBuilder: () {
              return 60.0;
            },
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return <Widget>[
                // 刷新指示器定位器
                const HeaderLocator.sliver(clearExtent: false),

                // 顶部区域
                SliverToBoxAdapter(
                  child: Column(
                    children: [
                      Consumer<AuthProvider>(
                        builder: (context, authProvider, child) {
                          final totalAssets = _calculateTotalAssets(authProvider);
                          return HeaderSection(
                            titleText: '总资产估值',
                            showDailyPnL: false,
                            menuItems: _buildMenuItems(),
                            totalAssetsData: totalAssets,
                          );
                        },
                      ),
                    ],
                  ),
                ),

                // 标签栏
                SliverPersistentHeader(
                  pinned: true,
                  delegate: StickyDelegate(
                    maxHeight: 60,
                    child: _buildTabbar(),
                    backgroundColor: context.templateColors.surface,
                  ),
                ),
              ];
            },
            body: _buildTabview(physics),
          ),
        );
      },
    );
  }

  // 构建标签栏
  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing14,
      ),
      child: Row(
        children: [
          Expanded(
            child: TabbarWidget(
              height: 50,
              controller: _tabController,
              tabs: _tabKeys,
              labelStyle: context.templateStyle.text.tabText,
              selectedColor: context.templateColors.textPrimary,
              unselectedColor: context.templateColors.textSecondary,
              showIndicator: true,
              indicatorSize: TabBarIndicatorSize.label,
              labelPadding: EdgeInsets.only(right: UiConstants.spacing18),
            ),
          ),
          SizedBox(width: UiConstants.spacing18),
          ThemedImage(
            name: 'icon_search',
            width: 20,
            height: 20,
            margin: EdgeInsets.only(right: UiConstants.spacing18),
          ),
          ThemedImage(name: '', width: 24, height: 24, followTheme: true),
        ],
      ),
    );
  }

  // 构建标签内容 - 使用动态高度 TabView
  Widget _buildTabview(ScrollPhysics physics) {
    return DynamicHeightTabView(
      controller: _tabController,
      children: [
        // 资产
        _buildAssetsList(physics),

        // 账户
        _buildAccountList(physics),
      ],
    );
  }

  // 构建资产列表
  Widget _buildAssetsList(physics) {
    return StreamBuilder<assets.AssetBalanceResponse?>(
      stream: assets.AssetsService.instance.assetsStream,
      builder: (context, snapshot) {
        final authProvider = context.read<AuthProvider>();
        final assetsData = _buildAssetsData(authProvider);

        return CustomScrollView(
          physics: physics,
          slivers: [
            // 资产列表
            if (assetsData.isEmpty)
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.all(UiConstants.spacing24),
                  child: Center(
                    child: Text(
                      '暂无资产数据',
                      style: context.templateStyle.text.bodyTextMedium.copyWith(
                        color: context.templateColors.textSecondary,
                      ),
                    ),
                  ),
                ),
              )
            else
              SliverList(
                delegate: SliverChildBuilderDelegate((context, index) {
                  final assetData = assetsData[index];

                  // 构建账户余额列表
                  final accountBalances =
                      (assetData['accounts'] as List<Map<String, dynamic>>)
                          .map(
                            (accountData) => AccountBalance.withBalance(
                              accountData['accountType'] as String,
                              accountData['balance'] as String,
                            ),
                          )
                          .toList();

                  return OverviewAssetsItem(
                    coinSymbol: assetData['coinSymbol'] as String,
                    coinName: assetData['coinName'] as String,
                    totalBalance: assetData['totalBalance'] as String,
                    equivalentValue: assetData['equivalentValue'] as String,
                    equivalentUnit: assetData['equivalentUnit'] as String,
                    accountBalances: accountBalances,
                    onTap: () {
                      // 处理资产项点击
                      // TODO: 导航到具体资产详情页面
                    },
                  );
                }, childCount: assetsData.length),
              ),

            // 底部安全守护组件
            SliverToBoxAdapter(child: _buildSecurityGuardSection()),
          ],
        );
      },
    );
  }

  // 构建账户列表
  Widget _buildAccountList(physics) {
    return StreamBuilder<assets.AssetBalanceResponse?>(
      stream: assets.AssetsService.instance.assetsStream,
      builder: (context, snapshot) {
        final authProvider = context.read<AuthProvider>();
        final accountItems = _buildAccountsData(authProvider);

        return CustomScrollView(
          physics: physics,
          slivers: [
            // 账户列表
            if (accountItems.isEmpty)
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.all(UiConstants.spacing24),
                  child: Center(
                    child: Text(
                      '暂无账户数据',
                      style: context.templateStyle.text.bodyTextMedium.copyWith(
                        color: context.templateColors.textSecondary,
                      ),
                    ),
                  ),
                ),
              )
            else
              SliverList(
                delegate: SliverChildBuilderDelegate((context, index) {
                  return OverviewAccountItem(
                    accountData: accountItems[index],
                    onTap: () {
                      // 处理账户项点击
                      // TODO: 导航到具体账户详情页面
                    },
                  );
                }, childCount: accountItems.length),
              ),

            // 底部安全守护组件
            SliverToBoxAdapter(child: _buildSecurityGuardSection()),
          ],
        );
      },
    );
  }

  /// 处理保护基金点击
  void _handleSecurityFundTap() {
    // TODO: 导航到保护基金页面
  }

  /// 处理安全防护点击
  void _handleSecurityProtectionTap() {
    // TODO: 导航到安全防护页面
  }

  /// 构建安全守护部分
  Widget _buildSecurityGuardSection() {
    return SecurityGuard(title: '您的资产安全，我们来守护', items: _securityGuardItems);
  }

  /// 根据currency_id获取币种的USDT价格
  double _getCurrencyUsdtPriceById(int currencyId, String symbol) {
    try {
      // 如果是USDT，价格为1
      if (symbol.toUpperCase() == 'USDT') {

        return 1.0;
      }

      // 从市场服务获取价格
      final marketService = MarketService.instance;

      // 检查MarketService是否已初始化
      if (!marketService.isInitialized) {
  
        return 1.0;
      }

      final currencies = marketService.currencyModels;



      // 根据currency_id查找对应的币种
      for (final currency in currencies) {
        if (currency.id == currencyId) {

          // 如果没有ticker数据，直接返回默认价格
          if (currency.tickers.isEmpty) {
            return 1.0;
          }

          // 获取ticker数据
          final ticker = currency.tickers['1']; // 现货市场类型为1
          if (ticker != null) {
            return ticker.lastPrice;
          } else {
            // 尝试其他ticker类型
            for (final entry in currency.tickers.entries) {
              return entry.value.lastPrice;
            }
          }
          break; // 找到币种后跳出循环
        }
      }


      return 1.0;
    } catch (e) {
      debugPrint('❌ 获取币种USDT价格失败: $symbol(ID:$currencyId), $e');
      return 1.0;
    }
  }



  /// 将USDT金额转换为用户选择的法币金额
  double _convertUsdtToFiat(double usdtAmount, AuthProvider authProvider) {
    try {
      final currentRate = authProvider.currentRate;
      final result = usdtAmount * currentRate;

      return result;
    } catch (e) {
      debugPrint('❌ USDT转法币失败: $e');
      return usdtAmount;
    }
  }

  /// 获取用户选择的法币符号
  String _getFiatSymbol(AuthProvider authProvider) {
    try {
      return authProvider.selectedCurrency?.symbol ?? 'USD';
    } catch (e) {
      debugPrint('❌ 获取法币符号失败: $e');
      return 'USD';
    }
  }

  /// 组装资产数据
  List<Map<String, dynamic>> _buildAssetsData(AuthProvider authProvider) {
    try {
      final assetsResponse = assets.AssetsService.instance.currentAssets;
      if (assetsResponse == null) {
        return [];
      }

      // 按币种分组统计
      final Map<String, Map<String, dynamic>> coinMap = {};

      // 遍历所有账户
      for (final account in assetsResponse.accounts) {
        // 遍历账户中的所有币种
        for (final currency in account.currencies) {
          if (!currency.hasBalance) continue; // 跳过无余额的币种

          final symbol = currency.symbol;

          // 初始化币种数据
          if (!coinMap.containsKey(symbol)) {
            coinMap[symbol] = {
              'coinSymbol': symbol,
              'coinName': _getCoinName(symbol),
              'currencyId': currency.currencyId,
              'totalBalance': 0.0,
              'accounts': <Map<String, dynamic>>[],
            };
          }

          // 累加总余额
          coinMap[symbol]!['totalBalance'] =
              (coinMap[symbol]!['totalBalance'] as double) + currency.totalAmount;

          // 添加账户余额信息
          (coinMap[symbol]!['accounts'] as List<Map<String, dynamic>>).add({
            'accountType': account.accountType.displayName,
            'balance': _formatBalance(currency.totalAmount),
          });
        }
      }

      // 转换为列表并计算等值
      final List<Map<String, dynamic>> result = [];
      final fiatSymbol = _getFiatSymbol(authProvider);

      for (final entry in coinMap.entries) {
        final symbol = entry.key;
        final data = entry.value;
        final totalBalance = data['totalBalance'] as double;

        // 第一步：币种数量 × ticker价格 = USDT价值
        final currencyId = data['currencyId'] as int;
        final tickerPrice = _getCurrencyUsdtPriceById(currencyId, symbol);
        final usdtValue = totalBalance * tickerPrice;

        // 第二步：USDT价值 × 汇率 = 法币价值
        final fiatValue = _convertUsdtToFiat(usdtValue, authProvider);

        result.add({
          'coinSymbol': symbol,
          'coinName': data['coinName'],
          'totalBalance': _formatBalance(totalBalance),
          'equivalentValue': NumberFormatUtil.formatWithComma(fiatValue),
          'equivalentUnit': fiatSymbol,
          'accounts': data['accounts'],
        });
      }

      // 按等值排序（从大到小）
      result.sort((a, b) {
        final aValue = double.tryParse(a['equivalentValue']) ?? 0.0;
        final bValue = double.tryParse(b['equivalentValue']) ?? 0.0;
        return bValue.compareTo(aValue);
      });

      return result;
    } catch (e) {
      debugPrint('❌ 组装资产数据失败: $e');
      return [];
    }
  }

  /// 获取币种名称
  String _getCoinName(String symbol) {
    // 这里可以添加币种名称映射
    final nameMap = {
      'BTC': 'Bitcoin',
      'ETH': 'Ethereum',
      'USDT': 'Tether',
      'BNB': 'BNB',
      'ADA': 'Cardano',
      'DOT': 'Polkadot',
      'XRP': 'XRP',
      'SOL': 'Solana',
    };

    return nameMap[symbol.toUpperCase()] ?? symbol;
  }

  /// 组装账户数据
  List<AccountItemData> _buildAccountsData(AuthProvider authProvider) {
    try {
      final assetsResponse = assets.AssetsService.instance.currentAssets;
      if (assetsResponse == null) {
        return [];
      }

      final List<AccountItemData> result = [];
      final colors = [
        const Color(0xFF00C896),
        const Color(0xFF8B5CF6),
        const Color(0xFF09B6D4),
        const Color(0xFFFF6B6B),
        const Color(0xFFFFA726),
        const Color(0xFF4CAF50),
        const Color(0xFF2196F3),
        const Color(0xFFE91E63),
      ];

      int colorIndex = 0;

      // 遍历所有账户
      for (final account in assetsResponse.accounts) {
        double totalUsdtValue = 0.0;

        // 计算账户总价值（累积所有币种的USDT价值）
        for (final currency in account.currencies) {
          if (!currency.hasBalance) continue;

          // 币种数量 × ticker价格 = USDT价值
          final tickerPrice = _getCurrencyUsdtPriceById(currency.currencyId, currency.symbol);
          final usdtValue = currency.totalAmount * tickerPrice;
          totalUsdtValue += usdtValue;
        }

        // 只显示有余额的账户
        if (totalUsdtValue > 0) {
          // USDT总价值 → 法币
          final fiatValue = _convertUsdtToFiat(totalUsdtValue, authProvider);
          final fiatSymbol = _getFiatSymbol(authProvider);

          result.add(AccountItemData(
            accountType: account.accountType.displayName,
            balance: NumberFormatUtil.formatWithComma(totalUsdtValue),
            coinSymbol: 'USDT',
            equivalentValue: NumberFormatUtil.formatWithComma(fiatValue),
            equivalentUnit: fiatSymbol,
            indicatorColor: colors[colorIndex % colors.length],
          ));
          colorIndex++;
        }
      }

      // 按等值排序（从大到小）
      result.sort((a, b) {
        final aValue = double.tryParse(a.equivalentValue) ?? 0.0;
        final bValue = double.tryParse(b.equivalentValue) ?? 0.0;
        return bValue.compareTo(aValue);
      });

      return result;
    } catch (e) {
      debugPrint('❌ 组装账户数据失败: $e');
      return [];
    }
  }

  /// 格式化余额显示
  /// 如果小数部分全为0，则只保留2位小数
  /// 如果小数部分不为0，则保留8位小数
  String _formatBalance(double balance) {
    // 检查是否为整数（小数部分为0）
    if (balance == balance.floorToDouble()) {
      // 如果是整数，显示2位小数
      return balance.toStringAsFixed(2);
    }

    // 如果有小数部分，格式化为8位小数并去掉末尾的0
    String formatted = balance.toStringAsFixed(8);

    // 去掉末尾的0，但至少保留2位小数
    String result = formatted.replaceAll(RegExp(r'0+$'), '');
    if (result.endsWith('.')) {
      result += '00';
    } else {
      // 确保至少有2位小数
      List<String> parts = result.split('.');
      if (parts.length > 1 && parts[1].length < 2) {
        result = balance.toStringAsFixed(2);
      }
    }

    return result;
  }
}
