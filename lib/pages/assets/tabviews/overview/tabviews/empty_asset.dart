/*
*  无资产
*/

import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/widgets/common/security_guard.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';

/// 福利活动数据模型
class WelfareActivity {
  final String title;
  final String description;
  final String imagePath;
  final VoidCallback? onTap;

  const WelfareActivity({
    required this.title,
    required this.description,
    required this.imagePath,
    this.onTap,
  });
}

class EmptyAsset extends StatefulWidget {
  const EmptyAsset({super.key});

  @override
  State<EmptyAsset> createState() => _EmptyAssetState();
}

class _EmptyAssetState extends State<EmptyAsset> with TickerProviderStateMixin {
  /// 页面配置常量
  static const double _maxRefreshOffset = 44.0;
  static const double _pinnedHeaderHeight = 53.0;
  static const double _emptyImageHeight = 240.0;
  static const Duration _refreshDuration = Duration(seconds: 2);

  /// 旋转动画控制器
  late AnimationController _rotationController;
  late Animation<double> _rotationAnimation;

  /// 获取福利活动数据
  WelfareActivity get _welfareActivity => WelfareActivity(
    title: '您的账户内无资产',
    description: '每天完成任意金额充值，即可获得10福利金',
    imagePath: 'welfare_activity',
    onTap: _handleWelfareActivityTap,
  );

  /// 获取安全守护项列表
  List<SecurityGuardItem> get _securityGuardItems => [
    SecurityGuardItem(
      title: '7.651亿美元的保护基金，您的资产安全是我们的立身之本',
      imagePath: 'security_fund',
      onTap: _handleSecurityFundTap,
    ),
    SecurityGuardItem(
      title: '银行级安全防护，多重加密保障您的数字资产',
      imagePath: 'security_protection',
      onTap: _handleSecurityProtectionTap,
    ),
  ];

  /// 下拉刷新
  Future<void> _onRefresh() async {
    // 模拟刷新操作
    await Future.delayed(_refreshDuration);
    // TODO: 添加实际的数据刷新逻辑
  }

  /// 处理充值按钮点击
  void _handleDepositTap() {
    // TODO: 导航到充值页面
  }

  /// 处理福利活动点击
  void _handleWelfareActivityTap() {
    // TODO: 导航到福利活动页面
  }

  /// 处理保护基金点击
  void _handleSecurityFundTap() {
    // TODO: 导航到保护基金页面
  }

  /// 处理安全防护点击
  void _handleSecurityProtectionTap() {
    // TODO: 导航到安全防护页面
  }

  @override
  void initState() {
    super.initState();

    // 初始化旋转动画
    _rotationController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    // 开始循环旋转
    _rotationController.repeat();
  }

  @override
  void dispose() {
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EasyRefresh.builder(
      header: ClassicHeader(
        clamping: true,
        position: IndicatorPosition.locator,
        triggerOffset: _maxRefreshOffset,
        processedDuration: const Duration(seconds: 1),
        safeArea: false,
        showMessage: false,
        showText: false,
        maxOverOffset: _maxRefreshOffset,
      ),
      onRefresh: _onRefresh,
      childBuilder: (context, physics) {
        return ScrollConfiguration(
          behavior: const ERScrollBehavior(),
          child: ExtendedNestedScrollView(
            physics: physics,
            onlyOneScrollInBody: true,
            pinnedHeaderSliverHeightBuilder: () => _pinnedHeaderHeight,
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return <Widget>[const HeaderLocator.sliver(clearExtent: false)];
            },
            body: SingleChildScrollView(
              physics: physics,
              child: Container(
                padding: EdgeInsets.symmetric(vertical: UiConstants.spacing18),
                child: Column(
                  children: [
                    _buildEmptyStateSection(),
                    _buildWelfareActivitySection(),
                    _buildSecurityGuardSection(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建空状态部分
  Widget _buildEmptyStateSection() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: Column(
        children: [
          AnimatedBuilder(
            animation: _rotationAnimation,
            builder: (context, child) {
              // 横向旋转+缩放组合动画
              final rotationAngle = _rotationAnimation.value * 2 * math.pi; // Y轴旋转（横向）
              final scale = 0.9 + 0.2 * (0.5 + 0.5 * math.sin(_rotationAnimation.value * 4 * math.pi)); // 缩放频率是旋转的2倍

              return Transform(
                alignment: Alignment.center,
                transform: Matrix4.identity()
                  ..scale(scale) // 先缩放
                  ..rotateY(rotationAngle), // Y轴旋转（从右到左的横向旋转）
                child: ThemedImage(
                  name: 'asset_top_no_asset',
                  width: double.infinity,
                  height: _emptyImageHeight,
                  format: ImageFormat.webp,
                  fit: BoxFit.fill,
                  followTheme: true,
                ),
              );
            },
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: UiConstants.spacing12),
            child: Text(
              '完成充值，即刻尊享卓越交易体验！',
              style: context.templateStyle.text.h2,
            ),
          ),
          CommonButton(
            width: double.infinity,
            height: 45,
            text: '充值',
            onPressed: _handleDepositTap,
          ),
        ],
      ),
    );
  }

  /// 构建福利活动部分
  Widget _buildWelfareActivitySection() {
    final activity = _welfareActivity;

    return Container(
      padding: EdgeInsets.only(
        bottom: UiConstants.spacing14,
        top: UiConstants.spacing24,
        left: UiConstants.spacing18,
        right: UiConstants.spacing18,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(width: 0.5, color: context.templateColors.divider),
        ),
      ),
      child: InkWellWidget(
        onTap: activity.onTap,
        child: Row(
          children: [
            ThemedImage(name: activity.imagePath, width: 70, height: 70),
            SizedBox(width: UiConstants.spacing12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    activity.title,
                    style: context.templateStyle.text.descriptionSmall,
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      top: UiConstants.spacing4,
                      bottom: UiConstants.spacing8,
                    ),
                    child: Text(
                      activity.description,
                      style: context.templateStyle.text.bodyTextMedium,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  ViewMoreButton(text: '立即查看', showArrow: true),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建安全守护部分
  Widget _buildSecurityGuardSection() {
    return SecurityGuard(title: '您的资产安全，我们来守护', items: _securityGuardItems);
  }
}
