/*
*  币种项组件
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class CurrencyItem extends StatefulWidget {
  final String? coinSymbol;
  final String? coinName;
  final String? balance;
  final String? equivalentValue;
  final String? equivalentUnit;
  final String? apr;
  final String? profitValue;
  final String? profitUnit;
  final VoidCallback? onTap;

  const CurrencyItem({
    super.key,
    this.coinSymbol,
    this.coinName,
    this.balance,
    this.equivalentValue,
    this.equivalentUnit,
    this.apr,
    this.profitValue,
    this.profitUnit,
    this.onTap,
  });

  @override
  State<CurrencyItem> createState() => _CurrencyItemState();
}

class _CurrencyItemState extends State<CurrencyItem> {
  // 是否展开
  bool _isExpanded = false;
  @override
  Widget build(BuildContext context) {
    return InkWellWidget(
      onTap: widget.onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18, vertical: UiConstants.spacing14),
        decoration: BoxDecoration(
          color: context.templateColors.cardBackground,
          borderRadius: BorderRadius.circular(UiConstants.borderRadius12),
        ),
        child: _buildCoinContainer(),
      ),
    );
  }

  /// 构建币种图标
  Widget _buildCoinIcon() {
    return ThemedImage.crypto(
      widget.coinSymbol?.toLowerCase() ?? 'usdt',
      size: 30,
      borderRadius: BorderRadius.circular(UiConstants.borderRadiusCircle),
      margin: EdgeInsets.only(top: UiConstants.spacing4),
    );
  }

  /// 构建币种头部信息
  Widget _buildCoinContainer() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 币种图标
        _buildCoinIcon(),

        SizedBox(width: UiConstants.spacing12),

        // 币种信息
        Expanded(
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(widget.coinSymbol ?? 'USDT', style: context.templateStyle.text.bodyTextMedium),
                      Text(widget.coinName ?? 'Tether', style: context.templateStyle.text.descriptionSmall),
                    ],
                  ),

                  // 右侧余额信息
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(widget.balance ?? '0.5252918', style: context.templateStyle.text.bodyTextMedium),
                      Text(
                        '≈${widget.equivalentValue ?? '3.77'} ${widget.equivalentUnit ?? 'CNY'}',
                        style: context.templateStyle.text.descriptionSmall.copyWith(color: context.templateColors.textSecondary),
                      ),
                    ],
                  ),
                ],
              ),
              if (_isExpanded)
                Container(
                  padding: EdgeInsets.symmetric(vertical: UiConstants.spacing14),
                  decoration: BoxDecoration(
                    border: Border(bottom: BorderSide(width: UiConstants.borderWidth0_5, color: context.templateColors.divider)),
                  ),
                  child: Column(children: [_buildInfoRows(), _buildActionButton()]),
                ),
            ],
          ),
        ),

        InkWellWidget(
          onTap:
              () => {
                setState(() {
                  _isExpanded = !_isExpanded;
                }),
              },
          child: Padding(
            padding: EdgeInsets.only(top: UiConstants.spacing10, left: UiConstants.spacing12),
            child: ThemedImage(name: _isExpanded ? 'arrow_triangle_up_gray' : 'arrow_triangle_down_gray', size: UiConstants.iconSize12),
          ),
        ),
      ],
    );
  }

  /// 构建信息行
  Widget _buildInfoRows() {
    return Column(
      children: [
        // 活期简单赚币标题
        Align(alignment: Alignment.centerLeft, child: Text('活期简单赚币', style: context.templateStyle.text.bodyTextMedium)),

        SizedBox(height: UiConstants.spacing12),

        // 数量行
        _buildInfoRow('数量', widget.balance ?? '0.5252918'),

        SizedBox(height: UiConstants.spacing4),

        // APR行
        _buildInfoRow('APR', widget.apr ?? '5.22%-10.22%'),

        SizedBox(height: UiConstants.spacing4),

        // 累计收益行
        _buildProfitRow(),
      ],
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: context.templateStyle.text.descriptionSmall.copyWith(color: context.templateColors.textSecondary)),
        Text(value, style: context.templateStyle.text.descriptionSmall),
      ],
    );
  }

  /// 构建累计收益行
  Widget _buildProfitRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text('累计收益', style: context.templateStyle.text.descriptionSmall.copyWith(color: context.templateColors.textSecondary)),
        Text(
          '${widget.profitValue ?? '0.06762222'} ${widget.profitUnit ?? 'USDT'}',
          style: context.templateStyle.text.descriptionSmall.copyWith(color: context.templateColors.tradeBuy),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton() {
    return CommonButton(
      text: '查看详情',
      size: CommonButtonSize.small,
      width: double.infinity,
      backgroundColor: context.templateColors.textTertiary.withValues(alpha: 0.1),
      foregroundColor: context.templateColors.textPrimary,
      textStyle: context.templateStyle.text.bodySmall,
      margin: EdgeInsets.only(top: UiConstants.spacing14),
      onPressed: widget.onTap,
    );
  }
}
