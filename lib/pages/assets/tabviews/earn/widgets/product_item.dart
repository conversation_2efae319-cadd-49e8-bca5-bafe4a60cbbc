/*
*  产品项组件
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class ProductItem extends StatefulWidget {
  const ProductItem({super.key});

  @override
  State<ProductItem> createState() => _ProductItemState();
}

class _ProductItemState extends State<ProductItem> {
  // 是否展开
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return SizedBox(child: Column(children: [_buildHeader(), if (_isExpanded) _buildFoldInfo()]));
  }

  // 构建头部信息
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18, vertical: UiConstants.spacing14),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('活期简单赚币', style: context.templateStyle.text.bodyTextMedium),
              Text('共 1 笔持仓', style: context.templateStyle.text.descriptionSmall),
            ],
          ),
          Row(
            children: [
              Text('3.77 CNY', style: context.templateStyle.text.bodyTextMedium),
              InkWellWidget(
                onTap:
                    () => {
                      setState(() {
                        _isExpanded = !_isExpanded;
                      }),
                    },
                child: Container(
                  padding: EdgeInsets.only(left: UiConstants.spacing10),
                  child: ThemedImage(name: 'arrow_triangle_down_gray', size: UiConstants.iconSize12),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建折叠信息
  Widget _buildFoldInfo() {
    return Padding(
      padding: EdgeInsets.only(right: UiConstants.spacing18, left: UiConstants.spacing56, top: UiConstants.spacing10),
      child: Container(
        padding: EdgeInsets.only(bottom: UiConstants.spacing10),
        decoration: BoxDecoration(border: Border(bottom: BorderSide(width: 0.5, color: context.templateColors.divider))),
        child: Column(
          children: [
            Row(
              children: [
                ThemedImage(
                  name: 'USDT',
                  size: 24,
                  folder: ThemedAssetFolder.crypto,
                  borderRadius: BorderRadius.circular(UiConstants.borderRadiusCircle),
                  margin: EdgeInsets.only(right: UiConstants.spacing4),
                ),
                Text('USDT', style: context.templateStyle.text.bodyTextMedium),
              ],
            ),
            SizedBox(height: UiConstants.spacing12),
            _buildInfoRow('数量', '0.5252918'),
            SizedBox(height: UiConstants.spacing4),
            _buildInfoRow('APR', '0.5252918'),
            SizedBox(height: UiConstants.spacing4),
            _buildInfoRow('累计收益', '0.5252918'),
            _buildActionButton(),
          ],
        ),
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: context.templateStyle.text.descriptionSmall.copyWith(color: context.templateColors.textSecondary)),
        Text(value, style: context.templateStyle.text.descriptionSmall),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton() {
    return CommonButton(
      text: '查看详情',
      size: CommonButtonSize.small,
      width: double.infinity,
      backgroundColor: context.templateColors.textTertiary.withValues(alpha: 0.1),
      foregroundColor: context.templateColors.textPrimary,
      textStyle: context.templateStyle.text.bodySmall,
      margin: EdgeInsets.only(top: UiConstants.spacing14),
      onPressed: () => {},
    );
  }
}
