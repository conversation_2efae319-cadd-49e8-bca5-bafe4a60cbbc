/*
*  链上资产项组件
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class OnchainAssetsItem extends StatefulWidget {
  const OnchainAssetsItem({super.key});

  @override
  State<OnchainAssetsItem> createState() => _OnchainAssetsItemState();
}

class _OnchainAssetsItemState extends State<OnchainAssetsItem> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing10,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: UiConstants.spacing4),
            child: ThemedImage(
              name: '',
              size: 24,
              borderRadius: BorderRadius.circular(
                UiConstants.borderRadiusCircle,
              ),
              folder: ThemedAssetFolder.crypto,
            ),
          ),
          SizedBox(width: UiConstants.spacing12),
          Expanded(
            child: Column(
              children: [
                // 交易对 + 余额 + 盈亏
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'BGSC',
                          style: context.templateStyle.text.bodyLargeMedium,
                        ),
                        Text.rich(
                          TextSpan(
                            style: context.templateStyle.text.descriptionSmall,
                            children: [
                              TextSpan(text: '¥ 0.94635 '),
                              TextSpan(
                                text: ' 0.00%',
                                style: context
                                    .templateStyle
                                    .text
                                    .descriptionSmall
                                    .copyWith(
                                      color: context.templateColors.tradeBuy,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '0.00000000 BGSC',
                          style: context.templateStyle.text.bodyLargeMedium,
                        ),
                        Text(
                          '¥ 0.00',
                          style: context.templateStyle.text.descriptionSmall,
                        ),
                      ],
                    ),
                  ],
                ),

                Container(
                  padding: EdgeInsets.symmetric(
                    vertical: UiConstants.spacing14,
                  ),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        width: 0.5,
                        color: context.templateColors.divider,
                      ),
                    ),
                  ),
                  child: Column(
                    children: [
                      // 持币盈亏
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '持币盈亏',
                            style: context.templateStyle.text.descriptionSmall
                                .copyWith(
                                  decorationStyle: TextDecorationStyle.dashed,
                                  decoration: TextDecoration.underline,
                                  decorationColor:
                                      context.templateColors.textTertiary,
                                ),
                          ),
                          Text(
                            '0.00 CNY',
                            style: context.templateStyle.text.descriptionSmall,
                          ),
                        ],
                      ),

                      // 买入市值
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '买入市值',
                            style: context.templateStyle.text.descriptionSmall,
                          ),
                          Text(
                            '0.00 CNY',
                            style: context.templateStyle.text.descriptionSmall,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
              left: UiConstants.spacing10,
              top: UiConstants.spacing4,
            ),
            child: InkWellWidget(
              child: Icon(
                RemixIcons.more_2_line,
                size: 20,
                color: context.templateColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
