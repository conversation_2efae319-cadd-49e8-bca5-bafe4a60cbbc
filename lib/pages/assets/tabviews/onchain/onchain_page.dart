/*
*  链上标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:qubic_exchange/models/pages/assets/common/menu_item_data.dart';
import '../../widgets/index.dart';
import './widgets/onchain_assets_item.dart';

class OnchainPage extends StatefulWidget {
  const OnchainPage({super.key});

  @override
  State<OnchainPage> createState() => _OnchainPageState();
}

class _OnchainPageState extends State<OnchainPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  static const List<TabItem> _tabKeys = [TabItem(title: '资产')];

  /// 菜单项列表
  late final List<MenuItemData> _menuItems;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabKeys.length, vsync: this);

    // 初始化菜单项列表
    _menuItems = [
      MenuItemData.trade(_handleGoToTrade),
      MenuItemData.copyManage(_handleCopyManagement),
      MenuItemData.becomeExpert(_handleBecomeExpert),
    ];
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    // 模拟刷新操作
    await Future.delayed(const Duration(seconds: 2));
    // 这里可以添加实际的数据刷新逻辑
  }

  // 处理去交易点击
  void _handleGoToTrade() {
    // TODO: 导航到交易页面
  }

  // 处理跟单管理点击
  void _handleCopyManagement() {
    // TODO: 导航到跟单管理页面
  }

  // 处理成为交易专家点击
  void _handleBecomeExpert() {
    // TODO: 导航到成为交易专家页面
  }

  /// 构建菜单项列表
  List<HeaderMenuItem> _buildMenuItems() {
    return _menuItems.map((menuData) {
      return HeaderMenuItem(
        iconName: menuData.iconName,
        title: menuData.title,
        onTap: menuData.onTap,
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final maxOffset = 44.0;
    return EasyRefresh.builder(
      header: ClassicHeader(
        clamping: true,
        position: IndicatorPosition.locator,
        triggerOffset: maxOffset,
        processedDuration: const Duration(seconds: 1),
        safeArea: false,
        showMessage: false,
        showText: false,
        maxOverOffset: maxOffset,
      ),
      onRefresh: _onRefresh,
      childBuilder: (context, physics) {
        return ScrollConfiguration(
          behavior: const ERScrollBehavior(),
          child: ExtendedNestedScrollView(
            physics: physics,
            onlyOneScrollInBody: true,
            pinnedHeaderSliverHeightBuilder: () {
              return 60.0;
            },
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return <Widget>[
                // 刷新指示器定位器
                const HeaderLocator.sliver(clearExtent: false),

                // 顶部区域
                SliverToBoxAdapter(
                  child: Column(
                    children: [
                      HeaderSection(
                        titleText: '链上总资产',
                        showDailyPnL: false,
                        menuItems: _buildMenuItems(),
                      ),
                    ],
                  ),
                ),

                // 标签栏
                SliverPersistentHeader(
                  pinned: true,
                  delegate: StickyDelegate(
                    maxHeight: 60,
                    child: _buildTabbar(),
                    backgroundColor: context.templateColors.surface,
                  ),
                ),
              ];
            },
            body: _buildTabview(physics),
          ),
        );
      },
    );
  }

  // 构建标签栏
  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing14,
      ),
      child: Row(
        children: [
          Expanded(
            child: TabbarWidget(
              height: 50,
              controller: _tabController,
              tabs: _tabKeys,
              labelStyle: context.templateStyle.text.tabText,
              selectedColor: context.templateColors.textPrimary,
              unselectedColor: context.templateColors.textSecondary,
              showIndicator: false,
              labelPadding: EdgeInsets.only(right: UiConstants.spacing14),
            ),
          ),
          SizedBox(width: UiConstants.spacing18),
          ThemedImage(name: '', width: 24, height: 24, followTheme: true),
        ],
      ),
    );
  }

  // 构建标签内容
  Widget _buildTabview(ScrollPhysics physics) {
    return TabBarView(
      controller: _tabController,
      physics: NeverScrollableScrollPhysics(),
      children: [
        // 资产列表
        _buildCopyList(physics),
      ],
    );
  }

  // 构建列表
  Widget _buildCopyList(physics) {
    return ListView.builder(
      physics: physics,
      itemCount: 10,
      itemBuilder: (context, index) {
        return OnchainAssetsItem();
      },
    );
  }
}
