/*
*  杠杆标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import './tabviews/cross_margin_page.dart';
import './tabviews/isolated_margin_page.dart';

class MarginTabPage extends StatefulWidget {
  const MarginTabPage({super.key});

  @override
  State<MarginTabPage> createState() => _MarginTabPageState();
}

class _MarginTabPageState extends State<MarginTabPage>
    with TickerProviderStateMixin {
  // 标签控制器
  late TabController _tabController;

  // 标签键
  final List<TabItem> _tabKeys = [TabItem(title: '逐仓'), TabItem(title: '全仓')];

  // 初始化
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabKeys.length, vsync: this);
  }

  // 销毁
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: [_buildTabbar(), Expanded(child: _buildTabview())]);
  }

  // 构建标签栏
  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing10,
      ),
      child: TabbarWidget(
        height: 24,
        controller: _tabController,
        tabs: _tabKeys,
        labelStyle: context.templateStyle.text.tabTextSmall,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: true,
        indicatorSize: TabBarIndicatorSize.label,
        indicatorStyle: TabBarIndicatorStyle.filled,
        indicatorColor: context.templateColors.inputBackground,
        tabPadding: EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
        labelPadding: EdgeInsets.only(right: UiConstants.spacing10),
      ),
    );
  }

  // 构建标签内容
  Widget _buildTabview() {
    return TabBarView(
      controller: _tabController,
      children: [
        // 逐仓
        IsolatedMarginPage(),

        // 全仓
        CrossMarginPage(),
      ],
    );
  }
}
