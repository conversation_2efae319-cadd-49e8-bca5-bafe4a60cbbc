/*
*  逐仓杠杆标签页
*/

import 'package:flutter/material.dart';

import 'package:provider/provider.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';
import 'package:qubic_exchange/services/assets/assets_service.dart';
import 'package:qubic_exchange/services/assets/models/asset_balance_model.dart';
import 'package:qubic_exchange/services/market/market_service.dart';
import 'package:qubic_exchange/utils/formatting/number_format_util.dart';
import '../../../widgets/index.dart';
import '../../../../../models/pages/assets/common/menu_item_data.dart';
import './../widgets/margin_assets_item.dart';

class IsolatedMarginPage extends StatefulWidget {
  const IsolatedMarginPage({super.key});

  @override
  State<IsolatedMarginPage> createState() => _IsolatedMarginPageState();
}

class _IsolatedMarginPageState extends State<IsolatedMarginPage>
    with TickerProviderStateMixin {
  // 标签控制器
  late TabController _tabController;

  // 标签键
  final List<TabItem> _tabKeys = [TabItem(title: '资产')];

  /// 菜单项列表
  late final List<MenuItemData> _menuItems;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabKeys.length, vsync: this);

    // 初始化菜单项列表
    _menuItems = [
      MenuItemData(
        iconName: 'deposit',
        title: '借款',
        onTap: () => _handleDeposit(),
      ),
      MenuItemData(
        iconName: 'deposit',
        title: '还款',
        onTap: () => _handleDeposit(),
      ),
      MenuItemData(
        iconName: 'transfer',
        title: '划转',
        onTap: () => _handleTransfer(),
      ),
      MenuItemData(
        iconName: 'pnl',
        title: '盈亏',
        onTap: () => _handlePnL(),
      ),
    ];


  }

  /// 格式化数字，去掉末尾的0，至少保留2位小数，添加千分位分隔符
  String _formatBalance(double amount) {
    // 先格式化为8位小数
    String formatted = amount.toStringAsFixed(8);

    // 去掉末尾的0
    formatted = formatted.replaceAll(RegExp(r'0*$'), '');

    // 如果末尾是小数点，去掉小数点
    if (formatted.endsWith('.')) {
      formatted = formatted.substring(0, formatted.length - 1);
    }

    // 确保至少有2位小数
    if (!formatted.contains('.')) {
      formatted += '.00';
    } else {
      final parts = formatted.split('.');
      if (parts[1].length < 2) {
        formatted += '0' * (2 - parts[1].length);
      }
    }

    // 分离整数部分和小数部分
    final parts = formatted.split('.');
    String integerPart = parts[0];
    String decimalPart = parts.length > 1 ? parts[1] : '';

    // 为整数部分添加千分位分隔符
    String formattedInteger = '';
    for (int i = 0; i < integerPart.length; i++) {
      if (i > 0 && (integerPart.length - i) % 3 == 0) {
        formattedInteger += ',';
      }
      formattedInteger += integerPart[i];
    }

    // 组合整数部分和小数部分
    return decimalPart.isNotEmpty ? '$formattedInteger.$decimalPart' : formattedInteger;
  }

  /// 计算逐仓杠杆账户总USDT价值
  double _calculateTotalUsdtValue() {
    final assetsService = AssetsService.instance;
    final isolatedMarginAccount = assetsService.getAccountBalance(AccountType.isolatedMargin);

    if (isolatedMarginAccount == null) return 0.0;

    double totalUsdt = 0.0;

    for (final currency in isolatedMarginAccount.currencies) {
      if (!currency.hasBalance) continue;

      // 逐仓杠杆主要计算保证金价值
      if (currency.isIsolatedMarginPair) {
        // 保证金余额直接按USDT计算（因为大部分逐仓杠杆都是以USDT为计价币）
        totalUsdt += currency.totalMarginAmount;
      } else {
        // 普通余额需要通过ticker换算
        final symbol = currency.symbol;
        if (symbol == 'USDT') {
          totalUsdt += currency.totalAmount;
        } else {
          final currencyModel = MarketService.getCurrencyModel(currency.currencyId);
          if (currencyModel != null) {
            dynamic ticker;
            for (final key in ['1', '5', '0']) {
              ticker = currencyModel.tickers[key];
              if (ticker != null && ticker.lastPrice > 0) {
                break;
              }
            }
            if (ticker != null && ticker.lastPrice > 0) {
              totalUsdt += currency.totalAmount * ticker.lastPrice;
            }
          }
        }
      }
    }

    return totalUsdt;
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    // 模拟刷新操作
    await Future.delayed(const Duration(seconds: 2));
    // 这里可以添加实际的数据刷新逻辑
  }

  // 处理充值点击
  void _handleDeposit() {
    // TODO: 导航到充值页面
  }

  // 处理划转点击
  void _handleTransfer() {
    // TODO: 导航到划转页面
  }

  // 处理盈亏点击
  void _handlePnL() {
    // TODO: 导航到盈亏页面
  }

  /// 构建菜单项列表
  List<HeaderMenuItem> _buildMenuItems() {
    return _menuItems.map((menuData) {
      return HeaderMenuItem(
        iconName: menuData.iconName,
        title: menuData.title,
        onTap: menuData.onTap,
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final maxOffset = 44.0;
    return EasyRefresh.builder(
      header: ClassicHeader(
        clamping: true,
        position: IndicatorPosition.locator,
        triggerOffset: maxOffset,
        processedDuration: const Duration(seconds: 1),
        safeArea: false,
        showMessage: false,
        showText: false,
        maxOverOffset: maxOffset,
      ),
      onRefresh: _onRefresh,
      childBuilder: (context, physics) {
        return ScrollConfiguration(
          behavior: const ERScrollBehavior(),
          child: ExtendedNestedScrollView(
            physics: physics,
            onlyOneScrollInBody: true,
            pinnedHeaderSliverHeightBuilder: () {
              return 60.0;
            },
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return <Widget>[
                // 刷新指示器定位器
                const HeaderLocator.sliver(clearExtent: false),

                // 顶部区域
                SliverToBoxAdapter(
                  child: Consumer<AuthProvider>(
                    builder: (context, authProvider, child) {
                      final totalUsdt = _calculateTotalUsdtValue();
                      final fiatSymbol = authProvider.selectedCurrency?.symbol ?? 'USD';

                      // 逐仓杠杆的净资产 = 总资产 - 总负债（暂时负债为0）
                      final totalDebt = 0.0;
                      final netAssets = totalUsdt - totalDebt;
                      final netAssetsFiatValue = netAssets * authProvider.currentRate;

                      return Column(
                        children: [
                          HeaderSection(
                            titleText: '杠杆资产估值',
                            showDailyPnL: true,
                            menuItems: _buildMenuItems(),
                            balanceValue: _formatBalance(netAssets),
                            balanceUnit: 'USDT',
                            equivalentValue: '≈ ${NumberFormatUtil.formatWithComma(netAssetsFiatValue, decimalDigits: 2)} $fiatSymbol',
                            insertWidget: WalletProfitOverview(
                              totalProfitData: WalletProfitData(
                                title: '净资产',
                                value: _formatBalance(netAssets),
                                unit: 'USDT',
                                equivalentValue: NumberFormatUtil.formatWithComma(netAssetsFiatValue, decimalDigits: 2),
                                equivalentUnit: fiatSymbol,
                                showDashed: true,
                              ),
                              availableBalanceData: WalletProfitData(
                                title: '总负债',
                                value: _formatBalance(totalDebt),
                                unit: 'USDT',
                                equivalentValue: NumberFormatUtil.formatWithComma(totalDebt * authProvider.currentRate, decimalDigits: 2),
                                equivalentUnit: fiatSymbol,
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),

                // 标签栏
                SliverPersistentHeader(
                  pinned: true,
                  delegate: StickyDelegate(
                    maxHeight: 60,
                    child: _buildTabbar(),
                    backgroundColor: context.templateColors.surface,
                  ),
                ),
              ];
            },
            body: _buildTabview(physics),
          ),
        );
      },
    );
  }

  // 构建标签栏
  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing14,
      ),
      child: Row(
        children: [
          Expanded(
            child: TabbarWidget(
              height: 50,
              controller: _tabController,
              tabs: _tabKeys,
              labelStyle: context.templateStyle.text.tabText,
              selectedColor: context.templateColors.textPrimary,
              unselectedColor: context.templateColors.textSecondary,
              showIndicator: false,
              labelPadding: EdgeInsets.only(right: UiConstants.spacing18),
            ),
          ),
          ThemedImage(
            name: 'icon_search',
            width: 20,
            height: 20,
            margin: EdgeInsets.only(right: UiConstants.spacing18),
          ),
          ThemedImage(name: '', width: 24, height: 24, followTheme: true),
        ],
      ),
    );
  }

  // 构建标签内容
  Widget _buildTabview(ScrollPhysics physics) {
    return TabBarView(
      controller: _tabController,
      physics: NeverScrollableScrollPhysics(),
      children: [
        // 资产列表
        _buildAssetsList(physics),
      ],
    );
  }

  // 构建资产列表
  Widget _buildAssetsList(ScrollPhysics physics) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final assetsService = AssetsService.instance;
        final isolatedMarginAccount = assetsService.getAccountBalance(AccountType.isolatedMargin);

        if (isolatedMarginAccount == null || isolatedMarginAccount.currencies.isEmpty) {
          return Center(
            child: Padding(
              padding: EdgeInsets.all(UiConstants.spacing32),
              child: Text(
                '暂无逐仓杠杆账户资产',
                style: context.templateStyle.text.bodyText.copyWith(
                  color: context.templateColors.textTertiary,
                ),
              ),
            ),
          );
        }

        // 过滤有余额的币种
        final currenciesWithBalance = isolatedMarginAccount.currenciesWithBalance;

        if (currenciesWithBalance.isEmpty) {
          return Center(
            child: Padding(
              padding: EdgeInsets.all(UiConstants.spacing32),
              child: Text(
                '暂无逐仓杠杆资产',
                style: context.templateStyle.text.bodyText.copyWith(
                  color: context.templateColors.textTertiary,
                ),
              ),
            ),
          );
        }

        return ListView.builder(
          physics: physics,
          itemCount: currenciesWithBalance.length,
          itemBuilder: (context, index) {
            final currency = currenciesWithBalance[index];

            // 解析交易对符号（如BTCUSDT -> BTC + USDT）
            final symbol = currency.symbol;
            String baseAsset = symbol;
            String quoteAsset = 'USDT';

            // 尝试解析交易对
            if (symbol.endsWith('USDT')) {
              baseAsset = symbol.substring(0, symbol.length - 4);
              quoteAsset = 'USDT';
            } else if (symbol.endsWith('BTC')) {
              baseAsset = symbol.substring(0, symbol.length - 3);
              quoteAsset = 'BTC';
            } else if (symbol.endsWith('ETH')) {
              baseAsset = symbol.substring(0, symbol.length - 3);
              quoteAsset = 'ETH';
            }

            // 计算法币价值（保证金余额 * 汇率）
            final fiatValue = currency.totalMarginAmount * authProvider.currentRate;
            final fiatSymbol = authProvider.selectedCurrency?.symbol ?? 'USD';

            // 构建MarginAssetData
            final assetData = MarginAssetData(
              symbol: symbol,
              baseAsset: baseAsset,
              quoteAsset: quoteAsset,
              logo: currency.logo,
              availableAmount: currency.availableAmount,
              frozenAmount: currency.frozenAmount,
              marginQuoteAmount: currency.marginQuoteAmount,
              marginFrozenAmount: currency.marginFrozenAmount,
              fiatValue: fiatValue,
              fiatSymbol: fiatSymbol,
            );

            return MarginAssetsItem(
              isCrossMargin: false,
              data: assetData,
              onTap: () {
                debugPrint('点击了逐仓杠杆资产: ${assetData.symbol}');
              },
            );
          },
        );
      },
    );
  }
}
