/*
*  杠杆资产项组件
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/utils/formatting/number_format_util.dart';

/// 杠杆资产数据模型
class MarginAssetData {
  /// 交易对符号（如BTCUSDT）
  final String symbol;

  /// 基础币种（如BTC）
  final String baseAsset;

  /// 计价币种（如USDT）
  final String quoteAsset;

  /// 币种图标
  final String? logo;

  /// 可用余额
  final double availableAmount;

  /// 冻结余额
  final double frozenAmount;

  /// 保证金余额
  final double marginQuoteAmount;

  /// 保证金冻结
  final double marginFrozenAmount;

  /// 法币价值
  final double fiatValue;

  /// 法币符号
  final String fiatSymbol;

  const MarginAssetData({
    required this.symbol,
    required this.baseAsset,
    required this.quoteAsset,
    this.logo,
    required this.availableAmount,
    required this.frozenAmount,
    required this.marginQuoteAmount,
    required this.marginFrozenAmount,
    required this.fiatValue,
    required this.fiatSymbol,
  });

  /// 总余额
  double get totalAmount => availableAmount + frozenAmount;

  /// 总保证金
  double get totalMarginAmount => marginQuoteAmount + marginFrozenAmount;

  /// 是否有余额
  bool get hasBalance => totalAmount > 0 || totalMarginAmount > 0;
}

class MarginAssetsItem extends StatefulWidget {
  /// 是否为全仓
  final bool isCrossMargin;

  /// 杠杆资产数据
  final MarginAssetData? data;

  /// 点击回调
  final VoidCallback? onTap;

  const MarginAssetsItem({
    super.key,
    this.isCrossMargin = true,
    this.data,
    this.onTap,
  });

  @override
  State<MarginAssetsItem> createState() => _MarginAssetsItemState();
}

class _MarginAssetsItemState extends State<MarginAssetsItem> {
  @override
  Widget build(BuildContext context) {
    final data = widget.data;

    // 如果没有数据，显示默认内容
    if (data == null) {
      return _buildDefaultContent();
    }

    Widget content = Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing10,
      ),
      child: Row(
        children: [
          // 全仓杠杆显示币种图标
          if (widget.isCrossMargin) ...[
            ThemedImage(
              name: data.baseAsset.toLowerCase(),
              size: 24,
              folder: ThemedAssetFolder.crypto,
              borderRadius: BorderRadius.circular(
                UiConstants.borderRadiusCircle,
              ),
            ),
            SizedBox(width: UiConstants.spacing12),
          ],
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 左侧：币种信息
                if (!widget.isCrossMargin)
                  // 逐仓杠杆显示交易对
                  Text(
                    data.symbol,
                    style: context.templateStyle.text.bodyLargeMedium,
                  )
                else
                  // 全仓杠杆显示币种名称
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        data.baseAsset,
                        style: context.templateStyle.text.bodyLargeMedium,
                      ),
                      Text(
                        data.baseAsset,
                        style: context.templateStyle.text.hintText.copyWith(
                          color: context.templateColors.textSecondary,
                        ),
                      ),
                    ],
                  ),

                // 右侧：余额信息
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      _formatQuoteBalance(data),
                      style: context.templateStyle.text.bodyLargeMedium,
                    ),
                    Text(
                      _formatUsdtBalance(data),
                      style: context.templateStyle.text.descriptionSmall,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );

    // 如果有点击回调，包装为可点击组件
    return widget.onTap != null
        ? GestureDetector(
          onTap: widget.onTap,
          behavior: HitTestBehavior.opaque,
          child: content,
        )
        : content;
  }

  /// 构建默认内容
  Widget _buildDefaultContent() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing10,
      ),
      child: Row(
        children: [
          if (widget.isCrossMargin) ...[
            ThemedImage(
              name: '',
              size: 24,
              followTheme: true,
              folder: ThemedAssetFolder.crypto,
              borderRadius: BorderRadius.circular(
                UiConstants.borderRadiusCircle,
              ),
            ),
            SizedBox(width: UiConstants.spacing12),
          ],
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (!widget.isCrossMargin)
                  Text(
                    'H/USDT',
                    style: context.templateStyle.text.bodyLargeMedium,
                  )
                else ...[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'USDT',
                        style: context.templateStyle.text.bodyLargeMedium,
                      ),
                      Text(
                        'USDT',
                        style: context.templateStyle.text.hintText.copyWith(
                          color: context.templateColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '0.00000000 ${!widget.isCrossMargin ? 'H' : ''}',
                      style: context.templateStyle.text.bodyLargeMedium,
                    ),
                    Text(
                      '0.00 ${!widget.isCrossMargin ? 'USDT' : 'CNY'}',
                      style: context.templateStyle.text.descriptionSmall,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 格式化数字，去掉末尾的0，至少保留2位小数，添加千分位分隔符
  String _formatBalance(double amount) {
    // 先格式化为8位小数
    String formatted = amount.toStringAsFixed(8);

    // 去掉末尾的0
    formatted = formatted.replaceAll(RegExp(r'0*$'), '');

    // 如果末尾是小数点，去掉小数点
    if (formatted.endsWith('.')) {
      formatted = formatted.substring(0, formatted.length - 1);
    }

    // 确保至少有2位小数
    if (!formatted.contains('.')) {
      formatted += '.00';
    } else {
      final parts = formatted.split('.');
      if (parts[1].length < 2) {
        formatted += '0' * (2 - parts[1].length);
      }
    }

    // 分离整数部分和小数部分
    final parts = formatted.split('.');
    String integerPart = parts[0];
    String decimalPart = parts.length > 1 ? parts[1] : '';

    // 为整数部分添加千分位分隔符
    String formattedInteger = '';
    for (int i = 0; i < integerPart.length; i++) {
      if (i > 0 && (integerPart.length - i) % 3 == 0) {
        formattedInteger += ',';
      }
      formattedInteger += integerPart[i];
    }

    // 组合整数部分和小数部分
    return decimalPart.isNotEmpty ? '$formattedInteger.$decimalPart' : formattedInteger;
  }

  /// 格式化第一行余额显示（available + frozen）
  String _formatQuoteBalance(MarginAssetData data) {
    if (widget.isCrossMargin) {
      // 全仓杠杆显示总余额
      return _formatBalance(data.totalAmount);
    } else {
      // 逐仓杠杆第一行：available + frozen 的总和
      return '${_formatBalance(data.totalAmount)} ${data.baseAsset}';
    }
  }

  /// 格式化第二行余额显示（margin_quote + margin_frozen）
  String _formatUsdtBalance(MarginAssetData data) {
    if (widget.isCrossMargin) {
      // 全仓杠杆显示法币价值
      return '${NumberFormatUtil.formatWithComma(data.fiatValue, decimalDigits: 2)} ${data.fiatSymbol}';
    } else {
      // 逐仓杠杆第二行：margin_quote + margin_frozen 的总和
      return '${_formatBalance(data.totalMarginAmount)} ${data.quoteAsset}';
    }
  }
}
