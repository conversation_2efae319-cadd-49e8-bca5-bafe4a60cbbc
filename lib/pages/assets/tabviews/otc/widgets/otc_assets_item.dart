/*
*  OTC 资产项组件
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/services/assets/models/asset_balance_model.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';
import 'package:qubic_exchange/services/market/market_service.dart';
import 'package:qubic_exchange/utils/formatting/number_format_util.dart';

class OtcAssetsItem extends StatefulWidget {
  final CurrencyBalance currency;
  final AuthProvider authProvider;

  const OtcAssetsItem({
    super.key,
    required this.currency,
    required this.authProvider,
  });

  @override
  State<OtcAssetsItem> createState() => _OtcAssetsItemState();
}

class _OtcAssetsItemState extends State<OtcAssetsItem> {

  /// 计算法币价值
  String _calculateFiatValue() {
    final currency = widget.currency;
    final selectedCurrency = widget.authProvider.selectedCurrency;
    final currentRate = widget.authProvider.currentRate;

    if (selectedCurrency == null) return '0.00';

    // 先计算USDT价值
    double usdtValue = 0.0;
    final symbol = currency.symbol;

    if (symbol == 'USDT') {
      usdtValue = currency.totalAmount;
    } else {
      final currencyModel = MarketService.getCurrencyModel(currency.currencyId);
      if (currencyModel != null) {
        dynamic ticker;
        for (final key in ['1', '5', '0']) {
          ticker = currencyModel.tickers[key];
          if (ticker != null && ticker.lastPrice > 0) {
            break;
          }
        }
        if (ticker != null && ticker.lastPrice > 0) {
          usdtValue = currency.totalAmount * ticker.lastPrice;
        }
      }
    }

    // 再换算为法币
    final fiatValue = usdtValue * currentRate;
    return NumberFormatUtil.formatWithComma(fiatValue, decimalDigits: 2);
  }

  /// 格式化余额显示
  String _formatBalance(double amount) {
    return NumberFormatUtil.formatWithComma(amount, decimalDigits: 8);
  }

  @override
  Widget build(BuildContext context) {
    final currency = widget.currency;
    final symbol = currency.symbol;
    final totalBalance = currency.totalAmount;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing14,
      ),
      child: Row(
        children: [
          ThemedImage(
            name: symbol.toLowerCase(),
            size: 24,
            folder: ThemedAssetFolder.crypto,
            borderRadius: BorderRadius.circular(UiConstants.borderRadiusCircle),
          ),
          SizedBox(width: UiConstants.spacing12),
          Expanded(
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      symbol,
                      style: context.templateStyle.text.bodyLargeMedium,
                    ),
                    Text(
                      symbol,
                      style: context.templateStyle.text.descriptionSmall,
                    ),
                  ],
                ),
                Spacer(),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      _formatBalance(totalBalance),
                      style: context.templateStyle.text.bodyLargeMedium,
                    ),
                    Text(
                      _calculateFiatValue(),
                      style: context.templateStyle.text.descriptionSmall,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
