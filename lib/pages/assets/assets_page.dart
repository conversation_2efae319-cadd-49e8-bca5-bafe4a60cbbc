/*
*  资产界面
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
// import 'package:qubic_exchange/pages/assets/tabviews/index.dart';

// 单独导入需要的页面
import 'package:qubic_exchange/pages/assets/tabviews/overview/overview_page.dart';
import 'package:qubic_exchange/pages/assets/tabviews/spot/spot_tab_page.dart';
import 'package:qubic_exchange/pages/assets/tabviews/futures/futures_tab_page.dart';
import 'package:qubic_exchange/pages/assets/tabviews/margin/margin_tab_page.dart';
import 'package:qubic_exchange/pages/assets/tabviews/futures_copy/futures_copy_page.dart';
import 'package:qubic_exchange/pages/assets/tabviews/earn/earn_tab_page.dart';
import 'package:qubic_exchange/pages/assets/tabviews/otc/otc_page.dart';
// 暂时注释掉链上和交易机器人页面的导入
// import 'package:qubic_exchange/pages/assets/tabviews/onchain/onchain_page.dart';
// import 'package:qubic_exchange/pages/assets/tabviews/trading_bot/trading_bot_page.dart';

class AssetsPage extends StatefulWidget {
  const AssetsPage({super.key});

  @override
  State<AssetsPage> createState() => _AssetsPageState();
}

class _AssetsPageState extends State<AssetsPage> with TickerProviderStateMixin {
  // 构建标签控制器
  late TabController _mainTabController;

  // 构建标签内容
  static const List<TabItem> _manTabKeys = [
    TabItem(title: '总览'),
    TabItem(title: '资金'),  // 原OTC改为资金，移动到第二位
    TabItem(title: '现货'),
    TabItem(title: '合约'),
    TabItem(title: '杠杆'),
    TabItem(title: '跟单'),
    // TabItem(title: '链上'),  // 暂时注释掉链上功能
    TabItem(title: '理财'),
    // TabItem(title: '交易机器人'),  // 暂时注释掉交易机器人功能
  ];

  @override
  void initState() {
    super.initState();
    _mainTabController = TabController(length: _manTabKeys.length, vsync: this);
  }

  @override
  void dispose() {
    _mainTabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.templateColors.surface,
      body: SafeArea(
        child: Column(children: [_buildMainTabbar(), _buildMainTabview()]),
      ),
    );
  }

  // 构建顶部标签栏
  Widget _buildMainTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            width: UiConstants.borderWidth0_5,
            color: context.templateColors.divider,
          ),
        ),
      ),
      child: TabbarWidget(
        controller: _mainTabController,
        tabs: _manTabKeys,
        labelStyle: context.templateStyle.text.bodyLargeMedium.copyWith(
          fontSize: UiConstants.fontSize18,
        ),
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: false,
        labelPadding: EdgeInsets.only(right: UiConstants.spacing14),
        dividerColor: Colors.transparent,
      ),
    );
  }

  // 构建标签内容
  Widget _buildMainTabview() {
    return Expanded(
      child: TabBarView(
        controller: _mainTabController,
        children: [
          // 总览
          OverviewPage(),

          // 资金 (原OTC) - 移动到第二位
          OtcPage(),

          // 现货
          SpotTabPage(),

          // 合约
          FuturesTabPage(),

          // 杠杆
          MarginTabPage(),

          // 合约跟单
          FuturesCopyPage(),

          // 链上 - 暂时注释掉
          // OnchainPage(),

          // 理财
          EarnTabPage(),

          // 交易机器人 - 暂时注释掉
          // TradingBotPage(),
        ],
      ),
    );
  }
}
