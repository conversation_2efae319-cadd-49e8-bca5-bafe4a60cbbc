/*
*  行情界面数字货币项数据模型
*
*  功能：
*  - 专门用于 MarketCryptoList 组件的数据模型
*  - 包含显示所需的所有字段
*  - 支持序列化和反序列化
*  - 提供数据验证和便捷方法
*/

import '../../../utils/formatting/number_format_util.dart';

/// 数字货币项数据模型
class CryptoItemData {
  /// 币种符号，如 BTC
  final String symbol;

  /// 交易对，如 USDT
  final String quoteCurrency;

  /// 当前价格（USDT）
  final double currentPrice;

  /// 人民币价格
  final double cnyPrice;

  /// 涨跌幅百分比
  final double changePercentage;

  /// 24小时成交量
  final double volume24h;

  /// 成交量单位（万、亿）
  final String volumeUnit;

  /// 价格精度（小数位数）
  final int precision;

  /// 标签（如 永续、10X 等）
  final String? tradingTag;

  /// 币种logo图标URL
  final String? logo;

  /// 最后更新时间
  final DateTime lastUpdated;

  CryptoItemData({
    required this.symbol,
    required this.quoteCurrency,
    required this.currentPrice,
    required this.cnyPrice,
    required this.changePercentage,
    required this.volume24h,
    required this.volumeUnit,
    this.precision = 2,
    this.tradingTag,
    this.logo,
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now() {
    // 数据验证
    _validateData();
  }

  /// 数据验证
  void _validateData() {
    if (symbol.isEmpty) throw ArgumentError('币种符号不能为空');
    if (quoteCurrency.isEmpty) throw ArgumentError('交易对不能为空');
    if (currentPrice < 0) throw ArgumentError('价格不能为负数');
    if (cnyPrice < 0) throw ArgumentError('人民币价格不能为负数');
    if (volume24h < 0) throw ArgumentError('成交量不能为负数');
    if (volumeUnit.isEmpty) throw ArgumentError('成交量单位不能为空');
  }

  /// 是否为正涨幅
  bool get isPriceIncreasing => changePercentage > 0;

  /// 是否为平盘
  bool get isPriceFlat => changePercentage == 0;

  /// 是否为负涨幅
  bool get isPriceDecreasing => changePercentage < 0;

  /// 获取完整交易对名称（现货格式）
  String get fullSymbolSpot => '$symbol / $quoteCurrency';

  /// 获取完整交易对名称（合约格式）
  String get fullSymbolContract => '$symbol$quoteCurrency';

  /// 获取格式化的成交量字符串
  String get formattedVolume => NumberFormatUtil.formatCompact(volume24h);

  /// 获取格式化的价格字符串
  String get formattedPrice => NumberFormatUtil.formatWithComma(currentPrice, decimalDigits: precision);

  /// 获取格式化的涨跌幅字符串
  String get formattedChangePercentage => '${changePercentage >= 0 ? '+' : ''}${NumberFormatUtil.formatWithComma(changePercentage)}%';

  /// 获取格式化的人民币价格字符串
  String get formattedCnyPrice => '0';

  /// 创建数据副本并支持部分字段更新
  CryptoItemData copyWith({
    String? symbol,
    String? quoteCurrency,
    double? currentPrice,
    double? cnyPrice,
    double? changePercentage,
    double? volume24h,
    String? volumeUnit,
    int? precision,
    String? tradingTag,
    String? logo,
    DateTime? lastUpdated,
  }) {
    return CryptoItemData(
      symbol: symbol ?? this.symbol,
      quoteCurrency: quoteCurrency ?? this.quoteCurrency,
      currentPrice: currentPrice ?? this.currentPrice,
      cnyPrice: cnyPrice ?? this.cnyPrice,
      changePercentage: changePercentage ?? this.changePercentage,
      volume24h: volume24h ?? this.volume24h,
      volumeUnit: volumeUnit ?? this.volumeUnit,
      precision: precision ?? this.precision,
      tradingTag: tradingTag ?? this.tradingTag,
      logo: logo ?? this.logo,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// 转换为 Map（用于序列化）
  Map<String, dynamic> toMap() {
    return {
      'symbol': symbol,
      'quoteCurrency': quoteCurrency,
      'currentPrice': currentPrice,
      'cnyPrice': cnyPrice,
      'changePercentage': changePercentage,
      'volume24h': volume24h,
      'volumeUnit': volumeUnit,
      'precision': precision,
      'tradingTag': tradingTag,
      'logo': logo,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// 从 Map 创建实例（用于反序列化）
  factory CryptoItemData.fromMap(Map<String, dynamic> map) {
    return CryptoItemData(
      symbol: map['symbol'] ?? '',
      quoteCurrency: map['quoteCurrency'] ?? '',
      currentPrice: (map['currentPrice'] ?? 0).toDouble(),
      cnyPrice: (map['cnyPrice'] ?? 0).toDouble(),
      changePercentage: (map['changePercentage'] ?? 0).toDouble(),
      volume24h: (map['volume24h'] ?? 0).toDouble(),
      volumeUnit: map['volumeUnit'] ?? '',
      precision: map['precision'] ?? 2,
      tradingTag: map['tradingTag'],
      logo: map['logo'],
      lastUpdated: map['lastUpdated'] != null ? DateTime.parse(map['lastUpdated']) : DateTime.now(),
    );
  }

  /// 从 JSON 创建实例
  factory CryptoItemData.fromJson(Map<String, dynamic> json) {
    return CryptoItemData.fromMap(json);
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return toMap();
  }

  /// 从 CryptoData 转换
  factory CryptoItemData.fromCryptoData(dynamic cryptoData) {
    return CryptoItemData(
      symbol: cryptoData.symbol,
      quoteCurrency: cryptoData.quoteCurrency,
      currentPrice: cryptoData.currentPrice,
      cnyPrice: cryptoData.cnyPrice,
      changePercentage: cryptoData.changePercentage,
      volume24h: cryptoData.volume24h,
      volumeUnit: cryptoData.volumeUnit,
      tradingTag: cryptoData.tradingTag,
      lastUpdated: cryptoData.lastUpdated,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CryptoItemData &&
        other.symbol == symbol &&
        other.quoteCurrency == quoteCurrency &&
        other.currentPrice == currentPrice &&
        other.cnyPrice == cnyPrice &&
        other.changePercentage == changePercentage &&
        other.volume24h == volume24h &&
        other.volumeUnit == volumeUnit &&
        other.tradingTag == tradingTag;
  }

  @override
  int get hashCode {
    return Object.hash(symbol, quoteCurrency, currentPrice, cnyPrice, changePercentage, volume24h, volumeUnit, tradingTag);
  }

  @override
  String toString() {
    return 'CryptoItemData(symbol: $symbol, quoteCurrency: $quoteCurrency, '
        'currentPrice: $currentPrice, changePercentage: $changePercentage%)';
  }
}
