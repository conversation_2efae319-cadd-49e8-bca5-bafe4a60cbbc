/*
* 价格提醒界面
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/pages/market/sub_pages/price_alert/dialogs/frequency_selector_sheet.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class PriceAlert extends StatefulWidget {
  const PriceAlert({super.key});

  @override
  State<PriceAlert> createState() => _PriceAlertState();
}

class _PriceAlertState extends State<PriceAlert> {
  String? _selectedFrequency = '只有一次';

  // 提醒模式相关状态
  bool _isNotificationModeExpanded = false;
  Map<String, bool> _notificationModes = {
    'APP 推送': true,
    '通知': false,
    '桌面弹出窗口': false,
  };

  // 获取已选择的提醒模式数量
  int get _selectedNotificationCount {
    return _notificationModes.values.where((selected) => selected).length;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(
        showBackButton: true,
        titleWidget: InkWellWidget(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('BTCUSDT', style: context.templateStyle.text.h4),
                  ThemedImage.asset(
                    'arrow_triangle_down',
                    size: UiConstants.iconSize10,
                    followTheme: true,
                    margin: EdgeInsets.only(left: UiConstants.spacing8),
                  ),
                ],
              ),
              Text('合约', style: context.templateStyle.text.descriptionSmall),
            ],
          ),
        ),
        actions: [
          Padding(
            padding: EdgeInsets.only(right: UiConstants.spacing18),
            child: Text('全部提醒', style: context.templateStyle.text.bodyText),
          ),
        ],
      ),
      backgroundColor: context.templateColors.surface,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // 价格选择
              _buildPriceSwitch(),

              // 输入区域
              _buildPriceInput(),

              // 频率选择
              _buildFrequency(),

              // 提醒模式
              _buildNotificationMode(),

              // 当前提醒列表
              _buildCurrentAlertList(),
            ],
          ),
        ),
      ),
    );
  }

  // 构建价格选择
  Widget _buildPriceSwitch() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '119042.1',
                style: context.templateStyle.text.h2.copyWith(
                  color: context.templateColors.tradeBuy,
                ),
              ),
            ],
          ),
          Text.rich(
            TextSpan(
              style: context.templateStyle.text.descriptionText.copyWith(
                color: context.templateColors.textTertiary,
              ),
              children: [
                TextSpan(text: '\$ 851723.98  '),
                TextSpan(
                  text: '+0.33%',
                  style: context.templateStyle.text.descriptionText.copyWith(
                    color: context.templateColors.tradeBuy,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建价格输入
  Widget _buildPriceInput() {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing18),
      child: Column(
        children: [
          /// 模式选择 & 价格转换
          Padding(
            padding: EdgeInsets.only(bottom: UiConstants.spacing12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Text('价格涨到', style: context.templateStyle.text.bodyText),
                    ThemedImage.asset(
                      'arrow_triangle_down_gray',
                      size: UiConstants.iconSize10,
                      margin: EdgeInsets.only(left: UiConstants.spacing4),
                    ),
                  ],
                ),
                Text(
                  '≈ ¥ 8623123.65',
                  style: context.templateStyle.text.descriptionText.copyWith(
                    color: context.templateColors.textTertiary,
                  ),
                ),
              ],
            ),
          ),

          /// 输入框
          TextFieldWidget(
            height: 54,
            hintText: '0.00',
            hintStyle: context.templateStyle.text.bodyLargeMedium,
            textStyle: context.templateStyle.text.bodyLargeMedium,
            textAlign: TextAlign.center,
            suffixIconConstraints: BoxConstraints(maxWidth: 70),
            prefixIconConstraints: BoxConstraints(maxWidth: 70),
            suffixIcon: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                SizedBox(
                  height: 20,
                  child: VerticalDivider(
                    width: UiConstants.spacing10,
                    color: context.templateColors.border,
                    thickness: 1,
                  ),
                ),
                InkWellWidget(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: UiConstants.spacing10,
                    ),
                    child: Icon(
                      RemixIcons.add_line,
                      size: UiConstants.iconSize24,
                      color: context.templateColors.textPrimary,
                    ),
                  ),
                ),
              ],
            ),
            prefixIcon: Row(
              children: [
                InkWellWidget(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: UiConstants.spacing10,
                    ),
                    child: Icon(
                      RemixIcons.subtract_line,
                      size: UiConstants.iconSize24,
                      color: context.templateColors.textPrimary,
                    ),
                  ),
                ),
                SizedBox(
                  height: 20,
                  child: VerticalDivider(
                    width: UiConstants.spacing10,
                    color: context.templateColors.border,
                    thickness: 1,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建价格滑块
  Widget _buildPriceSlider() {
    return Container();
  }

  // 构建频率选择
  Widget _buildFrequency() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(bottom: UiConstants.spacing12),
            child: Row(
              children: [
                Text('频率', style: context.templateStyle.text.bodyText),
                SizedBox(width: UiConstants.spacing6),
                Icon(
                  RemixIcons.information_2_line,
                  size: UiConstants.iconSize16,
                  color: context.templateColors.textPrimary,
                ),
              ],
            ),
          ),

          CommonButton(
            height: context.templateStyles.buttonHeightMedium,
            backgroundColor: context.templateColors.buttonSecondary,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _selectedFrequency ?? '选择频率',
                  style: context.templateStyle.text.bodyLarge,
                ),
                Icon(
                  RemixIcons.arrow_right_s_line,
                  size: UiConstants.iconSize20,
                  color: context.templateColors.textTertiary,
                ),
              ],
            ),
            onPressed:
                () => {
                  FrequencySelectorSheet.show(
                    context,
                    selectedFrequency: _selectedFrequency,
                    onSelected: (frequency) {
                      setState(() {
                        _selectedFrequency = frequency;
                      });
                    },
                  ),
                },
          ),
        ],
      ),
    );
  }

  // 构建提示模式
  Widget _buildNotificationMode() {
    return Container(
      margin: EdgeInsets.only(top: UiConstants.spacing14),
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: Column(
        children: [
          InkWellWidget(
            onTap: () {
              setState(() {
                _isNotificationModeExpanded = !_isNotificationModeExpanded;
              });
            },
            child: Row(
              children: [
                Text('提醒模式', style: context.templateStyle.text.bodyText),
                Text(
                  '（已选择 $_selectedNotificationCount 个）',
                  style: context.templateStyle.text.hintLarge,
                ),
                AnimatedRotation(
                  duration: const Duration(milliseconds: 200),
                  turns: _isNotificationModeExpanded ? 0.5 : 0,
                  child: ThemedImage.asset(
                    'arrow_triangle_down_gray',
                    size: UiConstants.iconSize10,
                    margin: EdgeInsets.only(left: UiConstants.spacing4),
                  ),
                ),
              ],
            ),
          ),

          // 展开收起内容
          AnimatedCrossFade(
            duration: const Duration(milliseconds: 300),
            crossFadeState:
                _isNotificationModeExpanded
                    ? CrossFadeState.showSecond
                    : CrossFadeState.showFirst,
            firstChild: const SizedBox.shrink(),
            secondChild: Container(
              width: double.infinity,
              padding: EdgeInsets.only(top: UiConstants.spacing22),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children:
                    _notificationModes.entries.map((entry) {
                      return Padding(
                        padding: EdgeInsets.only(bottom: UiConstants.spacing4),
                        child: CheckboxWidget(
                          value: entry.value,
                          customSize: UiConstants.iconSize16,
                          onChanged: (value) {
                            setState(() {
                              _notificationModes[entry.key] = value;
                            });
                          },
                          label: entry.key,
                          labelStyle: context.templateStyle.text.bodyTextMedium,
                        ),
                      );
                    }).toList(),
              ),
            ),
          ),

          // 创建提醒按钮
          CommonButton.primary(
            '创建提醒',
            size: CommonButtonSize.medium,
            borderRadius: UiConstants.borderRadiusCircle,
            width: double.infinity,
            margin: EdgeInsetsGeometry.only(top: UiConstants.spacing22),
            onPressed: () => {},
          ),
        ],
      ),
    );
  }

  // 构建当前提醒列表
  Widget _buildCurrentAlertList() {
    return Container(
      margin: EdgeInsets.only(top: UiConstants.spacing24),
      padding: EdgeInsets.all(UiConstants.spacing18),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            width: 7,
            color: context.templateColors.buttonSecondary,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('当前提醒', style: context.templateStyle.text.bodyLargeMedium),
              Text(
                '删除所有',
                style: context.templateStyle.text.descriptionText.copyWith(
                  color: context.templateColors.textTertiary,
                ),
              ),
            ],
          ),

          Padding(
            padding: EdgeInsets.only(top: UiConstants.spacing36),
            child: EmptyWidget(
              imageName: 'flutter_new_empty_data',
              imageSize: 100,
              text: '暂无提醒',
              format: ImageFormat.webp,
              fit: BoxFit.contain,
            ),
          ),
        ],
      ),
    );
  }
}
