/*
*  频率选择底部弹窗（使用公用弹窗）
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/utils/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class FrequencySelectorSheet {
  // 显示频率选择底部弹窗
  static Future<void> show(
    BuildContext context, {
    List<String>? frequencies,
    String? selectedFrequency,
    Function(String)? onSelected,
    bool showHeader = false,
    bool showCloseButton = false,
    bool showDragHandle = true,
    bool showHeaderBorder = false,
    double? maxHeight,
    double? headerHeight,
    bool useSafeArea = false,
  }) async {
    return await BottomSheetWidget.show<void>(
      context: context,
      title: '选择频率',
      titleAlign: TextAlign.start,
      showCloseButton: showCloseButton,
      showHeader: showHeader,
      showDragHandle: showDragHandle,
      headerHeight: headerHeight ?? 58,
      showHeaderBorder: showHeaderBorder,
      maxHeight: maxHeight ?? MediaQuery.of(context).size.height * 0.8,
      showBottomButton: false,
      useSafeArea: useSafeArea,
      child: _FrequencySelectorContent(
        frequencies: frequencies,
        selectedFrequency: selectedFrequency,
        onSelected: onSelected,
      ),
    );
  }
}

class _FrequencySelectorContent extends StatefulWidget {
  final List<String>? frequencies;
  final String? selectedFrequency;
  final Function(String)? onSelected;

  const _FrequencySelectorContent({
    Key? key,
    this.frequencies,
    this.selectedFrequency,
    this.onSelected,
  }) : super(key: key);

  @override
  State<_FrequencySelectorContent> createState() =>
      __FrequencySelectorContentState();
}

class __FrequencySelectorContentState extends State<_FrequencySelectorContent> {
  late List<String> _frequencies;
  late String _selectedFrequency;

  @override
  void initState() {
    super.initState();
    _frequencies = widget.frequencies ?? ['只有一次', '只有一天', '每次'];
    _selectedFrequency = widget.selectedFrequency ?? _frequencies.first;

    // 如果没有传入选中项，默认选中第一项并通知外部
    if (widget.selectedFrequency == null && widget.onSelected != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onSelected!(_selectedFrequency);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: UiConstants.spacing14),
        ..._frequencies.map((frequency) {
          return _buildCheckItem(text: frequency);
        }),

        Padding(
          padding: EdgeInsets.only(
            bottom: ScreenUtil.bottomSafeHeight(context),
          ),
          child: InkWellWidget(
            onTap: () => Navigator.of(context).pop(),
            child: SizedBox(
              width: double.infinity,
              height: 44,
              child: Center(
                child: Text(
                  '取消',
                  style: context.templateStyle.text.descriptionLarge,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCheckItem({required String text}) {
    final bool isSelected = text == _selectedFrequency;
    return InkWell(
      onTap: () {
        if (_selectedFrequency != text) {
          setState(() {
            _selectedFrequency = text;
          });
          if (widget.onSelected != null) {
            widget.onSelected!(text);
          }
        }

        Navigator.of(context).pop();
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: UiConstants.spacing16,
          horizontal: UiConstants.spacing18,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              text,
              style: context.templateStyle.text.bodyLargeMedium.copyWith(
                color:
                    isSelected
                        ? context.templateColors.primary
                        : context.templateColors.textTertiary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
