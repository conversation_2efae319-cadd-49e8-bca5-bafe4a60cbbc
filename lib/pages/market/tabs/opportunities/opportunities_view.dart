/*
  === 机会标签页 ===
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/utils/index.dart';
import './widgets/index.dart';

class OpportunitiesView extends StatefulWidget {
  final ScrollPhysics? physics;

  const OpportunitiesView({super.key, this.physics});

  @override
  State<OpportunitiesView> createState() => _OpportunitiesViewState();
}

class _OpportunitiesViewState extends State<OpportunitiesView> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: widget.physics,
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: _buildContentSections()),
    );
  }

  // 构建内容sections
  List<Widget> _buildContentSections() {
    return [
      // 每日早报
      DailyBriefSection(),

      // AI精选
      AiPicksSection(),

      // 领涨板块
      TopGainersSection(),

      // 交易日历
      TradingCalendarSection(),

      // 行情异动
      MarketMoversSection(),

      // 交易数据
      TradingDataSection(),

      // 全网数据
      NetworkDataSection(),

      // 提示文本
      Padding(
        padding: EdgeInsets.symmetric(
          vertical: UiConstants.spacing20,
          horizontal: UiConstants.spacing32,
        ).copyWith(bottom: ScreenUtil.screenHeight(context) * 0.07),
        child: Text('投资有风险，入市需谨慎。以上内容仅供参考，不构成投资建议。', style: context.templateStyle.text.hintText),
      ),
    ];
  }
}
