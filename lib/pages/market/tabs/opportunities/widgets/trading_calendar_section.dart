/*
  === 交易日历 ===
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';
import '../models/trading_event_model.dart';
import '../../../../../services/trading_events/trading_events_service.dart';

class TradingCalendarSection extends StatefulWidget {
  const TradingCalendarSection({super.key});

  @override
  State<TradingCalendarSection> createState() => _TradingCalendarSectionState();
}

class _TradingCalendarSectionState extends State<TradingCalendarSection> {
  List<TradingEventModel> _events = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadEventData();
  }

  /// 加载交易事件数据
  Future<void> _loadEventData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final response = await TradingEventsService.instance.getEventData();

      if (response != null && mounted) {
        setState(() {
          _events = response.events;
          _isLoading = false;
        });
      } else if (mounted) {
        setState(() {
          _events = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _events = [];
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing16),
      child: Column(children: [_buildHeader(), _isLoading ? _buildLoadingWidget() : _buildCalendarList()]),
    );
  }

  // 加载组件
  Widget _buildLoadingWidget() {
    return SizedBox(height: 200, child: Center(child: CircularProgressIndicator(color: context.templateColors.primary)));
  }

  // 头部
  Widget _buildHeader() {
    return Container(
      margin: EdgeInsets.only(bottom: UiConstants.spacing24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextWidget(text: '交易日历', style: context.templateStyle.text.h4),
          Icon(RemixIcons.arrow_right_s_line, size: 20, color: context.templateColors.textTertiary),
        ],
      ),
    );
  }

  // 日历列表
  Widget _buildCalendarList() {
    if (_events.isEmpty) {
      return SizedBox(height: 100, child: Center(child: TextWidget(text: '暂无交易事件', style: context.templateStyle.text.hintText)));
    }

    return Column(children: _events.map((event) => _buildCalendarItem(event)).toList());
  }

  // 日历项
  Widget _buildCalendarItem(TradingEventModel event) {
    return Container(
      margin: EdgeInsets.only(bottom: UiConstants.spacing16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 时间和日期
          SizedBox(
            width: 60,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(text: event.formattedTime, style: context.templateStyle.text.bodyTextMedium),
                SizedBox(height: UiConstants.spacing4),
                TextWidget(text: event.formattedDate, style: context.templateStyle.text.hintText),
              ],
            ),
          ),
          SizedBox(width: UiConstants.spacing8),
          // 标题和星级
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  text: event.title,
                  style: context.templateStyle.text.bodyTextMedium,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: UiConstants.spacing4),
                _buildStarRating(event.importance),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 星级评分
  Widget _buildStarRating(int importance) {
    return Row(
      children: List.generate(5, (index) {
        return Icon(Icons.star, size: 14, color: index < importance ? Color(0xFFFFB800) : context.templateColors.textTertiary);
      }),
    );
  }
}
