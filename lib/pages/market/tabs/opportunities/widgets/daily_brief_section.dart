/*
  == 每日早报 ==
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class DailyBriefSection extends StatefulWidget {
  const DailyBriefSection({super.key});

  @override
  State<DailyBriefSection> createState() => _DailyBriefSectionState();
}

class _DailyBriefSectionState extends State<DailyBriefSection> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: context.templateColors.divider, width: 1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            text: '今日市场简报：加密货币市场整体表现稳定，主流币种小幅上涨',
            style: context.templateStyle.text.bodyLargeMedium,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: UiConstants.spacing12),
          Row(
            children: [
              Flexible(
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: UiConstants.spacing8,
                    vertical: UiConstants.spacing4,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: context.templateColors.primary),
                    borderRadius: BorderRadius.circular(
                      UiConstants.borderRadius8,
                    ),
                  ),
                  child: Text(
                    '每日简报',
                    style: context.templateStyle.text.tagSecondaryTextMedium
                        .copyWith(color: context.templateColors.primary),
                  ),
                ),
              ),
              SizedBox(width: UiConstants.spacing8),
              Text('06-30', style: context.templateStyle.text.hintText),
            ],
          ),
        ],
      ),
    );
  }
}
