/*
  === 交易数据 ===
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class TradingDataSection extends StatefulWidget {
  const TradingDataSection({super.key});

  @override
  State<TradingDataSection> createState() => _TradingDataSectionState();
}

class _TradingDataSectionState extends State<TradingDataSection>
    with TickerProviderStateMixin {
  int _selectedTabIndex = 0; // 当前选中的标签索引
  late TabController _tabController;
  // 标签数据
  final List<String> _tabs = ['BGB', 'BTC', 'ETH'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    super.dispose();
    _tabController.dispose();
  }

  // 模拟交易数据
  final Map<String, Map<String, dynamic>> _tradingData = {
    'BGB': {
      'spotBuyVolume': '921.8088 BGB',
      'spotSellVolume': '575.0457 BGB',
      'leverageRatio': '23.72',
    },
    'BTC': {
      'spotBuyVolume': '12.5678 BTC',
      'spotSellVolume': '8.9012 BTC',
      'leverageRatio': '45.83',
    },
    'ETH': {
      'spotBuyVolume': '234.5678 ETH',
      'spotSellVolume': '189.0123 ETH',
      'leverageRatio': '31.56',
    },
  };

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing16),
      child: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          SizedBox(height: UiConstants.spacing16),
          _buildDataContent(),
        ],
      ),
    );
  }

  // 标题
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing12),
      child: Row(
        children: [
          TextWidget(text: '交易数据', style: context.templateStyle.text.h4),
          SizedBox(width: UiConstants.spacing4),
          InkWell(
            onTap: () => {},
            child: Icon(
              RemixIcons.information_fill,
              size: 20,
              color: context.templateColors.textTertiary,
            ),
          ),
          Spacer(),
          Icon(
            RemixIcons.arrow_right_s_line,
            size: 20,
            color: context.templateColors.textTertiary,
          ),
        ],
      ),
    );
  }

  // 标签栏
  Widget _buildTabBar() {
    return Container(
      height: 26,
      decoration: BoxDecoration(
        color: context.templateColors.buttonSecondary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
      ),
      child: TabbarWidget(
        height: 24,
        controller: _tabController,
        tabs: _tabs.map((key) => TabItem(title: key)).toList(),
        labelStyle: context.templateStyle.text.bodyTextMedium,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: true,
        indicatorStyle: TabBarIndicatorStyle.filled,
        labelPadding: EdgeInsets.zero,
        tabPadding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
      ),
    );
  }

  // 数据内容
  Widget _buildDataContent() {
    final currentData = _tradingData[_tabs[_selectedTabIndex]]!;

    return Container(
      decoration: BoxDecoration(
        border: Border.all(width: 1, color: context.templateColors.divider),
        borderRadius: BorderRadius.circular(UiConstants.borderRadius12),
      ),
      child: Column(
        children: [
          // 现货大单买入量和卖出量
          Row(
            children: [
              // 买入量
              Expanded(
                child: _buildDataCard(
                  title: '现货大单买入量（15m）',
                  value: currentData['spotBuyVolume'],
                  valueColor: context.templateColors.success,
                ),
              ),
              SizedBox(width: UiConstants.spacing12),
              // 卖出量
              Expanded(
                child: _buildDataCard(
                  title: '现货大单卖出量（15m）',
                  value: currentData['spotSellVolume'],
                  valueColor: context.templateColors.error,
                ),
              ),
            ],
          ),
          // 杠杆多空比
          _buildDataCard(
            title: '杠杆多空比（24h）',
            value: currentData['leverageRatio'],
            valueColor: context.templateColors.textPrimary,
            isFullWidth: true,
          ),
        ],
      ),
    );
  }

  // 数据卡片
  Widget _buildDataCard({
    required String title,
    required String value,
    required Color valueColor,
    bool isFullWidth = false,
  }) {
    return Container(
      width: isFullWidth ? double.infinity : null,
      padding: EdgeInsets.all(UiConstants.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          TextWidget(text: title, style: context.templateStyle.text.hintText),
          SizedBox(height: UiConstants.spacing8),
          // 数值
          TextWidget(
            text: value,
            style: context.templateStyle.text.h4.copyWith(
              color: valueColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
