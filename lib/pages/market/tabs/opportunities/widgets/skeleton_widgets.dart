/*
  === 骨架屏组件 ===
*/

import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:qubic_exchange/core/index.dart';

class SkeletonWidgets {
  // 基础骨架屏容器
  static Widget shimmerContainer({
    required double width,
    required double height,
    double? borderRadius,
  }) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(
            borderRadius ?? UiConstants.borderRadius4,
          ),
        ),
      ),
    );
  }

  // 文本骨架屏
  static Widget textSkeleton({double width = 100, double height = 16}) {
    return shimmerContainer(
      width: width,
      height: height,
      borderRadius: UiConstants.borderRadius4,
    );
  }

  // 每日早报骨架屏
  static Widget dailyBriefSkeleton(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: context.templateColors.divider, width: 1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题骨架屏
          textSkeleton(width: double.infinity, height: 20),
          SizedBox(height: UiConstants.spacing8),
          textSkeleton(
            width: MediaQuery.of(context).size.width * 0.7,
            height: 20,
          ),
          SizedBox(height: UiConstants.spacing12),
          Row(
            children: [
              textSkeleton(width: 100, height: 24),
              SizedBox(width: UiConstants.spacing8),
              textSkeleton(width: 50, height: 16),
            ],
          ),
        ],
      ),
    );
  }

  // AI精选骨架屏
  static Widget aiPicksSkeleton(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题骨架屏
          Row(
            children: [
              textSkeleton(width: 80, height: 24),
              SizedBox(width: UiConstants.spacing4),
              shimmerContainer(width: 20, height: 20),
            ],
          ),
          SizedBox(height: UiConstants.spacing12),
          // AI精选项目骨架屏
          ...List.generate(3, (index) => _aiPickItemSkeleton(context)),
        ],
      ),
    );
  }

  // AI精选项目骨架屏
  static Widget _aiPickItemSkeleton(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: UiConstants.spacing12),
      padding: EdgeInsets.all(UiConstants.spacing16),
      decoration: BoxDecoration(
        border: Border.all(color: context.templateColors.divider),
        borderRadius: BorderRadius.circular(UiConstants.borderRadius20),
      ),
      child: Column(
        children: [
          Row(
            children: [
              textSkeleton(width: 60, height: 20),
              SizedBox(width: UiConstants.spacing8),
              textSkeleton(width: 80, height: 16),
              Spacer(),
              textSkeleton(width: 60, height: 20),
            ],
          ),
          SizedBox(height: UiConstants.spacing12),
          Row(
            children: [
              textSkeleton(width: 100, height: 16),
              Spacer(),
              textSkeleton(width: 80, height: 16),
            ],
          ),
          SizedBox(height: UiConstants.spacing8),
          // 进度条骨架屏
          shimmerContainer(width: double.infinity, height: 8),
        ],
      ),
    );
  }

  // 全网数据骨架屏
  static Widget networkDataSkeleton(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题骨架屏
          Row(
            children: [
              textSkeleton(width: 80, height: 24),
              SizedBox(width: UiConstants.spacing4),
              shimmerContainer(width: 20, height: 20),
              Spacer(),
              shimmerContainer(width: 20, height: 20),
            ],
          ),
          SizedBox(height: UiConstants.spacing16),
          // 数据网格骨架屏
          Container(
            decoration: BoxDecoration(
              border: Border.all(
                width: 1,
                color: context.templateColors.divider,
              ),
              borderRadius: BorderRadius.circular(UiConstants.borderRadius20),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(child: _dataCardSkeleton()),
                    SizedBox(width: UiConstants.spacing12),
                    Expanded(child: _dataCardSkeleton()),
                  ],
                ),
                Row(
                  children: [
                    Expanded(child: _dataCardSkeleton()),
                    SizedBox(width: UiConstants.spacing12),
                    Expanded(child: _dataCardSkeleton()),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 数据卡片骨架屏
  static Widget _dataCardSkeleton() {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          textSkeleton(width: double.infinity, height: 16),
          SizedBox(height: UiConstants.spacing8),
          textSkeleton(width: 100, height: 20),
        ],
      ),
    );
  }

  // 通用section骨架屏
  static Widget sectionSkeleton(BuildContext context, {int itemCount = 3}) {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题骨架屏
          Row(
            children: [
              textSkeleton(width: 100, height: 24),
              SizedBox(width: UiConstants.spacing4),
              shimmerContainer(width: 20, height: 20),
              Spacer(),
              shimmerContainer(width: 20, height: 20),
            ],
          ),
          SizedBox(height: UiConstants.spacing16),
          // 内容骨架屏
          ...List.generate(
            itemCount,
            (index) => Container(
              margin: EdgeInsets.only(bottom: UiConstants.spacing12),
              child: Row(
                children: [
                  textSkeleton(width: 80, height: 20),
                  Spacer(),
                  textSkeleton(width: 60, height: 16),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
