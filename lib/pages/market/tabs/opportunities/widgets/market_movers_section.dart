/*
  === 行情异动 ===
*/

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class MarketMoversSection extends StatefulWidget {
  const MarketMoversSection({super.key});

  @override
  State<MarketMoversSection> createState() => _MarketMoversSectionState();
}

class _MarketMoversSectionState extends State<MarketMoversSection> {
  // 模拟行情异动数据
  final List<Map<String, dynamic>> _moversData = [
    {
      'symbol': 'GFAL',
      'pair': 'USDT',
      'time': '07-02 10:13:04',
      'type': '24小时新高',
      'value': '0.003385',
      'isPositive': true,
    },
    {
      'symbol': 'MBX',
      'pair': 'USDT',
      'time': '07-02 10:12:04',
      'type': '5分钟涨幅',
      'value': '+5.14%',
      'isPositive': true,
    },
    {
      'symbol': 'SAHARA',
      'pair': 'USDT',
      'time': '07-02 10:11:05',
      'type': '24小时新低',
      'value': '0.08048',
      'isPositive': false,
    },
    {
      'symbol': 'DOGS',
      'pair': 'USDT',
      'time': '07-02 10:10:00',
      'type': '快速流入',
      'value': '24.81万',
      'isPositive': null, // 中性
    },
    {
      'symbol': 'TON',
      'pair': 'USDT',
      'time': '07-02 10:10:00',
      'type': '快速流入',
      'value': '8.75',
      'isPositive': null, // 中性
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing16),
      child: Column(
        children: [_buildHeader(), _buildTableHeader(), _buildMoversList()],
      ),
    );
  }

  // 头部
  Widget _buildHeader() {
    return Container(
      margin: EdgeInsets.only(bottom: UiConstants.spacing16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextWidget(text: '市场动态', style: context.templateStyle.text.h4),
          Icon(
            RemixIcons.arrow_right_s_line,
            size: 20,
            color: context.templateColors.textTertiary,
          ),
        ],
      ),
    );
  }

  // 表格头部
  Widget _buildTableHeader() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing8),
      margin: EdgeInsets.only(bottom: UiConstants.spacing8),
      child: Row(
        children: [
          // 币种/时间 列
          Expanded(
            flex: 3,
            child: TextWidget(
              text: '币种/时间',
              style: context.templateStyle.text.descriptionText,
            ),
          ),
          // 类型 列
          Expanded(
            flex: 2,
            child: Center(
              child: TextWidget(
                text: '类型',
                style: context.templateStyle.text.descriptionText,
              ),
            ),
          ),
          // 数据 列
          Expanded(
            flex: 2,
            child: Align(
              alignment: Alignment.centerRight,
              child: TextWidget(
                text: '数据',
                style: context.templateStyle.text.descriptionText,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 异动列表
  Widget _buildMoversList() {
    return Column(
      children:
          _moversData.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return _buildMoverItem(item)
                .animate()
                .fadeIn(
                  duration: 400.ms,
                  delay: Duration(milliseconds: 100 + index * 120),
                )
                .slideX(
                  begin: 0.3,
                  end: 0,
                  duration: 400.ms,
                  delay: Duration(milliseconds: 100 + index * 120),
                );
          }).toList(),
    );
  }

  // 异动项目
  Widget _buildMoverItem(Map<String, dynamic> item) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: context.templateColors.divider.withValues(alpha: 0.3),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // 币种/时间 列
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 币种对
                Row(
                  children: [
                    TextWidget(
                      text: item['symbol'],
                      style: context.templateStyle.text.bodyLargeMedium,
                    ),
                    TextWidget(
                      text: ' / ${item['pair']}',
                      style: context.templateStyle.text.descriptionText,
                    ),
                  ],
                ),
                SizedBox(height: UiConstants.spacing4),
                // 时间
                TextWidget(
                  text: item['time'],
                  style: context.templateStyle.text.hintText.copyWith(
                    color: context.templateColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          // 类型 列
          Expanded(
            flex: 2,
            child: Center(
              child: TextWidget(
                text: item['type'],
                style: context.templateStyle.text.bodyLargeMedium,
                textAlign: TextAlign.center,
              ),
            ),
          ),
          // 数据 列
          Expanded(
            flex: 2,
            child: Align(
              alignment: Alignment.centerRight,
              child: TextWidget(
                text: item['value'],
                style: context.templateStyle.text.bodyLargeMedium.copyWith(
                  color: _getValueColor(item['isPositive']),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 获取数值颜色
  Color _getValueColor(bool? isPositive) {
    if (isPositive == null) {
      return context.templateColors.textPrimary; // 中性颜色
    }
    return isPositive
        ? context.templateColors.tradeBuy
        : context.templateColors.tradeSell;
  }
}
