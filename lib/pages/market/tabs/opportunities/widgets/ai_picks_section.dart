/*
  === AI 精选 ===
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/services/market/ai_recommend_service.dart';
import 'package:remixicon/remixicon.dart';
import '../models/ai_pick_data.dart';
import 'ai_pick_item.dart';

class AiPicksSection extends StatefulWidget {
  const AiPicksSection({super.key});

  @override
  State<AiPicksSection> createState() => _AiPicksSectionState();
}

class _AiPicksSectionState extends State<AiPicksSection> {
  // 展开/收起状态
  bool _isExpanded = false;

  // 默认显示的项目数量
  static const int _defaultItemCount = 3;

  // AI推荐数据
  List<AiPickData> _aiPicksData = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadAiPicksData();
  }

  /// 加载AI推荐数据
  Future<void> _loadAiPicksData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final data = await AiRecommendService.instance.fetchAiRecommendData(limit: 10);
      if (mounted) {
        setState(() {
          _aiPicksData = data;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(padding: EdgeInsets.all(UiConstants.spacing16), child: Column(children: [_buildHeader(), _buildPicksList(context)]));
  }

  // 标题
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing12),
      child: Row(
        children: [
          TextWidget(text: 'AI精选', style: context.templateStyle.text.h4),
          SizedBox(width: UiConstants.spacing4),
          InkWell(onTap: () => {}, child: Icon(RemixIcons.information_fill, size: 20, color: context.templateColors.textTertiary)),
          Spacer(),
          if (!_isLoading)
            InkWell(onTap: _loadAiPicksData, child: Icon(RemixIcons.refresh_line, size: 20, color: context.templateColors.textTertiary)),
        ],
      ),
    );
  }

  // 精选列表
  Widget _buildPicksList(BuildContext context) {
    if (_isLoading) {
      return Container(
        padding: EdgeInsets.all(UiConstants.spacing16),
        child: Center(child: CircularProgressIndicator(color: context.templateColors.primary)),
      );
    }

    if (_aiPicksData.isEmpty) {
      return Container(
        padding: EdgeInsets.all(UiConstants.spacing16),
        child: Center(
          child: TextWidget(
            text: '暂无AI推荐数据',
            style: context.templateStyle.text.bodyText.copyWith(color: context.templateColors.textTertiary),
          ),
        ),
      );
    }

    // 根据展开状态决定显示的数据
    final displayData = _isExpanded ? _aiPicksData : _aiPicksData.take(_defaultItemCount).toList();

    return Column(
      children: [
        // 精选项目列表
        ...displayData.asMap().entries.map((entry) {
          final data = entry.value;
          return AiPickItem(data: data);
        }),
        // 展开/收起按钮
        if (_aiPicksData.length > _defaultItemCount) _buildExpandButton(),
      ],
    );
  }

  // 展开/收起按钮
  Widget _buildExpandButton() {
    return Container(
      margin: EdgeInsets.only(top: UiConstants.spacing8),
      child: InkWell(
        onTap: () {
          setState(() {
            _isExpanded = !_isExpanded;
          });
        },
        borderRadius: BorderRadius.circular(UiConstants.borderRadius4),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: UiConstants.spacing8, horizontal: UiConstants.spacing16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              AnimatedRotation(
                turns: _isExpanded ? 0.5 : 0,
                duration: const Duration(milliseconds: 300),
                child: Icon(Icons.keyboard_arrow_down, size: 20, color: context.templateColors.textTertiary),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
