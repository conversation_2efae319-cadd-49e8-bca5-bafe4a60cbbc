/*
  === 领涨板块 ===
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/services/market/market_service.dart';

import 'package:qubic_exchange/l10n/managers/language_manager.dart';
import 'package:qubic_exchange/utils/index.dart';
import 'package:remixicon/remixicon.dart';

class TopGainersSection extends StatefulWidget {
  const TopGainersSection({super.key});

  @override
  State<TopGainersSection> createState() => _TopGainersSectionState();
}

class _TopGainersSectionState extends State<TopGainersSection> {
  // 领涨板块数据
  List<Map<String, dynamic>> _gainersData = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadTopGainersData();
  }

  /// 加载领涨板块数据
  Future<void> _loadTopGainersData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final data = await _calculateTopGainers();
      if (mounted) {
        setState(() {
          _gainersData = data;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 计算领涨板块数据
  Future<List<Map<String, dynamic>>> _calculateTopGainers() async {
    try {
      final List<Map<String, dynamic>> topGainers = [];

      // 遍历每个栏目 - 使用新的模型数据
      for (final category in MarketService.instance.categoryModels) {
        final categoryId = category.id;

        // 获取当前语言的栏目名称
        final currentLocale = LanguageManager().currentLocale;
        String targetLang = 'zh_cn';

        if (currentLocale.languageCode == 'en') {
          targetLang = 'en';
        } else if (currentLocale.countryCode == 'TW') {
          targetLang = 'zh_tw';
        }

        final categoryName = category.getName(targetLang: targetLang, fallbackLang: 'en');
        if (categoryName.isEmpty) {
          continue;
        }

        // 获取该栏目下的币种
        final categoryCurrencies =
            MarketService.instance.currencyModels.where((currency) {
              return currency.cateIds.contains(categoryId);
            }).toList();

        if (categoryCurrencies.isEmpty) {
          continue;
        }

        // 计算栏目平均涨跌幅和找到涨幅最大的币种
        double totalChangePercentage = 0.0;
        int validCount = 0;
        double maxChange = double.negativeInfinity;
        String topSymbol = '';
        double topSymbolChange = 0.0;

        for (final currency in categoryCurrencies) {
          final baseAsset = currency.baseAsset;
          final tickerData = currency.tickers['1']; // 现货市场数据

          if (tickerData != null) {
            final changePercentage = tickerData.priceChangeP;


            totalChangePercentage += changePercentage;
            validCount++;

            // 找到涨幅最大的币种
            if (changePercentage > maxChange) {
              maxChange = changePercentage;
              topSymbol = baseAsset;
              topSymbolChange = changePercentage;
            }
          }
        }

        if (validCount > 0) {
          final avgChangePercentage = totalChangePercentage / validCount;
          topGainers.add({
            'name': categoryName,
            'price': NumberFormatUtil.formatChangePercentage(avgChangePercentage),
            'symbol': topSymbol,
            'change': NumberFormatUtil.formatChangePercentage(topSymbolChange),
            'avgChange': avgChangePercentage,
          });
        }
      }

      // 按平均涨跌幅排序，取前6个
      topGainers.sort((a, b) => (b['avgChange'] as double).compareTo(a['avgChange'] as double));
      final result = topGainers.take(6).toList();

      return result;
    } catch (e) {
      debugPrint('💥 计算领涨板块数据异常: $e');
      return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(padding: EdgeInsets.all(UiConstants.spacing16), child: Column(children: [_buildHeader(), _buildGridList()]));
  }

  // 头部
  Widget _buildHeader() {
    return Container(
      margin: EdgeInsets.only(bottom: UiConstants.spacing16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextWidget(text: '涨幅榜', style: context.templateStyle.text.h4),
          Row(
            children: [
              if (!_isLoading)
                InkWell(
                  onTap: _loadTopGainersData,
                  child: Icon(RemixIcons.refresh_line, size: 20, color: context.templateColors.textTertiary),
                ),
              SizedBox(width: UiConstants.spacing8),
              Icon(RemixIcons.arrow_right_s_line, size: 20, color: context.templateColors.textTertiary),
            ],
          ),
        ],
      ),
    );
  }

  // 网格列表
  Widget _buildGridList() {
    if (_isLoading) {
      return Container(
        padding: EdgeInsets.all(UiConstants.spacing16),
        child: Center(child: CircularProgressIndicator(color: context.templateColors.primary)),
      );
    }

    if (_gainersData.isEmpty) {
      return Container(
        padding: EdgeInsets.all(UiConstants.spacing16),
        child: Center(
          child: TextWidget(
            text: '暂无涨幅榜数据',
            style: context.templateStyle.text.bodyText.copyWith(color: context.templateColors.textTertiary),
          ),
        ),
      );
    }

    return Wrap(
      spacing: UiConstants.spacing12, // 水平间距
      runSpacing: UiConstants.spacing12, // 垂直间距
      children:
          _gainersData.asMap().entries.map((entry) {
            final data = entry.value;
            return _buildGridItem(data);
          }).toList(),
    );
  }

  // 网格项目
  Widget _buildGridItem(Map<String, dynamic> data) {
    return SizedBox(
      width: (MediaQuery.of(context).size.width - UiConstants.spacing16 * 2 - UiConstants.spacing12 * 2) / 3,
      child: _buildItem(data),
    );
  }

  // 列表项
  Widget _buildItem(Map<String, dynamic> data) {
    final isPositive = !data['price'].toString().startsWith('-');

    return Container(
      padding: EdgeInsets.all(UiConstants.spacing12),
      decoration: BoxDecoration(
        color: context.templateColors.surface,
        borderRadius: BorderRadius.circular(UiConstants.borderRadius14),
        border: Border.all(color: context.templateColors.divider, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 板块名称
          Text(data['name'], style: context.templateStyle.text.bodyTextMedium, maxLines: 1, overflow: TextOverflow.ellipsis),
          SizedBox(height: UiConstants.spacing16),
          // 价格变化
          Text(
            data['price'],
            style: context.templateStyle.text.bodyLargeMedium.copyWith(
              color: isPositive ? context.templateColors.tradeBuy : context.templateColors.tradeSell,
            ),
          ),
          SizedBox(height: UiConstants.spacing8),
          // 底部信息
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(data['symbol'], style: context.templateStyle.text.hintText),
              SizedBox(width: UiConstants.spacing8),
              Text(
                data['change'],
                style: context.templateStyle.text.hintText.copyWith(
                  color: data['change'].toString().startsWith('+') ? context.templateColors.tradeBuy : context.templateColors.tradeSell,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
