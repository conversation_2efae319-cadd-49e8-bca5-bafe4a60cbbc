/*
  AI精选列表项组件
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/utils/index.dart';
import '../models/ai_pick_data.dart';

class AiPickItem extends StatefulWidget {
  final AiPickData data;
  final VoidCallback? onTap;
  final EdgeInsets? margin;
  final EdgeInsets? padding;

  const AiPickItem({super.key, required this.data, this.onTap, this.margin, this.padding});

  @override
  State<AiPickItem> createState() => _AiPickItemState();
}

class _AiPickItemState extends State<AiPickItem> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin ?? EdgeInsets.only(bottom: UiConstants.spacing12),
      padding: widget.padding ?? EdgeInsets.all(UiConstants.spacing16),
      decoration: BoxDecoration(
        color: context.templateColors.surface,
        borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
        border: Border.all(color: context.templateColors.divider, width: 1),
      ),
      child: InkWellWidget(
        onTap: widget.onTap,
        child: Column(children: [_buildHeader(), SizedBox(height: UiConstants.spacing12), _buildRatioBar()]),
      ),
    );
  }

  /// 构建头部信息
  Widget _buildHeader() {
    return Row(
      children: [
        // 左侧：币种图标和信息
        Expanded(
          child: Row(
            children: [
              // 币种图标
              Container(
                margin: EdgeInsets.only(right: UiConstants.spacing8),
                child:
                    widget.data.logoUrl != null && widget.data.logoUrl!.isNotEmpty
                        ? ThemedImage.network(
                          widget.data.logoUrl!,
                          width: 26,
                          height: 26,
                          borderRadius: BorderRadius.circular(13),
                          placeholder: ThemedImage.asset(
                            widget.data.symbol.toLowerCase(),
                            width: 26,
                            height: 26,
                            folder: ThemedAssetFolder.crypto,
                          ),
                          errorWidget: ThemedImage.asset(
                            widget.data.symbol.toLowerCase(),
                            width: 26,
                            height: 26,
                            folder: ThemedAssetFolder.crypto,
                          ),
                        )
                        : ThemedImage.asset(widget.data.symbol.toLowerCase(), width: 26, height: 26, folder: ThemedAssetFolder.crypto),
              ),
              // 币种信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 币种名称和标签
                    Text(widget.data.symbol, style: context.templateStyle.text.bodyLargeMedium),
                    SizedBox(height: UiConstants.spacing2),
                    Text(widget.data.tag, style: context.templateStyle.text.hintTextMedium),
                  ],
                ),
              ),
            ],
          ),
        ),
        // 右侧：价格和涨跌幅
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            // 涨跌幅
            Text(
              NumberFormatUtil.formatChangePercentage(widget.data.changePercentage),
              style: context.templateStyle.text.bodyLargeMedium.copyWith(
                color: widget.data.isPriceIncreasing ? context.templateColors.success : context.templateColors.error,
              ),
            ),
            SizedBox(height: UiConstants.spacing4),
            // 价格
            Text(
              NumberFormatUtil.formatWithComma(widget.data.currentPrice),
              style: context.templateStyle.text.bodyTextMedium.copyWith(color: context.templateColors.textTertiary),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建比例条
  Widget _buildRatioBar() {
    return RatioProgressBar(
      bullishRatio: widget.data.bullishRatio,
      bearishRatio: widget.data.bearishRatio,
      height: 4,
      borderRadius: 1,
      showLabels: true,
      showPercentage: true,
    );
  }
}
