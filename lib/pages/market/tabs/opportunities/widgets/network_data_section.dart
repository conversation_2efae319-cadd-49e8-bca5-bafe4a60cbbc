/*
  === 全网数据 ===
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class NetworkDataSection extends StatefulWidget {
  const NetworkDataSection({super.key});

  @override
  State<NetworkDataSection> createState() => _NetworkDataSectionState();
}

class _NetworkDataSectionState extends State<NetworkDataSection> {
  // 模拟全网数据
  final Map<String, dynamic> _networkData = {
    'globalMarketCap': '¥ 23.27万亿',
    'globalVolume24h': '¥ 6,949.12亿',
    'btcDominance': '64.66%',
    'openInterest': '¥ 4.98万亿',
  };

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing16),
      child: Column(children: [_buildHeader(), _buildDataGrid()]),
    );
  }

  // 标题
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing16),
      child: Row(
        children: [
          TextWidget(text: '网络数据', style: context.templateStyle.text.h4),
          SizedBox(width: UiConstants.spacing4),
          InkWell(
            onTap: () => {},
            child: Icon(
              RemixIcons.information_fill,
              size: 20,
              color: context.templateColors.textTertiary,
            ),
          ),
          Spacer(),
          Icon(
            RemixIcons.arrow_right_s_line,
            size: 20,
            color: context.templateColors.textTertiary,
          ),
        ],
      ),
    );
  }

  // 数据网格
  Widget _buildDataGrid() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(width: 1, color: context.templateColors.divider),
        borderRadius: BorderRadius.circular(UiConstants.borderRadius12),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildDataCard(
                  title: '全球市值',
                  value: _networkData['globalMarketCap'],
                ),
              ),
              SizedBox(width: UiConstants.spacing12),
              Expanded(
                child: _buildDataCard(
                  title: '24h成交量',
                  value: _networkData['globalVolume24h'],
                ),
              ),
            ],
          ),
          Row(
            children: [
              Expanded(
                child: _buildDataCard(
                  title: 'BTC占比',
                  value: _networkData['btcDominance'],
                ),
              ),
              SizedBox(width: UiConstants.spacing12),
              Expanded(
                child: _buildDataCard(
                  title: '持仓量',
                  value: _networkData['openInterest'],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 数据卡片
  Widget _buildDataCard({required String title, required String value}) {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          TextWidget(
            text: title,
            style: context.templateStyle.text.hintText,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: UiConstants.spacing8),
          // 数值
          TextWidget(
            text: value,
            style: context.templateStyle.text.h4.copyWith(
              color: context.templateColors.textPrimary,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
