/*
  AI精选数据模型
*/

class AiPickData {
  final String symbol; // 币种符号
  final String tag; // 标签（如"近期高点突破"）
  final double changePercentage; // 涨跌幅
  final double currentPrice; // 当前价格
  final double bullishRatio; // 看多比例
  final double bearishRatio; // 看空比例
  final String? logoUrl; // 币种logo
  final String sentimentSummary; // 情绪摘要

  const AiPickData({
    required this.symbol,
    required this.tag,
    required this.changePercentage,
    required this.currentPrice,
    required this.bullishRatio,
    required this.bearishRatio,
    this.logoUrl,
    this.sentimentSummary = '',
  });

  /// 是否为正涨幅
  bool get isPriceIncreasing => changePercentage > 0;

  /// 是否为平盘
  bool get isPriceFlat => changePercentage == 0;

  /// 数据验证
  void _validateData() {
    if (symbol.isEmpty) throw ArgumentError('币种符号不能为空');
    if (tag.isEmpty) throw ArgumentError('标签不能为空');
    if (currentPrice < 0) throw ArgumentError('价格不能为负数');
    if (bullishRatio < 0 || bullishRatio > 100) throw ArgumentError('看多比例必须在0-100之间');
    if (bearishRatio < 0 || bearishRatio > 100) throw ArgumentError('看空比例必须在0-100之间');
    if ((bullishRatio + bearishRatio - 100).abs() > 0.01) {
      throw ArgumentError('看多看空比例之和必须等于100%');
    }
  }

  /// 工厂构造函数，带数据验证
  factory AiPickData.validated({
    required String symbol,
    required String tag,
    required double changePercentage,
    required double currentPrice,
    required double bullishRatio,
    required double bearishRatio,
    String? logoUrl,
    String sentimentSummary = '',
  }) {
    final data = AiPickData(
      symbol: symbol,
      tag: tag,
      changePercentage: changePercentage,
      currentPrice: currentPrice,
      bullishRatio: bullishRatio,
      bearishRatio: bearishRatio,
      logoUrl: logoUrl,
      sentimentSummary: sentimentSummary,
    );
    data._validateData();
    return data;
  }

  /// 从JSON创建实例
  factory AiPickData.fromJson(Map<String, dynamic> json) {
    return AiPickData(
      symbol: json['symbol'] ?? '',
      tag: json['tag'] ?? '',
      changePercentage: (json['changePercentage'] ?? 0.0).toDouble(),
      currentPrice: (json['currentPrice'] ?? 0.0).toDouble(),
      bullishRatio: (json['bullishRatio'] ?? 0.0).toDouble(),
      bearishRatio: (json['bearishRatio'] ?? 0.0).toDouble(),
      logoUrl: json['logoUrl'],
      sentimentSummary: json['sentimentSummary'] ?? '',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'tag': tag,
      'changePercentage': changePercentage,
      'currentPrice': currentPrice,
      'bullishRatio': bullishRatio,
      'bearishRatio': bearishRatio,
      'logoUrl': logoUrl,
      'sentimentSummary': sentimentSummary,
    };
  }

  @override
  String toString() {
    return 'AiPickData(symbol: $symbol, tag: $tag, changePercentage: $changePercentage%, currentPrice: $currentPrice, bullishRatio: $bullishRatio%, bearishRatio: $bearishRatio%)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AiPickData &&
        other.symbol == symbol &&
        other.tag == tag &&
        other.changePercentage == changePercentage &&
        other.currentPrice == currentPrice &&
        other.bullishRatio == bullishRatio &&
        other.bearishRatio == bearishRatio;
  }

  @override
  int get hashCode {
    return symbol.hashCode ^
        tag.hashCode ^
        changePercentage.hashCode ^
        currentPrice.hashCode ^
        bullishRatio.hashCode ^
        bearishRatio.hashCode;
  }
}
