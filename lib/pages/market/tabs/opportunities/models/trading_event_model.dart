/*
  交易事件数据模型
*/

class TradingEventModel {
  final int id;
  final int sourceId;
  final String title;
  final String content;
  final String country;
  final int importance;
  final double previous;
  final String pubTime;
  final String unit;
  final double actual;
  final double consensus;
  final String impact;
  final String paraphrase;
  final String createdAt;
  final String updatedAt;

  TradingEventModel({
    required this.id,
    required this.sourceId,
    required this.title,
    required this.content,
    required this.country,
    required this.importance,
    required this.previous,
    required this.pubTime,
    required this.unit,
    required this.actual,
    required this.consensus,
    required this.impact,
    required this.paraphrase,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TradingEventModel.fromJson(Map<String, dynamic> json) {
    return TradingEventModel(
      id: json['id'] ?? 0,
      sourceId: json['source_id'] ?? 0,
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      country: json['country'] ?? '',
      importance: json['importance'] ?? 0,
      previous: (json['previous'] as num?)?.toDouble() ?? 0.0,
      pubTime: json['pub_time'] ?? '',
      unit: json['unit'] ?? '',
      actual: (json['actual'] as num?)?.toDouble() ?? 0.0,
      consensus: (json['consensus'] as num?)?.toDouble() ?? 0.0,
      impact: json['impact'] ?? '',
      paraphrase: json['paraphrase'] ?? '',
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'source_id': sourceId,
      'title': title,
      'content': content,
      'country': country,
      'importance': importance,
      'previous': previous,
      'pub_time': pubTime,
      'unit': unit,
      'actual': actual,
      'consensus': consensus,
      'impact': impact,
      'paraphrase': paraphrase,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  /// 获取格式化的发布时间（HH:mm）
  String get formattedTime {
    try {
      final dateTime = DateTime.parse(pubTime);
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return '';
    }
  }

  /// 获取格式化的日期（MM-dd）
  String get formattedDate {
    try {
      final dateTime = DateTime.parse(pubTime);
      return '${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
    } catch (e) {
      return '';
    }
  }
}

class TradingEventResponse {
  final String date;
  final int count;
  final List<TradingEventModel> events;

  TradingEventResponse({
    required this.date,
    required this.count,
    required this.events,
  });

  factory TradingEventResponse.fromJson(Map<String, dynamic> json) {
    final eventsList = json['events'] as List<dynamic>? ?? [];
    final events = eventsList.map((e) => TradingEventModel.fromJson(e as Map<String, dynamic>)).toList();

    return TradingEventResponse(
      date: json['date'] ?? '',
      count: json['count'] ?? 0,
      events: events,
    );
  }
}
