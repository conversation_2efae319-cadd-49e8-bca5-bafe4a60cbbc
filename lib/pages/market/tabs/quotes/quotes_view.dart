/*
*  市场标签页
*/

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/services/market/index.dart';
import 'package:qubic_exchange/services/network/websocket_service.dart';

import '../../widgets/market_crypto_list.dart';
import '../../models/crypto_item_data.dart';

class QuotesView extends StatefulWidget {
  final ScrollPhysics? physics;
  const QuotesView({super.key, this.physics});

  @override
  State<QuotesView> createState() => _QuotesViewState();
}

class _QuotesViewState extends State<QuotesView> with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  // 标签控制器
  late TabController _mainController;
  late TabController _subController;

  // 市场类型和币种栏目数据
  List<MarketTypeModel> _marketTypes = [];
  List<Map<String, dynamic>> _categories = [];

  // 币种数据存储 - 按市场类型分组 {marketTypeId: {currencyId: CryptoItemData}}
  final Map<String, Map<int, CryptoItemData>> _marketCryptoData = {};

  // 当前选中的市场类型和栏目
  String _currentMarketTypeId = '';
  String _currentCategoryId = '';
  // WebSocket相关
  StreamSubscription<Map<String, dynamic>>? _tickerSubscription;
  Timer? _updateTimer;
  Set<int> _pendingUpdates = {};
  static const Duration _updateInterval = Duration(milliseconds: 150);

  // 性能优化：限制更新频率
  DateTime _lastUpdateTime = DateTime.now();
  static const Duration _minUpdateInterval = Duration(milliseconds: 100);

  // 初始化状态
  bool _isInitialized = false;

  // 数据加载状态标记
  bool _isDataLoaded = false;

  @override
  void initState() {
    super.initState();
    _initializeMarketData();
  }

  @override
  void dispose() {
    _updateTimer?.cancel();
    _tickerSubscription?.cancel();
    _mainController.dispose();
    _subController.dispose();
    super.dispose();
  }

  /// 初始化市场数据
  Future<void> _initializeMarketData() async {
    // 如果数据已经加载过，直接返回
    if (_isDataLoaded) {
      setState(() {
        _isInitialized = true;
      });
      return;
    }

    try {
      // 检查MarketService是否已初始化
      if (!MarketService.instance.isInitialized) {
        await MarketService.instance.initialize();
      }

      // 如果栏目数据为空，尝试获取
      if (MarketService.instance.categoryModels.isEmpty) {
        await MarketService.instance.fetchCurrencyCateData();
      }

      // 如果市场类型数据为空，尝试获取
      if (MarketService.instance.marketTypeModels.isEmpty) {
        await MarketService.instance.fetchMarketTypeData();
      }

      // 获取市场类型模型数据
      _marketTypes = MarketService.instance.marketTypeModels;

      // 获取币种栏目数据，第一个固定为"全部"
      _categories = [
        {'id': 'all', 'name': '全部'},
      ];

      final categoryModels = MarketService.instance.categoryModels;

      // 处理多语言栏目数据
      for (final categoryModel in categoryModels) {
        final categoryName = categoryModel.getName();
        if (categoryName.isNotEmpty) {
          _categories.add({'id': categoryModel.id.toString(), 'name': categoryName});
        }
      }

      // 初始化TabController
      if (_marketTypes.isNotEmpty && _categories.isNotEmpty) {
        _mainController = TabController(length: _marketTypes.length, vsync: this);
        _subController = TabController(length: _categories.length, vsync: this);

        // 设置初始选中的市场类型和栏目
        _currentMarketTypeId = _marketTypes[0].id.toString();
        _currentCategoryId = 'all';

        // 添加标签切换监听器
        _mainController.addListener(() {
          if (!_mainController.indexIsChanging) {
            final newMarketTypeId = _marketTypes[_mainController.index].id.toString();
            _onMarketTypeChanged(newMarketTypeId);
          }
        });

        _subController.addListener(() {
          if (!_subController.indexIsChanging) {
            final newCategoryId = _categories[_subController.index]['id'].toString();
            _onCategoryChanged(newCategoryId);
          }
        });

        // 加载初始数据
        await _loadCryptoDataForCurrentMarket();

        // 订阅WebSocket ticker数据
        _subscribeToTickers();

        // 标记数据已加载
        _isDataLoaded = true;

        setState(() {
          _isInitialized = true;
        });
      }
    } catch (e) {
      debugPrint('初始化市场数据失败: $e');
      setState(() {
        _isInitialized = true;
      });
    }
  }

  /// 加载当前市场的币种数据
  Future<void> _loadCryptoDataForCurrentMarket() async {
    try {
      // 获取所有币种模型数据
      final allCurrencies = MarketService.instance.currencyModels;

      // 根据市场类型ID应用不同的过滤规则
      List<CurrencyModel> currentMarketCurrencies;

      switch (_currentMarketTypeId) {
        case '1': // 现货市场：使用market_type=1的数据
          currentMarketCurrencies =
              allCurrencies.where((currency) {
                return currency.marketType == 1;
              }).toList();
          break;

        case '5': // 合约市场：使用market_type=1且is_marginTrade=1的数据
          currentMarketCurrencies =
              allCurrencies.where((currency) {
                return currency.marketType == 1 && currency.isMarginTrade == 1;
              }).toList();
          break;

        case '6': // 杠杆市场：直接使用market_type=1的数据
          currentMarketCurrencies =
              allCurrencies.where((currency) {
                return currency.marketType == 1;
              }).toList();
          break;

        default: // 其他市场类型：按market_type匹配
          final targetMarketType = int.tryParse(_currentMarketTypeId) ?? 1;
          currentMarketCurrencies =
              allCurrencies.where((currency) {
                return currency.marketType == targetMarketType;
              }).toList();
          break;
      }

      // 转换为CryptoItemData格式并存储
      final dataMap = <int, CryptoItemData>{};
      for (final currency in currentMarketCurrencies) {
        final cryptoData = CryptoItemData(
          symbol: currency.baseAsset,
          quoteCurrency: currency.quoteAsset,
          currentPrice: 0.0, // 将通过ticker数据更新
          cnyPrice: 0.0, // 将通过ticker数据更新
          changePercentage: 0.0, // 将通过ticker数据更新
          volume24h: 0.0, // 将通过ticker数据更新
          volumeUnit: '万',
          precision: currency.mPricePrecision,
        );
        dataMap[currency.id] = cryptoData;
      }

      _marketCryptoData[_currentMarketTypeId] = dataMap;
    } catch (e) {
      debugPrint('加载币种数据失败: $e');
    }
  }

  /// 市场类型切换处理
  void _onMarketTypeChanged(String newMarketTypeId) async {
    setState(() {
      _currentMarketTypeId = newMarketTypeId;
      _pendingUpdates.clear();
    });

    // 如果新市场没有数据，加载数据
    if (!_marketCryptoData.containsKey(newMarketTypeId)) {
      await _loadCryptoDataForCurrentMarket();
      if (mounted) {
        setState(() {});
      }
    }
  }

  /// 栏目切换处理
  void _onCategoryChanged(String newCategoryId) {
    setState(() {
      _currentCategoryId = newCategoryId;
      _pendingUpdates.clear();
    });
  }

  /// 订阅ticker数据
  void _subscribeToTickers() {
    // 订阅market_type 1的ticker数据
    final subscribeMessage1 = {"action": "subscribe", "type": "ticker", "market_type": 1};

    // 订阅market_type 5的ticker数据
    final subscribeMessage5 = {"action": "subscribe", "type": "ticker", "market_type": 5};

    // 发送订阅消息
    WebSocketService.instance.sendMessage(subscribeMessage1);
    WebSocketService.instance.sendMessage(subscribeMessage5);

    // 监听ticker消息
    _tickerSubscription = WebSocketService.instance.subscribe('ticker', {}).listen(_handleTickerData);
  }

  /// 处理ticker数据
  void _handleTickerData(Map<String, dynamic> message) {
    if (message['type'] == 'ticker' && message['data'] != null) {
      final int marketType = message['market_type'] ?? 0;
      final List<dynamic> tickerList = message['data'];

      // 根据market_type确定对应的市场ID列表
      List<String> targetMarketIds = _getMarketIdsByType(marketType);

      // 立即处理当前显示的市场数据
      _handleCurrentMarketData(targetMarketIds, tickerList);

      // 异步处理其他市场数据
      _handleOtherMarketsDataAsync(targetMarketIds, tickerList);
    }
  }

  /// 立即处理当前显示的市场数据
  void _handleCurrentMarketData(List<String> targetMarketIds, List<dynamic> tickerList) {
    bool shouldUpdateUI = false;

    for (String marketId in targetMarketIds) {
      // 只处理当前显示的市场
      if (marketId == _currentMarketTypeId && _marketCryptoData.containsKey(marketId)) {
        final marketData = _marketCryptoData[marketId]!;

        for (final ticker in tickerList) {
          final currencyId = ticker['currency_id'] as int;

          if (marketData.containsKey(currencyId)) {
            _updateCryptoData(marketId, currencyId, ticker);
            _pendingUpdates.add(currencyId);
            shouldUpdateUI = true;
          }
        }
      }
    }

    // 立即更新UI
    if (shouldUpdateUI) {
      _scheduleImmediateUpdate();
    }
  }

  /// 异步处理其他市场数据
  void _handleOtherMarketsDataAsync(List<String> targetMarketIds, List<dynamic> tickerList) {
    // 使用microtask异步处理，不阻塞当前UI更新
    Future.microtask(() {
      // 性能优化：限制更新频率
      final now = DateTime.now();
      if (now.difference(_lastUpdateTime) < _minUpdateInterval) {
        return;
      }
      _lastUpdateTime = now;

      for (String marketId in targetMarketIds) {
        // 只处理非当前显示的市场
        if (marketId != _currentMarketTypeId && _marketCryptoData.containsKey(marketId)) {
          final marketData = _marketCryptoData[marketId]!;

          for (final ticker in tickerList) {
            final currencyId = ticker['currency_id'] as int;

            if (marketData.containsKey(currencyId)) {
              _updateCryptoData(marketId, currencyId, ticker);
            }
          }
        }
      }
    });
  }

  /// 根据market_type获取市场ID列表
  List<String> _getMarketIdsByType(int marketType) {
    switch (marketType) {
      case 1:
        return ['1', '6']; // 现货市场和杠杆市场使用market_type=1的数据
      case 5:
        return ['5']; // 合约市场使用market_type=5的数据
      default:
        return [];
    }
  }

  /// 更新单个币种数据
  void _updateCryptoData(String marketId, int currencyId, Map<String, dynamic> ticker) {
    final marketData = _marketCryptoData[marketId];
    final existingData = marketData?[currencyId];

    if (existingData != null) {
      // 获取价格数据
      final lastPrice = (ticker['last_price'] as num?)?.toDouble() ?? 0.0;
      final openPrice = (ticker['open_price'] as num?)?.toDouble() ?? 0.0;
      final volume = (ticker['volume'] as num?)?.toDouble() ?? 0.0;

      // 性能优化：只有数据真正变化时才更新
      if (existingData.currentPrice == lastPrice && existingData.volume24h == volume) {
        return; // 数据没有变化，跳过更新
      }

      // 计算涨跌幅：如果后端的price_changeP为0，则自己计算
      double changePercentage = (ticker['price_changeP'] as num?)?.toDouble() ?? 0.0;
      if (changePercentage == 0.0 && openPrice > 0) {
        changePercentage = ((lastPrice - openPrice) / openPrice) * 100;
      }

      marketData![currencyId] = existingData.copyWith(
        currentPrice: lastPrice,
        changePercentage: changePercentage,
        volume24h: volume,
        lastUpdated: DateTime.now(),
      );
    }
  }

  /// 立即更新UI（用于当前显示的tab）
  void _scheduleImmediateUpdate() {
    _updateTimer?.cancel();
    if (_pendingUpdates.isNotEmpty && mounted) {
      setState(() {
        _pendingUpdates.clear();
      });
    }
  }

  /// 批量更新UI（用于延迟更新）
  void _scheduleBatchUpdate() {
    _updateTimer?.cancel();
    _updateTimer = Timer(_updateInterval, () {
      if (_pendingUpdates.isNotEmpty && mounted) {
        setState(() {
          _pendingUpdates.clear();
        });
      }
    });
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    if (!_isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // 主标签栏
        Container(
          decoration: BoxDecoration(
            color: context.templateColors.surface,
            border: Border(bottom: BorderSide(width: 0.5, color: context.templateColors.divider)),
          ),
          child: _buildTabbar(),
        ),

        // 主标签内容 - 占据剩余空间
        Expanded(
          child: TabBarView(
            controller: _mainController,
            children:
                _marketTypes.map((marketType) {
                  return _buildMainTabContent(marketType);
                }).toList(),
          ),
        ),
      ],
    );
  }

  // 构建主标签栏
  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: TabbarWidget(
        height: 38,
        tabs: _marketTypes.map((marketType) => TabItem(title: marketType.name)).toList(),
        controller: _mainController,
        labelStyle: context.templateStyle.text.bodyTextMedium,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: true,
        indicatorSize: TabBarIndicatorSize.label,
        labelPadding: EdgeInsets.only(right: UiConstants.spacing18),
      ),
    );
  }

  // 构建子标签栏
  Widget _buildSubTabbar() {
    return Container(
      padding: EdgeInsets.only(
        left: UiConstants.spacing16,
        right: UiConstants.spacing16,
        top: UiConstants.spacing16,
        bottom: UiConstants.spacing10,
      ),
      decoration: BoxDecoration(color: context.templateColors.surface),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 子标签栏
          Expanded(
            child: TabbarWidget(
              height: 24,
              controller: _subController,
              tabs: _categories.map((category) => TabItem(title: category['name'] ?? '')).toList(),
              labelStyle: context.templateStyle.text.hintTextMedium,
              selectedColor: context.templateColors.textPrimary,
              unselectedColor: context.templateColors.textSecondary,
              showIndicator: true,
              indicatorStyle: TabBarIndicatorStyle.filled,
              labelPadding: EdgeInsets.zero,
              tabPadding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
            ),
          ),
          SizedBox(width: UiConstants.spacing14),
          // 类型下拉
          InkWellWidget(
            child: Padding(
              padding: EdgeInsets.only(bottom: UiConstants.spacing4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text('全部', style: context.templateStyle.text.hintTextMedium.copyWith(color: context.templateColors.textPrimary)),
                  SizedBox(width: UiConstants.spacing4),
                  ThemedImage.asset('arrow_triangle_down', width: 14, height: 14, followTheme: true),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建主标签内容
  Widget _buildMainTabContent(MarketTypeModel marketType) {
    return Column(
      children: [
        // 子标签栏
        Container(decoration: BoxDecoration(color: context.templateColors.surface), child: _buildSubTabbar()),

        // 子标签内容 - 占据剩余空间
        Flexible(
          child: TabBarView(
            controller: _subController,
            children:
                _categories.map((category) {
                  return _buildSubTabContent(marketType, category);
                }).toList(),
          ),
        ),
      ],
    );
  }

  // 构建子标签内容
  Widget _buildSubTabContent(MarketTypeModel marketType, Map<String, dynamic> category) {
    final cryptoData = _getCryptoDataForTab(marketType, category);
    final isContract = marketType.id == 5; // 合约市场
    final isNewListing = category['id'] == 'all'; // "全部"标签显示新币卡片

    // 如果没有数据，显示空状态
    if (cryptoData.isEmpty) {
      return _buildEmptyState();
    }

    return MarketCryptoList(cryptoDataList: cryptoData, isContract: isContract, isNewListing: isNewListing, physics: widget.physics);
  }

  /// 构建空状态视图
  Widget _buildEmptyState() {
    return EmptyWidget(text: '当前市场暂无可交易的币种', imageName: 'noData');
  }

  /// 获取指定市场类型和栏目的币种数据
  List<CryptoItemData> _getCryptoDataForTab(MarketTypeModel marketType, Map<String, dynamic> category) {
    final marketTypeId = marketType.id.toString();
    final categoryId = category['id'].toString();

    // 获取当前市场的所有币种数据
    final marketData = _marketCryptoData[marketTypeId];
    if (marketData == null) {
      return [];
    }

    List<CryptoItemData> cryptoList = marketData.values.toList();

    // 如果不是"全部"栏目，需要根据栏目ID过滤
    if (categoryId != 'all') {
      final allCurrencies = MarketService.instance.currencyModels;

      // 获取属于当前栏目的币种ID列表
      final categoryIds = <int>[];
      for (final currency in allCurrencies) {
        if (currency.cateIds.contains(int.tryParse(categoryId))) {
          categoryIds.add(currency.id);
        }
      }

      // 过滤出属于当前栏目的币种
      cryptoList =
          cryptoList.where((crypto) {
            // 通过symbol找到对应的currency_id
            final currency = allCurrencies.firstWhere(
              (c) => c.baseAsset == crypto.symbol && c.quoteAsset == crypto.quoteCurrency,
              orElse: () => throw StateError('Currency not found'),
            );
            try {
              return categoryIds.contains(currency.id);
            } catch (e) {
              return false;
            }
          }).toList();
    }

    return cryptoList;
  }
}
