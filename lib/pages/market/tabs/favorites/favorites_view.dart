/*
* 自选标签页
*/

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/services/market/index.dart';
import 'package:qubic_exchange/services/storage/storage_service.dart';
import 'package:qubic_exchange/services/network/websocket_service.dart';
import '../../widgets/index.dart';
import '../../widgets/market_tab_bar_delegate.dart';
import '../../models/crypto_item_data.dart';

class FavoritesView extends StatefulWidget {
  final ScrollPhysics? physics;
  final List<CryptoItemData>? availableCryptoData; // 可选择的加密货币数据

  const FavoritesView({super.key, this.physics, this.availableCryptoData});

  @override
  State<FavoritesView> createState() => _FavoritesViewState();
}

class _FavoritesViewState extends State<FavoritesView> with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  // 标签控制器
  late TabController _tabController;

  // 动态市场标签项 [{'id': '1', 'name': '现货'}, ...]
  List<Map<String, String>> _marketTabs = [];

  // 每个市场的自选币种ID列表 {'1': [866, 123, ...], '5': [456, 789, ...]}
  final Map<String, List<int>> _favoritesByMarket = {};

  // 每个市场的币种数据缓存 {'1': {currency_id: CryptoItemData}, '5': {...}}
  final Map<String, Map<int, CryptoItemData>> _marketCryptoData = {};

  // WebSocket相关
  StreamSubscription<Map<String, dynamic>>? _tickerSubscription;
  Set<int> _pendingUpdates = {};
  String _currentMarketId = '';

  // 性能优化：限制更新频率
  DateTime _lastUpdateTime = DateTime.now();
  static const Duration _minUpdateInterval = Duration(milliseconds: 100);

  // 数据加载状态标记
  bool _isDataLoaded = false;

  @override
  void initState() {
    super.initState();
    _initializeMarketTabs();

    // 等待WebSocket连接后再订阅
    _waitForWebSocketAndSubscribe();
  }

  @override
  void dispose() {
    _tickerSubscription?.cancel();
    _tabController.dispose();
    super.dispose();
  }

  /// 刷新数据方法，供外部调用
  Future<void> refreshData() async {
    try {
      debugPrint('🔄 FavoritesView开始刷新数据...');

      // 重置数据加载状态，允许重新加载
      _isDataLoaded = false;

      // 刷新MarketService数据
      await MarketService.instance.refreshAllData();

      // 重新初始化市场标签页
      await _initializeMarketTabs();

      // 刷新界面
      if (mounted) {
        setState(() {});
      }

      debugPrint('✅ FavoritesView数据刷新完成');
    } catch (e) {
      debugPrint('❌ FavoritesView数据刷新失败: $e');
    }
  }

  /// 初始化市场标签页
  Future<void> _initializeMarketTabs() async {
    // 如果数据已经加载过，直接返回
    if (_isDataLoaded) {
      return;
    }

    try {
      // 确保MarketService已初始化
      if (MarketService.instance.marketTypeModels.isEmpty) {
        // 如果MarketService还没有市场数据，先尝试获取
        await MarketService.instance.fetchMarketTypeData();
      }

      // 获取市场模型数据
      final marketModels = MarketService.instance.marketTypeModels;

      if (marketModels.isNotEmpty) {
        _marketTabs =
            marketModels.map((market) {
              return {'id': market.id.toString(), 'name': market.name};
            }).toList();
      } else {
        // 如果没有市场数据，使用默认的
        _marketTabs = [
          {'id': '1', 'name': '现货'},
          {'id': '5', 'name': '合约'},
          {'id': '6', 'name': '杠杆'},
        ];
      }

      // 初始化TabController
      _tabController = TabController(length: _marketTabs.length, vsync: this);

      // 添加标签切换监听器
      _tabController.addListener(() {
        if (!_tabController.indexIsChanging) {
          final newMarketId = _marketTabs[_tabController.index]['id']!;
          _onTabChanged(newMarketId);
        }
      });

      // 设置初始选中的市场ID
      if (_marketTabs.isNotEmpty) {
        _currentMarketId = _marketTabs[0]['id']!;
      }

      // 加载自选数据
      await _loadFavoritesData();

      // 标记数据已加载
      _isDataLoaded = true;

      // 刷新界面
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      debugPrint('❌ 市场标签初始化失败: $e');

      // 使用默认数据
      _marketTabs = [
        {'id': '1', 'name': '现货'},
        {'id': '5', 'name': '合约'},
        {'id': '6', 'name': '杠杆'},
      ];
      _tabController = TabController(length: _marketTabs.length, vsync: this);

      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return CustomScrollView(
      physics: widget.physics,
      slivers: [
        // 吸顶标签栏 - 修改为在主标签栏下方停靠
        SliverPersistentHeader(
          pinned: true,
          delegate: MarketSubTabBarDelegate(child: _buildTabbar(), height: 38, backgroundColor: context.templateColors.surface),
        ),

        // 标签内容页
        SliverFillRemaining(child: _buildTabView()),
      ],
    );
  }

  // 构建标签栏
  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      decoration: BoxDecoration(border: Border(bottom: BorderSide(width: 0.5, color: context.templateColors.divider))),
      child:
          _marketTabs.isEmpty
              ? const SizedBox.shrink()
              : TabbarWidget(
                height: 38,
                tabs: _marketTabs.map((tab) => TabItem(title: tab['name']!)).toList(),
                controller: _tabController,
                labelStyle: context.templateStyle.text.bodyTextMedium,
                indicatorStyle: TabBarIndicatorStyle.underline,
                indicatorSize: TabBarIndicatorSize.label,
                selectedColor: context.templateColors.textPrimary,
                unselectedColor: context.templateColors.textSecondary,
                showIndicator: true,
                labelPadding: EdgeInsets.only(right: UiConstants.spacing18),
              ),
    );
  }

  // 构建标签内容页
  Widget _buildTabView() {
    if (_marketTabs.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    return TabBarView(
      controller: _tabController,
      children:
          _marketTabs.map((tab) {
            return _buildTabContent(marketId: tab['id']!);
          }).toList(),
    );
  }

  // 构建标签内容 - 优先显示FavoritesListView，有自选后显示MarketCryptoList
  Widget _buildTabContent({required String marketId}) {
    // 根据市场ID获取对应的自选数据
    List<CryptoItemData> currentFavoritesData = _getFavoritesDataByMarketId(marketId);
    bool isContract = marketId == '5'; // 合约市场ID为5

    if (currentFavoritesData.isEmpty) {
      // 没有自选数据时，检查是否有可用数据
      final availableData = _getAvailableDataByMarketId(marketId);

      if (availableData == null || availableData.isEmpty) {
        // 没有可用数据时，显示空状态
        return EmptyWidget(
          text: '暂无${_getMarketName(marketId)}数据',
          showButton: true,
          buttonText: '刷新',
          onButtonTap: () async {
            // 刷新MarketService数据
            await MarketService.instance.fetchCurrencyData();
            if (mounted) {
              setState(() {});
            }
          },
        );
      } else {
        // 有可用数据时，显示选择界面
        return FavoritesListView(
          physics: widget.physics,
          availableCryptoData: availableData,
          onSelectionConfirmed: (selectedCryptoData) {
            setState(() {
              _setFavoritesDataByMarketId(marketId, selectedCryptoData);
            });
          },
        );
      }
    } else {
      // 有自选数据时，显示列表
      if (currentFavoritesData.isEmpty) {
        return _buildEmptyState();
      }
      return MarketCryptoList(isContract: isContract, physics: widget.physics, cryptoDataList: currentFavoritesData);
    }
  }

  /// 构建空状态视图
  Widget _buildEmptyState() {
    return EmptyWidget(text: '点击币种右侧的星标添加自选', imageName: 'noData');
  }

  /// 根据市场ID获取市场名称
  String _getMarketName(String marketId) {
    final tab = _marketTabs.firstWhere((tab) => tab['id'] == marketId, orElse: () => {'name': '币种'});
    return tab['name'] ?? '币种';
  }

  /// 根据市场类型获取对应的ticker数据
  TickerModel? _getTickerDataForMarket(CurrencyModel currency, String marketId) {
    // 根据市场类型选择对应的ticker数据
    String tickerKey;
    if (marketId == '6') {
      // 杠杆栏目使用现货的ticker数据
      tickerKey = '1';
    } else {
      // 其他栏目使用对应栏目ID的ticker数据
      tickerKey = marketId;
    }

    return currency.tickers[tickerKey];
  }

  /// 根据市场ID获取自选数据
  List<CryptoItemData> _getFavoritesDataByMarketId(String marketId) {
    final marketData = _marketCryptoData[marketId];
    return marketData?.values.toList() ?? [];
  }

  /// 根据市场ID设置自选数据
  void _setFavoritesDataByMarketId(String marketId, List<CryptoItemData> data) {
    // 更新自选ID列表 - 需要从MarketService中找到对应的ID
    final ids = <int>[];
    final updatedCryptoData = <CryptoItemData>[];

    for (final crypto in data) {
      final currency = MarketService.instance.currencyModels.firstWhere(
        (c) => c.baseAsset == crypto.symbol && c.quoteAsset == crypto.quoteCurrency,
        orElse: () => throw StateError('Currency not found'),
      );

      try {
        ids.add(currency.id);

        // 获取最新的ticker数据
        final tickerData = _getTickerDataForMarket(currency, marketId);

        // 安全地提取ticker数据
        double currentPrice = 0.0;
        double changePercentage = 0.0;
        double volume24h = 0.0;

        if (tickerData != null) {
          currentPrice = tickerData.lastPrice;
          changePercentage = tickerData.priceChangeP;
          volume24h = tickerData.volume;
        }

        // 创建包含最新ticker数据的CryptoItemData
        updatedCryptoData.add(crypto.copyWith(currentPrice: currentPrice, changePercentage: changePercentage, volume24h: volume24h));
      } catch (e) {
        // 如果没找到对应的币种模型，跳过
      }
    }

    _favoritesByMarket[marketId] = ids;
    // 更新币种数据缓存为包含ticker数据的版本，转换为Map结构
    final dataMap = <int, CryptoItemData>{};
    for (final crypto in updatedCryptoData) {
      final currency = MarketService.instance.currencyModels.firstWhere(
        (c) => c.baseAsset == crypto.symbol && c.quoteAsset == crypto.quoteCurrency,
        orElse: () => throw StateError('Currency not found'),
      );
      try {
        dataMap[currency.id] = crypto;
      } catch (e) {
        // 如果没找到对应的币种模型，跳过
      }
    }
    _marketCryptoData[marketId] = dataMap;

    // 保存到本地
    _saveFavoritesData();
  }

  /// 根据市场ID获取可用数据（从MarketService获取对应市场的币种）
  List<CryptoItemData>? _getAvailableDataByMarketId(String marketId) {
    try {
      // 从MarketService获取币种模型数据
      final allCurrencies = MarketService.instance.currencyModels;

      // 特殊处理：标签栏 id 为 1, 5, 6 的都使用 market_type = 1 的数据
      List<CurrencyModel> marketCurrencies;

      if (['1', '5', '6'].contains(marketId)) {
        // 使用 market_type = 1 的数据
        marketCurrencies =
            allCurrencies.where((currency) {
              if (marketId == '5') {
                // 合约标签栏：需要 market_type = 1 且 is_marginTrade = 1
                return currency.marketType == 1 && currency.isMarginTrade == 1;
              } else {
                // 现货和杠杆标签栏：只需要 market_type = 1
                return currency.marketType == 1;
              }
            }).toList();
      } else {
        // 其他标签栏：使用对应的 market_type
        final targetMarketType = int.tryParse(marketId) ?? 1;
        marketCurrencies =
            allCurrencies.where((currency) {
              return currency.marketType == targetMarketType;
            }).toList();
      }

      // 转换为CryptoItemData格式
      final cryptoData =
          marketCurrencies.map((currency) {
            return CryptoItemData(
              symbol: currency.baseAsset,
              quoteCurrency: currency.quoteAsset,
              currentPrice: 0.0, // 价格需要从行情数据获取
              cnyPrice: 0.0, // 人民币价格需要从行情数据获取
              changePercentage: 0.0, // 24小时涨跌幅需要从行情数据获取
              volume24h: 0.0, // 24小时成交量需要从行情数据获取
              volumeUnit: '万', // 默认单位
            );
          }).toList();

      // 如果没有对应市场的数据，返回空列表
      if (cryptoData.isEmpty) {
        return [];
      }

      return cryptoData;
    } catch (e) {
      return [];
    }
  }

  /// 加载保存的自选数据
  Future<void> _loadFavoritesData() async {
    try {
      final storage = StorageService.instance;

      // 确保_marketTabs已经初始化
      if (_marketTabs.isEmpty) {
        return;
      }

      // 加载每个市场的自选symbol列表
      for (final tab in _marketTabs) {
        final marketId = tab['id']!;
        final key = 'favorites_market_$marketId';
        final symbolList = storage.getList(key);

        if (symbolList != null) {
          try {
            final ids = symbolList.cast<int>();
            _favoritesByMarket[marketId] = ids;

            // 根据ID列表从MarketService获取完整数据
            await _loadCryptoDataForMarket(marketId, ids);
          } catch (e) {
            // 可能存储的是String类型，尝试转换
            try {
              final stringList = symbolList.cast<String>();
              final ids = stringList.map((s) => int.parse(s)).toList();
              _favoritesByMarket[marketId] = ids;
              await _loadCryptoDataForMarket(marketId, ids);
            } catch (e2) {
              // 静默处理转换错误
            }
          }
        }
      }
    } catch (e) {
      // 静默处理加载错误
    }
  }

  /// 根据ID列表为指定市场加载币种数据
  Future<void> _loadCryptoDataForMarket(String marketId, List<int> ids) async {
    try {
      final allCurrencies = MarketService.instance.currencyModels;

      if (allCurrencies.isEmpty) {
        debugPrint('⚠️ MarketService币种模型数据为空，跳过加载');
        return;
      }

      final cryptoDataList = <CryptoItemData>[];

      for (final id in ids) {
        // 直接根据ID查找币种模型数据
        final currency = allCurrencies.firstWhere((c) => c.id == id, orElse: () => throw StateError('Currency not found'));

        try {
          // 根据市场类型获取对应的ticker数据
          final tickerData = _getTickerDataForMarket(currency, marketId);

          // 安全地提取ticker数据
          double currentPrice = 0.0;
          double changePercentage = 0.0;
          double volume24h = 0.0;

          if (tickerData != null) {
            currentPrice = tickerData.lastPrice;
            changePercentage = tickerData.priceChangeP;
            volume24h = tickerData.volume;
          }

          cryptoDataList.add(
            CryptoItemData(
              symbol: currency.baseAsset,
              quoteCurrency: currency.quoteAsset,
              currentPrice: currentPrice,
              cnyPrice: 0.0, // CNY价格需要单独计算
              changePercentage: changePercentage,
              volume24h: volume24h,
              volumeUnit: '万',
              precision: currency.mPricePrecision,
            ),
          );
        } catch (e) {
          // 如果没找到对应的币种模型，跳过
        }
      }

      // 转换为Map结构
      final dataMap = <int, CryptoItemData>{};
      for (final crypto in cryptoDataList) {
        final currency = MarketService.instance.currencyModels.firstWhere(
          (c) => c.baseAsset == crypto.symbol && c.quoteAsset == crypto.quoteCurrency,
          orElse: () => throw StateError('Currency not found'),
        );
        try {
          dataMap[currency.id] = crypto;
        } catch (e) {
          // 如果没找到对应的币种模型，跳过
        }
      }
      _marketCryptoData[marketId] = dataMap;
    } catch (e) {
      // 静默处理加载错误
    }
  }

  /// 保存自选数据到本地存储
  Future<void> _saveFavoritesData() async {
    try {
      final storage = StorageService.instance;

      // 保存每个市场的自选ID列表
      for (final entry in _favoritesByMarket.entries) {
        final marketId = entry.key;
        final ids = entry.value;
        final key = 'favorites_market_$marketId';
        await storage.setList(key, ids);
      }
    } catch (e) {
      // 静默处理保存错误
    }
  }

  // ========== WebSocket相关方法 ==========

  /// 等待WebSocket连接并订阅
  void _waitForWebSocketAndSubscribe() {
    // 监听WebSocket连接状态
    WebSocketService.instance.connectionStream.listen((isConnected) {
      if (isConnected && mounted) {
        _subscribeToTickers();
      }
    });

    // 如果已经连接，直接订阅
    if (WebSocketService.instance.isConnected) {
      _subscribeToTickers();
    }
  }

  /// 订阅ticker数据
  void _subscribeToTickers() {
    // 订阅market_type 1的ticker数据
    final subscribeMessage1 = {"action": "subscribe", "type": "ticker", "market_type": 1};

    // 订阅market_type 5的ticker数据
    final subscribeMessage5 = {"action": "subscribe", "type": "ticker", "market_type": 5};

    // 发送订阅消息
    WebSocketService.instance.sendMessage(subscribeMessage1);
    WebSocketService.instance.sendMessage(subscribeMessage5);

    // 监听ticker消息
    _tickerSubscription = WebSocketService.instance.subscribe('ticker', {}).listen(_handleTickerData);
  }

  /// 处理ticker数据
  void _handleTickerData(Map<String, dynamic> message) {
    if (message['type'] == 'ticker' && message['data'] != null) {
      final int marketType = message['market_type'] ?? 0;
      final List<dynamic> tickerList = message['data'];

      // 根据market_type确定对应的市场ID列表
      List<String> targetMarketIds = _getMarketIdsByType(marketType);

      // 立即处理当前显示的市场数据
      _handleCurrentMarketData(targetMarketIds, tickerList);

      // 异步处理其他市场数据
      _handleOtherMarketsDataAsync(targetMarketIds, tickerList);
    }
  }

  /// 立即处理当前显示的市场数据
  void _handleCurrentMarketData(List<String> targetMarketIds, List<dynamic> tickerList) {
    bool shouldUpdateUI = false;

    for (String marketId in targetMarketIds) {
      // 只处理当前显示的市场
      if (marketId == _currentMarketId && _marketCryptoData.containsKey(marketId)) {
        final marketData = _marketCryptoData[marketId]!;

        for (final ticker in tickerList) {
          final currencyId = ticker['currency_id'] as int;

          if (marketData.containsKey(currencyId)) {
            _updateCryptoData(marketId, currencyId, ticker);
            _pendingUpdates.add(currencyId);
            shouldUpdateUI = true;
          }
        }
      }
    }

    // 立即更新UI
    if (shouldUpdateUI) {
      _scheduleImmediateUpdate();
    }
  }

  /// 异步处理其他市场数据
  void _handleOtherMarketsDataAsync(List<String> targetMarketIds, List<dynamic> tickerList) {
    // 使用microtask异步处理，不阻塞当前UI更新
    Future.microtask(() {
      // 性能优化：限制更新频率
      final now = DateTime.now();
      if (now.difference(_lastUpdateTime) < _minUpdateInterval) {
        return;
      }
      _lastUpdateTime = now;

      for (String marketId in targetMarketIds) {
        // 只处理非当前显示的市场
        if (marketId != _currentMarketId && _marketCryptoData.containsKey(marketId)) {
          final marketData = _marketCryptoData[marketId]!;

          for (final ticker in tickerList) {
            final currencyId = ticker['currency_id'] as int;

            if (marketData.containsKey(currencyId)) {
              _updateCryptoData(marketId, currencyId, ticker);
            }
          }
        }
      }
    });
  }

  /// 根据market_type获取市场ID列表
  List<String> _getMarketIdsByType(int marketType) {
    switch (marketType) {
      case 1:
        return ['1', '6']; // 现货市场和杠杆市场使用market_type=1的数据
      case 5:
        return ['5']; // 合约市场使用market_type=5的数据
      default:
        return [];
    }
  }

  /// 更新单个币种数据
  void _updateCryptoData(String marketId, int currencyId, Map<String, dynamic> ticker) {
    final marketData = _marketCryptoData[marketId];
    final existingData = marketData?[currencyId];

    if (existingData != null) {
      // 获取价格数据
      final lastPrice = (ticker['last_price'] as num?)?.toDouble() ?? 0.0;
      final openPrice = (ticker['open_price'] as num?)?.toDouble() ?? 0.0;
      final volume = (ticker['volume'] as num?)?.toDouble() ?? 0.0;

      // 性能优化：只有数据真正变化时才更新
      if (existingData.currentPrice == lastPrice && existingData.volume24h == volume) {
        return; // 数据没有变化，跳过更新
      }

      // 计算涨跌幅：如果后端的price_changeP为0，则自己计算
      double changePercentage = (ticker['price_changeP'] as num?)?.toDouble() ?? 0.0;
      if (changePercentage == 0.0 && openPrice > 0) {
        changePercentage = ((lastPrice - openPrice) / openPrice) * 100;
      }

      marketData![currencyId] = existingData.copyWith(
        currentPrice: lastPrice,
        changePercentage: changePercentage,
        volume24h: volume,
        lastUpdated: DateTime.now(),
      );
    }
  }

  /// 立即更新UI（用于当前显示的tab）
  void _scheduleImmediateUpdate() {
    if (_pendingUpdates.isNotEmpty && mounted) {
      setState(() {
        _pendingUpdates.clear();
      });
    }
  }

  /// 标签切换处理
  void _onTabChanged(String newMarketId) {
    setState(() {
      _currentMarketId = newMarketId;
      _pendingUpdates.clear(); // 清空待更新队列
    });

    // 切换栏目后立即触发一次UI更新，确保显示最新数据
    if (mounted) {
      setState(() {});
    }
  }
}
