/*
*  链上交易
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import '../../widgets/market_crypto_list.dart';
import '../../models/crypto_item_data.dart';
import 'package:remixicon/remixicon.dart';

class ChainTradeView extends StatefulWidget {
  final ScrollPhysics? physics;

  const ChainTradeView({super.key, this.physics});

  @override
  State<ChainTradeView> createState() => _ChainTradeViewState();
}

class _ChainTradeViewState extends State<ChainTradeView>
    with TickerProviderStateMixin {
  // 标签控制器
  late TabController _mainController;
  late TabController _subController;

  // 标签项
  static const List<String> _mainKeys = ['精选', '新上', '总市值', '成交额', '持有人数'];
  static const List<String> _subKeys = ['全部', 'Base', 'BSC', 'SOL'];

  // 初始化
  @override
  void initState() {
    super.initState();
    _mainController = TabController(length: _mainKeys.length, vsync: this);
    _subController = TabController(length: _subKeys.length, vsync: this);

    // 不再需要监听器，TabView 会自动管理内容切换
  }

  // 销毁
  @override
  void dispose() {
    _mainController.dispose();
    _subController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 主标签栏
        Container(
          decoration: BoxDecoration(
            color: context.templateColors.surface,
            border: Border(
              bottom: BorderSide(
                width: 0.5,
                color: context.templateColors.divider,
              ),
            ),
          ),
          child: _buildMainTabbar(),
        ),

        // 主标签内容 - 占据剩余空间
        Flexible(
          child: TabBarView(
            controller: _mainController,
            children:
                _mainKeys.map((mainTabKey) {
                  return _buildMainTabContent(mainTabKey);
                }).toList(),
          ),
        ),
      ],
    );
  }

  // 构建主标签
  Widget _buildMainTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: TabbarWidget(
        height: 38,
        tabs: _mainKeys.map((key) => TabItem(title: key)).toList(),
        controller: _mainController,
        labelStyle: context.templateStyle.text.bodyTextMedium,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: true,
        indicatorSize: TabBarIndicatorSize.label,
        labelPadding: EdgeInsets.only(right: UiConstants.spacing18),
      ),
    );
  }

  // 构建子标签
  Widget _buildSubTabbar() {
    return Container(
      padding: EdgeInsets.only(
        left: UiConstants.spacing16,
        right: UiConstants.spacing16,
        top: UiConstants.spacing16,
        bottom: UiConstants.spacing10,
      ),
      decoration: BoxDecoration(color: context.templateColors.surface),
      child: Row(
        children: [
          Expanded(
            child: TabbarWidget(
              height: 24,
              controller: _subController,
              tabs: _subKeys.map((key) => TabItem(title: key)).toList(),
              labelStyle: context.templateStyle.text.hintTextMedium,
              selectedColor: context.templateColors.textPrimary,
              unselectedColor: context.templateColors.textSecondary,
              showIndicator: true,
              indicatorStyle: TabBarIndicatorStyle.filled,
              labelPadding: EdgeInsets.zero,
              tabPadding: EdgeInsets.symmetric(
                horizontal: UiConstants.spacing10,
              ),
            ),
          ),
          SizedBox(width: UiConstants.spacing18),
          InkWellWidget(
            child: Row(
              children: [
                Text(
                  '价格',
                  style: context.templateStyle.text.hintTextMedium.copyWith(
                    color: context.templateColors.textPrimary,
                  ),
                ),
                SizedBox(width: UiConstants.spacing4),
                Icon(
                  RemixIcons.arrow_left_right_fill,
                  size: 16,
                  color: context.templateColors.textTertiary,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 生成链上交易数据
  List<CryptoItemData> _generateChainTradeData({
    required String mainTabKey,
    required String subTabKey,
  }) {
    final baseSymbols = _getSymbolsBySubTab(subTabKey);

    return List.generate(baseSymbols.length, (index) {
      final symbol = baseSymbols[index];
      final isPositive = _getIsPositiveByMainTab(mainTabKey, index);
      final basePrice = _getBasePriceBySymbol(symbol);
      final adjustedPrice = _adjustPriceByMainTab(mainTabKey, basePrice, index);

      return CryptoItemData(
        symbol: symbol,
        quoteCurrency: 'USDT',
        currentPrice: adjustedPrice,
        cnyPrice: adjustedPrice * 7.2,
        changePercentage:
            isPositive ? (index % 10).toDouble() : -(index % 10).toDouble(),
        volume24h: _getVolumeByMainTab(mainTabKey, index),
        volumeUnit: index % 2 == 0 ? '亿' : '万',
        tradingTag: null,
      );
    });
  }

  // 根据子标签获取对应的币种
  List<String> _getSymbolsBySubTab(String subTabKey) {
    switch (subTabKey) {
      case '全部':
        return [
          'BTC',
          'ETH',
          'BNB',
          'SOL',
          'MATIC',
          'AVAX',
          'DOT',
          'LINK',
          'UNI',
          'AAVE',
        ];
      case 'Base':
        return ['CBETH', 'USDC', 'WETH', 'DEGEN', 'BRETT', 'TOSHI'];
      case 'BSC':
        return ['BNB', 'CAKE', 'XVS', 'ALPACA', 'BAKE', 'TWT'];
      case 'SOL':
        return ['SOL', 'RAY', 'SRM', 'FIDA', 'COPE', 'STEP'];
      default:
        return ['BTC', 'ETH', 'BNB'];
    }
  }

  // 根据币种获取基础价格
  double _getBasePriceBySymbol(String symbol) {
    switch (symbol) {
      case 'BTC':
        return 43250.0;
      case 'ETH':
        return 2650.0;
      case 'BNB':
        return 315.0;
      case 'SOL':
        return 98.5;
      case 'MATIC':
        return 0.85;
      case 'AVAX':
        return 35.2;
      case 'DOT':
        return 7.2;
      case 'LINK':
        return 14.8;
      case 'UNI':
        return 6.5;
      case 'AAVE':
        return 95.0;
      case 'CBETH':
        return 2640.0;
      case 'USDC':
        return 1.0;
      case 'WETH':
        return 2650.0;
      case 'DEGEN':
        return 0.012;
      case 'BRETT':
        return 0.089;
      case 'TOSHI':
        return 0.00015;
      case 'CAKE':
        return 2.1;
      case 'XVS':
        return 8.5;
      case 'ALPACA':
        return 0.25;
      case 'BAKE':
        return 0.18;
      case 'TWT':
        return 1.2;
      case 'RAY':
        return 1.8;
      case 'SRM':
        return 0.35;
      case 'FIDA':
        return 0.28;
      case 'COPE':
        return 0.045;
      case 'STEP':
        return 0.012;
      default:
        return 1.0;
    }
  }

  // 根据主标签判断涨跌
  bool _getIsPositiveByMainTab(String mainTabKey, int index) {
    switch (mainTabKey) {
      case '精选':
        return index % 3 != 0; // 大部分上涨
      case '新上':
        return index % 2 == 0; // 一半上涨一半下跌
      case '总市值':
        return index % 4 != 3; // 75% 上涨
      case '成交额':
        return index % 5 != 0; // 80% 上涨
      case '持有人数':
        return index % 3 == 0; // 33% 上涨
      default:
        return index % 2 == 0;
    }
  }

  // 根据主标签调整价格
  double _adjustPriceByMainTab(String mainTabKey, double basePrice, int index) {
    switch (mainTabKey) {
      case '精选':
        return basePrice + (index * 50); // 精选币种价格较高
      case '新上':
        return basePrice * 0.8 + (index * 20); // 新上币种价格较低
      case '总市值':
        return basePrice + (index * 100); // 按市值排序，价格递增
      case '成交额':
        return basePrice + (index * 75); // 按成交额排序
      case '持有人数':
        return basePrice + (index * 30); // 按持有人数排序
      default:
        return basePrice + (index * 50);
    }
  }

  // 根据主标签获取交易量
  double _getVolumeByMainTab(String mainTabKey, int index) {
    switch (mainTabKey) {
      case '精选':
        return 50.0 + index * 10; // 精选币种交易量大
      case '新上':
        return 5.0 + index * 2; // 新上币种交易量小
      case '总市值':
        return 100.0 - index * 5; // 按市值递减
      case '成交额':
        return 200.0 - index * 10; // 按成交额递减
      case '持有人数':
        return 30.0 + index * 5; // 按持有人数递增
      default:
        return 10.0 + index * 3;
    }
  }

  // 构建主标签内容
  Widget _buildMainTabContent(String mainTabKey) {
    return Column(
      children: [
        // 子标签栏
        Container(
          decoration: BoxDecoration(color: context.templateColors.surface),
          child: _buildSubTabbar(),
        ),

        // 子标签内容 - 占据剩余空间
        Flexible(
          child: TabBarView(
            controller: _subController,
            children:
                _subKeys.map((subTabKey) {
                  return _buildSubTabContent(mainTabKey, subTabKey);
                }).toList(),
          ),
        ),
      ],
    );
  }

  // 构建子标签内容
  Widget _buildSubTabContent(String mainTabKey, String subTabKey) {
    final cryptoData = _generateChainTradeData(
      mainTabKey: mainTabKey,
      subTabKey: subTabKey,
    );
    final isNewListing = mainTabKey == '新上'; // 新上标签显示新币卡片

    return MarketCryptoList(
      cryptoDataList: cryptoData,
      isContract: false, // 链上交易不是合约
      isNewListing: isNewListing,
      physics: widget.physics,
    );
  }
}
