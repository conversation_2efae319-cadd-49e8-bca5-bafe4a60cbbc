/*
    观点标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/models/dynamics_model.dart';
import 'package:qubic_exchange/services/clients/dio_request.dart';
import 'package:qubic_exchange/services/config/api_route.dart';
import 'package:qubic_exchange/services/market/market_service.dart';
import '../../widgets/index.dart';

class InsightsView extends StatefulWidget {
  final ScrollPhysics? physics;

  const InsightsView({super.key, this.physics});

  @override
  State<InsightsView> createState() => _InsightsViewState();
}

class _InsightsViewState extends State<InsightsView> {
  final ScrollController _scrollController = ScrollController();
  List<DynamicsItem> _posts = [];
  int _currentPage = 1;
  static const int _pageSize = 20;
  bool _isLoading = false;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  //执行下拉刷新
  Future<void> refreshData() async {
    // 只在视图显示时才执行刷新
    if (!mounted) return;
    // 重置状态并重新加载数据
    _currentPage = 1;
    _hasMore = true;
    await _loadData();
  }

  // 监听滚动事件
  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading && _hasMore) {
        _loadMoreData();
      }
    }
  }

  // 加载数据
  Future<void> _loadData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await DioRequest.instance.get('${ApiRoute.dynamicsList}?page=1&page_size=$_pageSize');

      if (response.success && response.data != null) {
        final dynamicsResponse = DynamicsListResponse.fromJson(response.data);
        setState(() {
          _posts = dynamicsResponse.list;
          _currentPage = 1;
          _hasMore = dynamicsResponse.page < dynamicsResponse.totalPage;
        });
      }
    } catch (e) {
      debugPrint('加载动态数据失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 加载更多数据
  Future<void> _loadMoreData() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final nextPage = _currentPage + 1;
      final response = await DioRequest.instance.get('${ApiRoute.dynamicsList}?page=$nextPage&page_size=$_pageSize');

      if (response.success && response.data != null) {
        final dynamicsResponse = DynamicsListResponse.fromJson(response.data);
        setState(() {
          _posts.addAll(dynamicsResponse.list);
          _currentPage = dynamicsResponse.page;
          _hasMore = dynamicsResponse.page < dynamicsResponse.totalPage;
        });
      }
    } catch (e) {
      debugPrint('加载更多动态数据失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 切换点赞状态
  void _toggleLike(int postId) {
    // TODO: 实现点赞API调用
  }

  // 处理评论
  void _handleComment(int postId) {
    // TODO: 实现评论功能
  }

  // 处理分享
  void _handleShare(int postId) {
    // TODO: 实现分享功能
  }

  // 处理关注
  void _handleFollow(String userName) {
    // TODO: 实现关注功能
  }

  // 根据dynamics_currency获取价格数据
  List<Map<String, dynamic>>? _getPriceDataFromDynamicsCurrency(List<DynamicsCurrency>? dynamicsCurrency) {
    if (dynamicsCurrency == null || dynamicsCurrency.isEmpty) return null;

    final List<Map<String, dynamic>> priceData = [];

    for (final currency in dynamicsCurrency) {
      final symbolId = currency.symbolId;

      // 从MarketService中查找对应的币种数据
      final currencyModel = MarketService.instance.currencyModels.firstWhere(
        (c) => c.id == symbolId,
        orElse: () => throw StateError('Currency not found'),
      );

      try {
        final baseAsset = currencyModel.baseAsset;
        final ticker1 = currencyModel.tickers['1']; // 获取market_type为1的ticker数据

        if (baseAsset.isNotEmpty && ticker1 != null) {
          final changePercent = ticker1.priceChangeP;

          // 格式化涨跌幅
          final changeStr = changePercent >= 0 ? '+${changePercent.toStringAsFixed(2)}%' : '${changePercent.toStringAsFixed(2)}%';

          priceData.add({'symbol': baseAsset, 'change': changeStr});
        }
      } catch (e) {
        // 如果找不到币种，跳过
      }
    }

    return priceData.isNotEmpty ? priceData : null;
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading && _posts.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_posts.isEmpty) {
      return const EmptyWidget(text: '暂无动态数据', imageName: 'noData');
    }

    return SizedBox(
      width: double.infinity,
      child: SingleChildScrollView(
        controller: _scrollController,
        physics: widget.physics,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 动态生成所有帖子
            ..._posts.map((post) {
              return PostCard(
                userAvatar: post.userAvatar,
                userName: post.userName,
                timeAgo: post.timeAgo,
                title: post.title,
                content: post.content,
                images: post.images,
                priceData: _getPriceDataFromDynamicsCurrency(post.dynamicsCurrency),
                likeCount: post.likeCount,
                commentCount: post.commentCount,
                userId: post.userIdString,
                isLiked: post.isLiked,
                onLike: () => _toggleLike(post.id),
                onComment: () => _handleComment(post.id),
                onShare: () => _handleShare(post.id),
                onFollow: () => _handleFollow(post.userName),
              );
            }),
            // 加载更多指示器
            if (_isLoading) const Padding(padding: EdgeInsets.all(16.0), child: CircularProgressIndicator()),
            // 推荐关注组件
            if (!_isLoading) Recommended(),
          ],
        ),
      ),
    );
  }
}
