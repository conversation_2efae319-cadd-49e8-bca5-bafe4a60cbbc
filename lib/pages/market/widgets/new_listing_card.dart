/*
*  新币上市
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

/// 新币上线数据模型
class NewListingData {
  final String symbol;
  final String quoteCurrency;
  final String launchTime;
  final DateTime endTime; // 倒计时结束时间
  final String status;
  final int currentPage;
  final int totalPages;

  const NewListingData({
    required this.symbol,
    required this.quoteCurrency,
    required this.launchTime,
    required this.endTime,
    required this.status,
    required this.currentPage,
    required this.totalPages,
  });
}

class NewListingCard extends StatefulWidget {
  final List<NewListingData>? listingData;
  final Function(NewListingData)? onCardTap;

  const NewListingCard({super.key, this.listingData, this.onCardTap});

  @override
  State<NewListingCard> createState() => _NewListingCardState();
}

class _NewListingCardState extends State<NewListingCard> {
  late PageController _pageController;

  // 默认数据
  List<NewListingData> get _listingData {
    return widget.listingData ??
        [
          NewListingData(
            symbol: 'BTC',
            quoteCurrency: 'USDT',
            launchTime: '07/14 20:00',
            endTime: DateTime.now().add(const Duration(hours: 1)), // 1小时后结束
            status: '即将开盘',
            currentPage: 1,
            totalPages: 1,
          ),
        ];
  }

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 130,
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing16,
        vertical: UiConstants.spacing12,
      ),
      decoration: BoxDecoration(
        color: context.templateColors.surface,
        border: Border(
          bottom: BorderSide(color: context.templateColors.divider, width: 0.5),
          top: BorderSide(color: context.templateColors.divider, width: 0.5),
        ),
      ),
      child: PageView.builder(
        controller: _pageController,
        itemCount: _listingData.length,
        itemBuilder: (context, index) {
          return _buildListingCard(_listingData[index]);
        },
      ),
    );
  }

  // 构建新币上线卡片
  Widget _buildListingCard(NewListingData data) {
    return InkWellWidget(
      onTap: () => widget.onCardTap?.call(data),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ImageWidget(
            width: 30,
            height: 30,
            assetFolder: AssetFolder.crypto,
            name: data.symbol.toLowerCase(),
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // 货币名称
              Text.rich(
                TextSpan(
                  style: context.templateStyle.text.bodyLargeMedium,
                  children: [
                    TextSpan(text: data.symbol),
                    TextSpan(
                      text: ' / ${data.quoteCurrency}',
                      style: context.templateStyle.text.descriptionText,
                    ),
                  ],
                ),
              ),
              Spacer(),

              // 开盘状态
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    data.status,
                    style: context.templateStyle.text.descriptionText.copyWith(
                      fontSize: 13,
                    ),
                  ),
                  CountdownWidget(
                    targetTime: data.endTime,
                    textStyle: context.templateStyle.text.bodyLargeMedium,
                    format: CountdownFormat.dayHourMinuteSecond,
                    showLabels: true,
                    labelTexts: {'days': '天'},
                    labelStyle: context.templateStyle.text.bodyLargeMedium,
                    numberDecoration: BoxDecoration(
                      color: context.templateColors.tabbarActive,
                      borderRadius: BorderRadius.circular(
                        UiConstants.borderRadius4,
                      ),
                    ),
                    spacing: 8.0,
                    onFinished: () {
                      // 倒计时结束后的处理
                      debugPrint('新币上市倒计时结束: ${data.symbol}');
                    },
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: UiConstants.spacing4),
          Row(
            children: [
              // 上线时间
              Text(
                '上线时间 ${data.launchTime}',
                style: context.templateStyle.text.descriptionText.copyWith(
                  fontSize: 13,
                ),
              ),
              Spacer(),

              // 分页指示器
              Text(
                '${data.currentPage}/${data.totalPages}',
                style: context.templateStyle.text.hintTextMedium,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
