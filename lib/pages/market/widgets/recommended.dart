/*
  推荐关注
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/display/image_widget.dart';
import 'package:remixicon/remixicon.dart';

// 推荐用户数据模型
class RecommendedUser {
  final String id;
  final String name;
  final String avatar;
  final String descriptionKey; // 多语言键
  final bool isFollowing;

  RecommendedUser({
    required this.id,
    required this.name,
    required this.avatar,
    required this.descriptionKey,
    this.isFollowing = false,
  });

  RecommendedUser copyWith({bool? isFollowing}) {
    return RecommendedUser(
      id: id,
      name: name,
      avatar: avatar,
      descriptionKey: descriptionKey,
      isFollowing: isFollowing ?? this.isFollowing,
    );
  }
}

class Recommended extends StatefulWidget {
  const Recommended({super.key});

  @override
  State<Recommended> createState() => _RecommendedState();
}

class _RecommendedState extends State<Recommended> {
  // 模拟推荐用户数据
  final List<RecommendedUser> _recommendedUsers = [
    RecommendedUser(
      id: '1',
      name: '<PERSON>',
      avatar: 'touxiang',
      descriptionKey: 'community_active_user',
    ),
    RecommendedUser(
      id: '2',
      name: 'Sarah <PERSON>',
      avatar: 'touxiang',
      descriptionKey: 'trading_expert',
    ),
    RecommendedUser(
      id: '3',
      name: 'Michael Zhang',
      avatar: 'touxiang',
      descriptionKey: 'market_analyst',
    ),
    RecommendedUser(
      id: '4',
      name: 'Lisa Liu',
      avatar: 'touxiang',
      descriptionKey: 'crypto_enthusiast',
    ),
    RecommendedUser(
      id: '5',
      name: 'David Kim',
      avatar: 'touxiang',
      descriptionKey: 'investment_advisor',
    ),
  ];

  // 切换关注状态
  void _toggleFollow(String userId) {
    setState(() {
      final index = _recommendedUsers.indexWhere((user) => user.id == userId);
      if (index != -1) {
        _recommendedUsers[index] = _recommendedUsers[index].copyWith(
          isFollowing: !_recommendedUsers[index].isFollowing,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(UiConstants.spacing12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: context.templateColors.tabbarBackground,
            width: 6,
          ),
        ),
      ),
      child: Column(children: [_buildHeader(), _buildList()]),
    );
  }

  // 标题 & 更多按钮
  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.only(bottom: UiConstants.spacing24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text('推荐关注', style: context.templateStyle.text.h4),
          Spacer(),
          Text('更多排行', style: context.templateStyle.text.descriptionText),
          Icon(
            RemixIcons.arrow_right_s_line,
            color: context.templateColors.textTertiary,
            size: 18,
          ),
        ],
      ),
    );
  }

  // 推荐关注列表
  Widget _buildList() {
    // 只显示前3个用户
    final displayUsers = _recommendedUsers.take(3).toList();

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: displayUsers.map((user) => _buildUserItem(user)).toList(),
    );
  }

  // 构建单个用户项
  Widget _buildUserItem(RecommendedUser user) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(bottom: UiConstants.spacing20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ImageWidget(
            name: user.avatar,
            width: 40,
            height: 40,
            radius: UiConstants.borderRadius10,
          ),
          Padding(
            padding: EdgeInsets.only(left: UiConstants.spacing12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.name,
                  style: context.templateStyle.text.bodyLargeMedium,
                ),
                SizedBox(height: UiConstants.spacing4),
                Text('社区活跃用户', style: context.templateStyle.text.hintText),
              ],
            ),
          ),
          Spacer(),
          SizedBox(
            height: 30,
            child: TextButton(
              onPressed: () => _toggleFollow(user.id),
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(
                  horizontal: UiConstants.spacing12,
                  vertical: UiConstants.spacing2,
                ),
                backgroundColor:
                    user.isFollowing
                        ? context.templateColors.primary.withValues(alpha: 0.1)
                        : context.templateColors.inputBackground,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    UiConstants.borderRadiusCircle,
                  ),
                ),
                splashFactory: NoSplash.splashFactory,
              ),
              child: Text(
                user.isFollowing ? '已关注' : '关注',
                style: context.templateStyle.text.hintText.copyWith(
                  color:
                      user.isFollowing
                          ? context.templateColors.primary
                          : context.templateColors.textSecondary,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
