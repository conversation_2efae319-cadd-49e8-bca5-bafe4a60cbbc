/*

  行情顶部搜索
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/pages/market/widgets/index.dart';
import 'package:qubic_exchange/routes/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:super_tooltip/super_tooltip.dart';

class SearchBarView extends StatefulWidget {
  const SearchBarView({super.key});

  @override
  State<SearchBarView> createState() => _SearchBarViewState();
}

class _SearchBarViewState extends State<SearchBarView> {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: context.templateColors.surface,
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing16,
        vertical: UiConstants.spacing8,
      ),
      child: Row(
        children: [
          // 搜索框
          Expanded(
            child: GestureDetector(
              onTap:
                  () => NavigationService().navigateTo(AppRoutes.searchCrypto),
              child: Container(
                height: 36,
                padding: EdgeInsets.symmetric(
                  horizontal: UiConstants.spacing12,
                ),
                decoration: BoxDecoration(
                  color: context.templateColors.inputBackground,
                  borderRadius: BorderRadius.circular(
                    UiConstants.borderRadius8,
                  ),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    ThemedImage.asset('icon_search', width: 18, height: 18),
                    SizedBox(width: UiConstants.spacing8),
                    Text(
                      '搜索币种',
                      style: context.templateStyle.text.descriptionText,
                    ),
                  ],
                ),
              ),
            ),
          ),
          SizedBox(width: UiConstants.spacing16),
          // 更多按钮
          DropdownMenuWidget(
            offset: EdgeInsets.only(top: 10),
            buttonWidget: ThemedImage.asset(
              'more',
              width: 20,
              height: 20,
              followTheme: true,
            ),
            onTap: () => {},
            enableAutoAdjust: true,
            direction: TooltipDirection.down,
            childBuilder: _buildToolTipMenuList,
          ),
        ],
      ),
    );
  }

  // 构建气泡框菜单列表
  Widget _buildToolTipMenuList(VoidCallback closeMenu) {
    // 菜单项
    Widget buildMenuItem({required String text, required VoidCallback onTap}) {
      return InkWellWidget(
        onTap: () {
          onTap(); // 执行菜单项的操作
          closeMenu(); // 关闭菜单
        },
        child: IntrinsicWidth(
          child: Container(
            constraints: BoxConstraints(minWidth: 80),
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.symmetric(
              horizontal: UiConstants.spacing6,
              vertical: UiConstants.spacing10,
            ),
            child: Text(
              text,
              style: context.templateStyle.text.descriptionText,
              textAlign: TextAlign.start,
            ),
          ),
        ),
      );
    }

    return SizedBox(
      child: Column(
        children: [
          buildMenuItem(
            text: '价格提醒',
            onTap: () => {NavigationService().navigateTo(AppRoutes.priceAlert)},
          ),
          buildMenuItem(
            text: 'Logo 设置',
            onTap: () => {LogoSettingDialog.show(context)},
          ),
        ],
      ),
    );
  }
}
