/*
*  行情界面专用数字货币列表组件
*
*  功能：
*  - 显示数字货币列表数据
*  - 支持吸顶排序功能
*  - 支持现货和合约模式
*  - 使用 CryptoItemData 数据模型
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hive/hive.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/utils/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/pages/market/models/crypto_item_data.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';
import 'market_sort.dart' as market_sort;
import 'new_listing_card.dart';

class MarketCryptoList extends StatefulWidget {
  final ScrollPhysics? physics; // 滚动物理效果
  final bool isContract; // 是否为合约
  final List<CryptoItemData>? cryptoDataList; // 数字货币列表数据
  final bool isNewListing; // 是否为新币上市
  final bool isUnderMaintenance; // 是否为维护中

  // 静态的Logo设置通知器
  static final ValueNotifier<bool> _logoSettingNotifier = ValueNotifier<bool>(
    false, // 默认不显示Logo
  );

  // 静态方法：更新全局Logo设置
  static void updateGlobalLogoSetting(bool showLogo) {
    _logoSettingNotifier.value = showLogo;
  }

  const MarketCryptoList({
    super.key,
    this.isContract = false,
    this.physics,
    this.cryptoDataList,
    this.isNewListing = false,
    this.isUnderMaintenance = false,
  });

  @override
  State<MarketCryptoList> createState() => _MarketCryptoListState();
}

class _MarketCryptoListState extends State<MarketCryptoList> {
  // 排序状态管理
  final Map<market_sort.SortType, market_sort.SortDirection> _sortStates = {
    market_sort.SortType.symbol: market_sort.SortDirection.none,
    market_sort.SortType.price: market_sort.SortDirection.none,
    market_sort.SortType.changePercent: market_sort.SortDirection.none,
  };

  // 排序后的数据列表
  List<CryptoItemData> _sortedDataList = [];

  // Logo显示设置
  bool _showLogo = false; // 默认不显示Logo

  @override
  void initState() {
    super.initState();
    _loadLogoSetting();
    _updateSortedData();

    // 监听全局Logo设置变化
    MarketCryptoList._logoSettingNotifier.addListener(_onLogoSettingChanged);
  }

  @override
  void dispose() {
    // 移除监听器
    MarketCryptoList._logoSettingNotifier.removeListener(_onLogoSettingChanged);
    super.dispose();
  }

  // Logo设置变化回调
  void _onLogoSettingChanged() {
    if (mounted) {
      setState(() {
        _showLogo = MarketCryptoList._logoSettingNotifier.value;
      });
    }
  }

  // 加载Logo设置
  Future<void> _loadLogoSetting() async {
    try {
      final box = await Hive.openBox('app_settings');
      final showLogo = box.get('show_crypto_logo', defaultValue: false);
      if (mounted) {
        setState(() {
          _showLogo = showLogo;
        });
        // 同步更新全局通知器
        MarketCryptoList._logoSettingNotifier.value = showLogo;
      }
    } catch (e) {
      debugPrint('加载Logo设置失败: $e');
    }
  }

  // 更新Logo设置（供外部调用）
  void updateLogoSetting(bool showLogo) {
    if (mounted && _showLogo != showLogo) {
      setState(() {
        _showLogo = showLogo;
      });
    }
  }

  @override
  void didUpdateWidget(MarketCryptoList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.cryptoDataList != oldWidget.cryptoDataList) {
      _updateSortedData();
    }
  }

  /// 更新排序后的数据
  void _updateSortedData() {
    if (widget.cryptoDataList == null) {
      _sortedDataList = [];
      return;
    }
    _sortedDataList = _applySorting(widget.cryptoDataList!);
  }

  /// 处理排序变化
  void _onSortChanged(
    market_sort.SortType sortType,
    market_sort.SortDirection direction,
  ) {
    setState(() {
      // 更新排序状态
      _sortStates[sortType] = direction;
      // 重新排序数据
      _updateSortedData();
    });
  }

  /// 应用排序逻辑
  List<CryptoItemData> _applySorting(List<CryptoItemData> data) {
    if (data.isEmpty) return data;

    // 创建数据副本以避免修改原始数据
    final sortedData = List<CryptoItemData>.from(data);

    // 找到当前激活的排序列
    market_sort.SortType? activeSortType;
    market_sort.SortDirection? activeSortDirection;

    for (final entry in _sortStates.entries) {
      if (entry.value != market_sort.SortDirection.none) {
        activeSortType = entry.key;
        activeSortDirection = entry.value;
        break;
      }
    }

    // 如果没有激活的排序，返回原始数据
    if (activeSortType == null || activeSortDirection == null) {
      return sortedData;
    }

    // 根据排序类型进行排序
    switch (activeSortType) {
      case market_sort.SortType.symbol:
        // 按币种符号排序
        sortedData.sort((a, b) {
          final comparison = a.symbol.compareTo(b.symbol);
          return activeSortDirection == market_sort.SortDirection.ascending
              ? comparison
              : -comparison;
        });
        break;

      case market_sort.SortType.price:
        // 按价格排序
        sortedData.sort((a, b) {
          final comparison = a.currentPrice.compareTo(b.currentPrice);
          return activeSortDirection == market_sort.SortDirection.ascending
              ? comparison
              : -comparison;
        });
        break;

      case market_sort.SortType.changePercent:
        // 按涨跌幅排序
        sortedData.sort((a, b) {
          final comparison = a.changePercentage.compareTo(b.changePercentage);
          return activeSortDirection == market_sort.SortDirection.ascending
              ? comparison
              : -comparison;
        });
        break;
    }

    return sortedData;
  }

  // 获取吸顶头部最大高度
  double _getMaxHeight() {
    double height = 40; // 排序组件的基础高度
    if (widget.isNewListing) {
      height += 130; // 新币卡片高度
    }
    return height;
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: widget.physics,
      slivers: [
        // 吸顶排序（包含新币卡片）
        SliverPersistentHeader(
          pinned: true,
          delegate: _CollapsibleHeaderDelegate(
            maxHeight: _getMaxHeight(),
            minHeight: 40,
            isNewListing: widget.isNewListing,
            onSortChanged: _onSortChanged,
          ),
        ),
        // 数字货币列表
        SliverList(
          delegate: SliverChildBuilderDelegate((context, index) {
            return _buildCryptoItem(index);
          }, childCount: _sortedDataList.length),
        ),
        // 底部安全区域
        SliverToBoxAdapter(
          child: SizedBox(height: ScreenUtil.bottomSafeHeight(context) * 2),
        ),
      ],
    );
  }

  // 构建数字货币项
  Widget _buildCryptoItem(int index) {
    // 使用排序后的数据
    final cryptoItem = _sortedDataList[index];
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing16,
        vertical: UiConstants.spacing10,
      ),
      child: Row(
        children: [
          if (_showLogo)
            Container(
              margin: EdgeInsets.only(right: UiConstants.spacing12),
              child: ThemedImage.crypto(
                cryptoItem.symbol.toLowerCase(),
                size: UiConstants.iconSize28,
                borderRadius: BorderRadius.circular(
                  UiConstants.borderRadiusCircle,
                ),
              ),
            ),

          /// 币种 & 成交额
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ///  交易对
                Row(
                  children: [
                    if (widget.isContract)
                      Text(
                        cryptoItem.fullSymbolContract,
                        style: context.templateStyle.text.bodyLargeMedium,
                      )
                    else
                      Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                              text: cryptoItem.symbol,
                              style: context.templateStyle.text.bodyLargeMedium,
                            ),
                            TextSpan(
                              text: ' / ${cryptoItem.quoteCurrency}',
                              style: context.templateStyle.text.hintText
                                  .copyWith(
                                    color: context.templateColors.textSecondary,
                                  ),
                            ),
                          ],
                        ),
                      ),

                    /// 标签
                    if (cryptoItem.tradingTag != null)
                      Container(
                        margin: EdgeInsets.only(left: UiConstants.spacing8),
                        padding: EdgeInsets.symmetric(
                          horizontal: UiConstants.spacing4,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                            UiConstants.borderRadius4,
                          ),
                          border: Border.all(
                            color: context.templateColors.divider,
                            width: 1,
                          ),
                          color: context.templateColors.surface,
                        ),
                        child: Text(
                          cryptoItem.tradingTag!,
                          style: context.templateStyle.text.hintText,
                        ),
                      ),
                  ],
                ),

                // 成交额
                Text(
                  cryptoItem.formattedVolume,
                  style: context.templateStyle.text.hintText.copyWith(
                    color: context.templateColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // 最新价格
          Expanded(
            flex: 1,
            child: Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                final currencySymbol =
                    authProvider.selectedCurrency?.symbol ?? '\$';
                final convertedPrice =
                    cryptoItem.currentPrice * authProvider.currentRate;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      cryptoItem.formattedPrice,
                      style: context.templateStyle.text.bodyLargeMedium,
                    ),
                    Text(
                      '$currencySymbol ${NumberFormatUtil.formatWithComma(convertedPrice, decimalDigits: 2)}',
                      style: context.templateStyle.text.hintText.copyWith(
                        color: context.templateColors.textSecondary,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
          SizedBox(width: UiConstants.spacing16),

          // 涨跌幅
          Expanded(
            flex: 1,
            child: Container(
              height: 33,
              decoration: BoxDecoration(
                color: _getChangePercentageColor(cryptoItem, context),
                borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
              ),
              alignment: Alignment.center,
              child: Text(
                _getChangePercentageText(cryptoItem),
                style: context.templateStyle.text.bodyTextMedium.copyWith(
                  color:
                      cryptoItem.isPriceFlat
                          ? context.templateColors.textSecondary
                          : Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取涨跌幅显示文本
  String _getChangePercentageText(CryptoItemData cryptoItem) {
    if (cryptoItem.isPriceFlat) {
      // 涨跌幅为0时不显示符号
      return '0.00%';
    }
    return cryptoItem.formattedChangePercentage;
  }

  /// 获取涨跌幅背景颜色
  Color _getChangePercentageColor(
    CryptoItemData cryptoItem,
    BuildContext context,
  ) {
    if (cryptoItem.isPriceFlat) {
      // 涨跌幅为0时显示灰色
      return context.templateColors.tabbarActive;
    }
    return cryptoItem.isPriceIncreasing
        ? context.templateColors.tradeBuy
        : context.templateColors.tradeSell;
  }
}

// 自定义可折叠头部代理
// 确保排序栏在吸顶时停靠在子标签栏下方，而不是穿过子标签栏
class _CollapsibleHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double maxHeight;
  final double minHeight;
  final bool isNewListing;
  final Function(market_sort.SortType, market_sort.SortDirection) onSortChanged;

  _CollapsibleHeaderDelegate({
    required this.maxHeight,
    required this.minHeight,
    required this.isNewListing,
    required this.onSortChanged,
  });

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    // 计算收缩比例 (0.0 = 完全展开, 1.0 = 完全收缩)
    final maxShrinkOffset = isNewListing ? 130.0 : 0.0;
    final shrinkRatio =
        maxShrinkOffset > 0
            ? (shrinkOffset / maxShrinkOffset).clamp(0.0, 1.0)
            : 0.0;

    return Container(
      color: context.templateColors.surface,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 新币卡片（根据收缩比例调整）
          if (isNewListing)
            SizedBox(
              height: (130 * (1 - shrinkRatio)).clamp(0.0, 130.0),
              child: ClipRect(
                child: OverflowBox(
                  alignment: Alignment.topCenter,
                  minHeight: 0,
                  maxHeight: 130,
                  child: Opacity(
                    opacity: (1 - shrinkRatio).clamp(0.0, 1.0),
                    child:
                        shrinkRatio < 0.99
                            ? NewListingCard()
                            : SizedBox.shrink(),
                  ),
                ),
              ),
            ),
          // 排序组件
          SizedBox(
            height: 40,
            child: market_sort.MarketSort(onSortChanged: onSortChanged),
          ),
        ],
      ),
    );
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return oldDelegate is! _CollapsibleHeaderDelegate ||
        oldDelegate.maxHeight != maxHeight ||
        oldDelegate.minHeight != minHeight ||
        oldDelegate.isNewListing != isNewListing;
  }
}
