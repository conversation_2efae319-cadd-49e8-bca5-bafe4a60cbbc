/*
*  自选排序栏
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

/// 排序类型枚举
enum SortType {
  symbol, // 币种/成交额
  price, // 最新价
  changePercent, // 涨跌幅
}

/// 排序方向枚举
enum SortDirection {
  none, // 默认状态（无排序）
  ascending, // 升序
  descending, // 降序
}

class MarketSort extends StatefulWidget {
  /// 排序回调函数
  final Function(SortType, SortDirection)? onSortChanged;

  /// 自定义文本
  final String? symbolText; // 币种/成交额列文本
  final String? priceText; // 最新价列文本
  final String? changePercentText; // 涨跌幅列文本

  const MarketSort({
    super.key,
    this.onSortChanged,
    this.symbolText,
    this.priceText,
    this.changePercentText,
  });

  @override
  State<MarketSort> createState() => _MarketSortState();
}

class _MarketSortState extends State<MarketSort> {
  // 当前排序状态映射
  final Map<SortType, SortDirection> _sortStates = {
    SortType.symbol: SortDirection.none,
    SortType.price: SortDirection.none,
    SortType.changePercent: SortDirection.none,
  };

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing16,
        vertical: UiConstants.spacing10,
      ),
      decoration: BoxDecoration(color: context.templateColors.surface),
      child: Row(
        children: [
          _buildSortItem(
            title: widget.symbolText ?? '币种/成交额',
            sortType: SortType.symbol,
            flex: 2,
            alignment: MainAxisAlignment.start,
          ),
          _buildSortItem(
            title: widget.priceText ?? '最新价',
            sortType: SortType.price,
            alignment: MainAxisAlignment.end,
          ),
          SizedBox(width: UiConstants.spacing16),
          _buildSortItem(
            title: widget.changePercentText ?? '涨跌幅',
            sortType: SortType.changePercent,
            alignment: MainAxisAlignment.end,
          ),
        ],
      ),
    );
  }

  /// 处理排序点击事件
  void _onSortTapped(SortType sortType) {
    setState(() {
      final currentDirection = _sortStates[sortType]!;

      // 清除其他列的排序状态
      _sortStates.updateAll(
        (key, value) => key == sortType ? value : SortDirection.none,
      );

      // 切换当前列的排序状态：默认 -> 升序 -> 降序 -> 默认
      switch (currentDirection) {
        case SortDirection.none:
          _sortStates[sortType] = SortDirection.ascending;
          break;
        case SortDirection.ascending:
          _sortStates[sortType] = SortDirection.descending;
          break;
        case SortDirection.descending:
          _sortStates[sortType] = SortDirection.none;
          break;
      }
    });

    // 触发回调
    widget.onSortChanged?.call(sortType, _sortStates[sortType]!);
  }

  /// 根据排序方向获取对应的图标名称
  String _getSortIconName(SortDirection direction) {
    switch (direction) {
      case SortDirection.none:
        return 'sort_normal';
      case SortDirection.ascending:
        return 'sort_up';
      case SortDirection.descending:
        return 'sort_down';
    }
  }

  // 排序项
  Widget _buildSortItem({
    required String title,
    required SortType sortType,
    int flex = 1,
    MainAxisAlignment alignment = MainAxisAlignment.start,
  }) {
    final sortDirection = _sortStates[sortType]!;
    final iconName = _getSortIconName(sortDirection);

    return Expanded(
      flex: flex,
      child: InkWellWidget(
        onTap: () => _onSortTapped(sortType),
        child: Row(
          mainAxisAlignment: alignment,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: context.templateStyle.text.hintText.copyWith(
                color:
                    sortDirection != SortDirection.none
                        ? context.templateColors.textPrimary
                        : context.templateColors.textSecondary,
                fontSize: 12,
              ),
            ),
            SizedBox(width: UiConstants.spacing2),
            ThemedImage.asset(
              iconName,
              followTheme: true,
              width: 12,
              height: 12,
            ),
          ],
        ),
      ),
    );
  }
}
