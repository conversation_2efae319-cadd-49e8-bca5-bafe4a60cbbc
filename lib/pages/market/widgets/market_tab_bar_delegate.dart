/*
*  市场通用吸顶代理
*
*  功能：
*  - 为市场页面提供层级感知的吸顶头部代理
*  - 支持自定义子组件和高度
*  - 可用于排序栏、标签栏等吸顶组件
*  - 支持多层级吸顶，确保每个层级停靠在正确位置
*/

import 'package:flutter/material.dart';

/// 市场通用吸顶代理
class MarketTabBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;

  MarketTabBarDelegate({required this.child, required this.height});

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return SizedBox(height: height, child: child);
  }

  @override
  double get maxExtent => height;

  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return oldDelegate != this;
  }
}

/// 市场子标签栏吸顶代理（第二层级）
///
/// 用于主标签页内的子标签栏，确保停靠在主标签栏下方
class MarketSubTabBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;
  final Color? backgroundColor;

  const MarketSubTabBarDelegate({
    required this.child,
    required this.height,
    this.backgroundColor,
  });

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(height: height, color: backgroundColor, child: child);
  }

  @override
  double get maxExtent => height;

  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    if (oldDelegate is! MarketSubTabBarDelegate) return true;

    return oldDelegate.child != child ||
        oldDelegate.height != height ||
        oldDelegate.backgroundColor != backgroundColor;
  }
}

/// 市场排序栏吸顶代理（第三层级）
///
/// 用于排序栏，确保停靠在子标签栏下方，支持可折叠效果
class MarketSortBarDelegate extends SliverPersistentHeaderDelegate {
  final double maxHeight;
  final double minHeight;
  final bool isNewListing;
  final Function(dynamic, dynamic) onSortChanged;
  final Color? backgroundColor;

  const MarketSortBarDelegate({
    required this.maxHeight,
    required this.minHeight,
    required this.isNewListing,
    required this.onSortChanged,
    this.backgroundColor,
  });

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    // 计算收缩比例 (0.0 = 完全展开, 1.0 = 完全收缩)
    final maxShrinkOffset = isNewListing ? 123.0 : 0.0;
    final shrinkRatio =
        maxShrinkOffset > 0
            ? (shrinkOffset / maxShrinkOffset).clamp(0.0, 1.0)
            : 0.0;

    return Container(
      color: backgroundColor,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 新币卡片（根据收缩比例调整）
          if (isNewListing)
            SizedBox(
              height: (123 * (1 - shrinkRatio)).clamp(0.0, 123.0),
              child: ClipRect(
                child: OverflowBox(
                  alignment: Alignment.topCenter,
                  minHeight: 0,
                  maxHeight: 123,
                  child: Opacity(
                    opacity: (1 - shrinkRatio).clamp(0.0, 1.0),
                    child:
                        shrinkRatio < 0.99
                            ? _buildNewListingCard()
                            : SizedBox.shrink(),
                  ),
                ),
              ),
            ),
          // 排序组件
          SizedBox(height: 40, child: _buildSortComponent()),
        ],
      ),
    );
  }

  // 构建新币卡片（需要在使用时传入具体实现）
  Widget _buildNewListingCard() {
    // 这里需要根据实际情况返回新币卡片
    return Container(); // 占位符
  }

  // 构建排序组件（需要在使用时传入具体实现）
  Widget _buildSortComponent() {
    // 这里需要根据实际情况返回排序组件
    return Container(); // 占位符
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    if (oldDelegate is! MarketSortBarDelegate) return true;

    return oldDelegate.maxHeight != maxHeight ||
        oldDelegate.minHeight != minHeight ||
        oldDelegate.isNewListing != isNewListing;
  }
}
