/*
*  Logo 设置底部弹窗
*/

import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/utils/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'market_crypto_list.dart';

class LogoSettingDialog {
  // 显示 Logo 设置底部弹窗
  static Future<void> show(BuildContext context) async {
    return await BottomSheetWidget.show<void>(
      context: context,
      title: 'Logo 设置',
      showCloseButton: true,
      showHeader: true,
      showDragHandle: false,
      headerHeight: 68,
      showHeaderBorder: false,
      showBottomButton: false,
      useSafeArea: false,
      child: _LogoSettingContent(),
    );
  }
}

class _LogoSettingContent extends StatefulWidget {
  const _LogoSettingContent();

  @override
  State<_LogoSettingContent> createState() => _LogoSettingContentState();
}

class _LogoSettingContentState extends State<_LogoSettingContent> {
  bool _showLogo = false; // 默认不显示Logo

  @override
  void initState() {
    super.initState();
    _loadLogoSetting();
  }

  // 加载Logo设置
  Future<void> _loadLogoSetting() async {
    try {
      final box = await Hive.openBox('app_settings');
      final showLogo = box.get('show_crypto_logo', defaultValue: false);
      if (mounted) {
        setState(() {
          _showLogo = showLogo;
        });
      }
    } catch (e) {
      debugPrint('加载Logo设置失败: $e');
    }
  }

  // 保存Logo设置
  Future<void> _saveLogoSetting(bool showLogo) async {
    try {
      final box = await Hive.openBox('app_settings');
      await box.put('show_crypto_logo', showLogo);

      if (mounted) {
        setState(() {
          _showLogo = showLogo;
        });

        // 关闭弹窗
        Navigator.of(context).pop();
      }

      // 直接通知所有MarketCryptoList组件更新Logo显示
      MarketCryptoList.updateGlobalLogoSetting(showLogo);
    } catch (e) {
      debugPrint('保存Logo设置失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(child: _buildSwichList());
  }

  // 构建选项列表
  Widget _buildSwichList() {
    return Column(
      children: [
        // 隐藏Logo选项（第一项，默认选中）
        _buildSwitchItem(
          labelText: '无 Logo 样式',
          isSelected: !_showLogo,
          onTap: () => _saveLogoSetting(false),
        ),

        // 显示Logo选项（第二项）
        _buildSwitchItem(
          labelText: '有 Logo 样式',
          isSelected: _showLogo,
          onTap: () => _saveLogoSetting(true),
        ),
        SizedBox(height: ScreenUtil.screenHeight(context) * 0.02),
      ],
    );
  }

  // 构建选项
  Widget _buildSwitchItem({
    required String labelText,
    bool isSelected = false,
    required VoidCallback onTap,
  }) {
    return InkWellWidget(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(bottom: UiConstants.spacing14),
        padding: EdgeInsets.all(UiConstants.spacing14),
        decoration: BoxDecoration(
          border: Border.all(
            width: context.templateStyles.borderWidthMedium,
            color:
                isSelected
                    ? context.templateColors.textPrimary
                    : context.templateColors.border,
          ),
          borderRadius: BorderRadius.circular(
            context.templateStyles.borderRadiusMedium,
          ),
          color: context.templateColors.popupBackground,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Text(
              labelText,
              style: context.templateStyle.text.h4.copyWith(
                color: context.templateColors.textPrimary,
              ),
            ),
            SizedBox(height: UiConstants.spacing12),
            // 示例展示
            Row(
              children: [
                // 根据选项显示不同的Logo
                if (labelText == '有 Logo 样式')
                  ThemedImage.crypto(
                    'btc',
                    size: UiConstants.iconSize24,
                    margin: EdgeInsets.only(right: UiConstants.spacing10),
                  ),
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text.rich(
                            TextSpan(
                              style: context.templateStyle.text.bodyLargeMedium,
                              children: [
                                TextSpan(text: 'BTC'),
                                TextSpan(
                                  text: '/USDT',
                                  style:
                                      context
                                          .templateStyle
                                          .text
                                          .descriptionSmall,
                                ),
                              ],
                            ),
                          ),
                          Text(
                            'Vol 88.88T',
                            style: context.templateStyle.text.descriptionSmall,
                          ),
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '60,000.00',
                            style: context.templateStyle.text.bodyLargeMedium,
                          ),
                          Text(
                            '\$ 60,000.00',
                            style: context.templateStyle.text.descriptionSmall,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(left: UiConstants.spacing16),
                  padding: EdgeInsets.symmetric(
                    horizontal: UiConstants.spacing10,
                    vertical: UiConstants.spacing8,
                  ),
                  decoration: BoxDecoration(
                    color: context.templateColors.primary,
                    borderRadius: BorderRadius.circular(
                      context.templateStyles.borderRadiusMedium,
                    ),
                  ),
                  child: Text(
                    '+100.00%',
                    style: context.templateStyle.text.bodyTextMedium.copyWith(
                      color: context.templateColors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
