/*
*  自选货币列表
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/utils/device/screen_util.dart';
import 'package:qubic_exchange/widgets/index.dart';
import '../models/crypto_item_data.dart';

class FavoritesListView extends StatefulWidget {
  final ScrollPhysics? physics;
  final List<CryptoItemData>? availableCryptoData;
  final void Function(List<CryptoItemData>)? onSelectionConfirmed;

  const FavoritesListView({
    super.key,
    this.physics,
    this.availableCryptoData,
    this.onSelectionConfirmed,
  });

  @override
  State<FavoritesListView> createState() => _FavoritesListViewState();
}

class _FavoritesListViewState extends State<FavoritesListView> {
  // 选中状态管理
  final Set<String> _selectedSymbols = <String>{};

  // 获取可用的加密货币数据
  List<CryptoItemData> get _availableItems {
    return widget.availableCryptoData ?? _getDefaultCryptoData();
  }

  // 默认示例数据
  List<CryptoItemData> _getDefaultCryptoData() {
    return [
      CryptoItemData(
        symbol: 'BTC',
        quoteCurrency: 'USDT',
        currentPrice: 43250.50,
        cnyPrice: 311244.60,
        changePercentage: 2.45,
        volume24h: 1.2,
        volumeUnit: '亿',
        tradingTag: '永续',
      ),
      CryptoItemData(
        symbol: 'ETH',
        quoteCurrency: 'USDT',
        currentPrice: 2650.30,
        cnyPrice: 19082.16,
        changePercentage: -1.23,
        volume24h: 8.5,
        volumeUnit: '万',
      ),
      CryptoItemData(
        symbol: 'BNB',
        quoteCurrency: 'USDT',
        currentPrice: 315.80,
        cnyPrice: 2273.76,
        changePercentage: 0.85,
        volume24h: 2.3,
        volumeUnit: '万',
      ),
      CryptoItemData(
        symbol: 'ADA',
        quoteCurrency: 'USDT',
        currentPrice: 0.4520,
        cnyPrice: 3.2544,
        changePercentage: 3.21,
        volume24h: 1.8,
        volumeUnit: '万',
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: UiConstants.spacing18,
        right: UiConstants.spacing18,
        top: UiConstants.spacing18,
      ),
      child: ListView(
        physics: widget.physics,
        children: [
          Column(
            children: [
              Padding(
                padding: EdgeInsets.only(bottom: UiConstants.spacing24),
                child: Wrap(
                  spacing: UiConstants.spacing8,
                  runSpacing: UiConstants.spacing8,
                  children:
                      _availableItems
                          .map((item) => _buildSelectItem(item))
                          .toList(),
                ),
              ),

              ButtonWidget(
                height: 40,
                text: '加入自选',
                width: double.infinity,
                borderRadius: UiConstants.borderRadius8,
                onPressed: () => _confirmSelection(),
              ),

              SizedBox(height: ScreenUtil.screenHeight(context) * 0.1),
            ],
          ),
        ],
      ),
    );
  }

  // 构建自选项
  Widget _buildSelectItem(CryptoItemData item) {
    final String itemKey = '${item.symbol}/${item.quoteCurrency}';
    final bool isSelected = _selectedSymbols.contains(itemKey);

    return SizedBox(
      width:
          (MediaQuery.of(context).size.width -
              UiConstants.spacing18 * 2 -
              UiConstants.spacing8) /
          2,
      child: InkWellWidget(
        onTap: () => _toggleSelection(item),
        child: Container(
          padding: EdgeInsets.symmetric(
            vertical: UiConstants.spacing14,
            horizontal: UiConstants.spacing12,
          ),
          decoration: BoxDecoration(
            border: Border.all(
              color: context.templateColors.divider,
              width: UiConstants.borderWidth0_5,
            ),
            borderRadius: BorderRadius.circular(UiConstants.borderRadius12),
            color: context.templateColors.surface,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(itemKey, style: context.templateStyle.text.bodyLargeMedium),
              CheckboxWidget(
                value: isSelected,
                size: CheckboxSize.small,
                activeColor: context.templateColors.textPrimary,
                activeFillColor: context.templateColors.textPrimary,
                onChanged: (value) => _toggleSelection(item),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 切换选中状态
  void _toggleSelection(CryptoItemData item) {
    final String itemKey = '${item.symbol}/${item.quoteCurrency}';
    setState(() {
      if (_selectedSymbols.contains(itemKey)) {
        _selectedSymbols.remove(itemKey);
      } else {
        _selectedSymbols.add(itemKey);
      }
    });
  }

  // 确认选择，返回已选中的数据
  void _confirmSelection() {
    if (widget.onSelectionConfirmed != null) {
      // 根据选中的符号找到对应的CryptoItemData
      final selectedCryptoData =
          _availableItems
              .where(
                (item) => _selectedSymbols.contains(
                  '${item.symbol}/${item.quoteCurrency}',
                ),
              )
              .toList();
      widget.onSelectionConfirmed!(selectedCryptoData);
    }
  }
}
