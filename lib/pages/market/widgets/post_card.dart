/*

  社区动态卡片
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class PostCard extends StatefulWidget {
  final String? userAvatar;
  final String userName;
  final String timeAgo;
  final String title;
  final String content;
  final List<String>? images;
  final List<Map<String, dynamic>>? priceData;
  final int likeCount;
  final int commentCount;
  final String? userId;
  final bool isLiked;
  final VoidCallback? onLike;
  final VoidCallback? onComment;
  final VoidCallback? onShare;
  final VoidCallback? onFollow;

  const PostCard({
    super.key,
    this.userAvatar,
    required this.userName,
    required this.timeAgo,
    required this.title,
    required this.content,
    this.images,
    this.priceData,
    this.likeCount = 0,
    this.commentCount = 0,
    this.userId,
    this.isLiked = false,
    this.onLike,
    this.onComment,
    this.onShare,
    this.onFollow,
  });

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: UiConstants.spacing16),
      padding: EdgeInsets.all(UiConstants.spacing16),
      decoration: BoxDecoration(
        color: context.templateColors.surface,
        border: Border(bottom: BorderSide(color: context.templateColors.tabbarBackground, width: 6)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户信息头部
          _buildUserHeader(),
          SizedBox(height: UiConstants.spacing12),
          // 标题
          _buildTitle(),
          SizedBox(height: UiConstants.spacing8),
          // 内容
          _buildContent(),
          if (widget.images != null && widget.images!.isNotEmpty) ...[
            SizedBox(height: UiConstants.spacing12),
            // 图片
            _buildImages(),
          ],
          if (widget.priceData != null && widget.priceData!.isNotEmpty) ...[
            SizedBox(height: UiConstants.spacing12),
            // 价格标签
            _buildPriceTags(),
          ],
          SizedBox(height: UiConstants.spacing16),
          // 底部操作栏
          _buildActions(),
          if (widget.userId != null) ...[
            SizedBox(height: UiConstants.spacing8),
            // 用户ID
            _buildUserId(),
          ],
        ],
      ),
    );
  }

  // 用户信息头部
  Widget _buildUserHeader() {
    return Row(
      children: [
        // 用户头像
        widget.userAvatar == null
            ? ThemedImage.asset('avatar.webp', width: 40, height: 40, borderRadius: BorderRadius.circular(UiConstants.borderRadius6))
            : ThemedImage.network(widget.userAvatar, width: 40, height: 40, borderRadius: BorderRadius.circular(UiConstants.borderRadius6)),
        //ImageWidget(name: , width: 40, height: 40, radius: UiConstants.borderRadius12),
        SizedBox(width: UiConstants.spacing12),
        // 用户名和时间
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(widget.userName, style: context.templateStyle.text.bodyLargeMedium),
              SizedBox(height: 2),
              Text(widget.timeAgo, style: context.templateStyle.text.descriptionSmall),
            ],
          ),
        ),
        // 关注按钮
        if (widget.onFollow != null)
          SizedBox(
            height: 30,
            child: TextButton(
              onPressed: widget.onFollow,
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing12, vertical: UiConstants.spacing2),
                backgroundColor: context.templateColors.inputBackground,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(UiConstants.borderRadiusCircle)),
                splashFactory: NoSplash.splashFactory,
              ),
              child: Text('关注', style: context.templateStyle.text.bodySmallMedium),
            ),
          ),
      ],
    );
  }

  // 标题
  Widget _buildTitle() {
    return Text(widget.title, style: context.templateStyle.text.h5, maxLines: 2, overflow: TextOverflow.ellipsis);
  }

  // 内容
  Widget _buildContent() {
    return Text(widget.content, style: context.templateStyle.text.descriptionText, maxLines: 3, overflow: TextOverflow.ellipsis);
  }

  // 图片显示
  Widget _buildImages() {
    if (widget.images == null || widget.images!.isEmpty) {
      return const SizedBox.shrink();
    }

    final images = widget.images!;

    // 单张图片
    if (images.length == 1) {
      return _buildSingleImage(images.first);
    }

    // 多张图片网格显示
    return _buildImageGrid(images);
  }

  // 单张图片显示
  Widget _buildSingleImage(String imageUrl) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
      child: ThemedImage.network(
        imageUrl,
        width: double.infinity,
        height: 200,
        fit: BoxFit.cover,
        placeholder: Container(
          width: double.infinity,
          height: 200,
          color: context.templateColors.inputBackground,
          child: Icon(Icons.image, size: 48, color: context.templateColors.textTertiary),
        ),
      ),
    );
  }

  // 图片网格显示
  Widget _buildImageGrid(List<String> images) {
    // 最多显示9张图片
    final displayImages = images.take(9).toList();

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: displayImages.length == 4 ? 2 : 3,
        crossAxisSpacing: UiConstants.spacing8,
        mainAxisSpacing: UiConstants.spacing8,
        childAspectRatio: 1.0,
      ),
      itemCount: displayImages.length,
      itemBuilder: (context, index) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
          child: ThemedImage.network(
            displayImages[index],
            fit: BoxFit.cover,
            placeholder: Container(
              color: context.templateColors.inputBackground,
              child: Icon(Icons.image, size: 24, color: context.templateColors.textTertiary),
            ),
          ),
        );
      },
    );
  }

  // 价格标签
  Widget _buildPriceTags() {
    return Wrap(
      spacing: UiConstants.spacing8,
      runSpacing: UiConstants.spacing8,
      children:
          widget.priceData!.map((data) {
            final symbol = data['symbol'] as String? ?? '';
            final change = data['change'] as String? ?? '';
            final isPositive = !change.startsWith('-');

            return Container(
              padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing8, vertical: UiConstants.spacing4),
              decoration: BoxDecoration(
                color:
                    isPositive
                        ? context.templateColors.tradeBuy.withValues(alpha: 0.1)
                        : context.templateColors.tradeSell.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(UiConstants.borderRadius10),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(symbol, style: context.templateStyle.text.hintTextMedium.copyWith(color: context.templateColors.textPrimary)),
                  SizedBox(width: UiConstants.spacing4),
                  Text(
                    change,
                    style: context.templateStyle.text.hintTextMedium.copyWith(
                      color: isPositive ? context.templateColors.tradeBuy : context.templateColors.tradeSell,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  // 底部操作栏
  Widget _buildActions() {
    return Row(
      children: [
        // 点赞按钮
        _buildActionButton(
          icon: widget.isLiked ? Icons.favorite : Icons.favorite_border,
          count: widget.likeCount,
          color: widget.isLiked ? context.templateColors.tradeBuy : context.templateColors.textTertiary,
          onTap: widget.onLike,
        ),
        Spacer(),
        // 评论按钮
        _buildActionButton(
          icon: Icons.chat_bubble_outline,
          count: widget.commentCount,
          color: context.templateColors.textTertiary,
          onTap: widget.onComment,
        ),
        Spacer(),
        // 分享按钮
        if (widget.onShare != null)
          GestureDetector(onTap: widget.onShare, child: Icon(Icons.share_outlined, size: 20, color: context.templateColors.textTertiary)),
        Spacer(),
        // 更多按钮
        Icon(Icons.more_horiz, size: 20, color: context.templateColors.textTertiary),
      ],
    );
  }

  // 操作按钮
  Widget _buildActionButton({required IconData icon, required int count, required Color color, VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 18, color: color),
          SizedBox(width: UiConstants.spacing4),
          Text(count.toString(), style: context.templateStyle.text.bodyTextMedium.copyWith(color: color, fontSize: 14)),
        ],
      ),
    );
  }

  // 用户ID
  Widget _buildUserId() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing12).copyWith(bottom: 0),
      child: Text(widget.userId!, style: context.templateStyle.text.descriptionText),
    );
  }
}
