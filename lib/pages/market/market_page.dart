/*
* 行情界面
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import './widgets/index.dart';
import './tabs/index.dart';

class MarketPage extends StatefulWidget {
  const MarketPage({super.key});

  @override
  State<MarketPage> createState() => _MarketPageState();
}

class _MarketPageState extends State<MarketPage> with TickerProviderStateMixin {
  // 创建标签栏控制器
  late final TabController _tabController;

  // 创建标签内容
  static const List<String> _tabKeys = ['自选', '市场', '链上交易', '机会', '观点'];

  // 各个页面的GlobalKey，用于调用刷新方法
  final GlobalKey _favoritesKey = GlobalKey();
  final GlobalKey _quotesKey = GlobalKey();
  final GlobalKey _chainTradeKey = GlobalKey();
  final GlobalKey _opportunitiesKey = GlobalKey();
  final GlobalKey _insightsKey = GlobalKey();

  // 初始化
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabKeys.length, vsync: this);
  }

  // 销毁
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    try {
      debugPrint('🔄 MarketPage开始下拉刷新...');

      // 根据当前tab调用对应页面的refreshData方法
      final currentIndex = _tabController.index;
      GlobalKey? currentKey;

      switch (currentIndex) {
        case 0: // 自选
          currentKey = _favoritesKey;
          break;
        case 1: // 市场
          currentKey = _quotesKey;
          break;
        case 2: // 链上交易
          currentKey = _chainTradeKey;
          break;
        case 3: // 机会
          currentKey = _opportunitiesKey;
          break;
        case 4: // 观点
          currentKey = _insightsKey;
          break;
      }

      if (currentKey != null) {
        final currentState = currentKey.currentState;
        if (currentState != null && currentState.mounted) {
          await (currentState as dynamic).refreshData();
        }
      }

      debugPrint('✅ MarketPage下拉刷新完成');
    } catch (e) {
      debugPrint('❌ MarketPage下拉刷新失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.templateColors.surface,
      body: SafeArea(
        child: EasyRefresh.builder(
          header: ClassicHeader(
            clamping: true,
            position: IndicatorPosition.locator,
            triggerOffset: 34,
            processedDuration: const Duration(seconds: 1),
            safeArea: false,
            showMessage: false,
            showText: false,
            maxOverOffset: 40,
          ),
          onRefresh: _onRefresh,
          childBuilder: (context, physics) {
            return ScrollConfiguration(
              behavior: const ERScrollBehavior(),
              child: ExtendedNestedScrollView(
                physics: physics,
                onlyOneScrollInBody: true,
                pinnedHeaderSliverHeightBuilder: () {
                  return 110.0; // 搜索栏 + 标签栏的总高度
                },
                headerSliverBuilder: (context, innerBoxIsScrolled) {
                  return <Widget>[
                    // 刷新指示器定位器
                    const HeaderLocator.sliver(clearExtent: false),
                    // 顶部内容
                    SliverPersistentHeader(
                      pinned: true,
                      delegate: StickyDelegate(
                        maxHeight: 110,
                        child: Column(children: [SearchBarView(), _buildTabbar()]),
                        backgroundColor: context.templateColors.surface,
                      ),
                    ),
                  ];
                },
                body: _buildTabView(physics),
              ),
            );
          },
        ),
      ),
    );
  }

  // 构建主标签栏
  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(width: UiConstants.borderWidth0_5, color: context.templateColors.divider)),
      ),
      child: TabbarWidget(
        controller: _tabController,
        tabs: _tabKeys.map((title) => TabItem(title: title)).toList(),
        labelStyle: context.templateStyle.text.bodyLargeMedium.copyWith(fontSize: UiConstants.fontSize18),
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: false,
        labelPadding: EdgeInsets.only(right: UiConstants.spacing14),
        dividerColor: Colors.transparent,
      ),
    );
  }

  // 构建主标签内容
  Widget _buildTabView(ScrollPhysics physics) {
    return TabBarView(
      controller: _tabController,
      children: [
        // 自选
        _AutomaticKeepAlive(child: FavoritesView(key: _favoritesKey, physics: physics)),

        // 市场
        _AutomaticKeepAlive(child: QuotesView(key: _quotesKey, physics: physics)),

        // 链上交易
        _AutomaticKeepAlive(child: ChainTradeView(key: _chainTradeKey, physics: physics)),

        // 机会
        _AutomaticKeepAlive(child: OpportunitiesView(key: _opportunitiesKey, physics: physics)),

        // 观点
        _AutomaticKeepAlive(child: InsightsView(key: _insightsKey, physics: physics)),
      ],
    );
  }
}

// 自动保持状态的包装器
class _AutomaticKeepAlive extends StatefulWidget {
  final Widget child;

  const _AutomaticKeepAlive({required this.child});

  @override
  State<_AutomaticKeepAlive> createState() => _AutomaticKeepAliveState();
}

class _AutomaticKeepAliveState extends State<_AutomaticKeepAlive> with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => true;
}
