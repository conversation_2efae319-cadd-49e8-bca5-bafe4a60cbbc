/*
  === 理财界面 ===
*/
import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:qubic_exchange/pages/wealth/widgets/export_widgets.dart';
import 'package:qubic_exchange/widgets/index.dart';

class WealthPage extends StatefulWidget {
  const WealthPage({super.key});

  @override
  State<WealthPage> createState() => _WealthPageState();
}

class _WealthPageState extends State<WealthPage> {
  // 初始化
  @override
  void initState() {
    super.initState();
  }

  // 销毁
  @override
  void dispose() {
    super.dispose();
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    // 模拟刷新操作
    await Future.delayed(const Duration(seconds: 2));
    // 这里可以添加实际的数据刷新逻辑
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.templateColors.surface,
      body: SafeArea(
        child: EasyRefresh.builder(
          header: ClassicHeader(
            clamping: true,
            position: IndicatorPosition.locator,
            triggerOffset: 34,
            processedDuration: const Duration(seconds: 1),
            safeArea: false,
            showMessage: false,
            showText: false,
            maxOverOffset: 40,
          ),
          onRefresh: _onRefresh,
          childBuilder: (context, physics) {
            return ScrollConfiguration(
              behavior: const ERScrollBehavior(),
              child: ExtendedNestedScrollView(
                physics: physics,
                onlyOneScrollInBody: true,
                pinnedHeaderSliverHeightBuilder: () {
                  return 53.0;
                },
                headerSliverBuilder: (context, innerBoxIsScrolled) {
                  return <Widget>[
                    // 刷新指示器定位器
                    const HeaderLocator.sliver(clearExtent: false),
                    // 吸顶 TabBar
                    SliverPersistentHeader(
                      pinned: true,
                      delegate: StickyDelegate(
                        maxHeight: 53,
                        child: WealthSearch(),
                        backgroundColor: context.templateColors.surface,
                      ),
                    ),
                  ];
                },
                body: Container(),
              ),
            );
          },
        ),
      ),
    );
  }
}
