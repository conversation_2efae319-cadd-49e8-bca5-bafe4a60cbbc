/*
*  理财搜索
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class WealthSearch extends StatefulWidget {
  const WealthSearch({super.key});

  @override
  State<WealthSearch> createState() => _WealthSearchState();
}

class _WealthSearchState extends State<WealthSearch> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: TextFieldWidget(
        hintText: '搜索',
        suffixIcon: Padding(
          padding: EdgeInsets.only(left: UiConstants.spacing10),
          child: ThemedImage.asset('icon_search', width: 18, height: 18),
        ),
      ),
    );
  }
}
