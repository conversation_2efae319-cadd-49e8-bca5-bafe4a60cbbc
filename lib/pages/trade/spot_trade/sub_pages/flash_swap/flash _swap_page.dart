/*
*  闪兑
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class FlashSwapPage extends StatefulWidget {
  const FlashSwapPage({super.key});

  @override
  State<FlashSwapPage> createState() => _FlashSwapPageState();
}

class _FlashSwapPageState extends State<FlashSwapPage> {
  // 消耗币种
  String _consumeCoin = 'USDT';
  // 获得币种
  String _receiveCoin = 'BTC';
  // 消耗数量控制器
  final TextEditingController _consumeController = TextEditingController();
  // 获得数量控制器
  final TextEditingController _receiveController = TextEditingController();

  @override
  void dispose() {
    _consumeController.dispose();
    _receiveController.dispose();
    super.dispose();
  }

  // 交换消耗和获得的币种
  void _swapCoins() {
    setState(() {
      // 交换币种
      final tempCoin = _consumeCoin;
      _consumeCoin = _receiveCoin;
      _receiveCoin = tempCoin;

      // 清空输入
      _consumeController.clear();
      _receiveController.clear();
    });
  }

  // 计算获得数量（模拟计算）
  void _calculateReceiveAmount() {
    final consumeAmount = double.tryParse(_consumeController.text) ?? 0.0;
    if (consumeAmount > 0) {
      // 这里应该调用实际的汇率API，现在用模拟数据
      final rate = _getExchangeRate(_consumeCoin, _receiveCoin);
      final receiveAmount = consumeAmount * rate;
      setState(() {
        _receiveController.text = receiveAmount.toStringAsFixed(8);
      });
    } else {
      setState(() {
        _receiveController.clear();
      });
    }
  }

  // 反向计算消耗数量（根据获得数量计算）
  void _calculateConsumeAmount() {
    final receiveAmount = double.tryParse(_receiveController.text) ?? 0.0;
    if (receiveAmount > 0) {
      // 反向计算：获得数量 / 汇率 = 消耗数量
      final rate = _getExchangeRate(_consumeCoin, _receiveCoin);
      if (rate > 0) {
        final consumeAmount = receiveAmount / rate;
        setState(() {
          _consumeController.text = consumeAmount.toStringAsFixed(8);
        });
      }
    } else {
      setState(() {
        _consumeController.clear();
      });
    }
  }

  // 获取汇率（模拟数据）
  double _getExchangeRate(String fromCoin, String toCoin) {
    // 模拟汇率数据
    final rates = {
      'USDT_BTC': 0.000023,
      'BTC_USDT': 43250.0,
      'USDT_ETH': 0.00041,
      'ETH_USDT': 2450.0,
      'BTC_ETH': 17.8,
      'ETH_BTC': 0.056,
    };
    return rates['${fromCoin}_$toCoin'] ?? 1.0;
  }

  // 可选择的币种列表
  final List<String> _availableCoins = ['USDT', 'BTC', 'ETH', 'BNB'];

  // 显示币种选择弹窗
  void _showCoinSelector({required bool isConsume}) async {
    final selectedCoin = await showModalBottomSheet<String>(
      context: context,
      builder: (context) {
        return Container(
          padding: EdgeInsets.all(UiConstants.spacing16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '选择${isConsume ? '消耗' : '获得'}币种',
                style: context.templateStyle.text.h4,
              ),
              SizedBox(height: UiConstants.spacing16),
              ..._availableCoins.map((coin) {
                return ListTile(
                  leading: ThemedImage(name: coin, size: 32),
                  title: Text(
                    coin,
                    style: context.templateStyle.text.bodyLargeMedium,
                  ),
                  onTap: () => Navigator.pop(context, coin),
                );
              }),
            ],
          ),
        );
      },
    );

    if (selectedCoin != null) {
      setState(() {
        if (isConsume) {
          _consumeCoin = selectedCoin;
        } else {
          _receiveCoin = selectedCoin;
        }
        _consumeController.clear();
        _receiveController.clear();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.all(UiConstants.spacing18),
        child: Column(
          children: [
            // 标题栏
            _buildHeader(),
            // 兑换表单
            _buildSwapForm(),
            // 兑换率 & 按钮
            _buildRateAndButton(),
          ],
        ),
      ),
    );
  }

  // 构建标题栏
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing18),
      child: Row(
        children: [
          Text('现货账户', style: context.templateStyle.text.bodyLargeMedium),
          Container(
            margin: EdgeInsets.only(left: UiConstants.spacing12),
            padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
            decoration: BoxDecoration(
              border: Border.all(color: context.templateColors.primary),
              borderRadius: BorderRadius.circular(UiConstants.borderRadius4),
            ),
            child: Text(
              '0 手续费',
              style: context.templateStyle.text.tagText.copyWith(
                color: context.templateColors.primary,
              ),
            ),
          ),
          Spacer(),

          // 常见问题
          Padding(
            padding: EdgeInsets.only(right: UiConstants.spacing16),
            child: InkWellWidget(
              child: ThemedImage(
                name: 'strategy_guide',
                size: 24,
                followTheme: true,
              ),
            ),
          ),

          // 历史订单
          InkWellWidget(
            child: ThemedImage(
              name: 'orderHistory',
              size: 24,
              followTheme: true,
            ),
          ),
        ],
      ),
    );
  }

  // 构建兑换表单
  Widget _buildSwapForm() {
    /// 构建通用的交易项组件
    Widget buildTradeItem({
      required String title,
      required String coinSymbol,
      required String hintText,
      String? warningText,
      bool isInput = true,
      VoidCallback? onCoinTap,
      TextEditingController? controller,
      Function(String)? onChanged,
    }) {
      return Container(
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing18,
          vertical: UiConstants.spacing14,
        ),
        decoration: BoxDecoration(
          color: context.templateColors.cardBackground,
          borderRadius: BorderRadius.circular(UiConstants.borderRadius14),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题文本
            Text(title, style: context.templateStyle.text.descriptionText),
            // 货币选择按钮 & 数量输入
            Row(
              children: [
                InkWellWidget(
                  onTap: onCoinTap,
                  child: Row(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(right: UiConstants.spacing4),
                        child: ThemedImage(
                          name: coinSymbol,
                          size: 26,
                          folder: ThemedAssetFolder.crypto,
                          borderRadius: BorderRadius.circular(
                            UiConstants.borderRadiusCircle,
                          ),
                          margin: EdgeInsets.only(right: UiConstants.spacing4),
                        ),
                      ),
                      Text(
                        coinSymbol,
                        style: context.templateStyle.text.bodyLargeMedium,
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: UiConstants.spacing4),
                        child: ThemedImage(
                          name: 'arrow_triangle_down',
                          size: UiConstants.iconSize10,
                          followTheme: true,
                        ),
                      ),
                    ],
                  ),
                ),

                Expanded(
                  child:
                      isInput
                          ? TextFieldWidget(
                            height: 68,
                            controller: controller,
                            hintText: hintText,
                            hintStyle: context.templateStyle.text.h3.copyWith(
                              color: context.templateColors.textTertiary,
                            ),
                            textStyle: context.templateStyle.text.h3,
                            textAlign: TextAlign.end,
                            onChanged: onChanged,
                            focusBorderColor: Colors.transparent,
                            borderColor: context.templateColors.cardBackground,
                            fillColor: context.templateColors.cardBackground,
                            keyboardType: TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                          )
                          : Text(
                            hintText,
                            textAlign: TextAlign.end,
                            style: context.templateStyle.text.h3,
                          ),
                ),
              ],
            ),
            // 提示文本
            SizedBox(
              width: double.infinity,
              child: Text(
                warningText ?? '',
                textAlign: TextAlign.end,
                style: context.templateStyle.text.hintText.copyWith(
                  color: context.templateColors.tradeSell,
                ),
              ),
            ),
          ],
        ),
      );
    }

    /// 构建交换按钮
    Widget _buildSwapButton() {
      return InkWellWidget(
        onTap: _swapCoins, // 点击让消耗与获得交换位置
        child: Container(
          width: 35,
          height: 35,
          decoration: BoxDecoration(
            color: context.templateColors.surface,
            borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
          ),
          child: ThemedImage(name: 'swap', size: 16, followTheme: true),
        ),
      );
    }

    return Stack(
      children: [
        Column(
          children: [
            buildTradeItem(
              title: '消耗',
              hintText: '0.01～1000', // 显示最小数量～最大数量
              coinSymbol: _consumeCoin,
              isInput: true,
              controller: _consumeController,
              onChanged: (value) => _calculateReceiveAmount(),
              onCoinTap: () => _showCoinSelector(isConsume: true),
            ),
            SizedBox(height: UiConstants.spacing16),
            buildTradeItem(
              title: '获得',
              hintText: '0.00001～10', // 显示最小数量～最大数量
              coinSymbol: _receiveCoin,
              isInput: true, // 改为可输入
              controller: _receiveController,
              onChanged: (value) => _calculateConsumeAmount(), // 反向计算消耗数量
              onCoinTap: () => _showCoinSelector(isConsume: false),
            ),
          ],
        ),

        Positioned(
          top: 123,
          left: 0,
          right: 0,
          child: Center(child: _buildSwapButton()),
        ),
      ],
    );
  }

  // 构建兑换率 & 按钮
  Widget _buildRateAndButton() {
    final rate = _getExchangeRate(_consumeCoin, _receiveCoin);
    return Container(
      padding: EdgeInsets.only(top: UiConstants.spacing32),
      child: Column(
        children: [
          // 兑换率
          Text(
            '兑换率: 1 $_consumeCoin = ${rate.toStringAsFixed(8)} $_receiveCoin',
            style: context.templateStyle.text.descriptionText,
          ),
          SizedBox(height: UiConstants.spacing14),
          // 按钮
          CommonButton.primary(
            '兑换',
            onPressed: () {
              // TODO: 实现兑换逻辑
            },
            height: 48,
            width: double.infinity,
          ),
        ],
      ),
    );
  }
}
