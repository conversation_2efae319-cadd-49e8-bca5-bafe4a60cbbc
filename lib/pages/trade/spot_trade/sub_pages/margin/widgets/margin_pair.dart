/*
* 杠杆交易对
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class MarginPair extends StatefulWidget {
  const MarginPair({super.key});

  @override
  State<MarginPair> createState() => _MarginPairState();
}

class _MarginPairState extends State<MarginPair> {
  @override
  Widget build(BuildContext context) {
    return _buildPair();
  }

  // 构建交易对信息
  Widget _buildPair() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing10,
      ),
      child: Row(
        children: [
          InkWellWidget(
            onTap:
                () => {
                  // TODO 打开交易对选择弹窗
                },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 交易对名称
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text('BTCUSDT', style: context.templateStyle.text.h4),
                    Padding(
                      padding: EdgeInsets.only(left: UiConstants.spacing4),
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: UiConstants.spacing4,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: context.templateColors.primary,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(
                            UiConstants.borderRadius4,
                          ),
                        ),
                        child: Text(
                          '10x',
                          style: context.templateStyle.text.hintText.copyWith(
                            color: context.templateColors.primary,
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: UiConstants.spacing4),
                      child: ThemedImage(
                        name: 'arrow_triangle_down',
                        size: 16,
                        followTheme: true,
                      ),
                    ),
                  ],
                ),

                // 标签 & 涨跌幅
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      '+0.50%',
                      style: context.templateStyle.text.hintTextMedium.copyWith(
                        color: context.templateColors.tradeBuy,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          Spacer(),
          Row(
            children: [
              Padding(
                padding: EdgeInsets.only(right: UiConstants.spacing16),
                child: Row(
                  children: [
                    Text(
                      '0.00',
                      style: context.templateStyle.text.descriptionTextMedium
                          .copyWith(color: context.templateColors.primary),
                    ),
                  ],
                ),
              ),
              // k线图按钮
              InkWellWidget(
                onTap:
                    () => {
                      // TODO 跳转至k线图界面
                    },
                child: ThemedImage(
                  name: 'strategy_kline',
                  size: 24,
                  followTheme: true,
                ),
              ),
              InkWellWidget(
                onTap:
                    () => {
                      // TODO 显示更多操作菜单
                    },
                child: Padding(
                  padding: EdgeInsets.only(left: UiConstants.spacing16),
                  child: ThemedImage(name: 'more', size: 24, followTheme: true),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
