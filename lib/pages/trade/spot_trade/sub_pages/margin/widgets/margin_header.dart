/*
*  杠杆头部
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/widgets/trade_common/index.dart';

class MarginHeader extends StatefulWidget {
  const MarginHeader({super.key});

  @override
  State<MarginHeader> createState() => _MarginHeaderState();
}

class _MarginHeaderState extends State<MarginHeader> {
  // 表单高度（仅计算当前显示的组件）
  double _formHeight = 400.0; // 设置合理的初始高度

  // 表单的 GlobalKey，用于获取实际渲染高度
  final GlobalKey _formKey = GlobalKey();

  // 构建仓位模式
  final List<String> _positionActions = ['买入', '卖出'];
  // 当前选中的仓位索引
  int _selectedPositionIndex = 0;

  // 构建订单类型
  final List<OrderTypeOption> _orderTypes = [
    OrderTypeOption(text: '限价单', index: 0),
    OrderTypeOption(text: '市价单', index: 1),
    OrderTypeOption(text: '止盈止损', index: 2),
    OrderTypeOption(text: '高级限价单', index: 3),
    OrderTypeOption(text: 'OCO', index: 4),
  ];
  // 当前选中的订单类型
  OrderTypeOption? _selectedOrderType;
  // 当前选中的订单索引
  int _selectedOrderIndex = 0;

  // 当前选中的订单策略
  OrderStrategyOption? _selectedOrderStrategy;

  // 构建订单策略选项（用于高级限价单）
  final List<OrderStrategyOption> _orderStrategies = [
    OrderStrategyOption(
      text: '只做 Maker',
      description: '该委托单始终为挂单状态。若系统判断该委托单会立即与已存在的委托单成交时，则自动取消该委托单。',
      index: 0,
    ),
    OrderStrategyOption(text: 'IOC', description: '立即成交并取消未成交部分。', index: 1),
    OrderStrategyOption(text: 'FOK', description: '立即全部成交，否则自动撤单。', index: 2),
  ];

  // 初始化
  @override
  void initState() {
    super.initState();
    // 初始化选中的订单类型为第一项
    _selectedOrderType = _orderTypes.first;
    // 初始化选中的订单索引为第一项
    _selectedOrderIndex = _orderTypes.first.index;
    // 初始化选中的订单策略为第一项
    _selectedOrderStrategy = _orderStrategies.first;
    // 在下一帧获取表单高度
    WidgetsBinding.instance.addPostFrameCallback((_) => _updateFormHeight());
  }

  // 更新表单高度
  void _updateFormHeight() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_formKey.currentContext != null) {
        final RenderBox renderBox =
            _formKey.currentContext!.findRenderObject() as RenderBox;
        final newHeight = renderBox.size.height;
        if (newHeight != _formHeight && newHeight > 0) {
          setState(() {
            _formHeight = newHeight;
          });
        }
      }
    });
  }

  // 销毁
  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: UiConstants.spacing18,
        right: UiConstants.spacing18,
        bottom: UiConstants.spacing10,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(flex: 5, child: _buildForm()),
          SizedBox(width: UiConstants.spacing16),
          Expanded(
            flex: 4,
            child: DepthBookView(
              height: _formHeight,
              isContract: false,
              marketType: 1,
              symbol: 'BTCUSDT', // 临时使用固定值，后续可以从父组件传递
            ),
          ),
        ],
      ),
    );
  }

  // 构建表单
  Widget _buildForm() {
    return Column(
      key: _formKey, // 添加 GlobalKey 用于获取高度
      children: [
        // 杠杆模式选择器
        MarginModeSelector(),

        // 仓位模式
        PositionActiontoggle(
          tabKeys: _positionActions,
          initialIndex: _selectedPositionIndex,
          selectedColor: Colors.white,
          indicatorColor:
              _selectedPositionIndex == 0
                  ? context.templateColors.tradeBuy
                  : context.templateColors.tradeSell,
          onChanged: (index) {
            setState(() {
              _selectedPositionIndex = index;
            });
          },
        ),

        // 订单类型
        OrderTypeToggle(
          orderTypes: _orderTypes,
          selectedOption: _selectedOrderType,
          onChanged: (option) {
            setState(() {
              _selectedOrderType = option;
              _selectedOrderIndex = option.index;
            });
            // 当订单类型改变时，重新计算表单高度
            _updateFormHeight();
          },
        ),

        // 动态内容区域
        _buildDynamicContent(),

        // 可用
        AvailableInfo(),

        // 可开
        OpenCloseInfo(),

        // 借款
        OpenCloseInfo(text: '借款'),

        // 按钮
        _selectedPositionIndex == 0
            ? CommonButton.buy(
              '登录',
              size: CommonButtonSize.medium,
              width: double.infinity,
              onPressed: () {},
            )
            : CommonButton.sell(
              '登录',
              size: CommonButtonSize.medium,
              width: double.infinity,
              onPressed: () {},
            ),
      ],
    );
  }

  // 构建动态内容
  Widget _buildDynamicContent() {
    switch (_selectedOrderIndex) {
      case 0: // 限价单
        return Column(
          children: [
            // 价格
            PriceInput(),
            // 数量
            QuantityInput(isButton: false),
            // 杠杆滑块
            LeverageSelector(),
            // 交易额
            TradeAmountInput(isButton: false),
          ],
        );
      case 1: // 市价单
        return Column(
          children: [
            // 市价成交
            MarketOrder(),
            // 交易额
            TradeAmountInput(),
          ],
        );
      case 2: // 止盈止损
        return Column(
          children: [
            // 触发价格
            TriggerPrice(isContract: false),
            // 价格
            PriceInput(showBBO: true),
            // 数量
            QuantityInput(isButton: false),
            // 杠杆滑块
            LeverageSelector(),
            // 交易额
            TradeAmountInput(),
          ],
        );
      case 3: // 高级限价单
        return Column(
          children: [
            // 订单策略
            OrderStrategyOptionsToggle(
              options: _orderStrategies,
              selectedOption: _selectedOrderStrategy,
              onChanged: (option) {
                setState(() {
                  _selectedOrderStrategy = option;
                });
              },
            ),
            // 价格
            PriceInput(showBBO: false, showIncrementButtons: true),
            // 数量
            QuantityInput(isButton: false),
            // 杠杆滑块
            LeverageSelector(),
            // 交易额
            TradeAmountInput(isButton: false),
          ],
        );
      case 4: // OCO
        return Column(
          children: [
            // 限价
            LimitPriceInput(
              padding: EdgeInsets.only(top: UiConstants.spacing8),
            ),
            // 触发价格
            TriggerPrice(isContract: false),
            // 委托价格
            OrderPriceInput(),
            // 数量
            QuantityInput(isButton: false),
            // 杠杆滑块
            LeverageSelector(),
            // 交易额
            TradeAmountInput(isButton: false),
          ],
        );
      default:
        return Container(); // 默认返回空容器
    }
  }
}
