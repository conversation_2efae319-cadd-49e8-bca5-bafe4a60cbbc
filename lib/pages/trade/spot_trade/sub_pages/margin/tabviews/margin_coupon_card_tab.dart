/*
*  体验券标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class MarginCouponCardTab extends StatefulWidget {
  final ScrollPhysics? physics;

  const MarginCouponCardTab({super.key, this.physics});

  @override
  State<MarginCouponCardTab> createState() => _MarginCouponCardTabState();
}

class _MarginCouponCardTabState extends State<MarginCouponCardTab>
    with TickerProviderStateMixin {
  // 当前选中的标签索引
  int _selectedIndex = 0;

  // 创建子标签控制器
  late TabController _subController;

  // 创建标签内容
  static const List<String> _subKeys = ['资产', '持仓', '体验券历史'];

  // 初始化
  @override
  void initState() {
    super.initState();
    _subController = TabController(length: _subKeys.length, vsync: this);
  }

  // 销毁
  @override
  void dispose() {
    _subController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: widget.physics,
      slivers: [
        // 标签栏部分 - 吸顶
        SliverPersistentHeader(
          pinned: true,
          delegate: StickyDelegate(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [_buildSubTabbar(), _buildOnlyCurrent()],
            ),
            maxHeight: 72,
            backgroundColor: context.templateColors.surface,
          ),
        ),

        // 子标签页内容
        SliverFillRemaining(
          fillOverscroll: false,
          hasScrollBody: false,
          child: _buildSubTabView(),
        ),
      ],
    );
  }

  // 构建只看当前
  Widget _buildOnlyCurrent() {
    return Container(
      color: context.templateColors.surface,
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing16,
        vertical: UiConstants.spacing4,
      ),
      child: Row(
        children: [
          // 只看当前按钮
          InkWellWidget(
            onTap: () => {},
            child: Row(
              children: [
                Checkbox(value: false, onChanged: (value) {}),
                SizedBox(width: UiConstants.spacing4),
                Text(
                  '只看当前',
                  style: context.templateStyle.text.bodyText.copyWith(
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),

          Spacer(),
          // 全部撤销
          Padding(
            padding: EdgeInsets.only(right: UiConstants.spacing12),
            child: InkWellWidget(
              child: Icon(
                RemixIcons.sort_desc,
                size: 18,
                color: context.templateColors.textSecondary,
              ),
            ),
          ),
          CommonButton.secondary(
            '规则说明',
            size: CommonButtonSize.small,
            height: 26,
            onPressed: () => {},
          ),
        ],
      ),
    );
  }

  // 构建子标签栏
  Widget _buildSubTabbar() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: TabbarWidget(
        height: 22,
        controller: _subController,
        tabs: _subKeys.map((key) => TabItem(title: key)).toList(),
        labelStyle: context.templateStyle.text.hintTextMedium,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: true,
        indicatorStyle: TabBarIndicatorStyle.filled,
        labelPadding: EdgeInsets.zero,
        tabPadding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
      ),
    );
  }

  // 构建子标签页
  Widget _buildSubTabView() {
    // 根据选中的索引显示不同的内容
    switch (_selectedIndex) {
      case 0:
        // 资产
        return Container(
          padding: EdgeInsets.all(UiConstants.spacing16),
          alignment: Alignment.center,
          child: EmptyWidget(text: '暂无资产数据'),
        );
      case 1:
        // 持仓
        return Container(
          padding: EdgeInsets.all(UiConstants.spacing16),
          alignment: Alignment.center,
          child: EmptyWidget(text: '暂无持仓数据'),
        );
      case 2:
        // 体验券记录
        return Container(
          padding: EdgeInsets.all(UiConstants.spacing16),
          alignment: Alignment.center,
          child: EmptyWidget(text: '暂无体验券历史'),
        );
      default:
        return Container();
    }
  }
}
