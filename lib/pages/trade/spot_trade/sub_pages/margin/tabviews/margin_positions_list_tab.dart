/*
*  持仓标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/pages/trade/contract_trade/sub_pages/contract/widgets/contract_sticky.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class MarginPositionsListTab extends StatefulWidget {
  final ScrollPhysics? physics;

  const MarginPositionsListTab({super.key, this.physics});

  @override
  State<MarginPositionsListTab> createState() => _MarginPositionsListTabState();
}

class _MarginPositionsListTabState extends State<MarginPositionsListTab> {
  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: widget.physics,
      slivers: [
        // 只看当前部分 - 吸顶
        SliverPersistentHeader(
          pinned: true,
          delegate: SimpleContractStickyDelegate(
            child: _buildOnlyCurrent(),
            height: 40,
            backgroundColor: context.templateColors.surface,
          ),
        ),
        // 委托列表
        SliverList(
          delegate: SliverChildBuilderDelegate((context, index) {
            return Container(
              height: 60,
              margin: EdgeInsets.symmetric(
                horizontal: UiConstants.spacing16,
                vertical: UiConstants.spacing4,
              ),
              padding: EdgeInsets.all(UiConstants.spacing12),
              decoration: BoxDecoration(
                color: context.templateColors.inputBackground,
                borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
              ),
              child: Center(
                child: Text(
                  '委托项目 ${index + 1}',
                  style: context.templateStyle.text.bodyText,
                ),
              ),
            );
          }, childCount: 10),
        ),
      ],
    );
  }

  // 构建只看当前
  Widget _buildOnlyCurrent() {
    return Container(
      color: context.templateColors.surface,
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing16,
        vertical: UiConstants.spacing4,
      ),
      child: Row(
        children: [
          // 只看当前按钮
          InkWellWidget(
            onTap: () => {},
            child: Row(
              children: [
                Checkbox(
                  value: false,
                  onChanged: (value) => {},
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                ),
                SizedBox(width: UiConstants.spacing4),
                Text(
                  '只看当前',
                  style: context.templateStyle.text.bodyText.copyWith(
                    fontSize: UiConstants.fontSize12 + 1,
                  ),
                ),
              ],
            ),
          ),

          Spacer(),
          // 全部撤销
          Padding(
            padding: EdgeInsets.only(right: UiConstants.spacing12),
            child: InkWellWidget(
              child: Icon(
                RemixIcons.sort_desc,
                size: 18,
                color: context.templateColors.textSecondary,
              ),
            ),
          ),
          CommonButton.secondary(
            '规则说明',
            height: UiConstants.spacing24 + UiConstants.spacing2,
            onPressed: () => {},
          ),
        ],
      ),
    );
  }
}
