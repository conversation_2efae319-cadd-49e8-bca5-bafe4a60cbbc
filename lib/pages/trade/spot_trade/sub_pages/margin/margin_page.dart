/*
*  杠杆界面
*/

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'widgets/margin_pair.dart';
import 'widgets/margin_header.dart';
import 'tabviews/expot_tabviews.dart';

class MarginPage extends StatefulWidget {
  // 滚动距离变化回调
  final Function(double scrollOffset)? onScrollOffsetChanged;

  const MarginPage({super.key, this.onScrollOffsetChanged});

  @override
  State<MarginPage> createState() => _MarginPageState();
}

class _MarginPageState extends State<MarginPage> with TickerProviderStateMixin {
  // 创建滚动控制器
  late ScrollController _scrollController;

  // 创建主标签控制器
  late TabController _mainController;

  // 创建主标签内容
  static final List<TabItem> _mainKeys = [
    TabItem(title: '委托', showDropdownArrow: true, onDropdownTap: () => {}),
    TabItem(title: '资产'),
    TabItem(title: '持仓'),
    TabItem(title: '体验券'),
  ];

  // 初始化
  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    // 添加滚动监听器
    _scrollController.addListener(_onScrollChanged);
    // 初始化控制器
    _mainController = TabController(length: _mainKeys.length, vsync: this);
  }

  // 销毁
  @override
  void dispose() {
    _scrollController.removeListener(_onScrollChanged);
    _scrollController.dispose();
    _mainController.dispose();
    super.dispose();
  }

  // 滚动变化监听
  void _onScrollChanged() {
    if (widget.onScrollOffsetChanged != null) {
      widget.onScrollOffsetChanged!(_scrollController.offset);
    }
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    // 模拟刷新操作
    await Future.delayed(const Duration(seconds: 2));
    // 这里可以添加实际的数据刷新逻辑
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 交易对
        MarginPair(),
        // 内容
        Expanded(
          child: EasyRefresh.builder(
            header: ClassicHeader(
              clamping: true,
              position: IndicatorPosition.locator,
              triggerOffset: 34,
              processedDuration: const Duration(seconds: 1),
              safeArea: false,
              showMessage: false,
              showText: false,
              maxOverOffset: 40,
            ),
            onRefresh: _onRefresh,
            childBuilder: (context, physics) {
              return ScrollConfiguration(
                behavior: const ERScrollBehavior(),
                child: ExtendedNestedScrollView(
                  controller: _scrollController,
                  physics: physics,
                  onlyOneScrollInBody: true,
                  pinnedHeaderSliverHeightBuilder: () {
                    return 53.0;
                  },
                  headerSliverBuilder: (context, innerBoxIsScrolled) {
                    return <Widget>[
                      // 刷新指示器定位器
                      const HeaderLocator.sliver(clearExtent: false),
                      // 现货头部
                      SliverToBoxAdapter(child: MarginHeader()),
                      // 吸顶 TabBar
                      SliverPersistentHeader(
                        pinned: true,
                        delegate: StickyDelegate(
                          maxHeight: 53,
                          child: _buildMainTabbar(),
                          backgroundColor: context.templateColors.surface,
                        ),
                      ),
                    ];
                  },
                  body: _buildMainTabView(physics),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  // 构建主标签栏
  Widget _buildMainTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(width: 1, color: context.templateColors.divider),
        ),
      ),
      child: TabbarWidget(
        height: 38,
        tabs: _mainKeys,
        controller: _mainController,
        labelStyle: context.templateStyle.text.bodyTextMedium,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: false,
        labelPadding: EdgeInsets.only(right: UiConstants.spacing18),
      ),
    );
  }

  // 构建主标签页内容
  Widget _buildMainTabView(ScrollPhysics physics) {
    return TabBarView(
      controller: _mainController,
      children: [
        // 委托
        _AutomaticKeepAlive(child: MarginOrderTab(physics: physics)),

        // 资产
        _AutomaticKeepAlive(child: MarginAssetsOverviewTab(physics: physics)),

        // 持仓
        _AutomaticKeepAlive(child: MarginPositionsListTab(physics: physics)),

        // 体验券
        _AutomaticKeepAlive(child: MarginCouponCardTab(physics: physics)),
      ],
    );
  }
}

// 自动保持状态的包装器
class _AutomaticKeepAlive extends StatefulWidget {
  final Widget child;

  const _AutomaticKeepAlive({required this.child});

  @override
  State<_AutomaticKeepAlive> createState() => _AutomaticKeepAliveState();
}

class _AutomaticKeepAliveState extends State<_AutomaticKeepAlive>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => true;
}
