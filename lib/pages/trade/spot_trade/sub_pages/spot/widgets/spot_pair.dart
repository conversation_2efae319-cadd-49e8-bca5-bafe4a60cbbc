/*
* 现货交易对
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/routes/index.dart';

class SpotPair extends StatefulWidget {
  const SpotPair({super.key});

  @override
  State<SpotPair> createState() => _SpotPairState();
}

class _SpotPairState extends State<SpotPair> {
  @override
  Widget build(BuildContext context) {
    return _buildPair();
  }

  // 构建交易对信息
  Widget _buildPair() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing10,
      ),
      child: Row(
        children: [
          InkWellWidget(
            onTap: () => {CryptoListBottomSheet.show(context)},
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 交易对名称
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text('BTCUSDT', style: context.templateStyle.text.h4),
                    Padding(
                      padding: EdgeInsets.only(left: UiConstants.spacing4),
                      child: ThemedImage(
                        name: 'arrow_triangle_down',
                        size: 16,
                        followTheme: true,
                      ),
                    ),
                  ],
                ),

                // 标签 & 涨跌幅
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      '+0.50%',
                      style: context.templateStyle.text.hintTextMedium.copyWith(
                        color: context.templateColors.tradeBuy,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          Spacer(),
          Row(
            children: [
              // k线图按钮
              InkWellWidget(
                onTap:
                    () => {
                      // TODO 跳转至k线图界面
                      NavigationService().navigateTo(AppRoutes.klinePage),
                    },
                child: ThemedImage(
                  name: 'strategy_kline',
                  size: 24,
                  followTheme: true,
                ),
              ),
              InkWellWidget(
                onTap:
                    () => {
                      // TODO 显示更多操作菜单
                    },
                child: Padding(
                  padding: EdgeInsets.only(left: UiConstants.spacing16),
                  child: ThemedImage(name: 'more', size: 24, followTheme: true),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
