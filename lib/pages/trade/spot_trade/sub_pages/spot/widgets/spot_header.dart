/*
*  现货头部
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/widgets/trade_common/index.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';
import 'package:qubic_exchange/routes/index.dart';
import 'package:qubic_exchange/services/trade/spot_trade_service.dart';
import 'package:qubic_exchange/services/assets/models/asset_balance_model.dart';
import 'package:qubic_exchange/services/trade/models/spot_trade_config.dart';
import 'package:qubic_exchange/services/market/models/currency_model.dart';

class SpotHeader extends StatefulWidget {
  final int? currencyId;

  const SpotHeader({super.key, this.currencyId});

  @override
  State<SpotHeader> createState() => _SpotHeaderState();
}

class _SpotHeaderState extends State<SpotHeader> {
  // 表单高度（仅计算当前显示的组件）
  double _formHeight = 400.0; // 设置合理的初始高度

  // 表单的 GlobalKey，用于获取实际渲染高度
  final GlobalKey _formKey = GlobalKey();

  // 仓位模式
  final List<String> _positionActions = ['买入', '卖出'];

  // 当前选中的仓位索引
  int _selectedPositionIndex = 0;

  // 当前选中的订单类型
  OrderTypeOption? _selectedOrderType;

  // 构建订单类型选项
  final List<OrderTypeOption> _orderTypes = [
    OrderTypeOption(text: '限价单', index: 0),
    OrderTypeOption(text: '市价单', index: 1),
    OrderTypeOption(text: '高级限价单', index: 2),
    OrderTypeOption(text: '止盈止损', index: 3),
    // OrderTypeOption(text: 'OCO', index: 4),
    OrderTypeOption(text: '计划委托', index: 5),
    OrderTypeOption(text: '追踪委托', index: 6),
  ];

  // 当前选中的订单策略
  OrderStrategyOption? _selectedOrderStrategy;

  // 构建订单策略选项（用于高级限价单）
  final List<OrderStrategyOption> _orderStrategies = [
    OrderStrategyOption(
      text: '只做 Maker',
      description: '该委托单始终为挂单状态。若系统判断该委托单会立即与已存在的委托单成交时，则自动取消该委托单。',
      index: 0,
    ),
    OrderStrategyOption(text: 'IOC', description: '立即成交并取消未成交部分。', index: 1),
    OrderStrategyOption(text: 'FOK', description: '立即全部成交，否则自动撤单。', index: 2),
  ];

  // 交易相关数据
  CurrencyBalance? _currencyBalance;
  CurrencyBalance? _quoteBalance;
  SpotTradeConfig? _tradeConfig;
  bool _hasInitializedConfig = false;
  DateTime? _lastRefreshTime;
  CurrencyModel? _currency;

  // 输入框控制器
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();

  // 防止循环更新的标志
  bool _isUpdatingQuantity = false;
  bool _isUpdatingAmount = false;

  // 当前市场价格（用于市价单计算）
  double? _currentMarketPrice;

  // 委托订单相关
  final TextEditingController _triggerPriceController = TextEditingController();
  final TextEditingController _placePriceController = TextEditingController();
  final TextEditingController _percentageController = TextEditingController();
  final TextEditingController _conditionQuantityController =
      TextEditingController();
  final TextEditingController _conditionAmountController =
      TextEditingController();
  int _selectedConditionOrderType = 0; // 0:止盈止损 1:计划委托 2:追踪委托
  bool _isUpdatingConditionQuantity = false;
  bool _isUpdatingConditionAmount = false;

  @override
  void initState() {
    super.initState();
    // 初始化选中的订单类型为第一项
    _selectedOrderType = _orderTypes.first;
    // 初始化选中的订单策略为第一项
    _selectedOrderStrategy = _orderStrategies.first;
    // 设置默认的time_in_force
    _updateTimeInForceByStrategy(_selectedOrderStrategy);
    // 设置表单清空回调
    SpotTradeService.instance.setFormClearedCallback(_clearViewFormData);
    // 设置委托单控制器监听
    _setupConditionOrderListeners();
    // 在下一帧获取表单高度和初始化数据
    WidgetsBinding.instance.addPostFrameCallback((_) => _initializeTradeData());
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _amountController.dispose();
    _triggerPriceController.dispose();
    _placePriceController.dispose();
    _percentageController.dispose();
    _conditionQuantityController.dispose();
    _conditionAmountController.dispose();
    // 清除回调
    SpotTradeService.instance.setFormClearedCallback(null);
    super.dispose();
  }

  /// 清空视图中的表单数据
  void _clearViewFormData() {
    if (mounted) {
      setState(() {
        _quantityController.clear();
        _amountController.clear();
        _triggerPriceController.clear();
        _placePriceController.clear();
        _percentageController.clear();
      });
      debugPrint('🧹 视图表单数据已清空');
    }
  }

  /// 初始化交易数据
  Future<void> _initializeTradeData() async {
    if (!mounted) {
      return;
    }
    _updateFormHeight();
    if (widget.currencyId == null) return;

    try {
      //获取币种的数据
      final currency = SpotTradeService.instance.getCurrencyData(
        widget.currencyId!,
      );
      if (currency != null) {
        setState(() {
          _currency = currency;
        });
      }
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // 判断是否登录
      if (authProvider.isLoggedIn && !authProvider.isTokenExpired) {
        final spotTradeService = SpotTradeService.instance;

        // 每次都重新获取可用余额
        await _refreshBalanceData();

        // 获取交易配置（只初始化一次）
        if (!_hasInitializedConfig) {
          final config = spotTradeService.getConfigByCurrencyId(
            widget.currencyId!,
            marketType: 1,
          );

          // 如果本地没有配置，尝试从API获取
          if (config == null) {
            await spotTradeService.fetchTradeConfig(widget.currencyId!);
          }

          if (mounted) {
            setState(() {
              _tradeConfig = spotTradeService.getConfigByCurrencyId(
                widget.currencyId!,
                marketType: 1,
              );
            });
          }

          // 初始化交易表单
          spotTradeService.initializeForm(widget.currencyId!);

          // 初始化委托订单表单
          spotTradeService.initializeConditionOrder(widget.currencyId!);

          // 初始化市场价格
          final ticker = spotTradeService.getCurrencyTicker(widget.currencyId!);
          if (ticker != null) {
            _currentMarketPrice = ticker.lastPrice;
            debugPrint('📈 初始化市场价格: $_currentMarketPrice');
          }

          _hasInitializedConfig = true;
          debugPrint('⚙️ 交易配置: ${_tradeConfig?.toString()}');
        }
      }
    } catch (e) {
      debugPrint('❌ 初始化交易数据失败: $e');
    }
  }

  /// 刷新余额数据
  Future<void> _refreshBalanceData() async {
    if (widget.currencyId == null) return;

    try {
      final spotTradeService = SpotTradeService.instance;

      // 获取最新的可用余额
      final balance = spotTradeService.getCurrencyBalance(
        widget.currencyId!,
        1,
      );
      final quoteBalance = spotTradeService.getCurrencyBalance(
        widget.currencyId!,
        1,
        quote: true,
      );
      if (mounted) {
        setState(() {
          _currencyBalance = balance;
          _quoteBalance = quoteBalance;
        });
      }
      debugPrint('💰 刷新余额: ${balance?.availableAmount ?? 0.0}');
    } catch (e) {
      debugPrint('❌ 刷新余额失败: $e');
    }
  }

  // 根据仓位索引获取指示器颜色
  Color _getIndicatorColor(int index) {
    switch (index) {
      case 0: // 买入
        return context.templateColors.tradeBuy;
      case 1: // 卖出
        return context.templateColors.tradeSell;
      default:
        return context.templateColors.tradeBuy;
    }
  }

  // 更新表单高度
  void _updateFormHeight() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      final renderBox =
          _formKey.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final newHeight = renderBox.size.height;
        if ((_formHeight - newHeight).abs() > 1.0) {
          // 避免微小变化导致频繁更新
          setState(() {
            _formHeight = newHeight;
          });
          debugPrint('现货表单高度更新: ${_formHeight.toStringAsFixed(1)}px');
        }
      } else {
        debugPrint('无法获取现货表单高度: RenderBox 为空');
      }
    });
  }

  @override
  void didUpdateWidget(SpotHeader oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 如果币种ID发生变化，重新初始化
    if (oldWidget.currencyId != widget.currencyId) {
      _hasInitializedConfig = false;
      _initializeTradeData();
    }
  }

  /// 提供给外部调用的刷新方法
  void refreshData() {
    _refreshBalanceData();
  }

  /// 下单方法
  Future<void> _placeOrder() async {
    // 判断当前订单类型
    if (_selectedOrderType!.index > 2) {
      // 止盈止损委托单
      await _placeConditionOrder();
    } else {
      // 普通限价单或市价单
      await _placeSpotOrder();
    }
  }

  /// 现货立即下单
  Future<void> _placeSpotOrder() async {
    // 先进行客户端验证，如果失败则显示具体错误信息
    if (!_validateOrderFormUI()) {
      return;
    }

    final success = await SpotTradeService.instance.placeOrder(
      tradeConfig: _tradeConfig,
      baseBalance: _currencyBalance,
      quoteBalance: _quoteBalance,
      currentMarketPrice: _currentMarketPrice,
    );

    if (mounted) {
      if (success) {
        Toastification.show(context, message: '下单成功');
        Future.delayed(Duration(seconds: 1), () {
          refreshData();
        });
      } else {
        Toastification.show(context, message: '下单失败，请稍后重试');
      }
    }
  }

  /// 接收来自DepthBookView的市场价格
  void updateMarketPrice(double price) {
    _currentMarketPrice = price;
    // 如果是市价单，重新计算数量
    if (_selectedOrderType?.index == 1 && _amountController.text.isNotEmpty) {
      final amount = double.tryParse(_amountController.text);
      if (amount != null) {
        _updateQuantityFromAmountWithPrice(amount, price);
      }
    }
  }

  /// 根据数量更新交易额
  void _updateAmountFromQuantity(double quantity) {
    if (_isUpdatingAmount) return;

    final form = SpotTradeService.instance.currentForm;
    if (form?.price == null || form!.price == 0) return;

    _isUpdatingQuantity = true;
    final amount = quantity * form.price!;
    _amountController.text = amount.toStringAsFixed(2);
    _isUpdatingQuantity = false;

    debugPrint('📊 根据数量 $quantity 更新交易额: $amount');
  }

  /// 根据交易额更新数量
  void _updateQuantityFromAmount(double amount) {
    if (_isUpdatingQuantity) return;

    // 获取价格：限价单使用表单价格，市价单使用市场价格
    double? price;
    if (_selectedOrderType?.index == 1) {
      // 市价单使用市场价格
      price = _currentMarketPrice;
      debugPrint('🔍 市价单获取价格: $_currentMarketPrice');

      // 如果没有市场价格，尝试从ticker获取
      if (price == null && widget.currencyId != null) {
        final ticker = SpotTradeService.instance.getCurrencyTicker(
          widget.currencyId!,
        );
        price = ticker?.lastPrice;
        debugPrint('🔍 从ticker获取价格: $price');
      }
    } else {
      // 限价单使用表单价格
      final form = SpotTradeService.instance.currentForm;
      price = form?.price;
      debugPrint('🔍 限价单获取价格: $price');
    }

    if (price == null || price == 0) {
      debugPrint('❌ 无法获取有效价格，无法计算数量 (price: $price)');
      return;
    }

    _updateQuantityFromAmountWithPrice(amount, price);
  }

  /// 根据交易额和指定价格更新数量
  void _updateQuantityFromAmountWithPrice(double amount, double price) {
    if (_isUpdatingQuantity) return;

    _isUpdatingAmount = true;
    final quantity = amount / price;
    _quantityController.text = quantity.toStringAsFixed(
      _currency!.sPricePrecision,
    );

    // 无论限价单还是市价单，都要将计算的数量保存到表单中
    SpotTradeService.instance.updateQuantity(quantity);

    _isUpdatingAmount = false;
  }

  /// 根据滑块百分比更新交易额
  void _updateAmountFromSlider(double percentage) {
    // 根据买卖方向选择不同的余额
    final balance =
        _selectedPositionIndex == 0 ? _quoteBalance : _currencyBalance;
    if (balance == null) return;

    final availableBalance = balance.availableAmount;
    final amount = availableBalance * (percentage / 100);

    _isUpdatingAmount = true;
    _amountController.text = amount.toStringAsFixed(2);
    _isUpdatingAmount = false;

    // 更新数量
    _updateQuantityFromAmount(amount);

    final direction = _selectedPositionIndex == 0 ? '买入' : '卖出';
    debugPrint(
      '🎚️ 滑块 $percentage% 设置交易额: $amount ($direction可用余额: $availableBalance)',
    );
  }

  /// 订单类型切换时重新计算数量
  void _recalculateQuantityOnTypeChange() {
    // 如果交易额输入框有数据，根据新的订单类型重新计算数量
    if (_amountController.text.isNotEmpty) {
      final amount = double.tryParse(_amountController.text);
      if (amount != null && amount > 0) {
        debugPrint('🔄 订单类型切换，根据交易额 $amount 重新计算数量');
        _updateQuantityFromAmount(amount);
      }
    }
  }

  /// 切换到市价单时重新计算数量
  void _clearQuantityForMarketOrder() {
    debugPrint('🔄 切换到市价单，重新计算数量');

    // 如果有交易额，根据市场价格重新计算数量
    if (_amountController.text.isNotEmpty) {
      final amount = double.tryParse(_amountController.text);
      if (amount != null && amount > 0) {
        debugPrint('🔄 根据交易额 $amount 重新计算市价单数量');
        _updateQuantityFromAmount(amount);
      }
    } else {
      // 如果没有交易额，清空数量
      _quantityController.clear();
      SpotTradeService.instance.updateQuantity(0.0);
      debugPrint('🧹 没有交易额，清空数量数据');
    }
  }

  /// 根据订单策略更新time_in_force
  void _updateTimeInForceByStrategy(OrderStrategyOption? strategy) {
    if (strategy == null) return;

    String timeInForce;
    switch (strategy.index) {
      case 0: // 只做 Maker
        timeInForce = 'gtc';
        break;
      case 1: // IOC
        timeInForce = 'ioc';
        break;
      case 2: // FOK
        timeInForce = 'fok';
        break;
      default:
        timeInForce = 'gtc';
    }

    // 更新表单中的time_in_force
    SpotTradeService.instance.updateTimeInForce(timeInForce);

    debugPrint('📋 订单策略: ${strategy.text} → time_in_force: $timeInForce');
  }

  /// UI层验证表单数据
  bool _validateOrderFormUI() {
    final form = SpotTradeService.instance.currentForm;
    if (form == null) {
      Toastification.show(context, message: '表单数据为空');
      return false;
    }

    // 验证数量
    if (form.quantity <= 0) {
      Toastification.show(context, message: '数量必须大于0');
      return false;
    }

    // 限价单必须有价格
    if (form.type == 'limit' && (form.price == null || form.price! <= 0)) {
      Toastification.show(context, message: '限价单必须设置价格');
      return false;
    }

    // 验证交易配置
    if (_tradeConfig != null) {
      if (form.quantity < _tradeConfig!.minTradeNum) {
        Toastification.show(
          context,
          message: '数量不能小于 ${_tradeConfig!.minTradeNum}',
        );
        return false;
      }
      if (form.quantity > _tradeConfig!.maxTradeNum) {
        Toastification.show(
          context,
          message: '数量不能超过 ${_tradeConfig!.maxTradeNum}',
        );
        return false;
      }
      if (form.type == 'limit' && form.price != null) {
        if (form.price! < _tradeConfig!.minTradePrice) {
          Toastification.show(
            context,
            message: '价格不能低于 ${_tradeConfig!.minTradePrice}',
          );
          return false;
        }
        if (form.price! > _tradeConfig!.maxTradePrice) {
          Toastification.show(
            context,
            message: '价格不能超过 ${_tradeConfig!.maxTradePrice}',
          );
          return false;
        }
      }
    }

    // 验证余额
    final balance = form.side == 'buy' ? _quoteBalance : _currencyBalance;
    if (balance == null) {
      Toastification.show(context, message: '余额数据未加载');
      return false;
    }

    double requiredAmount;
    String currencySymbol;

    if (form.side == 'buy') {
      currencySymbol = _currency?.quoteAsset ?? 'USDT';
      if (form.type == 'market') {
        final currentPrice = _currentMarketPrice ?? 0.0;
        requiredAmount = form.quantity * currentPrice;
      } else {
        requiredAmount = form.quantity * (form.price ?? 0.0);
      }
    } else {
      currencySymbol = _currency?.baseAsset ?? 'BTC';
      requiredAmount = form.quantity;
    }

    if (balance.availableAmount < requiredAmount) {
      Toastification.show(
        context,
        message:
            '$currencySymbol余额不足，需要 ${requiredAmount.toStringAsFixed(6)}，可用 ${balance.availableAmount.toStringAsFixed(6)}',
      );
      return false;
    }

    return true;
  }

  /// 设置委托单控制器监听
  void _setupConditionOrderListeners() {
    _triggerPriceController.addListener(_onTriggerPriceChanged);
    _placePriceController.addListener(_onPlacePriceChanged);
    _conditionQuantityController.addListener(_onConditionQuantityChanged);
    _percentageController.addListener(_onPercentageChanged);
  }

  /// 触发价格变化处理
  void _onTriggerPriceChanged() {
    final value = _triggerPriceController.text;
    SpotTradeService.instance.updateConditionOrder(triggerPrice: value);
    _updateTriggerCondition(value);

    // 触发价格变化时重新计算成交额（特别是市价单）
    final quantity = double.tryParse(_conditionQuantityController.text);
    if (quantity != null && quantity > 0) {
      _updateConditionAmountFromQuantity(quantity);
    }
  }

  /// 执行价格变化处理
  void _onPlacePriceChanged() {
    final value = _placePriceController.text;
    SpotTradeService.instance.updateConditionOrder(placePrice: value);

    // 价格变化时重新计算成交额
    final quantity = double.tryParse(_conditionQuantityController.text);
    if (quantity != null && quantity > 0) {
      _updateConditionAmountFromQuantity(quantity);
    }
  }

  /// 委托数量变化处理
  void _onConditionQuantityChanged() {
    final value = _conditionQuantityController.text;
    SpotTradeService.instance.updateConditionOrder(amount: value);

    // 联动计算成交额
    final quantity = double.tryParse(value);
    if (quantity != null && !_isUpdatingConditionQuantity) {
      _updateConditionAmountFromQuantity(quantity);
    }
  }

  /// 根据委托数量更新成交额
  void _updateConditionAmountFromQuantity(double quantity) {
    if (_isUpdatingConditionAmount) return;

    // 获取执行价格
    double? price = _getConditionOrderPrice();
    if (price == null || price == 0) return;

    _isUpdatingConditionQuantity = true;
    final amount = quantity * price;
    _conditionAmountController.text = amount.toStringAsFixed(2);
    _isUpdatingConditionQuantity = false;
  }

  /// 根据委托成交额更新数量
  void _updateConditionQuantityFromAmount(double amount) {
    if (_isUpdatingConditionQuantity) return;

    // 获取执行价格
    double? price = _getConditionOrderPrice();
    if (price == null || price == 0) return;

    _isUpdatingConditionAmount = true;
    final quantity = amount / price;
    _conditionQuantityController.text = quantity.toStringAsFixed(
      _currency?.sPricePrecision ?? 6,
    );

    // 更新委托订单数量
    SpotTradeService.instance.updateConditionOrder(amount: quantity.toString());
    _isUpdatingConditionAmount = false;
  }

  /// 获取委托订单的执行价格
  double? _getConditionOrderPrice() {
    final order = SpotTradeService.instance.currentConditionOrder;
    if (order == null) return null;

    // 如果是市价单，使用触发价格
    if (order.triggerType == '2') {
      return double.tryParse(order.triggerPrice ?? '');
    }

    // 如果是限价单，使用执行价格
    if (order.placePrice != null && order.placePrice!.isNotEmpty) {
      return double.tryParse(order.placePrice!);
    }

    // 如果没有执行价格，使用触发价格
    return double.tryParse(order.triggerPrice ?? '');
  }

  /// 百分比变化处理（追踪委托）
  void _onPercentageChanged() {
    final percentage = _percentageController.text;
    final triggerPrice = _triggerPriceController.text;
    if (percentage.isNotEmpty && triggerPrice.isNotEmpty) {
      _calculateTrackingPlacePrice(triggerPrice, percentage);
    }
  }

  /// 处理委托订单类型切换
  void _handleConditionOrderTypeChange(int orderTypeIndex) {
    String orderType;
    switch (orderTypeIndex) {
      case 0: // 止盈止损
        orderType = '1';
        break;
      case 1: // 计划委托
        orderType = '2';
        break;
      case 2: // 追踪委托
        orderType = '3';
        break;
      default:
        orderType = '1';
    }

    SpotTradeService.instance.updateConditionOrder(orderType: orderType);
    debugPrint('🔄 委托订单类型切换: $orderType');
  }

  /// 更新触发条件
  void _updateTriggerCondition(String triggerPrice) {
    if (_currentMarketPrice == null) return;

    final price = double.tryParse(triggerPrice);
    if (price == null) return;

    final side = _selectedPositionIndex == 0 ? '1' : '-1';
    String triggerCondition;

    if (side == '1') {
      // 买入：价格上涨时触发，使用大于等于
      triggerCondition = price >= _currentMarketPrice! ? '1' : '2';
    } else {
      // 卖出：价格下跌时触发，使用小于等于
      triggerCondition = price <= _currentMarketPrice! ? '2' : '1';
    }

    SpotTradeService.instance.updateConditionOrder(
      triggerCondition: triggerCondition,
      triggerPrice: triggerPrice,
    );

    debugPrint(
      '🎯 触发条件更新: $triggerCondition (触发价: $triggerPrice, 当前价: $_currentMarketPrice)',
    );
  }

  /// 计算追踪委托的执行价格
  void _calculateTrackingPlacePrice(String triggerPrice, String percentage) {
    final price = double.tryParse(triggerPrice);
    final percent = double.tryParse(percentage);

    if (price == null || percent == null) return;
    if (percent > 10) return; // 最大10%

    final side = _selectedPositionIndex == 0 ? '1' : '-1';
    double placePrice;

    if (side == '1') {
      // 买入：执行价格 = 触发价格 + (触发价格 * 百分比)
      placePrice = price + (price * percent / 100);
    } else {
      // 卖出：执行价格 = 触发价格 - (触发价格 * 百分比)
      placePrice = price - (price * percent / 100);
    }

    _placePriceController.text = placePrice.toStringAsFixed(6);
    SpotTradeService.instance.updateConditionOrder(
      placePrice: placePrice.toString(),
    );

    debugPrint('📊 追踪委托执行价格: $placePrice (触发价: $price, 百分比: $percent%)');
  }

  /// 委托单下单
  Future<void> _placeConditionOrder() async {
    // 验证委托单表单
    if (!_validateConditionOrderForm()) {
      return;
    }

    final success = await SpotTradeService.instance.placeConditionOrder();

    if (mounted) {
      if (success) {
        Toastification.show(context, message: '委托单下单成功');
        // 清空委托单表单
        _clearConditionOrderForm();
      } else {
        Toastification.show(context, message: '委托单下单失败');
      }
    }
  }

  /// 验证委托单表单
  bool _validateConditionOrderForm() {
    final order = SpotTradeService.instance.currentConditionOrder;
    if (order == null) {
      Toastification.show(context, message: '委托单数据为空');
      return false;
    }

    if (order.triggerPrice == null || order.triggerPrice!.isEmpty) {
      Toastification.show(context, message: '请输入触发价格');
      return false;
    }

    if (order.amount == null || order.amount!.isEmpty) {
      Toastification.show(context, message: '请输入委托数量');
      return false;
    }

    // 验证数量必须大于0
    final amount = double.tryParse(order.amount!) ?? 0;
    if (amount <= 0) {
      Toastification.show(context, message: '数量必须大于0');
      return false;
    }

    // 限价委托需要执行价格
    if (order.triggerType == '1' &&
        (order.placePrice == null || order.placePrice!.isEmpty)) {
      Toastification.show(context, message: '限价委托请输入执行价格');
      return false;
    }

    return true;
  }

  /// 清空委托单表单
  void _clearConditionOrderForm() {
    _triggerPriceController.clear();
    _placePriceController.clear();
    _percentageController.clear();
    _conditionQuantityController.clear();
    _conditionAmountController.clear();
    if (widget.currencyId != null) {
      SpotTradeService.instance.initializeConditionOrder(widget.currencyId!);
    }
  }

  /// 构建委托订单类型按钮
  Widget _buildConditionOrderTypeButton(String text, int index) {
    final isSelected = _selectedConditionOrderType == index;
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedConditionOrderType = index;
          });
          _handleConditionOrderTypeChange(index);
        },
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color:
                isSelected
                    ? context.templateColors.primary
                    : Colors.transparent,
            border: Border.all(
              color:
                  isSelected
                      ? context.templateColors.primary
                      : context.templateColors.border,
            ),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: context.templateStyle.text.bodyText.copyWith(
              color:
                  isSelected
                      ? Colors.white
                      : context.templateColors.textPrimary,
              fontSize: 12,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建执行价格输入
  Widget _buildExecutionPriceInput() {
    if (_selectedConditionOrderType == 2) {
      // 追踪委托：显示百分比输入
      return Column(
        children: [
          TextFieldWidget(
            controller: _percentageController,
            height: 42,
            labelText: '回调百分比(%)',
            cursorHeight: 12,
            onChanged: (value) {
              if (value.isNotEmpty && _triggerPriceController.text.isNotEmpty) {
                _calculateTrackingPlacePrice(
                  _triggerPriceController.text,
                  value,
                );
              }
            },
          ),
          SizedBox(height: UiConstants.spacing8),
          TextFieldWidget(
            controller: _placePriceController,
            height: 42,
            labelText: '执行价格',
            cursorHeight: 12,
            enabled: false, // 追踪委托的执行价格是自动计算的
          ),
        ],
      );
    } else {
      // 止盈止损和计划委托：显示执行价格输入
      return TextFieldWidget(
        controller: _placePriceController,
        height: 42,
        labelText: '执行价格',
        cursorHeight: 12,
        onChanged: (value) {
          SpotTradeService.instance.updateConditionOrder(placePrice: value);
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // 每次build时检查是否需要刷新余额（防抖：3秒内只刷新一次）
    final now = DateTime.now();
    if (_lastRefreshTime == null ||
        now.difference(_lastRefreshTime!).inSeconds >= 3) {
      _lastRefreshTime = now;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && widget.currencyId != null) {
          final authProvider = Provider.of<AuthProvider>(
            context,
            listen: false,
          );
          if (authProvider.isLoggedIn && !authProvider.isTokenExpired) {
            _refreshBalanceData();
          }
        }
      });
    }

    return Container(
      padding: EdgeInsets.only(
        left: UiConstants.spacing18,
        right: UiConstants.spacing18,
        bottom: UiConstants.spacing10,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(flex: 5, child: _buildLeftForm()),
          SizedBox(width: UiConstants.spacing16),
          Expanded(
            flex: 4,
            child: DepthBookView(
              height: _formHeight,
              isContract: false,
              marketType: 1,
              symbol: _currency!.symbol,
              onPriceSelected: (price) {
                updateMarketPrice(price);
              },
            ),
          ),
        ],
      ),
    );
  }

  // 构建左侧表单
  Widget _buildLeftForm() {
    return Column(
      key: _formKey, // 添加 GlobalKey 用于获取高度
      children: [
        // 仓位模式切换
        PositionActiontoggle(
          tabKeys: _positionActions,
          selectedColor: Colors.white,
          indicatorColor: _getIndicatorColor(_selectedPositionIndex),
          initialIndex: _selectedPositionIndex,
          onChanged: (index) {
            setState(() {
              _selectedPositionIndex = index;
            });
            // 更新交易表单的买卖方向
            final side = index == 0 ? 'buy' : 'sell';
            SpotTradeService.instance.updateSide(side);

            // 更新委托订单的买卖方向
            final conditionSide = index == 0 ? '1' : '-1';
            SpotTradeService.instance.updateConditionOrder(side: conditionSide);

            debugPrint('仓位切换为: ${_positionActions[index]}');
          },
        ),

        // 订单类型切换
        OrderTypeToggle(
          orderTypes: _orderTypes,
          selectedOption: _selectedOrderType,
          onChanged: (option) {
            setState(() {
              _selectedOrderType = option;
            });
            // 更新交易表单的订单类型（只处理限价单和市价单）
            if (option.index == 0) {
              // 限价单
              SpotTradeService.instance.updateOrderType('limit');
              // 限价单默认使用gtc
              SpotTradeService.instance.updateTimeInForce('gtc');
              // 切换到限价单时，如果有交易额，根据当前价格重新计算数量
              _recalculateQuantityOnTypeChange();
            } else if (option.index == 1) {
              // 市价单
              SpotTradeService.instance.updateOrderType('market');
              // 市价单默认使用gtc
              SpotTradeService.instance.updateTimeInForce('gtc');
              // 切换到市价单时，清空表单中的数量数据，数量应该根据交易额实时计算
              _clearQuantityForMarketOrder();
            } else if (option.index == 2) {
              // 高级限价单（止盈止损）
              SpotTradeService.instance.updateOrderType('limit');
              // 高级限价单使用当前选中的订单策略对应的time_in_force
              _updateTimeInForceByStrategy(_selectedOrderStrategy);
              // 初始化委托订单为止盈止损类型
              _handleConditionOrderTypeChange(0);
              // 清空委托单表单
              _clearConditionOrderForm();
              // 切换到高级限价单时，如果有交易额，根据当前价格重新计算数量
              _recalculateQuantityOnTypeChange();
            } else if (option.index == 5) {
              // 计划委托
              SpotTradeService.instance.updateOrderType('limit');
              _handleConditionOrderTypeChange(1);
              _clearConditionOrderForm();
            } else if (option.index == 6) {
              // 追踪委托
              SpotTradeService.instance.updateOrderType('limit');
              _handleConditionOrderTypeChange(2);
              _clearConditionOrderForm();
            }

            // 当订单类型改变时，重新计算表单高度
            _updateFormHeight();
            debugPrint('订单类型切换为: ${option.text}');
          },
        ),

        ..._buildOrderTypeSpecificInputs(),

        // 可用
        AvailableInfo(
          direction: _selectedPositionIndex == 0 ? 1 : 0,
          baseBalance: _currencyBalance,
          quoteBalance: _quoteBalance,
          symbol: _currency!.baseAsset,
          quoteSymbol: _currency!.quoteAsset,
        ),

        // 按钮
        Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            final isLoggedIn =
                authProvider.isLoggedIn && !authProvider.isTokenExpired;

            return _selectedPositionIndex == 0
                ? CommonButton.buy(
                  isLoggedIn ? '买入' : '登录',
                  size: CommonButtonSize.medium,
                  width: double.infinity,
                  onPressed: () {
                    if (isLoggedIn) {
                      _placeOrder();
                    } else {
                      NavigationService().navigateTo(
                        AppRoutes.login,
                        arguments: {
                          'transitionType': RouteTransitionType.slideUp,
                        },
                      );
                    }
                  },
                )
                : CommonButton.sell(
                  isLoggedIn ? '卖出' : '登录',
                  size: CommonButtonSize.medium,
                  width: double.infinity,
                  onPressed: () {
                    if (isLoggedIn) {
                      _placeOrder();
                    } else {
                      NavigationService().navigateTo(
                        AppRoutes.login,
                        arguments: {
                          'transitionType': RouteTransitionType.slideUp,
                        },
                      );
                    }
                  },
                );
          },
        ),
      ],
    );
  }

  // 根据订单类型显示不同的输入组件
  List<Widget> _buildOrderTypeSpecificInputs() {
    switch (_selectedOrderType?.index) {
      case 0: // 限价单
        return [
          // 价格
          PriceInput(
            onChanged: (price) {
              SpotTradeService.instance.updatePrice(price);
            },
          ),
          //数量
          QuantityInput(
            isButton: false,
            controller: _quantityController,
            onChanged: (quantity) {
              if (quantity != null && !_isUpdatingQuantity) {
                SpotTradeService.instance.updateQuantity(quantity);
                _updateAmountFromQuantity(quantity);
              }
            },
          ),
          // 滑块
          LeverageSelector(
            onChanged: (percentage) {
              _updateAmountFromSlider(percentage);
            },
          ),
          // 交易额
          TradeAmountInput(
            isButton: false,
            controller: _amountController,
            onChanged: (amount) {
              if (amount != null && !_isUpdatingAmount) {
                _updateQuantityFromAmount(amount);
              }
            },
          ),
          // 止盈止损
          TakeProfitStopLoss(
            isContract: false,
            onExpandChanged: (isExpanded) {
              // 当止盈止损展开/收缩时，重新计算表单高度
              _updateFormHeight();
            },
          ),
        ];
      case 1: // 市价单
        return [
          // 市价成交
          MarketOrder(),
          // 交易额
          TradeAmountInput(
            controller: _amountController,
            onChanged: (amount) {
              debugPrint('💰 市价单交易额输入: $amount');
              if (amount != null && !_isUpdatingAmount) {
                _updateQuantityFromAmount(amount);
              }
            },
          ),
          // 滑块
          LeverageSelector(
            onChanged: (percentage) {
              _updateAmountFromSlider(percentage);
            },
          ),
          // 止盈止损
          TakeProfitStopLoss(
            isContract: false,
            onExpandChanged: (isExpanded) {
              // 当止盈止损展开/收缩时，重新计算表单高度
              _updateFormHeight();
            },
          ),
        ];
      case 2: // 高级限价单
        return [
          // 订单策略切换
          OrderStrategyOptionsToggle(
            options: _orderStrategies,
            selectedOption: _selectedOrderStrategy,
            onChanged: (strategy) {
              setState(() {
                _selectedOrderStrategy = strategy;
              });

              // 根据订单策略设置time_in_force
              _updateTimeInForceByStrategy(strategy);

              // 设置委托订单的触发类型（限价/市价）
              final triggerType =
                  strategy.index == 0 ? '1' : '2'; // 只做Maker用限价，其他用市价
              SpotTradeService.instance.updateConditionOrder(
                triggerType: triggerType,
              );
            },
          ),
          // 价格
          PriceInput(
            showBBO: false,
            showIncrementButtons: true,
            onChanged: (price) {
              SpotTradeService.instance.updatePrice(price);
            },
          ),
          //数量
          QuantityInput(
            isButton: false,
            onChanged: (quantity) {
              if (quantity != null) {
                SpotTradeService.instance.updateQuantity(quantity);
              }
            },
          ),
          // 滑块
          LeverageSelector(),
          // 交易额
          TradeAmountInput(
            isButton: false,
            onChanged: (amount) {
              if (amount != null) {
                SpotTradeService.instance.updateQuantityByAmount(amount);
              }
            },
          ),
          // 止盈止损
          TakeProfitStopLoss(
            isContract: false,
            onExpandChanged: (isExpanded) {
              // 当止盈止损展开/收缩时，重新计算表单高度
              _updateFormHeight();
            },
          ),
        ];
      case 3: // 止盈止损
        return [
          // 触发价格
          TriggerPrice(
            isContract: false,
            controller: _triggerPriceController,
            onPriceChanged: (value) {
              SpotTradeService.instance.updateConditionOrder(
                triggerPrice: value,
              );
              _updateTriggerCondition(value);
            },
          ),
          // 价格
          PresetOrderPrice(
            labelText: '价格',
            controller: _placePriceController,
            onChanged: (price) {
              if (price != null) {
                SpotTradeService.instance.updateConditionOrder(
                  placePrice: price.toString(),
                );
              }
            },
            onOrderTypeChanged: (isMarketPrice) {
              final triggerType = isMarketPrice ? '2' : '1';
              SpotTradeService.instance.updateConditionOrder(
                triggerType: triggerType,
              );
              if (isMarketPrice) {
                _placePriceController.clear();
                SpotTradeService.instance.updateConditionOrder(
                  placePrice: null,
                );
              }
            },
          ),
          // 数量
          QuantityInput(
            isButton: false,
            controller: _conditionQuantityController,
            onChanged: (quantity) {
              if (quantity != null) {
                SpotTradeService.instance.updateConditionOrder(
                  amount: quantity.toString(),
                );
              }
            },
          ),
          // 滑块
          LeverageSelector(),
          // 交易额
          TradeAmountInput(
            isButton: false,
            controller: _conditionAmountController,
            onChanged: (amount) {
              if (amount != null && !_isUpdatingConditionAmount) {
                _updateConditionQuantityFromAmount(amount);
              }
            },
          ),
        ];

      // case 4: // OCO
      //   return [
      //     // 限价
      //     LimitPriceInput(padding: EdgeInsets.only(top: UiConstants.spacing8)),
      //     //触发价格
      //     TriggerPrice(isContract: false),
      //     // 委托价格
      //     OrderPriceInput(),
      //     // 数量
      //     QuantityInput(isButton: false),
      //     // 滑块
      //     LeverageSelector(),
      //     // 交易额
      //     TradeAmountInput(isButton: false),
      //   ];

      case 5: // 计划委托
        return [
          // 触发价格
          TriggerPrice(
            isContract: false,
            controller: _triggerPriceController,
            onPriceChanged: (value) {
              SpotTradeService.instance.updateConditionOrder(
                triggerPrice: value,
              );
              _updateTriggerCondition(value);
            },
          ),
          // 价格
          PresetOrderPrice(
            labelText: '价格',
            controller: _placePriceController,
            onChanged: (price) {
              if (price != null) {
                SpotTradeService.instance.updateConditionOrder(
                  placePrice: price.toString(),
                );
              }
            },
          ),
          // 数量
          QuantityInput(
            isButton: false,
            controller: _conditionQuantityController,
            padding: EdgeInsets.only(top: UiConstants.spacing8),
            onChanged: (quantity) {
              if (quantity != null) {
                SpotTradeService.instance.updateConditionOrder(
                  amount: quantity.toString(),
                );
              }
            },
          ),
          // 滑块
          LeverageSelector(),
          // 交易额
          TradeAmountInput(isButton: false),
          // 止盈止损
          TakeProfitStopLoss(
            isContract: false,
            onExpandChanged: (isExpanded) {
              // 当止盈止损展开/收缩时，重新计算表单高度
              _updateFormHeight();
            },
          ),
        ];

      case 6: // 追踪委托
        return [
          // 触发价格
          TriggerPrice(
            isContract: false,
            controller: _triggerPriceController,
            onPriceChanged: (value) {
              SpotTradeService.instance.updateConditionOrder(
                triggerPrice: value,
              );
              _updateTriggerCondition(value);
              // 触发价格变化时重新计算执行价格
              if (_percentageController.text.isNotEmpty) {
                _calculateTrackingPlacePrice(value, _percentageController.text);
              }
            },
          ),
          // 回调幅度
          CallbackAmount(
            controller: _percentageController,
            onChanged: (percentage) {
              // 回调幅度变化时重新计算执行价格
              if (_triggerPriceController.text.isNotEmpty) {
                _calculateTrackingPlacePrice(
                  _triggerPriceController.text,
                  percentage,
                );
              }
            },
          ),
          // 预设委托价（自动计算，只读）
          PresetOrderPrice(controller: _placePriceController, enabled: false),
          // 交易额
          TradeAmountInput(isButton: false),
          // 滑块
          LeverageSelector(),
        ];
      default:
        return [];
    }
  }
}
