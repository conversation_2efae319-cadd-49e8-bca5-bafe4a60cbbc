/*
*  现货订单项组件
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/models/trade/spot_order_model.dart';
import 'package:remixicon/remixicon.dart';

class SpotOrderItem extends StatelessWidget {
  /// 订单数据
  final SpotOrderModel? order;

  /// 修改订单回调
  final VoidCallback? onModify;

  /// 设置止盈止损回调
  final VoidCallback? onSetStopProfitLoss;

  /// 撤销订单回调
  final VoidCallback? onCancel;

  /// 是否显示底部边框
  final bool showBottomBorder;

  const SpotOrderItem({
    super.key,
    this.order,
    this.onModify,
    this.onSetStopProfitLoss,
    this.onCancel,
    this.showBottomBorder = true,
  });
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(
        left: UiConstants.spacing18,
        top: UiConstants.spacing14,
        right: UiConstants.spacing18,
        bottom: UiConstants.spacing4,
      ),
      decoration: BoxDecoration(
        color: context.templateColors.surface,
        border:
            showBottomBorder
                ? Border(
                  bottom: BorderSide(
                    width: context.templateStyles.borderWidthThin,
                    color: context.templateColors.border,
                  ),
                )
                : null,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 头部
          _buildItemHeader(context),

          // 数据
          _buildItemData(context),

          // 操作按钮
          _buildOrderToggle(context),
        ],
      ),
    );
  }

  // 构建现货头部：交易对、下单时间、类型（买入、卖出、计划委托...）
  Widget _buildItemHeader(BuildContext context) {
    final order = this.order ?? SpotOrderModel.mock();

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              order.symbol,
              style: context.templateStyle.text.bodyLargeMedium,
            ),
            Icon(
              RemixIcons.arrow_right_s_line,
              size: UiConstants.iconSize14,
              color: context.templateColors.textPrimary,
            ),
            Spacer(),
            Text(
              _formatDateTime(order.createTime),
              style: context.templateStyle.text.descriptionSmall,
            ),
          ],
        ),
        SizedBox(height: UiConstants.spacing4),

        /// 交易类型标签
        Row(
          children: [
            // 买卖方向标签 - 使用对应颜色
            _buildDirectionTag(context, order.direction),
            SizedBox(width: UiConstants.spacing4),
            // 订单类型标签 - 使用 textSecondary 颜色
            _buildOrderTypeTag(context, order.orderType),
            // 只有等待执行状态才显示状态标签
            if (order.status == SpotOrderStatus.waitingExecution) ...[
              SizedBox(width: UiConstants.spacing4),
              _buildOrderStatusTag(context, order.status),
            ],
          ],
        ),
      ],
    );
  }

  /// 构建交易方向标签（买入/卖出）
  Widget _buildDirectionTag(BuildContext context, TradeDirection direction) {
    final colors = context.templateColors;
    final isBuy = direction.isBuy;

    return TagWidget(
      text: direction.displayName,
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
      borderColor: isBuy ? colors.tradeBuy : colors.tradeSell,
      textStyle: context.templateStyle.text.tagSmall.copyWith(
        color: isBuy ? colors.tradeBuy : colors.tradeSell,
      ),
    );
  }

  /// 构建订单类型标签（限价/市价/计划委托等）
  Widget _buildOrderTypeTag(BuildContext context, SpotOrderType orderType) {
    final colors = context.templateColors;

    return TagWidget(
      text: orderType.displayName,
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
      borderColor: colors.textSecondary,
      textStyle: context.templateStyle.text.tagSmall.copyWith(
        color: colors.textSecondary,
      ),
    );
  }

  /// 构建订单状态标签（等待执行/待成交/已成交等）
  Widget _buildOrderStatusTag(BuildContext context, SpotOrderStatus status) {
    final colors = context.templateColors;

    // 等待执行状态使用灰色，其他状态也使用灰色保持一致
    final statusColor = colors.textSecondary;

    return TagWidget(
      text: status.displayName,
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
      borderColor: statusColor,
      borderWidth: context.templateStyles.borderWidthThin,
      textStyle: context.templateStyle.text.tagSmall.copyWith(
        color: statusColor,
      ),
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
  }

  // 构建现货订单数据组
  Widget _buildItemData(BuildContext context) {
    final order = this.order ?? SpotOrderModel.mock();

    return Container(
      margin: EdgeInsets.only(top: UiConstants.spacing18),
      child: _buildDataByOrderType(order),
    );
  }

  /// 根据订单类型构建不同的数据显示
  Widget _buildDataByOrderType(SpotOrderModel order) {
    switch (order.orderType) {
      case SpotOrderType.planOrder:
        return _buildPlanOrderData(order);
      case SpotOrderType.limit:
        return _buildLimitOrderData(order);
      case SpotOrderType.stopProfitLoss:
        return _buildStopProfitLossData(order);
      case SpotOrderType.market:
      case SpotOrderType.oco:
      case SpotOrderType.trailingOrder:
        return _buildDefaultOrderData(order);
    }
  }

  /// 构建数据项的 Wrap 布局（一行三个，第三个右对齐）
  Widget _buildDataWrap(List<Map<String, String>> items) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final itemWidth = constraints.maxWidth / 3;

        return Wrap(
          spacing: 0,
          runSpacing: UiConstants.spacing12,
          children:
              items.asMap().entries.map((entry) {
                final index = entry.key;
                final item = entry.value;
                final isThirdInRow = (index + 1) % 3 == 0;

                return SizedBox(
                  width: itemWidth,
                  child: OrderDataItem(
                    label: item['label']!,
                    value: item['value']!,
                    isEndAligned: isThirdInRow,
                  ),
                );
              }).toList(),
        );
      },
    );
  }

  /// 计划委托数据：触发价格、价格、委托数量、交易额
  Widget _buildPlanOrderData(SpotOrderModel order) {
    return _buildDataWrap([
      {
        'label': '触发价格(${order.quoteCurrency})',
        'value': order.triggerPrice ?? order.price,
      },
      {'label': '价格(${order.quoteCurrency})', 'value': order.price},
      {'label': '委托数量(${order.baseCurrency})', 'value': order.quantity},
      {'label': '交易额(${order.quoteCurrency})', 'value': order.amount},
    ]);
  }

  /// 限价数据：委托价格、委托数量、交易额、已成交
  Widget _buildLimitOrderData(SpotOrderModel order) {
    return _buildDataWrap([
      {'label': '委托价格(${order.quoteCurrency})', 'value': order.price},
      {'label': '委托数量(${order.baseCurrency})', 'value': order.quantity},
      {'label': '交易额(${order.quoteCurrency})', 'value': order.amount},
      {'label': '已成交(${order.baseCurrency})', 'value': order.filledQuantity},
    ]);
  }

  /// 止盈止损数据：触发价格、委托价格、数量
  Widget _buildStopProfitLossData(SpotOrderModel order) {
    return _buildDataWrap([
      {
        'label': '触发价格(${order.quoteCurrency})',
        'value': order.triggerPrice ?? order.price,
      },
      {'label': '委托价格(${order.quoteCurrency})', 'value': order.price},
      {'label': '数量(${order.baseCurrency})', 'value': order.quantity},
    ]);
  }

  /// 默认订单数据（市价、OCO、追踪委托等）
  Widget _buildDefaultOrderData(SpotOrderModel order) {
    // 市价单显示"市价"，其他显示具体价格
    final priceValue =
        order.orderType == SpotOrderType.market ? '市价' : order.price;

    return _buildDataWrap([
      {'label': '委托价格(${order.quoteCurrency})', 'value': priceValue},
      {'label': '委托数量(${order.baseCurrency})', 'value': order.quantity},
      {'label': '已成交数量(${order.baseCurrency})', 'value': order.filledQuantity},
      {'label': '交易额(${order.quoteCurrency})', 'value': order.amount},
    ]);
  }

  // 构建订单操作按钮组
  Widget _buildOrderToggle(BuildContext context) {
    final order = this.order ?? SpotOrderModel.mock();

    // 根据订单状态决定显示哪些按钮
    final List<Widget> buttons = [];

    // 修改按钮 - 只有待成交和部分成交的订单可以修改
    if (order.canModify) {
      buttons.add(
        Expanded(
          child: CommonButton.secondary(
            '修改',
            size: CommonButtonSize.small,
            onPressed: onModify,
          ),
        ),
      );
    }

    // 止盈止损按钮 - 限价单和部分成交的订单可以设置
    if (order.canModify && order.orderType == SpotOrderType.limit) {
      if (buttons.isNotEmpty) {
        buttons.add(SizedBox(width: UiConstants.spacing10));
      }
      buttons.add(
        Expanded(
          child: CommonButton.secondary(
            order.hasStopProfitLoss ? '修改止盈止损' : '止盈止损',
            size: CommonButtonSize.small,
            onPressed: onSetStopProfitLoss,
          ),
        ),
      );
    }

    // 撤销按钮 - 只有待成交和部分成交的订单可以撤销
    if (order.canCancel) {
      if (buttons.isNotEmpty) {
        buttons.add(SizedBox(width: UiConstants.spacing10));
      }
      buttons.add(
        Expanded(
          child: CommonButton.secondary(
            '撤销',
            size: CommonButtonSize.small,
            onPressed: onCancel,
          ),
        ),
      );
    }

    // 如果没有按钮，返回空容器
    if (buttons.isEmpty) {
      return SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.only(top: UiConstants.spacing14),
      child: Row(children: buttons),
    );
  }
}

/// 订单数据项组件
/// 用于显示标签和对应的数值
class OrderDataItem extends StatelessWidget {
  const OrderDataItem({
    super.key,
    required this.label,
    required this.value,
    this.valueStyle,
    this.labelStyle,
    this.isEndAligned = false,
  });

  /// 标签文本
  final String label;

  /// 数值文本
  final String value;

  /// 数值样式（可选）
  final TextStyle? valueStyle;

  /// 标签样式（可选）
  final TextStyle? labelStyle;

  /// 是否右对齐（用于每行第三个数据项）
  final bool isEndAligned;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment:
          isEndAligned ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: labelStyle ?? context.templateStyle.text.descriptionSmall,
          textAlign: isEndAligned ? TextAlign.end : TextAlign.start,
        ),
        SizedBox(height: UiConstants.spacing2),
        Text(
          value,
          style: valueStyle ?? context.templateStyle.text.bodyTextMedium,
          textAlign: isEndAligned ? TextAlign.end : TextAlign.start,
        ),
      ],
    );
  }
}
