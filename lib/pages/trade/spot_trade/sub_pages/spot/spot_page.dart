/*
*  现货界面
*/

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/models/trade/spot_order_model.dart';
import 'package:qubic_exchange/services/storage/storage_service.dart';
import 'widgets/spot_header.dart';
import 'tabviews/index.dart';
import '../../../widgets/index.dart';
import 'package:qubic_exchange/widgets/trade_common/index.dart';
import 'package:qubic_exchange/routes/index.dart';

class SpotPage extends StatefulWidget {
  // 滚动距离变化回调
  final Function(double scrollOffset)? onScrollOffsetChanged;

  const SpotPage({super.key, this.onScrollOffsetChanged});

  @override
  State<SpotPage> createState() => _SpotPageState();
}

class _SpotPageState extends State<SpotPage> with TickerProviderStateMixin {
  // 创建标签控制器
  late TabController _mainController;

  // 滚动控制器
  late ScrollController _scrollController;

  // 创建标签项
  // '交易机器人' 暂时不做
  static const List<String> _mainKeys = ['委托', '资产', '跟单'];

  // ========== 订单数据状态管理 ==========

  /// 所有订单数据
  List<SpotOrderModel> _allOrders = [];

  /// 当前选中的订单类型索引 (0: 全部, 1: 限价｜市价, 2: 追踪委托, 3: 止盈止损, 4: 计划委托, 5: OCO)
  int _selectedOrderTypeIndex = 0;

  /// 当前选择的币种ID
  int? _selectedCurrencyId;

  /// 订单类型选项配置
  static final List<({String text, int index, List<SpotOrderType> types})>
  _orderTypeOptions = [
    (text: '全部', index: 0, types: <SpotOrderType>[]), // 空数组表示显示所有类型
    (
      text: '限价｜市价',
      index: 1,
      types: [SpotOrderType.limit, SpotOrderType.market],
    ),
    (text: '追踪委托', index: 2, types: [SpotOrderType.trailingOrder]),
    (text: '止盈止损', index: 3, types: [SpotOrderType.stopProfitLoss]),
    (text: '计划委托', index: 4, types: [SpotOrderType.planOrder]),
  ];

  // 初始化
  @override
  void initState() {
    super.initState();
    _mainController = TabController(length: _mainKeys.length, vsync: this);

    // 初始化滚动控制器并添加监听器
    _scrollController = ScrollController();
    _scrollController.addListener(_onScrollChanged);

    // 初始化订单数据
    _initializeOrderData();

    // 加载缓存的币种ID
    _loadSelectedCurrencyId();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScrollChanged);
    _mainController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 初始化订单数据
  void _initializeOrderData() {
    _allOrders = _createMockOrders();
  }

  /// 加载缓存的币种ID
  Future<void> _loadSelectedCurrencyId() async {
    try {
      final storage = StorageService.instance;
      final cachedCurrencyId = storage.getInt('selected_spot_currency_id');

      if (cachedCurrencyId != null) {
        setState(() {
          _selectedCurrencyId = cachedCurrencyId;
        });
        debugPrint('从缓存加载币种ID: $cachedCurrencyId');
      }
    } catch (e) {
      debugPrint('加载缓存币种ID失败: $e');
    }
  }

  /// 创建模拟订单数据
  List<SpotOrderModel> _createMockOrders() {
    return [
      SpotOrderModel.mock(
        symbol: 'BTC/USDT',
        direction: TradeDirection.buy,
        orderType: SpotOrderType.limit,
        status: SpotOrderStatus.pending,
      ),
      SpotOrderModel.mock(
        symbol: 'BGB/USDT',
        direction: TradeDirection.buy,
        orderType: SpotOrderType.planOrder,
        status: SpotOrderStatus.waitingExecution,
      ),
      SpotOrderModel.mock(
        symbol: 'ETH/USDT',
        direction: TradeDirection.sell,
        orderType: SpotOrderType.market,
        status: SpotOrderStatus.partialFilled,
      ),
      SpotOrderModel.mock(
        symbol: 'SOL/USDT',
        direction: TradeDirection.sell,
        orderType: SpotOrderType.oco,
        status: SpotOrderStatus.pending,
      ),
      SpotOrderModel.mock(
        symbol: 'ADA/USDT',
        direction: TradeDirection.buy,
        orderType: SpotOrderType.stopProfitLoss,
        status: SpotOrderStatus.waitingExecution,
      ),
      SpotOrderModel.mock(
        symbol: 'DOT/USDT',
        direction: TradeDirection.sell,
        orderType: SpotOrderType.trailingOrder,
        status: SpotOrderStatus.pending,
      ),
    ];
  }

  // 滚动变化监听
  void _onScrollChanged() {
    if (widget.onScrollOffsetChanged != null) {
      widget.onScrollOffsetChanged!(_scrollController.offset);
    }
  }

  /// 计算各类型订单数量
  Map<int, int> _calculateOrderCounts() {
    final counts = <int, int>{};

    for (final option in _orderTypeOptions) {
      if (option.types.isEmpty) {
        // 全部类型
        counts[option.index] = _allOrders.length;
      } else {
        // 特定类型
        counts[option.index] =
            _allOrders
                .where((order) => option.types.contains(order.orderType))
                .length;
      }
    }

    return counts;
  }

  /// 根据选中的类型过滤订单
  List<SpotOrderModel> _getFilteredOrders() {
    final selectedOption = _orderTypeOptions[_selectedOrderTypeIndex];

    if (selectedOption.types.isEmpty) {
      // 显示所有订单
      return _allOrders;
    } else {
      // 按类型过滤
      return _allOrders
          .where((order) => selectedOption.types.contains(order.orderType))
          .toList();
    }
  }

  /// 处理订单类型选择
  void _handleOrderTypeSelection(OrderTypeOption? selectedOption) {
    if (selectedOption == null) return;

    setState(() {
      _selectedOrderTypeIndex = selectedOption.index;
    });
  }

  /// 处理币种选择
  void _handleCurrencySelected(int currencyId) async {
    debugPrint('选择了币种ID: $currencyId');

    // 将选择的币种ID写入缓存
    final storage = StorageService.instance;
    await storage.setInt('selected_spot_currency_id', currencyId);

    // 更新UI状态
    setState(() {
      _selectedCurrencyId = currencyId;
    });
  }

  // 刷新
  Future<void> _onRefresh() async {
    // 模拟刷新操作
    await Future.delayed(const Duration(seconds: 2));
    // 这里可以添加实际的数据刷新逻辑
    setState(() {
      _initializeOrderData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 交易对
        TradingPair(onCurrencySelected: _handleCurrencySelected),

        // 主体内容
        Expanded(
          child: EasyRefresh.builder(
            header: ClassicHeader(
              clamping: true,
              position: IndicatorPosition.locator,
              triggerOffset: 34,
              processedDuration: const Duration(seconds: 1),
              safeArea: false,
              showMessage: false,
              showText: false,
              maxOverOffset: 40,
            ),
            onRefresh: _onRefresh,
            childBuilder: (context, physics) {
              return ScrollConfiguration(
                behavior: const ERScrollBehavior(),
                child: ExtendedNestedScrollView(
                  controller: _scrollController,
                  physics: physics,
                  onlyOneScrollInBody: true,
                  pinnedHeaderSliverHeightBuilder: () {
                    return 53.0;
                  },
                  headerSliverBuilder: (context, innerBoxIsScrolled) {
                    return <Widget>[
                      // 刷新指示器定位器
                      const HeaderLocator.sliver(clearExtent: false),
                      // 现货头部
                      SliverToBoxAdapter(
                        child: SpotHeader(currencyId: _selectedCurrencyId),
                      ),
                      // 吸顶 TabBar
                      SliverPersistentHeader(
                        pinned: true,
                        delegate: StickyDelegate(
                          maxHeight: 53,
                          child: _buildMainTabbar(),
                          backgroundColor: context.templateColors.surface,
                        ),
                      ),
                    ];
                  },
                  body: _buildMainTabView(physics),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  // 构建主标签页
  Widget _buildMainTabbar() {
    final orderCounts = _calculateOrderCounts();
    final currentOrderCount = orderCounts[_selectedOrderTypeIndex] ?? 0;
    final tabTitle = _getTabTitle(currentOrderCount);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      decoration: BoxDecoration(
        color: context.templateColors.surface,
        border: Border(
          top: BorderSide(width: 1, color: context.templateColors.divider),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TabbarWidget(
              controller: _mainController,
              tabs: [
                TabItem(
                  title: tabTitle,
                  showDropdownArrow: true,
                  onDropdownTap: () => _showOrderTypePicker(orderCounts),
                ),
                TabItem(title: '资产'),
                TabItem(title: '跟单'),
                // TabItem(title: '交易机器人'),
              ],
              showIndicator: false,
              labelPadding: EdgeInsets.only(right: UiConstants.spacing24),
              labelStyle: context.templateStyle.text.bodyTextMedium,
              selectedColor: context.templateColors.textPrimary,
              unselectedColor: context.templateColors.textSecondary,
            ),
          ),

          SizedBox(width: UiConstants.spacing18),

          InkWellWidget(
            onTap: () => NavigationService().navigateTo(AppRoutes.spotOrders),
            child: ThemedImage(
              name: 'orderHistory',
              size: 24,
              followTheme: true,
            ),
          ),
        ],
      ),
    );
  }

  /// 获取标签页标题
  String _getTabTitle(int count) {
    if (_selectedOrderTypeIndex == 0) {
      // 全部类型时显示"委托"
      return '委托($count)';
    } else {
      // 其他类型时显示具体类型名称
      final selectedOption = _orderTypeOptions[_selectedOrderTypeIndex];
      return '${selectedOption.text}($count)';
    }
  }

  /// 显示订单类型选择器
  void _showOrderTypePicker(Map<int, int> orderCounts) async {
    final options =
        _orderTypeOptions.map((option) {
          final count = orderCounts[option.index] ?? 0;
          return OrderTypeOption(
            text: '${option.text}($count)',
            index: option.index,
          );
        }).toList();

    final selectedOption = await OrderTypePicker.show(
      context,
      title: '当前委托',
      options: options,
      selectedOption: OrderTypeOption(
        text: options[_selectedOrderTypeIndex].text,
        index: _selectedOrderTypeIndex,
      ),
    );

    _handleOrderTypeSelection(selectedOption);
  }

  // 构建主标签页内容
  Widget _buildMainTabView(ScrollPhysics physics) {
    final filteredOrders = _getFilteredOrders();

    return TabBarView(
      controller: _mainController,
      children: [
        // 委托
        _AutomaticKeepAlive(
          child: SpotOrdersTab(
            physics: physics,
            orders: filteredOrders,
            selectedOrderTypeIndex: _selectedOrderTypeIndex,
            onOrderUpdated: _handleOrderUpdated,
          ),
        ),

        // 资产
        _AutomaticKeepAlive(child: SpotAssetsTab(physics: physics)),

        // 跟单
        _AutomaticKeepAlive(child: SpotCopyTradeTab(physics: physics)),

        // 交易机器人，暂时不做
        // _AutomaticKeepAlive(child: SpotTradingBotTab(physics: physics)),
      ],
    );
  }

  /// 处理订单更新（修改、撤销等）
  void _handleOrderUpdated(String orderId, SpotOrderModel? updatedOrder) {
    setState(() {
      final index = _allOrders.indexWhere((order) => order.orderId == orderId);
      if (index != -1) {
        if (updatedOrder != null) {
          // 更新订单
          _allOrders[index] = updatedOrder;
        } else {
          // 删除订单（撤销）
          _allOrders.removeAt(index);
        }
      }
    });
  }
}

// 自动保持状态的包装器
class _AutomaticKeepAlive extends StatefulWidget {
  final Widget child;

  const _AutomaticKeepAlive({required this.child});

  @override
  State<_AutomaticKeepAlive> createState() => _AutomaticKeepAliveState();
}

class _AutomaticKeepAliveState extends State<_AutomaticKeepAlive>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => true;
}
