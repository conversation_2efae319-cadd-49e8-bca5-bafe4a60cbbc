/*
*  跟单标签页内容
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/pages/trade/contract_trade/sub_pages/contract/widgets/contract_sticky.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/widgets/trade_common/index.dart';

class SpotCopyTradeTab extends StatefulWidget {
  final ScrollPhysics? physics;

  const SpotCopyTradeTab({super.key, this.physics});

  @override
  State<SpotCopyTradeTab> createState() => _SpotCopyTradeTabState();
}

class _SpotCopyTradeTabState extends State<SpotCopyTradeTab> {
  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: widget.physics,
      slivers: [
        // 下拉菜单 & 修改图标 - 吸顶
        SliverPersistentHeader(
          pinned: true,
          delegate: SimpleContractStickyDelegate(
            child: _buildDropdownAndModify(),
            height: 40,
            backgroundColor: context.templateColors.surface,
          ),
        ),
        // 跟单列表
        SliverList(
          delegate: SliverChildBuilderDelegate((context, index) {
            return Padding(
              padding: EdgeInsets.only(top: UiConstants.spacing32),
              child: EmptyWidget(
                text: '暂无跟单',
                showButton: true,
                buttonText: '跟单',
                textStyle: context.templateStyle.text.bodyTextMedium.copyWith(
                  fontSize: 12,
                  color: Colors.white,
                ),
                onButtonTap: () => {},
              ),
            );
          }, childCount: 1),
        ),
      ],
    );
  }

  // 构建下拉菜单 & 修改图标
  Widget _buildDropdownAndModify() {
    // 下拉菜单项
    Widget drowMenuItem({required String text, required VoidCallback onTap}) {
      return Padding(
        padding: EdgeInsets.only(right: UiConstants.spacing28),
        child: InkWellWidget(
          onTap: onTap,
          child: Row(
            children: [
              Text(
                text,
                style: context.templateStyle.text.descriptionText.copyWith(
                  fontSize: 13,
                ),
              ),
              ThemedImage(name: 'arrow_triangle_down', size: 14),
            ],
          ),
        ),
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: Row(
        children: [
          Row(
            children: [
              drowMenuItem(
                text: '明细',
                onTap:
                    () => {
                      DetailFilterSelector.show(
                        context,
                        options: [
                          DetailFilterOption(key: 'detail', text: '明细'),
                          DetailFilterOption(key: 'summary', text: '汇总'),
                        ],
                      ),
                    },
              ),
              drowMenuItem(
                text: '全部合约',
                onTap:
                    () => {
                      ContractSymbolSelector.show(
                        context,
                        options: [
                          ContractSymbolOption(key: 'all', text: '全部合约'),
                          ContractSymbolOption(key: 'btc', text: '当前合约'),
                        ],
                      ),
                    },
              ),
              drowMenuItem(
                text: '交易专家',
                onTap:
                    () => {
                      TradingExpertSelector.show(
                        context,
                        options: [
                          TradingExpertOption(key: 'all', text: '全部交易专家'),
                        ],
                      ),
                    },
              ),
            ],
          ),
          Spacer(),
          ThemedImage(name: 'edit', size: 20),
        ],
      ),
    );
  }
}
