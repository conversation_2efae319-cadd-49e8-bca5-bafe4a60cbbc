/*
*  委托标签页内容
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/utils/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/models/trade/spot_order_model.dart';
import 'package:qubic_exchange/pages/trade/widgets/edit_order/edit_order_dialog.dart';
import '../widgets/spot_order_item.dart';

/// 委托标签页配置常量
class _SpotOrdersTabConfig {
  static const double stickyHeaderHeight = 36.0;
  static const double bottomSpacingRatio = 0.1;
  static const double cancelButtonHeight = 26.0;
}

class SpotOrdersTab extends StatefulWidget {
  final ScrollPhysics? physics;

  /// 订单数据（从父组件传入）
  final List<SpotOrderModel>? orders;

  /// 当前选中的订单类型索引
  final int? selectedOrderTypeIndex;

  /// 订单更新回调（订单ID，更新后的订单数据，null表示删除）
  final void Function(String orderId, SpotOrderModel? updatedOrder)?
  onOrderUpdated;

  const SpotOrdersTab({
    super.key,
    this.physics,
    this.orders,
    this.selectedOrderTypeIndex,
    this.onOrderUpdated,
  });

  @override
  State<SpotOrdersTab> createState() => _SpotOrdersTabState();
}

class _SpotOrdersTabState extends State<SpotOrdersTab> {
  /// 是否只看当前交易对
  bool _onlyCurrentSymbol = false;

  /// 获取订单数据（优先使用父组件传入的数据）
  List<SpotOrderModel> get _orders {
    return widget.orders ?? [];
  }

  /// 获取过滤后的活跃订单
  List<SpotOrderModel> get _filteredOrders {
    if (!_onlyCurrentSymbol) {
      return _orders;
    }
    // 这里可以根据当前选择的交易对进行过滤
    // 暂时返回所有订单
    return _orders;
  }

  @override
  Widget build(BuildContext context) {
    final filteredOrders = _filteredOrders;

    return CustomScrollView(
      physics: widget.physics,
      slivers: [
        // 只看当前（吸顶）
        _buildStickyHeader(context),

        // 委托列表
        _buildOrdersList(context, filteredOrders),

        // 底部占位高度
        _buildBottomSpacing(context),
      ],
    );
  }

  /// 构建吸顶头部
  Widget _buildStickyHeader(BuildContext context) {
    return SliverPersistentHeader(
      pinned: true,
      delegate: StickyDelegate(
        child: _buildOnlyCurrent(),
        maxHeight: _SpotOrdersTabConfig.stickyHeaderHeight,
        minHeight: _SpotOrdersTabConfig.stickyHeaderHeight,
        backgroundColor: context.templateColors.surface,
      ),
    );
  }

  /// 构建订单列表
  Widget _buildOrdersList(BuildContext context, List<SpotOrderModel> orders) {
    if (orders.isEmpty) {
      return _buildEmptyState();
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        final isLast = index == orders.length - 1;
        return _buildOrderItem(orders[index], isLast: isLast);
      }, childCount: orders.length),
    );
  }

  /// 构建单个订单项
  Widget _buildOrderItem(SpotOrderModel order, {bool isLast = false}) {
    return SpotOrderItem(
      order: order,
      onModify: () => _handleModifyOrder(order),
      onSetStopProfitLoss: () => _handleSetStopProfitLoss(order),
      onCancel: () => _handleCancelOrder(order),
      showBottomBorder: !isLast,
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return SliverToBoxAdapter(
      child: Container(
        padding: EdgeInsets.all(UiConstants.spacing32),
        child: Column(
          children: [
            Icon(
              Icons.inbox_outlined,
              size: UiConstants.iconSize48,
              color: context.templateColors.textSecondary,
            ),
            SizedBox(height: UiConstants.spacing16),
            Text(
              '暂无委托订单',
              style: context.templateStyle.text.bodyText.copyWith(
                color: context.templateColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建底部间距
  Widget _buildBottomSpacing(BuildContext context) {
    return SliverToBoxAdapter(
      child: SizedBox(
        height:
            ScreenUtil.screenHeight(context) *
            _SpotOrdersTabConfig.bottomSpacingRatio,
      ),
    );
  }

  /// 构建只看当前交易对的控制栏
  Widget _buildOnlyCurrent() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: Row(
        children: [
          InkWellWidget(
            onTap: _toggleOnlyCurrentSymbol,
            child: Row(
              children: [
                CheckboxWidget(
                  value: _onlyCurrentSymbol,
                  customSize: UiConstants.iconSize14,
                  onChanged: (_) => _toggleOnlyCurrentSymbol(),
                ),
                SizedBox(width: UiConstants.spacing4),
                Text('只看当前', style: context.templateStyle.text.bodySmall),
              ],
            ),
          ),
          const Spacer(),
          CommonButton.secondary(
            '全部撤销',
            size: CommonButtonSize.small,
            height: _SpotOrdersTabConfig.cancelButtonHeight,
            onPressed: _handleCancelAllOrders,
          ),
        ],
      ),
    );
  }

  /// 切换只看当前交易对状态
  void _toggleOnlyCurrentSymbol() {
    setState(() {
      _onlyCurrentSymbol = !_onlyCurrentSymbol;
    });
  }

  /// 处理修改订单
  void _handleModifyOrder(SpotOrderModel order) async {
    // 使用现货订单编辑弹窗
    final modifiedOrder = await EditOrderDialog.showSpotOrder(
      context,
      orderData: order,
    );

    // 如果用户确认了修改，通知父组件更新订单
    if (modifiedOrder != null && widget.onOrderUpdated != null && mounted) {
      widget.onOrderUpdated!(order.orderId, modifiedOrder);

      // 显示成功提示
      if (mounted) {
        Toastification.show(context, message: '订单修改成功');
      }
    }
  }

  /// 处理设置止盈止损
  void _handleSetStopProfitLoss(SpotOrderModel order) async {
    debugPrint('设置止盈止损: ${order.orderId} - ${order.symbol}');

    // 使用止盈止损类型的弹窗
    final modifiedOrder = await EditOrderDialog.showStopProfitLoss(
      context,
      orderData: order,
    );

    // 如果用户确认了设置，通知父组件更新订单
    if (modifiedOrder != null && widget.onOrderUpdated != null && mounted) {
      widget.onOrderUpdated!(order.orderId, modifiedOrder);

      // 显示成功提示
      if (mounted) {
        Toastification.show(context, message: '止盈止损设置成功');
      }
    }
  }

  /// 处理撤销订单
  void _handleCancelOrder(SpotOrderModel order) {
    debugPrint('撤销订单: ${order.orderId} - ${order.symbol}');
    // TODO: 实现撤销订单逻辑
    // 可以显示确认对话框，然后调用API撤销订单
  }

  /// 处理全部撤销
  void _handleCancelAllOrders() {
    final cancelableOrders =
        _filteredOrders.where((order) => order.canCancel).toList();

    // 显示弹窗
    CenterDialog.showCancelAllConfirm(context);

    if (cancelableOrders.isEmpty) {
      debugPrint('没有可撤销的订单');
      return;
    }

    debugPrint('全部撤销 ${cancelableOrders.length} 个订单');
    // TODO: 实现全部撤销逻辑
    // 可以显示确认对话框，然后批量调用API撤销所有可撤销的订单
  }
}
