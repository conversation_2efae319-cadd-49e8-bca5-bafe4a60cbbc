/*
*  现货资产标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/models/trade/spot_asset_model.dart';
import 'package:qubic_exchange/services/trade/spot_asset_service.dart';
import 'package:remixicon/remixicon.dart';
import '../../../../widgets/index.dart';

class SpotAssetsTab extends StatefulWidget {
  final ScrollPhysics? physics;

  const SpotAssetsTab({super.key, this.physics});

  @override
  State<SpotAssetsTab> createState() => _SpotAssetsTabState();
}

class _SpotAssetsTabState extends State<SpotAssetsTab> {
  late SpotAssetService _assetService;

  @override
  void initState() {
    super.initState();
    _assetService = SpotAssetService();
    _loadAssets();
  }

  @override
  void dispose() {
    _assetService.removeListener(_onAssetDataChanged);
    super.dispose();
  }

  /// 加载资产数据
  Future<void> _loadAssets() async {
    _assetService.addListener(_onAssetDataChanged);
    await _assetService.loadAssets();
  }

  /// 资产数据变化监听
  void _onAssetDataChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: widget.physics,
      slivers: [
        // 只看当前（吸顶）
        SliverPersistentHeader(
          pinned: true,
          delegate: StickyDelegate(
            child: _buildOnlyCurrent(),
            maxHeight: 24,
            minHeight: 24,
            backgroundColor: context.templateColors.surface,
          ),
        ),

        // 资产列表
        SliverList(
          delegate: SliverChildBuilderDelegate((context, index) {
            final assets = _assetService.filteredAssets;
            if (index < assets.length) {
              return _buildAssetsItem(assets[index]);
            }
            return SizedBox.shrink();
          }, childCount: _assetService.filteredAssets.length),
        ),
      ],
    );
  }

  // 构建只看当前
  Widget _buildOnlyCurrent() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: Row(
        children: [
          InkWellWidget(
            onTap: () {
              _assetService.toggleShowOnlyWithBalance();
            },
            child: Row(
              children: [
                CheckboxWidget(
                  value: _assetService.showOnlyWithBalance,
                  customSize: UiConstants.iconSize14,
                  onChanged:
                      (value) => _assetService.toggleShowOnlyWithBalance(),
                ),
                SizedBox(width: UiConstants.spacing4),
                Text('只看当前', style: context.templateStyle.text.bodySmall),
              ],
            ),
          ),

          Spacer(),
          InkWellWidget(
            onTap: _handleSortByValue,
            child: Icon(
              _getSortIcon(),
              size: UiConstants.iconSize20,
              color: context.templateColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  // 构建资产项
  Widget _buildAssetsItem(SpotAssetModel asset) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing14,
      ),
      decoration: BoxDecoration(
        color: context.templateColors.surface,
        border: Border(
          bottom: BorderSide(
            width: context.templateStyles.borderWidthThin,
            color: context.templateColors.border,
          ),
        ),
      ),
      child: Column(
        children: [
          /// 货币图标 & 名称 & 分析按钮
          Row(
            children: [
              ThemedImage.crypto(
                asset.effectiveIconName,
                size: UiConstants.iconSize24,
                borderRadius: BorderRadius.circular(
                  UiConstants.borderRadiusCircle,
                ),
                margin: EdgeInsets.only(right: UiConstants.spacing10),
              ),
              Text(
                asset.symbol,
                style: context.templateStyle.text.bodyLargeMedium,
              ),
              Spacer(),
              InkWellWidget(
                onTap:
                    () => {
                      // TODO 跳转至分析界面
                    },
                child: Row(
                  children: [
                    Text(
                      '分析',
                      style: context.templateStyle.text.descriptionSmall,
                    ),
                    SizedBox(width: UiConstants.spacing4),
                    Icon(
                      RemixIcons.arrow_right_s_line,
                      size: UiConstants.iconSize16,
                      color: context.templateColors.textSecondary,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: UiConstants.spacing14),

          /// 累计盈亏 & 累计盈亏率
          if (asset.formattedTotalPnL.isNotEmpty)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '累计盈亏(USDT)',
                      style: context.templateStyle.text.descriptionSmall
                          .copyWith(
                            decorationStyle: TextDecorationStyle.dotted,
                            decoration: TextDecoration.underline,
                            decorationColor:
                                context.templateColors.textTertiary,
                          ),
                    ),
                    Text(
                      asset.formattedTotalPnL,
                      style: context.templateStyle.text.bodyLargeMedium
                          .copyWith(
                            color:
                                asset.hasPnLData
                                    ? (asset.isPositivePnL
                                        ? context.templateColors.tradeBuy
                                        : context.templateColors.tradeSell)
                                    : context.templateColors.textSecondary,
                          ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '累计盈亏率',
                      style: context.templateStyle.text.descriptionSmall
                          .copyWith(
                            decorationStyle: TextDecorationStyle.dotted,
                            decoration: TextDecoration.underline,
                            decorationColor:
                                context.templateColors.textTertiary,
                          ),
                    ),
                    Text(
                      asset.formattedTotalPnLRate,
                      style: context.templateStyle.text.bodyLargeMedium
                          .copyWith(
                            color:
                                asset.hasPnLData
                                    ? (asset.isPositivePnL
                                        ? context.templateColors.tradeBuy
                                        : context.templateColors.tradeSell)
                                    : context.templateColors.textSecondary,
                          ),
                    ),
                  ],
                ),
              ],
            ),
          SizedBox(height: UiConstants.spacing10),

          /// 总额 & 占用 & 可用
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '总额',
                      style: context.templateStyle.text.descriptionSmall
                          .copyWith(
                            decorationStyle: TextDecorationStyle.dotted,
                            decoration: TextDecoration.underline,
                            decorationColor:
                                context.templateColors.textTertiary,
                          ),
                    ),
                    Text(
                      asset.formattedTotalAmount,
                      style: context.templateStyle.text.bodyTextMedium,
                    ),
                    if (asset.formattedFiatValue.isNotEmpty)
                      Text(
                        asset.formattedFiatValue,
                        style: context.templateStyle.text.hintText,
                      ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '占用',
                      style: context.templateStyle.text.descriptionSmall
                          .copyWith(
                            decorationStyle: TextDecorationStyle.dotted,
                            decoration: TextDecoration.underline,
                            decorationColor:
                                context.templateColors.textTertiary,
                          ),
                    ),
                    Text(
                      asset.formattedFrozenAmount,
                      style: context.templateStyle.text.bodyTextMedium,
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '可用',
                      style: context.templateStyle.text.descriptionSmall
                          .copyWith(
                            decorationStyle: TextDecorationStyle.dotted,
                            decoration: TextDecoration.underline,
                            decorationColor:
                                context.templateColors.textTertiary,
                          ),
                    ),
                    Text(
                      asset.formattedAvailableAmount,
                      style: context.templateStyle.text.bodyTextMedium,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: UiConstants.spacing10),

          /// 成本价
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWellWidget(
                onTap: _handlePriceTypeSwitch,
                child: Row(
                  children: [
                    Text(
                      _getPriceTypeDisplayText(_assetService.selectedPriceType),
                      style: context.templateStyle.text.descriptionSmall,
                    ),
                    ThemedImage(
                      name: 'arrow_triangle_down_gray',
                      size: UiConstants.iconSize10,
                      margin: EdgeInsets.only(left: UiConstants.spacing4),
                    ),
                  ],
                ),
              ),
              Text(
                _formatPriceDisplay(asset),
                style: context.templateStyle.text.bodyTextMedium,
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 获取价格类型显示文本
  String _getPriceTypeDisplayText(String type) {
    switch (type) {
      case 'cost_asset':
        return '成本价(USDT)';
      case 'breakeven_trade':
        return '保本价(USDT)';
      default:
        return '成本价(USDT)';
    }
  }

  // 格式化价格显示
  String _formatPriceDisplay(SpotAssetModel asset) {
    final price = asset.getPriceByType(_assetService.selectedPriceType);
    if (price == null) return '--';
    return price.toStringAsFixed(2);
  }

  // 处理按总额估值排序
  void _handleSortByValue() {
    _assetService.sortByValue();

    // 显示排序提示
    final sortDirection = _assetService.sortAscending ? '升序' : '降序';
    Toastification.show(context, message: '总额估值按-$sortDirection排序');
  }

  // 获取排序图标
  IconData _getSortIcon() {
    if (_assetService.sortType != 'value') {
      // 不是按总额排序时，显示默认排序图标
      return RemixIcons.sort_desc;
    }

    // 按总额排序时，根据方向显示不同图标
    return _assetService.sortAscending
        ? RemixIcons.sort_asc
        : RemixIcons.sort_desc;
  }

  // 处理价格类型切换
  void _handlePriceTypeSwitch() async {
    final result = await PositionPriceTypeSwitchDialog.show(
      context,
      currentType: _assetService.selectedPriceType,
    );

    if (result != null &&
        result != _assetService.selectedPriceType &&
        mounted) {
      _assetService.switchPriceType(result);
    }
  }
}
