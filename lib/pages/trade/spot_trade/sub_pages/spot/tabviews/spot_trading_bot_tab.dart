/*
*  交易机器人标签页内容
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/pages/trade/contract_trade/sub_pages/contract/widgets/contract_sticky.dart';
import 'package:qubic_exchange/widgets/index.dart';

class SpotTradingBotTab extends StatefulWidget {
  final ScrollPhysics? physics;

  const SpotTradingBotTab({super.key, this.physics});

  @override
  State<SpotTradingBotTab> createState() => _SpotTradingBotTabState();
}

class _SpotTradingBotTabState extends State<SpotTradingBotTab>
    with TickerProviderStateMixin {
  // 构建选项卡控制器
  late TabController _tabController;

  // 构建选项
  static const List<String> _tabKeys = [
    '现货网格',
    '现货天地网格',
    '现货马丁格尔',
    '现货CTA',
    '现货定投',
    '囤币宝',
  ];

  // 初始化
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabKeys.length, vsync: this);
  }

  // 销毁
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: widget.physics,
      slivers: [
        // 选项卡 & 只看当前 - 吸顶
        SliverPersistentHeader(
          pinned: true,
          delegate: ContractStickyDelegate(
            maxHeight: 88,
            child: _buildTabbarAndOnlyCurrent(),
          ),
        ),
        // 交易机器人列表
        SliverFillRemaining(
          fillOverscroll: false,
          hasScrollBody: false,
          child: EmptyWidget(text: '暂无交易机器人'),
        ),
      ],
    );
  }

  // 构建选项卡 & 只看当前部分
  Widget _buildTabbarAndOnlyCurrent() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing4,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // 关键：让Column只占用必要的空间
        children: [
          TabbarWidget(
            height: 24,
            controller: _tabController,
            tabs: _tabKeys.map((key) => TabItem(title: key)).toList(),
            labelStyle: context.templateStyle.text.hintTextMedium.copyWith(
              fontSize: 13,
            ),
            selectedColor: context.templateColors.textPrimary,
            unselectedColor: context.templateColors.textSecondary,
            showIndicator: true,
            indicatorStyle: TabBarIndicatorStyle.filled,
            labelPadding: EdgeInsets.zero,
            tabPadding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
          ),

          SizedBox(height: UiConstants.spacing8), // 添加间距
          // 只看当前
          Row(
            children: [
              Checkbox(value: false, onChanged: (value) => {}),
              Text(
                '只看当前',
                style: context.templateStyle.text.bodyText.copyWith(
                  fontSize: 13,
                ),
              ),
              Spacer(),
              CommonButton.secondary(
                '创建交易机器人',
                size: CommonButtonSize.small,
                height: 26,
                onPressed: () => {},
              ),
            ],
          ),
        ],
      ),
    );
  }
}
