/*
* 链上交易对
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class OnChainPair extends StatefulWidget {
  const OnChainPair({super.key});

  @override
  State<OnChainPair> createState() => _OnChainPairState();
}

class _OnChainPairState extends State<OnChainPair> {
  @override
  Widget build(BuildContext context) {
    return _buildPair();
  }

  // 构建交易对信息
  Widget _buildPair() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing10,
      ),
      child: Row(
        children: [
          InkWellWidget(
            onTap:
                () => {
                  // TODO 打开交易对选择弹窗
                },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(
                        UiConstants.borderRadiusCircle,
                      ),
                      child: ThemedImage(size: 40, name: 'btc'),
                    ),

                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            width: 2,
                            color: context.templateColors.surface,
                          ),
                          borderRadius: BorderRadius.circular(
                            UiConstants.borderRadiusCircle,
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(
                            UiConstants.borderRadiusCircle,
                          ),
                          child: ThemedImage(name: 'sol', size: 20),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(width: UiConstants.spacing12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text('BTC', style: context.templateStyle.text.h4),
                        Padding(
                          padding: EdgeInsets.only(left: UiConstants.spacing4),
                          child: ThemedImage(
                            name: 'arrow_triangle_down',
                            size: 16,
                            followTheme: true,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Text(
                          '\$0.01954',
                          style: context.templateStyle.text.hintTextMedium
                              .copyWith(
                                color: context.templateColors.textPrimary,
                              ),
                        ),
                        SizedBox(width: UiConstants.spacing8),
                        Text(
                          '+8.02%',
                          style: context.templateStyle.text.hintTextMedium
                              .copyWith(color: context.templateColors.primary),
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: UiConstants.spacing12),
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: UiConstants.spacing4,
                            ),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: context.templateColors.divider,
                              ),
                              borderRadius: BorderRadius.circular(
                                UiConstants.borderRadius4,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  '价格',
                                  style: context.templateStyle.text.hintText,
                                ),
                                SizedBox(width: UiConstants.spacing4),
                                Icon(
                                  RemixIcons.repeat_line,
                                  color: context.templateColors.textTertiary,
                                  size: 12,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),

          Spacer(),
          Row(
            children: [
              // k线图按钮
              InkWellWidget(
                onTap:
                    () => {
                      // TODO 跳转至k线图界面
                    },
                child: ThemedImage(
                  name: 'strategy_kline',
                  size: 24,
                  followTheme: true,
                ),
              ),
              InkWellWidget(
                onTap:
                    () => {
                      // TODO 显示更多操作菜单
                    },
                child: Padding(
                  padding: EdgeInsets.only(left: UiConstants.spacing16),
                  child: ThemedImage(name: 'more', size: 24, followTheme: true),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
