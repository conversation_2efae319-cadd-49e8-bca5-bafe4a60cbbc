/*
*  链上交易表单
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';
import 'package:qubic_exchange/widgets/trade_common/index.dart';

class OnChainForm extends StatefulWidget {
  const OnChainForm({super.key});

  @override
  State<OnChainForm> createState() => _OnChainFormState();
}

class _OnChainFormState extends State<OnChainForm> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing18),
      child: Column(
        children: [
          // 表单顶部
          _buildFormTop(),

          // 价格 & 数量
          _buildFormInput(),

          // 止盈止损 & 优先模式 & 预估到账
          _buildFormBottom(),

          SizedBox(height: UiConstants.spacing14),
          // 按钮
          CommonButton.buy(
            '登录',
            size: CommonButtonSize.medium,
            borderRadius: UiConstants.borderRadius8,
            width: double.infinity,
            onPressed: () => {},
          ),
        ],
      ),
    );
  }

  // 表单顶部
  Widget _buildFormTop() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 模式切换
        Expanded(
          flex: 1,
          child: PositionActiontoggle(
            tabKeys: ['买入', '卖出'],
            initialIndex: 0,
            selectedColor: Colors.white,
            indicatorColor: context.templateColors.tradeBuy,
            onChanged: (index) {
              setState(() {});
            },
          ),
        ),

        // 类型切换
        Expanded(
          child: InkWellWidget(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text('市价', style: context.templateStyle.text.descriptionText),
                SizedBox(width: UiConstants.spacing4),
                ThemedImage(
                  name: 'arrow_triangle_down',
                  size: 14,
                  followTheme: true,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 表单输入
  Widget _buildFormInput() {
    return Container(
      decoration: BoxDecoration(
        color: context.templateColors.surface,
        borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
      ),
      margin: EdgeInsets.symmetric(vertical: UiConstants.spacing14),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(UiConstants.spacing20),
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      '交易额',
                      style: context.templateStyle.text.bodyTextMedium,
                    ),
                    Spacer(),
                    Row(
                      children: [
                        Text.rich(
                          TextSpan(
                            style: context.templateStyle.text.descriptionText,
                            children: [
                              TextSpan(text: '可用'),
                              TextSpan(
                                text: '-- USDT',
                                style: context.templateStyle.text.bodyText,
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: UiConstants.spacing4),

                        InkWellWidget(
                          child: Icon(
                            RemixIcons.add_box_fill,
                            size: 16,
                            color: context.templateColors.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                // 交易额输入
                Padding(
                  padding: EdgeInsets.only(top: UiConstants.spacing10),
                  child: TextFieldWidget(
                    height: 55,
                    hintText: '≥5',
                    focusBorderColor: Colors.transparent,
                    hintStyle: context.templateStyle.text.h2.copyWith(
                      color: context.templateColors.textTertiary,
                      fontWeight: UiConstants.fontWeightMedium,
                    ),
                    textStyle: context.templateStyle.text.h2.copyWith(
                      fontWeight: UiConstants.fontWeightMedium,
                    ),
                    suffixIcon: Text(
                      'USDT',
                      style: context.templateStyle.text.h3.copyWith(
                        color: context.templateColors.textSecondary,
                        fontWeight: UiConstants.fontWeightMedium,
                      ),
                    ),
                    borderColor: Colors.transparent,
                    padding: EdgeInsets.zero,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ),
          _buildCommonAmounts(),
        ],
      ),
    );
  }

  // 常用交易额选择
  Widget _buildCommonAmounts() {
    /// 常用交易额项
    Widget buildItem({
      int flex = 1,
      String value = '',
      required VoidCallback onTap,
      bool isButton = false,
      bool showBorder = true,
    }) {
      return Expanded(
        flex: flex,
        child: InkWellWidget(
          onTap: onTap,
          child: Container(
            height: 24,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              border: Border(
                right:
                    showBorder
                        ? BorderSide(
                          width: 2,
                          color: context.templateColors.surface,
                        )
                        : BorderSide.none,
              ),
            ),
            child:
                isButton
                    ? Icon(
                      RemixIcons.edit_fill,
                      color: context.templateColors.textPrimary,
                      size: 14,
                    )
                    : Text(
                      value,
                      style: context.templateStyle.text.descriptionText
                          .copyWith(color: context.templateColors.textPrimary),
                    ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(width: 2, color: context.templateColors.surface),
        ),
      ),
      child: Row(
        children: [
          buildItem(flex: 3, value: '10', onTap: () => {}),
          buildItem(flex: 3, value: '50', onTap: () => {}),
          buildItem(flex: 3, value: '100', onTap: () => {}),
          buildItem(
            flex: 2,
            onTap: () => {},
            isButton: true,
            showBorder: false,
          ),
        ],
      ),
    );
  }

  // 止盈止损 & 优先模式 & 预估到账
  Widget _buildFormBottom() {
    return SizedBox(
      child: Column(
        children: [
          // 止盈止损
          TakeProfitStopLoss(
            isContract: false,
            showAdvanced: false,
            takeProfitText: '止盈价格',
            stopLossText: '止损价格',
            takeProfitSuffix: Padding(
              padding: EdgeInsets.only(right: UiConstants.spacing12),
              child: Text(
                'USDT',
                style: context.templateStyle.text.descriptionText,
              ),
            ),
            stopLossSuffix: Padding(
              padding: EdgeInsets.only(right: UiConstants.spacing12),
              child: Text(
                'USDT',
                style: context.templateStyle.text.descriptionText,
              ),
            ),
            onExpandChanged: (isExpanded) {},
          ),
          SizedBox(height: UiConstants.spacing8),

          // 优先模式
          Row(
            children: [
              Text('优先模式', style: context.templateStyle.text.descriptionText),
              Spacer(),
              Text('价格优先  --', style: context.templateStyle.text.bodyText),
              SizedBox(width: UiConstants.spacing4),
              Icon(
                RemixIcons.edit_2_fill,
                size: 14,
                color: context.templateColors.textPrimary,
              ),
            ],
          ),
          SizedBox(height: UiConstants.spacing4),

          // 预估到账
          Row(
            children: [
              Text(
                '预估到账链上账户',
                style: context.templateStyle.text.descriptionText.copyWith(
                  decorationStyle: TextDecorationStyle.dotted,
                  decoration: TextDecoration.underline,
                  decorationColor: context.templateColors.textTertiary,
                ),
              ),
              Spacer(),
              Text('-- BTC', style: context.templateStyle.text.bodyText),
            ],
          ),
        ],
      ),
    );
  }
}
