/*
*  链上交易
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'widgets/expot_widget.dart';
import 'tabviews/on_chain_assets_tab.dart';
import 'tabviews/on_chain_order_tab.dart';
import 'package:qubic_exchange/widgets/trade_common/dialogs/order_type_picker.dart';

class OnChainPage extends StatefulWidget {
  // 滚动距离变化回调
  final Function(double scrollOffset)? onScrollOffsetChanged;

  const OnChainPage({super.key, this.onScrollOffsetChanged});

  @override
  State<OnChainPage> createState() => _OnChainPageState();
}

class _OnChainPageState extends State<OnChainPage>
    with TickerProviderStateMixin {
  // 标签控制器
  late TabController _mainController;

  // 当前选中的委托类型
  String _selectedOrderType = '全部';

  // 标签内容
  List<TabItem> get _mainKeys => [
    TabItem(title: '资产(0)'),
    TabItem(
      title: '${_selectedOrderType == '全部' ? '委托' : _selectedOrderType}(0)',
      showDropdownArrow: true,
      onDropdownTap: _showOrderTypeSelector,
    ),
  ];

  // 滚动控制器
  late ScrollController _scrollController;

  // 初始化
  @override
  void initState() {
    super.initState();
    _mainController = TabController(length: _mainKeys.length, vsync: this);
    _scrollController = ScrollController();
    // 添加滚动监听器
    _scrollController.addListener(_onScrollChanged);
  }

  // 销毁
  @override
  void dispose() {
    _scrollController.removeListener(_onScrollChanged);
    _scrollController.dispose();
    _mainController.dispose();
    super.dispose();
  }

  // 滚动变化监听
  void _onScrollChanged() {
    if (widget.onScrollOffsetChanged != null) {
      widget.onScrollOffsetChanged!(_scrollController.offset);
    }
  }

  // 获取当前选中项的索引
  int _getCurrentSelectedIndex() {
    switch (_selectedOrderType) {
      case '全部':
        return 0;
      case '市价':
        return 1;
      case '限价':
        return 2;
      case '止盈止损':
        return 3;
      default:
        return 0;
    }
  }

  // 根据索引获取选项
  OrderTypeOption _getOptionByIndex(int index) {
    final options = [
      OrderTypeOption(text: '全部', index: 0),
      OrderTypeOption(text: '市价', index: 1),
      OrderTypeOption(text: '限价', index: 2),
      OrderTypeOption(text: '止盈止损', index: 3),
    ];
    return options[index];
  }

  // 显示委托类型选择器
  void _showOrderTypeSelector() async {
    // 获取当前选中项的索引
    final currentIndex = _getCurrentSelectedIndex();

    // 使用独立的弹窗组件
    final selectedType = await OrderTypePicker.show(
      context,
      title: '当前委托',
      selectedOption: _getOptionByIndex(currentIndex),
      options: [
        OrderTypeOption(text: '全部', index: 0),
        OrderTypeOption(text: '市价', index: 1),
        OrderTypeOption(text: '限价', index: 2),
        OrderTypeOption(text: '止盈止损', index: 3),
      ],
    );

    // 如果用户选择了类型
    if (selectedType != null) {
      setState(() {
        _selectedOrderType = selectedType.text;
      });

      // 这里可以添加根据选择的委托类型刷新数据的逻辑
    }
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    // 模拟刷新操作
    await Future.delayed(const Duration(seconds: 2));
    // 这里可以添加实际的数据刷新逻辑
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 交易对
        OnChainPair(),

        // 表单
        Expanded(
          child: EasyRefresh.builder(
            header: ClassicHeader(
              clamping: true,
              position: IndicatorPosition.locator,
              triggerOffset: 34,
              processedDuration: const Duration(seconds: 1),
              safeArea: false,
              showMessage: false,
              showText: false,
              maxOverOffset: 40,
            ),
            onRefresh: _onRefresh,
            childBuilder: (context, physics) {
              return ScrollConfiguration(
                behavior: const ERScrollBehavior(),
                child: ExtendedNestedScrollView(
                  controller: _scrollController,
                  physics: physics,
                  onlyOneScrollInBody: true,
                  pinnedHeaderSliverHeightBuilder: () {
                    return 53.0;
                  },
                  headerSliverBuilder: (context, innerBoxIsScrolled) {
                    return <Widget>[
                      // 刷新指示器定位器
                      const HeaderLocator.sliver(clearExtent: false),
                      // 现货头部
                      SliverToBoxAdapter(child: OnChainForm()),
                      // 吸顶 TabBar
                      SliverPersistentHeader(
                        pinned: true,
                        delegate: StickyDelegate(
                          maxHeight: 53,
                          child: _buildMainTabbar(),
                          backgroundColor: context.templateColors.surface,
                        ),
                      ),
                    ];
                  },
                  body: _buildMainTabView(physics),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  // 构建主标签栏
  Widget _buildMainTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(width: 1, color: context.templateColors.divider),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TabbarWidget(
              height: 38,
              tabs: _mainKeys,
              controller: _mainController,
              labelStyle: context.templateStyle.text.bodyTextMedium,
              selectedColor: context.templateColors.textPrimary,
              unselectedColor: context.templateColors.textSecondary,
              showIndicator: false,
              labelPadding: EdgeInsets.only(right: UiConstants.spacing18),
            ),
          ),
          SizedBox(width: UiConstants.spacing10),

          InkWellWidget(
            child: ThemedImage(
              name: 'orderHistory',
              size: 24,
              followTheme: true,
            ),
          ),
        ],
      ),
    );
  }

  // 构建主标签页内容
  Widget _buildMainTabView(ScrollPhysics physics) {
    return TabBarView(
      controller: _mainController,
      children: [
        // 资产
        _AutomaticKeepAlive(child: OnChainAssetsTab(physics: physics)),

        // 委托
        _AutomaticKeepAlive(child: OnChainOrderTab(physics: physics)),
      ],
    );
  }
}

// 自动保持状态的包装器
class _AutomaticKeepAlive extends StatefulWidget {
  final Widget child;

  const _AutomaticKeepAlive({required this.child});

  @override
  State<_AutomaticKeepAlive> createState() => _AutomaticKeepAliveState();
}

class _AutomaticKeepAliveState extends State<_AutomaticKeepAlive>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => true;
}
