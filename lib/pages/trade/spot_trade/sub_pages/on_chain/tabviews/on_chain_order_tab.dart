/*
*  链上委托标签页内容
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class OnChainOrderTab extends StatefulWidget {
  final ScrollPhysics? physics;

  const OnChainOrderTab({super.key, this.physics});

  @override
  State<OnChainOrderTab> createState() => _OnChainOrderTabState();
}

class _OnChainOrderTabState extends State<OnChainOrderTab> {
  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: widget.physics,
      slivers: [
        // 只看当前（吸顶）
        SliverPersistentHeader(
          pinned: true,
          delegate: StickyDelegate(
            child: _buildOnlyCurrent(),
            maxHeight: 24,
            minHeight: 24,
            backgroundColor: context.templateColors.surface,
          ),
        ),

        // 委托列表
        SliverList(
          delegate: SliverChildBuilderDelegate((context, index) {
            return Padding(
              padding: EdgeInsets.only(top: UiConstants.spacing32),
              child: EmptyWidget(text: '暂无委托'),
            );
          }, childCount: 1),
        ),
      ],
    );
  }

  // 构建只看当前
  Widget _buildOnlyCurrent() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: Row(
        children: [
          InkWellWidget(
            child: Row(
              children: [
                Checkbox(value: false, onChanged: (value) => {}),
                SizedBox(width: UiConstants.spacing4),
                Text(
                  '只看当前',
                  style: context.templateStyle.text.bodyText.copyWith(
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),

          Spacer(),
          CommonButton.secondary(
            '全部撤销',
            size: CommonButtonSize.small,
            height: 26,
            onPressed: () => {},
          ),
        ],
      ),
    );
  }
}
