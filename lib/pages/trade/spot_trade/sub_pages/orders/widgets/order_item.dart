/*
*  订单项组件
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/models/trade/order_model.dart';
import 'package:qubic_exchange/models/trade/spot_order_model.dart';
import 'package:qubic_exchange/pages/trade/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class OrderItem extends StatefulWidget {
  /// 订单数据
  final OrderModel order;

  /// 修改订单回调
  final VoidCallback? onModify;

  /// 设置止盈止损回调
  final VoidCallback? onSetStopLoss;

  /// 撤销订单回调
  final VoidCallback? onCancel;

  /// 点击订单回调
  final VoidCallback? onTap;

  const OrderItem({
    super.key,
    required this.order,
    this.onModify,
    this.onSetStopLoss,
    this.onCancel,
    this.onTap,
  });

  @override
  State<OrderItem> createState() => _OrderItemState();
}

class _OrderItemState extends State<OrderItem> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing18,
          vertical: UiConstants.spacing12,
        ),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              width: context.templateStyles.borderWidthThin,
              color: context.templateColors.border,
            ),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头部信息：交易对和时间
            _buildHeader(),

            SizedBox(height: UiConstants.spacing8),

            // 标签行：买卖方向和订单类型
            _buildTags(),

            SizedBox(height: UiConstants.spacing12),

            // 订单详情信息
            _buildOrderDetails(),

            // 如果订单可操作，显示操作按钮
            if (_shouldShowActionButtons()) ...[
              SizedBox(height: UiConstants.spacing12),
              _buildActionButtons(),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建头部信息
  Widget _buildHeader() {
    return Row(
      children: [
        Text(
          widget.order.symbol,
          style: context.templateStyle.text.bodyLargeMedium,
        ),
        Icon(
          RemixIcons.arrow_right_s_line,
          size: UiConstants.iconSize14,
          color: context.templateColors.textPrimary,
        ),
        Spacer(),
        Text(
          _formatDate(widget.order.createdAt),
          style: context.templateStyle.text.descriptionSmall,
        ),
      ],
    );
  }

  /// 构建标签
  Widget _buildTags() {
    return Row(
      children: [
        // 买卖方向标签
        TagWidget(
          text: widget.order.side.displayName,
          textStyle: context.templateStyle.text.tagSmall.copyWith(
            color:
                widget.order.isBuyOrder
                    ? context.templateColors.tradeBuy
                    : context.templateColors.tradeSell,
          ),
          margin: EdgeInsets.only(right: UiConstants.spacing6),
          borderWidth: context.templateStyles.borderWidthThin,
          borderColor:
              widget.order.isBuyOrder
                  ? context.templateColors.tradeBuy
                  : context.templateColors.tradeSell,
          padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
        ),

        // 订单类型标签
        TagWidget(
          text: widget.order.orderType.displayName,
          textStyle: context.templateStyle.text.tagSmall,
          margin: EdgeInsets.only(right: UiConstants.spacing6),
          borderWidth: context.templateStyles.borderWidthThin,
          borderColor: context.templateColors.textSecondary,
          padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
        ),

        // 状态标签（只显示特定状态）
        if (_shouldShowStatusTag())
          TagWidget(
            text: widget.order.status.displayName,
            textStyle: context.templateStyle.text.tagSmall.copyWith(
              color: _getStatusColor(),
            ),
            borderWidth: context.templateStyles.borderWidthThin,
            borderColor: _getStatusColor(),
            padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
          ),
      ],
    );
  }

  /// 构建订单详情信息
  Widget _buildOrderDetails() {
    return Column(
      children: [
        // 触发价格（条件单）
        if (widget.order.triggerPrice != null)
          _buildInfoItem(
            label: '触发价格',
            value: '≤${widget.order.triggerPrice} USDT',
          ),

        // 价格信息
        if (widget.order.price != null)
          _buildInfoItem(label: '价格', value: '${widget.order.price} USDT'),

        // 数量信息
        _buildInfoItem(
          label: '委托数量',
          value: '${widget.order.quantity} ${_getBaseCurrency()}',
        ),

        // 交易额
        _buildInfoItem(label: '交易额', value: '${_calculateTradeAmount()} USDT'),

        // 止盈止损（可编辑显示）
        _buildInfoItem(
          label: '止盈止损',
          value: _getStopProfitLossDisplay(),
          showEdit: true,
        ),

        // 已成交数量（如果有成交）
        if (double.parse(widget.order.filledQuantity) > 0)
          _buildInfoItem(
            label: '已成交',
            value: '${widget.order.filledQuantity} ${_getBaseCurrency()}',
          ),
      ],
    );
  }

  /// 构建信息项
  Widget _buildInfoItem({
    required String label,
    required String value,
    bool showEdit = false,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: context.templateStyle.text.descriptionSmall),
          Spacer(),
          Text(value, style: context.templateStyle.text.bodySmallMedium),
          if (showEdit)
            Icon(
              RemixIcons.edit_line,
              size: UiConstants.iconSize10,
              color: context.templateColors.textSecondary,
            ),
        ],
      ),
    );
  }

  /// 构建操作按钮组
  Widget _buildActionButtons() {
    return Row(
      children: [
        // 修改按钮
        Expanded(
          child: CommonButton.secondary(
            '修改',
            size: CommonButtonSize.small,
            onPressed: _handleModifyOrder,
          ),
        ),
        SizedBox(width: UiConstants.spacing10),

        // 止盈止损按钮
        Expanded(
          child: CommonButton.secondary(
            '止盈止损',
            size: CommonButtonSize.small,
            onPressed: _handleSetStopLoss,
          ),
        ),
        SizedBox(width: UiConstants.spacing10),

        // 撤销按钮
        Expanded(
          child: CommonButton.secondary(
            '撤销',
            size: CommonButtonSize.small,
            onPressed: widget.onCancel,
          ),
        ),
      ],
    );
  }

  /// 是否应该显示操作按钮（现在总是显示三个按钮）
  bool _shouldShowActionButtons() {
    return true; // 总是显示操作按钮
  }

  /// 获取状态颜色
  Color _getStatusColor() {
    switch (widget.order.status) {
      case OrderStatus.pending:
      case OrderStatus.waitingTrigger:
        return context.templateColors.warning;
      case OrderStatus.partialFilled:
        return context.templateColors.info;
      case OrderStatus.filled:
        return context.templateColors.success;
      case OrderStatus.cancelled:
      case OrderStatus.rejected:
      case OrderStatus.expired:
        return context.templateColors.error;
      case OrderStatus.triggered:
        return context.templateColors.info;
    }
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 获取基础货币符号
  String _getBaseCurrency() {
    final parts = widget.order.symbol.split('/');
    return parts.isNotEmpty ? parts[0] : '';
  }

  /// 计算交易额
  String _calculateTradeAmount() {
    final quantity = double.tryParse(widget.order.quantity) ?? 0;
    final price = double.tryParse(widget.order.price ?? '0') ?? 0;
    final amount = quantity * price;
    return amount.toStringAsFixed(2);
  }

  /// 获取止盈止损显示文本
  String _getStopProfitLossDisplay() {
    final takeProfit = widget.order.takeProfitPrice ?? '--';
    final stopLoss = widget.order.stopPrice ?? '--';
    return '$takeProfit/$stopLoss';
  }

  /// 是否应该显示状态标签
  bool _shouldShowStatusTag() {
    // 不显示这些状态的标签：已过期、已拒绝、待成交、已撤销、已成交
    switch (widget.order.status) {
      case OrderStatus.expired:
      case OrderStatus.rejected:
      case OrderStatus.pending:
      case OrderStatus.cancelled:
      case OrderStatus.filled:
        return false;
      default:
        return true;
    }
  }

  /// 处理修改订单
  void _handleModifyOrder() {
    // 先调用原有的回调（如果有的话）
    widget.onModify?.call();

    // 转换为 SpotOrderModel 并显示编辑对话框
    final spotOrder = _convertToSpotOrderModel();
    EditOrderDialog.showSpotOrder(context, orderData: spotOrder);
  }

  /// 处理设置止盈止损
  void _handleSetStopLoss() {
    // 先调用原有的回调（如果有的话）
    widget.onSetStopLoss?.call();

    // 转换为 SpotOrderModel 并显示止盈止损对话框
    final spotOrder = _convertToSpotOrderModel();
    EditOrderDialog.showStopProfitLoss(context, orderData: spotOrder);
  }

  /// 将 OrderModel 转换为 SpotOrderModel
  SpotOrderModel _convertToSpotOrderModel() {
    return SpotOrderModel(
      orderId: widget.order.orderId,
      symbol: widget.order.symbol,
      direction:
          widget.order.isBuyOrder ? TradeDirection.buy : TradeDirection.sell,
      orderType: _convertOrderType(widget.order.orderType),
      status: _convertOrderStatus(widget.order.status),
      price: widget.order.price ?? '0',
      quantity: widget.order.quantity,
      filledQuantity: widget.order.filledQuantity,
      avgPrice: widget.order.averagePrice ?? '0',
      amount: _calculateTradeAmount(),
      filledAmount: '0', // 这个字段在 OrderModel 中没有对应
      fee: widget.order.fee ?? '0',
      createTime: widget.order.createdAt,
      updateTime: widget.order.updatedAt,
      takeProfitPrice: widget.order.takeProfitPrice,
      stopLossPrice: widget.order.stopPrice,
      triggerPrice: widget.order.triggerPrice,
    );
  }

  /// 转换订单类型
  SpotOrderType _convertOrderType(OrderType orderType) {
    switch (orderType) {
      case OrderType.market:
        return SpotOrderType.market;
      case OrderType.limit:
        return SpotOrderType.limit;
      case OrderType.plan:
        return SpotOrderType.planOrder;
      default:
        return SpotOrderType.limit;
    }
  }

  /// 转换订单状态
  SpotOrderStatus _convertOrderStatus(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return SpotOrderStatus.pending;
      case OrderStatus.partialFilled:
        return SpotOrderStatus.partialFilled;
      case OrderStatus.filled:
        return SpotOrderStatus.filled;
      case OrderStatus.cancelled:
        return SpotOrderStatus.cancelled;
      case OrderStatus.rejected:
        return SpotOrderStatus.rejected;
      case OrderStatus.waitingTrigger:
        return SpotOrderStatus.waitingExecution;
      default:
        return SpotOrderStatus.pending;
    }
  }
}
