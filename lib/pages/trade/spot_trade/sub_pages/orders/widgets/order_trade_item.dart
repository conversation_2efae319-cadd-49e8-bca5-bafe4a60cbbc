/*
*  订单/成交明细项组件
*  
*  用于显示历史委托和成交明细的通用组件
*  支持两种显示模式：历史委托模式和成交明细模式
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/models/trade/order_model.dart';
import 'package:remixicon/remixicon.dart';

/// 订单/成交明细显示模式
enum OrderTradeDisplayMode {
  /// 历史委托模式
  order,
  /// 成交明细模式
  trade,
}

class OrderTradeItem extends StatelessWidget {
  /// 订单数据
  final OrderModel order;

  /// 显示模式
  final OrderTradeDisplayMode mode;

  /// 点击订单回调
  final VoidCallback? onTap;

  const OrderTradeItem({
    super.key,
    required this.order,
    this.mode = OrderTradeDisplayMode.order,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing18,
          vertical: UiConstants.spacing12,
        ),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              width: context.templateStyles.borderWidthThin,
              color: context.templateColors.border,
            ),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头部信息：交易对和时间
            _buildHeader(context),

            SizedBox(height: UiConstants.spacing8),

            // 标签行：买卖方向和订单类型
            _buildTags(context),

            SizedBox(height: UiConstants.spacing12),

            // 订单详情信息
            _buildOrderDetails(context),
          ],
        ),
      ),
    );
  }

  /// 构建头部信息
  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Text(
          order.symbol,
          style: context.templateStyle.text.bodyLargeMedium,
        ),
        Icon(
          RemixIcons.arrow_right_s_line,
          size: UiConstants.iconSize14,
          color: context.templateColors.textPrimary,
        ),
        Spacer(),
        Text(
          _formatDateTime(order.createdAt),
          style: context.templateStyle.text.descriptionSmall,
        ),
      ],
    );
  }

  /// 构建标签
  Widget _buildTags(BuildContext context) {
    return Row(
      children: [
        // 买卖方向标签
        TagWidget(
          text: order.side.displayName,
          textStyle: context.templateStyle.text.tagSmall.copyWith(
            color: order.isBuyOrder
                ? context.templateColors.tradeBuy
                : context.templateColors.tradeSell,
          ),
          margin: EdgeInsets.only(right: UiConstants.spacing6),
          borderWidth: context.templateStyles.borderWidthThin,
          borderColor: order.isBuyOrder
              ? context.templateColors.tradeBuy
              : context.templateColors.tradeSell,
          padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
        ),

        // 订单类型标签
        TagWidget(
          text: order.orderType.displayName,
          textStyle: context.templateStyle.text.tagSmall,
          margin: EdgeInsets.only(right: UiConstants.spacing6),
          borderWidth: context.templateStyles.borderWidthThin,
          borderColor: context.templateColors.textSecondary,
          padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
        ),
      ],
    );
  }

  /// 构建订单详情信息
  Widget _buildOrderDetails(BuildContext context) {
    if (mode == OrderTradeDisplayMode.order) {
      return _buildOrderModeDetails(context);
    } else {
      return _buildTradeModeDetails(context);
    }
  }

  /// 构建历史委托详情
  Widget _buildOrderModeDetails(BuildContext context) {
    return Column(
      children: [
        // 成交价格
        if (order.averagePrice != null)
          _buildInfoItem(
            context,
            label: '成交价格',
            value: '${order.averagePrice} USDT',
          ),

        // 成交数量
        _buildInfoItem(
          context,
          label: '成交数量',
          value: '${order.filledQuantity} ${_getBaseCurrency()}',
        ),

        // 成交总额
        _buildInfoItem(
          context,
          label: '成交总额',
          value: '${_calculateFilledAmount()} USDT',
        ),

        // 流动性方向
        _buildInfoItem(
          context,
          label: '流动性方向',
          value: '吃单', // 这里可以根据实际业务逻辑调整
        ),

        // 手续费
        if (order.fee != null)
          _buildInfoItem(
            context,
            label: '手续费',
            value: '${order.fee} ${order.feeCurrency ?? _getBaseCurrency()}',
          ),

        // 成交时间
        _buildInfoItem(
          context,
          label: '成交时间',
          value: _formatDateTime(order.updatedAt ?? order.createdAt),
        ),
      ],
    );
  }

  /// 构建成交明细详情
  Widget _buildTradeModeDetails(BuildContext context) {
    return Column(
      children: [
        // 市价
        if (order.averagePrice != null)
          _buildInfoItem(
            context,
            label: '市价',
            value: '${order.averagePrice} USDT',
          ),

        // 委托数量
        _buildInfoItem(
          context,
          label: '委托数量',
          value: '${order.quantity} ${_getBaseCurrency()}',
        ),

        // 成交均价
        if (order.averagePrice != null)
          _buildInfoItem(
            context,
            label: '成交均价',
            value: '${order.averagePrice} USDT',
          ),

        // 成交数量
        _buildInfoItem(
          context,
          label: '成交数量',
          value: '${order.filledQuantity} ${_getBaseCurrency()}',
        ),

        // 成交总额
        _buildInfoItem(
          context,
          label: '成交总额',
          value: '${_calculateFilledAmount()} USDT',
        ),

        // 手续费
        if (order.fee != null)
          _buildInfoItem(
            context,
            label: '手续费',
            value: '${order.fee} ${order.feeCurrency ?? _getBaseCurrency()}',
          ),

        // 状态
        _buildInfoItem(
          context,
          label: '状态',
          value: order.status.displayName,
        ),
      ],
    );
  }

  /// 构建信息项
  Widget _buildInfoItem(
    BuildContext context, {
    required String label,
    required String value,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: context.templateStyle.text.descriptionSmall),
          Spacer(),
          Text(value, style: context.templateStyle.text.bodySmallMedium),
        ],
      ),
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}:${date.second.toString().padLeft(2, '0')}';
  }

  /// 获取基础货币符号
  String _getBaseCurrency() {
    final parts = order.symbol.split('/');
    return parts.isNotEmpty ? parts[0] : '';
  }

  /// 计算成交金额
  String _calculateFilledAmount() {
    final filledQuantity = double.tryParse(order.filledQuantity) ?? 0;
    final averagePrice = double.tryParse(order.averagePrice ?? '0') ?? 0;
    final amount = filledQuantity * averagePrice;
    return amount.toStringAsFixed(3);
  }
}
