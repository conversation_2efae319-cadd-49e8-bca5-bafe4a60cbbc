/*
*  主标签内容
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/models/trade/order_model.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'order_item.dart';
import 'order_trade_item.dart';

import '../../../../widgets/index.dart';

/// 订单列表类型枚举
enum OrderListType {
  /// 当前委托
  current,

  /// 历史委托
  history,

  /// 成交明细
  trade,
}

class MainTabViewSection extends StatefulWidget {
  /// 订单列表类型
  final OrderListType listType;

  /// 订单数据
  final List<OrderModel> orders;

  /// 刷新回调
  final Future<void> Function()? onRefresh;

  /// 修改订单回调（仅当前委托）
  final void Function(OrderModel order)? onModifyOrder;

  /// 设置止盈止损回调（仅当前委托）
  final void Function(OrderModel order)? onSetStopLoss;

  /// 撤销订单回调（仅当前委托）
  final void Function(OrderModel order)? onCancelOrder;

  /// 查看订单详情回调
  final void Function(OrderModel order)? onOrderTap;

  const MainTabViewSection({
    super.key,
    required this.listType,
    required this.orders,
    this.onRefresh,
    this.onModifyOrder,
    this.onSetStopLoss,
    this.onCancelOrder,
    this.onOrderTap,
  });

  @override
  State<MainTabViewSection> createState() => _MainTabViewSectionState();
}

class _MainTabViewSectionState extends State<MainTabViewSection> {
  // 筛选状态
  String _selectedSymbol = '全部币对';
  String _selectedOrderType = '全部委托';

  // 下拉刷新
  Future<void> _onRefresh() async {
    if (widget.onRefresh != null) {
      await widget.onRefresh!();
    } else {
      // 模拟刷新操作
      await Future.delayed(const Duration(seconds: 2));
    }
  }

  @override
  Widget build(BuildContext context) {
    return EasyRefresh.builder(
      header: ClassicHeader(
        clamping: true,
        position: IndicatorPosition.locator,
        triggerOffset: 34,
        processedDuration: const Duration(seconds: 1),
        safeArea: false,
        showMessage: false,
        showText: false,
        maxOverOffset: 40,
      ),
      onRefresh: _onRefresh,
      childBuilder: (context, physics) {
        return ScrollConfiguration(
          behavior: const ERScrollBehavior(),
          child: ExtendedNestedScrollView(
            physics: physics,
            onlyOneScrollInBody: true,
            pinnedHeaderSliverHeightBuilder: () {
              return _getFilterBarHeight();
            },
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return <Widget>[
                // 刷新指示器定位器
                const HeaderLocator.sliver(clearExtent: false),
                // 吸顶 TabBar
                SliverPersistentHeader(
                  pinned: true,
                  delegate: StickyDelegate(
                    maxHeight: _getFilterBarHeight(),
                    child: _buildFilterBar(),
                    backgroundColor: context.templateColors.surface,
                  ),
                ),
              ];
            },
            body: _buildOrderList(physics),
          ),
        );
      },
    );
  }

  /// 根据列表类型构建对应的订单列表
  Widget _buildOrderList(ScrollPhysics physics) {
    switch (widget.listType) {
      case OrderListType.current:
        return _buildCurrentOrdersList(physics, widget.orders);
      case OrderListType.history:
        return _buildHistoryOrdersList(physics, widget.orders);
      case OrderListType.trade:
        return _buildTradeHistoryList(physics, widget.orders);
    }
  }

  /// 获取筛选栏高度
  double _getFilterBarHeight() {
    // 当前委托不显示时间，高度较小
    if (widget.listType == OrderListType.current) {
      return 40.0;
    }
    // 历史委托和成交明细显示时间
    return 58.0;
  }

  /// 是否显示筛选功能
  bool _shouldShowFilter() {
    return widget.listType != OrderListType.current;
  }

  /// 是否显示时间范围
  bool _shouldShowTimeRange() {
    return widget.listType != OrderListType.current;
  }

  // 构建筛选栏
  Widget _buildFilterBar() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing10,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              InkWellWidget(
                onTap: _showSymbolSelector,
                child: Row(
                  children: [
                    Text(
                      _selectedSymbol,
                      style: context.templateStyle.text.hintText,
                    ),
                    ThemedImage.asset(
                      'icon_arrow_down_new_gray',
                      size: UiConstants.iconSize10,
                      margin: EdgeInsets.only(left: UiConstants.spacing6),
                    ),
                  ],
                ),
              ),
              SizedBox(width: UiConstants.spacing10),
              InkWellWidget(
                onTap: _showOrderTypeSelector,
                child: Row(
                  children: [
                    Text(
                      _selectedOrderType,
                      style: context.templateStyle.text.hintText,
                    ),
                    ThemedImage.asset(
                      'icon_arrow_down_new_gray',
                      size: UiConstants.iconSize10,
                      margin: EdgeInsets.only(left: UiConstants.spacing6),
                    ),
                  ],
                ),
              ),
              Spacer(),
              // 只有历史委托和成交明细显示filter图标
              if (_shouldShowFilter())
                InkWellWidget(
                  onTap: _showTimeRangePicker,
                  child: ThemedImage.asset(
                    'icon_filter_gray',
                    size: UiConstants.iconSize20,
                    followTheme: true,
                    format: ImageFormat.webp,
                  ),
                ),
            ],
          ),
          // 只有历史委托和成交明细显示时间范围
          if (_shouldShowTimeRange()) ...[
            SizedBox(height: UiConstants.spacing4),
            Text(
              '2025-04-25 到 2025-07-25',
              style: context.templateStyle.text.hintSmall,
            ),
          ],
        ],
      ),
    );
  }

  /// 构建当前委托列表
  Widget _buildCurrentOrdersList(
    ScrollPhysics physics,
    List<OrderModel> orders,
  ) {
    if (orders.isEmpty) {
      return _buildEmptyState('暂无当前委托');
    }

    return ListView.builder(
      physics: physics,
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return OrderItem(
          order: order,
          onModify:
              widget.onModifyOrder != null
                  ? () => widget.onModifyOrder!(order)
                  : null,
          onSetStopLoss:
              widget.onSetStopLoss != null
                  ? () => widget.onSetStopLoss!(order)
                  : null,
          onCancel:
              widget.onCancelOrder != null
                  ? () => widget.onCancelOrder!(order)
                  : null,
          onTap:
              widget.onOrderTap != null
                  ? () => widget.onOrderTap!(order)
                  : null,
        );
      },
    );
  }

  /// 构建历史委托列表
  Widget _buildHistoryOrdersList(
    ScrollPhysics physics,
    List<OrderModel> orders,
  ) {
    if (orders.isEmpty) {
      return _buildEmptyState('暂无历史委托');
    }

    return ListView.builder(
      physics: physics,
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return OrderTradeItem(
          order: order,
          mode: OrderTradeDisplayMode.order,
          onTap:
              widget.onOrderTap != null
                  ? () => widget.onOrderTap!(order)
                  : null,
        );
      },
    );
  }

  /// 构建成交明细列表
  Widget _buildTradeHistoryList(
    ScrollPhysics physics,
    List<OrderModel> orders,
  ) {
    if (orders.isEmpty) {
      return _buildEmptyState('暂无成交明细');
    }

    return ListView.builder(
      physics: physics,
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return OrderTradeItem(
          order: order,
          mode: OrderTradeDisplayMode.trade,
          onTap:
              widget.onOrderTap != null
                  ? () => widget.onOrderTap!(order)
                  : null,
        );
      },
    );
  }

  /// 构建空状态
  Widget _buildEmptyState(String message) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(UiConstants.spacing32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ThemedImage.asset(
              'empty_state',
              size: UiConstants.iconSize64,
              followTheme: true,
            ),
            SizedBox(height: UiConstants.spacing16),
            Text(
              message,
              style: context.templateStyle.text.descriptionText,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 显示币对选择器
  void _showSymbolSelector() async {
    final result = await SymbolSelectorBottomSheet.show(
      context,
      selectedSymbol: _selectedSymbol,
      onSelected: (symbol) {
        setState(() {
          _selectedSymbol = symbol;
        });
      },
    );

    if (result != null) {
      setState(() {
        _selectedSymbol = result;
      });
    }
  }

  /// 显示订单类型选择器
  void _showOrderTypeSelector() async {
    final orderTypes = ['全部委托', '计划委托', 'OCO', '限价｜市价', '止盈止损', '追踪委托'];

    final result = await OrderTypeSelectorBottomSheet.show(
      context,
      orderTypes: orderTypes,
      selectedType: _selectedOrderType,
      onSelected: (type) {
        setState(() {
          _selectedOrderType = type;
        });
      },
    );

    if (result != null) {
      setState(() {
        _selectedOrderType = result;
      });
    }
  }

  /// 显示时间范围选择器
  void _showTimeRangePicker() async {
    final result = await TimePickerBottomSheet.show(
      context,
      // 不传入初始日期，使用默认逻辑：快捷选项选择第一项（7天），开始时间为7天前，结束时间为当天
    );

    if (result != null) {
      // 处理时间范围选择结果
      debugPrint('选择的时间范围: ${result.startDate} 到 ${result.endDate}');
      debugPrint('隐藏失败订单: ${result.hideFailedOrders}');

      // 这里可以添加实际的筛选逻辑
      // 例如：根据选择的时间范围重新加载订单数据
    }
  }
}
