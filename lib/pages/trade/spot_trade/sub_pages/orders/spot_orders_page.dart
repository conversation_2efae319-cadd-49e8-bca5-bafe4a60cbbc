/*
*  现货订单界面
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/models/trade/order_model_example.dart';
import 'package:qubic_exchange/models/trade/order_model.dart';
import './widgets/main_tab_view_section.dart';

class SpotOrdersPage extends StatefulWidget {
  const SpotOrdersPage({super.key});

  @override
  State<SpotOrdersPage> createState() => _SpotOrdersPageState();
}

class _SpotOrdersPageState extends State<SpotOrdersPage>
    with TickerProviderStateMixin {
  // 吸顶高度
  final double stickyHeaderHeight = 120.0;

  // 下拉距离
  final double refreshOffset = 44.0;

  // 标签控制器
  late TabController _mainController;

  // 标签项
  static const List<TabItem> _mainKeys = [
    TabItem(title: '当前委托'),
    TabItem(title: '历史委托'),
    TabItem(title: '成交明细'),
  ];

  // 初始化
  @override
  void initState() {
    super.initState();
    _mainController = TabController(length: _mainKeys.length, vsync: this);
  }

  // 销毁
  @override
  void dispose() {
    _mainController.dispose();
    super.dispose();
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    // 模拟刷新操作
    await Future.delayed(const Duration(seconds: 2));
    // 这里可以添加实际的数据刷新逻辑
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(
        showBackButton: true,
        title: '现货订单',
        actions: [
          ThemedImage.asset(
            'icon_name',
            size: UiConstants.iconSize24,
            followTheme: true,
            margin: EdgeInsets.only(right: UiConstants.spacing10),
          ),
        ],
      ),
      backgroundColor: context.templateColors.surface,
      body: SafeArea(
        child: Column(
          children: [
            // 主标签栏
            _buildMainTabbar(),

            // 主标签内容
            Expanded(child: _buildMainTabView()),
          ],
        ),
      ),
    );
  }

  // 构建主标签栏
  Widget _buildMainTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      decoration: BoxDecoration(
        color: context.templateColors.surface,
        border: Border(
          bottom: BorderSide(
            width: context.templateStyles.borderWidthThin,
            color: context.templateColors.border,
          ),
        ),
      ),
      child: TabbarWidget(
        controller: _mainController,
        tabs: _mainKeys,
        height: 44,
        labelStyle: context.templateStyle.text.tabText,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: true,
        indicatorSize: TabBarIndicatorSize.label,
        labelPadding: EdgeInsets.only(right: UiConstants.spacing18),
      ),
    );
  }

  // 构建主标签内容
  Widget _buildMainTabView() {
    final sampleOrders = OrderModelExample.createSampleOrders();

    // 过滤当前委托：排除已撤销和已成交的订单
    final currentOrders =
        sampleOrders
            .where(
              (order) =>
                  order.status != OrderStatus.cancelled &&
                  order.status != OrderStatus.filled,
            )
            .toList();

    // 过滤历史委托：只显示已撤销和已成交的订单
    final historyOrders =
        sampleOrders
            .where(
              (order) =>
                  order.status == OrderStatus.cancelled ||
                  order.status == OrderStatus.filled,
            )
            .toList();

    // 成交明细：只显示已成交的订单
    final tradeHistory =
        sampleOrders
            .where((order) => order.status == OrderStatus.filled)
            .toList();

    return TabBarView(
      controller: _mainController,
      children: [
        // 当前委托
        MainTabViewSection(
          listType: OrderListType.current,
          orders: currentOrders,
          onRefresh: _onRefresh,
          onModifyOrder: (order) {
            // TODO: 实现修改订单功能
            debugPrint('修改订单: ${order.orderId}');
          },
          onSetStopLoss: (order) {
            // TODO: 实现设置止盈止损功能
            debugPrint('设置止盈止损: ${order.orderId}');
          },
          onCancelOrder: (order) {
            // TODO: 实现撤销订单功能
            debugPrint('撤销订单: ${order.orderId}');
          },
          onOrderTap: (order) {
            // TODO: 实现查看订单详情功能
            debugPrint('查看订单详情: ${order.orderId}');
          },
        ),
        // 历史委托
        MainTabViewSection(
          listType: OrderListType.history,
          orders: historyOrders,
          onRefresh: _onRefresh,
          onOrderTap: (order) {
            // TODO: 实现查看历史委托详情功能
            debugPrint('查看历史委托详情: ${order.orderId}');
          },
        ),
        // 成交明细
        MainTabViewSection(
          listType: OrderListType.trade,
          orders: tradeHistory,
          onRefresh: _onRefresh,
          onOrderTap: (order) {
            // TODO: 实现查看成交明细详情功能
            debugPrint('查看成交明细详情: ${order.orderId}');
          },
        ),
      ],
    );
  }
}
