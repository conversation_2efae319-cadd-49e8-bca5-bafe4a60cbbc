/*
*  跟单交易主入口
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class CopyOrderTrade extends StatefulWidget {
  const CopyOrderTrade({super.key});

  @override
  State<CopyOrderTrade> createState() => _CopyOrderTradeState();
}

class _CopyOrderTradeState extends State<CopyOrderTrade>
    with TickerProviderStateMixin {
  // 构建选项卡控制器
  late TabController _tabController;

  // 构建选项卡项
  static const List<String> _tabKeys = ['跟单交易'];

  // 初始化
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabKeys.length, vsync: this);
  }

  // 销毁
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.templateColors.surface,
      body: SafeArea(
        child: Column(
          children: [
            // 使用 AnimatedSize 实现平滑的高度过渡
            AnimatedSize(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: _buildTabbar(),
            ),
            // 使用 AnimatedContainer 让内容区域也有平滑过渡
            Expanded(
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                child: _buildTabView(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建顶部选项卡
  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(width: 1, color: context.templateColors.divider),
        ),
      ),
      child: TabbarWidget(
        controller: _tabController,
        tabs: _tabKeys.map((key) => TabItem(title: key)).toList(),
        labelStyle: context.templateStyle.text.bodyLargeMedium.copyWith(
          fontSize: 18,
        ),
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: false,
        labelPadding: EdgeInsets.only(right: UiConstants.spacing14),
        dividerColor: Colors.transparent,
      ),
    );
  }

  // 构建选项卡内容
  Widget _buildTabView() {
    return TabBarView(
      controller: _tabController,
      children: [
        // 跟单交易内容
        Container(
          color: context.templateColors.background,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('跟单交易', style: context.templateStyle.text.h3),
                SizedBox(height: UiConstants.spacing16),
                Text('跟单交易功能即将上线', style: context.templateStyle.text.bodyText),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
