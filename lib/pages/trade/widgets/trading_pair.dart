/*
*  交易对组件
*/

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/routes/index.dart';
import 'package:qubic_exchange/services/storage/storage_service.dart';
import 'package:qubic_exchange/services/market/market_service.dart';
import 'package:qubic_exchange/services/market/models/currency_model.dart';

class TradingPair extends StatefulWidget {
  final Function(int currencyId)? onCurrencySelected;

  const TradingPair({
    super.key,
    this.onCurrencySelected,
  });

  @override
  State<TradingPair> createState() => _TradingPairState();
}

class _TradingPairState extends State<TradingPair> {

  /// 当前选择的币种信息
  CurrencyModel? _currentCurrency;

  /// 当前币种的ticker数据
  TickerModel? _currentTicker;

  /// ticker数据更新定时器
  Timer? _tickerTimer;

  @override
  void initState() {
    super.initState();
    _loadCurrentCurrency();
    _startTickerTimer();
  }

  @override
  void dispose() {
    _tickerTimer?.cancel();
    super.dispose();
  }

  /// 加载当前币种信息
  Future<void> _loadCurrentCurrency() async {
    try {
      final storage = StorageService.instance;
      final marketService = MarketService.instance;

      // 从缓存获取币种ID
      int? currencyId = storage.getInt('selected_spot_currency_id');

      // 如果缓存没有数据，使用第一个币种
      if (currencyId == null) {
        final currencies = marketService.currencyModels;
        if (currencies.isNotEmpty) {
          currencyId = currencies.first.id;
          // 保存默认币种到缓存
          await storage.setInt('selected_spot_currency_id', currencyId);
        }
      }

      if (currencyId != null) {
        // 根据ID获取币种信息
        final currency = marketService.currencyModels.firstWhere(
          (c) => c.id == currencyId,
          orElse: () => marketService.currencyModels.first,
        );

        // 获取ticker数据
        final ticker = currency.tickers['1']; // 现货市场类型为1

        setState(() {
          _currentCurrency = currency;
          _currentTicker = ticker;
        });
      }
    } catch (e) {
      debugPrint('加载币种信息失败: $e');
    }
  }

  /// 显示币种选择器
  Future<void> _showCurrencySelector() async {
    await CryptoListBottomSheet.show(
      context,
      marketType: 1,
      field: 'is_spotTrade',
      onItemSelected: (item) {
        // 关闭弹窗并传递币种ID
        Navigator.of(context).pop();
        if (item.currencyId != null) {
          widget.onCurrencySelected?.call(item.currencyId!);
          // 更新当前币种信息
          _updateCurrentCurrency(item.currencyId!);
        }
      },
    );
  }

  /// 更新当前币种信息
  void _updateCurrentCurrency(int currencyId) {
    try {
      final marketService = MarketService.instance;
      final currency = marketService.currencyModels.firstWhere(
        (c) => c.id == currencyId,
        orElse: () => marketService.currencyModels.first,
      );

      final ticker = currency.tickers['1']; // 现货市场类型为1

      setState(() {
        _currentCurrency = currency;
        _currentTicker = ticker;
      });
    } catch (e) {
      debugPrint('更新币种信息失败: $e');
    }
  }

  /// 启动ticker数据更新定时器
  void _startTickerTimer() {
    _tickerTimer?.cancel();
    _tickerTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTickerData();
    });
  }

  /// 更新ticker数据
  void _updateTickerData() {
    if (_currentCurrency == null) return;

    try {
      final marketService = MarketService.instance;
      final currency = marketService.currencyModels.firstWhere(
        (c) => c.id == _currentCurrency!.id,
        orElse: () => _currentCurrency!,
      );

      final ticker = currency.tickers['1']; // 现货市场类型为1
      if (ticker != null && mounted) {
        setState(() {
          _currentTicker = ticker;
        });
      }
    } catch (e) {
      debugPrint('更新ticker数据失败: $e');
    }
  }

  /// 获取当前交易对符号
  String _getCurrentSymbol() {
    if (_currentCurrency != null) {
      return '${_currentCurrency!.baseAsset}${_currentCurrency!.quoteAsset}';
    }
    return 'BTCUSDT'; // 默认值
  }

  /// 获取当前涨跌幅
  String _getCurrentChangePercent() {
    if (_currentTicker != null) {
      final changePercent = _currentTicker!.priceChangeP;
      final sign = changePercent >= 0 ? '+' : '';
      return '$sign${changePercent.toStringAsFixed(2)}%';
    }
    return '+0.00%'; // 默认值
  }

  /// 获取涨跌幅颜色
  Color _getChangePercentColor(BuildContext context) {
    if (_currentTicker != null) {
      final changePercent = _currentTicker!.priceChangeP;
      if (changePercent > 0) {
        return context.templateColors.tradeBuy; // 绿色
      } else if (changePercent < 0) {
        return context.templateColors.tradeSell; // 红色
      }
    }
    return context.templateColors.textSecondary; // 灰色
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing10,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 交易对信息
          _buildPair(),

          // 操作按钮
          _buildActions(),
        ],
      ),
    );
  }

  // 构建交易对信息
  Widget _buildPair() {
    return InkWellWidget(
      onTap: () => _showCurrencySelector(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// 交易对名称
          Row(
            children: [
              Text(
                _getCurrentSymbol(),
                style: context.templateStyle.text.h3,
              ),
              ThemedImage.asset(
                'arrow_triangle_down',
                size: UiConstants.iconSize10,
                followTheme: true,
                margin: EdgeInsets.only(left: UiConstants.spacing8),
              ),
            ],
          ),

          /// 标签 & 涨跌幅
          Row(
            children: [
              Text(
                _getCurrentChangePercent(),
                style: context.templateStyle.text.bodySmall.copyWith(
                  color: _getChangePercentColor(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建操作按钮
  Widget _buildActions() {
    /// 构建操作按钮单项
    Widget buildToggleButton({
      required String imageName,
      required VoidCallback onTap,
    }) {
      return Padding(
        padding: EdgeInsets.only(left: UiConstants.spacing16),
        child: InkWellWidget(
          onTap: onTap,
          child: ThemedImage(
            name: imageName,
            size: UiConstants.iconSize24,
            followTheme: true,
          ),
        ),
      );
    }

    return Row(
      children: [
        /// K 线图
        buildToggleButton(
          imageName: 'strategy_kline',
          onTap:
              () => {
                // TODO 跳转至k线图界面
                NavigationService().navigateTo(AppRoutes.klinePage),
              },
        ),

        /// 更多操作
        buildToggleButton(
          imageName: 'more',
          onTap: () {
            FunctionBottomSheet.show(context);
          },
        ),
      ],
    );
  }
}
