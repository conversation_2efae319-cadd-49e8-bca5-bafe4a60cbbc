/*
*  订单簿深度底部弹窗
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class OrderbookDepthBottomSheet {
  // 显示订单簿深度底部弹窗
  static Future<void> show(
    BuildContext context, {
    required List<String> depths,
    String? selectedDepth,
    Function(String)? onSelected,
    bool showHeader = true,
    bool showCloseButton = false,
    bool showDragHandle = true,
    bool showHeaderBorder = false,
    double? maxHeight,
    double? headerHeight,
    bool useSafeArea = false,
  }) async {
    return await BottomSheetWidget.show<void>(
      context: context,
      title: '选择订单簿深度',
      titleAlign: TextAlign.start,
      showCloseButton: showCloseButton,
      showHeader: showHeader,
      showDragHandle: showDragHandle,
      headerHeight: headerHeight ?? 58,
      showHeaderBorder: showHeaderBorder,
      showBottomButton: false,
      maxHeight: maxHeight ?? MediaQuery.of(context).size.height * 0.8,
      useSafeArea: useSafeArea,
      child: _OrderbookDepthSelectionContent(
        depths: depths,
        selectedDepth: selectedDepth,
        onSelected: onSelected,
      ),
    );
  }
}

class _OrderbookDepthSelectionContent extends StatefulWidget {
  final List<String> depths;
  final String? selectedDepth;
  final Function(String)? onSelected;

  const _OrderbookDepthSelectionContent({
    required this.depths,
    this.selectedDepth,
    this.onSelected,
  });

  @override
  State<_OrderbookDepthSelectionContent> createState() =>
      _OrderbookDepthSelectionContentState();
}

class _OrderbookDepthSelectionContentState
    extends State<_OrderbookDepthSelectionContent> {
  String? _selectedDepth;

  @override
  void initState() {
    super.initState();
    _selectedDepth = widget.selectedDepth;
  }

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: widget.depths.length,
      separatorBuilder:
          (context, index) =>
              Divider(height: 1, color: context.templateColors.popupBackground),
      itemBuilder: (context, index) {
        final depth = widget.depths[index];
        final isSelected = _selectedDepth == depth;
        return _buildSelectItem(
          depth: depth,
          isSelected: isSelected,
          onTap: () => _handleOptionSelected(depth),
        );
      },
    );
  }

  // 处理选项选择
  void _handleOptionSelected(String depth) {
    setState(() {
      _selectedDepth = depth;
    });

    // 调用回调函数
    widget.onSelected?.call(depth);

    // 关闭弹窗并返回选中的深度
    Navigator.of(context).pop(depth);
  }

  // 构建选择项
  Widget _buildSelectItem({
    required String depth,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWellWidget(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: UiConstants.spacing14),
        child: Row(
          children: [
            Expanded(
              child: Text(
                depth,
                style: context.templateStyle.text.bodyLargeMedium,
              ),
            ),
            if (isSelected)
              Icon(
                RemixIcons.check_line,
                size: UiConstants.iconSize20,
                color: context.templateColors.textPrimary,
              ),
          ],
        ),
      ),
    );
  }
}
