/*
*  订单修改底部弹窗
*/

import 'package:flutter/material.dart';
import 'package:toastification/toastification.dart' hide Toastification;
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/utils/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/models/trade/spot_order_model.dart';

/// 修改订单对话框配置常量
class _ModifyOrderDialogConfig {
  static const double keyboardBottomPadding = 0.75;
  static const double inputFieldHeight = 48.0;
  static const double cursorHeight = 12.0;
  static const double suffixIconMaxWidth = 80.0;

  // 按钮配置
  static const double defaultButtonPadding = 14.0;
  static const double wideButtonPadding = 28.0;
}

/// 修改订单内容组件（供外部弹窗调用）
class ModifyOrderContent extends StatefulWidget {
  final SpotOrderModel orderData;

  const ModifyOrderContent({super.key, required this.orderData});

  @override
  State<ModifyOrderContent> createState() => _ModifyOrderContentState();
}

class _ModifyOrderContentState extends State<ModifyOrderContent> {
  // 表单控制器
  late TextEditingController _triggerPriceController;
  late TextEditingController _orderPriceController;
  late TextEditingController _quantityController;
  late TextEditingController _takeProfitController;
  late TextEditingController _stopLossController;

  // 表单键
  final _formKey = GlobalKey<FormState>();

  // 价格类型状态（true: 市价, false: 限价）
  bool _isMarketPrice = false;

  // 交易额货币类型状态（true: 计价货币, false: 基础货币）
  bool _isQuoteCurrency = true;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  @override
  void dispose() {
    _triggerPriceController.dispose();
    _orderPriceController.dispose();
    _quantityController.dispose();
    _takeProfitController.dispose();
    _stopLossController.dispose();
    super.dispose();
  }

  // 初始化控制器
  void _initializeControllers() {
    _triggerPriceController = TextEditingController(
      text: _extractNumericValue(widget.orderData.triggerPrice),
    );
    _orderPriceController = TextEditingController(
      text: _extractNumericValue(widget.orderData.price),
    );
    _quantityController = TextEditingController(
      text: _extractNumericValue(widget.orderData.quantity),
    );
    _takeProfitController = TextEditingController(
      text: _extractNumericValue(widget.orderData.takeProfitPrice),
    );
    _stopLossController = TextEditingController(
      text: _extractNumericValue(widget.orderData.stopLossPrice),
    );
  }

  // 提取纯数字值（去除≥、≤等符号）
  String _extractNumericValue(String? value) {
    if (value == null || value.isEmpty) return '';

    // 移除≥、≤、>、<等符号，只保留数字、小数点和逗号
    String cleanValue = value.replaceAll(RegExp(r'[≥≤><]'), '').trim();

    return cleanValue;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom:
            MediaQuery.of(context).viewInsets.bottom *
            _ModifyOrderDialogConfig.keyboardBottomPadding,
      ),
      child: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildOrderInfo(),
              _buildPriceInfo(),
              _buildModifyForm(),
              _buildHintText(),
              _buildBottomButtons(),
            ],
          ),
        ),
      ),
    );
  }

  // 构建交易对、交易类型
  Widget _buildOrderInfo() {
    final orderData = widget.orderData;

    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing10),
      child: Row(
        children: [
          Text(orderData.symbol, style: context.templateStyle.text.h4),
          _buildDirectionTag(orderData.direction),
          _buildOrderTypeTag(orderData.orderType),
        ],
      ),
    );
  }

  // 构建交易方向标签
  Widget _buildDirectionTag(TradeDirection direction) {
    return TagWidget(
      text: direction.displayName,
      backgroundColor:
          direction.isBuy
              ? context.templateColors.tradeBuy.withValues(alpha: 0.2)
              : context.templateColors.tradeSell.withValues(alpha: 0.2),
      textStyle: context.templateStyle.text.tagText.copyWith(
        color:
            direction.isBuy
                ? context.templateColors.tradeBuy
                : context.templateColors.tradeSell,
      ),
      margin: EdgeInsets.only(left: UiConstants.spacing6),
    );
  }

  // 构建订单类型标签
  Widget _buildOrderTypeTag(SpotOrderType orderType) {
    return TagWidget(
      text: orderType.displayName,
      backgroundColor: context.templateColors.tabbarBackground,
      textStyle: context.templateStyle.text.tagText.copyWith(
        color: context.templateColors.textTertiary,
      ),
      margin: EdgeInsets.only(left: UiConstants.spacing6),
    );
  }

  // 构建价格信息
  Widget _buildPriceInfo() {
    final infoItems = _generatePriceInfoItems();

    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing6),
      child: Column(children: infoItems.map(_buildInfoItem).toList()),
    );
  }

  // 生成价格信息项列表
  List<({String label, String value})> _generatePriceInfoItems() {
    final orderData = widget.orderData;
    final quoteCurrency = _extractQuoteCurrency();

    final List<({String label, String value})> items = [];

    // 触发价格（如果存在）
    if (orderData.triggerPrice != null) {
      items.add((
        label: '触发价格',
        value: '${orderData.triggerPrice} $quoteCurrency',
      ));
    }

    // 基础信息
    items.addAll([
      (label: '委托价格', value: '${orderData.price} $quoteCurrency'),
      (label: '委托数量', value: orderData.quantity),
      (label: '委托金额', value: '${orderData.amount} $quoteCurrency'),
    ]);

    // 成交信息（如果有）
    if (orderData.filledQuantity != '0') {
      items.add((label: '已成交数量', value: orderData.filledQuantity));
    }

    if (orderData.avgPrice != '0.00') {
      items.add((label: '成交均价', value: '${orderData.avgPrice} $quoteCurrency'));
    }

    return items;
  }

  // 构建单个信息项
  Widget _buildInfoItem(({String label, String value}) item) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(item.label, style: context.templateStyle.text.bodySmall),
          Text(item.value, style: context.templateStyle.text.bodySmall),
        ],
      ),
    );
  }

  // 提取计价货币
  String _extractQuoteCurrency() {
    return widget.orderData.symbol.split('/').last;
  }

  // 提取基础货币
  String _extractBaseCurrency() {
    return widget.orderData.symbol.split('/').first;
  }

  // 获取当前选中的货币
  String _getCurrentCurrency() {
    return _isQuoteCurrency ? _extractQuoteCurrency() : _extractBaseCurrency();
  }

  // 切换货币类型
  void _toggleCurrencyType() {
    setState(() {
      _isQuoteCurrency = !_isQuoteCurrency;
      // 切换货币时，可能需要重新计算交易额
      _recalculateTradeAmount();
    });
  }

  // 重新计算交易额（根据货币类型切换）
  void _recalculateTradeAmount() {
    // TODO: 根据当前价格和货币类型重新计算交易额
    // 这里可以添加具体的计算逻辑
    debugPrint('货币类型切换: ${_isQuoteCurrency ? "计价货币" : "基础货币"}');
  }

  // 构建修改输入表单
  Widget _buildModifyForm() {
    final formFields = _generateFormFields();
    return Column(children: formFields);
  }

  // 生成表单字段列表
  List<Widget> _generateFormFields() {
    final orderData = widget.orderData;
    final List<Widget> fields = [];

    // 根据订单类型添加特定字段
    fields.addAll(_buildOrderTypeSpecificFields(orderData.orderType));

    // 交易额字段（所有订单类型都需要）
    fields.add(_buildTradeAmountField());

    // 止盈止损额外字段
    fields.addAll(_buildProfitLossFields());

    return fields;
  }

  // 构建订单类型特定字段
  List<Widget> _buildOrderTypeSpecificFields(SpotOrderType orderType) {
    switch (orderType) {
      case SpotOrderType.planOrder:
        return [
          _buildInputField(
            controller: _triggerPriceController,
            labelText: '触发价',
          ),
          _buildPlanOrderPriceField(),
        ];

      case SpotOrderType.stopProfitLoss:
        return [
          _buildInputField(
            controller: _triggerPriceController,
            labelText: '触发价',
          ),
          _buildStopProfitLossPriceField(),
        ];

      case SpotOrderType.oco:
        return [
          _buildInputField(
            controller: _triggerPriceController,
            labelText: '触发价',
          ),
          _buildInputField(controller: _orderPriceController, labelText: '委托价'),
        ];

      case SpotOrderType.market:
        return []; // 市价单不需要委托价

      default:
        return [
          _buildInputField(controller: _orderPriceController, labelText: '委托价'),
        ];
    }
  }

  // 构建止盈止损额外字段
  List<Widget> _buildProfitLossFields() {
    final orderData = widget.orderData;
    final List<Widget> fields = [];

    final shouldShowProfitLoss =
        orderData.orderType == SpotOrderType.stopProfitLoss ||
        orderData.takeProfitPrice != null ||
        orderData.stopLossPrice != null;

    if (!shouldShowProfitLoss) return fields;

    if (orderData.takeProfitPrice != null) {
      fields.add(
        _buildInputField(controller: _takeProfitController, labelText: '止盈价格'),
      );
    }

    if (orderData.stopLossPrice != null) {
      fields.add(
        _buildInputField(controller: _stopLossController, labelText: '止损价格'),
      );
    }

    return fields;
  }

  // 构建输入框的通用方法
  Widget _buildInputField({
    required TextEditingController controller,
    required String labelText,
    String? hintText,
  }) {
    return TextFieldWidget(
      controller: controller,
      labelText: labelText,
      hintText: hintText,
      height: _ModifyOrderDialogConfig.inputFieldHeight,
      cursorHeight: _ModifyOrderDialogConfig.cursorHeight,
      contentPadding: EdgeInsets.only(
        left: UiConstants.spacing10,
        top: UiConstants.spacing8,
        bottom: UiConstants.spacing8,
      ),
      padding: EdgeInsets.zero,
      margin: EdgeInsets.only(bottom: UiConstants.spacing8),
      radius: BorderRadius.circular(UiConstants.borderRadius6),
      keyboardType: TextInputType.numberWithOptions(decimal: true),
      textAlignVertical: TextAlignVertical.center,
    );
  }

  // 构建底部按钮
  Widget _buildBottomButtons() {
    return Container(
      padding: EdgeInsets.only(
        top: UiConstants.spacing20,
        bottom: ScreenUtil.bottomSafeHeight(context),
      ),
      child: Row(
        children: [
          Expanded(
            child: CommonButton.primary(
              '确认修改',
              size: CommonButtonSize.medium,
              borderRadius: UiConstants.borderRadiusCircle,
              onPressed: _handleModifyOrder,
            ),
          ),
        ],
      ),
    );
  }

  // 处理订单修改
  void _handleModifyOrder() {
    // 验证输入
    if (!_validateInputs()) {
      return;
    }

    // 构建修改后的订单模型
    final modifiedOrder = _buildModifiedOrderModel();

    // 这里可以调用API提交修改
    _submitOrderModification(modifiedOrder);

    // 先隐藏键盘，再关闭弹窗并返回修改后的订单
    FocusScope.of(context).unfocus();
    Navigator.of(context).pop(modifiedOrder);
  }

  // 验证输入
  bool _validateInputs() {
    // 验证委托价格（市价单或市价模式下不需要验证）
    if (widget.orderData.orderType != SpotOrderType.market && !_isMarketPrice) {
      if (_orderPriceController.text.trim().isEmpty) {
        _showErrorMessage('请输入委托价格');
        return false;
      }
      if (double.tryParse(_orderPriceController.text) == null) {
        _showErrorMessage('委托价格格式不正确');
        return false;
      }
    }

    // 验证委托数量
    if (_quantityController.text.trim().isEmpty) {
      _showErrorMessage('请输入委托数量');
      return false;
    }
    if (double.tryParse(_quantityController.text) == null) {
      _showErrorMessage('委托数量格式不正确');
      return false;
    }

    // 验证触发价格（如果需要）
    if (_triggerPriceController.text.isNotEmpty) {
      if (double.tryParse(_triggerPriceController.text) == null) {
        _showErrorMessage('触发价格格式不正确');
        return false;
      }
    }

    return true;
  }

  // 构建修改后的订单模型
  SpotOrderModel _buildModifiedOrderModel() {
    final originalOrder = widget.orderData;

    // 获取修改后的值
    final newPrice =
        _isMarketPrice ? 'MARKET' : _orderPriceController.text.trim();
    final newQuantity = _quantityController.text.trim();
    final newTriggerPrice =
        _triggerPriceController.text.trim().isEmpty
            ? null
            : _triggerPriceController.text.trim();
    final newTakeProfitPrice =
        _takeProfitController.text.trim().isEmpty
            ? null
            : _takeProfitController.text.trim();
    final newStopLossPrice =
        _stopLossController.text.trim().isEmpty
            ? null
            : _stopLossController.text.trim();

    // 重新计算金额（如果需要）
    final newAmount = _calculateAmount(newPrice, newQuantity);

    // 创建修改后的订单模型
    return SpotOrderModel(
      orderId: originalOrder.orderId,
      symbol: originalOrder.symbol,
      orderType: originalOrder.orderType,
      direction: originalOrder.direction,
      price: newPrice,
      quantity: newQuantity,
      amount: newAmount,
      filledAmount: originalOrder.filledAmount, // 添加缺失的参数
      status: originalOrder.status, // 状态保持不变
      createTime: originalOrder.createTime,
      fee: originalOrder.fee,
      filledQuantity: originalOrder.filledQuantity,
      avgPrice: originalOrder.avgPrice,
      triggerPrice: newTriggerPrice,
      takeProfitPrice: newTakeProfitPrice,
      stopLossPrice: newStopLossPrice,
    );
  }

  // 计算金额
  String _calculateAmount(String price, String quantity) {
    try {
      if (price == 'MARKET') {
        // 市价单使用原始金额或根据当前市价计算
        return widget.orderData.amount;
      }

      final priceValue = double.parse(price.replaceAll(',', ''));
      final quantityValue = double.parse(quantity.replaceAll(',', ''));

      if (_isQuoteCurrency) {
        // 如果当前是计价货币模式，quantity就是金额
        return quantity;
      } else {
        // 如果当前是基础货币模式，需要计算金额
        return (priceValue * quantityValue).toStringAsFixed(2);
      }
    } catch (e) {
      // 计算失败时返回原始金额
      return widget.orderData.amount;
    }
  }

  // 构建修改后的订单数据（用于API调用）
  Map<String, dynamic> _buildModifiedOrderData() {
    final orderData = widget.orderData;

    return {
      'orderId': orderData.orderId,
      'symbol': orderData.symbol,
      'orderType': orderData.orderType.name,
      'direction': orderData.direction.name,
      'price': _isMarketPrice ? 'MARKET' : _orderPriceController.text.trim(),
      'priceType': _isMarketPrice ? 'market' : 'limit',
      'quantity': _quantityController.text.trim(),
      'quantityCurrency': _getCurrentCurrency(), // 添加货币类型信息
      'isQuoteCurrency': _isQuoteCurrency, // 添加货币类型标识
      'triggerPrice':
          _triggerPriceController.text.trim().isEmpty
              ? null
              : _triggerPriceController.text.trim(),
      'takeProfitPrice':
          _takeProfitController.text.trim().isEmpty
              ? null
              : _takeProfitController.text.trim(),
      'stopLossPrice':
          _stopLossController.text.trim().isEmpty
              ? null
              : _stopLossController.text.trim(),
    };
  }

  // 提交订单修改（模拟API调用）
  void _submitOrderModification(SpotOrderModel modifiedOrder) {
    // TODO: 实现实际的API调用
    final apiData = _buildModifiedOrderData();
    debugPrint('提交订单修改: $apiData');

    // 这里可以调用实际的API
    // await orderService.modifyOrder(apiData);
  }

  // 显示错误消息
  void _showErrorMessage(String message) {
    Toastification.show(
      context,
      message: message,
      type: ToastificationType.error,
    );
  }

  // 构建计划委托价格字段（左侧输入框，右侧限价/市价切换按钮）
  Widget _buildPlanOrderPriceField() {
    return Padding(
      padding: EdgeInsets.only(bottom: UiConstants.spacing8),
      child: Row(
        children: [
          // 左侧：根据状态显示输入框或市价显示
          _isMarketPrice ? _buildMarketPrice() : _buildLimitPriceInput(),
          SizedBox(width: UiConstants.spacing8),
          // 右侧限价/市价切换按钮
          _buildPriceTypeToggleButton(),
        ],
      ),
    );
  }

  // 构建限价输入框
  Widget _buildLimitPriceInput() {
    return Expanded(
      child: TextFieldWidget(
        controller: _orderPriceController,
        labelText: '委托价格',
        height: 48,
        cursorHeight: 12,
        contentPadding: EdgeInsets.only(
          left: UiConstants.spacing10,
          top: UiConstants.spacing8,
          bottom: UiConstants.spacing8,
        ),
        padding: EdgeInsets.zero,
        radius: BorderRadius.circular(UiConstants.borderRadius6),
        keyboardType: TextInputType.numberWithOptions(decimal: true),
        textAlignVertical: TextAlignVertical.center,
      ),
    );
  }

  // 构建止盈止损价格字段（左侧输入框，右侧类型选择按钮）
  Widget _buildStopProfitLossPriceField() {
    return Padding(
      padding: EdgeInsets.only(bottom: UiConstants.spacing8),
      child: Row(
        children: [
          // 左侧输入框
          Expanded(
            child: TextFieldWidget(
              controller: _orderPriceController,
              labelText: '委托价格',
              height: 48,
              cursorHeight: 12,
              contentPadding: EdgeInsets.only(
                left: UiConstants.spacing10,
                top: UiConstants.spacing8,
                bottom: UiConstants.spacing8,
              ),
              padding: EdgeInsets.zero,
              radius: BorderRadius.circular(UiConstants.borderRadius6),
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              textAlignVertical: TextAlignVertical.center,
            ),
          ),
          SizedBox(width: UiConstants.spacing8),
          // 右侧类型选择按钮
          _buildStopLossTypeButton(),
        ],
      ),
    );
  }

  // 构建交易额输入字段（后缀为可切换的货币文本+down图标）
  Widget _buildTradeAmountField() {
    final currentCurrency = _getCurrentCurrency();

    return Padding(
      padding: EdgeInsets.only(bottom: UiConstants.spacing8),
      child: TextFieldWidget(
        controller: _quantityController,
        labelText: '交易额',
        height: _ModifyOrderDialogConfig.inputFieldHeight,
        cursorHeight: _ModifyOrderDialogConfig.cursorHeight,
        contentPadding: EdgeInsets.only(
          left: UiConstants.spacing10,
          top: UiConstants.spacing8,
          bottom: UiConstants.spacing8,
        ),
        padding: EdgeInsets.zero,
        radius: BorderRadius.circular(UiConstants.borderRadius6),
        keyboardType: TextInputType.numberWithOptions(decimal: true),
        textAlignVertical: TextAlignVertical.center,
        suffixIcon: _buildTradeAmountSuffix(currentCurrency),
        suffixIconConstraints: BoxConstraints(
          maxWidth: _ModifyOrderDialogConfig.suffixIconMaxWidth,
        ),
        helperText: _generateHelperText(),
      ),
    );
  }

  // 构建交易额后缀（可点击切换货币）
  Widget _buildTradeAmountSuffix(String currency) {
    return InkWellWidget(
      onTap: _toggleCurrencyType,
      child: Padding(
        padding: EdgeInsets.only(right: UiConstants.spacing10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              currency,
              style: context.templateStyle.text.bodyTextMedium.copyWith(
                color: context.templateColors.primary,
              ),
            ),
            ThemedImage(
              name: 'arrow_triangle_down',
              size: UiConstants.iconSize10,
              followTheme: true,
              margin: EdgeInsets.only(left: UiConstants.spacing6),
            ),
          ],
        ),
      ),
    );
  }

  // 生成帮助文本（根据当前货币类型显示相应的换算）
  String _generateHelperText() {
    final orderData = widget.orderData;
    final baseCurrency = _extractBaseCurrency();
    final quoteCurrency = _extractQuoteCurrency();

    if (_isQuoteCurrency) {
      // 当前显示计价货币，帮助文本显示基础货币换算
      return '≈ ${orderData.quantity} $baseCurrency';
    } else {
      // 当前显示基础货币，帮助文本显示计价货币换算
      return '≈ ${orderData.amount} $quoteCurrency';
    }
  }

  // 构建通用切换按钮
  Widget _buildToggleButton({
    required String text,
    required VoidCallback onTap,
    bool isActive = false,
    bool isWide = false,
    Widget? suffixIcon,
  }) {
    final horizontalPadding =
        isWide
            ? _ModifyOrderDialogConfig.wideButtonPadding
            : _ModifyOrderDialogConfig.defaultButtonPadding;

    return InkWellWidget(
      onTap: onTap,
      child: Container(
        height: _ModifyOrderDialogConfig.inputFieldHeight,
        padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: _getButtonBackgroundColor(isActive),
          borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              text,
              style: context.templateStyle.text.bodyTextMedium.copyWith(
                color: context.templateColors.textPrimary,
              ),
            ),
            if (suffixIcon != null) ...[
              SizedBox(width: UiConstants.spacing4),
              suffixIcon,
            ],
          ],
        ),
      ),
    );
  }

  // 获取按钮背景色
  Color _getButtonBackgroundColor(bool isActive) {
    return isActive
        ? context.templateColors.primary.withValues(alpha: 0.1)
        : context.templateColors.inputBackground;
  }

  // 构建限价/市价切换按钮
  Widget _buildPriceTypeToggleButton() {
    return _buildToggleButton(
      text: _isMarketPrice ? '市价' : '限价',
      onTap: _togglePriceType,
      isActive: _isMarketPrice,
      isWide: true,
    );
  }

  // 构建止盈止损类型选择按钮
  Widget _buildStopLossTypeButton() {
    return _buildToggleButton(
      text: '类型',
      onTap: _showStopLossTypeDialog,
      suffixIcon: ThemedImage(
        name: 'arrow_triangle_down',
        size: UiConstants.iconSize10,
        followTheme: true,
      ),
    );
  }

  // 切换价格类型
  void _togglePriceType() {
    setState(() {
      _isMarketPrice = !_isMarketPrice;
      // 如果切换到市价，清空价格输入框
      if (_isMarketPrice) {
        _orderPriceController.clear();
      }
    });
  }

  // 显示止盈止损类型选择对话框
  void _showStopLossTypeDialog() {
    // TODO: 实现止盈止损类型选择弹窗
    debugPrint('显示止盈止损类型选择弹窗');
  }

  // 构建市价显示
  Widget _buildMarketPrice() {
    final quoteCurrency = _extractQuoteCurrency();

    return Expanded(
      child: Container(
        height: _ModifyOrderDialogConfig.inputFieldHeight,
        padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing12),
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          color: context.templateColors.inputBackground,
          borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '市价成交',
              style: context.templateStyle.text.bodyTextMedium.copyWith(
                color: context.templateColors.textTertiary,
              ),
            ),
            Text(
              quoteCurrency,
              style: context.templateStyle.text.bodyTextMedium.copyWith(
                color: context.templateColors.textTertiary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建提示文本
  Widget _buildHintText() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing8),
      child: Text(
        '注意：受市场波动影响，一键撤单并重新挂单的修改功能，也存在无法成交或无法完全成功挂单的可能',
        style: context.templateStyle.text.bodySmall.copyWith(
          color: context.templateColors.warning,
        ),
      ),
    );
  }
}
