/*
*  编辑订单弹窗
*
*  功能：
*  - 提供订单编辑的弹窗入口
*  - 支持现货、止盈止损、杠杆三种订单类型
*  - 调用对应的订单内容组件
*  - 处理弹窗的显示和关闭逻辑
*
*  使用示例：
*  ```dart
*  // 方式1：指定订单类型
*  final result = await EditOrderDialog.show(context,
*    orderData: order,
*    orderType: OrderEditType.spot
*  );
*
*  // 方式2：使用便捷方法（推荐）
*  final result = await EditOrderDialog.showSpotOrder(context, orderData: order);
*  final result = await EditOrderDialog.showStopProfitLoss(context, orderData: order);
*  final result = await EditOrderDialog.showLeverageOrder(context, orderData: order);
*  ```
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/models/trade/spot_order_model.dart';
import 'modify_order_content.dart';
import 'take_profit_stop_loss_content.dart';

/// 订单编辑类型枚举
enum OrderEditType {
  /// 现货订单
  spot('现货', 'spot'),

  /// 止盈止损订单
  stopProfitLoss('止盈止损', 'stop_profit_loss'),

  /// 杠杆订单
  leverage('杠杆', 'leverage');

  const OrderEditType(this.displayName, this.value);

  /// 显示名称
  final String displayName;

  /// 枚举值
  final String value;

  /// 从字符串值获取枚举
  static OrderEditType fromValue(String value) {
    return OrderEditType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => OrderEditType.spot,
    );
  }

  /// 获取弹窗标题
  String get dialogTitle {
    switch (this) {
      case OrderEditType.spot:
        return '修改现货订单';
      case OrderEditType.stopProfitLoss:
        return '修改止盈止损';
      case OrderEditType.leverage:
        return '修改杠杆订单';
    }
  }

  /// 是否支持该订单类型的编辑
  bool get isEditable {
    switch (this) {
      case OrderEditType.spot:
        return true;
      case OrderEditType.stopProfitLoss:
        return true;
      case OrderEditType.leverage:
        return true; // 根据业务需求可以调整
    }
  }
}

class EditOrderDialog {
  /// 显示编辑订单底部弹窗
  static Future<SpotOrderModel?> show(
    BuildContext context, {
    required SpotOrderModel orderData,
    OrderEditType orderType = OrderEditType.spot,
  }) {
    // 检查订单类型是否支持编辑
    if (!orderType.isEditable) {
      throw ArgumentError('订单类型 ${orderType.displayName} 不支持编辑');
    }

    return BottomSheetWidget.show<SpotOrderModel?>(
      context: context,
      title: orderType.dialogTitle,
      showCloseButton: true,
      showHeader: true,
      showDragHandle: false,
      headerHeight: 68,
      showHeaderBorder: false,
      showBottomButton: false,
      useSafeArea: false,
      child: _buildOrderContent(orderData, orderType),
    );
  }

  /// 根据订单类型构建对应的内容组件
  static Widget _buildOrderContent(
    SpotOrderModel orderData,
    OrderEditType orderType,
  ) {
    switch (orderType) {
      case OrderEditType.spot:
        return ModifyOrderContent(orderData: orderData);

      case OrderEditType.stopProfitLoss:
        return TakeProfitStopLossContent(orderData: orderData);

      case OrderEditType.leverage:
        // TODO: 实现杠杆订单编辑组件
        return ModifyOrderContent(orderData: orderData); // 临时使用现货组件
    }
  }

  /// 便捷方法：显示现货订单编辑弹窗
  static Future<SpotOrderModel?> showSpotOrder(
    BuildContext context, {
    required SpotOrderModel orderData,
  }) {
    return show(context, orderData: orderData, orderType: OrderEditType.spot);
  }

  /// 便捷方法：显示止盈止损订单编辑弹窗
  static Future<SpotOrderModel?> showStopProfitLoss(
    BuildContext context, {
    required SpotOrderModel orderData,
  }) {
    return show(
      context,
      orderData: orderData,
      orderType: OrderEditType.stopProfitLoss,
    );
  }

  /// 便捷方法：显示杠杆订单编辑弹窗
  static Future<SpotOrderModel?> showLeverageOrder(
    BuildContext context, {
    required SpotOrderModel orderData,
  }) {
    return show(
      context,
      orderData: orderData,
      orderType: OrderEditType.leverage,
    );
  }
}
