/*
*  止盈止损弹窗内容
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/models/trade/spot_order_model.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/utils/index.dart';
import 'package:qubic_exchange/widgets/display/common_slider.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/widgets/trade_common/index.dart';

class TakeProfitStopLossContent extends StatefulWidget {
  final SpotOrderModel orderData;
  final double? currentPrice; // 当前价格

  const TakeProfitStopLossContent({
    super.key,
    required this.orderData,
    this.currentPrice,
  });

  @override
  State<TakeProfitStopLossContent> createState() =>
      _TakeProfitStopLossContentState();
}

class _TakeProfitStopLossContentState extends State<TakeProfitStopLossContent> {
  // 是否市价
  bool _isMarketPrice = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom * 0.7,
      ),
      child: Column(
        children: [
          // 基础信息
          _buildBaseInfo(),

          // 价格信息
          _buildInfoList(_generatePriceInfoItems()),

          // 止盈输入区域
          _buildInputArea(),

          // 止损输入区域
          _buildInputArea(),

          CommonButton.primary(
            '确认',
            size: CommonButtonSize.medium,
            width: double.infinity,
            borderRadius: UiConstants.borderRadiusCircle,
            margin: EdgeInsetsGeometry.only(top: UiConstants.spacing24),
            onPressed: () => {},
          ),

          SizedBox(height: ScreenUtil.bottomSafeHeight(context)),
        ],
      ),
    );
  }

  // 构建基础信息
  Widget _buildBaseInfo() {
    return Container(
      child: Row(
        children: [
          Text('BTC/USDT', style: context.templateStyle.text.h4),
          TagWidget(
            text: '买入',
            backgroundColor: context.templateColors.tradeBuy.withValues(
              alpha: 0.2,
            ),
            textStyle: context.templateStyle.text.tagText.copyWith(
              color: context.templateColors.tradeBuy,
            ),
            margin: EdgeInsets.only(left: UiConstants.spacing6),
          ),
          TagWidget(
            text: '计划委托',
            backgroundColor: context.templateColors.tabbarBackground,
            textStyle: context.templateStyle.text.tagText.copyWith(
              color: context.templateColors.textTertiary,
            ),
            margin: EdgeInsets.only(left: UiConstants.spacing6),
          ),
        ],
      ),
    );
  }

  // 生成价格信息项列表
  List<({String label, String value})> _generatePriceInfoItems() {
    final orderData = widget.orderData;
    final quoteCurrency = 'USDT';

    // 格式化当前价格
    final currentPriceText =
        widget.currentPrice != null
            ? '${NumberFormatUtil.formatCryptoPrice(widget.currentPrice!)} $quoteCurrency'
            : '--';

    // 只显示三个信息：当前价格、委托价格、委托数量
    return [
      (label: '当前价格', value: currentPriceText),
      (label: '委托价格', value: '${orderData.price} $quoteCurrency'),
      (label: '委托数量', value: orderData.quantity),
    ];
  }

  // 构建信息列表
  Widget _buildInfoList(List<({String label, String value})> items) {
    return Column(children: items.map(_buildInfoItem).toList());
  }

  Widget _buildInfoItem(({String label, String value}) item) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(item.label, style: context.templateStyle.text.bodySmall),
          Text(item.value, style: context.templateStyle.text.bodySmall),
        ],
      ),
    );
  }

  // 构建输入区域
  Widget _buildInputArea() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: UiConstants.spacing10),
          child: Text('止盈', style: context.templateStyle.text.bodyTextMedium),
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 价格输入
            Expanded(flex: 4, child: _buildPriceInput()),
            SizedBox(width: UiConstants.spacing8),
            // 收益率输入
            Expanded(flex: 2, child: _buildPercentageInput()),
          ],
        ),

        _buildSlider(),

        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 市价
            Expanded(flex: 4, child: _buildMarketPriceInput()),
            SizedBox(width: UiConstants.spacing8),
            // 切换按钮
            Expanded(flex: 2, child: _buildPriceTypeToggleButton()),
          ],
        ),

        Padding(
          padding: EdgeInsets.symmetric(vertical: UiConstants.spacing10),
          child: Text.rich(
            TextSpan(
              style: context.templateStyle.text.descriptionSmall,
              children: [
                TextSpan(text: '当最新价格'),
                TextSpan(text: '≥9.25 USDT'),
                TextSpan(text: '时，将以'),
                TextSpan(text: '-- USDT'),
                TextSpan(text: '卖出,'),
                TextSpan(text: '数量'),
                TextSpan(text: '0.2000000 BTC'),
                TextSpan(text: '，预计收益 -- USDT(--%)'),
              ],
            ),
            softWrap: true,
            overflow: TextOverflow.visible,
          ),
        ),
      ],
    );
  }

  // 构建滑块
  Widget _buildSlider() {
    return Padding(
      padding: EdgeInsets.only(bottom: UiConstants.spacing8),
      child: CommonSlider(
        min: 0,
        max: 100,
        initialValue: 50,
        showLabels: true,
        showTicks: true,
        showDividers: true,
        enableTooltip: true,
        sliderHeight: 200,
      ),
    );
  }

  // 构建价格输入框
  Widget _buildPriceInput() {
    return Padding(
      padding: EdgeInsets.only(bottom: UiConstants.spacing8),
      child: TextFieldWidget(
        labelText: '价格',
        height: 48,
        cursorHeight: 12,
        contentPadding: EdgeInsets.only(
          left: UiConstants.spacing10,
          top: UiConstants.spacing4,
          bottom: UiConstants.spacing4,
        ),
        padding: EdgeInsets.zero,
        radius: BorderRadius.circular(UiConstants.borderRadius6),
        keyboardType: TextInputType.numberWithOptions(decimal: true),
      ),
    );
  }

  // 构建收益率输入框
  Widget _buildPercentageInput() {
    return Padding(
      padding: EdgeInsets.only(bottom: UiConstants.spacing8),
      child: TextFieldWidget(
        labelText: '收益率',
        height: 48,
        cursorHeight: 12,
        contentPadding: EdgeInsets.only(
          left: UiConstants.spacing10,
          top: UiConstants.spacing4,
          bottom: UiConstants.spacing4,
        ),
        padding: EdgeInsets.zero,
        radius: BorderRadius.circular(UiConstants.borderRadius6),
        keyboardType: TextInputType.numberWithOptions(decimal: true),
        suffixIcon: Padding(
          padding: EdgeInsets.only(right: UiConstants.spacing10),
          child: Text('%', style: context.templateStyle.text.descriptionText),
        ),
        suffixIconConstraints: BoxConstraints(maxWidth: 70),
      ),
    );
  }

  // 构建市价输入框
  Widget _buildMarketPriceInput() {
    return Container(
      height: 48,
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing12),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        color: context.templateColors.inputBackground,
        borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '市价成交',
            style: context.templateStyle.text.bodyTextMedium.copyWith(
              color: context.templateColors.textTertiary,
            ),
          ),
          Text(
            'USDT',
            style: context.templateStyle.text.bodyTextMedium.copyWith(
              color: context.templateColors.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  // 构建市价、限价切换按钮
  Widget _buildPriceTypeToggleButton() {
    return _buildToggleButton(
      text: _isMarketPrice ? '市价' : '限价',
      onTap: _togglePriceType,
      isActive: _isMarketPrice,
      isWide: true,
    );
  }

  // 切换价格类型
  void _togglePriceType() {
    setState(() {
      _isMarketPrice = !_isMarketPrice;
    });
  }

  // 构建通用切换按钮
  Widget _buildToggleButton({
    required String text,
    required VoidCallback onTap,
    bool isActive = false,
    bool isWide = false,
  }) {
    return InkWellWidget(
      onTap: onTap,
      child: Container(
        height: 48,
        padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing14),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: context.templateColors.tabbarBackground,
          borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
        ),
        child: Text(text, style: context.templateStyle.text.bodyTextMedium),
      ),
    );
  }
}
