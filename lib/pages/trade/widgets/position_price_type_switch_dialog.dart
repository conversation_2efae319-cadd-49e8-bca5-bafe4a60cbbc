/*
*  持仓价格类型切换弹窗
*/

import 'package:flutter/material.dart';
import 'package:toastification/toastification.dart' hide Toastification;
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/utils/device/screen_util.dart';
import 'package:qubic_exchange/widgets/index.dart';

class PositionPriceTypeSwitchDialog {
  // 显示持仓价格类型切换底部弹窗
  static Future<String?> show(
    BuildContext context, {
    required String currentType,
    List<String>? priceTypes,
  }) {
    final List<String> defaultPriceTypes = ['cost_asset', 'breakeven_trade'];

    return BottomSheetWidget.show<String>(
      context: context,
      title: '选择持有价类型',
      titleAlign: TextAlign.start,
      showCloseButton: false,
      showHeader: true,
      showDragHandle: true,
      headerHeight: 68,
      showHeaderBorder: false,
      showBottomButton: false,
      useSafeArea: false,
      child: _PositionPriceTypeSwitchContent(
        currentType: currentType,
        priceTypes: priceTypes ?? defaultPriceTypes,
      ),
    );
  }
}

class _PositionPriceTypeSwitchContent extends StatefulWidget {
  final String currentType;
  final List<String> priceTypes;

  const _PositionPriceTypeSwitchContent({
    required this.currentType,
    required this.priceTypes,
  });

  @override
  State<_PositionPriceTypeSwitchContent> createState() =>
      _PositionPriceTypeSwitchContentState();
}

class _PositionPriceTypeSwitchContentState
    extends State<_PositionPriceTypeSwitchContent> {
  String? _selectedType;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.currentType;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...widget.priceTypes.map(
          (type) => _buildSelectItem(
            labelText: _getPriceTypeLabel(type),
            descriptionText: _getPriceTypeDescription(type),
            isSelected: _selectedType == type,
            onTap: () => _handleOptionSelected(type),
          ),
        ),
        SizedBox(height: UiConstants.spacing16),
        _buildBottomTip(),
        SizedBox(height: ScreenUtil.bottomSafeHeight(context)),
      ],
    );
  }

  // 处理选项选择
  void _handleOptionSelected(String type) {
    setState(() {
      _selectedType = type;
    });

    // 显示切换成功提示
    Toastification.show(
      context,
      message: '已切换至${_getPriceTypeLabel(type)}',
      type: ToastificationType.success,
    );

    // 关闭弹窗并返回选中的类型
    Navigator.of(context).pop(type);
  }

  // 获取价格类型标签
  String _getPriceTypeLabel(String type) {
    switch (type) {
      case 'breakeven_trade':
        return '保本价-交易';
      case 'cost_asset':
        return '成本价-资产';
      case 'mark':
        return '标记价格';
      case 'last':
        return '最新价格';
      case 'index':
        return '指数价格';
      default:
        return type;
    }
  }

  // 获取价格类型描述
  String _getPriceTypeDescription(String type) {
    switch (type) {
      case 'breakeven_trade':
        return '保本价 = (期初交易持仓量 × 期初保本价 + 变动量 × 变动时价格) ÷ (期初交易持仓量 + 变动量)\n统计2024年9月19日10:15:44 (UTC+8) 后的交易数据';
      case 'cost_asset':
        return '成本价 = (期初持仓量 × 期初成本价 + 增量 × 增量时价格) ÷ (期初持仓量 + 增量)\n统计2023年8月28日8:00:00 (UTC+8) 后的交易数据';
      case 'mark':
        return '用于计算未实现盈亏的标记价格';
      case 'last':
        return '最新成交价格';
      case 'index':
        return '现货指数价格';
      default:
        return '价格类型描述';
    }
  }

  // 构建选择项
  Widget _buildSelectItem({
    required String labelText,
    required String descriptionText,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWellWidget(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(UiConstants.spacing14),
        margin: EdgeInsets.only(bottom: UiConstants.spacing10),
        decoration: BoxDecoration(
          border: Border.all(
            width: context.templateStyles.borderWidthThin,
            color:
                isSelected
                    ? context.templateColors.textPrimary
                    : context.templateColors.border,
          ),
          borderRadius: BorderRadius.circular(UiConstants.borderRadius12),
          color: context.templateColors.popupBackground,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              labelText,
              style: context.templateStyle.text.bodyLargeMedium.copyWith(
                color: context.templateColors.textPrimary,
              ),
            ),
            SizedBox(height: UiConstants.spacing8),
            Text(
              descriptionText,
              style: context.templateStyle.text.hintText,
              maxLines: null,
              softWrap: true,
            ),
          ],
        ),
      ),
    );
  }

  // 构建底部提示信息
  Widget _buildBottomTip() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
      child: RichText(
        textAlign: TextAlign.start,
        text: TextSpan(
          style: context.templateStyle.text.bodyText,
          children: [
            TextSpan(text: '如果想了解更多关于现货盈亏分析信息，请点击\n'),
            TextSpan(
              text: '查看更多',
              style: context.templateStyle.text.bodyText.copyWith(
                color: context.templateColors.primary,
              ),
            ),
            TextSpan(text: ' >'),
          ],
        ),
      ),
    );
  }
}
