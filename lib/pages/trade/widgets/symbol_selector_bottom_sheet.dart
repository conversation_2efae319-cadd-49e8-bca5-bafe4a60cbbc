/*
*  币对选择底部弹窗
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/utils/device/screen_util.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:remixicon/remixicon.dart';

class SymbolSelectorBottomSheet {
  // 显示币对选择底部弹窗
  static Future<String?> show(
    BuildContext context, {
    List<String>? symbols,
    String? selectedSymbol,
    Function(String)? onSelected,
    bool showHeader = true,
    bool showCloseButton = false,
    bool showDragHandle = true,
    bool showHeaderBorder = false,
    double? maxHeight,
    double? headerHeight,
    bool useSafeArea = false,
  }) async {
    // 如果没有提供symbols，使用默认的交易对列表
    final defaultSymbols = symbols ?? _getDefaultSymbols();

    return await BottomSheetWidget.show<String>(
      context: context,
      title: '现货订单',
      titleAlign: TextAlign.start,
      showCloseButton: showCloseButton,
      showHeader: showHeader,
      showDragHandle: showDragHandle,
      headerHeight: headerHeight ?? 58,
      showHeaderBorder: showHeaderBorder,
      showBottomButton: false,
      maxHeight: maxHeight ?? MediaQuery.of(context).size.height * 0.8,
      contentPadding: EdgeInsets.zero,
      useSafeArea: useSafeArea,
      child: _SymbolSelectionContent(
        symbols: defaultSymbols,
        selectedSymbol: selectedSymbol,
        onSelected: onSelected,
      ),
    );
  }

  /// 获取默认的交易对列表
  static List<String> _getDefaultSymbols() {
    return [
      'LINK/USDT',
      'UNI/USDT',
      'SUSHI/USDT',
      'COMP/USDT',
      'AAVE/USDT',
      'YFI/USDT',
      'DOGE/USDT',
      'CHZ/USDT',
      'ENJ/USDT',
      'MANA/USDT',
      'BTC/USDT',
      'ETH/USDT',
      'BNB/USDT',
      'ADA/USDT',
      'SOL/USDT',
      'DOT/USDT',
      'AVAX/USDT',
      'MATIC/USDT',
      'ATOM/USDT',
      'NEAR/USDT',
    ];
  }
}

class _SymbolSelectionContent extends StatefulWidget {
  final List<String> symbols;
  final String? selectedSymbol;
  final Function(String)? onSelected;

  const _SymbolSelectionContent({
    required this.symbols,
    this.selectedSymbol,
    this.onSelected,
  });

  @override
  State<_SymbolSelectionContent> createState() =>
      _SymbolSelectionContentState();
}

class _SymbolSelectionContentState extends State<_SymbolSelectionContent> {
  String? _selectedSymbol;
  final TextEditingController _searchController = TextEditingController();
  List<String> _filteredSymbols = [];

  // 全部币对选项的标识
  static const String _allSymbolsOption = '全部币对';

  @override
  void initState() {
    super.initState();
    _selectedSymbol = widget.selectedSymbol;
    _filteredSymbols = widget.symbols;
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredSymbols = widget.symbols;
      } else {
        _filteredSymbols =
            widget.symbols
                .where((symbol) => symbol.toLowerCase().contains(query))
                .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: ScreenUtil.screenHeight(context) * 0.6,
      child: Column(
        children: [
          // 搜索框
          _buildSearchBar(),

          // 列表内容
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: _filteredSymbols.length + 1,
              itemBuilder: (context, index) {
                if (index == 0) {
                  // 全部币对选项
                  return _buildSelectItem(
                    symbol: _allSymbolsOption,
                    isSelected: _selectedSymbol == _allSymbolsOption,
                    onTap: () => _handleOptionSelected(_allSymbolsOption),
                    isSpecialOption: true,
                  );
                } else {
                  // 具体交易对
                  final symbol = _filteredSymbols[index - 1];
                  final isSelected = _selectedSymbol == symbol;
                  return _buildSelectItem(
                    symbol: symbol,
                    isSelected: isSelected,
                    onTap: () => _handleOptionSelected(symbol),
                    isSpecialOption: false,
                  );
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建搜索框
  Widget _buildSearchBar() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing16,
        vertical: UiConstants.spacing10,
      ),
      child: TextFieldWidget(hintText: '搜索'),
    );
  }

  // 构建选择项
  Widget _buildSelectItem({
    required String symbol,
    required bool isSelected,
    required VoidCallback onTap,
    bool isSpecialOption = false,
  }) {
    return InkWellWidget(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing18,
          vertical: UiConstants.spacing18,
        ),
        color:
            isSelected && isSpecialOption
                ? context.templateColors.tabbarActive
                : Colors.transparent,
        child: Row(
          children: [
            Expanded(
              child: Text(
                symbol,
                style: context.templateStyle.text.bodyTextMedium,
              ),
            ),
            if (isSelected)
              Icon(
                RemixIcons.check_line,
                size: 20,
                color:
                    isSpecialOption
                        ? context.templateColors.primary
                        : context.templateColors.textPrimary,
              ),
          ],
        ),
      ),
    );
  }

  // 处理选项选择
  void _handleOptionSelected(String symbol) {
    setState(() {
      _selectedSymbol = symbol;
    });

    // 调用回调函数
    widget.onSelected?.call(symbol);

    // 关闭弹窗并返回选中的symbol
    Navigator.of(context).pop(symbol);
  }
}
