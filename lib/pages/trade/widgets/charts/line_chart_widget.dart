/*
* 曲线图组件
* 基于 Syncfusion Flutter Charts 实现的时间序列线图
*/

import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:intl/intl.dart';
import 'package:qubic_exchange/core/index.dart';

/// 图表数据点模型
class ChartDataPoint {
  final DateTime time;
  final double value;

  ChartDataPoint({required this.time, required this.value});
}

/// 胜负图数据点模型
class WinLossDataPoint {
  final String category;
  final double value;
  final bool isPositive;

  WinLossDataPoint({
    required this.category,
    required this.value,
    required this.isPositive,
  });
}

/// 图表类型枚举
enum ChartType {
  line, // 线图
  spline, // 平滑线图
  area, // 面积图
  splineArea, // 平滑面积图
  column, // 柱状图
  winLoss, // 胜负图
}

/// 曲线图组件
class LineChartWidget extends StatefulWidget {
  /// 图表数据
  final List<ChartDataPoint> data;

  /// 胜负图数据
  final List<WinLossDataPoint>? winLossData;

  /// 图表标题
  final String? title;

  /// 图表类型
  final ChartType chartType;

  /// 线条颜色
  final Color? lineColor;

  /// 面积填充颜色
  final Color? fillColor;

  /// 线条宽度
  final double lineWidth;

  /// 是否显示数据点
  final bool showDataPoints;

  /// 是否显示网格线
  final bool showGridLines;

  /// 是否启用缩放
  final bool enableZooming;

  /// 是否启用平移
  final bool enablePanning;

  /// 是否启用工具提示
  final bool enableTooltip;

  /// 图表高度
  final double? height;

  /// Y轴数值格式化器
  final String Function(double value)? yAxisFormatter;

  /// X轴时间格式化器
  final String Function(DateTime time)? xAxisFormatter;

  /// 工具提示格式化器
  final String Function(ChartDataPoint point)? tooltipFormatter;

  /// X轴标签颜色
  final Color? xAxisLabelColor;

  /// Y轴标签颜色
  final Color? yAxisLabelColor;

  /// X轴线条颜色
  final Color? xAxisLineColor;

  /// Y轴线条颜色
  final Color? yAxisLineColor;

  /// X轴刻度标记颜色
  final Color? xAxisTickColor;

  /// Y轴刻度标记颜色
  final Color? yAxisTickColor;

  /// 网格线颜色
  final Color? gridLineColor;

  /// 是否启用十字线
  final bool enableCrosshair;

  /// 十字线颜色
  final Color? crosshairLineColor;

  /// 十字线宽度
  final double crosshairLineWidth;

  /// 是否显示X轴
  final bool showXAxis;

  /// 是否显示Y轴
  final bool showYAxis;

  /// 胜负图每一项的宽度（0.0-1.0，1.0表示无间距）
  final double winLossItemWidth;

  /// 是否显示胜负图分界线
  final bool showWinLossZeroLine;

  /// 胜负图分界线颜色
  final Color? winLossZeroLineColor;

  /// 点击回调
  final void Function(ChartDataPoint point)? onPointTap;

  const LineChartWidget({
    super.key,
    required this.data,
    this.winLossData,
    this.title,
    this.chartType = ChartType.spline,
    this.lineColor,
    this.fillColor,
    this.lineWidth = 2.0,
    this.showDataPoints = false,
    this.showGridLines = true,
    this.enableZooming = false,
    this.enablePanning = false,
    this.enableTooltip = true,
    this.height,
    this.yAxisFormatter,
    this.xAxisFormatter,
    this.tooltipFormatter,
    this.xAxisLabelColor,
    this.yAxisLabelColor,
    this.xAxisLineColor,
    this.yAxisLineColor,
    this.xAxisTickColor,
    this.yAxisTickColor,
    this.gridLineColor,
    this.enableCrosshair = false,
    this.crosshairLineColor,
    this.crosshairLineWidth = 1.0,
    this.showXAxis = true,
    this.showYAxis = true,
    this.winLossItemWidth = 0.8, // 默认宽度80%，留20%间距
    this.showWinLossZeroLine = true, // 默认显示分界线
    this.winLossZeroLineColor,
    this.onPointTap,
  });

  @override
  State<LineChartWidget> createState() => _LineChartWidgetState();

  /// 创建简单的线图
  factory LineChartWidget.simple({
    Key? key,
    required List<ChartDataPoint> data,
    String? title,
    Color? lineColor,
    double lineWidth = 2.0,
    double? height,
    Color? xAxisLabelColor,
    Color? yAxisLabelColor,
    Color? xAxisTickColor,
    Color? yAxisTickColor,
    bool enableCrosshair = false,
    Color? crosshairLineColor,
    void Function(ChartDataPoint point)? onPointTap,
  }) {
    return LineChartWidget(
      key: key,
      data: data,
      title: title,
      chartType: ChartType.spline,
      lineColor: lineColor,
      lineWidth: lineWidth,
      height: height,
      xAxisLabelColor: xAxisLabelColor,
      yAxisLabelColor: yAxisLabelColor,
      xAxisTickColor: xAxisTickColor,
      yAxisTickColor: yAxisTickColor,
      enableCrosshair: enableCrosshair,
      crosshairLineColor: crosshairLineColor,
      onPointTap: onPointTap,
    );
  }

  /// 创建面积图
  factory LineChartWidget.area({
    Key? key,
    required List<ChartDataPoint> data,
    String? title,
    Color? lineColor,
    Color? fillColor,
    double lineWidth = 2.0,
    double? height,
  }) {
    return LineChartWidget(
      key: key,
      data: data,
      title: title,
      chartType: ChartType.splineArea,
      lineColor: lineColor,
      fillColor: fillColor,
      lineWidth: lineWidth,
      height: height,
    );
  }

  /// 创建胜负图
  factory LineChartWidget.winLoss({
    Key? key,
    required List<WinLossDataPoint> winLossData,
    String? title,
    double? height,
    Color? positiveColor,
    Color? negativeColor,
    String Function(double value)? yAxisFormatter,
    bool showXAxis = false, // 胜负图默认不显示X轴
    bool showYAxis = false, // 胜负图默认不显示Y轴
    double itemWidth = 0.8, // 每一项的宽度（0.0-1.0）
    bool showZeroLine = true, // 是否显示分界线
    Color? zeroLineColor, // 分界线颜色
    void Function(WinLossDataPoint point)? onPointTap,
  }) {
    return LineChartWidget(
      key: key,
      data: [], // 胜负图不使用时间序列数据
      winLossData: winLossData,
      title: title,
      chartType: ChartType.winLoss,
      height: height,
      lineColor: positiveColor,
      fillColor: negativeColor,
      yAxisFormatter: yAxisFormatter,
      showGridLines: false,
      enableTooltip: false,
      showXAxis: showXAxis, // 胜负图默认不显示X轴
      showYAxis: showYAxis, // 胜负图默认不显示Y轴
      winLossItemWidth: itemWidth, // 传递宽度参数
      showWinLossZeroLine: showZeroLine, // 传递分界线显示参数
      winLossZeroLineColor: zeroLineColor, // 传递分界线颜色参数
    );
  }
}

class _LineChartWidgetState extends State<LineChartWidget> {
  late ZoomPanBehavior _zoomPanBehavior;
  late TooltipBehavior _tooltipBehavior;
  late CrosshairBehavior _crosshairBehavior;
  late TrackballBehavior _trackballBehavior;

  @override
  void initState() {
    super.initState();
    _initializeBehaviors();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _initializeBehaviors();
  }

  /// 初始化图表行为
  void _initializeBehaviors() {
    // 初始化缩放平移行为 - 禁用区域缩放
    _zoomPanBehavior = ZoomPanBehavior(
      enablePinching: widget.enableZooming,
      enablePanning: widget.enablePanning,
      enableDoubleTapZooming: widget.enableZooming,
      enableMouseWheelZooming: widget.enableZooming,
      enableSelectionZooming: false, // 禁用区域选择缩放
    );

    // 初始化工具提示行为 - 禁用节点点击提示
    _tooltipBehavior = TooltipBehavior(
      enable: false, // 禁用节点点击提示
    );

    // 初始化十字线行为
    _crosshairBehavior = CrosshairBehavior(
      enable: false, // 禁用原有的十字线
    );

    // 初始化轨迹球行为（用于实现十字线聚焦效果）
    _trackballBehavior = TrackballBehavior(
      enable: widget.enableCrosshair,
      lineColor:
          widget.crosshairLineColor ?? Colors.white.withValues(alpha: 0.8),
      lineWidth: widget.crosshairLineWidth,
      lineDashArray: [3, 3], // 虚线效果
      activationMode: ActivationMode.singleTap,
      hideDelay: 3000, // 3秒后自动隐藏
      shouldAlwaysShow: false,
      // 配置标记设置 - 在交叉点显示空心圆
      markerSettings: TrackballMarkerSettings(
        markerVisibility: TrackballVisibilityMode.visible,
        width: 8, // 标记宽度
        height: 8, // 标记高度
        borderWidth: 2, // 边框宽度
        borderColor: Colors.white, // 边框颜色
        color: Colors.transparent, // 填充颜色（透明）
        shape: DataMarkerType.circle, // 圆形标记
      ),
      // 配置tooltip显示模式
      tooltipDisplayMode: TrackballDisplayMode.floatAllPoints,
      // 自定义tooltip样式
      tooltipSettings: InteractiveTooltip(
        enable: true,
        color: const Color(0xFF2D2D2D), // 深色背景
        borderColor: Colors.white.withValues(alpha: 0.3),
        borderWidth: 1,
        textStyle: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
        format: 'point.x : point.y', // 显示格式
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colors = context.templateColors;

    return SizedBox(
      height: widget.height,
      child: SfCartesianChart(
        title:
            widget.title != null
                ? ChartTitle(
                  text: widget.title!,
                  textStyle: context.templateStyle.text.h5.copyWith(
                    color: colors.textPrimary,
                  ),
                )
                : ChartTitle(text: ''),
        backgroundColor: Colors.transparent,
        plotAreaBorderWidth: 0,
        margin: EdgeInsets.all(UiConstants.spacing8),

        // 主X轴 - 根据图表类型选择轴类型
        primaryXAxis: _buildPrimaryXAxis(colors),

        // 主Y轴 - 数值轴
        primaryYAxis: _buildPrimaryYAxis(colors),

        // 缩放平移行为
        zoomPanBehavior: _zoomPanBehavior,

        // 工具提示行为
        tooltipBehavior: _tooltipBehavior,

        // 十字线行为
        crosshairBehavior: _crosshairBehavior,

        // 轨迹球行为（实现十字线聚焦效果）
        trackballBehavior: _trackballBehavior,

        // 图表触摸交互事件
        onChartTouchInteractionUp: (ChartTouchInteractionArgs args) {
          // 处理图表点击事件
        },

        // 数据系列
        series: _buildSeries(),
      ),
    );
  }

  /// 构建主X轴
  ChartAxis _buildPrimaryXAxis(dynamic colors) {
    if (widget.chartType == ChartType.winLoss ||
        widget.chartType == ChartType.column) {
      // 胜负图和柱状图使用分类轴
      return CategoryAxis(
        majorGridLines: MajorGridLines(
          width:
              widget.chartType == ChartType.winLoss
                  ? 0
                  : (widget.showGridLines ? 0.5 : 0),
          color: widget.gridLineColor ?? colors.divider.withValues(alpha: 0.3),
        ),
        axisLine: AxisLine(
          width:
              widget.showXAxis
                  ? (widget.chartType == ChartType.winLoss ? 0 : 0.5)
                  : 0,
          color: widget.xAxisLineColor ?? colors.divider,
        ),
        majorTickLines: MajorTickLines(
          size:
              widget.showXAxis
                  ? (widget.chartType == ChartType.winLoss ? 0 : 4)
                  : 0,
          width:
              widget.showXAxis
                  ? (widget.chartType == ChartType.winLoss ? 0 : 1)
                  : 0,
          color: widget.xAxisTickColor ?? Colors.transparent,
        ),
        labelStyle: context.templateStyle.text.bodySmall.copyWith(
          color:
              widget.showXAxis
                  ? (widget.xAxisLabelColor ?? colors.textSecondary)
                  : Colors.transparent,
        ),
        isVisible: widget.showXAxis, // 控制X轴是否显示
      );
    } else {
      // 其他图表类型使用时间轴
      return DateTimeAxis(
        majorGridLines: MajorGridLines(
          width: widget.showGridLines ? 0.5 : 0,
          color: widget.gridLineColor ?? colors.divider.withValues(alpha: 0.3),
        ),
        axisLine: AxisLine(
          width: widget.showXAxis ? 0.5 : 0,
          color: widget.xAxisLineColor ?? colors.divider,
        ),
        majorTickLines: MajorTickLines(
          size: widget.showXAxis ? 4 : 0,
          width: widget.showXAxis ? 1 : 0,
          color: widget.xAxisTickColor ?? Colors.transparent,
        ),
        minorTickLines: MinorTickLines(
          size: widget.showXAxis ? 2 : 0,
          width: widget.showXAxis ? 0.5 : 0,
          color: widget.xAxisTickColor ?? Colors.transparent,
        ),
        labelStyle: context.templateStyle.text.bodySmall.copyWith(
          color:
              widget.showXAxis
                  ? (widget.xAxisLabelColor ?? colors.textSecondary)
                  : Colors.transparent,
        ),
        dateFormat: DateFormat('HH:mm'),
        intervalType: DateTimeIntervalType.auto,
        isVisible: widget.showXAxis, // 控制X轴是否显示
      );
    }
  }

  /// 构建主Y轴
  ChartAxis _buildPrimaryYAxis(dynamic colors) {
    return NumericAxis(
      majorGridLines: MajorGridLines(
        width:
            widget.chartType == ChartType.winLoss
                ? 0
                : (widget.showGridLines ? 0.5 : 0),
        color: widget.gridLineColor ?? colors.divider.withValues(alpha: 0.3),
      ),
      axisLine: AxisLine(width: 0), // Y轴线始终隐藏
      majorTickLines: MajorTickLines(
        size:
            widget.showYAxis
                ? (widget.chartType == ChartType.winLoss ? 0 : 4)
                : 0,
        width:
            widget.showYAxis
                ? (widget.chartType == ChartType.winLoss ? 0 : 1)
                : 0,
        color: widget.yAxisTickColor ?? Colors.transparent,
      ),
      minorTickLines: MinorTickLines(
        size:
            widget.showYAxis
                ? (widget.chartType == ChartType.winLoss ? 0 : 2)
                : 0,
        width:
            widget.showYAxis
                ? (widget.chartType == ChartType.winLoss ? 0 : 0.5)
                : 0,
        color: widget.yAxisTickColor ?? Colors.transparent,
      ),
      labelStyle: context.templateStyle.text.bodySmall.copyWith(
        color:
            widget.showYAxis
                ? (widget.yAxisLabelColor ?? colors.textTertiary)
                : Colors.transparent,
      ),
      numberFormat: NumberFormat.compact(),
      isVisible: widget.showYAxis, // 控制Y轴是否显示
    );
  }

  /// 构建数据系列
  List<CartesianSeries> _buildSeries() {
    final lineColor =
        widget.lineColor ?? context.templateColors.primary; // 默认青色
    final fillColor = widget.fillColor ?? lineColor.withValues(alpha: 0.3);

    // 通用的标记设置
    // 普通数据点标记（小圆点）
    final markerSettings = MarkerSettings(
      isVisible: widget.showDataPoints,
      height: 4,
      width: 4,
      color: lineColor,
      borderColor: lineColor,
    );

    switch (widget.chartType) {
      case ChartType.line:
        return [
          LineSeries<ChartDataPoint, DateTime>(
            dataSource: widget.data,
            xValueMapper: (ChartDataPoint point, _) => point.time,
            yValueMapper: (ChartDataPoint point, _) => point.value,
            color: lineColor,
            width: widget.lineWidth,
            markerSettings: markerSettings,
            animationDuration: 1000,
            enableTooltip: widget.enableTooltip,
            onPointTap: (ChartPointDetails details) {
              if (widget.onPointTap != null && details.pointIndex != null) {
                final point = widget.data[details.pointIndex!];
                widget.onPointTap!(point);
              }
            },
          ),
        ];

      case ChartType.spline:
        return [
          SplineSeries<ChartDataPoint, DateTime>(
            dataSource: widget.data,
            xValueMapper: (ChartDataPoint point, _) => point.time,
            yValueMapper: (ChartDataPoint point, _) => point.value,
            color: lineColor,
            width: widget.lineWidth,
            splineType: SplineType.natural,
            markerSettings: MarkerSettings(
              isVisible: widget.showDataPoints,
              height: 4,
              width: 4,
              shape: DataMarkerType.circle,
              color: lineColor,
              borderColor: lineColor,
              borderWidth: 1,
            ),
            // 自定义tooltip标记设置
            onRendererCreated: (ChartSeriesController controller) {
              // 这里可以添加自定义渲染逻辑
            },
            animationDuration: 1000,
            enableTooltip: widget.enableTooltip,
            onPointTap: (ChartPointDetails details) {
              if (widget.onPointTap != null && details.pointIndex != null) {
                final point = widget.data[details.pointIndex!];
                widget.onPointTap!(point);
              }
            },
          ),
        ];

      case ChartType.area:
        return [
          AreaSeries<ChartDataPoint, DateTime>(
            dataSource: widget.data,
            xValueMapper: (ChartDataPoint point, _) => point.time,
            yValueMapper: (ChartDataPoint point, _) => point.value,
            color: fillColor,
            borderColor: lineColor,
            borderWidth: widget.lineWidth,
            markerSettings: markerSettings,
            animationDuration: 1000,
            enableTooltip: widget.enableTooltip,
            onPointTap: (ChartPointDetails details) {
              if (widget.onPointTap != null && details.pointIndex != null) {
                final point = widget.data[details.pointIndex!];
                widget.onPointTap!(point);
              }
            },
          ),
        ];

      case ChartType.splineArea:
        return [
          SplineAreaSeries<ChartDataPoint, DateTime>(
            dataSource: widget.data,
            xValueMapper: (ChartDataPoint point, _) => point.time,
            yValueMapper: (ChartDataPoint point, _) => point.value,
            color: fillColor,
            borderColor: lineColor,
            borderWidth: widget.lineWidth,
            splineType: SplineType.natural,
            markerSettings: markerSettings,
            animationDuration: 1000,
            enableTooltip: widget.enableTooltip,
            onPointTap: (ChartPointDetails details) {
              if (widget.onPointTap != null && details.pointIndex != null) {
                final point = widget.data[details.pointIndex!];
                widget.onPointTap!(point);
              }
            },
          ),
        ];

      case ChartType.column:
        return [
          ColumnSeries<ChartDataPoint, DateTime>(
            dataSource: widget.data,
            xValueMapper: (ChartDataPoint point, _) => point.time,
            yValueMapper: (ChartDataPoint point, _) => point.value,
            color: lineColor,
            width: 0.6,
            borderRadius: const BorderRadius.all(Radius.circular(2)),
            animationDuration: 1000,
            enableTooltip: widget.enableTooltip,
            onPointTap: (ChartPointDetails details) {
              if (widget.onPointTap != null && details.pointIndex != null) {
                final point = widget.data[details.pointIndex!];
                widget.onPointTap!(point);
              }
            },
          ),
        ];

      case ChartType.winLoss:
        if (widget.winLossData == null) return [];
        final positiveColor =
            widget.lineColor ?? context.templateColors.success;
        final negativeColor = widget.fillColor ?? context.templateColors.error;

        // 创建零线数据（用于显示分界线）
        final zeroLineData =
            widget.winLossData!
                .map(
                  (point) => WinLossDataPoint(
                    category: point.category,
                    value: 0.0, // 零值
                    isPositive: true,
                  ),
                )
                .toList();

        final series = <CartesianSeries>[
          // 胜负图柱状图
          ColumnSeries<WinLossDataPoint, String>(
            dataSource: widget.winLossData!,
            xValueMapper: (WinLossDataPoint point, _) => point.category,
            yValueMapper: (WinLossDataPoint point, _) => point.value,
            pointColorMapper:
                (WinLossDataPoint point, _) =>
                    point.isPositive ? positiveColor : negativeColor,
            width: widget.winLossItemWidth, // 使用自定义宽度
            borderRadius: const BorderRadius.all(Radius.circular(2)),
            animationDuration: 1000,
            enableTooltip: widget.enableTooltip,
            dataLabelSettings: DataLabelSettings(
              isVisible: true,
              labelAlignment: ChartDataLabelAlignment.outer,
              textStyle: context.templateStyle.text.bodySmall.copyWith(
                color: context.templateColors.textSecondary,
              ),
              builder: (data, point, series, pointIndex, seriesIndex) {
                final winLossPoint = data as WinLossDataPoint;
                final formattedValue =
                    widget.yAxisFormatter?.call(winLossPoint.value) ??
                    winLossPoint.value.toStringAsFixed(6);

                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 4,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: context.templateColors.background.withValues(
                      alpha: 0.8,
                    ),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    formattedValue,
                    style: context.templateStyle.text.bodySmall.copyWith(
                      color:
                          winLossPoint.isPositive
                              ? positiveColor
                              : negativeColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                );
              },
            ),
          ),
        ];

        // 如果需要显示分界线，添加零线
        if (widget.showWinLossZeroLine) {
          series.add(
            LineSeries<WinLossDataPoint, String>(
              dataSource: zeroLineData,
              xValueMapper: (WinLossDataPoint point, _) => point.category,
              yValueMapper: (WinLossDataPoint point, _) => point.value,
              color:
                  widget.winLossZeroLineColor ??
                  context.templateColors.divider, // 使用自定义颜色或默认分割线颜色
              width: 1.0, // 线条宽度
              dashArray: const [5, 3], // 虚线效果
              animationDuration: 0, // 不需要动画
              enableTooltip: false, // 不显示tooltip
              markerSettings: const MarkerSettings(isVisible: false), // 不显示标记
            ),
          );
        }

        return series;
    }
  }
}
