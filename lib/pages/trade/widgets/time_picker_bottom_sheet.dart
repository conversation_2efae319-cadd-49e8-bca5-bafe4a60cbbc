/*
*  时间选择底部弹窗
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/widgets/picker/date_wheel_picker.dart';

/// 时间选择类型枚举
enum DateSelectionType { start, end }

/// 快捷时间选项配置
class QuickTimeOption {
  final String label;
  final int days;

  const QuickTimeOption({required this.label, required this.days});
}

/// 常量定义
class _TimePickerConstants {
  static const double pickerHeight = 200.0;
  static const double pickerItemHeight = 30.0;
  static const double dateInputHeight = 36.0;
  static const int noQuickOptionSelected = -1;

  static const List<QuickTimeOption> quickOptions = [
    QuickTimeOption(label: '7天', days: 7),
    QuickTimeOption(label: '30天', days: 30),
    QuickTimeOption(label: '90天', days: 90),
  ];
}

/// 时间范围选择结果
class TimeRangeResult {
  final DateTime startDate;
  final DateTime endDate;
  final bool hideFailedOrders;

  TimeRangeResult({
    required this.startDate,
    required this.endDate,
    this.hideFailedOrders = false,
  });
}

/// 时间选择器底部弹窗
///
/// 提供快捷时间选项、自定义日期范围选择和滚轮日期选择器
/// 支持开始时间和结束时间的独立选择，带有视觉反馈
class TimePickerBottomSheet {
  /// 显示时间选择底部弹窗
  static Future<TimeRangeResult?> show(
    BuildContext context, {
    DateTime? initialStartDate,
    DateTime? initialEndDate,
    bool initialHideFailedOrders = false,
    bool showHeader = true,
    bool showCloseButton = false,
    bool showDragHandle = true,
    bool showHeaderBorder = false,
    double? maxHeight,
    double? headerHeight,
    bool useSafeArea = true,
  }) async {
    return await BottomSheetWidget.show<TimeRangeResult>(
      context: context,
      title: '选择时间',
      titleAlign: TextAlign.start,
      showCloseButton: showCloseButton,
      showHeader: showHeader,
      showDragHandle: showDragHandle,
      headerHeight: headerHeight ?? 58,
      showHeaderBorder: showHeaderBorder,
      showBottomButton: false,
      maxHeight: maxHeight ?? MediaQuery.of(context).size.height * 0.8,
      useSafeArea: useSafeArea,
      child: _TimePickerContent(
        initialStartDate: initialStartDate,
        initialEndDate: initialEndDate,
        initialHideFailedOrders: initialHideFailedOrders,
      ),
    );
  }
}

class _TimePickerContent extends StatefulWidget {
  final DateTime? initialStartDate;
  final DateTime? initialEndDate;
  final bool initialHideFailedOrders;

  const _TimePickerContent({
    this.initialStartDate,
    this.initialEndDate,
    this.initialHideFailedOrders = false,
  });

  @override
  State<_TimePickerContent> createState() => _TimePickerContentState();
}

class _TimePickerContentState extends State<_TimePickerContent> {
  late DateTime _startDate;
  late DateTime _endDate;
  late bool _hideFailedOrders;

  // 快捷时间选项
  int _selectedQuickOption = _TimePickerConstants.noQuickOptionSelected;

  // 当前选择的时间类型
  DateSelectionType _selectionType = DateSelectionType.start;

  @override
  void initState() {
    super.initState();
    final now = DateTime.now();
    // 默认开始时间为7天前，结束时间为当前日期
    _startDate = widget.initialStartDate ?? now.subtract(Duration(days: 7));
    _endDate = widget.initialEndDate ?? now;
    _hideFailedOrders = widget.initialHideFailedOrders;

    // 如果没有传入初始日期，默认选择第一个快捷选项（7天）
    if (widget.initialStartDate == null && widget.initialEndDate == null) {
      _selectedQuickOption = 0; // 选择第一项（7天）
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 快捷时间选项
        _buildQuickTimeOptions(),

        // 自定义日期范围
        _buildCustomDateRange(),

        // 日历选择器
        _buildCalendar(),

        // 底部选项和按钮
        _buildBottomSection(),
      ],
    );
  }

  /// 构建快捷时间选项
  Widget _buildQuickTimeOptions() {
    return Row(
      children:
          _TimePickerConstants.quickOptions.asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            final isSelected = _selectedQuickOption == index;

            return Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                  right:
                      index < _TimePickerConstants.quickOptions.length - 1
                          ? UiConstants.spacing12
                          : 0,
                ),
                child: InkWellWidget(
                  onTap: () => _selectQuickOption(index, option.days),
                  child: Container(
                    height: _TimePickerConstants.dateInputHeight,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color:
                            isSelected
                                ? context.templateColors.textPrimary
                                : context.templateColors.border,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(
                        UiConstants.borderRadius8,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        option.label,
                        style: context.templateStyle.text.bodyTextMedium,
                      ),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
    );
  }

  /// 选择快捷时间选项
  void _selectQuickOption(int index, int days) {
    setState(() {
      _selectedQuickOption = index;
      _endDate = DateTime.now();
      _startDate = _endDate.subtract(Duration(days: days));

      // 重要：选择快捷选项后，自动切换到开始日期选择
      // 这样用户可以看到开始日期，并且滚轮会滚动到正确位置
      _selectionType = DateSelectionType.start;
    });
  }

  /// 构建自定义日期范围
  Widget _buildCustomDateRange() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: UiConstants.spacing10),
      child: Row(
        children: [
          // 开始日期
          Expanded(
            child: InkWellWidget(
              onTap: () => _selectStartDate(),
              child: Container(
                height: _TimePickerConstants.dateInputHeight,
                padding: EdgeInsets.symmetric(
                  horizontal: UiConstants.spacing12,
                ),
                decoration: BoxDecoration(
                  color: context.templateColors.inputBackground,
                  borderRadius: BorderRadius.circular(
                    UiConstants.borderRadius8,
                  ),
                  border: Border.all(
                    color:
                        _selectionType == DateSelectionType.start
                            ? context.templateColors.textPrimary
                            : context.templateColors.inputBackground,
                    width: _selectionType == DateSelectionType.start ? 1 : 1,
                  ),
                ),
                child: Center(
                  child: Text(
                    _formatDate(_startDate),
                    style: context.templateStyle.text.bodyTextMedium.copyWith(
                      color:
                          _selectionType == DateSelectionType.start
                              ? context.templateColors.textPrimary
                              : context.templateColors.textPrimary,
                    ),
                  ),
                ),
              ),
            ),
          ),

          // 分隔符
          Padding(
            padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing12),
            child: Text(
              '到',
              style: context.templateStyle.text.bodyTextMedium.copyWith(
                color: context.templateColors.textSecondary,
              ),
            ),
          ),

          // 结束日期
          Expanded(
            child: InkWellWidget(
              onTap: () => _selectEndDate(),
              child: Container(
                height: _TimePickerConstants.dateInputHeight,
                padding: EdgeInsets.symmetric(
                  horizontal: UiConstants.spacing12,
                ),
                decoration: BoxDecoration(
                  color: context.templateColors.inputBackground,
                  borderRadius: BorderRadius.circular(
                    UiConstants.borderRadius8,
                  ),
                  border: Border.all(
                    color:
                        _selectionType == DateSelectionType.end
                            ? context.templateColors.textPrimary
                            : context.templateColors.inputBackground,
                    width: _selectionType == DateSelectionType.end ? 1 : 1,
                  ),
                ),
                child: Center(
                  child: Text(
                    _formatDate(_endDate),
                    style: context.templateStyle.text.bodyTextMedium.copyWith(
                      color:
                          _selectionType == DateSelectionType.end
                              ? context.templateColors.textPrimary
                              : context.templateColors.textPrimary,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 格式化日期显示为 YYYY-MM-DD 格式
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 获取当前选中的日期（根据选择类型）
  DateTime get _currentSelectedDate =>
      _selectionType == DateSelectionType.start ? _startDate : _endDate;

  /// 选择开始日期
  void _selectStartDate() {
    setState(() {
      _selectionType = DateSelectionType.start;
      // 只有在有快捷选项选中时才清除，避免不必要的滚轮跳动
      if (_selectedQuickOption != _TimePickerConstants.noQuickOptionSelected) {
        _selectedQuickOption = _TimePickerConstants.noQuickOptionSelected;
      }
    });
  }

  /// 选择结束日期
  void _selectEndDate() {
    setState(() {
      _selectionType = DateSelectionType.end;
      // 只有在有快捷选项选中时才清除，避免不必要的滚轮跳动
      if (_selectedQuickOption != _TimePickerConstants.noQuickOptionSelected) {
        _selectedQuickOption = _TimePickerConstants.noQuickOptionSelected;
      }
    });
  }

  /// 构建日期滚轮选择器
  Widget _buildCalendar() {
    return DateWheelPicker(
      key: ValueKey(
        '${_selectionType}_${_currentSelectedDate.millisecondsSinceEpoch}',
      ), // 强制重建
      selectedDate: _currentSelectedDate,
      onDateChanged: _updateSelectedDate,
      config: DateWheelPickerConfig(
        height: _TimePickerConstants.pickerHeight,
        itemHeight: _TimePickerConstants.pickerItemHeight,
        showBorder: true,
        borderColor: context.templateColors.border,
        backgroundColor: context.templateColors.surface,
        borderRadius: UiConstants.borderRadius8,
        // 自定义文本样式
        selectedFontSize: UiConstants.fontSize20,
        unselectedFontSize: UiConstants.fontSize16,
        selectedFontWeight: UiConstants.fontWeightMedium,
        unselectedFontWeight: UiConstants.fontWeightRegular,
        selectedTextColor: context.templateColors.textPrimary,
        unselectedTextColor: context.templateColors.textSecondary,
        // 自定义视觉效果
        magnification: 1.15,
        squeeze: 1.2,
        diameterRatio: 2.8,
        offAxisFraction: 0.0,
      ),
      currentYearOnly: true,
    );
  }

  /// 更新选中的日期
  void _updateSelectedDate(DateTime newDate) {
    setState(() {
      if (_selectionType == DateSelectionType.start) {
        _startDate = newDate;
      } else {
        _endDate = newDate;
      }
      // 只有在有快捷选项选中时才清除，避免不必要的状态变化
      if (_selectedQuickOption != _TimePickerConstants.noQuickOptionSelected) {
        _selectedQuickOption = _TimePickerConstants.noQuickOptionSelected;
      }
    });
  }

  /// 构建底部区域
  Widget _buildBottomSection() {
    return Container(
      decoration: BoxDecoration(color: context.templateColors.popupBackground),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 隐藏撤单/失败单选项
          Row(
            children: [
              CheckboxWidget(
                value: _hideFailedOrders,
                customSize: UiConstants.iconSize14,
                onChanged: (value) {
                  setState(() {
                    _hideFailedOrders = value;
                  });
                },
                activeColor: context.templateColors.primary,
              ),
              SizedBox(width: UiConstants.spacing8),
              Text(
                '隐藏撤单/失败单',
                style: context.templateStyle.text.bodyTextMedium,
              ),
            ],
          ),
          SizedBox(height: UiConstants.spacing4),
          Text(
            '*需要查看更多数据，可在Web端导出近18个月的数据。',
            style: context.templateStyle.text.hintText,
          ),

          SizedBox(height: UiConstants.spacing16),

          // 操作按钮
          Row(
            children: [
              // 重置按钮
              Expanded(
                child: CommonButton.outlined(
                  '重置',
                  height: 40,
                  size: CommonButtonSize.medium,
                  width: double.infinity,
                  onPressed: _reset,
                ),
              ),

              SizedBox(width: UiConstants.spacing12),

              // 确定按钮
              Expanded(
                child: CommonButton.primary(
                  '确定',
                  height: 40,
                  size: CommonButtonSize.medium,
                  width: double.infinity,
                  onPressed: _confirm,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 重置选择
  void _reset() {
    setState(() {
      final now = DateTime.now();
      _endDate = now;
      _hideFailedOrders = false;
      _selectedQuickOption = 0; // 默认选择第一项（7天）
      _selectionType = DateSelectionType.start; // 重置为选择开始时间
      // 根据第一个快捷选项设置开始时间
      _startDate = now.subtract(
        Duration(days: _TimePickerConstants.quickOptions[0].days),
      );
    });
  }

  /// 确认选择
  void _confirm() {
    // 验证时间范围
    if (_startDate.isAfter(_endDate)) {
      // 如果开始时间晚于结束时间，交换它们
      final temp = _startDate;
      _startDate = _endDate;
      _endDate = temp;
    }

    final result = TimeRangeResult(
      startDate: _startDate,
      endDate: _endDate,
      hideFailedOrders: _hideFailedOrders,
    );
    Navigator.of(context).pop(result);
  }
}
