/*
*  订单类型选择底部弹窗
*/

import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:remixicon/remixicon.dart';

class OrderTypeSelectorBottomSheet {
  // 显示订单类型选择底部弹窗
  static Future<String?> show(
    BuildContext context, {
    required List<String> orderTypes,
    String? selectedType,
    Function(String)? onSelected,
    bool showHeader = true,
    bool showCloseButton = false,
    bool showDragHandle = true,
    bool showHeaderBorder = false,
    double? maxHeight,
    double? headerHeight,
    bool useSafeArea = false,
  }) async {
    return await BottomSheetWidget.show<String>(
      context: context,
      title: '选择订单类型',
      titleAlign: TextAlign.start,
      showCloseButton: showCloseButton,
      showHeader: showHeader,
      showDragHandle: showDragHandle,
      headerHeight: headerHeight ?? 58,
      showHeaderBorder: showHeaderBorder,
      showBottomButton: false,
      maxHeight: maxHeight ?? MediaQuery.of(context).size.height * 0.8,
      useSafeArea: useSafeArea,
      contentPadding: EdgeInsets.zero,
      child: _OrderTypeSelectionContent(
        orderTypes: orderTypes,
        selectedType: selectedType,
        onSelected: onSelected,
      ),
    );
  }
}

class _OrderTypeSelectionContent extends StatefulWidget {
  final List<String> orderTypes;
  final String? selectedType;
  final Function(String)? onSelected;

  const _OrderTypeSelectionContent({
    required this.orderTypes,
    this.selectedType,
    this.onSelected,
  });

  @override
  State<_OrderTypeSelectionContent> createState() =>
      _OrderTypeSelectionContentState();
}

class _OrderTypeSelectionContentState
    extends State<_OrderTypeSelectionContent> {
  String? _selectedType;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.selectedType;
  }

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: widget.orderTypes.length,
      separatorBuilder:
          (context, index) =>
              Divider(height: 1, color: context.templateColors.popupBackground),
      itemBuilder: (context, index) {
        final type = widget.orderTypes[index];
        final isSelected = _selectedType == type;
        return _buildSelectItem(
          type: type,
          isSelected: isSelected,
          onTap: () => _handleOptionSelected(type),
        );
      },
    );
  }

  // 处理选项选择
  void _handleOptionSelected(String type) {
    setState(() {
      _selectedType = type;
    });

    // 调用回调函数
    widget.onSelected?.call(type);

    // 关闭弹窗并返回选中的类型
    Navigator.of(context).pop(type);
  }

  // 构建选择项
  Widget _buildSelectItem({
    required String type,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWellWidget(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(UiConstants.spacing18),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? context.templateColors.tabbarBackground
                  : Colors.transparent,
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                type,
                style: context.templateStyle.text.bodyTextMedium,
              ),
            ),
            if (isSelected)
              Icon(
                RemixIcons.check_line,
                size: 20,
                color: context.templateColors.primary,
              ),
          ],
        ),
      ),
    );
  }
}
