/*
*  合约交易主入口
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'sub_pages/export_sub_page.dart';

class ContractTradePage extends StatefulWidget {
  const ContractTradePage({super.key});

  @override
  State<ContractTradePage> createState() => _ContractTradePageState();
}

class _ContractTradePageState extends State<ContractTradePage>
    with TickerProviderStateMixin {
  // 构建选项卡控制器
  late TabController _tabController;

  // 构建选项卡项
  static const List<String> _tabKeys = ['合约', '事件合约'];

  // 滚动状态管理
  bool _isTabBarVisible = true;
  double _hideScrollOffset = 0.0; // 记录隐藏时的滚动位置

  // 初始化
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabKeys.length, vsync: this);
  }

  // 销毁
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // 处理滚动距离变化
  void _handleScrollOffset(double scrollOffset) {
    const double hideThreshold = 40.0; // 隐藏阈值

    if (scrollOffset > hideThreshold && _isTabBarVisible) {
      // 向下滚动超过阈值，隐藏标签栏
      _hideTabBar();
      _hideScrollOffset = scrollOffset; // 记录隐藏时的位置
    } else if (scrollOffset <= _hideScrollOffset && !_isTabBarVisible) {
      // 回滚到隐藏位置或更上方，显示标签栏
      _showTabBar();
    }
  }

  // 隐藏标签栏
  void _hideTabBar() {
    if (_isTabBarVisible) {
      setState(() {
        _isTabBarVisible = false;
      });
    }
  }

  // 显示标签栏
  void _showTabBar() {
    if (!_isTabBarVisible) {
      setState(() {
        _isTabBarVisible = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.templateColors.surface,
      body: SafeArea(
        child: Column(
          children: [
            // 使用 AnimatedSize 实现平滑的高度过渡
            AnimatedSize(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: AnimatedSlide(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                offset: _isTabBarVisible ? Offset.zero : const Offset(0, -1),
                child:
                    _isTabBarVisible ? _buildTabbar() : const SizedBox.shrink(),
              ),
            ),
            // 使用 AnimatedContainer 让内容区域也有平滑过渡
            Flexible(
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                child: _buildTabView(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建顶部选项卡
  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            width: UiConstants.borderWidth0_5,
            color: context.templateColors.divider,
          ),
        ),
      ),
      child: TabbarWidget(
        controller: _tabController,
        tabs: _tabKeys.map((key) => TabItem(title: key)).toList(),
        labelStyle: context.templateStyle.text.bodyLargeMedium.copyWith(
          fontSize: UiConstants.fontSize18,
        ),
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: false,
        labelPadding: EdgeInsets.only(right: UiConstants.spacing14),
        dividerColor: Colors.transparent,
      ),
    );
  }

  // 构建选项卡内容
  Widget _buildTabView() {
    return TabBarView(
      controller: _tabController,
      children: [
        // 合约
        ContractPage(onScrollOffsetChanged: _handleScrollOffset),

        // 事件合约
        EventContractPage(onScrollOffsetChanged: _handleScrollOffset),
      ],
    );
  }
}
