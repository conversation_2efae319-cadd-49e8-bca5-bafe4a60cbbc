/*
*  合约界面
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/routes/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:qubic_exchange/widgets/trade_common/index.dart';
import 'widgets/export.dart';
import 'tabviews/export.dart';

class ContractPage extends StatefulWidget {
  // 滚动距离变化回调
  final Function(double scrollOffset)? onScrollOffsetChanged;

  const ContractPage({super.key, this.onScrollOffsetChanged});

  @override
  State<ContractPage> createState() => _ContractPageState();
}

class _ContractPageState extends State<ContractPage>
    with TickerProviderStateMixin {
  // 是否登录
  final bool _isLoggedIn = false;

  // 创建标签控制器
  late final TabController _tabController;

  // 创建标签内容
  static const List<String> _tabKeys = ['持仓', '委托', '跟单', '交易机器人', '资产'];

  // 创建滚动控制器
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabKeys.length, vsync: this);
    _scrollController = ScrollController();
    _scrollController.addListener(_onScrollChanged);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScrollChanged);
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  // 滚动变化监听
  void _onScrollChanged() {
    if (widget.onScrollOffsetChanged != null) {
      widget.onScrollOffsetChanged!(_scrollController.offset);
    }
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    // 模拟刷新操作
    await Future.delayed(const Duration(seconds: 2));
    // 这里可以添加实际的数据刷新逻辑
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.templateColors.surface,
      extendBody: true,
      body: SafeArea(
        bottom: false,
        child: Column(
          children: [
            // 合约交易对
            ContractPair(),

            Flexible(
              child: EasyRefresh.builder(
                header: ClassicHeader(
                  clamping: true,
                  position: IndicatorPosition.locator,
                  triggerOffset: 34,
                  processedDuration: const Duration(seconds: 1),
                  safeArea: false,
                  showMessage: false,
                  showText: false,
                  maxOverOffset: 40,
                ),
                onRefresh: _onRefresh,
                childBuilder: (context, physics) {
                  return ScrollConfiguration(
                    behavior: const ERScrollBehavior(),
                    child: ExtendedNestedScrollView(
                      controller: _scrollController,
                      physics: physics,
                      onlyOneScrollInBody: true,
                      pinnedHeaderSliverHeightBuilder: () {
                        return 53.0; // TabBar高度(63) + 顶部边框(1)
                      },
                      headerSliverBuilder: (context, innerBoxIsScrolled) {
                        return <Widget>[
                          // 刷新指示器定位器
                          const HeaderLocator.sliver(clearExtent: false),
                          // 合约头部
                          SliverToBoxAdapter(
                            child: ContractHeader(isLoggedIn: _isLoggedIn),
                          ),
                          // 吸顶 TabBar
                          SliverPersistentHeader(
                            pinned: true,
                            delegate: ContractStickyDelegate(
                              maxHeight: 53,
                              child: _buildTabbar(),
                              backgroundColor: context.templateColors.surface,
                            ),
                          ),
                        ];
                      },
                      body: _buildTabView(physics),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建底部标签栏
  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      decoration: BoxDecoration(
        color: context.templateColors.surface,
        border: Border(
          top: BorderSide(width: 1, color: context.templateColors.divider),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TabbarWidget(
              height: 53,
              controller: _tabController,
              tabs: [
                TabItem(title: '持仓'),
                TabItem(
                  title: '委托',
                  showDropdownArrow: true,
                  onDropdownTap:
                      () => {
                        OrderTypePicker.show(
                          context,
                          title: '当前委托',
                          options: [
                            OrderTypeOption(text: '全部', index: 0),
                            OrderTypeOption(text: '限价｜市价', index: 1),
                            OrderTypeOption(text: '追踪委托', index: 2),
                            OrderTypeOption(text: '止盈止损', index: 3),
                            OrderTypeOption(text: '计划委托', index: 4),
                          ],
                        ),
                      },
                ),
                TabItem(title: '跟单'),
                TabItem(title: '交易机器人'),
                TabItem(title: '资产'),
              ],
              showIndicator: false,
              labelPadding: EdgeInsets.only(right: UiConstants.spacing24),
              labelStyle: context.templateStyle.text.bodyTextMedium,
              selectedColor: context.templateColors.textPrimary,
              unselectedColor: context.templateColors.textSecondary,
            ),
          ),
          SizedBox(width: UiConstants.spacing8),
          InkWellWidget(
            onTap:
                () => {
                  // 没有登录跳转登录
                  if (!_isLoggedIn)
                    NavigationService().navigateTo(
                      AppRoutes.login,
                      arguments: {
                        'transitionType': RouteTransitionType.slideUp,
                      },
                    ),
                },
            child: ThemedImage(
              name: 'orderHistory',
              size: 24,
              followTheme: true,
            ),
          ),
        ],
      ),
    );
  }

  // 构建底部标签页
  Widget _buildTabView(ScrollPhysics physics) {
    return TabBarView(
      controller: _tabController,
      children: [
        // 持仓
        ExtendedVisibilityDetector(
          uniqueKey: const Key('Tab0'),
          child: _AutomaticKeepAlive(child: PositionsView(physics: physics)),
        ),
        // 委托
        ExtendedVisibilityDetector(
          uniqueKey: const Key('Tab1'),
          child: _AutomaticKeepAlive(child: OrdersView(physics: physics)),
        ),
        // 跟单
        ExtendedVisibilityDetector(
          uniqueKey: const Key('Tab2'),
          child: _AutomaticKeepAlive(child: CopyTradingView(physics: physics)),
        ),
        // 交易机器人
        ExtendedVisibilityDetector(
          uniqueKey: const Key('Tab3'),
          child: _AutomaticKeepAlive(child: TradingBotView(physics: physics)),
        ),
        // 资产
        ExtendedVisibilityDetector(
          uniqueKey: const Key('Tab4'),
          child: const _AutomaticKeepAlive(child: AssetsView()),
        ),
      ],
    );
  }
}

// 自动保持状态的包装器
class _AutomaticKeepAlive extends StatefulWidget {
  final Widget child;

  const _AutomaticKeepAlive({required this.child});

  @override
  State<_AutomaticKeepAlive> createState() => _AutomaticKeepAliveState();
}

class _AutomaticKeepAliveState extends State<_AutomaticKeepAlive>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => true;
}
