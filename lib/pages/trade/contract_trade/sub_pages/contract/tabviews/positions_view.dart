/*
*  持仓标签页内容
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';
import '../widgets/contract_sticky.dart';

class PositionsView extends StatefulWidget {
  final ScrollPhysics? physics;

  const PositionsView({super.key, this.physics});

  @override
  State<PositionsView> createState() => _PositionsViewState();
}

class _PositionsViewState extends State<PositionsView> {
  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: widget.physics,
      slivers: [
        // 只看当前部分 - 吸顶
        SliverPersistentHeader(
          pinned: true,
          delegate: SimpleContractStickyDelegate(
            child: _buildOnlyCurrent(),
            height: 40,
            backgroundColor: context.templateColors.surface,
          ),
        ),
        // 持仓列表
        SliverList(
          delegate: SliverChildBuilderDelegate((context, index) {
            return Container(
              height: 60,
              margin: EdgeInsets.symmetric(
                horizontal: UiConstants.spacing16,
                vertical: UiConstants.spacing4,
              ),
              padding: EdgeInsets.all(UiConstants.spacing12),
              decoration: BoxDecoration(
                color: context.templateColors.surface,
                borderRadius: BorderRadius.circular(UiConstants.borderRadius14),
              ),
              child: Center(
                child: Text(
                  '持仓项目 ${index + 1}',
                  style: context.templateStyle.text.bodyText,
                ),
              ),
            );
          }, childCount: 10),
        ),
      ],
    );
  }

  // 构建只看当前
  Widget _buildOnlyCurrent() {
    return Container(
      color: context.templateColors.surface,
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing16,
        vertical: UiConstants.spacing4,
      ),
      child: Row(
        children: [
          // 只看当前按钮
          InkWellWidget(
            onTap: () => {},
            child: Row(
              children: [
                Checkbox(value: false, onChanged: (value) => {}),
                SizedBox(width: UiConstants.spacing4),
                Text(
                  '只看当前',
                  style: context.templateStyle.text.bodyText.copyWith(
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),

          Spacer(),
          // 排序
          InkWellWidget(
            onTap: () => {},
            child: Icon(
              RemixIcons.sort_desc,
              size: 18,
              color: context.templateColors.textTertiary,
            ),
          ),
        ],
      ),
    );
  }
}
