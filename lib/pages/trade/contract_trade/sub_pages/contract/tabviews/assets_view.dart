/*
*  资产标签页内容
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/widgets/index.dart';

class AssetsView extends StatefulWidget {
  final ScrollPhysics? physics;

  const AssetsView({super.key, this.physics});

  @override
  State<AssetsView> createState() => _AssetsViewState();
}

class _AssetsViewState extends State<AssetsView> {
  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: widget.physics,
      slivers: [
        // 资产列表
        SliverFillRemaining(
          fillOverscroll: false,
          hasScrollBody: false,
          child: EmptyWidget(text: '暂无数据'),
        ),
      ],
    );
  }
}
