/*
* 合约头部
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/widgets/trade_common/index.dart';

class ContractHeader extends StatefulWidget {
  // 是否登录
  final bool isLoggedIn;

  const ContractHeader({super.key, this.isLoggedIn = false});

  @override
  State<ContractHeader> createState() => _ContractHeaderState();
}

class _ContractHeaderState extends State<ContractHeader> {
  // 当前选中的仓位操作类型 (0: 开仓, 1: 平仓)
  int _selectedPositionAction = 0;

  // 当前选中的订单类型
  OrderTypeOption? _selectedOrderType;

  // 当前选中的订单策略
  OrderStrategyOption? _selectedOrderStrategy;

  // 表单高度（仅计算当前显示的组件）
  double _formHeight = 400.0; // 设置合理的初始高度

  // 表单的 GlobalKey，用于获取实际渲染高度
  final GlobalKey _formKey = GlobalKey();

  // 订单类型列表
  final List<OrderTypeOption> _orderTypes = [
    OrderTypeOption(text: '限价单', index: 0),
    OrderTypeOption(text: '高级限价单', index: 1),
    OrderTypeOption(text: '市价单', index: 2),
    OrderTypeOption(text: '计划委托', index: 3),
    OrderTypeOption(text: '追踪委托', index: 4),
  ];

  // 订单策略列表
  final List<OrderStrategyOption> _orderStrategies = [
    OrderStrategyOption(
      text: '只做 Maker',
      description: '该委托单始终为挂单状态。若系统判断该委托单会立即与已存在的委托单成交时，则自动取消该委托单。',
      index: 0,
    ),
    OrderStrategyOption(text: 'IOC', description: '立即成交并取消未成交部分。', index: 1),
    OrderStrategyOption(text: 'FOK', description: '立即全部成交，否则自动撤单。', index: 2),
  ];

  @override
  void initState() {
    super.initState();
    // 设置默认的订单类型
    _selectedOrderType = _orderTypes.first;

    // 页面初始化完成后获取表单高度
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateFormHeight();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing10,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(flex: 5, child: _buildForm()),
          SizedBox(width: UiConstants.spacing16),
          Expanded(
            flex: 4,
            child: DepthBookView(
              height: _formHeight,
              isContract: true,
              marketType: 2, // 合约市场类型为2
              symbol: 'BTCUSDT', // 临时使用固定值，后续可以从父组件传递
            ),
          ),
        ],
      ),
    );
  }

  // 获取表单当前高度
  void _updateFormHeight() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      final renderBox =
          _formKey.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final newHeight = renderBox.size.height;
        if ((_formHeight - newHeight).abs() > 1.0) {
          // 避免微小变化导致频繁更新
          setState(() {
            _formHeight = newHeight;
          });
          debugPrint('表单高度更新: ${_formHeight.toStringAsFixed(1)}px');
        }
      } else {
        debugPrint('无法获取表单高度: RenderBox 为空');
      }
    });
  }

  // 构建左侧表单
  Widget _buildForm() {
    return NotificationListener<SizeChangedLayoutNotification>(
      onNotification: (notification) {
        // 当布局发生变化时更新高度
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _updateFormHeight();
        });
        return false;
      },
      child: SizeChangedLayoutNotifier(
        child: Container(
          key: _formKey,
          child: Column(
            children: [
              // 交易选择器
              TradeOptionsSelector(isLoggedIn: widget.isLoggedIn),

              // 仓位模式切换
              PositionActiontoggle(
                initialIndex: _selectedPositionAction,
                onChanged: (index) {
                  setState(() {
                    _selectedPositionAction = index;
                  });
                },
              ),

              // 订单类型切换
              OrderTypeToggle(
                orderTypes: _orderTypes,
                selectedOption: _selectedOrderType,
                onChanged: (option) {
                  setState(() {
                    _selectedOrderType = option;
                  });
                },
              ),

              // 订单策略切换(  订单类型为：高级限价单的时候显示 )
              if (_selectedOrderType?.index == 1)
                OrderStrategyOptionsToggle(
                  options: _orderStrategies,
                  selectedOption: _selectedOrderStrategy,
                  onChanged: (strategy) {
                    setState(() {
                      _selectedOrderStrategy = strategy;
                    });
                  },
                ),

              // 市价成交 （  订单类型为：市价单的时候显示  ）
              if (_selectedOrderType?.index == 2) MarketOrder(),

              // 价格输入 (  订单类型为：市价单 or 计划委托 or 追踪委托的时候隐藏 )
              if (_selectedOrderType?.index != 2 &&
                  _selectedOrderType?.index != 3 &&
                  _selectedOrderType?.index != 4)
                PriceInput(showIncrementButtons: true),

              // 触发价格 & 执行价格 (  订单类型为：计划委托的时候显示 )
              if (_selectedOrderType?.index == 3) ...[
                // 触发价格
                TriggerPrice(),

                // 执行价格
                ExecutionPrice(),
              ],

              // 触发价格 & 回调幅度 (   订单类型为：追踪委托的时候显示  )
              if (_selectedOrderType?.index == 4) ...[
                // 触发价格
                TriggerPrice(),

                // 回调幅度
                CallbackAmount(),
              ],

              // 最高价 & 最低价 (  订单类型为：分段委托的时候显示  )
              if (_selectedOrderType?.index == 5) ...[
                // 最高价
                HighestPrice(),

                // 最低价
                LowestPrice(),

                // 总数量
                TotalQuantity(),

                // 数量分配
                QuantityAllocation(),

                // 平均委托价格
                AverageOrderPrice(),
              ],

              // 数量输入
              QuantityInput(),

              if (_selectedPositionAction != 1)
                // 滑块
                LeverageSelector(),

              // 根据选中的仓位操作类型显示不同内容
              Builder(
                builder: (context) {
                  return _selectedPositionAction == 0
                      ? _buildOpenPosition()
                      : _buildClosePosition();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建开仓内容
  Widget _buildOpenPosition() {
    return SizedBox(
      child: Column(
        children: [
          // 止盈止损
          TakeProfitStopLoss(isContract: true),

          // 可用
          AvailableInfo(),

          // 可开多
          OpenCloseInfo(),

          // 开多按钮
          CommonButton.buy(
            '登录',
            size: CommonButtonSize.medium,
            width: double.infinity,
            onPressed: () {},
          ),

          // 可开空
          OpenCloseInfo(),

          // 开空按钮
          CommonButton.sell(
            '登录',
            size: CommonButtonSize.medium,
            width: double.infinity,
            onPressed: () {},
          ),
        ],
      ),
    );
  }

  // 构建平仓内容
  Widget _buildClosePosition() {
    return SizedBox(
      child: Column(
        children: [
          // 可平多
          OpenCloseInfo(text: '可平'),

          // 平多按钮
          CommonButton.buy(
            '登录',
            size: CommonButtonSize.medium,
            width: double.infinity,
            onPressed: () {},
          ),

          // 可平空
          OpenCloseInfo(text: '可平'),

          // 平空按钮
          CommonButton.sell(
            '登录',
            size: CommonButtonSize.medium,
            width: double.infinity,
            onPressed: () {},
          ),
        ],
      ),
    );
  }
}
