/*
* 合约交易对
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class ContractPair extends StatefulWidget {
  const ContractPair({super.key});

  @override
  State<ContractPair> createState() => _ContractPairState();
}

class _ContractPairState extends State<ContractPair> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 通知
        _buildNotice(),

        // 合约交易对
        _buildPair(),
      ],
    );
  }

  // 构建通知信息
  Widget _buildNotice() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing4,
      ),
      child: Row(
        children: [
          ThemedImage(name: '', size: 14),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
            child: Text(
              '2025 KCGI西甲荣耀！争夺600万美金终极奖池！',
              style: context.templateStyle.text.bodyText,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Spacer(),
          InkWellWidget(child: Icon(RemixIcons.close_line)),
        ],
      ),
    );
  }

  // 构建交易对信息
  Widget _buildPair() {
    return Container(
      padding: EdgeInsets.only(
        left: UiConstants.spacing18,
        right: UiConstants.spacing18,
        bottom: UiConstants.spacing4,
      ),
      child: Row(
        children: [
          InkWellWidget(
            onTap:
                () => {
                  // TODO 打开交易对选择弹窗
                },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 交易对名称
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text('BTCUSDT', style: context.templateStyle.text.h3),
                    Padding(
                      padding: EdgeInsets.only(left: UiConstants.spacing4),
                      child: ThemedImage(
                        name: 'arrow_triangle_down',
                        size: 16,
                        followTheme: true,
                      ),
                    ),
                  ],
                ),

                // 标签 & 涨跌幅
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      '永续',
                      style: context.templateStyle.text.descriptionTextMedium
                          .copyWith(
                            color: context.templateColors.textSecondary,
                          ),
                    ),
                    SizedBox(width: UiConstants.spacing8),
                    Text(
                      '+0.50%',
                      style: context.templateStyle.text.descriptionTextMedium
                          .copyWith(color: context.templateColors.tradeBuy),
                    ),
                  ],
                ),
              ],
            ),
          ),

          Spacer(),
          Row(
            children: [
              // k线图按钮
              InkWellWidget(
                onTap:
                    () => {
                      // TODO 跳转至k线图界面
                    },
                child: ThemedImage(
                  name: 'strategy_kline',
                  size: 24,
                  followTheme: true,
                ),
              ),
              InkWellWidget(
                onTap:
                    () => {
                      // TODO 显示更多操作菜单
                    },
                child: Padding(
                  padding: EdgeInsets.only(left: UiConstants.spacing16),
                  child: ThemedImage(name: 'more', size: 24, followTheme: true),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
