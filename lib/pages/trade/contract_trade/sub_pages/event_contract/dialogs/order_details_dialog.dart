/*
*  订单详情底部弹窗
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:remixicon/remixicon.dart';

class OrderDetailsDialog {
  // 显示订单详情底部弹窗
  static Future<void> show(
    BuildContext context, {
    required String orderId,
    required String orderType,
    required String orderStatus,
    required String orderTime,
    required String orderPrice,
    required String orderQuantity,
    required String orderAmount,
    required String orderFee,
    required String orderSide,
    required String orderSymbol,
    required String orderMargin,
  }) {
    return showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) {
        return _OrderDetailsContent(
          orderId: orderId,
          orderType: orderType,
          orderStatus: orderStatus,
          orderTime: orderTime,
          orderPrice: orderPrice,
          orderQuantity: orderQuantity,
          orderAmount: orderAmount,
          orderFee: orderFee,
          orderSide: orderSide,
          orderSymbol: orderSymbol,
          orderMargin: orderMargin,
        );
      },
    );
  }
}

class _OrderDetailsContent extends StatelessWidget {
  final String orderId;
  final String orderType;
  final String orderStatus;
  final String orderTime;
  final String orderPrice;
  final String orderQuantity;
  final String orderAmount;
  final String orderFee;
  final String orderSide;
  final String orderSymbol;
  final String orderMargin;
  const _OrderDetailsContent({
    required this.orderId,
    required this.orderType,
    required this.orderStatus,
    required this.orderTime,
    required this.orderPrice,
    required this.orderQuantity,
    required this.orderAmount,
    required this.orderFee,
    required this.orderSide,
    required this.orderSymbol,
    required this.orderMargin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: UiConstants.spacing16,
        horizontal: UiConstants.spacing18,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题
          Row(
            children: [
              Container(
                width: UiConstants.spacing20,
                height: UiConstants.spacing20,
                decoration: BoxDecoration(
                  color: context.templateColors.tradeBuy,
                  borderRadius: BorderRadius.circular(
                    context.templateStyles.borderRadiusSmall,
                  ),
                ),
                child: Icon(
                  RemixIcons.arrow_right_up_line,
                  size: UiConstants.fontSize16,
                  color: context.templateColors.white,
                ),
              ),
            ],
          ),
          SizedBox(height: UiConstants.spacing24),

          // 订单信息
          _buildOrderInfo(context),
        ],
      ),
    );
  }

  // 构建订单信息
  Widget _buildOrderInfo(BuildContext context) {
    // 构建订单项
    Widget buildOrderItem(String label, String value) {
      return Padding(
        padding: EdgeInsets.symmetric(vertical: UiConstants.spacing8),
        child: Row(
          children: [
            Text(label, style: context.templateStyle.text.descriptionText),
            Spacer(),
            Text(value, style: context.templateStyle.text.bodyTextMedium),
          ],
        ),
      );
    }

    return Column(
      children: [
        // 订单ID
        buildOrderItem('订单ID', orderId),
        // 订单类型
        buildOrderItem('订单类型', orderType),
        // 订单状态
        buildOrderItem('订单状态', orderStatus),
        // 订单时间
        buildOrderItem('订单时间', orderTime),
        // 订单价格
        buildOrderItem('订单价格', orderPrice),
        // 订单数量
        buildOrderItem('订单数量', orderQuantity),
        // 订单金额
        buildOrderItem('订单金额', orderAmount),
        // 订单手续费
        buildOrderItem('订单手续费', orderFee),
        // 订单方向
        buildOrderItem('订单方向', orderSide),
        // 订单交易对
        buildOrderItem('订单交易对', orderSymbol),
        // 订单保证金
        buildOrderItem('订单保证金', orderMargin),
      ],
    );
  }
}
