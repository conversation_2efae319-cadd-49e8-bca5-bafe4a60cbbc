/*
*  事件合约头部
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class EventContractHeader extends StatefulWidget {
  const EventContractHeader({super.key});

  @override
  State<EventContractHeader> createState() => _EventContractHeaderState();
}

class _EventContractHeaderState extends State<EventContractHeader>
    with TickerProviderStateMixin {
  // 构建时间周期控制器
  late TabController _timeFrameController;
  // 构建时间周期选项
  final List<String> _timeFrames = [
    '1分',
    '5分',
    '15分',
    '30分',
    '1小时',
    '4小时',
    '日线',
  ];

  // 构建时间单位控制器
  late TabController _timeUnitController;
  // 构建时间单位选项
  final List<String> _timeUnits = ['10m', '30m', '1h', '5h', '1d'];

  // 数量输入控制器
  final TextEditingController _quantityController = TextEditingController();

  // 初始化
  @override
  void initState() {
    super.initState();
    _timeFrameController = TabController(
      length: _timeFrames.length,
      vsync: this,
    );
    _timeUnitController = TabController(length: _timeUnits.length, vsync: this);
  }

  // 销毁
  @override
  void dispose() {
    _quantityController.dispose();
    _timeFrameController.dispose();
    _timeUnitController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing10),
      child: Column(
        children: [
          // 图表区域
          _buildChartArea(),

          // 数量输入
          _buildQuantityInput(),

          // 沉底内容
          _buildBottomContent(),
        ],
      ),
    );
  }

  // 构建图表区域
  Widget _buildChartArea() {
    /// 构建周期切换
    Widget buildCycleSwitch() {
      final textStyle = context.templateStyle.text.descriptionTextMedium
          .copyWith(fontSize: 13, color: context.templateColors.textTertiary);
      return Container(
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing18,
          vertical: UiConstants.spacing4,
        ),
        decoration: BoxDecoration(
          border: Border(
            top: BorderSide(width: 0.5, color: context.templateColors.divider),
            bottom: BorderSide(
              width: 0.5,
              color: context.templateColors.divider,
            ),
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: TabBar(
                controller: _timeFrameController,
                tabs:
                    _timeFrames
                        .asMap()
                        .entries
                        .map((entry) => Tab(height: 28, text: entry.value))
                        .toList(),
                labelStyle: textStyle.copyWith(
                  color: context.templateColors.textPrimary,
                ),
                unselectedLabelStyle: textStyle,
                overlayColor: WidgetStateProperty.all(Colors.transparent),
                dividerHeight: 0,
                indicatorColor: Colors.transparent,
                labelPadding: EdgeInsets.only(right: UiConstants.spacing16),
                padding: EdgeInsets.zero,
                isScrollable: true,
                tabAlignment: TabAlignment.start,
              ),
            ),
            Text('指数价格', style: context.templateStyle.text.hintText),
          ],
        ),
      );
    }

    /// 构建时间单位
    Widget buildTimeUnit() {
      final textStyle = context.templateStyle.text.descriptionTextMedium
          .copyWith(fontSize: 13);
      return Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing18,
          vertical: UiConstants.spacing10,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(bottom: UiConstants.spacing10),
              child: Text(
                '时间单位',
                style: context.templateStyle.text.descriptionText.copyWith(
                  color: context.templateColors.textTertiary,
                  decorationStyle: TextDecorationStyle.dashed,
                  decoration: TextDecoration.underline,
                  decorationColor: context.templateColors.textTertiary,
                ),
              ),
            ),
            LayoutBuilder(
              builder:
                  (builder, size) => TabBar(
                    controller: _timeUnitController,
                    tabs:
                        _timeUnits
                            .asMap()
                            .entries
                            .map(
                              (entry) => AnimatedBuilder(
                                animation: _timeUnitController,
                                builder: (context, child) {
                                  final isSelected =
                                      _timeUnitController.index == entry.key;
                                  return Container(
                                    width:
                                        size.maxWidth / 4 -
                                        UiConstants.spacing8 * 3,
                                    height: 35,
                                    padding: EdgeInsets.symmetric(
                                      horizontal: UiConstants.spacing12,
                                    ),
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                        width: isSelected ? 1.5 : 0.5,
                                        color:
                                            isSelected
                                                ? context
                                                    .templateColors
                                                    .textPrimary
                                                : context
                                                    .templateColors
                                                    .divider,
                                      ),
                                      borderRadius: BorderRadius.circular(
                                        UiConstants.borderRadius8,
                                      ),
                                    ),
                                    child: Tab(text: entry.value),
                                  );
                                },
                              ),
                            )
                            .toList(),
                    labelStyle: textStyle.copyWith(
                      color: context.templateColors.textPrimary,
                    ),
                    unselectedLabelStyle: textStyle,
                    overlayColor: WidgetStateProperty.all(Colors.transparent),
                    dividerHeight: 0,
                    indicatorColor: Colors.transparent,
                    labelPadding: EdgeInsets.only(right: UiConstants.spacing16),
                    padding: EdgeInsets.zero,
                    isScrollable: true,
                    tabAlignment: TabAlignment.start,
                  ),
            ),
          ],
        ),
      );
    }

    /// 构建 K线区域
    Widget buildKLine() {
      return Container(
        height: 260,
        decoration: BoxDecoration(color: context.templateColors.surface),
      );
    }

    return Column(
      children: [
        // 时间周期切换
        buildCycleSwitch(),

        // K线区域
        buildKLine(),

        // 时间单位
        buildTimeUnit(),
      ],
    );
  }

  // 构建数量输入
  Widget _buildQuantityInput() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(),
            child: Text(
              '数量(USDT)',
              style: context.templateStyle.text.descriptionText.copyWith(
                color: context.templateColors.textTertiary,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: UiConstants.spacing10),
            child: TextFieldWidget(
              controller: _quantityController,
              height: 48,
              cursorHeight: 12,
              contentPadding: EdgeInsets.only(
                left: UiConstants.spacing10,
                top: UiConstants.spacing4,
                bottom: UiConstants.spacing4,
              ),
              padding: EdgeInsets.zero,
              radius: BorderRadius.circular(UiConstants.borderRadius12),
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              hintText: '',
              suffixIcon: Padding(
                padding: EdgeInsets.only(right: UiConstants.spacing10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    InkWellWidget(
                      child: Icon(
                        RemixIcons.subtract_line,
                        size: 20,
                        color: context.templateColors.textTertiary,
                      ),
                    ),

                    SizedBox(width: UiConstants.spacing10),

                    InkWellWidget(
                      child: Icon(
                        RemixIcons.add_line,
                        size: 20,
                        color: context.templateColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              suffixIconConstraints: BoxConstraints(maxWidth: 80),
            ),
          ),
        ],
      ),
    );
  }

  // 构建沉底内容
  Widget _buildBottomContent() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: Column(
        children: [
          SizedBox(height: UiConstants.spacing10),
          // 可用
          Row(
            children: [
              Text(
                '可用',
                style: context.templateStyle.text.descriptionText.copyWith(
                  color: context.templateColors.textTertiary,
                ),
              ),
              Spacer(),
              InkWellWidget(
                child: Row(
                  children: [
                    Text(
                      '2342.46 USDT',
                      style: context.templateStyle.text.bodyText,
                    ),
                    SizedBox(width: UiConstants.spacing4),
                    Icon(
                      RemixIcons.repeat_line,
                      size: 14,
                      color: context.templateColors.primary,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: UiConstants.spacing14),
          // 支付率
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Text(
                      '支付率',
                      style: context.templateStyle.text.descriptionText
                          .copyWith(color: context.templateColors.textTertiary),
                    ),
                    Spacer(),
                    Text(
                      '100%',
                      style: context.templateStyle.text.descriptionText,
                    ),
                  ],
                ),
              ),
              SizedBox(width: UiConstants.spacing8),
              Expanded(
                child: Row(
                  children: [
                    Text(
                      '支付率',
                      style: context.templateStyle.text.descriptionText
                          .copyWith(color: context.templateColors.textTertiary),
                    ),
                    Spacer(),
                    Text(
                      '100%',
                      style: context.templateStyle.text.descriptionText,
                    ),
                  ],
                ),
              ),
            ],
          ),
          // 支付金额
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Text(
                      '支付金额',
                      style: context.templateStyle.text.descriptionText
                          .copyWith(
                            color: context.templateColors.textTertiary,
                            decorationStyle: TextDecorationStyle.dashed,
                            decoration: TextDecoration.underline,
                            decorationColor:
                                context.templateColors.textTertiary,
                          ),
                    ),
                    Spacer(),
                    Text('0 USDT', style: context.templateStyle.text.bodyText),
                  ],
                ),
              ),
              SizedBox(width: UiConstants.spacing8),
              Expanded(
                child: Row(
                  children: [
                    Text(
                      '支付金额',
                      style: context.templateStyle.text.descriptionText
                          .copyWith(
                            color: context.templateColors.textTertiary,
                            decorationStyle: TextDecorationStyle.dashed,
                            decoration: TextDecoration.underline,
                            decorationColor:
                                context.templateColors.textTertiary,
                          ),
                    ),
                    Spacer(),
                    Text('0 USDT', style: context.templateStyle.text.bodyText),
                  ],
                ),
              ),
            ],
          ),

          // 上涨 & 下跌
          SizedBox(height: UiConstants.spacing10),
          Row(
            children: [
              Expanded(
                child: CommonButton.buy(
                  '上涨',
                  size: CommonButtonSize.medium,
                  width: double.infinity,
                  onPressed: () {},
                ),
              ),
              SizedBox(width: UiConstants.spacing8),
              Expanded(
                child: CommonButton.sell(
                  '下跌',
                  size: CommonButtonSize.medium,
                  width: double.infinity,
                  onPressed: () {},
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
