/*
*  事件合约交易对
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class EventContractPair extends StatefulWidget {
  const EventContractPair({super.key});

  @override
  State<EventContractPair> createState() => _EventContractPairState();
}

class _EventContractPairState extends State<EventContractPair> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing16,
      ),
      child: Row(
        children: [
          Text('ETHUSDT', style: context.templateStyle.text.h3),
          Padding(
            padding: EdgeInsets.only(left: UiConstants.spacing4),
            child: ThemedImage(
              name: 'arrow_triangle_down',
              size: 14,
              followTheme: true,
            ),
          ),
          Spacer(),
          Text(
            '上涨 80%',
            style: context.templateStyle.text.h5.copyWith(
              color: context.templateColors.tradeBuy,
              fontWeight: UiConstants.fontWeightMedium,
            ),
          ),
          SizedBox(width: UiConstants.spacing16),
          Text(
            '下跌 45.5%',
            style: context.templateStyle.text.h5.copyWith(
              color: context.templateColors.tradeSell,
              fontWeight: UiConstants.fontWeightMedium,
            ),
          ),
        ],
      ),
    );
  }
}
