/*
*  事件合约
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'widgets/export_widgets.dart';
import 'tabviews/opened_position_tab.dart';
import 'tabviews/closed_position_tab.dart';
import 'models/index.dart';

class EventContractPage extends StatefulWidget {
  // 滚动距离变化回调
  final Function(double scrollOffset)? onScrollOffsetChanged;

  const EventContractPage({super.key, this.onScrollOffsetChanged});

  @override
  State<EventContractPage> createState() => _EventContractPageState();
}

class _EventContractPageState extends State<EventContractPage>
    with TickerProviderStateMixin {
  // 创建标签控制器
  late final TabController _mainController;

  // 创建滚动控制器
  late final ScrollController _scrollController;

  // 仓位数据
  late List<EventContractPosition> _openedPositions;
  late List<EventContractPosition> _closedPositions;

  // 创建标签内容
  List<TabItem> get _mainKeys => [
    TabItem(title: '已开仓(${_openedPositions.length})'),
    TabItem(title: '已平仓(${_closedPositions.length})'),
  ];

  // 初始化
  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScrollChanged);
    _mainController = TabController(length: 2, vsync: this);
    _openedPositions = [];
    _closedPositions = [];
    _loadInitialData();
  }

  // 滚动变化监听
  void _onScrollChanged() {
    if (widget.onScrollOffsetChanged != null) {
      widget.onScrollOffsetChanged!(_scrollController.offset);
    }
  }

  /// 加载初始数据
  Future<void> _loadInitialData() async {
    await _loadPositionsData();
  }

  /// 加载仓位数据
  Future<void> _loadPositionsData() async {
    try {
      // 模拟API调用延迟
      await Future.delayed(const Duration(milliseconds: 500));

      // 模拟从API获取数据
      final openedData = await _fetchOpenedPositions();
      final closedData = await _fetchClosedPositions();

      setState(() {
        _openedPositions = openedData;
        _closedPositions = closedData;
      });
    } catch (e) {
      // 错误处理 - 在实际项目中应该使用日志框架
      debugPrint('加载仓位数据失败: $e');
    }
  }

  /// 模拟获取已开仓数据
  Future<List<EventContractPosition>> _fetchOpenedPositions() async {
    return [];
  }

  /// 模拟获取已平仓数据
  Future<List<EventContractPosition>> _fetchClosedPositions() async {
    return [];
  }

  // 销毁
  @override
  void dispose() {
    _scrollController.removeListener(_onScrollChanged);
    _scrollController.dispose();
    _mainController.dispose();
    super.dispose();
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    await _loadPositionsData();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 事件合约交易对
        EventContractPair(),

        // 事件合约滚动内容
        Expanded(
          child: EasyRefresh.builder(
            header: ClassicHeader(
              clamping: true,
              position: IndicatorPosition.locator,
              triggerOffset: 34,
              processedDuration: const Duration(seconds: 1),
              safeArea: false,
              showMessage: false,
              showText: false,
              maxOverOffset: 40,
            ),
            onRefresh: _onRefresh,
            childBuilder: (context, physics) {
              return ScrollConfiguration(
                behavior: const ERScrollBehavior(),
                child: ExtendedNestedScrollView(
                  controller: _scrollController,
                  physics: physics,
                  onlyOneScrollInBody: true,
                  pinnedHeaderSliverHeightBuilder: () {
                    return 42.0;
                  },
                  headerSliverBuilder: (context, innerBoxIsScrolled) {
                    return <Widget>[
                      // 刷新指示器定位器
                      const HeaderLocator.sliver(clearExtent: false),
                      // 合约头部
                      SliverToBoxAdapter(child: EventContractHeader()),
                      // 吸顶 TabBar
                      SliverPersistentHeader(
                        pinned: true,
                        delegate: StickyDelegate(
                          maxHeight: 42,
                          child: _buildMainTabbar(),
                          backgroundColor: context.templateColors.surface,
                        ),
                      ),
                    ];
                  },
                  body: _buildMainTabView(physics),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  // 构建主标签栏
  Widget _buildMainTabbar() {
    return Container(
      height: 42,
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(width: 0.5, color: context.templateColors.divider),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TabbarWidget(
              height: 42,
              tabs: _mainKeys,
              controller: _mainController,
              labelStyle: context.templateStyle.text.bodyTextMedium,
              selectedColor: context.templateColors.textPrimary,
              unselectedColor: context.templateColors.textSecondary,
              showIndicator: true,
              indicatorSize: TabBarIndicatorSize.label,
              labelPadding: EdgeInsets.only(right: UiConstants.spacing18),
            ),
          ),
          SizedBox(width: UiConstants.spacing10),

          InkWellWidget(
            child: ThemedImage(
              name: 'orderHistory',
              size: 24,
              followTheme: true,
            ),
          ),
        ],
      ),
    );
  }

  // 构建主标签内容
  Widget _buildMainTabView(ScrollPhysics physics) {
    return TabBarView(
      controller: _mainController,
      children: [
        // 已开仓
        _AutomaticKeepAlive(
          child: OpenedPositionTab(
            key: ValueKey('opened_${_openedPositions.length}'),
            physics: physics,
            positions: _openedPositions,
          ),
        ),

        // 已平仓
        _AutomaticKeepAlive(
          child: ClosedPositionTab(
            key: ValueKey('closed_${_closedPositions.length}'),
            physics: physics,
            positions: _closedPositions,
          ),
        ),
      ],
    );
  }
}

// 自动保持状态的包装器
class _AutomaticKeepAlive extends StatefulWidget {
  final Widget child;

  const _AutomaticKeepAlive({required this.child});

  @override
  State<_AutomaticKeepAlive> createState() => _AutomaticKeepAliveState();
}

class _AutomaticKeepAliveState extends State<_AutomaticKeepAlive>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => true;
}
