/*
*  已平仓标签页内容
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/utils/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';
import '../models/index.dart';

class ClosedPositionTab extends StatefulWidget {
  final ScrollPhysics? physics;
  final List<EventContractPosition>? positions;

  const ClosedPositionTab({super.key, this.physics, this.positions});

  @override
  State<ClosedPositionTab> createState() => _ClosedPositionTabState();
}

class _ClosedPositionTabState extends State<ClosedPositionTab> {
  // 仓位数据列表
  late List<EventContractPosition> _positions;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void didUpdateWidget(ClosedPositionTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当外部传入的数据发生变化时，更新内部状态
    if (widget.positions != oldWidget.positions) {
      _initializeData();
    }
  }

  /// 初始化数据
  void _initializeData() {
    // 使用外部传入的数据，如果没有则为空列表
    _positions = widget.positions != null ? List.from(widget.positions!) : [];
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: widget.physics,
      slivers: [
        // 空状态
        if (_positions.isEmpty)
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 100),
              child: EmptyWidget(text: '暂无已平仓记录'),
            ),
          ),
        // 已平仓列表
        if (_positions.isNotEmpty)
          SliverList(
            delegate: SliverChildBuilderDelegate((context, index) {
              return _buildClosedPositionItem(_positions[index]);
            }, childCount: _positions.length),
          ),

        // 底部安全区域
        SliverToBoxAdapter(
          child: SizedBox(height: ScreenUtil.screenHeight(context) * 0.1),
        ),
      ],
    );
  }

  // 构建已平仓项
  Widget _buildClosedPositionItem(EventContractPosition position) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing14,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(width: 0.5, color: context.templateColors.divider),
        ),
      ),
      child: Column(
        children: [
          // 交易对 & 盈利/亏损
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 18,
                height: 18,
                margin: EdgeInsets.only(right: UiConstants.spacing8),
                decoration: BoxDecoration(
                  color:
                      position.isUpDirection
                          ? context.templateColors.tradeBuy
                          : context.templateColors.tradeSell,
                  borderRadius: BorderRadius.circular(
                    UiConstants.borderRadius4,
                  ),
                ),
                child: Icon(
                  position.isUpDirection
                      ? RemixIcons.arrow_right_up_line
                      : RemixIcons.arrow_right_down_line,
                  size: 18,
                  color: Colors.white,
                ),
              ),
              Text(
                position.symbol,
                style: context.templateStyle.text.bodyLargeMedium,
              ),
              Spacer(),
              Text(
                '${position.formattedProfitLoss} USDT',
                style: context.templateStyle.text.h4.copyWith(
                  color:
                      position.isProfitable
                          ? context.templateColors.tradeBuy
                          : context.templateColors.tradeSell,
                  fontWeight: UiConstants.fontWeightMedium,
                ),
              ),
            ],
          ),
          SizedBox(height: UiConstants.spacing12),
          // 数量 & 开仓价 & 开仓时间
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '数量(USDT)',
                      style: context.templateStyle.text.hintText,
                    ),
                    Text(
                      position.formattedQuantity,
                      style: context.templateStyle.text.bodyText,
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('开仓价', style: context.templateStyle.text.hintText),
                    Text(
                      position.formattedOpenPrice,
                      style: context.templateStyle.text.bodyText,
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text('开仓时间', style: context.templateStyle.text.hintText),
                    Text(
                      position.formattedOpenTime,
                      style: context.templateStyle.text.bodyText,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: UiConstants.spacing12),

          // 奖金支付率 & 平仓价 & 平仓时间
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('奖金支付率', style: context.templateStyle.text.hintText),
                    Text(
                      position.formattedBonusPayoutRate,
                      style: context.templateStyle.text.bodyText,
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('平仓价', style: context.templateStyle.text.hintText),
                    Text(
                      position.formattedClosePrice,
                      style: context.templateStyle.text.bodyText,
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text('平仓时间', style: context.templateStyle.text.hintText),
                    Text(
                      position.formattedCloseTime,
                      style: context.templateStyle.text.bodyText,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
