/*
* === 事件合约历史订单 ===
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';

class HistoryOrdersPage extends StatefulWidget {
  const HistoryOrdersPage({super.key});

  @override
  State<HistoryOrdersPage> createState() => _HistoryOrdersPageState();
}

class _HistoryOrdersPageState extends State<HistoryOrdersPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.templateColors.surface,
      body: SafeArea(
        child: Column(
          children: [
            // 总盈亏
            _buildTotalProfit(),

            // 主标签栏
            _buildMainTabbar(),

            // 主标签页内容
            _buildMainTabView(),
          ],
        ),
      ),
    );
  }

  // 构建总盈亏
  Widget _buildTotalProfit() {
    return Container(
      padding: EdgeInsets.all(context.templateStyles.spacingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('总盈亏', style: context.templateStyle.text.descriptionText),
          SizedBox(height: UiConstants.spacing10),
          Text.rich(
            TextSpan(children: [TextSpan(text: '10'), TextSpan(text: 'USDT')]),
          ),
        ],
      ),
    );
  }

  // 构建主标签栏
  Widget _buildMainTabbar() {
    return Container();
  }

  // 构建主标签页内容
  Widget _buildMainTabView() {
    return Container();
  }
}
