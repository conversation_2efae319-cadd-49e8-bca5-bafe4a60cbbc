/*
*  事件合约仓位数据模型
*  
*  功能：
*  - 定义事件合约已开仓位的数据结构
*  - 包含仓位的所有必要信息
*  - 支持序列化和反序列化
*  - 提供数据验证和便捷方法
*/

/// 事件合约仓位方向枚举
enum EventContractDirection {
  /// 上涨
  up,

  /// 下跌
  down,
}

/// 事件合约仓位状态枚举
enum EventContractPositionStatus {
  /// 进行中
  active,

  /// 已结算
  settled,

  /// 已取消
  cancelled,
}

/// 事件合约仓位数据模型
class EventContractPosition {
  /// 仓位ID
  final String positionId;

  /// 交易对符号（如：ETHUSDT）
  final String symbol;

  /// 基础货币（如：ETH）
  final String baseCurrency;

  /// 计价货币（如：USDT）
  final String quoteCurrency;

  /// 仓位方向（上涨/下跌）
  final EventContractDirection direction;

  /// 仓位状态
  final EventContractPositionStatus status;

  /// 开仓价格
  final double openPrice;

  /// 指数价格
  final double indexPrice;

  /// 当前价格
  final double currentPrice;

  /// 平仓价格（如果已平仓）
  final double? closePrice;

  /// 仓位数量（USDT）
  final double quantity;

  /// 奖金支付率（百分比）
  final double bonusPayoutRate;

  /// 奖金金额（USDT）
  final double bonusAmount;

  /// 开仓时间
  final DateTime openTime;

  /// 结算时间（如果已结算）
  final DateTime? settlementTime;

  /// 剩余时间（秒）
  final int remainingTimeSeconds;

  /// 总时长（秒）
  final int totalDurationSeconds;

  /// 创建时间
  final DateTime createdAt;

  /// 更新时间
  final DateTime updatedAt;

  EventContractPosition({
    required this.positionId,
    required this.symbol,
    required this.baseCurrency,
    required this.quoteCurrency,
    required this.direction,
    required this.status,
    required this.openPrice,
    required this.indexPrice,
    required this.currentPrice,
    this.closePrice,
    required this.quantity,
    required this.bonusPayoutRate,
    required this.bonusAmount,
    required this.openTime,
    this.settlementTime,
    required this.remainingTimeSeconds,
    required this.totalDurationSeconds,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now() {
    // 数据验证
    _validateData();
  }

  /// 数据验证
  void _validateData() {
    if (positionId.isEmpty) throw ArgumentError('仓位ID不能为空');
    if (symbol.isEmpty) throw ArgumentError('交易对符号不能为空');
    if (baseCurrency.isEmpty) throw ArgumentError('基础货币不能为空');
    if (quoteCurrency.isEmpty) throw ArgumentError('计价货币不能为空');
    if (openPrice <= 0) throw ArgumentError('开仓价格必须大于0');
    if (indexPrice <= 0) throw ArgumentError('指数价格必须大于0');
    if (currentPrice <= 0) throw ArgumentError('当前价格必须大于0');
    if (quantity <= 0) throw ArgumentError('仓位数量必须大于0');
    if (bonusPayoutRate < 0 || bonusPayoutRate > 100) {
      throw ArgumentError('奖金支付率必须在0-100之间');
    }
    if (bonusAmount < 0) throw ArgumentError('奖金金额不能为负数');
    if (remainingTimeSeconds < 0) throw ArgumentError('剩余时间不能为负数');
    if (totalDurationSeconds <= 0) throw ArgumentError('总时长必须大于0');
  }

  /// 是否为上涨方向
  bool get isUpDirection => direction == EventContractDirection.up;

  /// 是否为下跌方向
  bool get isDownDirection => direction == EventContractDirection.down;

  /// 是否为活跃状态
  bool get isActive => status == EventContractPositionStatus.active;

  /// 是否已结算
  bool get isSettled => status == EventContractPositionStatus.settled;

  /// 是否已取消
  bool get isCancelled => status == EventContractPositionStatus.cancelled;

  /// 获取完整交易对名称
  String get fullSymbol => symbol;

  /// 获取方向显示文本
  String get directionText => isUpDirection ? '上涨' : '下跌';

  /// 获取状态显示文本
  String get statusText {
    switch (status) {
      case EventContractPositionStatus.active:
        return '进行中';
      case EventContractPositionStatus.settled:
        return '已结算';
      case EventContractPositionStatus.cancelled:
        return '已取消';
    }
  }

  /// 获取格式化的开仓价格
  String get formattedOpenPrice => openPrice.toStringAsFixed(2);

  /// 获取格式化的指数价格
  String get formattedIndexPrice => indexPrice.toStringAsFixed(2);

  /// 获取格式化的当前价格
  String get formattedCurrentPrice => currentPrice.toStringAsFixed(2);

  /// 获取格式化的平仓价格
  String get formattedClosePrice => closePrice?.toStringAsFixed(2) ?? '--';

  /// 获取格式化的数量
  String get formattedQuantity => quantity.toStringAsFixed(0);

  /// 获取格式化的奖金支付率
  String get formattedBonusPayoutRate =>
      '${bonusPayoutRate.toStringAsFixed(0)}%';

  /// 获取格式化的奖金金额
  String get formattedBonusAmount => bonusAmount.toStringAsFixed(2);

  /// 获取格式化的开仓时间
  String get formattedOpenTime {
    return '${openTime.year.toString().substring(2)}-'
        '${openTime.month.toString().padLeft(2, '0')}-'
        '${openTime.day.toString().padLeft(2, '0')} '
        '${openTime.hour.toString().padLeft(2, '0')}:'
        '${openTime.minute.toString().padLeft(2, '0')}:'
        '${openTime.second.toString().padLeft(2, '0')}';
  }

  /// 获取格式化的平仓时间
  String get formattedCloseTime {
    if (settlementTime == null) return '--';
    return '${settlementTime!.year.toString().substring(2)}-'
        '${settlementTime!.month.toString().padLeft(2, '0')}-'
        '${settlementTime!.day.toString().padLeft(2, '0')} '
        '${settlementTime!.hour.toString().padLeft(2, '0')}:'
        '${settlementTime!.minute.toString().padLeft(2, '0')}:'
        '${settlementTime!.second.toString().padLeft(2, '0')}';
  }

  /// 获取格式化的剩余时间
  String get formattedRemainingTime {
    final hours = remainingTimeSeconds ~/ 3600;
    final minutes = (remainingTimeSeconds % 3600) ~/ 60;
    final seconds = remainingTimeSeconds % 60;
    return '${hours.toString().padLeft(2, '0')}:'
        '${minutes.toString().padLeft(2, '0')}:'
        '${seconds.toString().padLeft(2, '0')}';
  }

  /// 获取剩余时间进度（0.0 - 1.0）
  double get remainingTimeProgress {
    if (totalDurationSeconds <= 0) return 0.0;
    return (totalDurationSeconds - remainingTimeSeconds) / totalDurationSeconds;
  }

  /// 计算盈亏金额
  double get profitLoss {
    if (isUpDirection) {
      return currentPrice > openPrice ? bonusAmount : -quantity;
    } else {
      return currentPrice < openPrice ? bonusAmount : -quantity;
    }
  }

  /// 计算盈亏率
  double get profitLossRate {
    if (quantity <= 0) return 0.0;
    return (profitLoss / quantity) * 100;
  }

  /// 是否盈利
  bool get isProfitable => profitLoss > 0;

  /// 获取格式化的盈亏金额
  String get formattedProfitLoss {
    final prefix = profitLoss >= 0 ? '+' : '';
    return '$prefix${profitLoss.toStringAsFixed(2)}';
  }

  /// 获取格式化的盈亏率
  String get formattedProfitLossRate {
    final prefix = profitLossRate >= 0 ? '+' : '';
    return '$prefix${profitLossRate.toStringAsFixed(2)}%';
  }

  /// 创建数据副本并支持部分字段更新
  EventContractPosition copyWith({
    String? positionId,
    String? symbol,
    String? baseCurrency,
    String? quoteCurrency,
    EventContractDirection? direction,
    EventContractPositionStatus? status,
    double? openPrice,
    double? indexPrice,
    double? currentPrice,
    double? closePrice,
    double? quantity,
    double? bonusPayoutRate,
    double? bonusAmount,
    DateTime? openTime,
    DateTime? settlementTime,
    int? remainingTimeSeconds,
    int? totalDurationSeconds,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EventContractPosition(
      positionId: positionId ?? this.positionId,
      symbol: symbol ?? this.symbol,
      baseCurrency: baseCurrency ?? this.baseCurrency,
      quoteCurrency: quoteCurrency ?? this.quoteCurrency,
      direction: direction ?? this.direction,
      status: status ?? this.status,
      openPrice: openPrice ?? this.openPrice,
      indexPrice: indexPrice ?? this.indexPrice,
      currentPrice: currentPrice ?? this.currentPrice,
      closePrice: closePrice ?? this.closePrice,
      quantity: quantity ?? this.quantity,
      bonusPayoutRate: bonusPayoutRate ?? this.bonusPayoutRate,
      bonusAmount: bonusAmount ?? this.bonusAmount,
      openTime: openTime ?? this.openTime,
      settlementTime: settlementTime ?? this.settlementTime,
      remainingTimeSeconds: remainingTimeSeconds ?? this.remainingTimeSeconds,
      totalDurationSeconds: totalDurationSeconds ?? this.totalDurationSeconds,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// 转换为 Map（用于序列化）
  Map<String, dynamic> toMap() {
    return {
      'positionId': positionId,
      'symbol': symbol,
      'baseCurrency': baseCurrency,
      'quoteCurrency': quoteCurrency,
      'direction': direction.name,
      'status': status.name,
      'openPrice': openPrice,
      'indexPrice': indexPrice,
      'currentPrice': currentPrice,
      'closePrice': closePrice,
      'quantity': quantity,
      'bonusPayoutRate': bonusPayoutRate,
      'bonusAmount': bonusAmount,
      'openTime': openTime.toIso8601String(),
      'settlementTime': settlementTime?.toIso8601String(),
      'remainingTimeSeconds': remainingTimeSeconds,
      'totalDurationSeconds': totalDurationSeconds,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// 从 Map 创建实例（用于反序列化）
  factory EventContractPosition.fromMap(Map<String, dynamic> map) {
    return EventContractPosition(
      positionId: map['positionId'] ?? '',
      symbol: map['symbol'] ?? '',
      baseCurrency: map['baseCurrency'] ?? '',
      quoteCurrency: map['quoteCurrency'] ?? '',
      direction: EventContractDirection.values.firstWhere(
        (e) => e.name == map['direction'],
        orElse: () => EventContractDirection.up,
      ),
      status: EventContractPositionStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => EventContractPositionStatus.active,
      ),
      openPrice: (map['openPrice'] ?? 0).toDouble(),
      indexPrice: (map['indexPrice'] ?? 0).toDouble(),
      currentPrice: (map['currentPrice'] ?? 0).toDouble(),
      closePrice: map['closePrice']?.toDouble(),
      quantity: (map['quantity'] ?? 0).toDouble(),
      bonusPayoutRate: (map['bonusPayoutRate'] ?? 0).toDouble(),
      bonusAmount: (map['bonusAmount'] ?? 0).toDouble(),
      openTime:
          map['openTime'] != null
              ? DateTime.parse(map['openTime'])
              : DateTime.now(),
      settlementTime:
          map['settlementTime'] != null
              ? DateTime.parse(map['settlementTime'])
              : null,
      remainingTimeSeconds: map['remainingTimeSeconds'] ?? 0,
      totalDurationSeconds: map['totalDurationSeconds'] ?? 0,
      createdAt:
          map['createdAt'] != null
              ? DateTime.parse(map['createdAt'])
              : DateTime.now(),
      updatedAt:
          map['updatedAt'] != null
              ? DateTime.parse(map['updatedAt'])
              : DateTime.now(),
    );
  }

  /// 从 JSON 创建实例
  factory EventContractPosition.fromJson(Map<String, dynamic> json) {
    return EventContractPosition.fromMap(json);
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return toMap();
  }

  /// 创建示例数据（用于测试和开发）
  factory EventContractPosition.example({
    String? symbol,
    EventContractDirection? direction,
    double? quantity,
    double? openPrice,
  }) {
    final now = DateTime.now();
    return EventContractPosition(
      positionId: 'pos_${now.millisecondsSinceEpoch}',
      symbol: symbol ?? 'ETHUSDT',
      baseCurrency: symbol?.split('USDT').first ?? 'ETH',
      quoteCurrency: 'USDT',
      direction: direction ?? EventContractDirection.up,
      status: EventContractPositionStatus.active,
      openPrice: openPrice ?? 2978.00,
      indexPrice: openPrice ?? 2978.00,
      currentPrice: (openPrice ?? 2978.00) + 10.0,
      quantity: quantity ?? 1000.0,
      bonusPayoutRate: 100.0,
      bonusAmount: (quantity ?? 1000.0) * 1.045,
      openTime: now.subtract(const Duration(hours: 2)),
      remainingTimeSeconds: 36000, // 10小时
      totalDurationSeconds: 43200, // 12小时
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EventContractPosition &&
        other.positionId == positionId &&
        other.symbol == symbol &&
        other.direction == direction &&
        other.status == status;
  }

  @override
  int get hashCode {
    return positionId.hashCode ^
        symbol.hashCode ^
        direction.hashCode ^
        status.hashCode;
  }

  @override
  String toString() {
    return 'EventContractPosition('
        'positionId: $positionId, '
        'symbol: $symbol, '
        'direction: $directionText, '
        'status: $statusText, '
        'quantity: $formattedQuantity, '
        'openPrice: $formattedOpenPrice'
        ')';
  }
}
