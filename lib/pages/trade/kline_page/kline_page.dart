/*
*  K线图界面
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/routes/index.dart';
import './tabviews/index.dart';

class KlinePage extends StatefulWidget {
  const KlinePage({super.key});

  @override
  State<KlinePage> createState() => _KlinePageState();
}

class _KlinePageState extends State<KlinePage> with TickerProviderStateMixin {
  // 构建标签控制器
  late TabController _mainController;

  // 构建标签内容
  static const List<TabItem> _mainKeys = [
    TabItem(title: '行情'),
    TabItem(title: '数据'),
    TabItem(title: '广场', showBadge: true),
    TabItem(title: '币种概览'),
  ];

  // 初始化
  @override
  void initState() {
    super.initState();
    _mainController = TabController(length: _mainKeys.length, vsync: this);
  }

  // 销毁
  @override
  void dispose() {
    _mainController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final iconButtonSize = UiConstants.iconSize24;
    return Scaffold(
      backgroundColor: context.templateColors.surface,
      appBar: AppBarWidget(
        leadingWidth: 180,
        leading: Padding(
          padding: EdgeInsets.only(left: UiConstants.spacing4),
          child: Row(
            children: [
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: Icon(
                  Icons.arrow_back,
                  size: UiConstants.iconSize24,
                  color: context.templateColors.textPrimary,
                ),
                style: IconButton.styleFrom(
                  padding: EdgeInsets.zero,
                  minimumSize: Size(44, 44),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  splashFactory: NoSplash.splashFactory,
                  overlayColor: Colors.transparent,
                ),
              ),
              Text('BTC/USDT', style: context.templateStyle.text.h4),
              Padding(
                padding: EdgeInsets.only(left: UiConstants.spacing4),
                child: ThemedImage(
                  name: 'arrow_triangle_down',
                  size: UiConstants.iconSize10,
                  followTheme: true,
                ),
              ),
            ],
          ),
        ),
        actions: [
          Padding(
            padding: EdgeInsets.only(right: UiConstants.spacing18),
            child: Row(
              children: [
                InkWellWidget(
                  onTap: () => {},
                  child: ThemedImage(
                    size: iconButtonSize,
                    name: 'icon_favorite_off',
                    followTheme: true,
                  ),
                ),
                SizedBox(width: UiConstants.spacing10),
                InkWellWidget(
                  onTap: () => {},
                  child: ThemedImage(
                    size: iconButtonSize,
                    name: 'icon_record',
                    followTheme: true,
                  ),
                ),
                SizedBox(width: UiConstants.spacing10),
                InkWellWidget(
                  onTap:
                      () => {
                        // 跳转至价格提醒
                        NavigationService().navigateTo(AppRoutes.priceAlert),
                      },
                  child: ThemedImage(
                    size: iconButtonSize,
                    name: 'icon_reminder',
                    followTheme: true,
                  ),
                ),
                SizedBox(width: UiConstants.spacing10),
                InkWellWidget(
                  onTap: () => {},
                  child: ThemedImage(
                    size: iconButtonSize,
                    name: 'icon_share',
                    followTheme: true,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 主标签栏
            _buildMainTabbar(),

            // 主标签内容页
            _buildMainTabview(),

            // 底部操作栏
            _buildBottomOperationBar(),
          ],
        ),
      ),
    );
  }

  // 构建主标签栏
  Widget _buildMainTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: TabbarWidget(
        height: 38,
        controller: _mainController,
        tabs: _mainKeys,
        labelStyle: context.templateStyle.text.bodyTextMedium,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: true,
        indicatorSize: TabBarIndicatorSize.label,
        labelPadding: EdgeInsets.only(right: UiConstants.spacing18),
      ),
    );
  }

  // 构建主标签内容页
  Widget _buildMainTabview() {
    return Expanded(
      child: TabBarView(
        controller: _mainController,
        children: [
          /// 行情标签页
          KlineMarketTab(),

          // 数据
          KlineDataTab(),

          // 广场
          Container(),

          // 币种概览
          KlineCurrencyOverviewTab(),
        ],
      ),
    );
  }

  // 构建底部操作栏
  Widget _buildBottomOperationBar() {
    /// 图标按钮项
    Widget buildIconButton({
      required VoidCallback onTap,
      required String imageName,
      required String text,
    }) {
      return Expanded(
        child: InkWellWidget(
          onTap: onTap,
          child: Column(
            children: [
              ThemedImage(
                name: imageName,
                size: UiConstants.iconSize24,
                followTheme: true,
              ),
              SizedBox(height: UiConstants.spacing4),
              Text(text, style: context.templateStyle.text.bodySmall),
            ],
          ),
        ),
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing10,
      ),
      child: Row(
        children: [
          buildIconButton(text: '更多', imageName: 'more', onTap: () => {}),
          buildIconButton(
            text: '合约',
            imageName: 'icon_contract',
            onTap: () => {},
          ),
          buildIconButton(
            text: '机器人',
            imageName: 'icon_trade_bot',
            onTap: () => {},
          ),
          buildIconButton(
            text: '杠杆',
            imageName: 'icon_leverage',
            onTap: () => {},
          ),
          SizedBox(width: UiConstants.spacing10),
          Expanded(
            flex: 4,
            child: CommonButton.primary(
              '交易',
              height: UiConstants.buttonHeightMedium,
              width: double.infinity,
              onPressed: () => {},
            ),
          ),
        ],
      ),
    );
  }
}
