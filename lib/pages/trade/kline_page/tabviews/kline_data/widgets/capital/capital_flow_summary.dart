/*
*  资金流向 - 总览
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

class CapitalFlowSummary extends StatefulWidget {
  const CapitalFlowSummary({super.key});

  @override
  State<CapitalFlowSummary> createState() => _CapitalFlowSummaryState();
}

/// 资金流向数据模型
class CapitalFlowData {
  final String category;
  final double percentage;
  final Color color;

  CapitalFlowData({
    required this.category,
    required this.percentage,
    required this.color,
  });
}

class _CapitalFlowSummaryState extends State<CapitalFlowSummary>
    with TickerProviderStateMixin {
  /// 标签控制器
  late TabController _tabController;

  /// 模拟数据 - 按照图片中的颜色和数据
  final List<CapitalFlowData> _chartData = [
    CapitalFlowData(
      category: '大单卖出',
      percentage: 67.40,
      color: const Color(0xFFE91E63), // 主要的红色
    ),
    CapitalFlowData(
      category: '大单买入',
      percentage: 29.11,
      color: const Color(0xFF00BCD4), // 主要的青色
    ),
    CapitalFlowData(
      category: '中单买入',
      percentage: 12.29,
      color: const Color(0xFF4FC3F7), // 浅青色
    ),
    CapitalFlowData(
      category: '中单卖出',
      percentage: 12.13,
      color: const Color(0xFFFF69B4), // 浅红色
    ),
    CapitalFlowData(
      category: '小单买入',
      percentage: 6.05,
      color: const Color(0xFF81D4FA), // 更浅的青色
    ),
    CapitalFlowData(
      category: '小单卖出',
      percentage: 8.02,
      color: const Color(0xFFFFB3E6), // 更浅的红色
    ),
  ];

  /// 模拟资金流向详细数据
  final Map<String, Map<String, double>> _detailData = {
    '大单': {'buyIn': 12.726736, 'sellOut': 29.495743, 'netFlow': -16.769007},
    '中单': {'buyIn': 0.997585, 'sellOut': 0.490609, 'netFlow': 0.506976},
    '小单': {'buyIn': 0.018160, 'sellOut': 0.004411, 'netFlow': 0.013749},
    '汇总': {'buyIn': 13.742481, 'sellOut': 29.990763, 'netFlow': -16.248282},
  };

  /// 标签内容
  static const List<TabItem> tabs = [
    TabItem(title: '15m'),
    TabItem(title: '30m'),
    TabItem(title: '1h'),
    TabItem(title: '2h'),
    TabItem(title: '4h'),
    TabItem(title: '1d'),
  ];

  /// 初始化
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: tabs.length, vsync: this);
  }

  /// 销毁
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            width: context.templateStyles.borderWidthThin,
            color: context.templateColors.border,
          ),
        ),
      ),
      child: Column(children: [_buildHeader(), _buildChart()]),
    );
  }

  // 构建标题
  Widget _buildHeader() {
    return Row(
      children: [
        Text('资金流向分析', style: context.templateStyle.text.h4),
        SizedBox(width: UiConstants.spacing6),
        Icon(
          RemixIcons.information_2_fill,
          size: UiConstants.iconSize16,
          color: context.templateColors.textTertiary,
        ),
      ],
    );
  }

  // 构建图表区域
  Widget _buildChart() {
    /// 构建时间选择
    Widget buildTimeSelector() {
      return Container(
        padding: EdgeInsets.symmetric(vertical: UiConstants.spacing10),
        child: TabbarWidget(
          controller: _tabController,
          tabs: tabs,
          height: 24,
          labelStyle: context.templateStyle.text.bodySmallMedium,
          selectedColor: context.templateColors.textPrimary,
          unselectedColor: context.templateColors.textTertiary,
          showIndicator: true,
          indicatorSize: TabBarIndicatorSize.label,
          indicatorStyle: TabBarIndicatorStyle.filled,
          labelPadding: EdgeInsets.only(right: UiConstants.spacing6),
          tabPadding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
          indicatorRadius: UiConstants.borderRadius6,
        ),
      );
    }

    /// 构建饼图
    Widget buildPieChart() {
      return SizedBox(
        width: double.infinity,
        height: 320,
        child: SfCircularChart(
          backgroundColor: Colors.transparent,
          margin: EdgeInsets.all(UiConstants.spacing32), // 增加外边距
          series: <CircularSeries>[
            DoughnutSeries<CapitalFlowData, String>(
              dataSource: _chartData,
              xValueMapper: (CapitalFlowData data, _) => data.category,
              yValueMapper: (CapitalFlowData data, _) => data.percentage,
              pointColorMapper: (CapitalFlowData data, _) => data.color,
              innerRadius: '65%', // 内圆半径，形成环形效果
              radius: '50%', // 外圆半径，为标签留出足够空间
              strokeWidth: 3, // 设置边框宽度来创建间距
              strokeColor: context.templateColors.background, // 使用主题背景色创建间距效果
              cornerStyle: CornerStyle.bothFlat, // 扇形角的样式
              dataLabelSettings: DataLabelSettings(
                isVisible: true, // 显示数据标签
                labelPosition: ChartDataLabelPosition.outside, // 标签位置在外部
                connectorLineSettings: const ConnectorLineSettings(
                  type: ConnectorType.line, // 使用直线连接
                  length: '20%', // 设置较短的基础长度，让系统自动调整
                  width: 1, // 连接线宽度
                  color: Colors.white38, // 连接线颜色
                ),
                textStyle: TextStyle(
                  color: context.templateColors.textPrimary,
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                ),
                labelIntersectAction: LabelIntersectAction.shift, // 重叠时智能移动标签位置
                // 使用自定义builder来显示百分比符号
                builder: (
                  dynamic data,
                  dynamic point,
                  dynamic series,
                  int pointIndex,
                  int seriesIndex,
                ) {
                  final CapitalFlowData chartData = data as CapitalFlowData;
                  return Text(
                    '${chartData.percentage}%',
                    style: TextStyle(
                      color: context.templateColors.textPrimary,
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                    ),
                  );
                },
              ),
              enableTooltip: false, // 禁用工具提示
              animationDuration: 1000, // 动画时长
            ),
          ],
          legend: const Legend(
            isVisible: false, // 不显示图例
          ),
        ),
      );
    }

    return Column(
      children: [
        /// 时间选择
        buildTimeSelector(),

        /// 饼图
        buildPieChart(),

        /// 数据列表
        _buildDataList(),
      ],
    );
  }

  /// 构建数据信息展示区域
  Widget _buildDataList() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing10),
      child: Column(
        children: [
          // 表头
          _buildDataHeader(),
          SizedBox(height: UiConstants.spacing6),
          // 数据行
          ..._detailData.entries.map(
            (entry) => _buildDataRow(
              entry.key,
              entry.value['buyIn']!,
              entry.value['sellOut']!,
              entry.value['netFlow']!,
              isTotal: entry.key == '汇总',
            ),
          ),
        ],
      ),
    );
  }

  /// 构建表头
  Widget _buildDataHeader() {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            '',
            style: context.templateStyle.text.hintText,
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            '买入(BTC)',
            textAlign: TextAlign.start,
            style: context.templateStyle.text.hintText,
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            '卖出(BTC)',
            textAlign: TextAlign.start,
            style: context.templateStyle.text.hintText,
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            '净流入',
            textAlign: TextAlign.end,
            style: context.templateStyle.text.hintText,
          ),
        ),
      ],
    );
  }

  /// 获取对应类别和类型的颜色
  Color? _getColorForCategoryAndType(String category, String type) {
    if (category == '汇总') return null; // 汇总不显示颜色

    final colorMap = {
      '大单': {
        'buy': const Color(0xFF00BCD4), // 大单买入 - 主要青色
        'sell': const Color(0xFFE91E63), // 大单卖出 - 主要红色
      },
      '中单': {
        'buy': const Color(0xFF4FC3F7), // 中单买入 - 浅青色
        'sell': const Color(0xFFFF69B4), // 中单卖出 - 浅红色
      },
      '小单': {
        'buy': const Color(0xFF81D4FA), // 小单买入 - 更浅青色
        'sell': const Color(0xFFFFB3E6), // 小单卖出 - 更浅红色
      },
    };

    return colorMap[category]?[type];
  }

  /// 构建数据行
  Widget _buildDataRow(
    String category,
    double buyIn,
    double sellOut,
    double netFlow, {
    bool isTotal = false,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing4),
      child: Row(
        children: [
          // 类别
          Expanded(
            flex: 2,
            child: Text(category, style: context.templateStyle.text.hintText),
          ),
          // 买入
          Expanded(
            flex: 3,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                // 颜色指示器（汇总不显示）
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color:
                        isTotal
                            ? Colors.transparent
                            : _getColorForCategoryAndType(category, 'buy'),
                  ),
                ),
                SizedBox(width: UiConstants.spacing6),
                Text(
                  buyIn.toStringAsFixed(6),
                  style: context.templateStyle.text.bodySmallMedium,
                ),
              ],
            ),
          ),
          // 卖出
          Expanded(
            flex: 3,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                // 颜色指示器（汇总不显示）
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color:
                        isTotal
                            ? Colors.transparent
                            : _getColorForCategoryAndType(category, 'sell'),
                  ),
                ),
                SizedBox(width: UiConstants.spacing6),
                Text(
                  sellOut.toStringAsFixed(6),
                  style: context.templateStyle.text.bodySmallMedium,
                ),
              ],
            ),
          ),
          // 净流入
          Expanded(
            flex: 3,
            child: Text(
              netFlow >= 0
                  ? netFlow.toStringAsFixed(6)
                  : netFlow.toStringAsFixed(6),
              textAlign: TextAlign.end,
              style: context.templateStyle.text.bodySmallMedium,
            ),
          ),
        ],
      ),
    );
  }
}
