/*
*  资金净流
*/

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/pages/trade/widgets/charts/line_chart_widget.dart';

class NetCapitalInflow extends StatefulWidget {
  const NetCapitalInflow({super.key});

  @override
  State<NetCapitalInflow> createState() => _NetCapitalInflowState();
}

class _NetCapitalInflowState extends State<NetCapitalInflow> {
  late List<ChartDataPoint> _chartData;

  @override
  void initState() {
    super.initState();
    _generateSampleData();
  }

  /// 生成示例资金流入数据
  void _generateSampleData() {
    final now = DateTime.now();

    // 模拟24小时资金净流入数据，类似截图中的波动效果
    _chartData = [
      ChartDataPoint(time: now.subtract(Duration(hours: 24)), value: 5.737857),
      ChartDataPoint(time: now.subtract(Duration(hours: 22)), value: 8.2),
      ChartDataPoint(time: now.subtract(Duration(hours: 20)), value: 12.5),
      ChartDataPoint(time: now.subtract(Duration(hours: 18)), value: 15.8),
      ChartDataPoint(time: now.subtract(Duration(hours: 16)), value: 18.3),
      ChartDataPoint(time: now.subtract(Duration(hours: 14)), value: 22.1),
      ChartDataPoint(time: now.subtract(Duration(hours: 12)), value: 42.147979),
      ChartDataPoint(time: now.subtract(Duration(hours: 10)), value: 35.2),
      ChartDataPoint(time: now.subtract(Duration(hours: 8)), value: 28.8),
      ChartDataPoint(time: now.subtract(Duration(hours: 6)), value: 25.4),
      ChartDataPoint(time: now.subtract(Duration(hours: 4)), value: 23.942918),
      ChartDataPoint(time: now.subtract(Duration(hours: 2)), value: -12.467204),
      ChartDataPoint(time: now.subtract(Duration(hours: 1)), value: -8.3),
      ChartDataPoint(time: now, value: -30.672265),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing24,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            width: context.templateStyles.borderWidthThin,
            color: context.templateColors.border,
          ),
        ),
      ),
      child: Column(children: [_buildHeader(), _buildChart()]),
    );
  }

  // 构建标题
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text('24 小时资金净流入(BTC)', style: context.templateStyle.text.h4),
            SizedBox(width: UiConstants.spacing6),
          ],
        ),
      ],
    );
  }

  // 构建图表
  Widget _buildChart() {
    final colors = context.templateColors;

    return Container(
      margin: EdgeInsets.only(top: UiConstants.spacing16),
      height: 200,
      child: LineChartWidget(
        data: _chartData,
        chartType: ChartType.spline,
        lineColor: context.templateColors.primary, // 青色线条，类似截图
        lineWidth: 2.0,
        height: 200,
        showGridLines: true,
        xAxisLabelColor: colors.textTertiary, // 自定义X轴标签颜色
        yAxisLabelColor: colors.textTertiary, // 自定义Y轴标签颜色
        gridLineColor: colors.divider.withValues(alpha: 0.1),
        crosshairLineColor: colors.textTertiary,
        enableZooming: false, // 禁用缩放
        enablePanning: false, // 禁用平移
        enableCrosshair: true, // 启用十字线
        enableTooltip: true, // 启用tooltip
        onPointTap: (point) {},
      ),
    );
  }
}
