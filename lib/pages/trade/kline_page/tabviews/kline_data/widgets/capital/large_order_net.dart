/*
*  大单净流
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/pages/trade/widgets/charts/line_chart_widget.dart';

class LargeOrderNet extends StatefulWidget {
  const LargeOrderNet({super.key});

  @override
  State<LargeOrderNet> createState() => _LargeOrderNetState();
}

class _LargeOrderNetState extends State<LargeOrderNet> {
  // 示例数据 - 实际使用时应该从API获取
  List<WinLossDataPoint> get _chartData => [
    WinLossDataPoint(category: '24h', value: -231.313829, isPositive: false),
    WinLossDataPoint(category: '48h', value: 15.720788, isPositive: true),
    WinLossDataPoint(category: '72h', value: -180.663502, isPositive: false),
    WinLossDataPoint(category: '96h', value: -4.779055, isPositive: false),
    WinLossDataPoint(category: '120h', value: 42.434644, isPositive: true),
  ];

  // 格式化数值显示
  String _formatValue(double value) {
    if (value.abs() >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(2)}M';
    } else if (value.abs() >= 1000) {
      return '${(value / 1000).toStringAsFixed(2)}K';
    } else {
      return value.toStringAsFixed(6);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing24,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            width: context.templateStyles.borderWidthThin,
            color: context.templateColors.border,
          ),
        ),
      ),
      child: Column(children: [_buildHeader(), _buildChart()]),
    );
  }

  // 构建标题
  Widget _buildHeader() {
    return Row(
      children: [
        Text('5 x 24 小时大单净流入(BTC)', style: context.templateStyle.text.h4),
        SizedBox(width: UiConstants.spacing6),
      ],
    );
  }

  // 构建图表
  Widget _buildChart() {
    return Container(
      margin: EdgeInsets.only(top: UiConstants.spacing16),
      height: 200,
      child: LineChartWidget.winLoss(
        winLossData: _chartData,
        height: 200,
        positiveColor: context.templateColors.success,
        negativeColor: context.templateColors.error,
        yAxisFormatter: _formatValue,
        itemWidth: 0.3,
      ),
    );
  }
}
