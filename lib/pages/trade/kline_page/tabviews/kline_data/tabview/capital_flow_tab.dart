/*
*  资金流向标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import '../widgets/index.dart';

class CapitalFlowTab extends StatefulWidget {
  final ScrollPhysics? physics;
  const CapitalFlowTab({super.key, this.physics});

  @override
  State<CapitalFlowTab> createState() => _CapitalFlowTabState();
}

class _CapitalFlowTabState extends State<CapitalFlowTab> {
  @override
  Widget build(BuildContext context) {
    return ListView(
      physics: widget.physics,
      children: [
        // 资金流向分析
        const CapitalFlowSummary(),

        // 大单净流
        const LargeOrderNet(),

        // 资金净流
        NetCapitalInflow(),
      ],
    );
  }
}
