/*
*  杠杆数据标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';
import 'package:qubic_exchange/pages/trade/widgets/charts/line_chart_widget.dart';
import 'package:super_tooltip/super_tooltip.dart';
import 'dart:math' as math;

/// 杠杆数据类型枚举
enum LeverageDataType {
  borrowingGrowth, // 杠杆借贷存量增速
  longShortRatio, // 杠杆多空比
  isolatedBorrowingRatio, // 逐仓借贷比
}

/// 货币类型枚举
enum CurrencyCategory {
  base('基础货币'),
  quote('计价货币');

  const CurrencyCategory(this.label);
  final String label;
}

/// 货币选项
class CurrencyOption {
  final String symbol;
  final String name;
  final CurrencyCategory category;

  const CurrencyOption({
    required this.symbol,
    required this.name,
    required this.category,
  });
}

/// 预定义的货币选项
class CurrencyOptions {
  static const List<CurrencyOption> options = [
    // 基础货币
    CurrencyOption(
      symbol: 'BTC',
      name: 'Bitcoin',
      category: CurrencyCategory.base,
    ),

    // 计价货币
    CurrencyOption(
      symbol: 'USDT',
      name: 'Tether',
      category: CurrencyCategory.quote,
    ),
  ];

  static List<CurrencyOption> getByCategory(CurrencyCategory category) {
    return options.where((option) => option.category == category).toList();
  }
}

class LeverageDataTab extends StatefulWidget {
  const LeverageDataTab({super.key});

  @override
  State<LeverageDataTab> createState() => _LeverageDataTabState();
}

class _LeverageDataTabState extends State<LeverageDataTab>
    with TickerProviderStateMixin {
  // 为每个图表创建独立的 TabController
  late TabController _borrowingGrowthTabController;
  late TabController _longShortRatioTabController;
  late TabController _isolatedBorrowingTabController;

  static const List<TabItem> _tabKeys = [
    TabItem(title: '24h'),
    TabItem(title: '30d'),
  ];

  // 当前选择的货币
  CurrencyOption _selectedCurrency = CurrencyOptions.options.first;

  // 每个图表的时间周期索引
  int _borrowingGrowthPeriodIndex = 0;
  int _longShortRatioPeriodIndex = 0;
  int _isolatedBorrowingPeriodIndex = 0;

  @override
  void initState() {
    super.initState();

    // 初始化三个独立的 TabController
    _borrowingGrowthTabController = TabController(length: 2, vsync: this);
    _longShortRatioTabController = TabController(length: 2, vsync: this);
    _isolatedBorrowingTabController = TabController(length: 2, vsync: this);

    // 监听各个 TabController 变化
    _borrowingGrowthTabController.addListener(() {
      if (!_borrowingGrowthTabController.indexIsChanging) {
        setState(() {
          _borrowingGrowthPeriodIndex = _borrowingGrowthTabController.index;
        });
      }
    });

    _longShortRatioTabController.addListener(() {
      if (!_longShortRatioTabController.indexIsChanging) {
        setState(() {
          _longShortRatioPeriodIndex = _longShortRatioTabController.index;
        });
      }
    });

    _isolatedBorrowingTabController.addListener(() {
      if (!_isolatedBorrowingTabController.indexIsChanging) {
        setState(() {
          _isolatedBorrowingPeriodIndex = _isolatedBorrowingTabController.index;
        });
      }
    });
  }

  @override
  void dispose() {
    _borrowingGrowthTabController.dispose();
    _longShortRatioTabController.dispose();
    _isolatedBorrowingTabController.dispose();
    super.dispose();
  }

  /// 生成图表数据
  List<ChartDataPoint> _generateChartData(LeverageDataType dataType) {
    final now = DateTime.now();

    // 根据数据类型获取对应的周期索引
    int periodIndex;
    switch (dataType) {
      case LeverageDataType.borrowingGrowth:
        periodIndex = _borrowingGrowthPeriodIndex;
        break;
      case LeverageDataType.longShortRatio:
        periodIndex = _longShortRatioPeriodIndex;
        break;
      case LeverageDataType.isolatedBorrowingRatio:
        periodIndex = _isolatedBorrowingPeriodIndex;
        break;
    }

    final isLongPeriod = periodIndex == 1; // 30d
    final dataPoints = isLongPeriod ? 30 : 24; // 30天或24小时
    final interval =
        isLongPeriod ? const Duration(days: 1) : const Duration(hours: 1);

    return List.generate(dataPoints, (index) {
      final time = now.subtract(interval * (dataPoints - 1 - index));
      double value;

      switch (dataType) {
        case LeverageDataType.borrowingGrowth:
          // 杠杆借贷存量增速数据 (%)
          value = _generateBorrowingGrowthData(index, dataPoints);
          break;
        case LeverageDataType.longShortRatio:
          // 杠杆多空比数据
          value = _generateLongShortRatioData(index, dataPoints);
          break;
        case LeverageDataType.isolatedBorrowingRatio:
          // 逐仓借贷比数据 (%)
          value = _generateIsolatedBorrowingData(index, dataPoints);
          break;
      }

      return ChartDataPoint(time: time, value: value);
    });
  }

  /// 生成杠杆借贷存量增速数据
  double _generateBorrowingGrowthData(int index, int total) {
    // 模拟借贷存量增速，范围在 -5% 到 15%
    final baseValue = 5.0;
    final variation = 10.0;
    final trend = (index / total) * 2 - 1; // -1 到 1 的趋势
    final noise = (index * 17 % 100) / 100.0 - 0.5; // 随机噪声

    return baseValue + variation * trend + noise * 3;
  }

  /// 生成杠杆多空比数据
  double _generateLongShortRatioData(int index, int total) {
    // 模拟多空比，范围在 0.5 到 3.0
    final baseValue = 1.5;
    final variation = 1.0;
    final cycle = (index / total) * 2 * 3.14159; // 周期性变化
    final noise = (index * 23 % 100) / 100.0 - 0.5; // 随机噪声

    return baseValue +
        variation * (0.5 + 0.3 * (1 + math.sin(cycle))) +
        noise * 0.2;
  }

  /// 生成逐仓借贷比数据
  double _generateIsolatedBorrowingData(int index, int total) {
    // 模拟逐仓借贷比，范围在 10% 到 80%
    final baseValue = 45.0;
    final variation = 25.0;
    final trend = math.sin((index / total) * 3.14159); // 正弦趋势
    final noise = (index * 31 % 100) / 100.0 - 0.5; // 随机噪声

    return baseValue + variation * trend + noise * 5;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // 杠杆借贷存量增速
          _buildCommonPart(
            labelText: '杠杆借贷存量增速',
            dataType: LeverageDataType.borrowingGrowth,
          ),

          // 杠杆多空比
          _buildCommonPart(
            labelText: '杠杆多空比',
            dataType: LeverageDataType.longShortRatio,
          ),

          // 逐仓借贷比
          _buildCommonPart(
            labelText: '逐仓借贷比',
            dataType: LeverageDataType.isolatedBorrowingRatio,
            showDropdown: false,
          ),
        ],
      ),
    );
  }

  // 构建公用部分
  Widget _buildCommonPart({
    required String labelText,
    required LeverageDataType dataType,
    bool showDropdown = true,
  }) {
    // 根据数据类型获取对应的 TabController
    TabController tabController;
    switch (dataType) {
      case LeverageDataType.borrowingGrowth:
        tabController = _borrowingGrowthTabController;
        break;
      case LeverageDataType.longShortRatio:
        tabController = _longShortRatioTabController;
        break;
      case LeverageDataType.isolatedBorrowingRatio:
        tabController = _isolatedBorrowingTabController;
        break;
    }
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing18),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            width: context.templateStyles.borderWidthThin,
            color: context.templateColors.divider,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Text(
                    labelText,
                    style: context.templateStyle.text.h5.copyWith(
                      fontWeight: UiConstants.fontWeightMedium,
                    ),
                  ),
                  SizedBox(width: UiConstants.spacing6),
                  Icon(
                    RemixIcons.information_2_fill,
                    size: UiConstants.iconSize16,
                    color: context.templateColors.textTertiary,
                  ),
                ],
              ),

              if (showDropdown)
                DropdownMenuWidget(
                  direction: TooltipDirection.down,
                  offset: EdgeInsets.only(top: UiConstants.spacing8),
                  buttonWidget: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _selectedCurrency.symbol,
                        style: context.templateStyle.text.descriptionSmall,
                      ),
                      ThemedImage.asset(
                        'arrow_triangle_down_gray',
                        size: UiConstants.iconSize10,
                        margin: EdgeInsets.only(left: UiConstants.spacing6),
                      ),
                    ],
                  ),
                  childBuilder:
                      (closeMenu) => _buildCurrencyDropdownContent(closeMenu),
                ),
            ],
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: UiConstants.spacing10),
            child: TabbarWidget(
              controller: tabController,
              tabs: _tabKeys,
              height: 24,
              labelStyle: context.templateStyle.text.bodySmallMedium,
              selectedColor: context.templateColors.textPrimary,
              unselectedColor: context.templateColors.textTertiary,
              showIndicator: true,
              indicatorSize: TabBarIndicatorSize.label,
              indicatorStyle: TabBarIndicatorStyle.filled,
              labelPadding: EdgeInsets.only(right: UiConstants.spacing6),
              tabPadding: EdgeInsets.symmetric(
                horizontal: UiConstants.spacing10,
              ),
              indicatorRadius: UiConstants.borderRadius6,
            ),
          ),
          SizedBox(height: UiConstants.spacing10),

          // 图表
          _buildChart(dataType),
        ],
      ),
    );
  }

  /// 构建图表
  Widget _buildChart(LeverageDataType dataType) {
    final chartData = _generateChartData(dataType);

    return SizedBox(
      height: 200,
      child: LineChartWidget.simple(
        data: chartData,
        lineColor: context.templateColors.primary,
        lineWidth: 2.0,
        height: 200,
        enableCrosshair: false,
      ),
    );
  }

  /// 构建货币下拉菜单内容
  Widget _buildCurrencyDropdownContent(VoidCallback closeMenu) {
    return Container(
      constraints: BoxConstraints(maxWidth: 200, maxHeight: 300),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 直接显示所有货币选项，不分类
          ...CurrencyOptions.options.map(
            (currency) =>
                _buildCurrencyItem(currency: currency, closeMenu: closeMenu),
          ),
        ],
      ),
    );
  }

  /// 构建单个货币选项
  Widget _buildCurrencyItem({
    required CurrencyOption currency,
    required VoidCallback closeMenu,
  }) {
    final isSelected = _selectedCurrency.symbol == currency.symbol;

    return InkWellWidget(
      onTap: () {
        setState(() {
          _selectedCurrency = currency;
        });
        closeMenu();
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing8,
          vertical: UiConstants.spacing6,
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    currency.symbol,
                    style: context.templateStyle.text.bodySmallMedium.copyWith(
                      color:
                          isSelected
                              ? context.templateColors.primary
                              : context.templateColors.textPrimary,
                    ),
                  ),
                  if (currency.name != currency.symbol)
                    Text(
                      currency.name,
                      style: context.templateStyle.text.descriptionSmall
                          .copyWith(color: context.templateColors.textTertiary),
                    ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check,
                size: UiConstants.iconSize14,
                color: context.templateColors.primary,
              ),
          ],
        ),
      ),
    );
  }
}
