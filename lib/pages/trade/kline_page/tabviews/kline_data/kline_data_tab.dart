/*
*  K线数据标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import './tabview/capital_flow_tab.dart';
import './tabview/leverage_data_tab.dart';

class KlineDataTab extends StatefulWidget {
  const KlineDataTab({super.key});

  @override
  State<KlineDataTab> createState() => _KlineDataTabState();
}

class _KlineDataTabState extends State<KlineDataTab>
    with TickerProviderStateMixin {
  /// 标签控制器
  late TabController _tabController;

  /// 标签内容
  static const List<TabItem> _tabs = [
    TabItem(title: '资金流向'),
    TabItem(title: '杠杆数据'),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    // 模拟刷新操作
    await Future.delayed(const Duration(seconds: 2));
    // 这里可以添加实际的数据刷新逻辑
  }

  @override
  Widget build(BuildContext context) {
    final maxOffset = 44.0;
    final tabbarHeight = 64.0;
    return EasyRefresh.builder(
      header: ClassicHeader(
        clamping: true,
        position: IndicatorPosition.locator,
        triggerOffset: maxOffset,
        processedDuration: const Duration(seconds: 1),
        safeArea: false,
        showMessage: false,
        showText: false,
        maxOverOffset: maxOffset,
      ),
      onRefresh: _onRefresh,
      childBuilder: (context, physics) {
        return ScrollConfiguration(
          behavior: const ERScrollBehavior(),
          child: ExtendedNestedScrollView(
            physics: physics,
            onlyOneScrollInBody: true,
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return <Widget>[
                // 刷新指示器定位器
                const HeaderLocator.sliver(clearExtent: false),
                // 标签栏
                SliverPersistentHeader(
                  pinned: true,
                  delegate: StickyDelegate(
                    maxHeight: tabbarHeight,
                    child: _buildTabbar(),
                    backgroundColor: context.templateColors.surface,
                  ),
                ),
              ];
            },
            body: _buildTabView(physics),
          ),
        );
      },
    );
  }

  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing20,
      ),
      child: TabbarWidget(
        controller: _tabController,
        tabs: _tabs,
        height: 24,
        labelStyle: context.templateStyle.text.bodySmallMedium,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textTertiary,
        showIndicator: true,
        indicatorSize: TabBarIndicatorSize.label,
        indicatorStyle: TabBarIndicatorStyle.filled,
        labelPadding: EdgeInsets.only(right: UiConstants.spacing6),
        tabPadding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
        indicatorRadius: UiConstants.borderRadius6,
      ),
    );
  }

  Widget _buildTabView(ScrollPhysics physics) {
    return TabBarView(
      controller: _tabController,
      children: [
        /// 资金流向
        CapitalFlowTab(physics: physics),

        /// 杠杆数据
        LeverageDataTab(),
      ],
    );
  }
}
