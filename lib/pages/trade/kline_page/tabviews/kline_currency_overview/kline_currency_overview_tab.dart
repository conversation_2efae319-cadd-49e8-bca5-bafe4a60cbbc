/*
*  币种概览标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import './tabviews/base_info_tab.dart';
import './tabviews/token_economics_tab.dart';

class KlineCurrencyOverviewTab extends StatefulWidget {
  const KlineCurrencyOverviewTab({super.key});

  @override
  State<KlineCurrencyOverviewTab> createState() =>
      _KlineCurrencyOverviewTabState();
}

class _KlineCurrencyOverviewTabState extends State<KlineCurrencyOverviewTab>
    with TickerProviderStateMixin {
  // TabController
  late TabController _tabController;

  // 标签内容
  static const List<TabItem> _tabKeys = [
    TabItem(title: '基本资料'),
    TabItem(title: '代币经济学'),
  ];

  // 初始化
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabKeys.length, vsync: this);
  }

  // 销毁
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // 构建 UI 内容
  @override
  Widget build(BuildContext context) {
    return Column(children: [_buildTabBar(), _buildAdaptiveTabView()]);
  }

  // 构建 TabBar
  Widget _buildTabBar() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing16,
        vertical: UiConstants.spacing12,
      ),
      child: TabbarWidget(
        controller: _tabController,
        tabs: _tabKeys,
        height: 28,
        labelStyle: context.templateStyle.text.bodySmallMedium,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textTertiary,
        showIndicator: true,
        indicatorSize: TabBarIndicatorSize.label,
        indicatorStyle: TabBarIndicatorStyle.filled,
        labelPadding: EdgeInsets.only(right: UiConstants.spacing4),
        tabPadding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
        indicatorRadius: UiConstants.borderRadiusCircle,
      ),
    );
  }

  // 构建自适应高度的 TabView
  Widget _buildAdaptiveTabView() {
    return Expanded(
      child: AnimatedSize(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        child: IntrinsicHeight(
          child: TabBarView(
            controller: _tabController,
            children: [
              // 基本信息
              BaseInfoTab(),

              // 代币经济学
              TokenEconomicsTab(),
            ],
          ),
        ),
      ),
    );
  }
}
