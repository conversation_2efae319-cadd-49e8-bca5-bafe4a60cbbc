/*
*  基本信息
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:intl/intl.dart';

/// 加密货币基本信息数据模型
class CryptoCurrencyInfo {
  final String symbol;
  final String name;
  final String iconName;
  final int rank;
  final double marketCap;
  final double fullyDilutedMarketCap;
  final double volume24h;
  final double marketCapRatio;
  final double marketDominance;
  final DateTime launchDate;
  final double allTimeHigh;
  final DateTime allTimeHighDate;
  final double allTimeLow;
  final DateTime allTimeLowDate;
  final String blockchain;
  final double circulatingSupply;
  final double maxSupply;
  final double totalSupply;
  final double circulationRate;
  final List<MarketSector> sectors;
  final String description;
  final List<CommunityLink> communityLinks;
  final List<ResourceLink> resourceLinks;

  CryptoCurrencyInfo({
    required this.symbol,
    required this.name,
    required this.iconName,
    required this.rank,
    required this.marketCap,
    required this.fullyDilutedMarketCap,
    required this.volume24h,
    required this.marketCapRatio,
    required this.marketDominance,
    required this.launchDate,
    required this.allTimeHigh,
    required this.allTimeHighDate,
    required this.allTimeLow,
    required this.allTimeLowDate,
    required this.blockchain,
    required this.circulatingSupply,
    required this.maxSupply,
    required this.totalSupply,
    required this.circulationRate,
    required this.sectors,
    required this.description,
    required this.communityLinks,
    required this.resourceLinks,
  });
}

/// 市场板块数据模型
class MarketSector {
  final String name;
  final double changePercent24h;

  MarketSector({required this.name, required this.changePercent24h});
}

/// 社区链接数据模型
class CommunityLink {
  final String name;
  final String iconName;
  final String url;

  CommunityLink({
    required this.name,
    required this.iconName,
    required this.url,
  });
}

/// 资源链接数据模型
class ResourceLink {
  final String name;
  final String iconName;
  final String url;

  ResourceLink({required this.name, required this.iconName, required this.url});
}

class BaseInfoTab extends StatefulWidget {
  const BaseInfoTab({super.key});

  @override
  State<BaseInfoTab> createState() => _BaseInfoTabState();
}

class _BaseInfoTabState extends State<BaseInfoTab> {
  // 示例数据 - 实际使用时应该从API获取
  late CryptoCurrencyInfo _currencyInfo;

  // 简介展开状态
  bool _isDescriptionExpanded = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  /// 初始化数据
  void _initializeData() {
    _currencyInfo = CryptoCurrencyInfo(
      symbol: 'BTC',
      name: 'Bitcoin',
      iconName: 'btc',
      rank: 1,
      marketCap: 1.95e12, // $1.95T
      fullyDilutedMarketCap: 2.05e12, // $2.05T
      volume24h: 28.5e9, // $28.5B
      marketCapRatio: 0.0146, // 1.46%
      marketDominance: 54.2, // 54.2%
      launchDate: DateTime(2009, 1, 3),
      allTimeHigh: 73750.07,
      allTimeHighDate: DateTime(2024, 3, 14),
      allTimeLow: 67.81,
      allTimeLowDate: DateTime(2013, 7, 6),
      blockchain: 'Bitcoin',
      circulatingSupply: 19.78e6, // 19.78M BTC
      maxSupply: 21e6, // 21M BTC
      totalSupply: 19.78e6, // 19.78M BTC
      circulationRate: 94.2, // 94.2%
      sectors: [
        MarketSector(name: 'Layer 1', changePercent24h: 1.70),
        MarketSector(name: 'Store of Value', changePercent24h: 2.15),
        MarketSector(name: 'Proof of Work', changePercent24h: 0.85),
      ],
      description:
          'Bitcoin是世界上第一个去中心化的数字货币，由神秘的中本聪在2009年创建。它基于革命性的区块链技术，允许点对点的价值传输，无需银行或政府等中央机构的参与。\n\nBitcoin采用工作量证明（Proof of Work）共识机制，通过挖矿来维护网络安全和处理交易。其总供应量被限制在2100万枚，这种稀缺性使其被广泛认为是"数字黄金"。\n\nBitcoin不仅是加密货币市场的领导者，也是整个区块链生态系统的基石。它启发了数千种其他加密货币的诞生，并推动了去中心化金融（DeFi）、非同质化代币（NFT）等创新应用的发展。\n\n作为价值存储工具，Bitcoin已被多家机构和公司采用，包括特斯拉、MicroStrategy等知名企业。许多国家也开始将Bitcoin纳入其金融体系，萨尔瓦多甚至将其作为法定货币。',
      communityLinks: [
        CommunityLink(
          name: 'Twitter',
          iconName: 'icon_twitter',
          url: 'https://twitter.com/bitcoin',
        ),
        CommunityLink(
          name: 'Reddit',
          iconName: 'icon_reddit',
          url: 'https://reddit.com/r/bitcoin',
        ),
        CommunityLink(
          name: 'Telegram',
          iconName: 'icon_telegram',
          url: 'https://t.me/bitcoin',
        ),
      ],
      resourceLinks: [
        ResourceLink(
          name: 'Official Website',
          iconName: 'icon_website',
          url: 'https://bitcoin.org',
        ),
        ResourceLink(
          name: 'Whitepaper',
          iconName: 'icon_document',
          url: 'https://bitcoin.org/bitcoin.pdf',
        ),
        ResourceLink(
          name: 'GitHub',
          iconName: 'icon_github',
          url: 'https://github.com/bitcoin/bitcoin',
        ),
      ],
    );
  }

  /// 格式化数值
  String _formatNumber(double value) {
    if (value >= 1e12) {
      return '\$${(value / 1e12).toStringAsFixed(2)}T';
    } else if (value >= 1e9) {
      return '\$${(value / 1e9).toStringAsFixed(2)}B';
    } else if (value >= 1e6) {
      return '\$${(value / 1e6).toStringAsFixed(2)}M';
    } else if (value >= 1e3) {
      return '\$${(value / 1e3).toStringAsFixed(2)}K';
    } else {
      return '\$${value.toStringAsFixed(2)}';
    }
  }

  /// 格式化供应量
  String _formatSupply(double value) {
    if (value >= 1e9) {
      return '${(value / 1e9).toStringAsFixed(2)}B';
    } else if (value >= 1e6) {
      return '${(value / 1e6).toStringAsFixed(2)}M';
    } else if (value >= 1e3) {
      return '${(value / 1e3).toStringAsFixed(2)}K';
    } else {
      return value.toStringAsFixed(0);
    }
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.all(UiConstants.spacing18),
        child: Column(
          children: [
            // 货币图标 & 名称
            _buildPair(),

            // 关键信息
            _buildKeyInfo(),

            // 所属板块
            _buildMarket(),

            // 社区
            _buildCommunity(),

            // 资源
            _buildResources(),

            // 免责声明
            _buildDisclaimer(),
          ],
        ),
      ),
    );
  }

  // 构建货币图标 & 名称
  Widget _buildPair() {
    return Padding(
      padding: EdgeInsets.only(bottom: UiConstants.spacing28),
      child: Row(
        children: [
          ThemedImage.crypto(
            _currencyInfo.iconName,
            size: UiConstants.iconSize28,
            borderRadius: BorderRadius.circular(12),
            margin: EdgeInsets.only(right: UiConstants.spacing12),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _currencyInfo.name,
                style: context.templateStyle.text.h4.copyWith(
                  fontWeight: UiConstants.fontWeightMedium,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建关键信息
  Widget _buildKeyInfo() {
    /// 关键信息项
    Widget buildInfoItem({
      required String labelText,
      required String value,
      String? time,
      Color? color,
      bool showDecoration = true,
      VoidCallback? onTap,
    }) {
      return Padding(
        padding: EdgeInsets.symmetric(vertical: UiConstants.spacing6),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            InkWellWidget(
              onTap: showDecoration ? onTap : null,
              // () => {},
              child: Text(
                labelText,
                style: context.templateStyle.text.descriptionText.copyWith(
                  decorationStyle: TextDecorationStyle.dotted,
                  decoration: TextDecoration.underline,
                  decorationColor:
                      showDecoration
                          ? context.templateColors.textTertiary
                          : Colors.transparent,
                ),
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  value,
                  style: context.templateStyle.text.bodyTextMedium.copyWith(
                    color: context.templateColors.textPrimary,
                  ),
                ),
                if (time != null)
                  Padding(
                    padding: EdgeInsets.only(top: UiConstants.spacing4),
                    child: Text(
                      time,
                      style: context.templateStyle.text.hintText,
                    ),
                  ),
              ],
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        buildInfoItem(
          labelText: '排名',
          value: 'NO.${_currencyInfo.rank}',
          onTap: () => {},
        ),
        buildInfoItem(
          labelText: '市值',
          value: _formatNumber(_currencyInfo.marketCap),
          onTap: () => {},
        ),
        buildInfoItem(
          labelText: '完全稀释市值',
          value: _formatNumber(_currencyInfo.fullyDilutedMarketCap),
          onTap: () => {},
        ),
        buildInfoItem(
          labelText: '交易额/市值(24小时)',
          value: '${(_currencyInfo.marketCapRatio * 100).toStringAsFixed(2)}%',
          onTap: () => {},
        ),
        buildInfoItem(
          labelText: '市场占有率',
          value: '${_currencyInfo.marketDominance.toStringAsFixed(1)}%',
          onTap: () => {},
        ),
        Divider(color: context.templateColors.divider),
        buildInfoItem(
          labelText: '发行时间',
          value: _formatDate(_currencyInfo.launchDate),
          showDecoration: false,
        ),
        buildInfoItem(
          labelText: '历史最高价',
          value: _formatNumber(_currencyInfo.allTimeHigh),
          time: _formatDate(_currencyInfo.allTimeHighDate),
          showDecoration: false,
        ),
        buildInfoItem(
          labelText: '历史最低价',
          value: _formatNumber(_currencyInfo.allTimeLow),
          time: _formatDate(_currencyInfo.allTimeLowDate),
          showDecoration: false,
        ),
        buildInfoItem(
          labelText: '所属公链',
          value: _currencyInfo.blockchain,
          showDecoration: false,
        ),
        Divider(
          color: context.templateColors.divider,
          height: UiConstants.spacing16,
        ),
        buildInfoItem(
          labelText: '流通供应量',
          value:
              '${_formatSupply(_currencyInfo.circulatingSupply)} ${_currencyInfo.symbol}',
          showDecoration: false,
        ),
        buildInfoItem(
          labelText: '最大供应量',
          value:
              '${_formatSupply(_currencyInfo.maxSupply)} ${_currencyInfo.symbol}',
          showDecoration: false,
        ),
        buildInfoItem(
          labelText: '总供应量',
          value:
              '${_formatSupply(_currencyInfo.totalSupply)} ${_currencyInfo.symbol}',
          showDecoration: false,
        ),
        buildInfoItem(
          labelText: '流通率',
          value: '${_currencyInfo.circulationRate.toStringAsFixed(1)}%',
          showDecoration: false,
        ),
      ],
    );
  }

  // 构建所属板块
  Widget _buildMarket() {
    /// 构建板块标签
    Widget buildSectorTag(MarketSector sector) {
      final isPositive = sector.changePercent24h >= 0;
      return TagWidget(
        backgroundColor: context.templateColors.tabbarActive,
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing10,
          vertical: UiConstants.spacing4,
        ),
        radius: UiConstants.borderRadiusCircle,
        margin: EdgeInsets.only(
          right: UiConstants.spacing8,
          bottom: UiConstants.spacing8,
        ),
        child: Text.rich(
          TextSpan(
            style: context.templateStyle.text.tagText,
            children: [
              TextSpan(text: '${sector.name}  '),
              TextSpan(
                text:
                    '${isPositive ? '+' : ''}${sector.changePercent24h.toStringAsFixed(2)}%',
                style: context.templateStyle.text.tagText.copyWith(
                  color:
                      isPositive
                          ? context.templateColors.tradeBuy
                          : context.templateColors.tradeSell,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(top: UiConstants.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('所属板块', style: context.templateStyle.text.h4),
          Padding(
            padding: EdgeInsets.symmetric(vertical: UiConstants.spacing14),
            child: Wrap(
              children:
                  _currencyInfo.sectors
                      .map((sector) => buildSectorTag(sector))
                      .toList(),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(bottom: UiConstants.spacing14),
            child: _buildDescription(),
          ),
        ],
      ),
    );
  }

  // 构建简介部分
  Widget _buildDescription() {
    const int maxLines = 3; // 收起状态下显示的最大行数

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${_currencyInfo.symbol} 简介',
          style: context.templateStyle.text.bodyLargeMedium,
        ),
        SizedBox(height: UiConstants.spacing8),
        AnimatedCrossFade(
          duration: const Duration(milliseconds: 300),
          crossFadeState:
              _isDescriptionExpanded
                  ? CrossFadeState.showSecond
                  : CrossFadeState.showFirst,
          firstChild: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _currencyInfo.description,
                style: context.templateStyle.text.bodyText.copyWith(
                  color: context.templateColors.textSecondary,
                  height: 1.5,
                ),
                maxLines: maxLines,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: UiConstants.spacing8),
              _buildExpandButton(true),
            ],
          ),
          secondChild: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _currencyInfo.description,
                style: context.templateStyle.text.bodyText.copyWith(
                  color: context.templateColors.textSecondary,
                  height: 1.5,
                ),
              ),
              SizedBox(height: UiConstants.spacing8),
              _buildExpandButton(false),
            ],
          ),
        ),
      ],
    );
  }

  // 构建展开/收起按钮
  Widget _buildExpandButton(bool isExpand) {
    return SizedBox(
      width: double.infinity,
      child: InkWellWidget(
        onTap: () {
          setState(() {
            _isDescriptionExpanded = !_isDescriptionExpanded;
          });
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              isExpand ? '显示更多' : '收起',
              style: context.templateStyle.text.bodySmallMedium.copyWith(
                color: context.templateColors.primary,
              ),
            ),
            SizedBox(width: UiConstants.spacing4),
            AnimatedRotation(
              duration: const Duration(milliseconds: 300),
              turns: isExpand ? 0 : 0.5,
              child: Icon(
                Icons.keyboard_arrow_down,
                size: UiConstants.iconSize16,
                color: context.templateColors.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建社区
  Widget _buildCommunity() {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('社区', style: context.templateStyle.text.bodyLargeMedium),
          Padding(
            padding: EdgeInsets.symmetric(vertical: UiConstants.spacing14),
            child: Wrap(
              children:
                  _currencyInfo.communityLinks
                      .map(
                        (link) =>
                            _buildLinkTag(link.name, link.iconName, link.url),
                      )
                      .toList(),
            ),
          ),
        ],
      ),
    );
  }

  // 构建资源
  Widget _buildResources() {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('资源', style: context.templateStyle.text.bodyLargeMedium),
          Padding(
            padding: EdgeInsets.symmetric(vertical: UiConstants.spacing14),
            child: Wrap(
              children:
                  _currencyInfo.resourceLinks
                      .map(
                        (link) =>
                            _buildLinkTag(link.name, link.iconName, link.url),
                      )
                      .toList(),
            ),
          ),
        ],
      ),
    );
  }

  // 构建链接标签
  Widget _buildLinkTag(String name, String iconName, String url) {
    return InkWellWidget(
      onTap: () {
        // TODO: 打开链接
        debugPrint('Opening: $url');
      },
      child: TagWidget(
        backgroundColor: context.templateColors.tabbarActive,
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing10,
          vertical: UiConstants.spacing4,
        ),
        radius: UiConstants.borderRadiusCircle,
        margin: EdgeInsets.only(
          right: UiConstants.spacing8,
          bottom: UiConstants.spacing8,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ThemedImage.asset(
              iconName,
              size: UiConstants.iconSize16,
              margin: EdgeInsets.only(right: UiConstants.spacing4),
            ),
            Text(name, style: context.templateStyle.text.tagText),
          ],
        ),
      ),
    );
  }

  // 免责声明
  Widget _buildDisclaimer() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing32),
      child: Text.rich(
        TextSpan(
          style: context.templateStyle.text.hintText,
          children: [
            TextSpan(text: '免责声明：含第三方数据，不作为投资决策参考。详情见'),
            TextSpan(
              text: '风险提示',
              style: context.templateStyle.text.hintTextMedium.copyWith(
                color: context.templateColors.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
