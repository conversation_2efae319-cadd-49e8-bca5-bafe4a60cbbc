/*
*  代币经济学标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class TokenEconomicsTab extends StatefulWidget {
  const TokenEconomicsTab({super.key});

  @override
  State<TokenEconomicsTab> createState() => _TokenEconomicsTabState();
}

class _TokenEconomicsTabState extends State<TokenEconomicsTab> {
  // 独特性展开状态
  bool _isUniquenessExpanded = false;

  // 代币效用展开状态
  bool _isUtilityExpanded = false;

  // 独特性内容
  final String _uniquenessDescription =
      'Bitcoin作为世界上第一个成功的加密货币，具有无可替代的独特性。它是去中心化数字货币的先驱，建立了区块链技术的基础范式。\n\nBitcoin的独特之处在于其完全去中心化的特性，没有中央发行机构或控制实体。网络由全球数千个节点维护，任何人都可以参与验证交易和维护网络安全。\n\n其工作量证明共识机制经过十多年的实战检验，证明了其安全性和可靠性。Bitcoin网络从未被成功攻击过，展现了其强大的抗审查能力和不可篡改性。\n\n作为"数字黄金"，Bitcoin已成为价值存储的重要选择，被机构投资者和个人投资者广泛认可。';

  // 代币效用内容
  final String _utilityDescription =
      'Bitcoin的核心效用体现在其作为去中心化数字货币的功能。它可以实现全球范围内的点对点价值传输，无需依赖传统金融机构。\n\n作为价值存储工具，Bitcoin提供了对抗通胀和货币贬值的保护。其固定供应量设计使其成为稀缺资产，具有长期保值增值的潜力。\n\nBitcoin还具有优秀的可分割性和便携性。每个比特币可以分割到小数点后8位（聪），使得小额交易成为可能。同时，Bitcoin可以轻松跨境转移，为国际贸易和汇款提供了高效解决方案。\n\n在DeFi生态中，Bitcoin通过包装代币（如WBTC）的形式参与各种金融协议，扩展了其应用场景和效用价值。';

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.all(UiConstants.spacing18),
        child: Column(
          children: [
            // 独特性介绍
            _buildUniquenessIntroduction(),

            SizedBox(height: UiConstants.spacing16),

            // 代币效用介绍
            _buildUtilityIntroduction(),

            SizedBox(height: UiConstants.spacing16),

            // 查看更多反馈
            _buildMoreView(),

            // 免责声明
            _buildDisclaimer(),
          ],
        ),
      ),
    );
  }

  // 构建独特性介绍
  Widget _buildUniquenessIntroduction() {
    const int maxLines = 3; // 收起状态下显示的最大行数

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('独特性', style: context.templateStyle.text.bodyLargeMedium),
        SizedBox(height: UiConstants.spacing8),
        AnimatedCrossFade(
          duration: const Duration(milliseconds: 300),
          crossFadeState:
              _isUniquenessExpanded
                  ? CrossFadeState.showSecond
                  : CrossFadeState.showFirst,
          firstChild: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _uniquenessDescription,
                style: context.templateStyle.text.bodyText.copyWith(
                  color: context.templateColors.textSecondary,
                  height: 1.5,
                ),
                maxLines: maxLines,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: UiConstants.spacing8),
              _buildUniquenessExpandButton(true),
            ],
          ),
          secondChild: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _uniquenessDescription,
                style: context.templateStyle.text.bodyText.copyWith(
                  color: context.templateColors.textSecondary,
                  height: 1.5,
                ),
              ),
              SizedBox(height: UiConstants.spacing8),
              _buildUniquenessExpandButton(false),
            ],
          ),
        ),
      ],
    );
  }

  // 构建代币效用介绍
  Widget _buildUtilityIntroduction() {
    const int maxLines = 3; // 收起状态下显示的最大行数

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('代币效用', style: context.templateStyle.text.bodyLargeMedium),
        SizedBox(height: UiConstants.spacing8),
        AnimatedCrossFade(
          duration: const Duration(milliseconds: 300),
          crossFadeState:
              _isUtilityExpanded
                  ? CrossFadeState.showSecond
                  : CrossFadeState.showFirst,
          firstChild: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _utilityDescription,
                style: context.templateStyle.text.bodyText.copyWith(
                  color: context.templateColors.textSecondary,
                  height: 1.5,
                ),
                maxLines: maxLines,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: UiConstants.spacing8),
              _buildUtilityExpandButton(true),
            ],
          ),
          secondChild: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _utilityDescription,
                style: context.templateStyle.text.bodyText.copyWith(
                  color: context.templateColors.textSecondary,
                  height: 1.5,
                ),
              ),
              SizedBox(height: UiConstants.spacing8),
              _buildUtilityExpandButton(false),
            ],
          ),
        ),
      ],
    );
  }

  // 构建独特性展开/收起按钮
  Widget _buildUniquenessExpandButton(bool isExpand) {
    return SizedBox(
      width: double.infinity,
      child: InkWellWidget(
        onTap: () {
          setState(() {
            _isUniquenessExpanded = !_isUniquenessExpanded;
          });
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              isExpand ? '显示更多' : '收起',
              style: context.templateStyle.text.bodySmallMedium.copyWith(
                color: context.templateColors.primary,
              ),
            ),
            SizedBox(width: UiConstants.spacing4),
            AnimatedRotation(
              duration: const Duration(milliseconds: 300),
              turns: isExpand ? 0 : 0.5,
              child: Icon(
                Icons.keyboard_arrow_down,
                size: UiConstants.iconSize16,
                color: context.templateColors.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建代币效用展开/收起按钮
  Widget _buildUtilityExpandButton(bool isExpand) {
    return SizedBox(
      width: double.infinity,
      child: InkWellWidget(
        onTap: () {
          setState(() {
            _isUtilityExpanded = !_isUtilityExpanded;
          });
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              isExpand ? '显示更多' : '收起',
              style: context.templateStyle.text.bodySmallMedium.copyWith(
                color: context.templateColors.primary,
              ),
            ),
            SizedBox(width: UiConstants.spacing4),
            AnimatedRotation(
              duration: const Duration(milliseconds: 300),
              turns: isExpand ? 0 : 0.5,
              child: Icon(
                Icons.keyboard_arrow_down,
                size: UiConstants.iconSize16,
                color: context.templateColors.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建查看更多
  Widget _buildMoreView() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          width: context.templateStyles.borderWidthThin,
          color: context.templateColors.border,
        ),
        borderRadius: BorderRadius.circular(
          context.templateStyles.borderRadiusMedium,
        ),
      ),
      padding: EdgeInsets.all(UiConstants.spacing14),
      child: Row(
        children: [
          Icon(
            RemixIcons.information_2_fill,
            size: UiConstants.iconSize16,
            color: context.templateColors.textSecondary,
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing6),
              child: Text(
                '信息有误？想看更多？点击这里反馈。',
                style: context.templateStyle.text.descriptionSmall,
              ),
            ),
          ),
          Icon(
            RemixIcons.arrow_right_s_line,
            size: UiConstants.iconSize20,
            color: context.templateColors.textSecondary,
          ),
        ],
      ),
    );
  }

  // 免责声明
  Widget _buildDisclaimer() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing32),
      child: Text.rich(
        TextSpan(
          style: context.templateStyle.text.hintText,
          children: [
            TextSpan(text: '免责声明：含第三方数据，不作为投资决策参考。详情见'),
            TextSpan(
              text: '风险提示',
              style: context.templateStyle.text.hintTextMedium.copyWith(
                color: context.templateColors.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
