/*
*  成交标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';

class FilledTabView extends StatefulWidget {
  final ScrollPhysics? physics;
  const FilledTabView({super.key, this.physics});

  @override
  State<FilledTabView> createState() => _FilledTabViewState();
}

class _FilledTabViewState extends State<FilledTabView> {
  @override
  Widget build(BuildContext context) {
    return Column(children: [_buildFilledHeader(), _buildFilledList()]);
  }

  // 构建成交列表
  Widget _buildFilledList() {
    return Flexible(
      child: ListView.builder(
        physics: widget.physics,
        itemBuilder: (context, index) => _buildFilledItem(),
        itemCount: 10,
      ),
    );
  }

  // 构建成交项
  Widget _buildFilledItem() {
    return Container(
      height: 26,
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: Row(
        children: [
          Expanded(
            child: Text(
              '15:15:39',
              style: context.templateStyle.text.bodySmall,
            ),
          ),
          Expanded(
            child: Text(
              '118,822.76',
              style: context.templateStyle.text.bodySmall.copyWith(
                color: context.templateColors.tradeBuy,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            child: Text(
              '0.002412',
              style: context.templateStyle.text.bodySmall,
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  // 构建成交表头
  Widget _buildFilledHeader() {
    return Container(
      height: 28,
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: Row(
        children: [
          Expanded(
            child: Text(
              '时间',
              style: context.templateStyle.text.descriptionSmall,
            ),
          ),
          Expanded(
            child: Text(
              '价格(USDT)',
              style: context.templateStyle.text.descriptionSmall,
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            child: Text(
              '数量(BTC)',
              style: context.templateStyle.text.descriptionSmall,
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }
}
