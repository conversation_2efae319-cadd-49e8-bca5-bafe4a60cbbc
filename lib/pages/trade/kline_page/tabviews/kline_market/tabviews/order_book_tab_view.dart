/*
*  订单簿标签页内容
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'dart:async';
import 'dart:math' as math;
import 'package:qubic_exchange/pages/trade/widgets/orderbook_depth_bottom_sheet.dart';

/// 订单簿数据项
class OrderBookItem {
  final double price;
  final double amount;
  final double total;
  final double percentage; // 用于背景条宽度
  final String id; // 用于跟踪动画状态

  OrderBookItem({
    required this.price,
    required this.amount,
    required this.total,
    required this.percentage,
    required this.id,
  });
}

class OrderBookTabView extends StatefulWidget {
  final ScrollPhysics? physics;
  const OrderBookTabView({super.key, this.physics});

  @override
  State<OrderBookTabView> createState() => _OrderBookTabViewState();
}

class _OrderBookTabViewState extends State<OrderBookTabView> {
  late String _selectedDepth = '0.001';

  // 模拟数据
  List<OrderBookItem> _sellOrders = [];
  List<OrderBookItem> _buyOrders = [];
  late Timer _dataUpdateTimer;
  final math.Random _random = math.Random();
  @override
  void initState() {
    super.initState();
    _initMockData();

    // 启动数据更新定时器，每2秒更新一次
    _dataUpdateTimer = Timer.periodic(
      const Duration(seconds: 2),
      (timer) => _updateMockData(),
    );
  }

  @override
  void dispose() {
    _dataUpdateTimer.cancel();
    super.dispose();
  }

  /// 初始化模拟数据
  void _initMockData() {
    // 生成卖盘数据（从低到高价格）
    _sellOrders = List.generate(15, (index) {
      final price = 115647.21 + (index * 0.01);
      final amount = 0.1 + (_random.nextDouble() * 0.9);
      return OrderBookItem(
        price: price,
        amount: amount,
        total: price * amount,
        percentage: 0.3 + (_random.nextDouble() * 0.7),
        id: 'sell_$index',
      );
    });

    // 生成买盘数据（从高到低价格）
    _buyOrders = List.generate(15, (index) {
      final price = 115647.20 - (index * 0.01);
      final amount = 0.1 + (_random.nextDouble() * 0.9);
      return OrderBookItem(
        price: price,
        amount: amount,
        total: price * amount,
        percentage: 0.3 + (_random.nextDouble() * 0.7),
        id: 'buy_$index',
      );
    });
  }

  /// 更新模拟数据
  void _updateMockData() {
    if (!mounted) return;

    // 更新卖盘数据
    for (int i = 0; i < _sellOrders.length; i++) {
      final oldItem = _sellOrders[i];
      final amount = (oldItem.amount + (_random.nextDouble() - 0.5) * 0.1)
          .clamp(0.1, 1.0);
      _sellOrders[i] = OrderBookItem(
        price: oldItem.price,
        amount: amount,
        total: oldItem.price * amount,
        percentage: 0.3 + (_random.nextDouble() * 0.7),
        id: oldItem.id,
      );
    }

    // 更新买盘数据
    for (int i = 0; i < _buyOrders.length; i++) {
      final oldItem = _buyOrders[i];
      final amount = (oldItem.amount + (_random.nextDouble() - 0.5) * 0.1)
          .clamp(0.1, 1.0);
      _buyOrders[i] = OrderBookItem(
        price: oldItem.price,
        amount: amount,
        total: oldItem.price * amount,
        percentage: 0.3 + (_random.nextDouble() * 0.7),
        id: oldItem.id,
      );
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing14,
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildOrderOperationBar(),
          _buildOrderList(),
        ],
      ),
    );
  }

  // 顶部条幅
  Widget _buildHeader() {
    return RatioProgressBar(
      layout: RatioBarLayout.horizontal,
      bullishRatio: 56,
      bearishRatio: 44,
      bullishText: 'B',
      bearishText: 'S',
    );
  }

  // 买卖订单操作栏
  Widget _buildOrderOperationBar() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing8),
      child: Row(
        children: [
          // 左侧：买盘标题
          Expanded(
            child: Text(
              '买',
              style: context.templateStyle.text.descriptionSmall.copyWith(
                color: context.templateColors.textSecondary,
              ),
            ),
          ),

          // 右侧：卖盘标题
          Expanded(
            child: Text(
              '卖',
              style: context.templateStyle.text.descriptionSmall.copyWith(
                color: context.templateColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          Expanded(
            child: InkWellWidget(
              onTap:
                  () => {
                    OrderbookDepthBottomSheet.show(
                      context,
                      depths: ['0.001', '0.01', '0.1', '1.0'],
                      selectedDepth: '0.001',
                      onSelected: (depth) {
                        setState(() {
                          _selectedDepth = depth;
                        });
                      },
                    ),
                  },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ThemedImage(
                    name: 'icon_contract_order_book_both_left',
                    size: 14,
                    followTheme: true,
                    margin: EdgeInsets.only(right: UiConstants.spacing8),
                  ),
                  Text(
                    _selectedDepth,
                    style: context.templateStyle.text.descriptionSmall.copyWith(
                      color: context.templateColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 买卖订单列表
  Widget _buildOrderList() {
    // 确保两侧数据长度一致
    final maxLength = math.max(_buyOrders.length, _sellOrders.length);

    return Expanded(
      child: ListView.builder(
        physics: widget.physics,
        itemCount: maxLength,
        itemBuilder: (context, index) {
          return Row(
            children: [
              // 左侧：买盘
              Expanded(
                child:
                    index < _buyOrders.length
                        ? _buildOrderItem(
                          item: _buyOrders[index],
                          isSell: false,
                          isLeftSide: true,
                        )
                        : SizedBox(height: 20), // 空占位
              ),

              // 右侧：卖盘
              Expanded(
                child:
                    index < _sellOrders.length
                        ? _buildOrderItem(
                          item: _sellOrders[index],
                          isSell: true,
                          isLeftSide: false,
                        )
                        : SizedBox(height: 20), // 空占位
              ),
            ],
          );
        },
      ),
    );
  }

  /// 构建订单项
  Widget _buildOrderItem({
    required OrderBookItem item,
    required bool isSell,
    required bool isLeftSide,
  }) {
    return AnimatedOrderItem(
      key: ValueKey(item.id),
      item: item,
      isSell: isSell,
      isLeftSide: isLeftSide,
    );
  }
}

/// 带动画的订单项组件
class AnimatedOrderItem extends StatefulWidget {
  final OrderBookItem item;
  final bool isSell;
  final bool isLeftSide;

  const AnimatedOrderItem({
    super.key,
    required this.item,
    required this.isSell,
    required this.isLeftSide,
  });

  @override
  State<AnimatedOrderItem> createState() => _AnimatedOrderItemState();
}

class _AnimatedOrderItemState extends State<AnimatedOrderItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  double _previousPercentage = 0.0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _previousPercentage = widget.item.percentage;
    _animation = Tween<double>(
      begin: _previousPercentage,
      end: widget.item.percentage,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
    _controller.forward();
  }

  @override
  void didUpdateWidget(AnimatedOrderItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.item.percentage != widget.item.percentage) {
      _previousPercentage = oldWidget.item.percentage;
      _animation = Tween<double>(
        begin: _previousPercentage,
        end: widget.item.percentage,
      ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final backgroundColor =
        widget.isSell
            ? context.templateColors.tradeSell.withValues(alpha: 0.1)
            : context.templateColors.tradeBuy.withValues(alpha: 0.1);

    final textColor =
        widget.isSell
            ? context.templateColors.tradeSell
            : context.templateColors.tradeBuy;

    return SizedBox(
      height: 20,
      child: Stack(
        children: [
          // 背景进度条（带动画）
          Align(
            alignment:
                widget.isLeftSide
                    ? Alignment
                        .centerRight // 买区从右到左
                    : Alignment.centerLeft, // 卖区从左到右
            child: AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return FractionallySizedBox(
                  widthFactor: _animation.value.clamp(0.0, 1.0),
                  child: Container(
                    height: 20,
                    decoration: BoxDecoration(color: backgroundColor),
                  ),
                );
              },
            ),
          ),

          // 前景内容
          Padding(
            padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
            child: Row(
              children:
                  widget.isLeftSide
                      ? [
                        // 左侧买盘：数量 | 价格
                        Expanded(
                          child: Text(
                            widget.item.amount.toStringAsFixed(6),
                            style: context.templateStyle.text.descriptionSmall
                                .copyWith(
                                  color: context.templateColors.textSecondary,
                                ),
                            textAlign: TextAlign.left,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            widget.item.price.toStringAsFixed(2),
                            style: context.templateStyle.text.descriptionSmall
                                .copyWith(
                                  color: textColor,
                                  fontWeight: FontWeight.w500,
                                ),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ]
                      : [
                        // 右侧卖盘：价格 | 数量
                        Expanded(
                          child: Text(
                            widget.item.price.toStringAsFixed(2),
                            style: context.templateStyle.text.descriptionSmall
                                .copyWith(
                                  color: textColor,
                                  fontWeight: FontWeight.w500,
                                ),
                            textAlign: TextAlign.left,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            widget.item.amount.toStringAsFixed(6),
                            style: context.templateStyle.text.descriptionSmall
                                .copyWith(
                                  color: context.templateColors.textSecondary,
                                ),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ],
            ),
          ),
        ],
      ),
    );
  }
}
