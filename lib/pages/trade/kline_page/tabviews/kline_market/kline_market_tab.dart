/*
* K线行情标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import './widgets/index.dart';
import './tabviews/order_book_tab_view.dart';
import './tabviews/filled_tab_view.dart';

class KlineMarketTab extends StatefulWidget {
  const KlineMarketTab({super.key});

  @override
  State<KlineMarketTab> createState() => _KlineMarketTabState();
}

class _KlineMarketTabState extends State<KlineMarketTab>
    with TickerProviderStateMixin {
  // 构建标签控制器
  late TabController _tabController;
  // 构建标签内容
  final List<TabItem> _tabs = [TabItem(title: '订单簿'), TabItem(title: '成交')];
  // 初始化
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  // 销毁
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final maxOffset = 44.0;
    return EasyRefresh.builder(
      header: ClassicHeader(
        clamping: true,
        position: IndicatorPosition.locator,
        triggerOffset: maxOffset,
        processedDuration: const Duration(seconds: 1),
        safeArea: false,
        showMessage: false,
        showText: false,
        maxOverOffset: maxOffset,
      ),
      childBuilder: (context, physics) {
        return ScrollConfiguration(
          behavior: const ERScrollBehavior(),
          child: ExtendedNestedScrollView(
            physics: physics,
            onlyOneScrollInBody: true,
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return <Widget>[
                // 刷新指示器定位器
                const HeaderLocator.sliver(clearExtent: false),

                // 顶部区域
                SliverToBoxAdapter(child: _buildHeader()),

                // 标签栏
                SliverToBoxAdapter(child: _buildTabbar()),
              ];
            },
            body: _buildTabView(physics),
          ),
        );
      },
    );
  }

  // 构建顶部内容
  Widget _buildHeader() {
    return Container(
      child: Column(
        children: [
          // 价格
          KlineCryptoPrice(),

          // K线图
          // KlineChartArea(),

          // 新币倒计时
          KlineNewCoinCountDown(height: 340),
        ],
      ),
    );
  }

  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
      child: TabbarWidget(
        controller: _tabController,
        tabs: _tabs,
        height: 28,
        labelStyle: context.templateStyle.text.bodyTextMedium,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: true,
        indicatorSize: TabBarIndicatorSize.label,
        labelPadding: EdgeInsets.only(right: UiConstants.spacing18),
      ),
    );
  }

  Widget _buildTabView(ScrollPhysics physics) {
    return TabBarView(
      controller: _tabController,
      children: [
        /// 订单簿
        OrderBookTabView(physics: physics),

        /// 成交
        FilledTabView(physics: physics),
      ],
    );
  }
}
