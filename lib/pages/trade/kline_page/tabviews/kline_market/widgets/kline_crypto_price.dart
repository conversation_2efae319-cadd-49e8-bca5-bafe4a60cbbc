/*
*  K线行情标签页 - 数字货币价格组件
*/

import 'package:flutter/material.dart';
import 'package:remixicon/remixicon.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class KlineCryptoPrice extends StatefulWidget {
  const KlineCryptoPrice({super.key});

  @override
  State<KlineCryptoPrice> createState() => _KlineCryptoPriceState();
}

class _KlineCryptoPriceState extends State<KlineCryptoPrice> {
  // 公告是否已关闭的状态
  bool _isNoticeClosed = false;

  // SharedPreferences 存储键
  static const String _noticeClosedKey = 'kline_crypto_notice_closed';

  @override
  void initState() {
    super.initState();
    _loadNoticeState();
  }

  // 加载公告关闭状态
  Future<void> _loadNoticeState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isClosed = prefs.getBool(_noticeClosedKey) ?? false;
      if (mounted) {
        setState(() {
          _isNoticeClosed = isClosed;
        });
      }
    } catch (e) {
      // 如果加载失败，保持默认状态（显示公告）
      debugPrint('加载公告状态失败: $e');
    }
  }

  // 关闭公告
  Future<void> _closeNotice() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_noticeClosedKey, true);
      if (mounted) {
        setState(() {
          _isNoticeClosed = true;
        });
      }
    } catch (e) {
      // 如果保存失败，仍然更新UI状态
      debugPrint('保存公告状态失败: $e');
      if (mounted) {
        setState(() {
          _isNoticeClosed = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            width: context.templateStyles.borderWidthThin,
            color: context.templateColors.divider,
          ),
        ),
      ),
      child: Column(
        children: [
          _buildPriceArea(),
          // 只有在公告未关闭时才显示
          if (!_isNoticeClosed) _buildNotice(),
        ],
      ),
    );
  }

  // 价格区域
  Widget _buildPriceArea() {
    final rightDescStyle = context.templateStyle.text.descriptionSmall.copyWith(
      fontSize: UiConstants.fontSize10,
    );
    final rightValueStyle = context.templateStyle.text.bodySmall.copyWith(
      fontSize: UiConstants.fontSize10,
    );
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: UiConstants.spacing12,
        horizontal: UiConstants.spacing18,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 4,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '119,177.62',
                  style: context.templateStyle.text.h1.copyWith(
                    color: context.templateColors.tradeBuy,
                  ),
                ),
                Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: '≈ ¥ 848,586.21  ',
                        style: context.templateStyle.text.bodySmallMedium,
                      ),
                      TextSpan(
                        text: '- 0.13%',
                        style: context.templateStyle.text.bodySmallMedium
                            .copyWith(color: context.templateColors.tradeSell),
                      ),
                    ],
                  ),
                ),
                TagWidget(
                  text: '公链',
                  borderColor: context.templateColors.border,
                  backgroundColor: context.templateColors.surface,
                  textStyle: context.templateStyle.text.hintSmall,
                  padding: EdgeInsets.symmetric(
                    horizontal: UiConstants.spacing4,
                  ),
                  margin: EdgeInsets.only(top: UiConstants.spacing8),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 3,
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('24h最高价', style: rightDescStyle),
                    Text('120,177.62', style: rightValueStyle),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('24h最低价', style: rightDescStyle),
                    Text('120,177.62', style: rightValueStyle),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('24h成交量(BTC)', style: rightDescStyle),
                    Text('120,177.62', style: rightValueStyle),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('24h成交额(USDT)', style: rightDescStyle),
                    Text('120,177.62', style: rightValueStyle),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 公告
  Widget _buildNotice() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing8,
      ),
      child: Row(
        children: [
          ThemedImage(name: '', size: 14),
          Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
              child: Text(
                '2025 KCGI西甲荣耀！争夺600万美金终极奖池！',
                style: context.templateStyle.text.descriptionSmall,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          InkWellWidget(
            onTap: _closeNotice,
            child: Icon(
              RemixIcons.close_line,
              size: UiConstants.iconSize20,
              color: context.templateColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}
