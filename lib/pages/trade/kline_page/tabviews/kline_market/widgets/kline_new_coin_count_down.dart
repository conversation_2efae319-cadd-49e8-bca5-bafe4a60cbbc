/*
*  K线行情标签页 - 新币倒计时
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class KlineNewCoinCountDown extends StatefulWidget {
  final double height;

  const KlineNewCoinCountDown({super.key, required this.height});

  @override
  State<KlineNewCoinCountDown> createState() => _KlineNewCoinCountDownState();
}

class _KlineNewCoinCountDownState extends State<KlineNewCoinCountDown> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ThemedImage.crypto(
                'btc',
                size: UiConstants.iconSize28,
                borderRadius: BorderRadius.circular(
                  UiConstants.borderRadiusCircle,
                ),
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: UiConstants.spacing16),
            child: Text('即将上线', style: context.templateStyle.text.h4),
          ),
          CountdownWidget(
            totalSeconds: 300,
            format: CountdownFormat.dayHourMinuteSecond,
            textStyle: context.templateStyle.text.bodyLargeMedium,
            showLabels: true,
            labelTexts: {'days': '天'},
            labelStyle: context.templateStyle.text.bodyLargeMedium,
            numberDecoration: BoxDecoration(
              color: context.templateColors.tabbarActive,
              borderRadius: BorderRadius.circular(UiConstants.borderRadius4),
            ),
            onFinished: () {
              // TODO 倒计时完成执行事件
            },
          ),
        ],
      ),
    );
  }
}
