/*
*  === 启动引导页 ===
*
*  启动逻辑：
*  - 仅在首次启动时显示
*  - 用户点击按钮后跳转到注册页面并标记为非首次启动
*  - 后续启动的路由判断在 main.dart 中处理
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/l10n/index.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/routes/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/services/core/first_launch_service.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  /// 处理用户点击继续按钮
  Future<void> _handleContinuePressed() async {
    try {
      // 标记应用已启动过
      await FirstLaunchService.instance.markAsLaunched();

      // 跳转到注册界面，使用从右侧划入动画
      NavigationService().navigateToAndClearStack(
        AppRoutes.register,
        transition: 'slideRight',
      );
    } catch (e) {
      // 即使失败也跳转到注册页面
      NavigationService().navigateToAndClearStack(
        AppRoutes.register,
        transition: 'slideRight',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final imageSize = 150.0;
    final localizations = AppLocalizations.of(context);
    return Scaffold(
      backgroundColor: context.templateColors.surface,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
          child: Column(
            children: [
              /// 顶部图片和文字
              Expanded(
                flex: 4,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ThemedImage.asset(
                      'icon_allow_logo',
                      size: imageSize,
                      followTheme: true,
                    ),

                    /// 标题
                    Padding(
                      padding: EdgeInsets.only(
                        top: UiConstants.spacing24,
                        bottom: UiConstants.spacing10,
                      ),
                      child: Text(
                        localizations.welcomeTitle,
                        textAlign: TextAlign.center,
                        style: context.templateStyle.text.h1,
                      ),
                    ),

                    /// 副标题
                    Text(
                      localizations.welcomeSubtitle,
                      style: context.templateStyle.text.bodyText,
                    ),
                  ],
                ),
              ),
              Spacer(),

              // 按钮
              CommonButton.primary(
                localizations.agreeAndContinue,
                width: double.infinity,
                onPressed: _handleContinuePressed,
              ),

              // 协议
              Padding(
                padding: EdgeInsets.only(top: UiConstants.spacing10),
                child: Text.rich(
                  TextSpan(
                    style: context.templateStyle.text.hintText,
                    children: [
                      TextSpan(text: localizations.continueMeansAgree),
                      TextSpan(
                        text: ' ${localizations.userAgreement} ',
                        style: TextStyle(color: context.templateColors.primary),
                      ),
                      TextSpan(text: localizations.and),
                      TextSpan(
                        text: ' ${localizations.privacyPolicy} ',
                        style: TextStyle(color: context.templateColors.primary),
                      ),
                      TextSpan(text: localizations.disagreeCloseApp),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
