/*
*  人机验证方式切换弹窗
*
*  功能：
*  - 提供极验验证和谷歌验证两种验证方式选择
*  - 使用底部弹窗组件展示选项
*  - 默认选中第一项（极验验证）
*  - 支持选中状态显示和回调
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

/// 验证方式选项枚举
enum CaptchaType {
  geetest('geetest', '极验验证'),
  google('google', '谷歌验证');

  const CaptchaType(this.key, this.displayName);
  final String key;
  final String displayName;
}

/// 验证方式切换对话框
class CaptchaSwitchDialog {
  /// 显示验证方式选择底部弹窗
  static Future<CaptchaType?> show(
    BuildContext context, {
    CaptchaType? selectedType,
    String title = '切换验证方式',
    TextStyle? titleStyle,
    TextAlign titleAlign = TextAlign.left,
    bool showHeader = true,
    bool showCloseButton = false,
    bool showDragHandle = true,
    bool showHeaderBorder = false,
    double? maxHeight,
    double? headerHeight,
    bool useSafeArea = false,
    EdgeInsets padding = EdgeInsets.zero,
    Function(CaptchaType)? onSelected,
  }) {
    return BottomSheetWidget.show<CaptchaType>(
      context: context,
      title: title,
      titleStyle: titleStyle,
      titleAlign: titleAlign,
      showCloseButton: showCloseButton,
      showHeader: showHeader,
      showDragHandle: showDragHandle,
      headerHeight: headerHeight ?? UiConstants.buttonHeightMedium,
      showHeaderBorder: showHeaderBorder,
      maxHeight: maxHeight ?? MediaQuery.of(context).size.height * 0.6,
      showBottomButton: false,
      useSafeArea: useSafeArea,
      padding: padding,
      child: _CaptchaSwitchContent(
        selectedType: selectedType ?? CaptchaType.geetest,
        onSelected: onSelected,
      ),
    );
  }
}

/// 验证方式选择内容组件
class _CaptchaSwitchContent extends StatefulWidget {
  final CaptchaType selectedType;
  final Function(CaptchaType)? onSelected;

  const _CaptchaSwitchContent({required this.selectedType, this.onSelected});

  @override
  State<_CaptchaSwitchContent> createState() => _CaptchaSwitchContentState();
}

class _CaptchaSwitchContentState extends State<_CaptchaSwitchContent> {
  late CaptchaType _selectedType;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.selectedType;
  }

  @override
  Widget build(BuildContext context) {
    return _buildContent();
  }

  /// 构建选项列表
  Widget _buildContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 渲染验证方式选项列表
        ...CaptchaType.values.map(
          (type) => _buildSelectItem(
            type: type,
            isSelected: _selectedType == type,
            onTap: () => _handleOptionSelected(type),
          ),
        ),

        // 添加底部间距
        SizedBox(height: UiConstants.spacing16),
      ],
    );
  }

  /// 处理选项选择
  void _handleOptionSelected(CaptchaType type) {
    setState(() {
      _selectedType = type;
    });

    // 调用回调函数
    widget.onSelected?.call(type);

    // 立即关闭弹窗并返回结果
    Navigator.of(context).pop(type);
  }

  /// 构建选择项
  Widget _buildSelectItem({
    required CaptchaType type,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWellWidget(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: UiConstants.spacing16,
          horizontal: UiConstants.spacing16,
        ),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? context.templateColors.buttonSecondary
                  : context.templateColors.popupBackground,
          border: Border(
            bottom: BorderSide(
              width: UiConstants.borderWidth1,
              color: context.templateColors.divider.withAlpha(50),
            ),
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                type.displayName,
                style: context.templateStyle.text.bodyLargeMedium,
              ),
            ),
            if (isSelected)
              ThemedImage.asset(
                'icon_checked',
                width: UiConstants.iconSize20,
                height: UiConstants.iconSize20,
                followTheme: true,
              ),
          ],
        ),
      ),
    );
  }
}
