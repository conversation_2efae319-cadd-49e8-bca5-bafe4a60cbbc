/*
*  登录界面
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/routes/index.dart';
import 'package:qubic_exchange/l10n/index.dart';
import 'widgets/index.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> with TickerProviderStateMixin {
  late final TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // 处理注册按钮点击
  void _handleRegisterButtonPressed() {
    final navigationService = NavigationService();
    navigationService.isPreviousRoute(AppRoutes.register)
        ? navigationService.goBack()
        : navigationService.navigateTo(AppRoutes.register, transition: 'slideLeft');
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return Scaffold(
      backgroundColor: context.templateColors.surface,
      resizeToAvoidBottomInset: false,
      appBar: AppBarWidget(
        showBackButton: true,
        onBackPressed: () {
          // 使用正常的返回，会触发向下滑出动画
          NavigationService().goBack();
        },
        actions: [CommonButton(text: localizations.register, type: CommonButtonType.text, onPressed: () => _handleRegisterButtonPressed())],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 主体部分
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  Padding(
                    padding: EdgeInsets.only(left: UiConstants.spacing18, right: UiConstants.spacing18, bottom: UiConstants.spacing28),
                    child: TextWidget(text: localizations.welcomeBack, style: context.templateStyle.text.h1),
                  ),

                  // 标签栏
                  LoginTab(controller: _tabController),

                  // 登录表单
                  LoginForm(controller: _tabController),
                ],
              ),
            ),
          ),
          // 底部其他登陆方式
          SafeArea(bottom: false, child: LoginOtherOptions()),
        ],
      ),
    );
  }
}
