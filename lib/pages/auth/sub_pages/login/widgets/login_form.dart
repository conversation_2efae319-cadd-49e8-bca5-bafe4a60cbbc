/*
*  表单
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/routes/index.dart';
import 'package:qubic_exchange/models/pages/auth/login/index.dart';
import 'package:qubic_exchange/services/pages/auth/login/index.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';
import 'package:qubic_exchange/models/user/user_info.dart';
import 'package:provider/provider.dart';

class LoginForm extends StatefulWidget {
  final TabController controller;

  const LoginForm({super.key, required this.controller});

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  // 控制器
  final TextEditingController _emailPhoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _subAccountController = TextEditingController();
  final TextEditingController _subPasswordController = TextEditingController();

  // 状态变量
  int _currentTabIndex = 0;
  bool _showPasswordInput = false; // 控制是否显示密码输入框

  // 错误状态
  String? _emailPhoneError;
  String? _passwordError;
  String? _subAccountError;
  String? _subPasswordError;

  @override
  void initState() {
    super.initState();
    _currentTabIndex = widget.controller.index;
    widget.controller.addListener(_onTabChanged);
  }

  void _onTabChanged() {
    if (_currentTabIndex != widget.controller.index) {
      // 清除当前焦点
      FocusScope.of(context).unfocus();

      setState(() {
        _currentTabIndex = widget.controller.index;
        _showPasswordInput = false; // 切换tab时重置密码输入框显示状态
        _clearErrors();
      });
    }
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTabChanged);
    _emailPhoneController.dispose();
    _passwordController.dispose();
    _subAccountController.dispose();
    _subPasswordController.dispose();
    super.dispose();
  }

  // 清除错误状态
  void _clearErrors() {
    _emailPhoneError = null;
    _passwordError = null;
    _subAccountError = null;
    _subPasswordError = null;
  }

  // 验证邮箱格式
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // 验证手机号码格式
  bool _isValidPhone(String phone) {
    // 去掉所有空格后再验证
    final cleanPhone = phone.replaceAll(' ', '');
    return RegExp(r'^1[3-9]\d{9}$').hasMatch(cleanPhone);
  }

  // 处理下一步或登录按钮点击
  void _handleMainAccountLogin() {
    _clearErrors();

    if (!_showPasswordInput) {
      // 第一步：验证账户输入
      _validateAccountAndShowPassword();
    } else {
      // 第二步：验证密码并执行登录
      _validatePasswordAndLogin();
    }
  }

  // 验证账户输入并显示密码框
  void _validateAccountAndShowPassword() {
    // 验证邮箱/手机号
    if (_emailPhoneController.text.trim().isEmpty) {
      setState(() {
        _emailPhoneError = '请输入邮箱或手机号码';
      });
      return;
    }

    final input = _emailPhoneController.text.trim();
    // 去掉空格后再验证
    final cleanInput = input.replaceAll(' ', '');
    if (!_isValidEmail(cleanInput) && !_isValidPhone(input)) {
      setState(() {
        _emailPhoneError = '请输入有效的邮箱或手机号码';
      });
      return;
    }

    // 格式验证通过，直接显示密码输入框
    setState(() {
      _showPasswordInput = true;
      _emailPhoneError = null;
    });

    debugPrint('✅ 邮箱/手机号格式验证通过，显示密码输入框');
  }

  // 验证密码并执行登录
  void _validatePasswordAndLogin() {
    // 验证密码
    if (_passwordController.text.trim().isEmpty) {
      _passwordError = '请输入密码';
      setState(() {});
      return;
    }

    // 执行登录逻辑
    final account = _emailPhoneController.text.trim();
    final cleanAccount = account.replaceAll(' ', '');
    _performLogin(_isValidEmail(cleanAccount) ? 'email' : 'phone', cleanAccount, _passwordController.text.trim());
  }

  // 子账户登录
  void _handleSubAccountLogin() {
    _clearErrors();

    // 验证子账户名
    if (_subAccountController.text.trim().isEmpty) {
      _subAccountError = '请输入子账户名';
      setState(() {});
      return;
    }

    // 验证密码
    if (_subPasswordController.text.trim().isEmpty) {
      _subPasswordError = '请输入密码';
      setState(() {});
      return;
    }

    // 执行登录逻辑
    _performLogin('sub', _subAccountController.text.trim(), _subPasswordController.text.trim());
  }

  // 执行登录
  Future<void> _performLogin(String accountType, String account, String password) async {
    debugPrint('🔐 开始登录流程 - 账户类型: $accountType, 账户: $account');

    try {
      // 创建登录请求
      final loginRequest = LoginRequest(accountType: accountType, account: account, password: password);

      // 调用登录服务
      final loginService = LoginServiceFactory.instance;
      final response = await loginService.login(loginRequest);

      if (response.success) {
        debugPrint('✅ 登录接口调用成功');

        // 检查是否需要验证
        final userData = response.userData;
        if (userData != null && userData['need_verify'] == true) {
          // 需要验证，跳转到验证码页面
          debugPrint('🔐 需要验证，跳转到验证码页面');
          print(userData);
          final verifyInfo = userData['verify_info'] as Map<String, dynamic>?;

          NavigationService().navigateTo(
            AppRoutes.verifyCode,
            arguments: {
              'accountType': accountType,
              'account': account,
              'sessionId': response.sessionId,
              'userData': userData,
              'verifyInfo': verifyInfo,
            },
          );
        } else {
          // 直接登录成功，处理用户数据
          debugPrint('🎉 直接登录成功');
          await _handleLoginSuccess(userData);
        }
      } else {
        // 登录失败，显示错误信息
        debugPrint('❌ 登录失败: ${response.message}');
        _showLoginError(accountType, response.message ?? '登录失败');
      }
    } catch (e) {
      // 处理异常
      debugPrint('💥 登录异常: $e');
      _showLoginError(accountType, '网络错误，请稍后重试');
    }
  }

  // 处理登录成功
  Future<void> _handleLoginSuccess(Map<String, dynamic>? data) async {
    try {
      if (data == null) {
        if (mounted) {
          Toastification.show(context, message: '登录数据异常');
        }
        return;
      }

      // 提取认证信息
      final accessToken = data['access_token']?.toString();
      final refreshToken = data['refresh_token']?.toString();
      final expireAtTimestamp = data['expire_at'];
      final userData = data['user'] as Map<String, dynamic>?;

      // 将时间戳转换为DateTime
      DateTime? expireAt;
      if (expireAtTimestamp != null) {
        if (expireAtTimestamp is int) {
          expireAt = DateTime.fromMillisecondsSinceEpoch(expireAtTimestamp * 1000);
        } else if (expireAtTimestamp is String) {
          final timestamp = int.tryParse(expireAtTimestamp);
          if (timestamp != null) {
            expireAt = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
          }
        }
      }

      if (accessToken == null || userData == null) {
        if (mounted) {
          Toastification.show(context, message: '登录数据不完整');
        }
        return;
      }

      // 创建用户信息对象
      final userInfo = UserInfo.fromJson(userData);

      // 获取AuthProvider并设置认证数据
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.setAuthData(userInfo: userInfo, accessToken: accessToken, refreshToken: refreshToken, expireAt: expireAt);

      debugPrint('✅ 用户认证信息已缓存');

      if (mounted) {
        // 显示登录成功提示
        Toastification.show(context, message: '登录成功');

        // 1.5秒后跳转到首页
        Future.delayed(const Duration(milliseconds: 1500), () {
          if (mounted) {
            NavigationService().navigateToAndClearStack(AppRoutes.mainTabbarScreen);
          }
        });
      }
    } catch (e) {
      debugPrint('❌ 处理登录成功数据失败: $e');
      if (mounted) {
        Toastification.show(context, message: '登录处理失败，请重试');
      }
    }
  }

  // 显示登录错误
  void _showLoginError(String accountType, String message) {
    if (accountType == 'email' || accountType == 'phone') {
      setState(() {
        // 根据错误类型决定显示在哪个字段
        if (message.contains('账户') || message.contains('邮箱') || message.contains('手机')) {
          _emailPhoneError = message;
        } else {
          _passwordError = message;
        }
      });
    } else {
      setState(() {
        _subAccountError = message;
        _subPasswordError = message;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 400,
      child: TabBarView(controller: widget.controller, children: [_buildMainAccountInput(), _buildSubAccountInput()]),
    );
  }

  // 构建主账户输入区域
  Widget _buildMainAccountInput() {
    return Padding(
      padding: EdgeInsets.all(UiConstants.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 邮箱or手机号输入
          AccountInputField(
            controller: _emailPhoneController,
            inputType: AccountInputType.auto,
            hintText: '邮箱/手机号码',
            textStyle: context.templateStyle.text.inputText,
            errorText: _emailPhoneError,
            height: UiConstants.spacing48 + UiConstants.spacing2,
            textInputAction: TextInputAction.next,
            showClearButton: true,
            enableValidation: false, // 使用自定义验证逻辑
            validator: (value) {
              if (value?.trim().isEmpty ?? true) {
                return '请输入邮箱或手机号码';
              }
              final input = value!.trim();
              final cleanInput = input.replaceAll(' ', '');
              if (!_isValidEmail(cleanInput) && !_isValidPhone(input)) {
                return '请输入有效的邮箱或手机号码';
              }
              return null;
            },
            onChanged: (value) {
              if (_emailPhoneError != null) {
                _clearErrors();
              }

              // 实时验证并自动显示密码框
              final input = value.trim();
              final cleanInput = input.replaceAll(' ', '');
              if (cleanInput.isNotEmpty && (_isValidEmail(cleanInput) || _isValidPhone(input))) {
                // 格式验证通过，自动显示密码框
                if (!_showPasswordInput) {
                  setState(() {
                    _showPasswordInput = true;
                  });
                }
              } else {
                // 格式不正确或为空，隐藏密码框
                if (_showPasswordInput) {
                  setState(() {
                    _showPasswordInput = false;
                    _passwordController.clear();
                    _passwordError = null;
                  });
                }
              }

              setState(() {});
            },
          ),

          // 密码输入 - 只有在显示密码输入状态时才显示
          if (_showPasswordInput) ...[
            SizedBox(height: UiConstants.spacing12),
            PasswordInputField(
              controller: _passwordController,
              hintText: '密码',
              textStyle: context.templateStyle.text.inputText,
              errorText: _passwordError,
              height: UiConstants.spacing48 + UiConstants.spacing2,
              textInputAction: TextInputAction.done,
              showVisibilityToggle: true,
              enableStrengthValidation: false, // 登录时不需要强度验证
              onChanged: (value) {
                if (_passwordError != null) {
                  _clearErrors();
                }
                setState(() {});
              },
            ),
          ],

          SizedBox(height: UiConstants.spacing24),

          // 登录按钮 - 根据状态显示不同文本
          CommonButton.primary(
            _showPasswordInput ? '登录' : '下一步',
            width: double.infinity,
            height: UiConstants.buttonHeightMedium,
            onPressed: _handleMainAccountLogin,
          ),
        ],
      ),
    );
  }

  // 构建子账户输入区域
  Widget _buildSubAccountInput() {
    return Padding(
      padding: EdgeInsets.all(UiConstants.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 子账户名输入
          TextFieldWidget(
            controller: _subAccountController,
            hintText: '子账户名',
            textStyle: context.templateStyle.text.inputText,
            errorText: _subAccountError,
            height: UiConstants.spacing48 + UiConstants.spacing2,
            borderColor: _subAccountError != null ? context.templateColors.error : null,
            textInputAction: TextInputAction.next,
            onChanged: (value) {
              if (_subAccountError != null) {
                _clearErrors();
              }
              setState(() {});
            },
          ),
          SizedBox(height: UiConstants.spacing12),

          // 密码输入 - 使用专用组件
          PasswordInputField(
            controller: _subPasswordController,
            hintText: '密码',
            textStyle: context.templateStyle.text.inputText,
            errorText: _subPasswordError,
            height: UiConstants.spacing48 + UiConstants.spacing2,
            textInputAction: TextInputAction.done,
            showVisibilityToggle: true,
            enableStrengthValidation: false, // 登录时不需要强度验证
            onChanged: (value) {
              if (_subPasswordError != null) {
                _clearErrors();
              }
              setState(() {});
            },
          ),

          // 忘记密码
          Padding(
            padding: EdgeInsets.only(top: UiConstants.spacing8, bottom: UiConstants.spacing28),
            child: InkWellWidget(
              onTap: () {
                // TODO: 跳转到忘记密码页面
                NavigationService().navigateTo(AppRoutes.forgotPassword);
              },
              child: Text('忘记您的密码了吗？', style: context.templateStyle.text.infoText),
            ),
          ),

          // 登录按钮
          CommonButton.primary('登录', width: double.infinity, height: UiConstants.buttonHeightMedium, onPressed: _handleSubAccountLogin),
        ],
      ),
    );
  }
}
