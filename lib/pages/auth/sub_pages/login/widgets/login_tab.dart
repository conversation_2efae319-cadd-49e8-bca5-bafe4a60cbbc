/*
*  登陆标签页
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class LoginTab extends StatefulWidget {
  final TabController controller;

  const LoginTab({super.key, required this.controller});

  @override
  State<LoginTab> createState() => _LoginTabState();
}

class _LoginTabState extends State<LoginTab> {
  @override
  Widget build(BuildContext context) {
    return TabbarWidget(
      controller: widget.controller,
      fillColor: context.templateColors.surface,
      height: 34,
      tabs: [TabItem(title: '邮箱/手机号码'), TabItem(title: '子账户')],
      labelStyle: context.templateStyle.text.bodyTextMedium,
      selectedColor: context.templateColors.textPrimary,
      unselectedColor: context.templateColors.textSecondary,
      showIndicator: true,
      isScrollable: true,
      tabAlignment: TabAlignment.start,
      indicatorSize: TabBarIndicatorSize.label,
      labelPadding: EdgeInsets.only(right: UiConstants.spacing16),
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing16),
      onTap: (index) {
        widget.controller.animateTo(index);
      },
    );
  }
}
