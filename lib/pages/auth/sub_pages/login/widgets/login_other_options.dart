/*
*  其他登录方式 & 验证方法
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/pages/auth/widgets/captcha_switch_dialog.dart';
import 'package:qubic_exchange/services/pages/auth/index.dart';
import 'package:qubic_exchange/services/auth/third_party_auth_service.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';
import 'package:qubic_exchange/models/user/user_info.dart';
import 'package:qubic_exchange/routes/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class LoginOtherOptions extends StatefulWidget {
  const LoginOtherOptions({super.key});

  @override
  State<LoginOtherOptions> createState() => _LoginOtherOptionsState();
}

class _LoginOtherOptionsState extends State<LoginOtherOptions> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing28),
      child: Column(
        children: [
          // 其他注册方式
          _buildOtherRegisterOptions(),
          // 切换验证方法
          _buildSwitchVerifyMethod(),
        ],
      ),
    );
  }

  // 构建其他注册方式
  Widget _buildOtherRegisterOptions() {
    /// 登陆方式按钮
    Widget buildLoginMethodButton({String imageName = '', bool followTheme = false, required VoidCallback onTap}) {
      return InkWellWidget(
        onTap: onTap,
        child: Container(
          margin: EdgeInsets.only(left: UiConstants.spacing18),
          width: 40,
          height: 40,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            border: Border.all(color: context.templateColors.divider, width: 0.5),
            borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
          ),
          child: ThemedImage.asset(imageName, width: 18, height: 18, followTheme: followTheme),
        ),
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing16, vertical: UiConstants.spacing18),
      decoration: BoxDecoration(border: Border(bottom: BorderSide(width: 0.5, color: context.templateColors.divider))),
      child: Row(
        children: [
          Text('或通过以下方式', style: context.templateStyle.text.descriptionText),
          Spacer(),
          Row(
            children: [
              buildLoginMethodButton(imageName: 'icon_google_platform', onTap: () => _handleGoogleLogin()),
              buildLoginMethodButton(imageName: 'icon_apple_platform', followTheme: true, onTap: () => _handleAppleLogin()),
              // buildLoginMethodButton(imageName: 'icon_telegram_platform', onTap: () => {}),
            ],
          ),
        ],
      ),
    );
  }

  // 构建切换验证方法
  Widget _buildSwitchVerifyMethod() {
    return Consumer<CaptchaService>(
      builder: (context, captchaService, child) {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing16, vertical: UiConstants.spacing18),
          child: InkWellWidget(
            onTap: () => _showCaptchaSwitchDialog(captchaService),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('切换人机验证方式', style: context.templateStyle.text.bodyText),
                SizedBox(width: UiConstants.spacing8),
                ThemedImage.asset('arrow_triangle_down', size: UiConstants.iconSize10, followTheme: true),
              ],
            ),
          ),
        );
      },
    );
  }

  // 显示验证方式切换弹窗
  void _showCaptchaSwitchDialog(CaptchaService captchaService) async {
    final selectedType = await CaptchaSwitchDialog.show(
      context,
      selectedType: captchaService.currentType,
      onSelected: (type) async {
        await captchaService.switchCaptchaType(type);
        debugPrint('🔄 验证方式已切换为: ${type.displayName}');
      },
    );

    // 如果用户选择了新的验证方式，更新全局状态
    if (selectedType != null && selectedType != captchaService.currentType) {
      await captchaService.switchCaptchaType(selectedType);
      debugPrint('✅ 验证方式已保存: ${selectedType.displayName}');
    }
  }

  // 处理Google登录
  Future<void> _handleGoogleLogin() async {
    try {
      debugPrint('🔍 开始Google登录...');

      // 调用Google登录
      final googleResult = await _performGoogleLogin();

      if (googleResult != null) {
        debugPrint('🎉 Google登录成功，开始OAuth登录...');

        // 调用OAuth登录接口
        await _performOAuthLogin(googleResult);
      } else {
        if (mounted) {
          Toastification.show(context, message: 'Google登录失败');
        }
      }
    } catch (e) {
      debugPrint('❌ Google登录异常: $e');
      if (mounted) {
        Toastification.show(context, message: '登录失败，请稍后重试');
      }
    }
  }

  // 处理Apple登录
  Future<void> _handleAppleLogin() async {
    try {
      debugPrint('🍎 开始Apple登录...');

      // 调用Apple登录
      final appleResult = await _performAppleLogin();

      if (appleResult != null) {
        debugPrint('🎉 Apple登录成功，开始OAuth登录...');

        // 调用OAuth登录接口
        await _performOAuthLogin(appleResult);
      } else {
        if (mounted) {
          Toastification.show(context, message: 'Apple登录失败');
        }
      }
    } catch (e) {
      debugPrint('❌ Apple登录异常: $e');
      if (mounted) {
        Toastification.show(context, message: '登录失败，请稍后重试');
      }
    }
  }

  // 执行Google登录
  Future<Map<String, dynamic>?> _performGoogleLogin() async {
    try {
      final thirdPartyAuthService = ThirdPartyAuthService();
      final result = await thirdPartyAuthService.signInWithGoogle();

      // Google登录成功，返回结果
      return result;
    } catch (e) {
      debugPrint('❌ Google登录失败: $e');
      return null;
    }
  }

  // 执行Apple登录
  Future<Map<String, dynamic>?> _performAppleLogin() async {
    try {
      final thirdPartyAuthService = ThirdPartyAuthService();
      final result = await thirdPartyAuthService.signInWithApple();

      return result;
    } catch (e) {
      debugPrint('❌ Apple登录失败: $e');
      return null;
    }
  }

  // 执行OAuth登录
  Future<void> _performOAuthLogin(Map<String, dynamic> authResult) async {
    try {
      debugPrint('🔗 开始OAuth登录...');

      // 从第三方登录结果中提取信息
      final provider = authResult['type'] ?? 'unknown';
      String token = '';
      String? displayName;
      String? avatar;

      if (provider == 'google') {
        // Google登录数据格式
        token = authResult['idToken'] ?? '';
        displayName = authResult['displayName'];
        avatar = authResult['photoUrl'];
      } else if (provider == 'apple') {
        // Apple登录数据格式
        token = authResult['identityToken'] ?? '';
        final givenName = authResult['givenName'];
        final familyName = authResult['familyName'];

        // 组合Apple的姓名
        if (givenName != null || familyName != null) {
          displayName = '${givenName ?? ''} ${familyName ?? ''}'.trim();
          if (displayName.isEmpty) displayName = null;
        }
        // Apple通常不提供头像
        avatar = null;
      }

      // 调用OAuth登录接口
      final result = await RegisterServiceFactory.oauthLogin(
        provider: provider,
        token: token,
        displayName: displayName,
        avatar: avatar,
        inviteCode: null, // 暂时不传邀请码，可以根据需要添加
      );

      if (mounted) {
        if (result['success'] == true) {
          // 登录成功，处理用户数据
          await _handleLoginSuccess(result['data']);
        } else {
          Toastification.show(context, message: result['message'] ?? 'OAuth登录失败');
        }
      }
    } catch (e) {
      debugPrint('❌ OAuth登录异常: $e');
      if (mounted) {
        Toastification.show(context, message: 'OAuth登录失败，请稍后重试');
      }
    }
  }

  // 处理登录成功
  Future<void> _handleLoginSuccess(Map<String, dynamic>? data) async {
    try {
      if (data == null) {
        if (mounted) {
          Toastification.show(context, message: '登录数据异常');
        }
        return;
      }

      // 提取认证信息
      final accessToken = data['access_token']?.toString();
      final refreshToken = data['refresh_token']?.toString();
      final expireAtTimestamp = data['expire_at'];
      final userData = data['user'] as Map<String, dynamic>?;

      // 将时间戳转换为DateTime
      DateTime? expireAt;
      if (expireAtTimestamp != null) {
        if (expireAtTimestamp is int) {
          expireAt = DateTime.fromMillisecondsSinceEpoch(expireAtTimestamp * 1000);
        } else if (expireAtTimestamp is String) {
          final timestamp = int.tryParse(expireAtTimestamp);
          if (timestamp != null) {
            expireAt = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
          }
        }
      }

      if (accessToken == null || userData == null) {
        if (mounted) {
          Toastification.show(context, message: '登录数据不完整');
        }
        return;
      }

      // 创建用户信息对象
      final userInfo = UserInfo.fromJson(userData);

      // 获取AuthProvider并设置认证数据
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.setAuthData(userInfo: userInfo, accessToken: accessToken, refreshToken: refreshToken, expireAt: expireAt);

      debugPrint('✅ 用户认证信息已缓存');

      if (mounted) {
        // 显示登录成功提示
        Toastification.show(context, message: '登录成功');

        // 1.5秒后跳转到首页
        Future.delayed(const Duration(milliseconds: 1500), () {
          if (mounted) {
            NavigationService().navigateToAndClearStack(AppRoutes.mainTabbarScreen);
          }
        });
      }
    } catch (e) {
      debugPrint('❌ 处理登录成功数据失败: $e');
      if (mounted) {
        Toastification.show(context, message: '登录处理失败，请重试');
      }
    }
  }
}
