/*
 *  注册表单 
*/

import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:gt4_flutter_plugin/gt4_session_configuration.dart';
import 'package:qubic_exchange/charts/kline/flexi_kline.dart';
import 'package:remixicon/remixicon.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/routes/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/l10n/index.dart';
import 'package:gt4_flutter_plugin/gt4_flutter_plugin.dart';

/// 注册表单常量
class _RegisterFormConstants {
  static Duration get animationDuration => UiConstants.animationDurationMedium;
  static double get inputHeight => UiConstants.spacing48;
  static double get checkboxSize => UiConstants.fontSize14;
  static double get passwordStrengthFontSize => UiConstants.fontSize12;
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 32;
}

class RegisterForm extends StatefulWidget {
  final TabController tabController;

  const RegisterForm({super.key, required this.tabController});

  @override
  State<RegisterForm> createState() => _RegisterFormState();
}

class _RegisterFormState extends State<RegisterForm> {
  // 控制器
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _invitationCodeController =
      TextEditingController();

  // 状态变量
  final ValueNotifier<bool> _isPasswordVisible = ValueNotifier<bool>(false);
  final ValueNotifier<bool> _isInvitationCodeVisible = ValueNotifier<bool>(
    false,
  );
  final ValueNotifier<bool> _isAgreementChecked = ValueNotifier<bool>(false);
  int _currentTabIndex = 0; // 当前选中的tab索引

  // 焦点控制器
  final FocusNode _passwordFocusNode = FocusNode();
  bool _isPasswordFocused = false;

  // 动画控制器 - 不再需要，使用AnimatedContainer和AnimatedOpacity代替

  // 错误状态
  String? _emailError;
  String? _phoneError;
  String? _passwordError;

  // 密码强度状态
  bool _hasValidLength = false;
  bool _hasLetter = false;
  bool _hasNumber = false;

  @override
  void initState() {
    super.initState();
    _currentTabIndex = widget.tabController.index;
    _setupListeners();
  }

  /// 设置监听器
  void _setupListeners() {
    widget.tabController.addListener(_onTabChanged);
    _passwordFocusNode.addListener(_onPasswordFocusChanged);
  }

  // ========== 事件处理方法 ==========

  void _onPasswordFocusChanged() {
    setState(() {
      _isPasswordFocused = _passwordFocusNode.hasFocus;
    });

    // 失去焦点时检查密码强度
    if (!_isPasswordFocused) {
      _checkPasswordStrengthOnFocusLost();
    }
  }

  // 失去焦点时检查密码强度
  void _checkPasswordStrengthOnFocusLost() {
    final password = _passwordController.text.trim();

    // 如果密码为空，不显示错误
    if (password.isEmpty) {
      return;
    }

    // 检查是否所有密码强度要求都满足
    if (!_isPasswordStrengthValid()) {
      setState(() {
        _passwordError = AppLocalizations.of(context).passwordInvalidFormat;
      });
    }
  }

  // 检查密码强度是否有效
  bool _isPasswordStrengthValid() {
    return _hasValidLength && _hasLetter && _hasNumber;
  }

  // 判断是否应该显示密码强度提示
  bool _shouldShowPasswordStrength() {
    return _isPasswordFocused; // 只有在聚焦时才显示强度提示
  }

  /// Tab切换事件处理
  void _onTabChanged() {
    if (_currentTabIndex != widget.tabController.index) {
      // 清除当前焦点
      FocusScope.of(context).unfocus();

      setState(() {
        _currentTabIndex = widget.tabController.index;
        _clearErrors();
        _isPasswordFocused = false; // 重置密码框聚焦状态
      });
    }
  }

  @override
  void dispose() {
    widget.tabController.removeListener(_onTabChanged);
    _passwordFocusNode.removeListener(_onPasswordFocusChanged);
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _invitationCodeController.dispose();
    _passwordFocusNode.dispose();
    _isPasswordVisible.dispose();
    _isInvitationCodeVisible.dispose();
    _isAgreementChecked.dispose();
    super.dispose();
  }

  // 注册事件
  void _handleRegister() {
    // 清除之前的错误状态
    _clearErrors();

    // 验证协议同意
    if (!_isAgreementChecked.value) {
      Toastification.show(
        context,
        message: AppLocalizations.of(context).mustAgreeToPolicy,
      );
      return;
    }

    // 验证邮箱或手机号码（根据当前选中的tab）
    if (_currentTabIndex == 0) {
      // 验证邮箱
      if (_emailController.text.trim().isEmpty) {
        _emailError = AppLocalizations.of(context).enterEmail;
        setState(() {});
        return;
      } else if (!_isValidEmail(_emailController.text.trim())) {
        _emailError = AppLocalizations.of(context).enterValidEmail;
        setState(() {});
        return;
      }
    } else {
      // 验证手机号码
      if (_phoneController.text.trim().isEmpty) {
        _phoneError = AppLocalizations.of(context).enterPhone;
        setState(() {});
        return;
      } else if (!_isValidPhone(_phoneController.text.trim())) {
        _phoneError = AppLocalizations.of(context).enterValidPhone;
        setState(() {});
        return;
      }
    }

    // 验证密码
    final password = _passwordController.text.trim();
    if (password.isEmpty) {
      _passwordError = AppLocalizations.of(context).enterPassword;
      setState(() {});
      return;
    }

    // 验证密码强度要求
    if (!_hasValidLength) {
      _passwordError = AppLocalizations.of(context).passwordLengthError;
      setState(() {});
      return;
    }

    if (!_hasLetter) {
      _passwordError = '密码必须包含至少一个字母';
      setState(() {});
      return;
    }

    if (!_hasNumber) {
      _passwordError = AppLocalizations.of(context).passwordMissingNumber;
      setState(() {});
      return;
    }

    // 所有验证通过，先进行极验验证
    _showGeetestCaptcha();
  }

  // ========== 验证方法 ==========

  /// 清除所有错误状态
  void _clearErrors() {
    _emailError = null;
    _phoneError = null;
    _passwordError = null;
  }

  /// 验证邮箱格式
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// 清理手机号码（移除空格和特殊字符）
  String _cleanPhone(String phone) {
    return phone.replaceAll(RegExp(r'[^\d]'), '');
  }

  /// 验证手机号码格式
  bool _isValidPhone(String phone) {
    // 移除所有空格和特殊字符，只保留数字
    final cleanPhone = _cleanPhone(phone);
    // 验证清理后的手机号是否符合中国手机号格式
    return RegExp(r'^1[3-9]\d{9}$').hasMatch(cleanPhone);
  }

  /// 验证密码强度
  void _validatePasswordStrength(String password) {
    setState(() {
      _hasValidLength =
          password.length >= _RegisterFormConstants.minPasswordLength &&
          password.length <= _RegisterFormConstants.maxPasswordLength;
      _hasLetter = RegExp(r'[a-zA-Z]').hasMatch(password);
      _hasNumber = RegExp(r'[0-9]').hasMatch(password);
    });
  }

  // 显示极验验证码
  Future<void> _showGeetestCaptcha() async {
    try {
      var config = GT4SessionConfiguration();
      config.debugEnable = true;
      config.backgroundColor = context.templateColors.primary;
      // 创建极验验证码实例
      final gt4Plugin = Gt4FlutterPlugin('08fc76a91ea6eb7795e821f33cced546');
      // 设置事件监听
      gt4Plugin.addEventHandler(
        onShow: (Map<String, dynamic> message) {
          print(message);
        },
        onResult: (Map<String, dynamic> result) {
          debugPrint('极验验证码结果: $result');
          print(result);
          if (parseInt(result['status']) == 1) {
            // 验证成功，执行注册
            _performRegister(captchaOutput: result);
          } else {
            // 验证失败
            if (mounted) {
              Toastification.show(context, message: '验证失败，请重试');
            }
          }
        },
        onError: (Map<String, dynamic> error) {
          debugPrint('极验验证码错误: $error');
          if (mounted) {
            Toastification.show(context, message: '验证服务异常，请稍后重试');
          }
        },
      );

      // 启动验证
      gt4Plugin.verify();
    } catch (e) {
      // 处理异常
      debugPrint('极验验证码异常: $e');
      if (mounted) {
        Toastification.show(context, message: '验证服务异常，请稍后重试');
      }
    }
  }

  // 执行注册
  Future<void> _performRegister({Map<String, dynamic>? captchaOutput}) async {
    try {
      // 收集注册表单数据
      final registerData = <String, dynamic>{
        // 基本信息
        'accountType': _currentTabIndex == 0 ? 'email' : 'phone',
        'account':
            _currentTabIndex == 0
                ? _emailController.text.trim()
                : _cleanPhone(_phoneController.text.trim()),
        'password': _passwordController.text.trim(),
        'inviteCode':
            _invitationCodeController.text.trim().isEmpty
                ? null
                : _invitationCodeController.text.trim(),
        'agreeToTerms': _isAgreementChecked.value,

        // 注册场景标识
        'isRegister': true,
        'registerType': _currentTabIndex == 0 ? 'email' : 'phone',
        'registerScene': 'normal_register', // 普通注册场景
        // 邮箱注册特有字段
        if (_currentTabIndex == 0) 'email': _emailController.text.trim(),

        // 手机注册特有字段
        if (_currentTabIndex == 1) ...{
          'phone': _cleanPhone(_phoneController.text.trim()),
          'countryCode': '+86', // 默认使用中国区号，实际应该从UI获取
        },

        // 极验验证码数据
        if (captchaOutput != null) 'captchaOutput': captchaOutput,

        // 时间戳
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      debugPrint('📋 收集的注册数据: $registerData');

      // 跳转到验证码页面，传递完整的注册数据
      NavigationService().navigateTo(
        AppRoutes.verifyCode,
        arguments: {
          // 基本跳转参数
          'accountType': registerData['accountType'],
          'account': registerData['account'],
          'isRegister': true,

          // 完整的注册表单数据
          'registerData': registerData,

          // 注册类型和场景
          'registerType': registerData['registerType'],
          'registerScene': registerData['registerScene'],

          // 用于后续API调用的数据
          'formData': {
            'email': registerData['email'],
            'phone': registerData['phone'],
            'countryCode': registerData['countryCode'],
            'password': registerData['password'],
            'inviteCode': registerData['inviteCode'],
            'agreeToTerms': registerData['agreeToTerms'],
            'captchaOutput': registerData['captchaOutput'],
          },
        },
      );

      debugPrint('🚀 跳转到验证码页面，注册类型: ${registerData['registerType']}');
    } catch (e) {
      debugPrint('❌ 收集注册数据失败: $e');
      if (mounted) {
        Toastification.show(context, message: '数据处理失败，请重试');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing16),
      child: Form(
        child: Column(
          children: [
            IndexedStack(
              index: _currentTabIndex,
              children: [
                // 邮箱输入页面
                Column(
                  children: [
                    _buildEmailInput(),
                    SizedBox(height: UiConstants.spacing12),
                  ],
                ),
                // 手机号码输入页面
                Column(
                  children: [
                    _buildPhoneInput(),
                    SizedBox(height: UiConstants.spacing12),
                  ],
                ),
              ],
            ),

            // 密码输入
            _buildPasswordInput(),
            SizedBox(height: UiConstants.spacing16),

            // 邀请码输入
            _buildInvitationCodeInput(),
            SizedBox(height: UiConstants.spacing24),

            // 协议
            _buildAgreement(),

            // 创建账号
            CommonButton.primary(
              AppLocalizations.of(context).createAccount,
              height: UiConstants.buttonHeightMedium,
              width: double.infinity,
              onPressed: _handleRegister,
            ),
          ],
        ),
      ),
    );
  }

  // ========== UI构建方法 ==========

  /// 构建邮箱输入
  Widget _buildEmailInput() {
    return EmailInputField(
      controller: _emailController,
      hintText: AppLocalizations.of(context).enterEmail,
      textStyle: context.templateStyle.text.inputText,
      errorText: _emailError,
      height: _RegisterFormConstants.inputHeight,
      textInputAction: TextInputAction.next,
      showClearButton: true,
      onChanged: (value) {
        if (_emailError != null) {
          _clearErrors();
        }
        setState(() {});
      },
    );
  }

  // 构建手机号码输入
  Widget _buildPhoneInput() {
    return PhoneInputField(
      controller: _phoneController,
      hintText: AppLocalizations.of(context).enterPhoneNumber,
      textStyle: context.templateStyle.text.bodyLargeMedium,
      errorText: _phoneError,
      height: 50,
      textInputAction: TextInputAction.next,
      showClearButton: true,
      showCountryCode: true,
      defaultCountryCode: '+86',
      onCountryCodeChanged: (code) {
        // 处理国家代码变化
      },
      onChanged: (value) {
        if (_phoneError != null) {
          _clearErrors();
        }
        setState(() {});
      },
    );
  }

  // 构建密码输入
  Widget _buildPasswordInput() {
    return Column(
      children: [
        // 密码输入区域
        PasswordInputField(
          controller: _passwordController,
          focusNode: _passwordFocusNode,
          hintText: AppLocalizations.of(context).password,
          textStyle: context.templateStyle.text.bodyLargeMedium,
          errorText: _passwordError,
          height: 50,
          textInputAction: TextInputAction.done,
          showVisibilityToggle: true,
          showClearButton: true,
          enableStrengthValidation: true,
          showStrengthIndicator: true,
          onChanged: (value) {
            // 清除密码错误（包括强度验证错误）
            if (_passwordError != null) {
              setState(() {
                _passwordError = null;
              });
            }
            // 验证密码强度
            _validatePasswordStrength(value);
          },
        ),

        // 密码强度显示 - 带平滑动画过渡效果
        AnimatedSize(
          duration: _RegisterFormConstants.animationDuration,
          curve: Curves.easeInOut,
          child: AnimatedOpacity(
            duration: _RegisterFormConstants.animationDuration,
            curve: Curves.easeInOut,
            opacity: _shouldShowPasswordStrength() ? 1.0 : 0.0,
            child:
                _shouldShowPasswordStrength()
                    ? _buildPasswordStrengthContent()
                    : const SizedBox.shrink(),
          ),
        ),
      ],
    );
  }

  // 构建密码强度内容
  Widget _buildPasswordStrengthContent() {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: UiConstants.spacing12,
        horizontal: UiConstants.spacing4,
      ),
      child: Column(
        children: [
          _buildPasswordStrengthItem(
            AppLocalizations.of(context).passwordLengthRule,
            _hasValidLength,
          ),
          SizedBox(height: UiConstants.spacing4),
          _buildPasswordStrengthItem('包含至少一个字母', _hasLetter),
          SizedBox(height: UiConstants.spacing4),
          _buildPasswordStrengthItem(
            AppLocalizations.of(context).passwordNumberRule,
            _hasNumber,
          ),
        ],
      ),
    );
  }

  // 构建密码强度项
  Widget _buildPasswordStrengthItem(String text, bool isActive) {
    // 判断是否有输入内容
    final hasInput = _passwordController.text.isNotEmpty;
    // 确定颜色
    Color activeColor;
    Color inactiveColor;
    Color textColor;

    if (isActive) {
      // 已满足要求
      activeColor = context.templateColors.textPrimary;
      inactiveColor = context.templateColors.textPrimary;
      textColor = context.templateColors.textPrimary;
    } else if (hasInput) {
      // 有输入但未满足要求
      activeColor = context.templateColors.tradeSell;
      inactiveColor = context.templateColors.tradeSell;
      textColor = context.templateColors.tradeSell;
    } else {
      // 无输入
      activeColor = context.templateColors.textSecondary;
      inactiveColor = context.templateColors.textSecondary;
      textColor = context.templateColors.textSecondary;
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: _RegisterFormConstants.checkboxSize,
          height: _RegisterFormConstants.checkboxSize,
          decoration: BoxDecoration(
            color:
                isActive
                    ? activeColor
                    : (hasInput && !isActive
                        ? inactiveColor
                        : Colors.transparent),
            border: Border.all(color: inactiveColor, width: 1),
            borderRadius: BorderRadius.circular(2),
          ),
          child:
              isActive
                  ? Icon(
                    Icons.check,
                    size: _RegisterFormConstants.checkboxSize - 4,
                    color: Colors.white,
                  )
                  : null,
        ),
        SizedBox(width: UiConstants.spacing8),
        Expanded(
          child: Text(
            text,
            style: context.templateStyle.text.hintText.copyWith(
              color: textColor,
              fontSize: _RegisterFormConstants.passwordStrengthFontSize,
              fontWeight:
                  isActive
                      ? UiConstants.fontWeightMedium
                      : UiConstants.fontWeightRegular,
            ),
          ),
        ),
      ],
    );
  }

  // 构建邀请码输入
  Widget _buildInvitationCodeInput() {
    return ValueListenableBuilder<bool>(
      valueListenable: _isInvitationCodeVisible,
      builder: (context, isInvitationCodeVisible, child) {
        return Column(
          children: [
            /// 控制器
            Padding(
              padding: EdgeInsets.only(bottom: UiConstants.spacing8),
              child: GestureDetector(
                onTap: () {
                  _isInvitationCodeVisible.value =
                      !_isInvitationCodeVisible.value;
                },
                child: Row(
                  children: [
                    Text(
                      AppLocalizations.of(context).inviteCodeOptional,
                      style: context.templateStyle.text.bodyText,
                    ),
                    SizedBox(width: UiConstants.spacing4),
                    ThemedImage(
                      name:
                          _isInvitationCodeVisible.value
                              ? 'arrow_triangle_up_gray'
                              : 'arrow_triangle_down_gray',
                      size: UiConstants.iconSize10,
                    ),
                  ],
                ),
              ),
            ),

            /// 邀请码输入框 - 根据状态显示/隐藏
            if (isInvitationCodeVisible)
              TextFieldWidget(
                controller: _invitationCodeController,
                hintText: AppLocalizations.of(context).enterInviteCode,
                textStyle: context.templateStyle.text.bodyLargeMedium,
                height: 50,
                fillColor: context.templateColors.inputBackground,
                autofillHints: const [AutofillHints.oneTimeCode],
                textInputAction: TextInputAction.done,
                suffixIcon: Padding(
                  padding: EdgeInsets.only(right: UiConstants.spacing12),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 清除按钮 - 只在有内容时显示
                      if (_invitationCodeController.text.isNotEmpty) ...[
                        InkWellWidget(
                          onTap: () {
                            _invitationCodeController.clear();
                            setState(() {});
                          },
                          child: Icon(
                            RemixIcons.close_circle_fill,
                            size: 18,
                            color: context.templateColors.textTertiary,
                          ),
                        ),
                        SizedBox(width: UiConstants.spacing8),
                      ],
                      // 粘贴按钮
                      InkWellWidget(
                        onTap: () async {
                          // TODO: 实现粘贴功能
                        },
                        child: Text(
                          AppLocalizations.of(context).paste,
                          style: context.templateStyle.text.bodyText.copyWith(
                            color: context.templateColors.primary,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                onChanged: (value) {
                  setState(() {});
                },
              ),
          ],
        );
      },
    );
  }

  // 构建协议
  Widget _buildAgreement() {
    return ValueListenableBuilder<bool>(
      valueListenable: _isAgreementChecked,
      builder: (context, isAgreementChecked, child) {
        return Padding(
          padding: EdgeInsets.only(bottom: UiConstants.spacing22),
          child: InkWellWidget(
            onTap: () {
              _isAgreementChecked.value = !_isAgreementChecked.value;
            },
            child: Row(
              children: [
                CheckboxWidget(
                  customSize: UiConstants.spacing14,
                  value: isAgreementChecked,
                  checkIcon: 'icon_checked',
                  uncheckIcon: '',
                  activeColor: context.templateColors.textPrimary,
                  activeFillColor: context.templateColors.textPrimary,
                  onChanged: (value) => {_isAgreementChecked.value = value},
                ),
                SizedBox(width: UiConstants.spacing4),
                Expanded(
                  child: Text.rich(
                    TextSpan(
                      style: context.templateStyle.text.descriptionText
                          .copyWith(fontSize: 12),
                      children: [
                        TextSpan(
                          text:
                              '${AppLocalizations.of(context).iAgreeTo}\u00a0',
                        ),
                        TextSpan(
                          text:
                              '${AppLocalizations.of(context).userAgreementOne}\u00a0',
                          style: TextStyle(
                            color: context.templateColors.primary,
                          ),
                          recognizer:
                              TapGestureRecognizer()
                                ..onTap = () {
                                  // TODO 跳转用户协议
                                },
                        ),
                        TextSpan(
                          text: '${AppLocalizations.of(context).and}\u00a0',
                        ),
                        TextSpan(
                          text:
                              '${AppLocalizations.of(context).privacyPolicyOne}\u00a0',
                          style: TextStyle(
                            color: context.templateColors.primary,
                          ),
                          recognizer:
                              TapGestureRecognizer()
                                ..onTap = () {
                                  // TODO 跳转隐私政策
                                },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
