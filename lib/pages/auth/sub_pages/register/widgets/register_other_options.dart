/*
*  其他注册方式 & 验证方法
*/

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:gt4_flutter_plugin/gt4_flutter_plugin.dart';
import 'package:gt4_flutter_plugin/gt4_session_configuration.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/l10n/index.dart';
import 'package:qubic_exchange/pages/auth/widgets/captcha_switch_dialog.dart';
import 'package:qubic_exchange/services/auth/third_party_auth_service.dart';
import 'package:qubic_exchange/services/pages/auth/register/register_service_factory.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';
import 'package:qubic_exchange/models/user/user_info.dart';
import 'package:qubic_exchange/routes/index.dart';
import 'package:provider/provider.dart';

class RegisterOtherOptions extends StatefulWidget {
  const RegisterOtherOptions({super.key});

  @override
  State<RegisterOtherOptions> createState() => _RegisterOtherOptionsState();
}

class _RegisterOtherOptionsState extends State<RegisterOtherOptions> {
  // 当前选中的验证方式
  CaptchaType _currentCaptchaType = CaptchaType.geetest;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing28),
      child: Column(
        children: [
          // 其他注册方式
          _buildOtherRegisterOptions(),
          // 切换验证方法
          _buildSwitchVerifyMethod(),
        ],
      ),
    );
  }

  // 构建其他注册方式
  Widget _buildOtherRegisterOptions() {
    final localizations = AppLocalizations.of(context);

    /// 登陆方式按钮
    Widget buildLoginMethodButton({String imageName = '', bool followTheme = false, required VoidCallback onTap}) {
      return InkWellWidget(
        onTap: onTap,
        child: Container(
          margin: EdgeInsets.only(left: UiConstants.spacing18),
          width: 40,
          height: 40,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            border: Border.all(color: context.templateColors.divider, width: 0.5),
            borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
          ),
          child: ThemedImage.asset(imageName, width: 18, height: 18, followTheme: followTheme),
        ),
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing16, vertical: UiConstants.spacing18),
      decoration: BoxDecoration(border: Border(bottom: BorderSide(width: 0.5, color: context.templateColors.divider))),
      child: Row(
        children: [
          Text(localizations.loginViaOther, style: context.templateStyle.text.descriptionText),
          Spacer(),
          Row(
            children: [
              buildLoginMethodButton(imageName: 'icon_google_platform', onTap: () => _handleGoogleLogin()),
              buildLoginMethodButton(imageName: 'icon_apple_platform', followTheme: true, onTap: () => _handleAppleLogin()),
              // buildLoginMethodButton(
              //   imageName: 'icon_telegram_platform',
              //   onTap: () => {},
              // ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建切换验证方法
  Widget _buildSwitchVerifyMethod() {
    final localizations = AppLocalizations.of(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing16, vertical: UiConstants.spacing18),
      child: InkWellWidget(
        onTap: () => _showCaptchaSwitchDialog(),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(localizations.switchCaptchaMethod, style: context.templateStyle.text.bodyText),
            Container(
              margin: EdgeInsets.only(left: UiConstants.spacing8),
              child: ThemedImage.asset('arrow_triangle_down', size: UiConstants.iconSize10, followTheme: true),
            ),
          ],
        ),
      ),
    );
  }

  // 显示验证方式切换弹窗
  void _showCaptchaSwitchDialog() async {
    final selectedType = await CaptchaSwitchDialog.show(
      context,
      selectedType: _currentCaptchaType,
      onSelected: (type) {
        setState(() {
          _currentCaptchaType = type;
        });
      },
    );

    // 如果用户选择了新的验证方式，更新状态
    if (selectedType != null) {
      setState(() {
        _currentCaptchaType = selectedType;
      });
    }
  }

  // 处理Google登录
  Future<void> _handleGoogleLogin() async {
    try {
      // 先调用极验验证
      final captchaResult = await _showGeetestCaptcha();

      if (captchaResult == null) {
        // 用户取消验证或验证失败
        return;
      }

      // debugPrint('🔐 极验验证成功: $captchaResult');

      // 极验验证成功后，调用Google登录
      final googleResult = await _performGoogleLogin();

      if (googleResult != null) {
        debugPrint('🎉 Google登录成功: $googleResult');

        // 调用OAuth登录接口
        await _performOAuthLogin(googleResult);
      } else {
        if (mounted) {
          Toastification.show(context, message: 'Google登录失败');
        }
      }
    } catch (e) {
      debugPrint('❌ Google登录异常: $e');
      if (mounted) {
        Toastification.show(context, message: '登录失败，请稍后重试');
      }
    }
  }

  // 显示极验验证码
  Future<Map<String, dynamic>?> _showGeetestCaptcha() async {
    try {
      var config = GT4SessionConfiguration();
      config.debugEnable = true;
      config.backgroundColor = Colors.blue;

      // 创建极验验证码实例
      final gt4Plugin = Gt4FlutterPlugin('08fc76a91ea6eb7795e821f33cced546');

      // 创建一个 Completer 来等待验证结果
      final completer = Completer<Map<String, dynamic>?>();

      // 设置事件监听
      gt4Plugin.addEventHandler(
        onShow: (Map<String, dynamic> message) {
          print(message);
        },
        onResult: (Map<String, dynamic> result) {
          debugPrint('极验验证码结果: $result');
          print(result);
          if (int.tryParse(result['status'].toString()) == 1) {
            // 验证成功
            completer.complete(result);
          } else {
            // 验证失败
            if (mounted) {
              Toastification.show(context, message: '验证失败，请重试');
            }
            completer.complete(null);
          }
        },
        onError: (Map<String, dynamic> error) {
          debugPrint('极验验证码错误: $error');
          if (mounted) {
            Toastification.show(context, message: '验证服务异常，请稍后重试');
          }
          completer.complete(null);
        },
      );

      // 启动验证
      gt4Plugin.verify();

      // 等待验证结果
      return await completer.future;
    } catch (e) {
      // 处理异常
      debugPrint('极验验证码异常: $e');
      if (mounted) {
        Toastification.show(context, message: '验证服务异常，请稍后重试');
      }
      return null;
    }
  }

  // 处理Apple登录
  Future<void> _handleAppleLogin() async {
    try {
      debugPrint('🍎 开始Apple登录...');

      // 调用Apple登录
      final appleResult = await _performAppleLogin();

      if (appleResult != null) {
        debugPrint('🎉 Apple登录成功，开始OAuth登录...');

        // 调用OAuth登录接口
        await _performOAuthLogin(appleResult);
      } else {
        if (mounted) {
          Toastification.show(context, message: 'Apple登录失败');
        }
      }
    } catch (e) {
      debugPrint('❌ Apple登录异常: $e');
      if (mounted) {
        Toastification.show(context, message: '登录失败，请稍后重试');
      }
    }
  }

  // 执行Apple登录
  Future<Map<String, dynamic>?> _performAppleLogin() async {
    try {
      final thirdPartyAuthService = ThirdPartyAuthService();
      final result = await thirdPartyAuthService.signInWithApple();

      if (result != null) {
        debugPrint('📱 Apple登录认证数据:');
        debugPrint('   类型: ${result['type']}');
        debugPrint('   用户标识: ${result['userIdentifier']}');
        debugPrint('   邮箱: ${result['email']}');
        debugPrint('   名字: ${result['givenName']}');
        debugPrint('   姓氏: ${result['familyName']}');
        debugPrint('   身份令牌: ${result['identityToken']}');
        debugPrint('   授权码: ${result['authorizationCode']}');
      }

      return result;
    } catch (e) {
      debugPrint('❌ Apple登录失败: $e');
      return null;
    }
  }

  // 执行Google登录
  Future<Map<String, dynamic>?> _performGoogleLogin() async {
    try {
      final thirdPartyAuthService = ThirdPartyAuthService();
      final result = await thirdPartyAuthService.signInWithGoogle();

      // Google登录成功，返回结果

      return result;
    } catch (e) {
      debugPrint('❌ Google登录失败: $e');
      return null;
    }
  }

  // 执行OAuth登录
  Future<void> _performOAuthLogin(Map<String, dynamic> authResult) async {
    try {
      debugPrint('🔗 开始OAuth登录...');

      // 从第三方登录结果中提取信息
      final provider = authResult['type'] ?? 'unknown';
      String token = '';
      String? displayName;
      String? avatar;

      if (provider == 'google') {
        // Google登录数据格式
        token = authResult['idToken'] ?? '';
        displayName = authResult['displayName'];
        avatar = authResult['photoUrl'];
      } else if (provider == 'apple') {
        // Apple登录数据格式
        token = authResult['identityToken'] ?? '';
        final givenName = authResult['givenName'];
        final familyName = authResult['familyName'];

        // 组合Apple的姓名
        if (givenName != null || familyName != null) {
          displayName = '${givenName ?? ''} ${familyName ?? ''}'.trim();
          if (displayName.isEmpty) displayName = null;
        }
        // Apple通常不提供头像
        avatar = null;
      }

      // 调用OAuth登录接口
      final result = await RegisterServiceFactory.oauthLogin(
        provider: provider,
        token: token,
        displayName: displayName,
        avatar: avatar,
        inviteCode: null, // 暂时不传邀请码，可以根据需要添加
      );

      if (mounted) {
        if (result['success'] == true) {
          // 登录成功，处理用户数据
          await _handleLoginSuccess(result['data']);
        } else {
          Toastification.show(context, message: result['message'] ?? 'OAuth登录失败');
        }
      }
    } catch (e) {
      debugPrint('❌ OAuth登录异常: $e');
      if (mounted) {
        Toastification.show(context, message: 'OAuth登录失败，请稍后重试');
      }
    }
  }

  // 处理登录成功
  Future<void> _handleLoginSuccess(Map<String, dynamic>? data) async {
    try {
      if (data == null) {
        if (mounted) {
          Toastification.show(context, message: '登录数据异常');
        }
        return;
      }

      // 提取认证信息
      final accessToken = data['access_token']?.toString();
      final refreshToken = data['refresh_token']?.toString();
      final expireAtTimestamp = data['expire_at'];
      final userData = data['user'] as Map<String, dynamic>?;

      // 将时间戳转换为DateTime
      DateTime? expireAt;
      if (expireAtTimestamp != null) {
        if (expireAtTimestamp is int) {
          expireAt = DateTime.fromMillisecondsSinceEpoch(expireAtTimestamp * 1000);
        } else if (expireAtTimestamp is String) {
          final timestamp = int.tryParse(expireAtTimestamp);
          if (timestamp != null) {
            expireAt = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
          }
        }
      }

      if (accessToken == null || userData == null) {
        if (mounted) {
          Toastification.show(context, message: '登录数据不完整');
        }
        return;
      }

      // 创建用户信息对象
      final userInfo = UserInfo.fromJson(userData);

      // 获取AuthProvider并设置认证数据
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.setAuthData(userInfo: userInfo, accessToken: accessToken, refreshToken: refreshToken, expireAt: expireAt);

      debugPrint('✅ 用户认证信息已缓存');

      if (mounted) {
        // 显示登录成功提示
        Toastification.show(context, message: '登录成功');

        // 1.5秒后跳转到首页
        Future.delayed(const Duration(milliseconds: 1500), () {
          if (mounted) {
            NavigationService().navigateToAndClearStack(AppRoutes.mainTabbarScreen);
          }
        });
      }
    } catch (e) {
      debugPrint('❌ 处理登录成功数据失败: $e');
      if (mounted) {
        Toastification.show(context, message: '登录处理失败，请重试');
      }
    }
  }
}
