/*
*  注册标签栏
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class RegisterTabbar extends StatefulWidget {
  final TabController controller;

  const RegisterTabbar({super.key, required this.controller});

  @override
  State<RegisterTabbar> createState() => _RegisterTabbarState();
}

class _RegisterTabbarState extends State<RegisterTabbar> {
  @override
  Widget build(BuildContext context) {
    return TabbarWidget(
      controller: widget.controller,
      fillColor: context.templateColors.surface,
      height: 34,
      tabs: [TabItem(title: '邮箱'), TabItem(title: '手机号码')],
      labelStyle: context.templateStyle.text.bodyTextMedium,
      selectedColor: context.templateColors.textPrimary,
      unselectedColor: context.templateColors.textSecondary,
      showIndicator: true,
      isScrollable: true,
      tabAlignment: TabAlignment.start,
      indicatorSize: TabBarIndicatorSize.label,
      labelPadding: EdgeInsets.only(right: UiConstants.spacing16),
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing16),
      onTap: (index) {
        // 确保TabController的index变化能够触发监听器
        widget.controller.animateTo(index);
      },
    );
  }
}
