/*
*  注册界面
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/routes/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/l10n/index.dart';
import 'widgets/index.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // 初始化
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  // 销毁
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // 处理登录按钮点击
  void _handleLoginButtonPressed() {
    final navigationService = NavigationService();
    navigationService.isPreviousRoute(AppRoutes.login)
        ? navigationService.goBack()
        : navigationService.navigateTo(
          AppRoutes.login,
          transition: 'slideRight',
        );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return Scaffold(
      backgroundColor: context.templateColors.surface,
      resizeToAvoidBottomInset: false,
      appBar: AppBarWidget(
        showBackButton: true,
        onBackPressed: () {
          // 使用正常的返回，会触发向下滑出动画
          NavigationService().goBack();
        },
        actions: [
          CommonButton(
            text: localizations.login,
            type: CommonButtonType.text,
            onPressed: () => _handleLoginButtonPressed(),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  Padding(
                    padding: EdgeInsets.only(
                      left: UiConstants.spacing18,
                      right: UiConstants.spacing18,
                      bottom: UiConstants.spacing28,
                      top: UiConstants.spacing10,
                    ),
                    child: Text(
                      localizations.welcomeToApp(localizations.appname),
                      style: context.templateStyle.text.h1,
                    ),
                  ),

                  // 标签栏
                  RegisterTabbar(controller: _tabController),

                  // 注册表单
                  RegisterForm(tabController: _tabController),
                ],
              ),
            ),
          ),
          // 其他注册方式 & 验证方式
          SafeArea(bottom: false, child: RegisterOtherOptions()),
        ],
      ),
    );
  }
}
