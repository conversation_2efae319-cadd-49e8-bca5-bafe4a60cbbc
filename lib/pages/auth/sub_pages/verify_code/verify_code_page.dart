/*
*  验证码界面
*/

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pinput/pinput.dart';
import 'package:provider/provider.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/core/managers/focus_manager.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/routes/index.dart';
import 'package:qubic_exchange/providers/auth_provider.dart';
import 'package:qubic_exchange/models/user/user_info.dart';
import 'package:qubic_exchange/models/pages/auth/verify_code/index.dart';
import 'package:qubic_exchange/services/pages/auth/verify_code/index.dart';
import 'package:qubic_exchange/services/pages/auth/index.dart';
import 'package:qubic_exchange/services/pages/auth/register/register_service_factory.dart';
import 'package:qubic_exchange/services/clients/dio_request.dart';
import 'package:qubic_exchange/services/config/api_route.dart';

class VerifyCodePage extends StatefulWidget {
  const VerifyCodePage({super.key});

  @override
  State<VerifyCodePage> createState() => _VerifyCodePageState();
}

class _VerifyCodePageState extends State<VerifyCodePage> {
  final TextEditingController _emailCodeController = TextEditingController();
  final TextEditingController _phoneCodeController = TextEditingController();
  final TextEditingController _googleCodeController = TextEditingController();

  // 焦点节点
  final FocusNode _emailCodeFocusNode = FocusNode();
  final FocusNode _phoneCodeFocusNode = FocusNode();
  final FocusNode _googleCodeFocusNode = FocusNode();

  // 全局焦点管理器
  final GlobalFocusManager _globalFocusManager = GlobalFocusManager.instance;

  String? _accountType;
  String? _account;
  String? _sessionId;
  Map<String, dynamic>? _userData;
  bool _isRegister = false;

  // 新增：注册相关参数
  String? _registerType;
  String? _registerScene;
  Map<String, dynamic>? _registerData;
  Map<String, dynamic>? _formData;

  // 新增：验证信息参数（从登录界面跳转过来）
  Map<String, dynamic>? _verifyInfo;

  // 倒计时相关
  Timer? _emailCountdownTimer;
  Timer? _phoneCountdownTimer;
  int _emailCountdown = 0;
  int _phoneCountdown = 0;
  bool _isEmailSending = false;
  bool _isPhoneSending = false;
  bool _isRegistering = false;

  // 验证码显示状态
  bool _showEmailVerification = false;
  bool _showPhoneVerification = false;
  bool _showGoogleVerification = false;

  // 验证码输入完成回调（仅用于日志记录）
  void _onCodeCompleted(String title, String code) {
    debugPrint('验证码输入完成: $title - $code');
  }

  // 确认按钮处理方法
  Future<void> _handleConfirm() async {
    if (!mounted) return;

    // 验证必填的验证码是否已输入
    if (!_validateRequiredCodes()) {
      return;
    }

    // 如果是注册场景，执行注册逻辑
    if (_isRegister && _registerScene == 'normal_register') {
      await _handleRegister();
      return;
    }

    // 如果有verifyInfo，执行验证码登录逻辑
    if (_verifyInfo != null) {
      await _handleVerifyCodeLogin();
      return;
    }

    // 原有的登录验证逻辑
    try {
      // 创建验证码请求，只包含需要验证的验证码
      final verifyRequest = VerifyCodeRequest(
        sessionId: _sessionId ?? 'mock_session_id',
        emailCode:
            _showEmailVerification ? _emailCodeController.text.trim() : '',
        phoneCode:
            _showPhoneVerification ? _phoneCodeController.text.trim() : '',
        googleCode:
            _showGoogleVerification ? _googleCodeController.text.trim() : '',
      );

      // 调用验证码服务
      final verifyService = VerifyCodeServiceFactory.instance;
      final response = await verifyService.verifyCode(verifyRequest);

      if (!mounted) return;

      if (response.success) {
        // 验证成功，保存认证数据
        final authProvider = Provider.of<AuthProvider>(context, listen: false);

        // 创建用户信息
        UserInfo? userInfo;
        if (response.userData != null) {
          userInfo = UserInfo.fromJson(response.userData!);
        } else if (_userData != null) {
          userInfo = UserInfo.fromJson(_userData!);
        }

        // 设置认证数据
        await authProvider.setAuthData(
          userInfo: userInfo,
          accessToken: response.accessToken,
          refreshToken: response.refreshToken,
          expireAt: response.expireAt,
        );

        debugPrint('✅ 验证成功，用户已登录');

        // 跳转到主页
        NavigationService().navigateToAndClearStack(AppRoutes.mainTabbarScreen);
      } else {
        // 验证失败
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(response.message ?? '验证失败')));
        }
      }
    } catch (e) {
      debugPrint('💥 验证异常: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('网络错误，请稍后重试')));
      }
    }
  }

  // 处理验证码登录逻辑
  Future<void> _handleVerifyCodeLogin() async {
    if (!mounted) return;

    try {
      debugPrint('🔐 开始验证码登录...');

      // 根据verifyInfo组装请求参数
      final requestData = <String, dynamic>{};

      // 邮箱验证
      if (_verifyInfo!['email'] != null && _showEmailVerification) {
        requestData['email'] = _verifyInfo!['email'];
        requestData['email_code'] = _emailCodeController.text.trim();
      }

      // 手机验证
      if (_verifyInfo!['phone'] != null && _showPhoneVerification) {
        requestData['phone'] = _verifyInfo!['phone'];
        requestData['code'] = _verifyInfo!['code'] ?? '86'; // 默认中国区号
        requestData['sms_code'] = _phoneCodeController.text.trim();
      }

      // 谷歌验证
      if (_verifyInfo!['username'] != null && _showGoogleVerification) {
        requestData['username'] = _verifyInfo!['username'];
        requestData['google2fa_code'] = _googleCodeController.text.trim();
      }

      debugPrint('📤 验证码登录请求参数: $requestData');

      // 调用验证码登录接口
      final response = await DioRequest.instance.post(
        ApiRoute.verifyCodeLogin,
        body: requestData,
      );

      if (!mounted) return;

      if (response.success) {
        debugPrint('✅ 验证码登录成功');

        // 处理登录成功
        await _handleLoginSuccess(response.data);
      } else {
        debugPrint('❌ 验证码登录失败: ${response.message}');
        if (mounted) {
          Toastification.show(context, message: response.message);
        }
      }
    } catch (e) {
      debugPrint('💥 验证码登录异常: $e');
      if (mounted) {
        Toastification.show(context, message: '网络错误，请稍后重试');
      }
    }
  }

  // 处理登录成功
  Future<void> _handleLoginSuccess(Map<String, dynamic>? data) async {
    try {
      if (data == null) {
        if (mounted) {
          Toastification.show(context, message: '登录数据异常');
        }
        return;
      }

      // 提取认证信息
      final accessToken = data['access_token']?.toString();
      final refreshToken = data['refresh_token']?.toString();
      final expireAtTimestamp = data['expire_at'];
      final userData = data['user'] as Map<String, dynamic>?;

      // 将时间戳转换为DateTime
      DateTime? expireAt;
      if (expireAtTimestamp != null) {
        if (expireAtTimestamp is int) {
          expireAt = DateTime.fromMillisecondsSinceEpoch(
            expireAtTimestamp * 1000,
          );
        } else if (expireAtTimestamp is String) {
          final timestamp = int.tryParse(expireAtTimestamp);
          if (timestamp != null) {
            expireAt = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
          }
        }
      }

      if (accessToken == null || userData == null) {
        if (mounted) {
          Toastification.show(context, message: '登录数据不完整');
        }
        return;
      }

      // 创建用户信息对象
      final userInfo = UserInfo.fromJson(userData);

      // 获取AuthProvider并设置认证数据
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.setAuthData(
        userInfo: userInfo,
        accessToken: accessToken,
        refreshToken: refreshToken,
        expireAt: expireAt,
      );

      debugPrint('✅ 用户认证信息已缓存');

      if (mounted) {
        // 显示登录成功提示
        Toastification.show(context, message: '登录成功');

        // 1.5秒后跳转到首页
        Future.delayed(const Duration(milliseconds: 1500), () {
          if (mounted) {
            NavigationService().navigateToAndClearStack(
              AppRoutes.mainTabbarScreen,
            );
          }
        });
      }
    } catch (e) {
      debugPrint('❌ 处理登录成功数据失败: $e');
      if (mounted) {
        Toastification.show(context, message: '登录处理失败，请重试');
      }
    }
  }

  // 处理注册逻辑
  Future<void> _handleRegister() async {
    if (!mounted) return;

    setState(() {
      _isRegistering = true;
    });

    try {
      Map<String, dynamic> result;

      if (_registerType == 'email') {
        // 邮箱注册
        final email =
            _formData?['email']?.toString() ??
            _registerData?['email']?.toString() ??
            '';
        final password =
            _formData?['password']?.toString() ??
            _registerData?['password']?.toString() ??
            '';
        final inviteCode =
            _formData?['inviteCode']?.toString() ??
            _registerData?['inviteCode']?.toString();
        final emailCode = _emailCodeController.text.trim();

        if (email.isEmpty || password.isEmpty || emailCode.isEmpty) {
          if (mounted) {
            Toastification.show(context, message: '请填写完整的注册信息');
          }
          return;
        }

        result = await RegisterServiceFactory.registerByEmail(
          email: email,
          emailCode: emailCode,
          password: password,
          inviteCode: inviteCode,
        );
      } else if (_registerType == 'phone') {
        // 手机注册
        final phone =
            _formData?['phone']?.toString() ??
            _registerData?['phone']?.toString() ??
            '';
        final countryCode =
            _formData?['countryCode']?.toString().replaceAll('+', '') ??
            _registerData?['countryCode']?.toString().replaceAll('+', '') ??
            '86';
        final password =
            _formData?['password']?.toString() ??
            _registerData?['password']?.toString() ??
            '';
        final inviteCode =
            _formData?['inviteCode']?.toString() ??
            _registerData?['inviteCode']?.toString();
        final smsCode = _phoneCodeController.text.trim();

        if (phone.isEmpty || password.isEmpty || smsCode.isEmpty) {
          if (mounted) {
            Toastification.show(context, message: '请填写完整的注册信息');
          }
          return;
        }

        result = await RegisterServiceFactory.registerByPhone(
          phone: phone,
          countryCode: countryCode,
          smsCode: smsCode,
          password: password,
          inviteCode: inviteCode,
        );
      } else {
        if (mounted) {
          Toastification.show(context, message: '注册类型错误');
        }
        return;
      }

      if (!mounted) return;

      if (result['success'] == true) {
        // 注册成功
        Toastification.show(context, message: '注册成功');

        // 1.5秒后跳转到Face ID设置页面
        Future.delayed(const Duration(milliseconds: 1500), () {
          if (mounted) {
            NavigationService().navigateToAndClearStack(AppRoutes.authFaceid);
          }
        });
      } else {
        // 注册失败，显示具体错误信息
        Toastification.show(context, message: result['message']);
      }
    } catch (e) {
      debugPrint('❌ 注册异常: $e');
      if (mounted) {
        Toastification.show(context, message: '注册失败，请稍后重试');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRegistering = false;
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();

    // 注册焦点节点到全局焦点管理器
    _globalFocusManager.registerFocusNode(_emailCodeFocusNode);
    _globalFocusManager.registerFocusNode(_phoneCodeFocusNode);
    _globalFocusManager.registerFocusNode(_googleCodeFocusNode);

    // 获取路由传递的参数
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final args =
          ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
      if (args != null) {
        setState(() {
          // 基本参数
          _accountType = args['accountType'];
          _account = args['account'];
          _sessionId = args['sessionId'];
          _userData = args['userData'];
          _isRegister = args['isRegister'] ?? false;

          // 注册相关参数
          _registerType = args['registerType'];
          _registerScene = args['registerScene'];
          _registerData = args['registerData'];
          _formData = args['formData'];

          // 验证信息参数（从登录界面跳转过来）
          _verifyInfo = args['verifyInfo'];

          // 打印收集到的参数
          debugPrint('📋 收集到的路由参数:');
          debugPrint('   accountType: $_accountType');
          debugPrint('   account: $_account');
          debugPrint('   isRegister: $_isRegister');
          debugPrint('   registerType: $_registerType');
          debugPrint('   registerScene: $_registerScene');
          debugPrint('   registerData: $_registerData');
          debugPrint('   formData: $_formData');
          debugPrint('   verifyInfo: $_verifyInfo');

          // 根据参数确定显示哪些验证码
          _determineVerificationTypes();
        });
      }
    });
  }

  // 根据账户类型和用户数据确定显示哪些验证码
  void _determineVerificationTypes() {
    // 重置所有验证码显示状态
    _showEmailVerification = false;
    _showPhoneVerification = false;
    _showGoogleVerification = false;

    if (_isRegister && _registerScene == 'normal_register') {
      // 注册流程：根据注册类型显示对应的验证码
      if (_registerType == 'email') {
        // 邮箱注册：只显示邮箱验证码
        _showEmailVerification = true;
        debugPrint('📧 邮箱注册场景，显示邮箱验证码');
      } else if (_registerType == 'phone') {
        // 手机注册：只显示手机验证码
        _showPhoneVerification = true;
        debugPrint('📱 手机注册场景，显示手机验证码');
      }
      // 注册时默认不需要谷歌验证码
    } else if (_isRegister) {
      // 其他注册场景的处理
      if (_accountType == 'email') {
        _showEmailVerification = true;
      } else if (_accountType == 'phone') {
        _showPhoneVerification = true;
      }
    } else {
      // 登录流程：优先根据verifyInfo显示验证码
      if (_verifyInfo != null) {
        // 从登录界面跳转过来，根据verifyInfo显示对应的验证码
        if (_verifyInfo!['email'] != null) {
          _showEmailVerification = true;
          debugPrint('📧 根据verifyInfo显示邮箱验证码: ${_verifyInfo!['email']}');
        }

        if (_verifyInfo!['phone'] != null) {
          _showPhoneVerification = true;
          debugPrint('📱 根据verifyInfo显示手机验证码: ${_verifyInfo!['phone']}');
        }

        if (_verifyInfo!['google2fa'] != null ||
            _verifyInfo!['username'] != null) {
          _showGoogleVerification = true;
          debugPrint('🔐 根据verifyInfo显示谷歌验证码');
        }
      } else {
        // 原有逻辑：根据账户绑定情况显示验证码
        if (_accountType == 'email' || _hasEmailBound()) {
          _showEmailVerification = true;
        }

        if (_accountType == 'phone' || _hasPhoneBound()) {
          _showPhoneVerification = true;
        }

        if (_hasGoogleAuthBound()) {
          _showGoogleVerification = true;
        }
      }
    }

    debugPrint('📱 验证码显示状态:');
    debugPrint('   账户类型: $_accountType');
    debugPrint('   是否注册: $_isRegister');
    debugPrint('   注册类型: $_registerType');
    debugPrint('   注册场景: $_registerScene');
    debugPrint('   邮箱验证: $_showEmailVerification');
    debugPrint('   手机验证: $_showPhoneVerification');
    debugPrint('   谷歌验证: $_showGoogleVerification');

    // 自动发送验证码
    _autoSendVerificationCode();
  }

  // 自动发送验证码
  void _autoSendVerificationCode() {
    // 延迟一小段时间确保UI已经更新
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted) return;

      // 根据显示的验证码类型自动发送
      if (_showEmailVerification && _emailCountdown == 0 && !_isEmailSending) {
        debugPrint('🚀 自动发送邮箱验证码');
        _sendEmailCode();
      }

      if (_showPhoneVerification && _phoneCountdown == 0 && !_isPhoneSending) {
        debugPrint('🚀 自动发送短信验证码');
        _sendSmsCode();
      }

      // 注意：谷歌验证码不需要发送，是用户手动输入的
    });
  }

  // 检查是否绑定了邮箱
  bool _hasEmailBound() {
    return _userData?['email'] != null &&
        _userData!['email'].toString().isNotEmpty;
  }

  // 检查是否绑定了手机号
  bool _hasPhoneBound() {
    return _userData?['phone'] != null &&
        _userData!['phone'].toString().isNotEmpty;
  }

  // 检查是否绑定了谷歌验证器
  bool _hasGoogleAuthBound() {
    return _userData?['hasGoogleAuth'] == true ||
        _userData?['googleAuthEnabled'] == true;
  }

  // 验证必填的验证码是否已输入（仅检查格式，不验证内容）
  bool _validateRequiredCodes() {
    // 检查邮箱验证码
    if (_showEmailVerification) {
      final emailCode = _emailCodeController.text.trim();
      if (emailCode.isEmpty) {
        Toastification.show(context, message: '请输入邮箱验证码');
        return false;
      }
      if (emailCode.length != 6) {
        Toastification.show(context, message: '邮箱验证码必须为6位数字');
        return false;
      }
      if (!RegExp(r'^\d{6}$').hasMatch(emailCode)) {
        Toastification.show(context, message: '邮箱验证码格式错误');
        return false;
      }
    }

    // 检查手机验证码
    if (_showPhoneVerification) {
      final phoneCode = _phoneCodeController.text.trim();
      if (phoneCode.isEmpty) {
        Toastification.show(context, message: '请输入手机验证码');
        return false;
      }
      if (phoneCode.length != 6) {
        Toastification.show(context, message: '手机验证码必须为6位数字');
        return false;
      }
      if (!RegExp(r'^\d{6}$').hasMatch(phoneCode)) {
        Toastification.show(context, message: '手机验证码格式错误');
        return false;
      }
    }

    // 检查谷歌验证码
    if (_showGoogleVerification) {
      final googleCode = _googleCodeController.text.trim();
      if (googleCode.isEmpty) {
        Toastification.show(context, message: '请输入谷歌验证码');
        return false;
      }
      if (googleCode.length != 6) {
        Toastification.show(context, message: '谷歌验证码必须为6位数字');
        return false;
      }
      if (!RegExp(r'^\d{6}$').hasMatch(googleCode)) {
        Toastification.show(context, message: '谷歌验证码格式错误');
        return false;
      }
    }

    return true;
  }

  // 获取邮箱副标题（脱敏显示）
  String _getEmailSubtitle() {
    String email = '';

    // 优先从verifyInfo中获取
    if (_verifyInfo?['email'] != null) {
      email = _verifyInfo!['email'].toString();
    }
    // 其次从注册数据中获取
    else if (_isRegister && _formData?['email'] != null) {
      email = _formData!['email'].toString();
    } else if (_registerData?['email'] != null) {
      email = _registerData!['email'].toString();
    } else if (_accountType == 'email' && _account != null) {
      email = _account!;
    } else if (_userData?['email'] != null) {
      email = _userData!['email'].toString();
    }

    if (email.isNotEmpty) {
      return _maskEmail(email);
    }

    return 'cn.****@gmail.com'; // 默认显示
  }

  // 获取手机号副标题（脱敏显示）
  String _getPhoneSubtitle() {
    String phone = '';

    // 优先从verifyInfo中获取
    if (_verifyInfo?['phone'] != null) {
      phone = _verifyInfo!['phone'].toString();
    }
    // 其次从注册数据中获取
    else if (_isRegister && _formData?['phone'] != null) {
      phone = _formData!['phone'].toString();
    } else if (_registerData?['phone'] != null) {
      phone = _registerData!['phone'].toString();
    } else if (_accountType == 'phone' && _account != null) {
      phone = _account!;
    } else if (_userData?['phone'] != null) {
      phone = _userData!['phone'].toString();
    }

    if (phone.isNotEmpty) {
      return _maskPhone(phone);
    }

    return '138****8888'; // 默认显示
  }

  // 邮箱脱敏处理
  String _maskEmail(String email) {
    if (email.isEmpty) return email;

    final atIndex = email.indexOf('@');
    if (atIndex <= 0) return email;

    final username = email.substring(0, atIndex);
    final domain = email.substring(atIndex);

    if (username.length <= 2) {
      return '${username[0]}****$domain';
    } else {
      return '${username.substring(0, 2)}****$domain';
    }
  }

  // 手机号脱敏处理
  String _maskPhone(String phone) {
    if (phone.isEmpty) return phone;

    // 移除可能的国家代码前缀
    String cleanPhone = phone.replaceAll(RegExp(r'^\+\d+'), '');

    if (cleanPhone.length >= 7) {
      return '${cleanPhone.substring(0, 3)}****${cleanPhone.substring(cleanPhone.length - 4)}';
    } else if (cleanPhone.length >= 4) {
      return '${cleanPhone.substring(0, 2)}****${cleanPhone.substring(cleanPhone.length - 2)}';
    }

    return phone; // 如果长度不够，直接返回原始值
  }

  @override
  void dispose() {
    // 注销焦点节点
    _globalFocusManager.unregisterFocusNode(_emailCodeFocusNode);
    _globalFocusManager.unregisterFocusNode(_phoneCodeFocusNode);
    _globalFocusManager.unregisterFocusNode(_googleCodeFocusNode);

    // 释放资源
    _emailCodeController.dispose();
    _phoneCodeController.dispose();
    _googleCodeController.dispose();
    _emailCodeFocusNode.dispose();
    _phoneCodeFocusNode.dispose();
    _googleCodeFocusNode.dispose();

    // 取消倒计时
    _emailCountdownTimer?.cancel();
    _phoneCountdownTimer?.cancel();

    super.dispose();
  }

  // 发送邮箱验证码
  Future<void> _sendEmailCode() async {
    if (_isEmailSending || _emailCountdown > 0) return;

    setState(() {
      _isEmailSending = true;
    });

    try {
      String email = '';
      String type = 'register'; // 默认注册类型

      // 获取邮箱地址
      if (_verifyInfo?['email'] != null) {
        // 从verifyInfo中获取（登录验证场景）
        email = _verifyInfo!['email'].toString();
        type = 'login'; // 登录验证
      } else if (_isRegister && _formData?['email'] != null) {
        email = _formData!['email'].toString();
        type = 'register'; // 注册验证
      } else if (_registerData?['email'] != null) {
        email = _registerData!['email'].toString();
        type = 'register'; // 注册验证
      } else if (_accountType == 'email' && _account != null) {
        email = _account!;
        // 根据是否为注册场景确定类型
        type = _isRegister ? 'register' : 'login';
      }

      debugPrint('📧 发送邮箱验证码: email=$email, type=$type');

      if (email.isEmpty) {
        Toastification.show(context, message: '邮箱地址不能为空');
        return;
      }

      // 调用发送接口
      final result = await RegisterServiceFactory.sendEmailCode(email, type);

      if (!mounted) return;

      if (result['success'] == true) {
        Toastification.show(context, message: result['message']);
        _startEmailCountdown();
      } else {
        Toastification.show(context, message: result['message']);
      }
    } catch (e) {
      debugPrint('发送邮箱验证码异常: $e');
      if (mounted) {
        Toastification.show(context, message: '发送失败，请检查网络连接');
      }
    } finally {
      setState(() {
        _isEmailSending = false;
      });
    }
  }

  // 发送短信验证码
  Future<void> _sendSmsCode() async {
    if (_isPhoneSending || _phoneCountdown > 0) return;

    setState(() {
      _isPhoneSending = true;
    });

    try {
      String phone = '';
      String countryCode = '86'; // 默认中国区号
      String type = 'register'; // 默认注册类型

      // 获取手机号码
      if (_verifyInfo?['phone'] != null) {
        // 从verifyInfo中获取（登录验证场景）
        phone = _verifyInfo!['phone'].toString();
        countryCode = _verifyInfo!['code']?.toString() ?? '86';
        type = 'login'; // 登录验证
      } else if (_isRegister && _formData?['phone'] != null) {
        phone = _formData!['phone'].toString();
        countryCode =
            _formData!['countryCode']?.toString().replaceAll('+', '') ?? '86';
        type = 'register'; // 注册验证
      } else if (_registerData?['phone'] != null) {
        phone = _registerData!['phone'].toString();
        countryCode =
            _registerData!['countryCode']?.toString().replaceAll('+', '') ??
            '86';
        type = 'register'; // 注册验证
      } else if (_accountType == 'phone' && _account != null) {
        phone = _account!;
        // 根据是否为注册场景确定类型
        type = _isRegister ? 'register' : 'login';
      }

      debugPrint(
        '📱 发送手机验证码: phone=$phone, countryCode=$countryCode, type=$type',
      );

      if (phone.isEmpty) {
        Toastification.show(context, message: '手机号码不能为空');
        return;
      }

      // 调用发送接口
      final result = await RegisterServiceFactory.sendSmsCode(
        phone,
        countryCode,
        type,
      );

      if (!mounted) return;

      if (result['success'] == true) {
        Toastification.show(context, message: result['message']);
        _startPhoneCountdown();
      } else {
        Toastification.show(context, message: result['message']);
      }
    } catch (e) {
      debugPrint('发送短信验证码异常: $e');
      if (mounted) {
        Toastification.show(context, message: '发送失败，请检查网络连接');
      }
    } finally {
      setState(() {
        _isPhoneSending = false;
      });
    }
  }

  // 开始邮箱倒计时
  void _startEmailCountdown() {
    setState(() {
      _emailCountdown = 300; // 300秒倒计时
    });

    _emailCountdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _emailCountdown--;
      });

      if (_emailCountdown <= 0) {
        timer.cancel();
      }
    });
  }

  // 开始手机倒计时
  void _startPhoneCountdown() {
    setState(() {
      _phoneCountdown = 300; // 300秒倒计时
    });

    _phoneCountdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _phoneCountdown--;
      });

      if (_phoneCountdown <= 0) {
        timer.cancel();
      }
    });
  }

  // 粘贴验证码
  Future<void> _pasteCode(String type) async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      final text = clipboardData?.text?.trim() ?? '';

      // 判断是否为6位数字
      if (RegExp(r'^\d{6}$').hasMatch(text)) {
        setState(() {
          if (type == 'email') {
            _emailCodeController.text = text;
          } else if (type == 'phone') {
            _phoneCodeController.text = text;
          } else if (type == 'google') {
            _googleCodeController.text = text;
          }
        });

        if (mounted) {
          Toastification.show(context, message: '验证码已粘贴');
        }
      } else {
        if (mounted) {
          Toastification.show(context, message: '粘贴板内容不是6位数字验证码');
        }
      }
    } catch (e) {
      debugPrint('粘贴验证码失败: $e');
      if (mounted) {
        Toastification.show(context, message: '粘贴失败');
      }
    }
  }

  // 构建发送按钮
  Widget _buildSendButton(String title) {
    bool isLoading = false;
    int countdown = 0;

    if (title == '邮箱验证码') {
      isLoading = _isEmailSending;
      countdown = _emailCountdown;
    } else if (title == '手机验证码') {
      isLoading = _isPhoneSending;
      countdown = _phoneCountdown;
    }

    if (isLoading) {
      return Text(
        '发送中...',
        style: context.templateStyle.text.bodyTextMedium.copyWith(
          color: context.templateColors.textSecondary,
        ),
      );
    } else if (countdown > 0) {
      return Text(
        '${countdown}s',
        style: context.templateStyle.text.bodyTextMedium.copyWith(
          color: context.templateColors.textSecondary,
        ),
      );
    } else {
      return Text(
        '发送',
        style: context.templateStyle.text.bodyTextMedium.copyWith(
          color: context.templateColors.primary,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.templateColors.surface,
      appBar: AppBarWidget(showBackButton: true),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing18),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题
                _buildTitle(),

                // 动态显示验证码输入框
                if (_showEmailVerification)
                  _buildVerify(title: '邮箱验证码', subtitle: _getEmailSubtitle()),

                if (_showPhoneVerification)
                  _buildVerify(title: '手机验证码', subtitle: _getPhoneSubtitle()),

                if (_showGoogleVerification) _buildVerify(title: '谷歌验证码'),

                // 按钮
                _buildButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建标题
  Widget _buildTitle() {
    String title = '安全验证';
    String subtitle = '';

    if (_isRegister && _registerScene == 'normal_register') {
      if (_registerType == 'email') {
        title = '验证邮箱';
        subtitle = '请输入发送到您邮箱的验证码';
      } else if (_registerType == 'phone') {
        title = '验证手机号';
        subtitle = '请输入发送到您手机的验证码';
      }
    }

    return Padding(
      padding: EdgeInsets.only(bottom: UiConstants.spacing28),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: context.templateStyle.text.h1),
          if (subtitle.isNotEmpty) ...[
            SizedBox(height: UiConstants.spacing8),
            Text(subtitle, style: context.templateStyle.text.descriptionText),
          ],
        ],
      ),
    );
  }

  // 构建公用验证
  Widget _buildVerify({required String title, String subtitle = ''}) {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$title${subtitle.isNotEmpty ? '：$subtitle' : ''}',
            style: context.templateStyle.text.descriptionText.copyWith(
              fontSize: 13,
            ),
          ),
          SizedBox(height: UiConstants.spacing4),
          Pinput(
            controller:
                title == '邮箱验证码'
                    ? _emailCodeController
                    : title == '手机验证码'
                    ? _phoneCodeController
                    : _googleCodeController,
            focusNode:
                title == '邮箱验证码'
                    ? _emailCodeFocusNode
                    : title == '手机验证码'
                    ? _phoneCodeFocusNode
                    : _googleCodeFocusNode,
            length: 6,
            showCursor: false,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            focusedPinTheme: PinTheme(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: context.templateColors.inputBackground,
                border: Border.all(color: context.templateColors.textPrimary),
                borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
              ),
              textStyle: context.templateStyle.text.h2.copyWith(
                fontWeight: UiConstants.fontWeightMedium,
              ),
            ),
            defaultPinTheme: PinTheme(
              width: UiConstants.spacing48 + UiConstants.spacing2,
              height: UiConstants.spacing48 + UiConstants.spacing2,
              decoration: BoxDecoration(
                color: context.templateColors.inputBackground,
                border: Border.all(color: context.templateColors.inputBorder),
                borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
              ),
              textStyle: context.templateStyle.text.h2.copyWith(
                fontWeight: UiConstants.fontWeightMedium,
              ),
            ),
            onCompleted: (code) {
              // 验证码输入完成，仅记录日志
              _onCodeCompleted(title, code);
            },
          ),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: UiConstants.spacing4,
              vertical: UiConstants.spacing16,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 粘贴按钮
                InkWellWidget(
                  onTap:
                      () => _pasteCode(
                        title == '邮箱验证码'
                            ? 'email'
                            : title == '手机验证码'
                            ? 'phone'
                            : 'google',
                      ),
                  child: Text(
                    '粘贴',
                    style: context.templateStyle.text.bodyTextMedium.copyWith(
                      color: context.templateColors.primary,
                    ),
                  ),
                ),
                // 发送按钮
                InkWellWidget(
                  onTap: () {
                    if (title == '邮箱验证码') {
                      _sendEmailCode();
                    } else if (title == '手机验证码') {
                      _sendSmsCode();
                    }
                  },
                  child: _buildSendButton(title),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建按钮
  Widget _buildButton() {
    return Container(
      padding: EdgeInsets.only(top: UiConstants.spacing14),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CommonButton.primary(
            _isRegistering ? '注册中...' : '确认',
            height: UiConstants.buttonHeightMedium,
            width: double.infinity,
            onPressed: _isRegistering ? null : _handleConfirm,
          ),
          SizedBox(height: UiConstants.spacing20),
          InkWellWidget(
            child: Text(
              '没有收到验证码？',
              style: context.templateStyle.text.hintText.copyWith(
                fontSize: UiConstants.fontSize12 + 1,
                color: context.templateColors.textSecondary,
                decorationStyle: TextDecorationStyle.dashed,
                decoration: TextDecoration.underline,
                decorationColor: context.templateColors.textTertiary,
              ),
            ),
          ),
          SizedBox(height: UiConstants.spacing8),
          InkWellWidget(
            child: Text(
              '安全项不可用？',
              style: context.templateStyle.text.hintText.copyWith(
                fontSize: UiConstants.fontSize12 + 1,
                color: context.templateColors.textSecondary,
                decorationStyle: TextDecorationStyle.dashed,
                decoration: TextDecoration.underline,
                decorationColor: context.templateColors.textTertiary,
              ),
            ),
          ),
          SizedBox(height: UiConstants.spacing16),
        ],
      ),
    );
  }
}
