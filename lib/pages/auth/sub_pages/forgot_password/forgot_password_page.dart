/*
*  忘记密码界面
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/pages/auth/widgets/captcha_switch_dialog.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:pinput/pinput.dart';

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.templateColors.surface,
      appBar: AppBarWidget(showBackButton: true),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: UiConstants.spacing18,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题
                    _buildTitle(),

                    // 账户输入
                    _buildAccountInput(),

                    // 验证码输入
                    _buildCaptchaInput(),

                    // 按钮
                    CommonButton.primary(
                      '继续',
                      height: UiConstants.buttonHeightMedium,
                      width: double.infinity,
                      onPressed: () => {},
                    ),
                  ],
                ),
              ),
            ),
          ),

          SafeArea(
            child: InkWellWidget(
              onTap: () => {CaptchaSwitchDialog.show(context)},
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: UiConstants.spacing10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '切换人机验证方法',
                      style: context.templateStyle.text.bodyText,
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: UiConstants.spacing4),
                      child: Icon(
                        Icons.keyboard_arrow_down,
                        size: UiConstants.iconSize16,
                        color: context.templateColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建标题
  Widget _buildTitle() {
    return Padding(
      padding: EdgeInsets.only(bottom: UiConstants.spacing28),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('忘记您的密码了吗？', style: context.templateStyle.text.h1),
          SizedBox(height: UiConstants.spacing4),
          Text(
            '没关系，这种事谁都会发生。',
            style: context.templateStyle.text.descriptionText,
          ),
        ],
      ),
    );
  }

  // 构建 （ 邮箱 or 手机号码 ）输入框
  Widget _buildAccountInput() {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('邮箱/手机号', style: context.templateStyle.text.bodyTextMedium),
          SizedBox(height: UiConstants.spacing4),
          AccountInputField(
            inputType: AccountInputType.auto,
            height: 50,
            hintText: '邮箱/手机号',
            textInputAction: TextInputAction.next,
            showClearButton: true,
            enableValidation: true,
          ),
        ],
      ),
    );
  }

  // 构建验证码输入
  Widget _buildCaptchaInput() {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('验证码', style: context.templateStyle.text.bodyTextMedium),
          SizedBox(height: UiConstants.spacing4),
          Pinput(
            length: 6,
            showCursor: false,
            focusedPinTheme: PinTheme(
              width: UiConstants.spacing48 + UiConstants.spacing2,
              height: UiConstants.spacing48 + UiConstants.spacing2,
              decoration: BoxDecoration(
                color: context.templateColors.inputBackground,
                border: Border.all(color: context.templateColors.textPrimary),
                borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
              ),
              textStyle: context.templateStyle.text.h2.copyWith(
                fontWeight: UiConstants.fontWeightMedium,
              ),
            ),
            defaultPinTheme: PinTheme(
              width: UiConstants.spacing48 + UiConstants.spacing2,
              height: UiConstants.spacing48 + UiConstants.spacing2,
              decoration: BoxDecoration(
                color: context.templateColors.inputBackground,
                border: Border.all(color: context.templateColors.inputBorder),
                borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
              ),
              textStyle: context.templateStyle.text.h2.copyWith(
                fontWeight: UiConstants.fontWeightMedium,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
              top: UiConstants.spacing16,
              left: UiConstants.spacing4,
              right: UiConstants.spacing4,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '粘贴',
                  style: context.templateStyle.text.bodyTextMedium.copyWith(
                    color: context.templateColors.primary,
                  ),
                ),
                Text(
                  '发送',
                  style: context.templateStyle.text.bodyTextMedium.copyWith(
                    color: context.templateColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
