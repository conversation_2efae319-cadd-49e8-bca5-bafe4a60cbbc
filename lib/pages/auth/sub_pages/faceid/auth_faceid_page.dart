/*
*  面容识别界面
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/routes/constants/app_routes.dart';
import 'package:qubic_exchange/routes/core/navigation_service.dart';

class AuthFaceidPage extends StatefulWidget {
  const AuthFaceidPage({super.key});

  @override
  State<AuthFaceidPage> createState() => _AuthFaceidPageState();
}

class _AuthFaceidPageState extends State<AuthFaceidPage> {
  // 开启Face ID
  void _enableFaceID() {
    // TODO: 实现Face ID开启逻辑
    debugPrint('🔐 用户选择开启Face ID');

    // 这里可以添加生物识别认证的逻辑
    // 暂时直接跳转到主页面
    _navigateToMainPage();
  }

  // 跳过Face ID设置
  void _skipFaceID() {
    debugPrint('⏭️ 用户选择跳过Face ID设置');
    _navigateToMainPage();
  }

  // 跳转到主页面
  void _navigateToMainPage() {
    NavigationService().navigateToAndClearStack(AppRoutes.mainTabbarScreen);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.templateColors.popupBackground,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(UiConstants.spacing18),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    ThemedImage(
                      name: '',
                      width: 90,
                      height: 90,
                      followTheme: true,
                      margin: EdgeInsets.only(bottom: UiConstants.spacing24),
                    ),
                    Text('面容解锁', style: context.templateStyle.text.h2),
                    SizedBox(height: UiConstants.spacing16),
                    Text(
                      '可在开启App时快捷进入账户\n可在安全中心设置生物信息解锁功能',
                      style: context.templateStyle.text.descriptionText,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              CommonButton.primary(
                '现在开启',
                height: UiConstants.buttonHeightMedium,
                width: double.infinity,
                borderRadius: UiConstants.borderRadiusCircle,
                onPressed: () => _enableFaceID(),
              ),
              CommonButton(
                text: '稍后再说',
                height: UiConstants.buttonHeightMedium,
                width: double.infinity,
                type: CommonButtonType.text,
                textStyle: context.templateStyle.text.infoTextMedium,
                onPressed: () => _skipFaceID(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
