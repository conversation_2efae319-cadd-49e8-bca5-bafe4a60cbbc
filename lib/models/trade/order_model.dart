/*
 * 订单数据模型
 */

/// 订单类型枚举
enum OrderType {
  market('市价'),
  limit('限价'),
  stopLimit('止损限价'),
  stopMarket('止损市价'),
  takeProfitLimit('止盈限价'),
  takeProfitMarket('止盈市价'),
  trailingStop('跟踪止损'),
  plan('计划委托');

  const OrderType(this.displayName);
  final String displayName;
}

/// 订单方向枚举
enum OrderSide {
  buy('买入'),
  sell('卖出');

  const OrderSide(this.displayName);
  final String displayName;
}

/// 订单状态枚举
enum OrderStatus {
  pending('待成交'),
  partialFilled('部分成交'),
  filled('已成交'),
  cancelled('已撤销'),
  rejected('已拒绝'),
  expired('已过期'),
  triggered('已触发'),
  waitingTrigger('等待触发');

  const OrderStatus(this.displayName);
  final String displayName;
}

/// 时间有效性枚举
enum TimeInForce {
  gtc('Good Till Cancel'),
  ioc('Immediate Or Cancel'),
  fok('Fill Or Kill'),
  gtd('Good Till Date');

  const TimeInForce(this.displayName);
  final String displayName;
}

/// 订单数据模型
class OrderModel {
  /// 订单ID
  final String orderId;

  /// 客户端订单ID
  final String? clientOrderId;

  /// 交易对符号
  final String symbol;

  /// 订单类型
  final OrderType orderType;

  /// 订单方向
  final OrderSide side;

  /// 订单状态
  final OrderStatus status;

  /// 订单数量
  final String quantity;

  /// 已成交数量
  final String filledQuantity;

  /// 订单价格（限价单）
  final String? price;

  /// 触发价格（条件单）
  final String? triggerPrice;

  /// 止损价格
  final String? stopPrice;

  /// 止盈价格
  final String? takeProfitPrice;

  /// 平均成交价格
  final String? averagePrice;

  /// 时间有效性
  final TimeInForce timeInForce;

  /// 创建时间
  final DateTime createdAt;

  /// 更新时间
  final DateTime? updatedAt;

  /// 过期时间
  final DateTime? expireTime;

  /// 手续费
  final String? fee;

  /// 手续费币种
  final String? feeCurrency;

  /// 备注
  final String? remark;

  /// 是否只做Maker
  final bool postOnly;

  /// 是否减仓单
  final bool reduceOnly;

  /// 成交记录
  final List<TradeModel>? trades;

  const OrderModel({
    required this.orderId,
    this.clientOrderId,
    required this.symbol,
    required this.orderType,
    required this.side,
    required this.status,
    required this.quantity,
    required this.filledQuantity,
    this.price,
    this.triggerPrice,
    this.stopPrice,
    this.takeProfitPrice,
    this.averagePrice,
    required this.timeInForce,
    required this.createdAt,
    this.updatedAt,
    this.expireTime,
    this.fee,
    this.feeCurrency,
    this.remark,
    this.postOnly = false,
    this.reduceOnly = false,
    this.trades,
  });

  /// 获取订单进度百分比
  double get progressPercentage {
    final total = double.tryParse(quantity) ?? 0;
    final filled = double.tryParse(filledQuantity) ?? 0;
    if (total <= 0) return 0;
    return (filled / total * 100).clamp(0, 100);
  }

  /// 获取剩余数量
  String get remainingQuantity {
    final total = double.tryParse(quantity) ?? 0;
    final filled = double.tryParse(filledQuantity) ?? 0;
    return (total - filled).toStringAsFixed(8);
  }

  /// 是否为买单
  bool get isBuyOrder => side == OrderSide.buy;

  /// 是否为卖单
  bool get isSellOrder => side == OrderSide.sell;

  /// 是否为条件单
  bool get isConditionalOrder =>
      orderType == OrderType.stopLimit ||
      orderType == OrderType.stopMarket ||
      orderType == OrderType.takeProfitLimit ||
      orderType == OrderType.takeProfitMarket ||
      orderType == OrderType.trailingStop ||
      orderType == OrderType.plan;

  /// 是否可以修改
  bool get canModify =>
      status == OrderStatus.pending || status == OrderStatus.waitingTrigger;

  /// 是否可以撤销
  bool get canCancel =>
      status == OrderStatus.pending ||
      status == OrderStatus.partialFilled ||
      status == OrderStatus.waitingTrigger;

  /// 复制并更新订单
  OrderModel copyWith({
    String? orderId,
    String? clientOrderId,
    String? symbol,
    OrderType? orderType,
    OrderSide? side,
    OrderStatus? status,
    String? quantity,
    String? filledQuantity,
    String? price,
    String? triggerPrice,
    String? stopPrice,
    String? takeProfitPrice,
    String? averagePrice,
    TimeInForce? timeInForce,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? expireTime,
    String? fee,
    String? feeCurrency,
    String? remark,
    bool? postOnly,
    bool? reduceOnly,
    List<TradeModel>? trades,
  }) {
    return OrderModel(
      orderId: orderId ?? this.orderId,
      clientOrderId: clientOrderId ?? this.clientOrderId,
      symbol: symbol ?? this.symbol,
      orderType: orderType ?? this.orderType,
      side: side ?? this.side,
      status: status ?? this.status,
      quantity: quantity ?? this.quantity,
      filledQuantity: filledQuantity ?? this.filledQuantity,
      price: price ?? this.price,
      triggerPrice: triggerPrice ?? this.triggerPrice,
      stopPrice: stopPrice ?? this.stopPrice,
      takeProfitPrice: takeProfitPrice ?? this.takeProfitPrice,
      averagePrice: averagePrice ?? this.averagePrice,
      timeInForce: timeInForce ?? this.timeInForce,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      expireTime: expireTime ?? this.expireTime,
      fee: fee ?? this.fee,
      feeCurrency: feeCurrency ?? this.feeCurrency,
      remark: remark ?? this.remark,
      postOnly: postOnly ?? this.postOnly,
      reduceOnly: reduceOnly ?? this.reduceOnly,
      trades: trades ?? this.trades,
    );
  }
}

/// 成交记录模型
class TradeModel {
  /// 成交ID
  final String tradeId;

  /// 订单ID
  final String orderId;

  /// 成交价格
  final String price;

  /// 成交数量
  final String quantity;

  /// 成交金额
  final String amount;

  /// 手续费
  final String fee;

  /// 手续费币种
  final String feeCurrency;

  /// 成交时间
  final DateTime tradeTime;

  /// 是否为Maker
  final bool isMaker;

  const TradeModel({
    required this.tradeId,
    required this.orderId,
    required this.price,
    required this.quantity,
    required this.amount,
    required this.fee,
    required this.feeCurrency,
    required this.tradeTime,
    required this.isMaker,
  });
}
