/*
 * 订单数据模型示例数据
 */

import 'order_model.dart';

/// 订单示例数据
class OrderModelExample {
  /// 创建示例订单列表
  static List<OrderModel> createSampleOrders() {
    return [
      // 限价买单 - 待成交
      OrderModel(
        orderId: 'order_001',
        clientOrderId: 'client_001',
        symbol: 'BTC/USDT',
        orderType: OrderType.limit,
        side: OrderSide.buy,
        status: OrderStatus.pending,
        quantity: '0.1',
        filledQuantity: '0.0',
        price: '45000.00',
        timeInForce: TimeInForce.gtc,
        createdAt: DateTime.now().subtract(Duration(hours: 2)),
        postOnly: false,
        reduceOnly: false,
      ),

      // 市价卖单 - 已成交
      OrderModel(
        orderId: 'order_002',
        clientOrderId: 'client_002',
        symbol: 'ETH/USDT',
        orderType: OrderType.market,
        side: OrderSide.sell,
        status: OrderStatus.filled,
        quantity: '2.0',
        filledQuantity: '2.0',
        averagePrice: '3200.50',
        timeInForce: TimeInForce.ioc,
        createdAt: DateTime.now().subtract(Duration(hours: 1)),
        updatedAt: DateTime.now().subtract(Duration(minutes: 30)),
        fee: '6.401',
        feeCurrency: 'USDT',
        postOnly: false,
        reduceOnly: false,
      ),

      // 止损限价单 - 等待触发
      OrderModel(
        orderId: 'order_003',
        clientOrderId: 'client_003',
        symbol: 'BTC/USDT',
        orderType: OrderType.stopLimit,
        side: OrderSide.sell,
        status: OrderStatus.waitingTrigger,
        quantity: '0.05',
        filledQuantity: '0.0',
        price: '44000.00',
        triggerPrice: '44500.00',
        timeInForce: TimeInForce.gtc,
        createdAt: DateTime.now().subtract(Duration(minutes: 45)),
        postOnly: false,
        reduceOnly: false,
      ),

      // 计划委托 - 部分成交
      OrderModel(
        orderId: 'order_004',
        clientOrderId: 'client_004',
        symbol: 'ADA/USDT',
        orderType: OrderType.plan,
        side: OrderSide.buy,
        status: OrderStatus.partialFilled,
        quantity: '1000.0',
        filledQuantity: '350.0',
        price: '0.45',
        triggerPrice: '0.46',
        averagePrice: '0.451',
        timeInForce: TimeInForce.gtc,
        createdAt: DateTime.now().subtract(Duration(hours: 3)),
        updatedAt: DateTime.now().subtract(Duration(minutes: 15)),
        fee: '0.157',
        feeCurrency: 'USDT',
        postOnly: false,
        reduceOnly: false,
      ),

      // 止盈限价单 - 已撤销
      OrderModel(
        orderId: 'order_005',
        clientOrderId: 'client_005',
        symbol: 'SOL/USDT',
        orderType: OrderType.takeProfitLimit,
        side: OrderSide.sell,
        status: OrderStatus.cancelled,
        quantity: '10.0',
        filledQuantity: '0.0',
        price: '120.00',
        triggerPrice: '115.00',
        timeInForce: TimeInForce.gtc,
        createdAt: DateTime.now().subtract(Duration(hours: 4)),
        updatedAt: DateTime.now().subtract(Duration(hours: 1)),
        postOnly: false,
        reduceOnly: false,
      ),

      // 跟踪止损单 - 已触发
      OrderModel(
        orderId: 'order_006',
        clientOrderId: 'client_006',
        symbol: 'MATIC/USDT',
        orderType: OrderType.trailingStop,
        side: OrderSide.sell,
        status: OrderStatus.triggered,
        quantity: '500.0',
        filledQuantity: '500.0',
        triggerPrice: '0.85',
        averagePrice: '0.849',
        timeInForce: TimeInForce.gtc,
        createdAt: DateTime.now().subtract(Duration(hours: 6)),
        updatedAt: DateTime.now().subtract(Duration(minutes: 5)),
        fee: '0.424',
        feeCurrency: 'USDT',
        postOnly: false,
        reduceOnly: true,
      ),

      // 限价买单 - 已过期
      OrderModel(
        orderId: 'order_007',
        clientOrderId: 'client_007',
        symbol: 'DOT/USDT',
        orderType: OrderType.limit,
        side: OrderSide.buy,
        status: OrderStatus.expired,
        quantity: '50.0',
        filledQuantity: '0.0',
        price: '6.50',
        timeInForce: TimeInForce.gtd,
        createdAt: DateTime.now().subtract(Duration(days: 1)),
        updatedAt: DateTime.now().subtract(Duration(hours: 2)),
        expireTime: DateTime.now().subtract(Duration(hours: 2)),
        postOnly: true,
        reduceOnly: false,
      ),

      // 止损市价单 - 已拒绝
      OrderModel(
        orderId: 'order_008',
        clientOrderId: 'client_008',
        symbol: 'AVAX/USDT',
        orderType: OrderType.stopMarket,
        side: OrderSide.sell,
        status: OrderStatus.rejected,
        quantity: '20.0',
        filledQuantity: '0.0',
        triggerPrice: '35.00',
        timeInForce: TimeInForce.ioc,
        createdAt: DateTime.now().subtract(Duration(minutes: 30)),
        updatedAt: DateTime.now().subtract(Duration(minutes: 29)),
        remark: '余额不足',
        postOnly: false,
        reduceOnly: false,
      ),

      // 带止盈止损的限价单 - 待成交
      OrderModel(
        orderId: 'order_009',
        clientOrderId: 'client_009',
        symbol: 'LINK/USDT',
        orderType: OrderType.limit,
        side: OrderSide.buy,
        status: OrderStatus.pending,
        quantity: '100.0',
        filledQuantity: '0.0',
        price: '15.50',
        stopPrice: '14.00',
        takeProfitPrice: '18.00',
        timeInForce: TimeInForce.gtc,
        createdAt: DateTime.now().subtract(Duration(minutes: 20)),
        postOnly: false,
        reduceOnly: false,
      ),

      // 止盈市价单 - 待成交
      OrderModel(
        orderId: 'order_010',
        clientOrderId: 'client_010',
        symbol: 'UNI/USDT',
        orderType: OrderType.takeProfitMarket,
        side: OrderSide.sell,
        status: OrderStatus.waitingTrigger,
        quantity: '75.0',
        filledQuantity: '0.0',
        triggerPrice: '8.50',
        timeInForce: TimeInForce.gtc,
        createdAt: DateTime.now().subtract(Duration(minutes: 10)),
        postOnly: false,
        reduceOnly: false,
      ),
    ];
  }

  /// 根据状态筛选订单
  static List<OrderModel> getOrdersByStatus(OrderStatus status) {
    return createSampleOrders()
        .where((order) => order.status == status)
        .toList();
  }

  /// 根据订单类型筛选订单
  static List<OrderModel> getOrdersByType(OrderType type) {
    return createSampleOrders()
        .where((order) => order.orderType == type)
        .toList();
  }

  /// 根据交易对筛选订单
  static List<OrderModel> getOrdersBySymbol(String symbol) {
    return createSampleOrders()
        .where((order) => order.symbol == symbol)
        .toList();
  }

  /// 获取活跃订单（可操作的订单）
  static List<OrderModel> getActiveOrders() {
    return createSampleOrders()
        .where((order) => order.canModify || order.canCancel)
        .toList();
  }

  /// 获取历史订单（已完成的订单）
  static List<OrderModel> getHistoryOrders() {
    return createSampleOrders()
        .where((order) => 
            order.status == OrderStatus.filled ||
            order.status == OrderStatus.cancelled ||
            order.status == OrderStatus.rejected ||
            order.status == OrderStatus.expired)
        .toList();
  }
}
