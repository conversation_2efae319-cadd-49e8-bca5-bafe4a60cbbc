/*
*  现货资产数据模型
*
*  功能：
*  - 定义现货资产的数据结构
*  - 支持盈亏计算和格式化显示
*  - 支持不同价格类型的成本价格
*  - 提供数据验证和转换方法
*/

/// 现货资产数据模型
class SpotAssetModel {
  /// 资产ID
  final String assetId;

  /// 币种符号（如 BTC、ETH）
  final String symbol;

  /// 币种全名（如 Bitcoin、Ethereum）
  final String? fullName;

  /// 币种图标名称
  final String? iconName;

  /// 总额（原币种数量）
  final double totalAmount;

  /// 占用数量（原币种）
  final double frozenAmount;

  /// 可用数量（原币种）
  final double availableAmount;

  /// 累计盈亏（USDT）
  final double? totalPnL;

  /// 累计盈亏率（百分比）
  final double? totalPnLRate;

  /// 成本价格（USDT）- 成本价-资产
  final double? costPrice;

  /// 保本价格（USDT）- 保本价-交易
  final double? breakevenPrice;

  /// 当前市场价格（USDT）
  final double? currentPrice;

  /// 总价值（USDT）
  final double? totalValue;

  /// 法币价值（如人民币）
  final double? fiatValue;

  /// 法币符号（如 ¥、$）
  final String? fiatSymbol;

  /// 最后更新时间
  final DateTime? lastUpdated;

  const SpotAssetModel({
    required this.assetId,
    required this.symbol,
    this.fullName,
    this.iconName,
    required this.totalAmount,
    required this.frozenAmount,
    required this.availableAmount,
    this.totalPnL,
    this.totalPnLRate,
    this.costPrice,
    this.breakevenPrice,
    this.currentPrice,
    this.totalValue,
    this.fiatValue,
    this.fiatSymbol,
    this.lastUpdated,
  });

  /// 复制并修改部分字段
  SpotAssetModel copyWith({
    String? assetId,
    String? symbol,
    String? fullName,
    String? iconName,
    double? totalAmount,
    double? frozenAmount,
    double? availableAmount,
    double? totalPnL,
    double? totalPnLRate,
    double? costPrice,
    double? breakevenPrice,
    double? currentPrice,
    double? totalValue,
    double? fiatValue,
    String? fiatSymbol,
    DateTime? lastUpdated,
  }) {
    return SpotAssetModel(
      assetId: assetId ?? this.assetId,
      symbol: symbol ?? this.symbol,
      fullName: fullName ?? this.fullName,
      iconName: iconName ?? this.iconName,
      totalAmount: totalAmount ?? this.totalAmount,
      frozenAmount: frozenAmount ?? this.frozenAmount,
      availableAmount: availableAmount ?? this.availableAmount,
      totalPnL: totalPnL ?? this.totalPnL,
      totalPnLRate: totalPnLRate ?? this.totalPnLRate,
      costPrice: costPrice ?? this.costPrice,
      breakevenPrice: breakevenPrice ?? this.breakevenPrice,
      currentPrice: currentPrice ?? this.currentPrice,
      totalValue: totalValue ?? this.totalValue,
      fiatValue: fiatValue ?? this.fiatValue,
      fiatSymbol: fiatSymbol ?? this.fiatSymbol,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// 获取指定价格类型的价格
  double? getPriceByType(String priceType) {
    switch (priceType) {
      case 'cost_asset':
        return costPrice;
      case 'breakeven_trade':
        return breakevenPrice;
      default:
        return costPrice;
    }
  }

  /// 是否有盈亏数据
  bool get hasPnLData => totalPnL != null && totalPnLRate != null;

  /// 是否为正盈亏
  bool get isPositivePnL => totalPnL != null && totalPnL! > 0;

  /// 是否有价格数据
  bool get hasPriceData => costPrice != null || breakevenPrice != null;

  /// 是否有余额
  bool get hasBalance => totalAmount > 0;

  /// 格式化的总额显示
  String get formattedTotalAmount => _formatAmount(totalAmount);

  /// 格式化的占用数量显示
  String get formattedFrozenAmount => _formatAmount(frozenAmount);

  /// 格式化的可用数量显示
  String get formattedAvailableAmount => _formatAmount(availableAmount);

  /// 格式化的累计盈亏显示
  String get formattedTotalPnL {
    if (totalPnL == null) return '--';
    final sign = totalPnL! >= 0 ? '+' : '';
    return '$sign${_formatAmount(totalPnL!)} USDT';
  }

  /// 格式化的累计盈亏率显示
  String get formattedTotalPnLRate {
    if (totalPnLRate == null) return '--';
    final sign = totalPnLRate! >= 0 ? '+' : '';
    return '$sign${totalPnLRate!.toStringAsFixed(2)}%';
  }

  /// 格式化的成本价格显示
  String get formattedCostPrice {
    if (costPrice == null) return '--';
    return _formatAmount(costPrice!);
  }

  /// 格式化的保本价格显示
  String get formattedBreakevenPrice {
    if (breakevenPrice == null) return '--';
    return _formatAmount(breakevenPrice!);
  }

  /// 格式化的法币价值显示
  String get formattedFiatValue {
    if (fiatValue == null || fiatSymbol == null) return '';
    return '≈ $fiatSymbol ${_formatAmount(fiatValue!)}';
  }

  /// 获取币种图标名称（如果未设置则使用小写的symbol）
  String get effectiveIconName => iconName ?? symbol.toLowerCase();

  /// 格式化数量显示
  String _formatAmount(double amount) {
    if (amount == 0) return '0.00';
    if (amount.abs() < 0.000001) {
      return amount.toStringAsExponential(2);
    }
    if (amount.abs() < 1) {
      return amount
          .toStringAsFixed(6)
          .replaceAll(RegExp(r'0+$'), '')
          .replaceAll(RegExp(r'\.$'), '');
    }
    return amount
        .toStringAsFixed(4)
        .replaceAll(RegExp(r'0+$'), '')
        .replaceAll(RegExp(r'\.$'), '');
  }

  @override
  String toString() {
    return 'SpotAssetModel(symbol: $symbol, totalAmount: $totalAmount, totalPnL: $totalPnL)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SpotAssetModel && other.assetId == assetId;
  }

  @override
  int get hashCode => assetId.hashCode;
}

/// 现货资产列表数据模型
class SpotAssetListModel {
  /// 资产列表
  final List<SpotAssetModel> assets;

  /// 总资产价值（USDT）
  final double? totalValue;

  /// 总盈亏（USDT）
  final double? totalPnL;

  /// 总盈亏率（百分比）
  final double? totalPnLRate;

  /// 最后更新时间
  final DateTime? lastUpdated;

  const SpotAssetListModel({
    required this.assets,
    this.totalValue,
    this.totalPnL,
    this.totalPnLRate,
    this.lastUpdated,
  });

  /// 获取有余额的资产
  List<SpotAssetModel> get assetsWithBalance =>
      assets.where((asset) => asset.hasBalance).toList();

  /// 获取有盈亏数据的资产
  List<SpotAssetModel> get assetsWithPnL =>
      assets.where((asset) => asset.hasPnLData).toList();

  /// 资产总数
  int get totalCount => assets.length;

  /// 有余额的资产数量
  int get balanceCount => assetsWithBalance.length;
}
