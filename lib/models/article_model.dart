import 'package:qubic_exchange/services/common/common_service.dart';
import 'package:qubic_exchange/l10n/managers/language_manager.dart';

// 文章数据模型
class ArticleModel {
  final int id;
  final int categoryId;
  final Map<String, String> title;
  final Map<String, String> content;
  final Map<String, String> summary;
  final Map<String, String> remark;
  final List<int> currency;
  final int createdBy;
  final int updatedBy;
  final String createdAt;
  final String updatedAt;
  final List<CurrencyInfo> currencyList;

  ArticleModel({
    required this.id,
    required this.categoryId,
    required this.title,
    required this.content,
    required this.summary,
    required this.remark,
    required this.currency,
    required this.createdBy,
    required this.updatedBy,
    required this.createdAt,
    required this.updatedAt,
    required this.currencyList,
  });

  factory ArticleModel.fromJson(Map<String, dynamic> json) {
    return ArticleModel(
      id: _parseInt(json['id']) ?? 0,
      categoryId: _parseInt(json['category_id']) ?? 0,
      title: _parseStringMap(json['title']),
      content: _parseStringMap(json['content']),
      summary: _parseStringMap(json['summary']),
      remark: _parseStringMap(json['remark']),
      currency: _parseIntList(json['currency']),
      createdBy: _parseInt(json['created_by']) ?? 0,
      updatedBy: _parseInt(json['updated_by']) ?? 0,
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
      currencyList: _parseCurrencyList(json['currencyList']),
    );
  }

  // 获取本地化标题
  String get localizedTitle {
    final languageManager = LanguageManager();
    final currentLocale = languageManager.currentLocale;
    final targetLang = currentLocale.countryCode != null 
        ? '${currentLocale.languageCode}-${currentLocale.countryCode}'
        : currentLocale.languageCode;
    
    // 尝试获取对应语言的标题
    if (title.containsKey(targetLang)) {
      return title[targetLang] ?? '';
    }
    
    // 回退到中文
    if (title.containsKey('zh-CN')) {
      return title['zh-CN'] ?? '';
    }
    
    // 回退到英文
    if (title.containsKey('en')) {
      return title['en'] ?? '';
    }
    
    // 返回第一个可用的标题
    return title.values.isNotEmpty ? title.values.first : '';
  }

  // 获取本地化内容
  String get localizedContent {
    final languageManager = LanguageManager();
    final currentLocale = languageManager.currentLocale;
    final targetLang = currentLocale.countryCode != null 
        ? '${currentLocale.languageCode}-${currentLocale.countryCode}'
        : currentLocale.languageCode;
    
    // 尝试获取对应语言的内容
    if (content.containsKey(targetLang)) {
      return content[targetLang] ?? '';
    }
    
    // 回退到中文
    if (content.containsKey('zh-CN')) {
      return content['zh-CN'] ?? '';
    }
    
    // 回退到英文
    if (content.containsKey('en')) {
      return content['en'] ?? '';
    }
    
    // 返回第一个可用的内容
    return content.values.isNotEmpty ? content.values.first : '';
  }

  // 安全解析整数
  static int? _parseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) return int.tryParse(value);
    if (value is double) return value.toInt();
    return null;
  }

  // 安全解析字符串Map
  static Map<String, String> _parseStringMap(dynamic value) {
    if (value == null) return {};
    if (value is Map) {
      return value.map((key, val) => MapEntry(key.toString(), val?.toString() ?? ''));
    }
    return {};
  }

  // 安全解析整数列表
  static List<int> _parseIntList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((e) => _parseInt(e) ?? 0).toList();
    }
    return [];
  }

  // 安全解析货币信息列表
  static List<CurrencyInfo> _parseCurrencyList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value
          .whereType<Map<String, dynamic>>()
          .map((item) => CurrencyInfo.fromJson(item))
          .toList();
    }
    return [];
  }
}

// 货币信息模型
class CurrencyInfo {
  final int id;
  final int symbolId;
  final String symbol;
  final String? url;
  final int createdBy;
  final int updatedBy;
  final String createdAt;
  final String updatedAt;

  CurrencyInfo({
    required this.id,
    required this.symbolId,
    required this.symbol,
    this.url,
    required this.createdBy,
    required this.updatedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CurrencyInfo.fromJson(Map<String, dynamic> json) {
    return CurrencyInfo(
      id: ArticleModel._parseInt(json['id']) ?? 0,
      symbolId: ArticleModel._parseInt(json['symbol_id']) ?? 0,
      symbol: json['symbol']?.toString() ?? '',
      url: json['url']?.toString(),
      createdBy: ArticleModel._parseInt(json['created_by']) ?? 0,
      updatedBy: ArticleModel._parseInt(json['updated_by']) ?? 0,
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
    );
  }
}
