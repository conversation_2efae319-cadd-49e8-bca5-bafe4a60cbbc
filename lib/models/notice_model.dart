import 'package:qubic_exchange/services/common/common_service.dart';
import 'package:qubic_exchange/l10n/managers/language_manager.dart';

// 公告数据模型
class NoticeModel {
  final int id;
  final int categoryId;
  final List<NoticeText> title;
  final List<NoticeText> content;
  final int lookNum;
  final int createdBy;
  final int updatedBy;
  final String createdAt;
  final String updatedAt;

  NoticeModel({
    required this.id,
    required this.categoryId,
    required this.title,
    required this.content,
    required this.lookNum,
    required this.createdBy,
    required this.updatedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NoticeModel.fromJson(Map<String, dynamic> json) {
    return NoticeModel(
      id: _parseInt(json['id']) ?? 0,
      categoryId: _parseInt(json['category_id']) ?? 0,
      title: _parseTextList(json['title']),
      content: _parseTextList(json['content']),
      lookNum: _parseInt(json['look_num']) ?? 0,
      createdBy: _parseInt(json['created_by']) ?? 0,
      updatedBy: _parseInt(json['updated_by']) ?? 0,
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
    );
  }

  // 获取本地化标题
  String get localizedTitle {
    final langList = title.map((text) => {
      'lang': text.lang,
      'text': text.text,
    }).toList();
    
    final languageManager = LanguageManager();
    final currentLocale = languageManager.currentLocale;
    final targetLang = currentLocale.countryCode != null 
        ? '${currentLocale.languageCode}_${currentLocale.countryCode}'
        : currentLocale.languageCode;
    
    return CommonService.getLocalizedText(langList, targetLang: targetLang);
  }

  // 获取本地化内容
  String get localizedContent {
    final langList = content.map((text) => {
      'lang': text.lang,
      'text': text.text,
    }).toList();
    
    final languageManager = LanguageManager();
    final currentLocale = languageManager.currentLocale;
    final targetLang = currentLocale.countryCode != null 
        ? '${currentLocale.languageCode}_${currentLocale.countryCode}'
        : currentLocale.languageCode;
    
    return CommonService.getLocalizedText(langList, targetLang: targetLang);
  }

  // 安全解析整数
  static int? _parseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) return int.tryParse(value);
    if (value is double) return value.toInt();
    return null;
  }

  // 安全解析文本列表
  static List<NoticeText> _parseTextList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value
          .whereType<Map<String, dynamic>>()
          .map((item) => NoticeText.fromJson(item))
          .toList();
    }
    return [];
  }
}

// 公告文本模型
class NoticeText {
  final String lang;
  final String text;

  NoticeText({
    required this.lang,
    required this.text,
  });

  factory NoticeText.fromJson(Map<String, dynamic> json) {
    return NoticeText(
      lang: json['lang']?.toString() ?? '',
      text: json['text']?.toString() ?? '',
    );
  }
}
