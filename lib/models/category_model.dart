import 'package:qubic_exchange/services/common/common_service.dart';
import 'package:qubic_exchange/l10n/managers/language_manager.dart';

// 分类栏目数据模型
class CategoryModel {
  final int id;
  final String key;
  final List<CategoryName> name;
  final int sort;
  final int createdBy;
  final int updatedBy;
  final String createdAt;
  final String updatedAt;

  CategoryModel({
    required this.id,
    required this.key,
    required this.name,
    required this.sort,
    required this.createdBy,
    required this.updatedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    return CategoryModel(
      id: _parseInt(json['id']) ?? 0,
      key: json['key']?.toString() ?? '',
      name: _parseNameList(json['name']),
      sort: _parseInt(json['sort']) ?? 0,
      createdBy: _parseInt(json['created_by']) ?? 0,
      updatedBy: _parseInt(json['updated_by']) ?? 0,
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
    );
  }

  // 获取本地化名称
  String get displayName {
    // 将CategoryName列表转换为CommonService需要的格式
    final langList = name.map((categoryName) => {'lang': categoryName.lang, 'text': categoryName.text}).toList();

    // 获取当前设置的语言
    final languageManager = LanguageManager();
    final currentLocale = languageManager.currentLocale;
    final targetLang =
        currentLocale.countryCode != null ? '${currentLocale.languageCode}_${currentLocale.countryCode}' : currentLocale.languageCode;

    // 使用CommonService获取本地化文本
    return CommonService.getLocalizedText(langList, targetLang: targetLang);
  }

  // 安全解析整数
  static int? _parseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) return int.tryParse(value);
    if (value is double) return value.toInt();
    return null;
  }

  // 安全解析名称列表
  static List<CategoryName> _parseNameList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.whereType<Map<String, dynamic>>().map((item) => CategoryName.fromJson(item)).toList();
    }
    return [];
  }
}

// 分类名称模型
class CategoryName {
  final String lang;
  final String text;

  CategoryName({required this.lang, required this.text});

  factory CategoryName.fromJson(Map<String, dynamic> json) {
    return CategoryName(lang: json['lang']?.toString() ?? '', text: json['text']?.toString() ?? '');
  }
}
