class SupportedCurrency {
  final String symbol;
  final String name;
  final String iconUrl;
  final String value;

  const SupportedCurrency({
    required this.symbol,
    required this.name,
    required this.iconUrl,
    required this.value,
  });

  factory SupportedCurrency.fromJson(Map<String, dynamic> json) {
    return SupportedCurrency(
      symbol: json['symbol'] ?? '',
      name: json['name'] ?? '',
      iconUrl: json['iconUrl'] ?? '',
      value: json['value'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'name': name,
      'iconUrl': iconUrl,
      'value': value,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SupportedCurrency &&
        other.symbol == symbol &&
        other.name == name &&
        other.iconUrl == iconUrl &&
        other.value == value;
  }

  @override
  int get hashCode {
    return Object.hash(symbol, name, iconUrl, value);
  }
}
