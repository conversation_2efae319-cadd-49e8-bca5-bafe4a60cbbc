import 'supported_currency_model.dart';
import 'digital_currency_model.dart';

class CurrencyResponseModel {
  final Map<String, String> country;
  final String recommend;
  final List<DigitalCurrency> digitalCurrency;
  final Map<String, String> rates;
  final List<SupportedCurrency> supported;

  const CurrencyResponseModel({
    required this.country,
    required this.recommend,
    required this.digitalCurrency,
    required this.rates,
    required this.supported,
  });

  factory CurrencyResponseModel.fromJson(Map<String, dynamic> json) {
    // API返回的数据结构中，实际数据就在传入的json中，不需要再取data字段
    return CurrencyResponseModel(
      country: Map<String, String>.from(json['country'] ?? {}),
      recommend: json['recommend'] ?? '',
      digitalCurrency: (json['digitalCurrency'] as List<dynamic>?)?.map((item) => DigitalCurrency.fromJson(item)).toList() ?? [],
      rates: Map<String, String>.from(json['rates'] ?? {}),
      supported: (json['supported'] as List<dynamic>?)?.map((item) => SupportedCurrency.fromJson(item)).toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': {
        'country': country,
        'recommend': recommend,
        'digitalCurrency': digitalCurrency.map((item) => item.toJson()).toList(),
        'rates': rates,
        'supported': supported.map((item) => item.toJson()).toList(),
      },
    };
  }
}
