class DigitalCurrency {
  final int coinId;
  final String coinName;

  const DigitalCurrency({
    required this.coinId,
    required this.coinName,
  });

  factory DigitalCurrency.fromJson(Map<String, dynamic> json) {
    return DigitalCurrency(
      coinId: json['coinId'] ?? 0,
      coinName: json['coinName'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'coinId': coinId,
      'coinName': coinName,
    };
  }
}
