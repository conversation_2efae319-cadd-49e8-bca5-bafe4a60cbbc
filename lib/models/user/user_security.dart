/// 用户安全设置模型
class UserSecurity {
  final String? userId;
  final bool emailVerified;         // 邮箱验证状态
  final bool phoneVerified;         // 手机验证状态
  final bool identityVerified;      // 身份验证状态
  final bool twoFactorEnabled;      // 双因子认证状态
  final bool loginPasswordSet;      // 登录密码设置状态
  final bool tradingPasswordSet;    // 交易密码设置状态
  final DateTime? lastLoginTime;    // 最后登录时间
  final String? lastLoginIp;        // 最后登录IP
  final String? lastLoginDevice;    // 最后登录设备
  final List<String>? trustedDevices; // 信任设备列表
  final Map<String, dynamic>? securitySettings; // 其他安全设置

  const UserSecurity({
    this.userId,
    this.emailVerified = false,
    this.phoneVerified = false,
    this.identityVerified = false,
    this.twoFactorEnabled = false,
    this.loginPasswordSet = false,
    this.tradingPasswordSet = false,
    this.lastLoginTime,
    this.lastLoginIp,
    this.lastLoginDevice,
    this.trustedDevices,
    this.securitySettings,
  });

  /// 从JSON创建UserSecurity对象
  factory UserSecurity.fromJson(Map<String, dynamic> json) {
    return UserSecurity(
      userId: json['user_id']?.toString(),
      emailVerified: json['email_verified'] ?? false,
      phoneVerified: json['phone_verified'] ?? false,
      identityVerified: json['identity_verified'] ?? false,
      twoFactorEnabled: json['two_factor_enabled'] ?? false,
      loginPasswordSet: json['login_password_set'] ?? false,
      tradingPasswordSet: json['trading_password_set'] ?? false,
      lastLoginTime: json['last_login_time'] != null
          ? DateTime.parse(json['last_login_time'])
          : null,
      lastLoginIp: json['last_login_ip'],
      lastLoginDevice: json['last_login_device'],
      trustedDevices: json['trusted_devices'] != null
          ? List<String>.from(json['trusted_devices'])
          : null,
      securitySettings: json['security_settings'] as Map<String, dynamic>?,
    );
  }

  /// 将UserSecurity对象转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'email_verified': emailVerified,
      'phone_verified': phoneVerified,
      'identity_verified': identityVerified,
      'two_factor_enabled': twoFactorEnabled,
      'login_password_set': loginPasswordSet,
      'trading_password_set': tradingPasswordSet,
      'last_login_time': lastLoginTime?.toIso8601String(),
      'last_login_ip': lastLoginIp,
      'last_login_device': lastLoginDevice,
      'trusted_devices': trustedDevices,
      'security_settings': securitySettings,
    };
  }

  /// 获取安全等级（0-100）
  int get securityLevel {
    int level = 0;
    if (emailVerified) level += 20;
    if (phoneVerified) level += 20;
    if (identityVerified) level += 20;
    if (twoFactorEnabled) level += 20;
    if (loginPasswordSet) level += 10;
    if (tradingPasswordSet) level += 10;
    return level;
  }

  /// 获取安全等级描述
  String get securityLevelDescription {
    final level = securityLevel;
    if (level >= 80) return '高';
    if (level >= 60) return '中';
    if (level >= 40) return '低';
    return '很低';
  }

  /// 检查是否完成基础验证
  bool get isBasicVerified {
    return emailVerified && phoneVerified;
  }

  /// 检查是否完成高级验证
  bool get isAdvancedVerified {
    return isBasicVerified && identityVerified && twoFactorEnabled;
  }

  /// 创建副本并更新指定字段
  UserSecurity copyWith({
    String? userId,
    bool? emailVerified,
    bool? phoneVerified,
    bool? identityVerified,
    bool? twoFactorEnabled,
    bool? loginPasswordSet,
    bool? tradingPasswordSet,
    DateTime? lastLoginTime,
    String? lastLoginIp,
    String? lastLoginDevice,
    List<String>? trustedDevices,
    Map<String, dynamic>? securitySettings,
  }) {
    return UserSecurity(
      userId: userId ?? this.userId,
      emailVerified: emailVerified ?? this.emailVerified,
      phoneVerified: phoneVerified ?? this.phoneVerified,
      identityVerified: identityVerified ?? this.identityVerified,
      twoFactorEnabled: twoFactorEnabled ?? this.twoFactorEnabled,
      loginPasswordSet: loginPasswordSet ?? this.loginPasswordSet,
      tradingPasswordSet: tradingPasswordSet ?? this.tradingPasswordSet,
      lastLoginTime: lastLoginTime ?? this.lastLoginTime,
      lastLoginIp: lastLoginIp ?? this.lastLoginIp,
      lastLoginDevice: lastLoginDevice ?? this.lastLoginDevice,
      trustedDevices: trustedDevices ?? this.trustedDevices,
      securitySettings: securitySettings ?? this.securitySettings,
    );
  }

  @override
  String toString() {
    return 'UserSecurity(userId: $userId, securityLevel: $securityLevel, isBasicVerified: $isBasicVerified)';
  }
}
