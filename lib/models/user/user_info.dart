/// 用户信息模型
class UserInfo {
  final int? id;
  final int? parentId;
  final String? account;
  final String? username;
  final String? nickname;
  final String? displayName;
  final String? email;
  final String? phone;
  final String? phoneCountryCode;
  final String? avatar;
  final String? avatarUrl;
  final String? inviteCode;
  final int? registerType;
  final int? status;
  final String? lastLoginAt;
  final String? lastLoginIp;
  final String? lastLoginDevice;
  final dynamic socialBindings;
  final int? agentId;
  final int? agentClientId;
  final int? contractExpertId;
  final int? spotExpertId;
  final dynamic concernUids;
  final String? introduction;
  final String? language;
  final String? createdAt;
  final String? updatedAt;
  final String? deletedAt;
  final dynamic kycVerification;
  final bool isVip;

  UserInfo({
    this.id,
    this.parentId,
    this.account,
    this.username,
    this.nickname,
    this.displayName,
    this.email,
    this.phone,
    this.phoneCountryCode,
    this.avatar,
    this.avatarUrl,
    this.inviteCode,
    this.registerType,
    this.status,
    this.lastLoginAt,
    this.lastLoginIp,
    this.lastLoginDevice,
    this.socialBindings,
    this.agentId,
    this.agentClientId,
    this.contractExpertId,
    this.spotExpertId,
    this.concernUids,
    this.introduction,
    this.language,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.kycVerification,
    this.isVip = true,
  });

  // 从JSON创建UserInfo对象
  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      id: json['id'] as int?,
      parentId: json['parent_id'] as int?,
      account: json['account'] as String?,
      username: json['username'] as String?,
      nickname: json['nickname'] as String?,
      displayName: json['display_name'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      phoneCountryCode: json['phone_country_code'] as String?,
      avatar: json['avatar'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      inviteCode: json['invite_code'] as String?,
      registerType: json['register_type'] as int?,
      status: json['status'] as int?,
      lastLoginAt: json['last_login_at'] as String?,
      lastLoginIp: json['last_login_ip'] as String?,
      lastLoginDevice: json['last_login_device'] as String?,
      socialBindings: json['social_bindings'],
      agentId: json['agent_id'] as int?,
      agentClientId: json['agent_client_id'] as int?,
      contractExpertId: json['contract_expert_id'] as int?,
      spotExpertId: json['spot_expert_id'] as int?,
      concernUids: json['concern_uids'],
      introduction: json['introduction'] as String?,
      language: json['language'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      deletedAt: json['deleted_at'] as String?,
      kycVerification: json['kyc_verification'],
      isVip: json['is_vip'] as bool? ?? true,
    );
  }

  // 将UserInfo对象转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'parent_id': parentId,
      'account': account,
      'username': username,
      'nickname': nickname,
      'display_name': displayName,
      'email': email,
      'phone': phone,
      'phone_country_code': phoneCountryCode,
      'avatar': avatar,
      'avatar_url': avatarUrl,
      'invite_code': inviteCode,
      'register_type': registerType,
      'status': status,
      'last_login_at': lastLoginAt,
      'last_login_ip': lastLoginIp,
      'last_login_device': lastLoginDevice,
      'social_bindings': socialBindings,
      'agent_id': agentId,
      'agent_client_id': agentClientId,
      'contract_expert_id': contractExpertId,
      'spot_expert_id': spotExpertId,
      'concern_uids': concernUids,
      'introduction': introduction,
      'language': language,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
      'kyc_verification': kycVerification,
      'is_vip': isVip,
    };
  }

  // 便捷方法：获取显示名称
  String get displayNameOrUsername => displayName ?? username ?? account ?? 'Unknown';

  // 便捷方法：获取头像URL
  String get effectiveAvatarUrl => avatarUrl ?? avatar ?? '';

  // 便捷方法：检查是否为活跃用户
  bool get isActive => status == 1;

  // 便捷方法：检查是否有父账户
  bool get hasParent => parentId != null;
}
