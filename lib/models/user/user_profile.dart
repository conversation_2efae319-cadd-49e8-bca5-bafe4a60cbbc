/// 用户配置模型
class UserProfile {
  final String? userId;
  final String? language;           // 语言设置
  final String? currency;           // 默认货币
  final String? timezone;           // 时区设置
  final bool notificationsEnabled;  // 通知开关
  final bool emailNotifications;    // 邮件通知
  final bool smsNotifications;      // 短信通知
  final bool pushNotifications;     // 推送通知
  final bool twoFactorEnabled;      // 双因子认证
  final String? theme;              // 主题设置
  final Map<String, dynamic>? tradingPreferences; // 交易偏好设置

  const UserProfile({
    this.userId,
    this.language,
    this.currency,
    this.timezone,
    this.notificationsEnabled = true,
    this.emailNotifications = true,
    this.smsNotifications = true,
    this.pushNotifications = true,
    this.twoFactorEnabled = false,
    this.theme,
    this.tradingPreferences,
  });

  /// 从JSON创建UserProfile对象
  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      userId: json['user_id']?.toString(),
      language: json['language'],
      currency: json['currency'],
      timezone: json['timezone'],
      notificationsEnabled: json['notifications_enabled'] ?? true,
      emailNotifications: json['email_notifications'] ?? true,
      smsNotifications: json['sms_notifications'] ?? true,
      pushNotifications: json['push_notifications'] ?? true,
      twoFactorEnabled: json['two_factor_enabled'] ?? false,
      theme: json['theme'],
      tradingPreferences: json['trading_preferences'] as Map<String, dynamic>?,
    );
  }

  /// 将UserProfile对象转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'language': language,
      'currency': currency,
      'timezone': timezone,
      'notifications_enabled': notificationsEnabled,
      'email_notifications': emailNotifications,
      'sms_notifications': smsNotifications,
      'push_notifications': pushNotifications,
      'two_factor_enabled': twoFactorEnabled,
      'theme': theme,
      'trading_preferences': tradingPreferences,
    };
  }

  /// 创建副本并更新指定字段
  UserProfile copyWith({
    String? userId,
    String? language,
    String? currency,
    String? timezone,
    bool? notificationsEnabled,
    bool? emailNotifications,
    bool? smsNotifications,
    bool? pushNotifications,
    bool? twoFactorEnabled,
    String? theme,
    Map<String, dynamic>? tradingPreferences,
  }) {
    return UserProfile(
      userId: userId ?? this.userId,
      language: language ?? this.language,
      currency: currency ?? this.currency,
      timezone: timezone ?? this.timezone,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      smsNotifications: smsNotifications ?? this.smsNotifications,
      pushNotifications: pushNotifications ?? this.pushNotifications,
      twoFactorEnabled: twoFactorEnabled ?? this.twoFactorEnabled,
      theme: theme ?? this.theme,
      tradingPreferences: tradingPreferences ?? this.tradingPreferences,
    );
  }

  @override
  String toString() {
    return 'UserProfile(userId: $userId, language: $language, currency: $currency)';
  }
}
