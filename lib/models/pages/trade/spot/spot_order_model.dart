/*
 * 现货订单数据模型
 */

import 'package:flutter/material.dart';

/// 订单类型枚举
enum SpotOrderType {
  /// 限价单
  limit('限价'),

  /// 市价单
  market('市价'),

  /// 计划委托
  planOrder('计划委托'),

  /// OCO订单
  oco('OCO'),

  /// 止盈止损
  stopProfitLoss('止盈止损'),

  /// 追踪委托
  trailingOrder('追踪委托');

  const SpotOrderType(this.displayName);
  final String displayName;
}

/// 交易方向枚举
enum TradeDirection {
  /// 买入
  buy('买入'),

  /// 卖出
  sell('卖出');

  const TradeDirection(this.displayName);
  final String displayName;

  /// 获取对应的颜色类型
  bool get isBuy => this == TradeDirection.buy;
}

/// 订单状态枚举
enum SpotOrderStatus {
  /// 等待执行
  waitingExecution('等待执行'),

  /// 待成交
  pending('待成交'),

  /// 部分成交
  partialFilled('部分成交'),

  /// 已成交
  filled('已成交'),

  /// 已撤销
  cancelled('已撤销'),

  /// 已拒绝
  rejected('已拒绝');

  const SpotOrderStatus(this.displayName);
  final String displayName;
}

/// 现货订单数据模型
class SpotOrderModel {
  /// 订单ID
  final String orderId;

  /// 交易对
  final String symbol;

  /// 交易方向
  final TradeDirection direction;

  /// 订单类型
  final SpotOrderType orderType;

  /// 订单状态
  final SpotOrderStatus status;

  /// 委托价格
  final String price;

  /// 委托数量
  final String quantity;

  /// 已成交数量
  final String filledQuantity;

  /// 成交均价
  final String avgPrice;

  /// 委托金额
  final String amount;

  /// 已成交金额
  final String filledAmount;

  /// 手续费
  final String fee;

  /// 创建时间
  final DateTime createTime;

  /// 更新时间
  final DateTime? updateTime;

  /// 止盈价格（可选）
  final String? takeProfitPrice;

  /// 止损价格（可选）
  final String? stopLossPrice;

  /// 触发价格（计划委托等）
  final String? triggerPrice;

  const SpotOrderModel({
    required this.orderId,
    required this.symbol,
    required this.direction,
    required this.orderType,
    required this.status,
    required this.price,
    required this.quantity,
    required this.filledQuantity,
    required this.avgPrice,
    required this.amount,
    required this.filledAmount,
    required this.fee,
    required this.createTime,
    this.updateTime,
    this.takeProfitPrice,
    this.stopLossPrice,
    this.triggerPrice,
  });

  /// 获取基础货币
  String get baseCurrency => symbol.split('/').first;

  /// 获取计价货币
  String get quoteCurrency => symbol.split('/').last;

  /// 获取成交进度（0.0 - 1.0）
  double get fillProgress {
    final qty = double.tryParse(quantity) ?? 0;
    final filled = double.tryParse(filledQuantity) ?? 0;
    if (qty == 0) return 0;
    return (filled / qty).clamp(0.0, 1.0);
  }

  /// 是否有止盈止损
  bool get hasStopProfitLoss =>
      takeProfitPrice != null || stopLossPrice != null;

  /// 是否可以修改
  bool get canModify =>
      status == SpotOrderStatus.waitingExecution ||
      status == SpotOrderStatus.pending ||
      status == SpotOrderStatus.partialFilled;

  /// 是否可以撤销
  bool get canCancel =>
      status == SpotOrderStatus.waitingExecution ||
      status == SpotOrderStatus.pending ||
      status == SpotOrderStatus.partialFilled;

  /// 是否为活跃订单（在委托列表中显示）
  bool get isActive =>
      status == SpotOrderStatus.pending ||
      status == SpotOrderStatus.partialFilled;

  /// 是否为历史订单（在历史记录中显示）
  bool get isHistory =>
      status == SpotOrderStatus.filled ||
      status == SpotOrderStatus.cancelled ||
      status == SpotOrderStatus.rejected;

  /// 创建测试数据
  factory SpotOrderModel.mock({
    String? symbol,
    TradeDirection? direction,
    SpotOrderType? orderType,
    SpotOrderStatus? status,
  }) {
    final type = orderType ?? SpotOrderType.limit;

    // 根据交易对设置不同的测试数据
    final Map<String, Map<String, String>> mockData = {
      'BTC/USDT': {
        'price': '80,000',
        'quantity': '0.004494',
        'amount': '359.52',
        'triggerPrice': '≤80,000',
      },
      'ETH/USDT': {
        'price': '3,200',
        'quantity': '0.5000',
        'amount': '1,600.00',
        'triggerPrice': '≤3,200',
      },
      'BGB/USDT': {
        'price': '4',
        'quantity': '1',
        'amount': '4',
        'triggerPrice': '≥5.5',
      },
      'SOL/USDT': {
        'price': '180',
        'quantity': '2.5000',
        'amount': '450.00',
        'triggerPrice': '≤180',
      },
      'ADA/USDT': {
        'price': '0.85',
        'quantity': '1000',
        'amount': '850.00',
        'triggerPrice': '≥0.90',
      },
    };

    final data = mockData[symbol] ?? mockData['BTC/USDT']!;

    return SpotOrderModel(
      orderId: 'ORDER_${DateTime.now().millisecondsSinceEpoch}',
      symbol: symbol ?? 'BTC/USDT',
      direction: direction ?? TradeDirection.buy,
      orderType: type,
      status: status ?? SpotOrderStatus.pending,
      price: data['price']!,
      quantity: data['quantity']!,
      filledQuantity: '0',
      avgPrice: '0.00',
      amount: data['amount']!,
      filledAmount: '0.00',
      fee: '0.00',
      createTime: DateTime.now(),
      // 根据订单类型设置触发价格
      triggerPrice:
          (type == SpotOrderType.planOrder ||
                  type == SpotOrderType.stopProfitLoss ||
                  type == SpotOrderType.oco)
              ? data['triggerPrice']!
              : null,
    );
  }

  /// 从JSON创建实例
  factory SpotOrderModel.fromJson(Map<String, dynamic> json) {
    return SpotOrderModel(
      orderId: json['orderId'] ?? '',
      symbol: json['symbol'] ?? '',
      direction: TradeDirection.values.firstWhere(
        (e) => e.name == json['direction'],
        orElse: () => TradeDirection.buy,
      ),
      orderType: SpotOrderType.values.firstWhere(
        (e) => e.name == json['orderType'],
        orElse: () => SpotOrderType.limit,
      ),
      status: SpotOrderStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => SpotOrderStatus.pending,
      ),
      price: json['price']?.toString() ?? '0',
      quantity: json['quantity']?.toString() ?? '0',
      filledQuantity: json['filledQuantity']?.toString() ?? '0',
      avgPrice: json['avgPrice']?.toString() ?? '0',
      amount: json['amount']?.toString() ?? '0',
      filledAmount: json['filledAmount']?.toString() ?? '0',
      fee: json['fee']?.toString() ?? '0',
      createTime: DateTime.tryParse(json['createTime'] ?? '') ?? DateTime.now(),
      updateTime:
          json['updateTime'] != null
              ? DateTime.tryParse(json['updateTime'])
              : null,
      takeProfitPrice: json['takeProfitPrice']?.toString(),
      stopLossPrice: json['stopLossPrice']?.toString(),
      triggerPrice: json['triggerPrice']?.toString(),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'orderId': orderId,
      'symbol': symbol,
      'direction': direction.name,
      'orderType': orderType.name,
      'status': status.name,
      'price': price,
      'quantity': quantity,
      'filledQuantity': filledQuantity,
      'avgPrice': avgPrice,
      'amount': amount,
      'filledAmount': filledAmount,
      'fee': fee,
      'createTime': createTime.toIso8601String(),
      'updateTime': updateTime?.toIso8601String(),
      'takeProfitPrice': takeProfitPrice,
      'stopLossPrice': stopLossPrice,
      'triggerPrice': triggerPrice,
    };
  }
}
