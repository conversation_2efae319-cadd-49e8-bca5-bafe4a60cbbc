/// 注册响应模型
class RegisterResponse {
  final bool success;
  final String? message;
  final String? sessionId;
  final String? userId;
  final Map<String, dynamic>? userData;
  final bool requiresVerification;

  const RegisterResponse({
    required this.success,
    this.message,
    this.sessionId,
    this.userId,
    this.userData,
    this.requiresVerification = true,
  });

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'sessionId': sessionId,
      'userId': userId,
      'userData': userData,
      'requiresVerification': requiresVerification,
    };
  }

  /// 从JSON创建实例
  factory RegisterResponse.fromJson(Map<String, dynamic> json) {
    return RegisterResponse(
      success: json['success'] as bool,
      message: json['message'] as String?,
      sessionId: json['sessionId'] as String?,
      userId: json['userId'] as String?,
      userData: json['userData'] as Map<String, dynamic>?,
      requiresVerification: json['requiresVerification'] as bool? ?? true,
    );
  }

  /// 创建成功响应
  factory RegisterResponse.success({
    String? sessionId,
    String? userId,
    Map<String, dynamic>? userData,
    bool requiresVerification = true,
  }) {
    return RegisterResponse(
      success: true,
      sessionId: sessionId,
      userId: userId,
      userData: userData,
      requiresVerification: requiresVerification,
    );
  }

  /// 创建失败响应
  factory RegisterResponse.failure(String message) {
    return RegisterResponse(
      success: false,
      message: message,
      requiresVerification: false,
    );
  }

  /// 创建需要验证的响应
  factory RegisterResponse.needsVerification({
    required String sessionId,
    String? message,
  }) {
    return RegisterResponse(
      success: true,
      message: message ?? '注册成功，请完成验证',
      sessionId: sessionId,
      requiresVerification: true,
    );
  }

  @override
  String toString() {
    return 'RegisterResponse(success: $success, message: $message, requiresVerification: $requiresVerification)';
  }
}
