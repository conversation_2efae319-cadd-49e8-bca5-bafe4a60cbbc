/// 注册请求模型
class RegisterRequest {
  final String registrationType; // 'email' 或 'phone'
  final String? email;           // 邮箱（邮箱注册时使用）
  final String? phone;           // 手机号（手机注册时使用）
  final String? countryCode;     // 国家区号（手机注册时使用）
  final String password;         // 密码
  final String? inviteCode;      // 邀请码（可选）
  final bool agreeToTerms;       // 是否同意条款

  const RegisterRequest({
    required this.registrationType,
    this.email,
    this.phone,
    this.countryCode,
    required this.password,
    this.inviteCode,
    required this.agreeToTerms,
  });

  /// 验证注册请求数据
  bool get isValid {
    if (!agreeToTerms) return false;
    if (password.isEmpty) return false;
    
    if (registrationType == 'email') {
      return email != null && email!.isNotEmpty && _isValidEmail(email!);
    } else if (registrationType == 'phone') {
      return phone != null && phone!.isNotEmpty && countryCode != null;
    }
    
    return false;
  }

  /// 验证邮箱格式
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// 获取注册账户标识
  String get accountIdentifier {
    if (registrationType == 'email') {
      return email ?? '';
    } else {
      return '${countryCode ?? ''}${phone ?? ''}';
    }
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'registrationType': registrationType,
      'email': email,
      'phone': phone,
      'countryCode': countryCode,
      'password': password,
      'inviteCode': inviteCode,
      'agreeToTerms': agreeToTerms,
    };
  }

  /// 从JSON创建实例
  factory RegisterRequest.fromJson(Map<String, dynamic> json) {
    return RegisterRequest(
      registrationType: json['registrationType'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      countryCode: json['countryCode'] as String?,
      password: json['password'] as String,
      inviteCode: json['inviteCode'] as String?,
      agreeToTerms: json['agreeToTerms'] as bool,
    );
  }

  /// 创建邮箱注册请求
  factory RegisterRequest.email({
    required String email,
    required String password,
    String? inviteCode,
    required bool agreeToTerms,
  }) {
    return RegisterRequest(
      registrationType: 'email',
      email: email,
      password: password,
      inviteCode: inviteCode,
      agreeToTerms: agreeToTerms,
    );
  }

  /// 创建手机注册请求
  factory RegisterRequest.phone({
    required String phone,
    required String countryCode,
    required String password,
    String? inviteCode,
    required bool agreeToTerms,
  }) {
    return RegisterRequest(
      registrationType: 'phone',
      phone: phone,
      countryCode: countryCode,
      password: password,
      inviteCode: inviteCode,
      agreeToTerms: agreeToTerms,
    );
  }

  @override
  String toString() {
    return 'RegisterRequest(type: $registrationType, account: $accountIdentifier)';
  }
}
