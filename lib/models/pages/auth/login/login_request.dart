/// 登录请求模型
class LoginRequest {
  final String accountType; // 'main' 或 'sub'
  final String account;     // 邮箱/手机号/子账户名
  final String password;    // 密码

  const LoginRequest({
    required this.accountType,
    required this.account,
    required this.password,
  });

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'accountType': accountType,
      'account': account,
      'password': password,
    };
  }

  /// 从JSON创建实例
  factory LoginRequest.fromJson(Map<String, dynamic> json) {
    return LoginRequest(
      accountType: json['accountType'] as String,
      account: json['account'] as String,
      password: json['password'] as String,
    );
  }

  @override
  String toString() {
    return 'LoginRequest(accountType: $accountType, account: $account)';
  }
}
