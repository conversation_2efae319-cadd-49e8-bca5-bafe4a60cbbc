/// 登录响应模型
class LoginResponse {
  final bool success;
  final String? message;
  final String? sessionId;
  final Map<String, dynamic>? userData;

  const LoginResponse({
    required this.success,
    this.message,
    this.sessionId,
    this.userData,
  });

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'sessionId': sessionId,
      'userData': userData,
    };
  }

  /// 从JSON创建实例
  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      success: json['success'] as bool,
      message: json['message'] as String?,
      sessionId: json['sessionId'] as String?,
      userData: json['userData'] as Map<String, dynamic>?,
    );
  }

  /// 创建成功响应
  factory LoginResponse.success({
    String? sessionId,
    Map<String, dynamic>? userData,
  }) {
    return LoginResponse(
      success: true,
      sessionId: sessionId,
      userData: userData,
    );
  }

  /// 创建失败响应
  factory LoginResponse.failure(String message) {
    return LoginResponse(
      success: false,
      message: message,
    );
  }

  @override
  String toString() {
    return 'LoginResponse(success: $success, message: $message)';
  }
}
