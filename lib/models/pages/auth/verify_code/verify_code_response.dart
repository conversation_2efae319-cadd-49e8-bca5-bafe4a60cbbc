/// 验证码响应模型
class VerifyCodeResponse {
  final bool success;
  final String? message;
  final String? accessToken;
  final String? refreshToken;
  final DateTime? expireAt;
  final Map<String, dynamic>? userData;

  const VerifyCodeResponse({
    required this.success,
    this.message,
    this.accessToken,
    this.refreshToken,
    this.expireAt,
    this.userData,
  });

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'expireAt': expireAt?.toIso8601String(),
      'userData': userData,
    };
  }

  /// 从JSON创建实例
  factory VerifyCodeResponse.fromJson(Map<String, dynamic> json) {
    return VerifyCodeResponse(
      success: json['success'] as bool,
      message: json['message'] as String?,
      accessToken: json['accessToken'] as String?,
      refreshToken: json['refreshToken'] as String?,
      expireAt: json['expireAt'] != null 
          ? DateTime.parse(json['expireAt'] as String)
          : null,
      userData: json['userData'] as Map<String, dynamic>?,
    );
  }

  /// 创建成功响应
  factory VerifyCodeResponse.success({
    String? accessToken,
    String? refreshToken,
    DateTime? expireAt,
    Map<String, dynamic>? userData,
  }) {
    return VerifyCodeResponse(
      success: true,
      accessToken: accessToken,
      refreshToken: refreshToken,
      expireAt: expireAt,
      userData: userData,
    );
  }

  /// 创建失败响应
  factory VerifyCodeResponse.failure(String message) {
    return VerifyCodeResponse(
      success: false,
      message: message,
    );
  }

  @override
  String toString() {
    return 'VerifyCodeResponse(success: $success, message: $message)';
  }
}
