/// 验证码请求模型
class VerifyCodeRequest {
  final String sessionId;
  final String emailCode;
  final String phoneCode;
  final String googleCode;

  const VerifyCodeRequest({
    required this.sessionId,
    required this.emailCode,
    required this.phoneCode,
    required this.googleCode,
  });

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'sessionId': sessionId,
      'emailCode': emailCode,
      'phoneCode': phoneCode,
      'googleCode': googleCode,
    };
  }

  /// 从JSON创建实例
  factory VerifyCodeRequest.fromJson(Map<String, dynamic> json) {
    return VerifyCodeRequest(
      sessionId: json['sessionId'] as String,
      emailCode: json['emailCode'] as String,
      phoneCode: json['phoneCode'] as String,
      googleCode: json['googleCode'] as String,
    );
  }

  /// 检查是否所有验证码都已填写
  bool get isComplete {
    return emailCode.length == 6 && 
           phoneCode.length == 6 && 
           googleCode.length == 6;
  }

  @override
  String toString() {
    return 'VerifyCodeRequest(sessionId: $sessionId, codes: ${emailCode.length}/6, ${phoneCode.length}/6, ${googleCode.length}/6)';
  }
}
