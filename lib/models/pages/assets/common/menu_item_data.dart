/*
*  菜单项数据模型
*  
*  通用的菜单项数据结构，用于 list 循环生成菜单
*/

import 'package:flutter/material.dart';

/// 菜单项数据模型
class MenuItemData {
  /// 图标名称
  final String iconName;

  /// 标题文本
  final String title;

  /// 点击回调
  final VoidCallback onTap;

  /// 是否启用
  final bool enabled;

  /// 徽章文本（可选）
  final String? badgeText;

  /// 徽章颜色（可选）
  final Color? badgeColor;

  const MenuItemData({
    required this.iconName,
    required this.title,
    required this.onTap,
    this.enabled = true,
    this.badgeText,
    this.badgeColor,
  });

  /// 创建充值菜单项
  static MenuItemData deposit(VoidCallback onTap) {
    return MenuItemData(iconName: 'icon_deposit', title: '充值', onTap: onTap);
  }

  /// 创建提现菜单项
  static MenuItemData withdraw(VoidCallback onTap) {
    return MenuItemData(iconName: 'icon_withdraw', title: '提现', onTap: onTap);
  }

  /// 创建划转菜单项
  static MenuItemData transfer(VoidCallback onTap) {
    return MenuItemData(iconName: 'icon_transfer', title: '划转', onTap: onTap);
  }

  /// 创建盈亏菜单项
  static MenuItemData pnl(VoidCallback onTap) {
    return MenuItemData(iconName: 'icon_pnl', title: '盈亏', onTap: onTap);
  }

  /// 创建去交易菜单项
  static MenuItemData trade(VoidCallback onTap) {
    return MenuItemData(iconName: 'icon_go_trade', title: '去交易', onTap: onTap);
  }

  // 创建去跟单
  static MenuItemData copyTrade(VoidCallback onTap) {
    return MenuItemData(iconName: 'copy_trade', title: '去跟单', onTap: onTap);
  }

  // 创建跟单管理
  static MenuItemData copyManage(VoidCallback onTap) {
    return MenuItemData(iconName: 'copy_manage', title: '跟单管理', onTap: onTap);
  }

  // 创建成为交易专家
  static MenuItemData becomeExpert(VoidCallback onTap) {
    return MenuItemData(
      iconName: 'become_expert',
      title: '成为交易专家',
      onTap: onTap,
    );
  }

  /// 复制并修改属性
  MenuItemData copyWith({
    String? iconName,
    String? title,
    VoidCallback? onTap,
    bool? enabled,
    String? badgeText,
    Color? badgeColor,
  }) {
    return MenuItemData(
      iconName: iconName ?? this.iconName,
      title: title ?? this.title,
      onTap: onTap ?? this.onTap,
      enabled: enabled ?? this.enabled,
      badgeText: badgeText ?? this.badgeText,
      badgeColor: badgeColor ?? this.badgeColor,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MenuItemData &&
        other.iconName == iconName &&
        other.title == title &&
        other.enabled == enabled &&
        other.badgeText == badgeText &&
        other.badgeColor == badgeColor;
  }

  @override
  int get hashCode {
    return Object.hash(iconName, title, enabled, badgeText, badgeColor);
  }

  @override
  String toString() {
    return 'MenuItemData(iconName: $iconName, title: $title, enabled: $enabled, badgeText: $badgeText, badgeColor: $badgeColor)';
  }
}

/// 菜单项构建器
class MenuItemBuilder {
  /// 构建现货页面菜单项
  static List<MenuItemData> buildSpotMenuItems({
    required VoidCallback onDeposit,
    required VoidCallback onWithdraw,
    required VoidCallback onTransfer,
    required VoidCallback onPnL,
  }) {
    return [
      MenuItemData.deposit(onDeposit),
      MenuItemData.withdraw(onWithdraw),
      MenuItemData.transfer(onTransfer),
      MenuItemData.pnl(onPnL),
    ];
  }
}
