/*
 * 社区动态数据模型
 */

class CommunityPostModel {
  final String id;
  final String userId;
  final String userName;
  final String? userAvatar;
  final String publishTime;
  final String content;
  final List<String>? images;
  final int likeCount;
  final int commentCount;
  final int shareCount;
  final bool isLiked;
  final bool isFollowed;
  final List<String>? tags;

  const CommunityPostModel({
    required this.id,
    required this.userId,
    required this.userName,
    this.userAvatar,
    required this.publishTime,
    required this.content,
    this.images,
    this.likeCount = 0,
    this.commentCount = 0,
    this.shareCount = 0,
    this.isLiked = false,
    this.isFollowed = false,
    this.tags,
  });

  /// 从JSON创建CommunityPostModel
  factory CommunityPostModel.fromJson(Map<String, dynamic> json) {
    return CommunityPostModel(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      userAvatar: json['userAvatar'],
      publishTime: json['publishTime'] ?? '',
      content: json['content'] ?? '',
      images: json['images'] != null 
          ? List<String>.from(json['images']) 
          : null,
      likeCount: json['likeCount'] ?? 0,
      commentCount: json['commentCount'] ?? 0,
      shareCount: json['shareCount'] ?? 0,
      isLiked: json['isLiked'] ?? false,
      isFollowed: json['isFollowed'] ?? false,
      tags: json['tags'] != null 
          ? List<String>.from(json['tags']) 
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'userAvatar': userAvatar,
      'publishTime': publishTime,
      'content': content,
      'images': images,
      'likeCount': likeCount,
      'commentCount': commentCount,
      'shareCount': shareCount,
      'isLiked': isLiked,
      'isFollowed': isFollowed,
      'tags': tags,
    };
  }

  /// 复制并修改部分属性
  CommunityPostModel copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userAvatar,
    String? publishTime,
    String? content,
    List<String>? images,
    int? likeCount,
    int? commentCount,
    int? shareCount,
    bool? isLiked,
    bool? isFollowed,
    List<String>? tags,
  }) {
    return CommunityPostModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userAvatar: userAvatar ?? this.userAvatar,
      publishTime: publishTime ?? this.publishTime,
      content: content ?? this.content,
      images: images ?? this.images,
      likeCount: likeCount ?? this.likeCount,
      commentCount: commentCount ?? this.commentCount,
      shareCount: shareCount ?? this.shareCount,
      isLiked: isLiked ?? this.isLiked,
      isFollowed: isFollowed ?? this.isFollowed,
      tags: tags ?? this.tags,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommunityPostModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CommunityPostModel(id: $id, userName: $userName, content: ${content.length > 50 ? content.substring(0, 50) + '...' : content})';
  }
}

/// 社区动态操作类型
enum CommunityPostAction {
  like,
  comment,
  share,
  follow,
  report,
}

/// 社区动态操作结果
class CommunityPostActionResult {
  final bool success;
  final String? message;
  final CommunityPostModel? updatedPost;

  const CommunityPostActionResult({
    required this.success,
    this.message,
    this.updatedPost,
  });
}
