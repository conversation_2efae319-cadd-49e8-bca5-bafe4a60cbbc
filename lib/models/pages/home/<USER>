/*
 * 首页数据模型
 */

import 'token_board_model.dart';

/// 首页数据状态枚举
enum HomeDataStatus { initial, loading, loaded, error, refreshing }

/// 首页数据模型
class HomeDataModel {
  final HomeDataStatus status;
  final TokenBoardDataModel? tokenBoardData;
  final String? errorMessage;
  final DateTime? lastUpdated;

  const HomeDataModel({this.status = HomeDataStatus.initial, this.tokenBoardData, this.errorMessage, this.lastUpdated});

  /// 是否正在加载
  bool get isLoading => status == HomeDataStatus.loading;

  /// 是否正在刷新
  bool get isRefreshing => status == HomeDataStatus.refreshing;

  /// 是否有错误
  bool get hasError => status == HomeDataStatus.error;

  /// 是否已加载
  bool get isLoaded => status == HomeDataStatus.loaded;

  /// 是否有数据
  bool get hasData => tokenBoardData != null && !tokenBoardData!.isEmpty;

  /// 从JSON创建HomeDataModel
  factory HomeDataModel.fromJson(Map<String, dynamic> json) {
    final statusString = json['status'] as String?;
    final status = HomeDataStatus.values.firstWhere((s) => s.name == statusString, orElse: () => HomeDataStatus.initial);

    TokenBoardDataModel? tokenBoardData;
    if (json['tokenBoardData'] != null) {
      tokenBoardData = TokenBoardDataModel.fromJson(json['tokenBoardData']);
    }

    DateTime? lastUpdated;
    if (json['lastUpdated'] != null) {
      lastUpdated = DateTime.parse(json['lastUpdated']);
    }

    return HomeDataModel(status: status, tokenBoardData: tokenBoardData, errorMessage: json['errorMessage'], lastUpdated: lastUpdated);
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'status': status.name,
      'tokenBoardData': tokenBoardData?.toJson(),
      'errorMessage': errorMessage,
      'lastUpdated': lastUpdated?.toIso8601String(),
    };
  }

  /// 复制并修改部分属性
  HomeDataModel copyWith({
    HomeDataStatus? status,
    TokenBoardDataModel? tokenBoardData,
    String? errorMessage,
    DateTime? lastUpdated,
    bool clearError = false,
  }) {
    return HomeDataModel(
      status: status ?? this.status,
      tokenBoardData: tokenBoardData ?? this.tokenBoardData,
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// 创建加载状态
  HomeDataModel toLoading() {
    return copyWith(status: HomeDataStatus.loading, clearError: true);
  }

  /// 创建刷新状态
  HomeDataModel toRefreshing() {
    return copyWith(status: HomeDataStatus.refreshing, clearError: true);
  }

  /// 创建加载成功状态
  HomeDataModel toLoaded(TokenBoardDataModel data) {
    return copyWith(status: HomeDataStatus.loaded, tokenBoardData: data, lastUpdated: DateTime.now(), clearError: true);
  }

  /// 创建错误状态
  HomeDataModel toError(String error) {
    return copyWith(status: HomeDataStatus.error, errorMessage: error);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HomeDataModel &&
        other.status == status &&
        other.tokenBoardData == tokenBoardData &&
        other.errorMessage == errorMessage &&
        other.lastUpdated == lastUpdated;
  }

  @override
  int get hashCode {
    return Object.hash(status, tokenBoardData, errorMessage, lastUpdated);
  }

  @override
  String toString() {
    return 'HomeDataModel(status: $status, hasData: $hasData, error: $errorMessage)';
  }
}

/// 首页配置模型
class HomeConfigModel {
  final bool showTokenBoard;
  final bool showPromotions;
  final bool showCryptoIncentives;
  final List<TokenBoardType> enabledBoardTypes;
  final int refreshIntervalSeconds;

  const HomeConfigModel({
    this.showTokenBoard = true,
    this.showPromotions = true,
    this.showCryptoIncentives = true,
    this.enabledBoardTypes = const [TokenBoardType.favorite, TokenBoardType.hot, TokenBoardType.gainers, TokenBoardType.newCoins],
    this.refreshIntervalSeconds = 30,
  });

  /// 从JSON创建HomeConfigModel
  factory HomeConfigModel.fromJson(Map<String, dynamic> json) {
    final enabledBoardTypesJson = json['enabledBoardTypes'] as List?;
    List<TokenBoardType> enabledBoardTypes = [];

    if (enabledBoardTypesJson != null) {
      enabledBoardTypes =
          enabledBoardTypesJson
              .map(
                (typeString) =>
                    TokenBoardType.values.firstWhere((type) => type.displayName == typeString, orElse: () => TokenBoardType.hot),
              )
              .toList();
    }

    return HomeConfigModel(
      showTokenBoard: json['showTokenBoard'] ?? true,
      showPromotions: json['showPromotions'] ?? true,
      showCryptoIncentives: json['showCryptoIncentives'] ?? true,
      enabledBoardTypes: enabledBoardTypes.isNotEmpty ? enabledBoardTypes : const [TokenBoardType.hot],
      refreshIntervalSeconds: json['refreshIntervalSeconds'] ?? 30,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'showTokenBoard': showTokenBoard,
      'showPromotions': showPromotions,
      'showCryptoIncentives': showCryptoIncentives,
      'enabledBoardTypes': enabledBoardTypes.map((type) => type.displayName).toList(),
      'refreshIntervalSeconds': refreshIntervalSeconds,
    };
  }

  /// 复制并修改部分属性
  HomeConfigModel copyWith({
    bool? showTokenBoard,
    bool? showPromotions,
    bool? showCryptoIncentives,
    List<TokenBoardType>? enabledBoardTypes,
    int? refreshIntervalSeconds,
  }) {
    return HomeConfigModel(
      showTokenBoard: showTokenBoard ?? this.showTokenBoard,
      showPromotions: showPromotions ?? this.showPromotions,
      showCryptoIncentives: showCryptoIncentives ?? this.showCryptoIncentives,
      enabledBoardTypes: enabledBoardTypes ?? this.enabledBoardTypes,
      refreshIntervalSeconds: refreshIntervalSeconds ?? this.refreshIntervalSeconds,
    );
  }

  @override
  String toString() {
    return 'HomeConfigModel(showTokenBoard: $showTokenBoard, enabledBoardTypes: ${enabledBoardTypes.length})';
  }
}
