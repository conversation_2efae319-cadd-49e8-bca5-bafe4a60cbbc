/*
 * 代币数据模型
 */

class TokenModel {
  final String symbol;
  final String name;
  final String price;
  final double changeAmount; // 涨跌额（数值）
  final double changePercentage; // 涨跌幅百分比（数值）
  final String volume;
  final String quoteCurrency;
  final bool isUp;
  final String? onlineTime; // 上线时间（新币榜用）
  final String? openingGain; // 开盘涨幅（新币榜用）
  final String? marketCap; // 总市值（成交额榜用）
  final String? onChainVolume; // 链上成交额（成交额榜用）

  const TokenModel({
    required this.symbol,
    required this.name,
    required this.price,
    required this.changeAmount,
    required this.changePercentage,
    required this.volume,
    this.quoteCurrency = 'USDT',
    required this.isUp,
    this.onlineTime,
    this.openingGain,
    this.marketCap,
    this.onChainVolume,
  });

  /// 获取格式化的涨跌额字符串
  String get formattedChangeAmount {
    final sign = changeAmount >= 0 ? '+' : '';
    return '$sign${changeAmount.toStringAsFixed(changeAmount.abs() >= 1 ? 2 : 6)}';
  }

  /// 获取格式化的涨跌幅字符串
  String get formattedChangePercentage {
    final sign = changePercentage >= 0 ? '+' : '';
    return '$sign${changePercentage.toStringAsFixed(2)}%';
  }

  /// 从JSON创建TokenModel
  factory TokenModel.fromJson(Map<String, dynamic> json) {
    return TokenModel(
      symbol: json['symbol'] ?? '',
      name: json['name'] ?? '',
      price: json['price'] ?? '0.00',
      changeAmount: (json['changeAmount'] ?? 0.0).toDouble(),
      changePercentage: (json['changePercentage'] ?? 0.0).toDouble(),
      volume: json['volume'] ?? '0',
      quoteCurrency: json['quoteCurrency'] ?? 'USDT',
      isUp: json['isUp'] ?? false,
      onlineTime: json['onlineTime'],
      openingGain: json['openingGain'],
      marketCap: json['marketCap'],
      onChainVolume: json['onChainVolume'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'symbol': symbol,
      'name': name,
      'price': price,
      'changeAmount': changeAmount,
      'changePercentage': changePercentage,
      'volume': volume,
      'quoteCurrency': quoteCurrency,
      'isUp': isUp,
    };

    if (onlineTime != null) data['onlineTime'] = onlineTime;
    if (openingGain != null) data['openingGain'] = openingGain;
    if (marketCap != null) data['marketCap'] = marketCap;
    if (onChainVolume != null) data['onChainVolume'] = onChainVolume;

    return data;
  }

  /// 复制并修改部分属性
  TokenModel copyWith({
    String? symbol,
    String? name,
    String? price,
    double? changeAmount,
    double? changePercentage,
    String? volume,
    String? quoteCurrency,
    bool? isUp,
    String? onlineTime,
    String? openingGain,
    String? marketCap,
    String? onChainVolume,
  }) {
    return TokenModel(
      symbol: symbol ?? this.symbol,
      name: name ?? this.name,
      price: price ?? this.price,
      changeAmount: changeAmount ?? this.changeAmount,
      changePercentage: changePercentage ?? this.changePercentage,
      volume: volume ?? this.volume,
      quoteCurrency: quoteCurrency ?? this.quoteCurrency,
      isUp: isUp ?? this.isUp,
      onlineTime: onlineTime ?? this.onlineTime,
      openingGain: openingGain ?? this.openingGain,
      marketCap: marketCap ?? this.marketCap,
      onChainVolume: onChainVolume ?? this.onChainVolume,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TokenModel &&
        other.symbol == symbol &&
        other.name == name &&
        other.price == price &&
        other.changeAmount == changeAmount &&
        other.changePercentage == changePercentage &&
        other.volume == volume &&
        other.quoteCurrency == quoteCurrency &&
        other.isUp == isUp &&
        other.onlineTime == onlineTime &&
        other.openingGain == openingGain &&
        other.marketCap == marketCap &&
        other.onChainVolume == onChainVolume;
  }

  @override
  int get hashCode {
    return Object.hash(
      symbol,
      name,
      price,
      changeAmount,
      changePercentage,
      volume,
      quoteCurrency,
      isUp,
      onlineTime,
      openingGain,
      marketCap,
      onChainVolume,
    );
  }

  @override
  String toString() {
    return 'TokenModel(symbol: $symbol, name: $name, price: $price, changeAmount: $changeAmount, changePercentage: $changePercentage, isUp: $isUp)';
  }
}
