/*
 * 代币看板数据模型
 */

import 'token_model.dart';

/// 代币看板类型枚举
enum TokenBoardType {
  favorite('自选'),
  hot('热门'),
  gainers('涨幅榜'),
  newCoins('新币榜'),
  stocks('股票'),
  forex('外汇'),
  option('期货');

  const TokenBoardType(this.displayName);
  final String displayName;
}

/// 代币交易类型枚举
enum TokenTradeType {
  spot('现货'),
  contract('合约');
  // onChain('链上交易');

  const TokenTradeType(this.displayName);
  final String displayName;
}

/// 代币看板数据模型
class TokenBoardModel {
  final TokenBoardType boardType;
  final Map<TokenTradeType, List<TokenModel>> tokensByType;

  const TokenBoardModel({required this.boardType, required this.tokensByType});

  /// 获取指定交易类型的代币列表
  List<TokenModel> getTokensByTradeType(TokenTradeType tradeType) {
    return tokensByType[tradeType] ?? [];
  }

  /// 获取所有可用的交易类型
  List<TokenTradeType> get availableTradeTypes {
    return tokensByType.keys.toList();
  }

  /// 获取代币总数
  int get totalTokenCount {
    return tokensByType.values.fold(0, (sum, tokens) => sum + tokens.length);
  }

  /// 是否为空
  bool get isEmpty {
    return tokensByType.isEmpty || tokensByType.values.every((tokens) => tokens.isEmpty);
  }

  /// 从JSON创建TokenBoardModel
  factory TokenBoardModel.fromJson(Map<String, dynamic> json) {
    final boardTypeString = json['boardType'] as String;
    final boardType = TokenBoardType.values.firstWhere((type) => type.displayName == boardTypeString, orElse: () => TokenBoardType.hot);

    final Map<TokenTradeType, List<TokenModel>> tokensByType = {};
    final tokensData = json['tokensByType'] as Map<String, dynamic>;

    for (final entry in tokensData.entries) {
      final tradeType = TokenTradeType.values.firstWhere((type) => type.displayName == entry.key, orElse: () => TokenTradeType.spot);

      final tokensList = (entry.value as List).map((tokenJson) => TokenModel.fromJson(tokenJson)).toList();

      tokensByType[tradeType] = tokensList;
    }

    return TokenBoardModel(boardType: boardType, tokensByType: tokensByType);
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> tokensData = {};

    for (final entry in tokensByType.entries) {
      tokensData[entry.key.displayName] = entry.value.map((token) => token.toJson()).toList();
    }

    return {'boardType': boardType.displayName, 'tokensByType': tokensData};
  }

  /// 复制并修改部分属性
  TokenBoardModel copyWith({TokenBoardType? boardType, Map<TokenTradeType, List<TokenModel>>? tokensByType}) {
    return TokenBoardModel(boardType: boardType ?? this.boardType, tokensByType: tokensByType ?? this.tokensByType);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TokenBoardModel && other.boardType == boardType && _mapEquals(other.tokensByType, tokensByType);
  }

  @override
  int get hashCode {
    return Object.hash(boardType, tokensByType);
  }

  @override
  String toString() {
    return 'TokenBoardModel(boardType: ${boardType.displayName}, tokenCount: $totalTokenCount)';
  }
}

/// 辅助函数：比较两个Map是否相等
bool _mapEquals<K, V>(Map<K, V>? a, Map<K, V>? b) {
  if (a == null) return b == null;
  if (b == null || a.length != b.length) return false;
  if (identical(a, b)) return true;
  for (final K key in a.keys) {
    if (!b.containsKey(key) || b[key] != a[key]) return false;
  }
  return true;
}

/// 代币看板数据集合模型
class TokenBoardDataModel {
  final Map<TokenBoardType, TokenBoardModel> boards;

  const TokenBoardDataModel({required this.boards});

  /// 获取指定类型的看板数据
  TokenBoardModel? getBoardByType(TokenBoardType type) {
    return boards[type];
  }

  /// 获取所有可用的看板类型
  List<TokenBoardType> get availableBoardTypes {
    return boards.keys.toList();
  }

  /// 是否为空
  bool get isEmpty {
    return boards.isEmpty || boards.values.every((board) => board.isEmpty);
  }

  /// 从JSON创建TokenBoardDataModel
  factory TokenBoardDataModel.fromJson(Map<String, dynamic> json) {
    final Map<TokenBoardType, TokenBoardModel> boards = {};

    for (final entry in json.entries) {
      final boardType = TokenBoardType.values.firstWhere((type) => type.displayName == entry.key, orElse: () => TokenBoardType.hot);

      boards[boardType] = TokenBoardModel.fromJson({'boardType': entry.key, 'tokensByType': entry.value});
    }

    return TokenBoardDataModel(boards: boards);
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    for (final entry in boards.entries) {
      data[entry.key.displayName] = entry.value.tokensByType.map(
        (tradeType, tokens) => MapEntry(tradeType.displayName, tokens.map((token) => token.toJson()).toList()),
      );
    }

    return data;
  }

  @override
  String toString() {
    return 'TokenBoardDataModel(boardCount: ${boards.length})';
  }
}
