/*
 * 动态数据模型
 */

/// 动态列表响应模型
class DynamicsListResponse {
  final List<DynamicsItem> list;
  final int total;
  final int page;
  final int pageSize;
  final int totalPage;

  DynamicsListResponse({required this.list, required this.total, required this.page, required this.pageSize, required this.totalPage});

  factory DynamicsListResponse.fromJson(Map<String, dynamic> json) {
    return DynamicsListResponse(
      list: (json['list'] as List<dynamic>?)?.map((item) => DynamicsItem.fromJson(item as Map<String, dynamic>)).toList() ?? [],
      total: json['total'] as int? ?? 0,
      page: json['page'] as int? ?? 1,
      pageSize: json['page_size'] as int? ?? 20,
      totalPage: json['total_page'] as int? ?? 0,
    );
  }
}

/// 动态项模型
class DynamicsItem {
  final int id;
  final int pidId;
  final int userId;
  final int liked;
  final List<int>? likedUids;
  final String title;
  final String content;
  final List<String>? images;
  final List<int>? forwardUids;
  final List<int>? collectUids;
  final int lookAuth;
  final int lookNum;
  final List<DynamicsCurrency>? dynamicsCurrency;
  final int? hotTopicId;
  final int type;
  final int createdBy;
  final int updatedBy;
  final String createdAt;
  final String updatedAt;
  final DynamicsUser? user;
  final HotTopic? hottopic;

  DynamicsItem({
    required this.id,
    required this.pidId,
    required this.userId,
    required this.liked,
    this.likedUids,
    required this.title,
    required this.content,
    this.images,
    this.forwardUids,
    this.collectUids,
    required this.lookAuth,
    required this.lookNum,
    this.dynamicsCurrency,
    this.hotTopicId,
    required this.type,
    required this.createdBy,
    required this.updatedBy,
    required this.createdAt,
    required this.updatedAt,
    this.user,
    this.hottopic,
  });

  factory DynamicsItem.fromJson(Map<String, dynamic> json) {
    return DynamicsItem(
      id: json['id'] as int? ?? 0,
      pidId: json['pid_id'] as int? ?? 0,
      userId: json['user_id'] as int? ?? 0,
      liked: json['liked'] as int? ?? 0,
      likedUids: (json['liked_uids'] as List<dynamic>?)?.cast<int>(),
      title: json['title'] as String? ?? '',
      content: json['content'] as String? ?? '',
      images: (json['images'] as List<dynamic>?)?.cast<String>(),
      forwardUids: (json['forward_uids'] as List<dynamic>?)?.cast<int>(),
      collectUids: (json['collect_uids'] as List<dynamic>?)?.cast<int>(),
      lookAuth: json['look_auth'] as int? ?? 1,
      lookNum: json['look_num'] as int? ?? 0,
      dynamicsCurrency:
          (json['dynamics_currency'] as List<dynamic>?)?.map((item) => DynamicsCurrency.fromJson(item as Map<String, dynamic>)).toList(),
      hotTopicId: json['hot_topic_id'] as int?,
      type: json['type'] as int? ?? 1,
      createdBy: json['created_by'] as int? ?? 0,
      updatedBy: json['updated_by'] as int? ?? 0,
      createdAt: json['created_at'] as String? ?? '',
      updatedAt: json['updated_at'] as String? ?? '',
      user: json['user'] != null ? DynamicsUser.fromJson(json['user'] as Map<String, dynamic>) : null,
      hottopic: json['hottopic'] != null ? HotTopic.fromJson(json['hottopic'] as Map<String, dynamic>) : null,
    );
  }

  /// 是否已点赞
  bool get isLiked => liked > 0;

  /// 获取格式化的时间
  String get formattedTime {
    try {
      final dateTime = DateTime.parse(createdAt);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inMinutes < 1) {
        return '刚刚';
      } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes}分钟前';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}小时前';
      } else if (difference.inDays < 30) {
        return '${difference.inDays}天前';
      } else {
        return createdAt.substring(0, 10);
      }
    } catch (e) {
      return createdAt;
    }
  }

  // 兼容旧代码的getter
  String get userName => user?.displayName ?? '';
  String get publishTime => createdAt;
  int get likeCount => liked;
  String? get userAvatar => user?.avatar;
  String get timeAgo => formattedTime;
  int get commentCount => lookNum;
  String? get userIdString => user?.id.toString();

  /// 获取格式化后的内容（包含热门话题标签）
  String get formattedContent {
    String result = content;
    if (hottopic?.title != null && hottopic!.title.isNotEmpty) {
      result += ' #${hottopic!.title}#';
    }
    return result;
  }
}

/// 动态货币模型
class DynamicsCurrency {
  final int id;
  final int symbolId;
  final String? symbol;
  final String? url;
  final int createdBy;
  final int updatedBy;
  final String createdAt;
  final String updatedAt;

  DynamicsCurrency({
    required this.id,
    required this.symbolId,
    this.symbol,
    this.url,
    required this.createdBy,
    required this.updatedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DynamicsCurrency.fromJson(Map<String, dynamic> json) {
    return DynamicsCurrency(
      id: json['id'] as int? ?? 0,
      symbolId: json['symbol_id'] as int? ?? 0,
      symbol: json['symbol'] as String?,
      url: json['url'] as String?,
      createdBy: json['created_by'] as int? ?? 0,
      updatedBy: json['updated_by'] as int? ?? 0,
      createdAt: json['created_at'] as String? ?? '',
      updatedAt: json['updated_at'] as String? ?? '',
    );
  }
}

/// 动态用户模型
class DynamicsUser {
  final int id;
  final String displayName;
  final String? avatar;
  final String avatarUrl;

  DynamicsUser({required this.id, required this.displayName, this.avatar, required this.avatarUrl});

  factory DynamicsUser.fromJson(Map<String, dynamic> json) {
    return DynamicsUser(
      id: json['id'] as int? ?? 0,
      displayName: json['display_name'] as String? ?? '',
      avatar: json['avatar'] as String?,
      avatarUrl: json['avatar_url'] as String? ?? '',
    );
  }
}

/// 热门话题模型
class HotTopic {
  final int id;
  final List<int> currency;
  final String title;
  final String content;
  final int lookNum;
  final int createdBy;
  final int updatedBy;
  final String createdAt;
  final String updatedAt;

  HotTopic({
    required this.id,
    required this.currency,
    required this.title,
    required this.content,
    required this.lookNum,
    required this.createdBy,
    required this.updatedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory HotTopic.fromJson(Map<String, dynamic> json) {
    return HotTopic(
      id: json['id'] as int? ?? 0,
      currency: (json['currency'] as List<dynamic>?)?.cast<int>() ?? [],
      title: json['title'] as String? ?? '',
      content: json['content'] as String? ?? '',
      lookNum: json['look_num'] as int? ?? 0,
      createdBy: json['created_by'] as int? ?? 0,
      updatedBy: json['updated_by'] as int? ?? 0,
      createdAt: json['created_at'] as String? ?? '',
      updatedAt: json['updated_at'] as String? ?? '',
    );
  }
}

// 兼容旧代码的别名
typedef DynamicsModel = DynamicsItem;
