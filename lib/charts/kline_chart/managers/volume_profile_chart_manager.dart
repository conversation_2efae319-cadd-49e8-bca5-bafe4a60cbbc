import 'package:flutter/foundation.dart';
import 'dart:async';
import '../../kline/flexi_kline.dart';
import '../config/indicator_config/volume_profile_chart_config.dart';

/// Volume Profile Chart 管理器
/// 负责处理横向成交量分布图的数据获取和绘制逻辑
class VolumeProfileChartManager {
  final FlexiKlineController _controller;
  VolumeProfileChartConfig? _config;
  bool _isEnabled = false;
  bool _isInitialized = false;
  
  // 当前数据缓存
  Map<String, dynamic>? _currentData;
  Range? _lastRequestedRange;
  
  // 防抖计时器
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(seconds: 1);
  bool _isPendingExecution = false;
  Range? _pendingRange; // 防抖期间暂存的时间范围
  
  // 实时数据定时绘制 - 独立机制，不影响原有功能
  Timer? _realtimeRenderTimer;
  static const Duration _realtimeRenderInterval = Duration(seconds: 5); // 恢复到5秒，避免性能问题
  bool _hasRealtimeDataUpdate = false; // 标记是否有实时数据更新
  
  // 回调函数
  Function(String instrumentId, int startTime, int endTime)? onDataRequested;
  Function(Map<String, dynamic> data, VolumeProfileChartConfig config)? onRenderRequested;

  VolumeProfileChartManager({
    required FlexiKlineController controller,
    this.onDataRequested,
    this.onRenderRequested,
  }) : _controller = controller;

  /// 初始化管理器
  void initialize() {
    if (_isInitialized) return;
    
    _isInitialized = true;
    
    // 启动实时数据定时绘制器
    _startRealtimeRenderTimer();
    
    if (kDebugMode) {
      print('[VolumeProfileChartManager] 管理器已初始化');
    }
  }

  /// 启动实时数据定时绘制器（独立于原有功能）
  void _startRealtimeRenderTimer() {
    _realtimeRenderTimer?.cancel();
    _realtimeRenderTimer = Timer.periodic(_realtimeRenderInterval, (timer) {
      // 检查管理器是否已初始化且未被dispose
      if (!_isInitialized) {
        if (kDebugMode) {
          print('[VolumeProfileChartManager] 管理器未初始化，停止实时定时器');
        }
        timer.cancel();
        return;
      }
      
      // 检查是否有待处理的条件 - 只在有实时数据更新时才处理
      if (_hasRealtimeDataUpdate && _isEnabled && _config != null && _currentData != null) {
        try {
          // 在执行前再次检查controller状态
          final klineData = _controller.curKlineData;
          if (klineData.isEmpty) {
            if (kDebugMode) {
              print('[VolumeProfileChartManager] KlineData为空，跳过定时绘制');
            }
            return;
          }

          if (kDebugMode) {
            print('[VolumeProfileChartManager] 执行定时数据更新（5秒间隔）');
          }

          // 使用现有的数据进行重绘，触发从缓存中重新获取最新的聚合数据
          _requestRealtimeDataUpdate();

          // 重置标记，避免无意义的重复计算
          _hasRealtimeDataUpdate = false;
        } catch (e) {
          // Controller已被dispose或其他错误，停止定时器
          if (kDebugMode) {
            print('[VolumeProfileChartManager] Controller状态异常，停止定时器: $e');
          }
          timer.cancel();
          _realtimeRenderTimer = null;
        }
      }
    });
  }

  /// 停止实时数据定时绘制器
  void _stopRealtimeRenderTimer() {
    _realtimeRenderTimer?.cancel();
    _realtimeRenderTimer = null;
  }

  /// 请求实时数据更新（从缓存获取最新聚合数据）
  void _requestRealtimeDataUpdate() {
    if (!_isEnabled || !_isInitialized || _lastRequestedRange == null) return;
    
    // 获取当前交易对信息
    final klineData = _controller.curKlineData;
    final instrumentId = klineData.req.instId;
    
    // 使用上次请求的时间范围，从缓存中获取最新的聚合数据
    final startTime = _lastRequestedRange!.start < _lastRequestedRange!.end ? _lastRequestedRange!.start : _lastRequestedRange!.end;
    final endTime = _lastRequestedRange!.start < _lastRequestedRange!.end ? _lastRequestedRange!.end : _lastRequestedRange!.start;
    
    // 通过回调请求数据（这会触发缓存数据的获取）
    onDataRequested?.call(instrumentId, startTime, endTime);
  }

  /// 更新配置
  void updateConfig(VolumeProfileChartConfig? config) {
    final oldEnabled = _isEnabled;
    _config = config;
    _isEnabled = config?.enabled ?? false;
    
    // 如果从禁用变为启用，且已初始化，则立即处理
    if (!oldEnabled && _isEnabled && _isInitialized) {
      // 强制重新获取数据，而不是依赖缓存
      _handleVisibleRangeChanged();
    }
    // 如果配置发生变化且当前启用状态
    else if (_isEnabled && _config != null) {
      if (_currentData != null) {
        // 使用保存的数据重新渲染
        onRenderRequested?.call(_currentData!, _config!);
      } else {
        // 没有缓存数据，重新获取
        _handleVisibleRangeChanged();
      }
    }
    
    // 如果从启用变为禁用，清除当前绘制
    if (oldEnabled && !_isEnabled) {
      _clearCurrentRender();
    }
  }

  /// 处理可见范围变化（带防抖逻辑）
  void handleVisibleRangeChanged({bool isInitialLoad = false}) {
    if (!_isEnabled || !_isInitialized) return;
    
    // 如果是初始化加载，立即执行，跳过防抖
    if (isInitialLoad) {
      _handleVisibleRangeChanged();
      return;
    }
    
    // 获取当前的时间范围
    final currentRange = _getVisibleTimeRange();
    if (currentRange == null) return;
    
    // 检查时间窗口是否真的发生了变化
    // 首先检查是否与上次请求的范围相同
    if (_lastRequestedRange != null && 
        _lastRequestedRange!.start == currentRange.start && 
        _lastRequestedRange!.end == currentRange.end) {
      // 时间窗口没有变化，不需要防抖
      return;
    }
    
    // 再检查是否与当前待处理的范围相同（防抖期间的重复调用）
    if (_pendingRange != null && 
        _pendingRange!.start == currentRange.start && 
        _pendingRange!.end == currentRange.end) {
      // 防抖期间相同范围的重复调用，跳过
      return;
    }
    
    // 取消之前的防抖计时器
    _debounceTimer?.cancel();
    _isPendingExecution = false;
    
    // 暂存当前的时间范围
    _pendingRange = currentRange;
    
    // 注意：不在这里更新_lastRequestedRange，等真正执行数据请求时再更新
    
    // 设置新的防抖计时器
    _isPendingExecution = true;
    _debounceTimer = Timer(_debounceDuration, () {
      if (!_isPendingExecution || _pendingRange == null) return; // 已被取消
      
      // 再次检查时间窗口是否还是和防抖开始时一样
      final finalRange = _getVisibleTimeRange();
      if (finalRange == null) {
        _isPendingExecution = false;
        _pendingRange = null;
        return;
      }
      
      // 比较防抖开始时的范围和现在的范围
      if (_pendingRange!.start == finalRange.start && _pendingRange!.end == finalRange.end) {
        _isPendingExecution = false;
        _pendingRange = null;
        // 防抖结束后强制执行数据请求，绕过重复检查
        _handleVisibleRangeChanged(forceRequest: true);
      } else {
        // 时间窗口在防抖期间又发生了变化，重新开始防抖
        _isPendingExecution = false;
        _pendingRange = null;
        // 重置_lastRequestedRange为当前最新的范围
        _lastRequestedRange = finalRange;
        handleVisibleRangeChanged(); // 递归调用重新开始防抖
      }
    });
    
    if (kDebugMode) {
      print('[VolumeProfile] 时间窗口: ${DateTime.fromMillisecondsSinceEpoch(currentRange.start).toString().substring(0, 19)} - ${DateTime.fromMillisecondsSinceEpoch(currentRange.end).toString().substring(0, 19)}');
    }
  }

  /// 立即处理可见范围变化（跳过防抖，用于紧急情况）
  void handleVisibleRangeChangedImmediate() {
    if (!_isEnabled || !_isInitialized) return;
    
    // 取消防抖计时器，立即执行
    _debounceTimer?.cancel();
    _isPendingExecution = false;
    _pendingRange = null;
    
    if (kDebugMode) {
      print('[VolumeProfileChartManager] 立即执行范围变化处理（跳过防抖）');
    }
    
    _handleVisibleRangeChanged();
  }

  /// 内部处理可见范围变化的逻辑
  void _handleVisibleRangeChanged({bool forceRequest = false}) {
    final visibleRange = _getVisibleTimeRange();
    if (visibleRange == null) return;
    
    // 检查时间范围是否变化，或者强制请求
    if (!forceRequest && _lastRequestedRange != null && 
        _lastRequestedRange!.start == visibleRange.start && 
        _lastRequestedRange!.end == visibleRange.end) {
      return; // 时间范围没有变化且非强制请求
    }
    
    // 执行数据请求时更新_lastRequestedRange
    _lastRequestedRange = visibleRange;
    
    _requestData(visibleRange);
  }

  /// 获取当前可见的时间范围
  Range? _getVisibleTimeRange() {
    try {
      final klineData = _controller.curKlineData;
      if (klineData.isEmpty) return null;
      
      final start = klineData.start;
      final end = klineData.end;
      
      // 简单的边界检查，防止数组越界
      if (start >= klineData.length || end <= 0 || end > klineData.length) return null;
      
      final startTs = klineData.list[start].ts;
      final endTs = klineData.list[end - 1].ts;
      
      final currentRange = Range(startTs, endTs);
      
      return currentRange;
    } catch (e) {
      return null;
    }
  }

  /// 请求数据
  void _requestData(Range timeRange) {
    // 修正时间范围：确保start是较早时间，end是较晚时间
    final startTime = timeRange.start < timeRange.end ? timeRange.start : timeRange.end;
    final endTime = timeRange.start < timeRange.end ? timeRange.end : timeRange.start;
    
    // 获取当前交易对信息
    final klineData = _controller.curKlineData;
    final instrumentId = klineData.req.instId;
    
    // 通过回调请求数据
    onDataRequested?.call(instrumentId, startTime, endTime);
  }

  /// 处理接收到的数据
  void handleDataReceived(Map<String, dynamic> data) {
    if (!_isEnabled) return;
    
    _currentData = data;
    
    // 通过回调请求渲染，传递当前配置（如果为null则由渲染回调处理）
    onRenderRequested?.call(data, _config ?? const VolumeProfileChartConfig());
  }

  /// 设置当前数据（用于保存渲染参数）
  void setCurrentData(Map<String, dynamic> data) {
    _currentData = data;
    
    if (kDebugMode) {
      print('[VolumeProfileChartManager] 当前数据已更新');
    }
  }

  /// 标记有实时数据更新（由实时数据聚合触发）
  void markRealtimeDataUpdate() {
    _hasRealtimeDataUpdate = true;
    // 只标记状态，不立即触发更新，避免频繁计算造成卡顿
    // 由定时器统一处理更新，保证性能
  }

  /// 清除当前绘制
  void _clearCurrentRender() {
    _currentData = null;
    
    if (kDebugMode) {
      print('[VolumeProfileChartManager] 清除当前绘制');
    }
    
    // TODO: 这里应该清除画布上的Volume Profile Chart绘制
    // 用户稍后会提供具体的清除实现
  }

  /// 清理所有数据并重置状态（币种切换时使用）
  void clearAllDataAndReset() {
    _currentData = null;
    _lastRequestedRange = null;
    _pendingRange = null;
    _hasRealtimeDataUpdate = false;
    
    // 取消当前的防抖定时器
    _debounceTimer?.cancel();
    _debounceTimer = null;
    _isPendingExecution = false;
    
    if (kDebugMode) {
      print('[VolumeProfileChartManager] 已清理所有数据并重置状态');
    }
  }

  /// 强制刷新
  void forceRefresh() {
    if (!_isEnabled || !_isInitialized) return;
    
    if (kDebugMode) {
      print('[VolumeProfileChartManager] 强制刷新被调用');
    }
    
    _handleVisibleRangeChanged();
  }

  /// 获取当前配置
  VolumeProfileChartConfig? get config => _config;

  /// 是否启用
  bool get isEnabled => _isEnabled;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 是否有实时数据更新
  bool get hasRealtimeDataUpdate => _hasRealtimeDataUpdate;

  /// 清理资源
  void dispose() {
    _debounceTimer?.cancel();
    _stopRealtimeRenderTimer();
    _isInitialized = false;  // 标记已dispose，确保定时器能检测到状态变化
    _currentData = null;
    _lastRequestedRange = null;
    _pendingRange = null;
    _isPendingExecution = false;
    _hasRealtimeDataUpdate = false;
    
    if (kDebugMode) {
      print('[VolumeProfileChartManager] 资源已清理');
    }
  }
} 