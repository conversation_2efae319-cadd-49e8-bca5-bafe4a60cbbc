import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';

/// 成交量分布数据结构
class VolumeDistributionData {
  final double price;
  final double volume;

  VolumeDistributionData({required this.price, required this.volume});

  factory VolumeDistributionData.fromJson(Map<String, dynamic> json) {
    return VolumeDistributionData(
      price: double.parse(json['price'].toString()),
      volume: double.parse(json['volume'].toString()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'price': price,
      'volume': volume,
    };
  }
}

/// 合并后的成交量分布数据
class MergedVolumeProfileData {
  final List<VolumeDistributionData> buyPriceDistribution;
  final List<VolumeDistributionData> sellPriceDistribution;
  final Map<String, Map<double, double>> buyVolumeByTime;
  final Map<String, Map<double, double>> sellVolumeByTime;
  final int? startTime;
  final int? endTime;
  final String? symbol;

  // 缓存计算结果以提高性能
  Map<double, double>? _cachedTotalVolumeByPrice;
  double? _cachedPOC;
  Map<String, double>? _cachedValueArea;
  double? _lastValueAreaPercentage;

  MergedVolumeProfileData({
    required this.buyPriceDistribution,
    required this.sellPriceDistribution,
    required this.buyVolumeByTime,
    required this.sellVolumeByTime,
    this.startTime,
    this.endTime,
    this.symbol,
  });

  /// 获取买入成交量按价格分布的Map，带缓存优化
  Map<double, double> getBuyVolumeByPrice() {
    final Map<double, double> result = {};
    for (final item in buyPriceDistribution) {
      result[item.price] = item.volume;
    }
    return result;
  }

  /// 获取卖出成交量按价格分布的Map
  Map<double, double> getSellVolumeByPrice() {
    final Map<double, double> result = {};
    for (final item in sellPriceDistribution) {
      result[item.price] = item.volume;
    }
    return result;
  }

  /// 获取总成交量按价格分布的Map，带缓存优化
  Map<double, double> getTotalVolumeByPrice() {
    if (_cachedTotalVolumeByPrice != null) {
      return _cachedTotalVolumeByPrice!;
    }

    final Map<double, double> result = {};
    
    // 先添加买入成交量
    for (final item in buyPriceDistribution) {
      result[item.price] = (result[item.price] ?? 0.0) + item.volume;
    }
    
    // 再添加卖出成交量
    for (final item in sellPriceDistribution) {
      result[item.price] = (result[item.price] ?? 0.0) + item.volume;
    }
    
    _cachedTotalVolumeByPrice = result;
    return result;
  }

  /// 优化的POC计算（Point of Control）- 成交量最大的价格，带缓存
  double calculatePOC() {
    if (_cachedPOC != null) {
      return _cachedPOC!;
    }

    final totalVolume = getTotalVolumeByPrice();
    if (totalVolume.isEmpty) {
      _cachedPOC = 0.0;
      return 0.0;
    }
    
    double maxVolume = 0.0;
    double pocPrice = 0.0;
    
    // 遍历找到最大成交量的价格
    totalVolume.forEach((price, volume) {
      if (volume > maxVolume) {
        maxVolume = volume;
        pocPrice = price;
      }
    });
    
    _cachedPOC = pocPrice;
    return pocPrice;
  }

  /// 计算VWAP（Volume Weighted Average Price）
  double calculateVWAP() {
    final totalVolume = getTotalVolumeByPrice();
    if (totalVolume.isEmpty) return 0.0;
    
    double totalValue = 0.0;
    double totalVol = 0.0;
    
    totalVolume.forEach((price, volume) {
      totalValue += price * volume;
      totalVol += volume;
    });
    
    return totalVol > 0 ? totalValue / totalVol : 0.0;
  }

  /// 优化的Value Area计算 - 使用标准Volume Profile算法
  /// 返回 {'VAL': value, 'VAH': value, 'poc_volume': volume, 'value_area_volume': volume}
  Map<String, double> calculateValueArea({double percentage = 0.7}) {
    // 检查缓存
    if (_cachedValueArea != null && _lastValueAreaPercentage == percentage) {
      return _cachedValueArea!;
    }

    final totalVolume = getTotalVolumeByPrice();
    if (totalVolume.isEmpty) {
      final emptyResult = {'VAL': 0.0, 'VAH': 0.0, 'poc_volume': 0.0, 'value_area_volume': 0.0};
      _cachedValueArea = emptyResult;
      _lastValueAreaPercentage = percentage;
      return emptyResult;
    }
    
    // 验证输入参数
    if (percentage <= 0 || percentage > 1.0) {
      percentage = 0.7; // 默认70%
    }
    
    // 按价格排序并预处理数据
    final sortedEntries = totalVolume.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));
    
    if (sortedEntries.length == 1) {
      // 只有一个价格点的特殊情况
      final singlePrice = sortedEntries.first.key;
      final result = {
        'VAH': singlePrice, 
        'VAL': singlePrice, 
        'poc_volume': sortedEntries.first.value,
        'value_area_volume': sortedEntries.first.value
      };
      _cachedValueArea = result;
      _lastValueAreaPercentage = percentage;
      return result;
    }
    
    final totalVol = sortedEntries.fold(0.0, (sum, entry) => sum + entry.value);
    final targetVolume = totalVol * percentage;
    
    // 获取POC价格和索引
    final poc = calculatePOC();
    
    // 使用更精确的POC查找算法
    int pocIndex = _findPOCIndex(sortedEntries, poc);
    
    if (pocIndex == -1) {
      // 容错：找到成交量最大的索引
      pocIndex = _findMaxVolumeIndex(sortedEntries);
    }
    
    // 标准Volume Profile算法：从POC向两边扩展
    final valueAreaResult = _expandValueArea(sortedEntries, pocIndex, targetVolume, totalVol);
    
    final result = {
      'VAL': valueAreaResult['VAL']!,
      'VAH': valueAreaResult['VAH']!,
      'poc_volume': sortedEntries[pocIndex].value,
      'value_area_volume': valueAreaResult['accumulated_volume']!,
    };
    
    _cachedValueArea = result;
    _lastValueAreaPercentage = percentage;
    return result;
  }

  /// 精确查找POC索引
  int _findPOCIndex(List<MapEntry<double, double>> sortedEntries, double pocPrice) {
    const double tolerance = 1e-10; // 浮点数容差
    int bestIndex = -1;
    double minDiff = double.infinity;
    
    for (int i = 0; i < sortedEntries.length; i++) {
      final diff = (sortedEntries[i].key - pocPrice).abs();
      if (diff < tolerance) {
        return i; // 精确匹配
      }
      if (diff < minDiff) {
        minDiff = diff;
        bestIndex = i;
      }
    }
    
    return bestIndex;
  }

  /// 查找成交量最大的索引（容错方法）
  int _findMaxVolumeIndex(List<MapEntry<double, double>> sortedEntries) {
    if (sortedEntries.isEmpty) return -1;
    
    int maxIndex = 0;
    double maxVolume = sortedEntries[0].value;
    
    for (int i = 1; i < sortedEntries.length; i++) {
      if (sortedEntries[i].value > maxVolume) {
        maxVolume = sortedEntries[i].value;
        maxIndex = i;
      }
    }
    
    return maxIndex;
  }

  /// 标准Volume Profile Value Area扩展算法
  Map<String, double> _expandValueArea(
    List<MapEntry<double, double>> sortedEntries, 
    int pocIndex, 
    double targetVolume,
    double totalVolume
  ) {
    double accumulatedVolume = sortedEntries[pocIndex].value;
    int lowIndex = pocIndex;
    int highIndex = pocIndex;
    
    // 验证参数
    if (pocIndex < 0 || pocIndex >= sortedEntries.length) {
      return {
        'VAL': sortedEntries.first.key,
        'VAH': sortedEntries.last.key,
        'accumulated_volume': totalVolume,
      };
    }
    
    int iterations = 0;
    const maxIterations = 1000; // 防止无限循环
    
    // 标准Volume Profile算法：从POC开始，每次向成交量更大的一边扩展一个价格级别
    while (accumulatedVolume < targetVolume && (lowIndex > 0 || highIndex < sortedEntries.length - 1) && iterations < maxIterations) {
      iterations++;
      
      double leftVolume = 0;
      double rightVolume = 0;
      
      // 检查可扩展的方向和对应的成交量
      bool canExpandLeft = lowIndex > 0;
      bool canExpandRight = highIndex < sortedEntries.length - 1;
      
      if (canExpandLeft) {
        leftVolume = sortedEntries[lowIndex - 1].value;
      }
      
      if (canExpandRight) {
        rightVolume = sortedEntries[highIndex + 1].value;
      }
      
      // 标准扩展逻辑：优先选择成交量更大的方向
      bool expandLeft = false;
      bool expandRight = false;
      
      if (canExpandLeft && canExpandRight) {
        // 两边都可以扩展：选择成交量更大的一边
        if (leftVolume > rightVolume) {
          expandLeft = true;
        } else if (rightVolume > leftVolume) {
          expandRight = true;
        } else {
          // 成交量相等时：优先向下扩展（传统Volume Profile行为）
          expandLeft = true;
        }
      } else if (canExpandLeft) {
        // 只能向左（向下）扩展
        expandLeft = true;
      } else if (canExpandRight) {
        // 只能向右（向上）扩展
        expandRight = true;
      } else {
        // 无法继续扩展
        break;
      }
      
      // 执行扩展
      if (expandLeft) {
        lowIndex--;
        accumulatedVolume += sortedEntries[lowIndex].value;
      }
      
      if (expandRight) {
        highIndex++;
        accumulatedVolume += sortedEntries[highIndex].value;
      }
      
      // 检查是否已达到目标成交量
      if (accumulatedVolume >= targetVolume) {
        break;
      }
    }
    
    final result = {
      'VAL': sortedEntries[lowIndex].key,   // 价值区域下边界
      'VAH': sortedEntries[highIndex].key,  // 价值区域上边界
      'accumulated_volume': accumulatedVolume,
    };
    
    return result;
  }

  /// 获取Value Area统计信息
  Map<String, dynamic> getValueAreaStats({double percentage = 0.7}) {
    final valueArea = calculateValueArea(percentage: percentage);
    final totalVolumeByPrice = getTotalVolumeByPrice();
    final totalVolume = totalVolumeByPrice.values.fold(0.0, (sum, vol) => sum + vol);
    
    // 安全计算价格范围
    double priceRange = 0.0;
    if (totalVolumeByPrice.isNotEmpty) {
      final prices = totalVolumeByPrice.keys.toList();
      final maxPrice = prices.reduce((a, b) => a > b ? a : b);
      final minPrice = prices.reduce((a, b) => a < b ? a : b);
      priceRange = maxPrice - minPrice;
    }
    
    return {
      'poc': calculatePOC(),
      'val': valueArea['VAL'],
      'vah': valueArea['VAH'],
      'poc_volume': valueArea['poc_volume'],
      'value_area_volume': valueArea['value_area_volume'],
      'value_area_percentage': totalVolume > 0 ? (valueArea['value_area_volume']! / totalVolume) : 0.0,
      'total_volume': totalVolume,
      'price_range': priceRange,
      'volume_weighted_midpoint': calculateVWAP(),
    };
  }
}

/// 时间感知的全局缓存数据结构
class TimeAwareVolumeCache {
  // 按时间戳存储成交量分布 Map<timestamp, Map<price, volume>>
  Map<String, Map<double, double>> buyVolumeByTime = {};
  Map<String, Map<double, double>> sellVolumeByTime = {};
  
  // 时间范围和元数据
  int? startTime;
  int? endTime;
  String? symbol;
  DateTime? lastUpdateTime;
  DateTime? lastSaveTime; // 最后保存到本地的时间

  /// 默认构造函数
  TimeAwareVolumeCache();

  /// 序列化为JSON
  Map<String, dynamic> toJson() {
    return {
      'buyVolumeByTime': buyVolumeByTime.map((k, v) => MapEntry(k, v.map((pk, pv) => MapEntry(pk.toString(), pv)))),
      'sellVolumeByTime': sellVolumeByTime.map((k, v) => MapEntry(k, v.map((pk, pv) => MapEntry(pk.toString(), pv)))),
      'startTime': startTime,
      'endTime': endTime,
      'symbol': symbol,
      'lastUpdateTime': lastUpdateTime?.millisecondsSinceEpoch,
      'lastSaveTime': lastSaveTime?.millisecondsSinceEpoch,
      'version': 1, // 版本控制
    };
  }

  /// 从JSON反序列化
  factory TimeAwareVolumeCache.fromJson(Map<String, dynamic> json) {
    final cache = TimeAwareVolumeCache();
    
    // 反序列化买入数据
    final buyData = json['buyVolumeByTime'] as Map<String, dynamic>? ?? {};
    for (final timeEntry in buyData.entries) {
      final timestamp = timeEntry.key;
      final priceMap = timeEntry.value as Map<String, dynamic>;
      cache.buyVolumeByTime[timestamp] = priceMap.map((k, v) => MapEntry(double.parse(k), v.toDouble()));
    }
    
    // 反序列化卖出数据
    final sellData = json['sellVolumeByTime'] as Map<String, dynamic>? ?? {};
    for (final timeEntry in sellData.entries) {
      final timestamp = timeEntry.key;
      final priceMap = timeEntry.value as Map<String, dynamic>;
      cache.sellVolumeByTime[timestamp] = priceMap.map((k, v) => MapEntry(double.parse(k), v.toDouble()));
    }
    
    // 反序列化元数据
    cache.startTime = json['startTime'] as int?;
    cache.endTime = json['endTime'] as int?;
    cache.symbol = json['symbol'] as String?;
    
    final lastUpdateMs = json['lastUpdateTime'] as int?;
    if (lastUpdateMs != null) {
      cache.lastUpdateTime = DateTime.fromMillisecondsSinceEpoch(lastUpdateMs);
    }
    
    final lastSaveMs = json['lastSaveTime'] as int?;
    if (lastSaveMs != null) {
      cache.lastSaveTime = DateTime.fromMillisecondsSinceEpoch(lastSaveMs);
    }
    
    return cache;
  }

  /// 检查缓存是否过期（7天）
  bool isExpired() {
    if (lastUpdateTime == null) return true;
    final now = DateTime.now();
    final cacheAge = now.difference(lastUpdateTime!);
    return cacheAge.inDays >= 7;
  }

  /// 检查缓存是否包含指定时间范围
  bool containsTimeRange(int requestStart, int requestEnd) {
    if (startTime == null || endTime == null) return false;
    
    // 基本时间范围检查
    if (!(startTime! <= requestStart && endTime! >= requestEnd)) {
      return false;
    }
    
    // 检查是否有足够的时间戳数据覆盖请求范围
    final relevantTimestamps = getTimestampsInRange(requestStart, requestEnd);
    
    // 如果没有相关的时间戳数据，认为缓存不完整
    if (relevantTimestamps.isEmpty) {
      if (kDebugMode) {
        print('[VolumeProfile] 缓存时间范围匹配但无相关时间戳数据');
      }
      return false;
    }
    
    return true;
  }

  /// 检查缓存是否有效（未过期）
  bool isValid() {
    if (lastUpdateTime == null) return false;
    final now = DateTime.now();
    final cacheAge = now.difference(lastUpdateTime!);
    return cacheAge.inMinutes < 30; // 缓存30分钟有效
  }

  /// 获取指定时间范围内的时间戳
  List<String> getTimestampsInRange(int startTime, int endTime) {
    final startDateTime = DateTime.fromMillisecondsSinceEpoch(startTime);
    final endDateTime = DateTime.fromMillisecondsSinceEpoch(endTime);
    
    final relevantTimestamps = <String>[];
    
    // 合并买入和卖出数据的所有时间戳
    final allTimestamps = <String>{};
    allTimestamps.addAll(buyVolumeByTime.keys);
    allTimestamps.addAll(sellVolumeByTime.keys);
    
    for (String timestamp in allTimestamps) {
      // 解析时间戳字符串为DateTime
      try {
        final timestampDateTime = DateTime.parse(timestamp);
        // 使用更宽松的时间范围匹配，考虑到可能的时间精度问题
        if (timestampDateTime.isAfter(startDateTime.subtract(Duration(minutes: 2))) && 
            timestampDateTime.isBefore(endDateTime.add(Duration(minutes: 2)))) {
          relevantTimestamps.add(timestamp);
        }
      } catch (e) {
        // 时间戳解析失败，跳过
        if (kDebugMode) {
          print('[VolumeProfile] 时间戳解析失败: $timestamp');
        }
        continue;
      }
    }
    return relevantTimestamps;
  }

  /// 合并新数据到缓存
  void mergeData(MergedVolumeProfileData newData) {
    // 合并买入数据 - 按时间戳和价格存储（替换而非累加，避免重复计算）
    for (final timeEntry in newData.buyVolumeByTime.entries) {
      final timestamp = timeEntry.key;
      final priceVolumeMap = timeEntry.value;
      
      // 直接替换整个时间戳的数据，避免重复累加
      buyVolumeByTime[timestamp] = Map<double, double>.from(priceVolumeMap);
    }

    // 合并卖出数据 - 按时间戳和价格存储（替换而非累加，避免重复计算）
    for (final timeEntry in newData.sellVolumeByTime.entries) {
      final timestamp = timeEntry.key;
      final priceVolumeMap = timeEntry.value;
      
      // 直接替换整个时间戳的数据，避免重复累加
      sellVolumeByTime[timestamp] = Map<double, double>.from(priceVolumeMap);
    }

    // 扩展时间范围 - 安全处理nullable字段
    if (newData.startTime != null) {
      if (startTime == null || newData.startTime! < startTime!) {
        startTime = newData.startTime;
      }
    }
    
    if (newData.endTime != null) {
      if (endTime == null || newData.endTime! > endTime!) {
        endTime = newData.endTime;
      }
    }

    symbol = newData.symbol ?? symbol;
    lastUpdateTime = DateTime.now();
  }

  /// 根据时间范围和价格范围筛选缓存数据
  MergedVolumeProfileData filterByTimeAndPriceRange(int requestStart, int requestEnd, double minPrice, double maxPrice) {
    final relevantTimestamps = getTimestampsInRange(requestStart, requestEnd);
    
    // 合并指定时间范围内的所有数据
    final Map<double, double> mergedBuyVolume = {};
    final Map<double, double> mergedSellVolume = {};
    
    for (String timestamp in relevantTimestamps) {
      // 合并买入数据
      final buyData = buyVolumeByTime[timestamp] ?? {};
      for (final entry in buyData.entries) {
        final price = entry.key;
        final volume = entry.value;
        if (price >= minPrice && price <= maxPrice) {
          mergedBuyVolume[price] = (mergedBuyVolume[price] ?? 0.0) + volume;
        }
      }
      
      // 合并卖出数据
      final sellData = sellVolumeByTime[timestamp] ?? {};
      for (final entry in sellData.entries) {
        final price = entry.key;
        final volume = entry.value;
        if (price >= minPrice && price <= maxPrice) {
          mergedSellVolume[price] = (mergedSellVolume[price] ?? 0.0) + volume;
        }
      }
    }
    
    // 转换为VolumeDistributionData列表
    final List<VolumeDistributionData> filteredBuyData = mergedBuyVolume.entries
        .map((e) => VolumeDistributionData(price: e.key, volume: e.value))
        .toList();
    final List<VolumeDistributionData> filteredSellData = mergedSellVolume.entries
        .map((e) => VolumeDistributionData(price: e.key, volume: e.value))
        .toList();

    // 构建时间感知的数据结构
    final Map<String, Map<double, double>> resultBuyByTime = {};
    final Map<String, Map<double, double>> resultSellByTime = {};
    
    if (mergedBuyVolume.isNotEmpty) {
      resultBuyByTime['merged'] = mergedBuyVolume;
    }
    if (mergedSellVolume.isNotEmpty) {
      resultSellByTime['merged'] = mergedSellVolume;
    }

    return MergedVolumeProfileData(
      buyPriceDistribution: filteredBuyData,
      sellPriceDistribution: filteredSellData,
      buyVolumeByTime: resultBuyByTime,
      sellVolumeByTime: resultSellByTime,
      startTime: requestStart,
      endTime: requestEnd,
      symbol: symbol,
    );
  }

  /// 清空缓存
  void clear() {
    buyVolumeByTime.clear();
    sellVolumeByTime.clear();
    startTime = null;
    endTime = null;
    symbol = null;
    lastUpdateTime = null;
  }
}

/// Volume Profile数据管理器
class VolumeProfileDataManager {
  static const String _baseUrl = 'http://data-service.algoquant.org/api/v1/aggregated/volume-distribution';
  
  static final VolumeProfileDataManager _instance = VolumeProfileDataManager._internal();
  factory VolumeProfileDataManager() => _instance;
  VolumeProfileDataManager._internal();

  /// 全局缓存：每个交易对都有独立的缓存
  final Map<String, TimeAwareVolumeCache> _globalCaches = {};
  
  /// 币种数据可用性状态：用于控制是否处理socket数据
  final Map<String, bool> _instrumentDataAvailability = {};
  
  // 本地持久化相关
  static Timer? _saveTimer;
  static bool _isInitialized = false;
  static const String _cacheKeyPrefix = 'volume_profile_cache_';

  /// 标准化币种格式（用于API请求和缓存）
  String _normalizeInstrumentId(String instrumentId) {
    // 移除斜杠，转换为大写，用于API请求和缓存键
    return instrumentId.replaceAll('/', '').toUpperCase();
  }

  /// 初始化数据管理器（启动本地缓存加载和定时保存）
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    // 加载本地缓存
    await _instance._loadAllCachesFromLocal();
    
    // 启动定时保存器（1分钟间隔）
    _saveTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _instance._saveAllCachesToLocal();
    });
    
    _isInitialized = true;
    
    if (kDebugMode) {
      print('[VolumeProfileDataManager] 已初始化，加载了${_instance._globalCaches.length}个本地缓存');
    }
  }

  /// 保存所有缓存到本地
  Future<void> _saveAllCachesToLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      int savedCount = 0;
      
      for (final entry in _instance._globalCaches.entries) {
        final symbol = entry.key;
        final cache = entry.value;
        
        // 只保存有更新且未过期的缓存
        if (cache.lastUpdateTime != null && !cache.isExpired()) {
          // 检查是否需要保存（避免无意义的写入）
          if (cache.lastSaveTime == null || 
              cache.lastUpdateTime!.isAfter(cache.lastSaveTime!)) {
            
            cache.lastSaveTime = DateTime.now();
            final cacheKey = '$_cacheKeyPrefix$symbol';
            final jsonString = jsonEncode(cache.toJson());
            
            await prefs.setString(cacheKey, jsonString);
            savedCount++;
          }
        }
      }
      
      if (savedCount > 0 && kDebugMode) {
        print('[VolumeProfileDataManager] 保存了$savedCount个缓存到本地');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[VolumeProfileDataManager] 保存缓存失败: $e');
      }
    }
  }

  /// 从本地加载所有缓存
  Future<void> _loadAllCachesFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final allKeys = prefs.getKeys();
      int loadedCount = 0;
      
      for (final key in allKeys) {
        if (key.startsWith(_cacheKeyPrefix)) {
          final symbol = key.substring(_cacheKeyPrefix.length);
          final jsonString = prefs.getString(key);
          
          if (jsonString != null) {
            try {
              final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;
              final cache = TimeAwareVolumeCache.fromJson(jsonData);
              
              // 检查缓存是否过期
              if (!cache.isExpired()) {
                _globalCaches[symbol] = cache;
                loadedCount++;
              } else {
                // 删除过期缓存
                await prefs.remove(key);
                if (kDebugMode) {
                  print('[VolumeProfileDataManager] 删除过期缓存: $symbol');
                }
              }
            } catch (e) {
              if (kDebugMode) {
                print('[VolumeProfileDataManager] 解析缓存失败 $symbol: $e');
              }
              // 删除损坏的缓存
              await prefs.remove(key);
            }
          }
        }
      }
      
      if (kDebugMode) {
        print('[VolumeProfileDataManager] 从本地加载了$loadedCount个有效缓存');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[VolumeProfileDataManager] 加载本地缓存失败: $e');
      }
    }
  }

  /// 获取指定交易对的缓存，如果不存在则创建
  TimeAwareVolumeCache _getCache(String instrumentId) {
    final normalizedId = _normalizeInstrumentId(instrumentId);
    if (!_globalCaches.containsKey(normalizedId)) {
      _globalCaches[normalizedId] = TimeAwareVolumeCache();
    }
    return _globalCaches[normalizedId]!;
  }

  /// 实时聚合成交数据到缓存
  /// [symbol] 交易对
  /// [price] 成交价格
  /// [quantity] 成交量
  /// [isBuyerMaker] 是否买方挂单（true=卖出成交，false=买入成交）
  /// [tradeTime] 成交时间戳（毫秒）
  void aggregateRealtimeTradeData(
    String symbol, 
    double price, 
    double quantity, 
    bool isBuyerMaker, 
    int tradeTime
  ) {
    // 检查该币种是否应该处理socket数据
    if (!shouldProcessSocketData(symbol)) {
      return;
    }
    
    final cache = _getCache(symbol);
    
    // 将时间戳转换为ISO字符串格式（与API格式保持一致）
    final tradeDateTime = DateTime.fromMillisecondsSinceEpoch(tradeTime);
    final timestampKey = tradeDateTime.toIso8601String().substring(0, 19);
    
    // 根据买卖方向更新对应的缓存
    if (isBuyerMaker) {
      // 买方挂单 = 卖出成交
      if (!cache.sellVolumeByTime.containsKey(timestampKey)) {
        cache.sellVolumeByTime[timestampKey] = {};
      }
      
      // 聚合相同价格的成交量
      final currentVolume = cache.sellVolumeByTime[timestampKey]![price] ?? 0.0;
      cache.sellVolumeByTime[timestampKey]![price] = currentVolume + quantity;
    } else {
      // 卖方挂单 = 买入成交
      if (!cache.buyVolumeByTime.containsKey(timestampKey)) {
        cache.buyVolumeByTime[timestampKey] = {};
      }
      
      // 聚合相同价格的成交量
      final currentVolume = cache.buyVolumeByTime[timestampKey]![price] ?? 0.0;
      cache.buyVolumeByTime[timestampKey]![price] = currentVolume + quantity;
    }
    
    // 更新缓存的时间范围和元数据
    if (cache.startTime == null || tradeTime < cache.startTime!) {
      cache.startTime = tradeTime;
    }
    if (cache.endTime == null || tradeTime > cache.endTime!) {
      cache.endTime = tradeTime;
    }
    
    cache.symbol = symbol;
    cache.lastUpdateTime = DateTime.now();
  }

  /// 获取Volume Profile数据的主要方法
  Future<MergedVolumeProfileData?> getVolumeProfileData(String instrumentId, int startTime, int endTime) async {
    final normalizedId = _normalizeInstrumentId(instrumentId);

    // 确保已初始化
    if (!_isInitialized) {
      await initialize();
    }

    final cache = _getCache(instrumentId); // 注意：这里仍然用原始ID，因为_getCache内部会标准化

    // 检查缓存是否包含所需时间范围且有效
    // 总是重新筛选缓存数据以包含最新的聚合数据（包括实时数据）
    if (cache.isValid() && cache.containsTimeRange(startTime, endTime)) {
      // 重新筛选以确保包含最新的实时聚合数据
      return _filterCacheByKlinePriceRange(cache, startTime, endTime);
    }
    
    // 智能数据获取策略
    int fetchStartTime = startTime;
    
    if (cache.endTime != null && !cache.isExpired()) {
      // 如果有本地缓存且未过期，只获取增量数据
      if (cache.endTime! < endTime) {
        fetchStartTime = cache.endTime!;
        if (kDebugMode) {
          print('[VolumeProfile] 增量更新模式，从${DateTime.fromMillisecondsSinceEpoch(fetchStartTime).toString().substring(0, 19)}开始');
        }
      } else if (cache.startTime! > startTime) {
        // 需要获取更早的数据
        fetchStartTime = startTime;
        endTime = cache.startTime!;
      } else {
        // 缓存已包含所需范围，直接返回
        return _filterCacheByKlinePriceRange(cache, startTime, endTime);
      }
    } else {
      // 无有效缓存，获取完整数据
      if (kDebugMode) {
        print('[VolumeProfile] 完整数据获取模式');
      }
    }

    // 从API获取数据（使用标准化的币种ID）
    final newData = await _fetchVolumeProfileData(normalizedId, fetchStartTime, endTime);
    if (newData != null) {
      // 更新缓存
      cache.mergeData(newData);
      
      // 重要：从缓存中筛选返回数据，确保数据一致性
      return _filterCacheByKlinePriceRange(cache, startTime, endTime);
    }
    
    return null;
  }

  /// 根据K线价格范围从缓存中筛选数据
  MergedVolumeProfileData? _filterCacheByKlinePriceRange(TimeAwareVolumeCache cache, int startTime, int endTime) {
    final allBuyPrices = cache.buyVolumeByTime.values.expand((e) => e.keys).toList();
    final allSellPrices = cache.sellVolumeByTime.values.expand((e) => e.keys).toList();
    final allPrices = [...allBuyPrices, ...allSellPrices];
    
    if (allPrices.isEmpty) return null;
    
    allPrices.sort();
    final minPrice = allPrices.first;
    final maxPrice = allPrices.last;
    
    return cache.filterByTimeAndPriceRange(startTime, endTime, minPrice, maxPrice);
  }

  /// 清除指定交易对的缓存
  void clearCache(String instrumentId) {
    final normalizedId = _normalizeInstrumentId(instrumentId);
    _globalCaches[normalizedId]?.clear();
  }

  /// 清除所有缓存
  void clearAllCaches() {
    _globalCaches.forEach((key, cache) => cache.clear());
    _globalCaches.clear();
  }

  /// 从API获取Volume Profile数据
  Future<MergedVolumeProfileData?> _fetchVolumeProfileData(String instrumentId, int startTime, int endTime) async {
    try {
      final response = await _fetchAllPages(instrumentId, startTime, endTime);
      
      if (response == null || response.isEmpty) {
        return null;
      }
      
      // 合并所有页面的数据
      final mergedData = _mergeApiResponse(response, startTime, endTime);
      
      if (kDebugMode) {
        // 输出价格范围信息来验证数据正确性
        final buyPrices = mergedData.buyPriceDistribution.map((e) => e.price).toList();
        final sellPrices = mergedData.sellPriceDistribution.map((e) => e.price).toList();
        final allPrices = [...buyPrices, ...sellPrices];
        
        if (allPrices.isNotEmpty) {
          allPrices.sort();
          final minPrice = allPrices.first;
          final maxPrice = allPrices.last;
          print('[VolumeProfile] 价格范围: ${minPrice.toStringAsFixed(2)} - ${maxPrice.toStringAsFixed(2)}');
        }
      }
      
      return mergedData;
      
    } catch (e) {
      if (kDebugMode) {
        print('[VolumeProfileDataManager] 获取数据时出错: $e');
      }
      return null;
    }
  }

  /// 获取所有页面的数据
  Future<List<Map<String, dynamic>>?> _fetchAllPages(String instrumentId, int startTime, int endTime) async {
    final allData = <Map<String, dynamic>>[];
    int currentPage = 1;
    int totalPages = 1;
    bool hasAnyVolumeData = false; // 添加标记来跟踪是否有实际的成交量数据
    
    do {
      final url = _buildUrl(instrumentId, startTime, endTime, currentPage);
      final response = await http.get(Uri.parse(url));
      if (response.statusCode != 200) {
        if (kDebugMode) {
          print('[VolumeProfileDataManager] API请求失败: ${response.statusCode}');
        }
        return null;
      }
      
      final jsonData = json.decode(response.body);
      
      if (!jsonData['success']) {
        return null;
      }
      
      // 从aggregated_data字段获取实际数据
      final aggregatedData = jsonData['aggregated_data'];
      if (aggregatedData != null && aggregatedData is List) {
        // 检查每个数据项是否有实际的成交量分布数据
        for (final item in aggregatedData) {
          final Map<String, dynamic> buyData = item['buy_price_distribution'] ?? {};
          final Map<String, dynamic> sellData = item['sell_price_distribution'] ?? {};
          
          // 检查是否有买入或卖出数据
          bool hasValidBuyData = false;
          bool hasValidSellData = false;
          
          // 检查买入数据
          for (final timestampEntry in buyData.entries) {
            final List<dynamic> priceVolumeList = timestampEntry.value ?? [];
            if (priceVolumeList.isNotEmpty) {
              hasValidBuyData = true;
              break;
            }
          }
          
          // 检查卖出数据
          for (final timestampEntry in sellData.entries) {
            final List<dynamic> priceVolumeList = timestampEntry.value ?? [];
            if (priceVolumeList.isNotEmpty) {
              hasValidSellData = true;
              break;
            }
          }
          
          // 如果有任何有效数据，添加到结果中
          if (hasValidBuyData || hasValidSellData) {
            allData.add(item);
            hasAnyVolumeData = true;
          }
        }
        
        // 从根级别获取总页数，只在第一页时获取
        if (currentPage == 1) {
          totalPages = jsonData['total_pages'] ?? 1;
        }
      } else {
        break;
      }
      
      currentPage++;
    } while (currentPage <= totalPages);
    
    // 只有当确实有成交量数据时才返回数据，否则返回null
    if (hasAnyVolumeData && allData.isNotEmpty) {
      if (kDebugMode) {
        print('[VolumeProfileDataManager] 币种 $instrumentId 有有效的成交量分布数据，数据项数: ${allData.length}');
      }
      return allData;
    } else {
      if (kDebugMode) {
        print('[VolumeProfileDataManager] 币种 $instrumentId 没有有效的成交量分布数据');
      }
      return null;
    }
  }

  /// 构建API请求URL
  String _buildUrl(String instrumentId, int startTime, int endTime, int page) {
    // 不使用toUtc()，直接使用本地时间
    final startDateTime = DateTime.fromMillisecondsSinceEpoch(startTime);
    final endDateTime = DateTime.fromMillisecondsSinceEpoch(endTime);
    
    final startTimeStr = startDateTime.toIso8601String().substring(0, 19);
    final endTimeStr = endDateTime.toIso8601String().substring(0, 19);
    
    return '$_baseUrl?symbol=$instrumentId&start_time=$startTimeStr&end_time=$endTimeStr&page=$page&page_size=1000';
  }

  /// 合并API响应数据
  MergedVolumeProfileData _mergeApiResponse(List<Map<String, dynamic>> allData, int startTime, int endTime) {
    final List<VolumeDistributionData> buyDistribution = [];
    final List<VolumeDistributionData> sellDistribution = [];
    final Map<String, Map<double, double>> buyVolumeByTime = {};
    final Map<String, Map<double, double>> sellVolumeByTime = {};
    
    for (final item in allData) {
      // 新格式：buy_price_distribution和sell_price_distribution是以时间戳为key的对象
      final Map<String, dynamic> buyData = item['buy_price_distribution'] ?? {};
      final Map<String, dynamic> sellData = item['sell_price_distribution'] ?? {};
      
      // 处理买入分布数据
      for (final timestampEntry in buyData.entries) {
        final timestamp = timestampEntry.key;
        final List<dynamic> priceVolumeList = timestampEntry.value ?? [];
        
        if (!buyVolumeByTime.containsKey(timestamp)) {
          buyVolumeByTime[timestamp] = {};
        }
        
        for (final priceVolumeData in priceVolumeList) {
          final priceVolume = VolumeDistributionData.fromJson(priceVolumeData);
          buyDistribution.add(priceVolume);
          
          // 存储到时间感知的Map中
          buyVolumeByTime[timestamp]![priceVolume.price] = 
              (buyVolumeByTime[timestamp]![priceVolume.price] ?? 0.0) + priceVolume.volume;
        }
      }
      
      // 处理卖出分布数据
      for (final timestampEntry in sellData.entries) {
        final timestamp = timestampEntry.key;
        final List<dynamic> priceVolumeList = timestampEntry.value ?? [];
        
        if (!sellVolumeByTime.containsKey(timestamp)) {
          sellVolumeByTime[timestamp] = {};
        }
        
        for (final priceVolumeData in priceVolumeList) {
          final priceVolume = VolumeDistributionData.fromJson(priceVolumeData);
          sellDistribution.add(priceVolume);
          
          // 存储到时间感知的Map中
          sellVolumeByTime[timestamp]![priceVolume.price] = 
              (sellVolumeByTime[timestamp]![priceVolume.price] ?? 0.0) + priceVolume.volume;
        }
      }
    }
    
    return MergedVolumeProfileData(
      buyPriceDistribution: buyDistribution,
      sellPriceDistribution: sellDistribution,
      buyVolumeByTime: buyVolumeByTime,
      sellVolumeByTime: sellVolumeByTime,
      startTime: startTime,
      endTime: endTime,
      symbol: null, // 从API响应中提取symbol字段，这里先设为null
    );
  }

  /// 清理过期的缓存数据
  void cleanupExpiredCache() {
    final expiredKeys = <String>[];
    
    _globalCaches.forEach((key, cache) {
      if (!cache.isValid()) {
        expiredKeys.add(key);
      }
    });
    
    for (final key in expiredKeys) {
      _globalCaches[key]?.clear();
      _globalCaches.remove(key);
      if (kDebugMode) {
        print('[VolumeProfileDataManager] 清理过期缓存: $key');
      }
    }
  }

  /// 获取缓存状态信息
  Map<String, dynamic> getCacheStatus() {
    final status = <String, dynamic>{};
    status['total_cached_instruments'] = _globalCaches.length;
    status['cache_details'] = <String, dynamic>{};
    
    _globalCaches.forEach((key, cache) {
      status['cache_details'][key] = {
        'start_time': cache.startTime,
        'end_time': cache.endTime,
        'last_update_time': cache.lastUpdateTime?.toIso8601String(),
        'last_save_time': cache.lastSaveTime?.toIso8601String(),
        'is_valid': cache.isValid(),
        'is_expired': cache.isExpired(),
        'buy_timestamps': cache.buyVolumeByTime.length,
        'sell_timestamps': cache.sellVolumeByTime.length,
        'symbol': cache.symbol,
      };
    });
    
    return status;
  }

  /// 释放资源
  void dispose() {
    // 在释放前保存一次缓存
    if (_isInitialized) {
      _saveAllCachesToLocal();
    }
    
    // 停止定时器
    _saveTimer?.cancel();
    _saveTimer = null;
    _isInitialized = false;
    
    clearAllCaches();
    if (kDebugMode) {
      print('[VolumeProfileDataManager] 数据管理器已释放');
    }
  }

  /// 静态方法：全局释放资源（用于应用关闭时清理）
  static void disposeAll() {
    // 停止定时器
    _saveTimer?.cancel();
    _saveTimer = null;
    _isInitialized = false;
    
    // 清理所有缓存
    _instance._globalCaches.forEach((key, cache) => cache.clear());
    _instance._globalCaches.clear();
    
    if (kDebugMode) {
      print('[VolumeProfileDataManager] 全局资源已释放');
    }
  }

  /// 检查指定币种是否有缓存数据
  bool hasCachedData(String instrumentId) {
    final normalizedId = _normalizeInstrumentId(instrumentId);
    final cache = _globalCaches[normalizedId];
    if (cache == null) return false;
    
    // 检查是否有任何时间段的数据
    final hasBuyData = cache.buyVolumeByTime.isNotEmpty;
    final hasSellData = cache.sellVolumeByTime.isNotEmpty;
    
    return hasBuyData || hasSellData;
  }

  /// 检查指定币种是否有Volume Profile数据
  Future<bool> hasVolumeProfileData(String instrumentId) async {
    final normalizedId = _normalizeInstrumentId(instrumentId);
    
    try {
      // 首先检查本地缓存
      if (hasCachedData(instrumentId)) {
        return true;
      }
      
      // 本地缓存没有数据，检查接口是否有数据
      // 使用一个较小的时间范围来测试是否有数据，减少网络请求开销
      final now = DateTime.now().millisecondsSinceEpoch;
      final testStartTime = now - (24 * 60 * 60 * 1000); // 过去24小时
      final testEndTime = now;
      
      // 使用标准化的币种ID调用_fetchAllPages来检查原始数据
      final rawData = await _fetchAllPages(normalizedId, testStartTime, testEndTime);
      
      // _fetchAllPages已经检查了实际的买卖分布数据，如果返回非null说明有有效数据
      final hasValidData = rawData != null;
      
      return hasValidData;
    } catch (e) {
      if (kDebugMode) {
        print('[VolumeProfileDataManager] 检查币种 $instrumentId 数据可用性时出错: $e');
      }
      return false;
    }
  }

  /// 检查并处理币种切换时的数据可用性
  Future<bool> checkInstrumentDataAvailability(String oldInstrumentId, String newInstrumentId) async {
    // 如果币种没有变化，返回true（保持现状）
    if (oldInstrumentId == newInstrumentId) return true;
    
    try {
      // 检查新币种是否有Volume Profile数据
      final hasData = await hasVolumeProfileData(newInstrumentId);
      
      if (kDebugMode) {
        if (hasData) {
          print('[VolumeProfileDataManager] 币种 $newInstrumentId 有Volume Profile数据，可以继续使用指标');
        } else {
          print('[VolumeProfileDataManager] 币种 $newInstrumentId 没有Volume Profile数据，建议移除指标');
        }
      }
      
      return hasData;
    } catch (e) {
      if (kDebugMode) {
        print('[VolumeProfileDataManager] 检查币种切换数据可用性时出错: $e');
      }
      return false;
    }
  }

  /// 设置币种数据可用性状态
  void setInstrumentDataAvailability(String instrumentId, bool isAvailable) {
    final normalizedId = _normalizeInstrumentId(instrumentId);
    _instrumentDataAvailability[normalizedId] = isAvailable;
    
    if (kDebugMode) {
      print('[VolumeProfileDataManager] 设置币种 $instrumentId 数据可用性: $isAvailable');
    }
  }

  /// 检查币种是否应该处理socket数据
  bool shouldProcessSocketData(String instrumentId) {
    final normalizedId = _normalizeInstrumentId(instrumentId);
    final isAvailable = _instrumentDataAvailability[normalizedId] ?? true; // 默认为true，保持兼容性
    
    // if (!isAvailable && kDebugMode) {
    //   print('[VolumeProfileDataManager] 币种 $instrumentId 数据不可用，跳过socket数据处理');
    // }
    
    return isAvailable;
  }
} 