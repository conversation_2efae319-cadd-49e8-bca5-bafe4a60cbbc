import 'dart:async';
import 'dart:collection';
import 'dart:ui' as ui;
import 'package:flutter/scheduler.dart';
import 'package:flutter/material.dart';
import '../config/indicator_config_manager.dart';

/// 指标绘制优化器 - 专门优化指标的绘制性能
class IndicatorPaintOptimizer {
  static final IndicatorPaintOptimizer _instance = IndicatorPaintOptimizer._internal();
  factory IndicatorPaintOptimizer() => _instance;
  IndicatorPaintOptimizer._internal();

  // 指标绘制队列
  final Queue<IndicatorPaintTask> _paintQueue = Queue();
  
  // 当前绘制状态
  bool _isPainting = false;
  
  // 指标绘制缓存
  final Map<String, IndicatorPaintCache> _paintCache = {};
  
  // 性能统计
  final Map<String, List<double>> _indicatorPaintTimes = {};
  
  // 绘制优先级配置（从指标配置文件中动态读取）
  final Map<String, int> _indicatorPriorities = {};

  /// 调度指标绘制
  void scheduleIndicatorPaint({
    required String indicatorKey,
    required Canvas canvas,
    required Size size,
    required VoidCallback paintCallback,
    bool forceRepaint = false,
  }) {
    // 检查缓存
    if (!forceRepaint && _hasCachedPaint(indicatorKey, size)) {
      _drawFromCache(indicatorKey, canvas);
      return;
    }

    final task = IndicatorPaintTask(
      indicatorKey: indicatorKey,
      canvas: canvas,
      size: size,
      paintCallback: paintCallback,
      priority: _indicatorPriorities[indicatorKey] ?? 999,
      createdAt: DateTime.now(),
    );

    _paintQueue.add(task);
    
    if (!_isPainting) {
      _processPaintQueue();
    }
  }

  /// 处理绘制队列
  void _processPaintQueue() {
    if (_isPainting || _paintQueue.isEmpty) return;
    
    _isPainting = true;
    _processNextPaintBatch();
  }

  /// 处理下一批绘制任务
  void _processNextPaintBatch() {
    if (_paintQueue.isEmpty) {
      _isPainting = false;
      return;
    }

    // 按优先级排序
    final tasks = <IndicatorPaintTask>[];
    const maxBatchSize = 3; // 每批最多处理3个指标
    
    // 收集当前批次的任务
    while (_paintQueue.isNotEmpty && tasks.length < maxBatchSize) {
      tasks.add(_paintQueue.removeFirst());
    }
    
    // 按优先级排序
    tasks.sort((a, b) => a.priority.compareTo(b.priority));
    
    // 执行绘制任务
    for (final task in tasks) {
      _executePaintTask(task);
    }
    
    // 如果还有任务，在下一帧继续
    if (_paintQueue.isNotEmpty) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _processNextPaintBatch();
      });
    } else {
      _isPainting = false;
    }
  }

  /// 执行单个绘制任务
  void _executePaintTask(IndicatorPaintTask task) {
    final startTime = DateTime.now();
    
    try {
      // 创建绘制缓存
      final cache = _createPaintCache(task);
      
      // 执行绘制
      task.paintCallback();
      
      // 保存缓存
      _paintCache[task.indicatorKey] = cache;
      
      // 记录绘制时间
      final paintTime = DateTime.now().difference(startTime).inMicroseconds / 1000.0;
      _recordPaintTime(task.indicatorKey, paintTime);
      
      // 性能监控：记录慢绘制但不输出日志
      
    } catch (e) {
      // 忽略绘制失败，继续处理其他任务
    }
  }

  /// 创建绘制缓存
  IndicatorPaintCache _createPaintCache(IndicatorPaintTask task) {
    return IndicatorPaintCache(
      indicatorKey: task.indicatorKey,
      size: task.size,
      createdAt: DateTime.now(),
      lastUsed: DateTime.now(),
    );
  }

  /// 检查是否有缓存的绘制
  bool _hasCachedPaint(String indicatorKey, Size size) {
    final cache = _paintCache[indicatorKey];
    if (cache == null) return false;
    
    // 检查尺寸是否匹配
    if (cache.size != size) return false;
    
    // 检查缓存是否过期（5秒）
    final age = DateTime.now().difference(cache.createdAt);
    if (age.inSeconds > 5) {
      _paintCache.remove(indicatorKey);
      return false;
    }
    
    return true;
  }

  /// 从缓存绘制
  void _drawFromCache(String indicatorKey, Canvas canvas) {
    final cache = _paintCache[indicatorKey];
    if (cache?.picture != null) {
      canvas.drawPicture(cache!.picture!);
      cache.lastUsed = DateTime.now();
    }
  }

  /// 记录绘制时间
  void _recordPaintTime(String indicatorKey, double paintTime) {
    _indicatorPaintTimes[indicatorKey] ??= [];
    final times = _indicatorPaintTimes[indicatorKey]!;
    
    times.add(paintTime);
    if (times.length > 10) {
      times.removeAt(0);
    }
  }

  /// 清理过期缓存
  void cleanupExpiredCache() {
    final now = DateTime.now();
    final keysToRemove = <String>[];
    
    for (final entry in _paintCache.entries) {
      final age = now.difference(entry.value.lastUsed);
      if (age.inMinutes > 2) {
        keysToRemove.add(entry.key);
      }
    }
    
    for (final key in keysToRemove) {
      _paintCache[key]?.dispose();
      _paintCache.remove(key);
    }
    
    // 缓存清理完成，不输出日志
  }

  /// 强制清理所有缓存
  void clearAllCache() {
    for (final cache in _paintCache.values) {
      cache.dispose();
    }
    _paintCache.clear();
    
    // 所有缓存清理完成
  }

  /// 从配置管理器更新指标优先级
  void updatePrioritiesFromConfig(IndicatorConfigManager configManager) {
    // 清空现有优先级
    _indicatorPriorities.clear();

    // 从配置中读取优先级
    for (final type in IndicatorType.values) {
      final config = configManager.getConfig(type);
      if (config != null) {
        _indicatorPriorities[type.key] = config.renderPriority;
      }
    }

    // 指标优先级更新完成
  }

  /// 设置指标优先级
  void setIndicatorPriority(String indicatorKey, int priority) {
    _indicatorPriorities[indicatorKey] = priority;
  }

  /// 获取指标平均绘制时间
  double getAveragePaintTime(String indicatorKey) {
    final times = _indicatorPaintTimes[indicatorKey];
    if (times == null || times.isEmpty) return 0.0;
    
    return times.reduce((a, b) => a + b) / times.length;
  }

  /// 获取性能统计
  Map<String, dynamic> getPerformanceStats() {
    final stats = <String, dynamic>{};
    
    for (final entry in _indicatorPaintTimes.entries) {
      final times = entry.value;
      if (times.isNotEmpty) {
        stats[entry.key] = {
          'averageTime': times.reduce((a, b) => a + b) / times.length,
          'maxTime': times.reduce((a, b) => a > b ? a : b),
          'minTime': times.reduce((a, b) => a < b ? a : b),
          'sampleCount': times.length,
        };
      }
    }
    
    return {
      'indicatorStats': stats,
      'cacheSize': _paintCache.length,
      'queueSize': _paintQueue.length,
      'isPainting': _isPainting,
    };
  }

  /// 释放资源
  void dispose() {
    clearAllCache();
    _paintQueue.clear();
    _indicatorPaintTimes.clear();
  }
}

/// 指标绘制任务
class IndicatorPaintTask {
  final String indicatorKey;
  final Canvas canvas;
  final Size size;
  final VoidCallback paintCallback;
  final int priority;
  final DateTime createdAt;

  IndicatorPaintTask({
    required this.indicatorKey,
    required this.canvas,
    required this.size,
    required this.paintCallback,
    required this.priority,
    required this.createdAt,
  });

  @override
  String toString() {
    return 'IndicatorPaintTask(key: $indicatorKey, priority: $priority, age: ${DateTime.now().difference(createdAt).inMilliseconds}ms)';
  }
}

/// 指标绘制缓存
class IndicatorPaintCache {
  final String indicatorKey;
  final Size size;
  final DateTime createdAt;
  DateTime lastUsed;
  ui.Picture? picture;

  IndicatorPaintCache({
    required this.indicatorKey,
    required this.size,
    required this.createdAt,
    required this.lastUsed,
    this.picture,
  });

  void dispose() {
    picture?.dispose();
    picture = null;
  }
}
