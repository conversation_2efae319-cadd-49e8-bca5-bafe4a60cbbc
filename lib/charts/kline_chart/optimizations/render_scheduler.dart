import 'dart:async';
import 'dart:collection';
import 'package:flutter/scheduler.dart';

/// 渲染调度器 - 控制K线图和指标的绘制优先级和顺序
class RenderScheduler {
  static final RenderScheduler _instance = RenderScheduler._internal();
  factory RenderScheduler() => _instance;
  RenderScheduler._internal();

  // 渲染任务队列
  final Queue<RenderTask> _renderQueue = Queue();
  
  // 当前是否正在渲染
  bool _isRendering = false;
  
  // 渲染统计
  int _totalRenderTasks = 0;
  int _completedRenderTasks = 0;
  
  // 性能监控
  final List<double> _renderTimes = [];
  DateTime? _renderStartTime;

  /// 渲染优先级定义
  static const Map<RenderLayer, int> _layerPriority = {
    RenderLayer.background: 1,    // 背景网格
    RenderLayer.candle: 2,        // K线主体
    RenderLayer.volume: 3,        // 成交量
    RenderLayer.mainIndicator: 4, // 主区指标
    RenderLayer.subIndicator: 5,  // 副区指标
    RenderLayer.cross: 6,         // 十字线
    RenderLayer.overlay: 7,       // 覆盖层（绘图工具等）
  };

  /// 调度渲染任务
  void scheduleRender(RenderTask task) {
    _renderQueue.add(task);
    _totalRenderTasks++;
    
    if (!_isRendering) {
      _processRenderQueue();
    }
  }

  /// 批量调度多个渲染任务
  void scheduleMultipleRenders(List<RenderTask> tasks) {
    // 按优先级排序
    tasks.sort((a, b) => _layerPriority[a.layer]!.compareTo(_layerPriority[b.layer]!));
    
    for (final task in tasks) {
      _renderQueue.add(task);
      _totalRenderTasks++;
    }
    
    if (!_isRendering) {
      _processRenderQueue();
    }
  }

  /// 处理渲染队列
  void _processRenderQueue() {
    if (_isRendering || _renderQueue.isEmpty) return;
    
    _isRendering = true;
    _renderStartTime = DateTime.now();
    
    // 使用微任务确保不阻塞UI线程
    _processNextFrame();
  }

  /// 处理下一帧的渲染任务
  void _processNextFrame() {
    if (_renderQueue.isEmpty) {
      _isRendering = false;
      _recordRenderTime();
      return;
    }

    // 每帧最多处理3个渲染任务，避免阻塞
    const maxTasksPerFrame = 3;
    int processedCount = 0;
    
    while (_renderQueue.isNotEmpty && processedCount < maxTasksPerFrame) {
      final task = _renderQueue.removeFirst();
      
      try {
        _executeRenderTask(task);
        _completedRenderTasks++;
        processedCount++;
      } catch (e) {
        // 忽略渲染任务执行失败，继续处理其他任务
      }
    }

    // 如果还有任务，在下一帧继续处理
    if (_renderQueue.isNotEmpty) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _processNextFrame();
      });
    } else {
      _isRendering = false;
      _recordRenderTime();
    }
  }

  /// 执行单个渲染任务
  void _executeRenderTask(RenderTask task) {
    // 执行渲染回调
    task.renderCallback();
  }

  /// 记录渲染时间
  void _recordRenderTime() {
    if (_renderStartTime != null) {
      final totalTime = DateTime.now().difference(_renderStartTime!).inMicroseconds / 1000.0;
      _renderTimes.add(totalTime);
      
      if (_renderTimes.length > 30) {
        _renderTimes.removeAt(0);
      }
      
      // 渲染批次完成，记录时间用于性能统计
    }
  }

  /// 清空渲染队列（紧急情况下使用）
  void clearRenderQueue() {
    _renderQueue.clear();
    _isRendering = false;
    
    // 渲染队列已清空
  }

  /// 获取渲染统计信息
  Map<String, dynamic> getRenderStats() {
    final avgRenderTime = _renderTimes.isEmpty 
        ? 0.0 
        : _renderTimes.reduce((a, b) => a + b) / _renderTimes.length;
    
    return {
      'totalTasks': _totalRenderTasks,
      'completedTasks': _completedRenderTasks,
      'pendingTasks': _renderQueue.length,
      'isRendering': _isRendering,
      'averageRenderTime': avgRenderTime,
      'maxRenderTime': _renderTimes.isEmpty ? 0.0 : _renderTimes.reduce((a, b) => a > b ? a : b),
    };
  }

  /// 重置统计信息
  void resetStats() {
    _totalRenderTasks = 0;
    _completedRenderTasks = 0;
    _renderTimes.clear();
  }
}

/// 渲染层级枚举
enum RenderLayer {
  background,     // 背景网格
  candle,         // K线主体
  volume,         // 成交量
  mainIndicator,  // 主区指标
  subIndicator,   // 副区指标
  cross,          // 十字线
  overlay,        // 覆盖层
}

/// 渲染任务
class RenderTask {
  final RenderLayer layer;
  final VoidCallback renderCallback;
  final String? description;
  final DateTime createdAt;

  RenderTask({
    required this.layer,
    required this.renderCallback,
    this.description,
  }) : createdAt = DateTime.now();

  @override
  String toString() {
    return 'RenderTask(layer: $layer, description: $description, age: ${DateTime.now().difference(createdAt).inMilliseconds}ms)';
  }
}

/// 指标渲染管理器
class IndicatorRenderManager {
  final RenderScheduler _scheduler = RenderScheduler();
  
  // 指标渲染状态
  final Map<String, bool> _indicatorRenderStates = {};
  
  // 指标优先级配置
  final Map<String, int> _indicatorPriorities = {
    'MA': 1,        // 移动平均线 - 最高优先级
    'BOLL': 2,      // 布林带
    'MACD': 3,      // MACD
    'RSI': 4,       // RSI
    'KDJ': 5,       // KDJ
    'VOL': 6,       // 成交量
    'VWAP': 7,      // VWAP
    'SAR': 8,       // SAR
  };

  /// 调度指标渲染
  void scheduleIndicatorRender({
    required String indicatorKey,
    required VoidCallback renderCallback,
    bool isMainIndicator = true,
  }) {
    // 检查是否已在渲染中
    if (_indicatorRenderStates[indicatorKey] == true) {
      return;
    }
    
    _indicatorRenderStates[indicatorKey] = true;
    
    final layer = isMainIndicator ? RenderLayer.mainIndicator : RenderLayer.subIndicator;
    final priority = _indicatorPriorities[indicatorKey] ?? 999;
    
    final task = RenderTask(
      layer: layer,
      renderCallback: () {
        renderCallback();
        _indicatorRenderStates[indicatorKey] = false;
      },
      description: '$indicatorKey (priority: $priority)',
    );
    
    _scheduler.scheduleRender(task);
  }

  /// 批量调度多个指标渲染
  void scheduleMultipleIndicators(Map<String, VoidCallback> indicators, {bool isMainIndicator = true}) {
    final tasks = <RenderTask>[];
    
    // 按优先级排序指标
    final sortedIndicators = indicators.entries.toList()
      ..sort((a, b) => (_indicatorPriorities[a.key] ?? 999).compareTo(_indicatorPriorities[b.key] ?? 999));
    
    for (final entry in sortedIndicators) {
      final indicatorKey = entry.key;
      final renderCallback = entry.value;
      
      if (_indicatorRenderStates[indicatorKey] == true) {
        continue;
      }
      
      _indicatorRenderStates[indicatorKey] = true;
      
      final layer = isMainIndicator ? RenderLayer.mainIndicator : RenderLayer.subIndicator;
      
      tasks.add(RenderTask(
        layer: layer,
        renderCallback: () {
          renderCallback();
          _indicatorRenderStates[indicatorKey] = false;
        },
        description: '$indicatorKey (batch)',
      ));
    }
    
    _scheduler.scheduleMultipleRenders(tasks);
  }

  /// 设置指标优先级
  void setIndicatorPriority(String indicatorKey, int priority) {
    _indicatorPriorities[indicatorKey] = priority;
  }

  /// 获取指标渲染状态
  bool isIndicatorRendering(String indicatorKey) {
    return _indicatorRenderStates[indicatorKey] ?? false;
  }

  /// 清理指标渲染状态
  void clearIndicatorStates() {
    _indicatorRenderStates.clear();
  }

  /// 获取渲染统计
  Map<String, dynamic> getStats() {
    return {
      'scheduler': _scheduler.getRenderStats(),
      'indicatorStates': Map.from(_indicatorRenderStates),
      'indicatorPriorities': Map.from(_indicatorPriorities),
    };
  }
}
