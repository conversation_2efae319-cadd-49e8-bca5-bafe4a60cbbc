import 'dart:async';
import 'dart:collection';
import 'package:flutter/scheduler.dart';

/// 高级性能优化器 - 解决K线图卡顿问题
class AdvancedPerformanceOptimizer {
  // 单例模式
  static final AdvancedPerformanceOptimizer _instance = AdvancedPerformanceOptimizer._internal();
  factory AdvancedPerformanceOptimizer() => _instance;
  AdvancedPerformanceOptimizer._internal();

  // 帧率监控
  final FrameRateMonitor _frameRateMonitor = FrameRateMonitor();
  
  // 智能重绘管理器
  final SmartRepaintManager _repaintManager = SmartRepaintManager();

  // 数据处理优化器
  final DataProcessingOptimizer _dataOptimizer = DataProcessingOptimizer();

  // 内存管理器
  final MemoryManager _memoryManager = MemoryManager();

  // 渲染优化器
  final RenderOptimizer _renderOptimizer = RenderOptimizer();

  // 公共访问器
  SmartRepaintManager get repaintManager => _repaintManager;
  DataProcessingOptimizer get dataOptimizer => _dataOptimizer;
  MemoryManager get memoryManager => _memoryManager;
  RenderOptimizer get renderOptimizer => _renderOptimizer;

  /// 初始化高级性能优化器
  void initialize() {
    _frameRateMonitor.start();
    _repaintManager.initialize();
    _dataOptimizer.initialize();
    _memoryManager.initialize();
    _renderOptimizer.initialize();
    
    // 高级性能优化器初始化完成
  }

  /// 获取性能统计信息
  Map<String, dynamic> getPerformanceStats() {
    return {
      'frameRate': _frameRateMonitor.currentFPS,
      'repaintCount': _repaintManager.repaintCount,
      'memoryUsage': _memoryManager.getMemoryUsage(),
      'renderTime': _renderOptimizer.averageRenderTime,
    };
  }

  /// 优化WebSocket数据处理
  void optimizeWebSocketData(dynamic data, Function(dynamic) processor) {
    _dataOptimizer.processData(data, processor);
  }

  /// 优化重绘操作
  void optimizeRepaint(String layerKey, VoidCallback repaintCallback) {
    _repaintManager.scheduleRepaint(layerKey, repaintCallback);
  }

  /// 优化内存使用
  void optimizeMemory() {
    _memoryManager.performCleanup();
  }

  /// 启用高性能模式
  void enableHighPerformanceMode() {
    _renderOptimizer.enableHighPerformanceMode();
  }

  /// 禁用高性能模式
  void disableHighPerformanceMode() {
    _renderOptimizer.disableHighPerformanceMode();
  }

  /// 是否处于高性能模式
  bool get isHighPerformanceMode => _renderOptimizer.isHighPerformanceMode;

  /// 获取渲染优化建议
  Map<String, dynamic> getOptimizationSuggestions() {
    return _renderOptimizer.getOptimizationSuggestions();
  }

  /// 释放资源
  void dispose() {
    _frameRateMonitor.stop();
    _repaintManager.dispose();
    _dataOptimizer.dispose();
    _memoryManager.dispose();
    _renderOptimizer.dispose();
  }
}

/// 帧率监控器
class FrameRateMonitor {
  Timer? _timer;
  int _frameCount = 0;
  double _currentFPS = 60.0;
  DateTime _lastTime = DateTime.now();

  double get currentFPS => _currentFPS;

  void start() {
    SchedulerBinding.instance.addPostFrameCallback(_onFrame);
    _timer = Timer.periodic(const Duration(seconds: 1), _calculateFPS);
  }

  void _onFrame(Duration timestamp) {
    _frameCount++;
    SchedulerBinding.instance.addPostFrameCallback(_onFrame);
  }

  void _calculateFPS(Timer timer) {
    final now = DateTime.now();
    final elapsed = now.difference(_lastTime).inMilliseconds;
    
    if (elapsed > 0) {
      _currentFPS = (_frameCount * 1000.0) / elapsed;
      _frameCount = 0;
      _lastTime = now;
      
      // 如果帧率过低，触发优化
      if (_currentFPS < 30) {
        _triggerPerformanceOptimization();
      }
    }
  }

  void _triggerPerformanceOptimization() {
    // 检测到低帧率，触发性能优化
    // 触发内存清理和渲染优化
    AdvancedPerformanceOptimizer()._memoryManager.performEmergencyCleanup();
    AdvancedPerformanceOptimizer()._renderOptimizer.enableHighPerformanceMode();
  }

  void stop() {
    _timer?.cancel();
  }
}

/// 智能重绘管理器
class SmartRepaintManager {
  final Map<String, Timer?> _repaintTimers = {};
  final Map<String, VoidCallback?> _pendingRepaints = {};
  final Map<String, int> _repaintPriority = {};
  int _repaintCount = 0;

  int get repaintCount => _repaintCount;

  void initialize() {
    // 设置重绘优先级
    _repaintPriority['chart'] = 1;      // 最高优先级
    _repaintPriority['cross'] = 2;      // 中等优先级
    _repaintPriority['grid'] = 3;       // 低优先级
    _repaintPriority['draw'] = 2;       // 中等优先级
  }

  /// 智能调度重绘
  void scheduleRepaint(String layerKey, VoidCallback repaintCallback) {
    // 取消之前的重绘计时器
    _repaintTimers[layerKey]?.cancel();
    _pendingRepaints[layerKey] = repaintCallback;

    // 根据优先级设置不同的延迟
    final priority = _repaintPriority[layerKey] ?? 3;
    final delay = Duration(milliseconds: priority * 16); // 16ms = 1帧

    _repaintTimers[layerKey] = Timer(delay, () {
      _executePendingRepaints();
    });
  }

  /// 执行待处理的重绘
  void _executePendingRepaints() {
    // 按优先级排序执行
    final sortedKeys = _pendingRepaints.keys.toList()
      ..sort((a, b) => (_repaintPriority[a] ?? 3).compareTo(_repaintPriority[b] ?? 3));

    for (final key in sortedKeys) {
      final callback = _pendingRepaints[key];
      if (callback != null) {
        callback();
        _repaintCount++;
        _pendingRepaints[key] = null;
      }
    }
  }

  void dispose() {
    for (final timer in _repaintTimers.values) {
      timer?.cancel();
    }
    _repaintTimers.clear();
    _pendingRepaints.clear();
  }
}

/// 数据处理优化器
class DataProcessingOptimizer {
  final Queue<dynamic> _dataQueue = Queue();
  Timer? _processingTimer;

  static const int _maxQueueSize = 50;
  static const Duration _processingInterval = Duration(milliseconds: 33); // 30 FPS

  void initialize() {
    _startProcessingTimer();
  }

  void _startProcessingTimer() {
    _processingTimer = Timer.periodic(_processingInterval, (_) {
      _processBatchData();
    });
  }

  /// 处理数据
  void processData(dynamic data, Function(dynamic) processor) {
    if (_dataQueue.length >= _maxQueueSize) {
      _dataQueue.removeFirst(); // 移除最旧的数据
    }
    
    _dataQueue.add({
      'data': data,
      'processor': processor,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  /// 批量处理数据
  void _processBatchData() {
    if (_dataQueue.isEmpty) return;

    final batchSize = _dataQueue.length.clamp(1, 10);
    final batch = <Map<String, dynamic>>[];
    
    for (int i = 0; i < batchSize; i++) {
      if (_dataQueue.isNotEmpty) {
        batch.add(_dataQueue.removeFirst());
      }
    }

    // 在下一帧处理数据，避免阻塞当前帧
    SchedulerBinding.instance.addPostFrameCallback((_) {
      for (final item in batch) {
        try {
          final processor = item['processor'] as Function(dynamic);
          processor(item['data']);
        } catch (e) {
          // 忽略数据处理错误，继续处理其他数据
        }
      }
    });
  }

  void dispose() {
    _processingTimer?.cancel();
    _dataQueue.clear();
  }
}

/// 内存管理器
class MemoryManager {
  Timer? _cleanupTimer;
  final Map<String, WeakReference<Object>> _objectCache = {};
  final List<Function()> _cleanupTasks = [];

  static const Duration _cleanupInterval = Duration(minutes: 2);
  static const int _maxCacheSize = 100;

  void initialize() {
    _startCleanupTimer();
  }

  void _startCleanupTimer() {
    _cleanupTimer = Timer.periodic(_cleanupInterval, (_) {
      performCleanup();
    });
  }

  /// 注册清理任务
  void registerCleanupTask(Function() task) {
    _cleanupTasks.add(task);
  }

  /// 执行常规清理
  void performCleanup() {
    _cleanObjectCache();
    _executeCleanupTasks();

    // 内存清理完成
  }

  /// 执行紧急清理（低帧率时触发）
  void performEmergencyCleanup() {
    performCleanup();

    // 强制垃圾回收（仅在调试模式下）
    // 执行紧急内存清理
  }

  /// 清理对象缓存
  void _cleanObjectCache() {
    final keysToRemove = <String>[];

    for (final entry in _objectCache.entries) {
      if (entry.value.target == null) {
        keysToRemove.add(entry.key);
      }
    }

    for (final key in keysToRemove) {
      _objectCache.remove(key);
    }

    // 如果缓存过大，移除最旧的条目
    if (_objectCache.length > _maxCacheSize) {
      final keysToRemove = _objectCache.keys.take(_objectCache.length - _maxCacheSize).toList();
      for (final key in keysToRemove) {
        _objectCache.remove(key);
      }
    }
  }

  /// 执行清理任务
  void _executeCleanupTasks() {
    for (final task in _cleanupTasks) {
      try {
        task();
      } catch (e) {
        // 忽略清理任务执行失败，继续执行其他任务
      }
    }
  }

  /// 获取内存使用情况
  Map<String, dynamic> getMemoryUsage() {
    return {
      'cacheSize': _objectCache.length,
      'cleanupTasks': _cleanupTasks.length,
      'maxCacheSize': _maxCacheSize,
    };
  }

  void dispose() {
    _cleanupTimer?.cancel();
    _objectCache.clear();
    _cleanupTasks.clear();
  }
}

/// 渲染优化器
class RenderOptimizer {
  bool _highPerformanceMode = false;
  final List<double> _renderTimes = [];
  DateTime? _lastRenderStart;

  static const int _maxRenderTimesSamples = 30;

  double get averageRenderTime {
    if (_renderTimes.isEmpty) return 0.0;
    return _renderTimes.reduce((a, b) => a + b) / _renderTimes.length;
  }

  void initialize() {
    // 初始化渲染优化器
  }

  /// 开始渲染计时
  void startRenderTiming() {
    _lastRenderStart = DateTime.now();
  }

  /// 结束渲染计时
  void endRenderTiming() {
    if (_lastRenderStart != null) {
      final renderTime = DateTime.now().difference(_lastRenderStart!).inMicroseconds / 1000.0;
      _addRenderTime(renderTime);
    }
  }

  /// 添加渲染时间样本
  void _addRenderTime(double time) {
    _renderTimes.add(time);

    if (_renderTimes.length > _maxRenderTimesSamples) {
      _renderTimes.removeAt(0);
    }

    // 如果平均渲染时间过长，启用高性能模式
    if (averageRenderTime > 16.0 && !_highPerformanceMode) {
      enableHighPerformanceMode();
    }
  }

  /// 启用高性能模式
  void enableHighPerformanceMode() {
    _highPerformanceMode = true;

    // 启用高性能模式
  }

  /// 禁用高性能模式
  void disableHighPerformanceMode() {
    _highPerformanceMode = false;

    // 禁用高性能模式
  }

  /// 是否处于高性能模式
  bool get isHighPerformanceMode => _highPerformanceMode;

  /// 获取渲染优化建议
  Map<String, dynamic> getOptimizationSuggestions() {
    final suggestions = <String>[];

    if (averageRenderTime > 16.0) {
      suggestions.add('减少绘制复杂度');
    }

    if (averageRenderTime > 33.0) {
      suggestions.add('启用数据采样');
      suggestions.add('减少指标数量');
    }

    return {
      'averageRenderTime': averageRenderTime,
      'highPerformanceMode': _highPerformanceMode,
      'suggestions': suggestions,
    };
  }

  void dispose() {
    _renderTimes.clear();
  }
}
