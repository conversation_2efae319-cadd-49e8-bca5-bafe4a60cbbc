import 'dart:async';
import 'package:flutter/material.dart';

/// 手势防抖优化器 - 优化拖动和缩放过程中的重绘性能
class GestureDebounceOptimizer {
  static final GestureDebounceOptimizer _instance = GestureDebounceOptimizer._internal();
  factory GestureDebounceOptimizer() => _instance;
  GestureDebounceOptimizer._internal();

  // 防抖计时器
  Timer? _gestureDebounceTimer;
  Timer? _scaleDebounceTimer;
  Timer? _panDebounceTimer;
  
  // 手势状态
  bool _isGestureActive = false;
  bool _isPanActive = false;
  bool _isScaleActive = false;
  
  // 待执行的回调
  VoidCallback? _pendingIndicatorRepaint;
  VoidCallback? _pendingCandleRepaint;
  
  // 防抖延迟配置
  static const Duration _panDebounceDelay = Duration(milliseconds: 80);
  static const Duration _scaleDebounceDelay = Duration(milliseconds: 120);
  
  // 性能统计
  int _gestureCount = 0;
  int _debouncedGestureCount = 0;
  int _immediateRepaintCount = 0;

  /// 开始手势操作
  void startGesture() {
    _isGestureActive = true;
    _gestureCount++;
    
    // 手势开始，开始性能统计
  }

  /// 结束手势操作
  void endGesture() {
    _isGestureActive = false;
    _isPanActive = false;
    _isScaleActive = false;
    
    // 立即执行待处理的重绘
    _executeImmediateRepaint();
    
    // 手势结束，执行最终重绘
  }

  /// 开始平移操作
  void startPan() {
    _isPanActive = true;
    startGesture();
  }

  /// 平移过程中的重绘请求
  void requestPanRepaint({
    required VoidCallback candleRepaint,
    required VoidCallback indicatorRepaint,
  }) {
    // K线实时绘制
    candleRepaint();
    _immediateRepaintCount++;
    
    // 指标防抖绘制
    _pendingIndicatorRepaint = indicatorRepaint;
    _debouncePanRepaint();
  }

  /// 开始缩放操作
  void startScale() {
    _isScaleActive = true;
    startGesture();
  }

  /// 缩放过程中的重绘请求
  void requestScaleRepaint({
    required VoidCallback candleRepaint,
    required VoidCallback indicatorRepaint,
  }) {
    // K线实时绘制
    candleRepaint();
    _immediateRepaintCount++;
    
    // 指标防抖绘制
    _pendingIndicatorRepaint = indicatorRepaint;
    _debounceScaleRepaint();
  }

  /// 通用手势重绘请求
  void requestGestureRepaint({
    required VoidCallback candleRepaint,
    required VoidCallback indicatorRepaint,
  }) {
    if (_isPanActive) {
      requestPanRepaint(
        candleRepaint: candleRepaint,
        indicatorRepaint: indicatorRepaint,
      );
    } else if (_isScaleActive) {
      requestScaleRepaint(
        candleRepaint: candleRepaint,
        indicatorRepaint: indicatorRepaint,
      );
    } else {
      // 非手势状态，立即绘制
      candleRepaint();
      indicatorRepaint();
      _immediateRepaintCount++;
    }
  }

  /// 平移防抖重绘
  void _debouncePanRepaint() {
    _panDebounceTimer?.cancel();
    _panDebounceTimer = Timer(_panDebounceDelay, () {
      if (_pendingIndicatorRepaint != null && _isPanActive) {
        _pendingIndicatorRepaint!();
        _debouncedGestureCount++;
        _pendingIndicatorRepaint = null;
      }
    });
  }

  /// 缩放防抖重绘
  void _debounceScaleRepaint() {
    _scaleDebounceTimer?.cancel();
    _scaleDebounceTimer = Timer(_scaleDebounceDelay, () {
      if (_pendingIndicatorRepaint != null && _isScaleActive) {
        _pendingIndicatorRepaint!();
        _debouncedGestureCount++;
        _pendingIndicatorRepaint = null;
      }
    });
  }



  /// 立即执行重绘（手势结束时）
  void _executeImmediateRepaint() {
    // 取消所有防抖计时器
    _gestureDebounceTimer?.cancel();
    _panDebounceTimer?.cancel();
    _scaleDebounceTimer?.cancel();
    
    // 立即执行待处理的重绘
    if (_pendingIndicatorRepaint != null) {
      _pendingIndicatorRepaint!();
      _pendingIndicatorRepaint = null;
      _immediateRepaintCount++;
    }
    
    if (_pendingCandleRepaint != null) {
      _pendingCandleRepaint!();
      _pendingCandleRepaint = null;
      _immediateRepaintCount++;
    }
  }

  /// 强制立即重绘所有内容
  void forceImmediateRepaint({
    required VoidCallback candleRepaint,
    required VoidCallback indicatorRepaint,
  }) {
    // 取消所有防抖
    _gestureDebounceTimer?.cancel();
    _panDebounceTimer?.cancel();
    _scaleDebounceTimer?.cancel();
    
    // 立即执行
    candleRepaint();
    indicatorRepaint();
    _immediateRepaintCount++;
    
    // 清空待处理的回调
    _pendingIndicatorRepaint = null;
    _pendingCandleRepaint = null;
  }

  /// 检查是否在手势操作中
  bool get isGestureActive => _isGestureActive;
  bool get isPanActive => _isPanActive;
  bool get isScaleActive => _isScaleActive;

  /// 获取性能统计
  Map<String, dynamic> getPerformanceStats() {
    final totalGestures = _gestureCount;
    final optimizationRate = totalGestures > 0 
        ? ((_debouncedGestureCount / totalGestures) * 100).toStringAsFixed(1)
        : '0.0';
    
    return {
      'totalGestures': totalGestures,
      'debouncedGestures': _debouncedGestureCount,
      'immediateRepaints': _immediateRepaintCount,
      'optimizationRate': '$optimizationRate%',
      'isGestureActive': _isGestureActive,
      'isPanActive': _isPanActive,
      'isScaleActive': _isScaleActive,
      'hasPendingRepaint': _pendingIndicatorRepaint != null,
    };
  }

  /// 重置统计信息
  void resetStats() {
    _gestureCount = 0;
    _debouncedGestureCount = 0;
    _immediateRepaintCount = 0;
  }

  /// 释放资源
  void dispose() {
    _gestureDebounceTimer?.cancel();
    _panDebounceTimer?.cancel();
    _scaleDebounceTimer?.cancel();
    
    _pendingIndicatorRepaint = null;
    _pendingCandleRepaint = null;
    
    _isGestureActive = false;
    _isPanActive = false;
    _isScaleActive = false;
  }
}

/// 优化的重绘管理器
class OptimizedRepaintManager {
  final GestureDebounceOptimizer _gestureOptimizer = GestureDebounceOptimizer();
  
  // 分层重绘回调
  VoidCallback? _candleRepaintCallback;
  VoidCallback? _indicatorRepaintCallback;
  VoidCallback? _drawRepaintCallback;
  
  /// 设置重绘回调
  void setRepaintCallbacks({
    required VoidCallback candleRepaint,
    required VoidCallback indicatorRepaint,
    required VoidCallback drawRepaint,
  }) {
    _candleRepaintCallback = candleRepaint;
    _indicatorRepaintCallback = indicatorRepaint;
    _drawRepaintCallback = drawRepaint;
  }

  /// 请求图表重绘
  void requestChartRepaint({bool isGesture = false}) {
    if (_candleRepaintCallback == null || _indicatorRepaintCallback == null) {
      return;
    }

    if (isGesture && _gestureOptimizer.isGestureActive) {
      // 手势过程中使用防抖
      _gestureOptimizer.requestGestureRepaint(
        candleRepaint: _candleRepaintCallback!,
        indicatorRepaint: _indicatorRepaintCallback!,
      );
    } else {
      // 非手势状态立即重绘
      _candleRepaintCallback!();
      _indicatorRepaintCallback!();
    }
  }

  /// 请求绘图层重绘
  void requestDrawRepaint() {
    _drawRepaintCallback?.call();
  }

  /// 开始手势操作
  void startGesture() => _gestureOptimizer.startGesture();
  
  /// 结束手势操作
  void endGesture() => _gestureOptimizer.endGesture();
  
  /// 开始平移
  void startPan() => _gestureOptimizer.startPan();
  
  /// 开始缩放
  void startScale() => _gestureOptimizer.startScale();

  /// 强制立即重绘
  void forceImmediateRepaint() {
    if (_candleRepaintCallback != null && _indicatorRepaintCallback != null) {
      _gestureOptimizer.forceImmediateRepaint(
        candleRepaint: _candleRepaintCallback!,
        indicatorRepaint: _indicatorRepaintCallback!,
      );
    }
  }

  /// 获取性能统计
  Map<String, dynamic> getStats() => _gestureOptimizer.getPerformanceStats();

  /// 释放资源
  void dispose() => _gestureOptimizer.dispose();
}
