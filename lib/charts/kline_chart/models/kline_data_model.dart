import 'dart:math' as math;
import 'package:decimal/decimal.dart';
import '../../kline/flexi_kline.dart';

/// K线数据模型生成器
class KlineDataGenerator {
  final math.Random _random = math.Random();

  /// 生成模拟K线数据
  List<CandleModel> generateMockData() {
    final List<CandleModel> candles = [];

    final List<double> basePrices = [
      87650.0,
      87700.0,
      87600.0,
      87800.0,
      87750.0
    ];

    final int latestCandleIndex = basePrices.length - 1;
    final DateTime baseDate = DateTime(2025, 4, 1, 10, 0, 0);

    for (int i = 0; i < basePrices.length; i++) {
      double currentBasePrice = basePrices[i];
      
      double open = currentBasePrice - _random.nextDouble() * 50;
      double close = currentBasePrice + _random.nextDouble() * 50;
      double high = math.max(open, close) + _random.nextDouble() * 80;
      double low = math.min(open, close) - _random.nextDouble() * 80;
      if (low > open) low = open - 10;
      if (low > close) low = close - 10;
      if (high < open) high = open + 10;
      if (high < close) high = close + 10;

      final timestamp =
          baseDate
              .add(Duration(hours: i - latestCandleIndex))
              .millisecondsSinceEpoch;

      // 生成买卖成交区间数据 (使用 PriceLevelEntry)
      List<PriceLevelEntry> sellLevelEntries = [];
      List<PriceLevelEntry> buyLevelEntries = [];
      double currentTotalSellVolume = 0;
      double currentTotalBuyVolume = 0;

      // 卖单区间
      if (i % 2 == 0 || i == latestCandleIndex) { // 每隔一条K线加卖单数据，最后一条总是有
        double vol1 = (_random.nextInt(50) + 20).toDouble();
        sellLevelEntries.add(PriceLevelEntry(
            priceMin: double.parse((low + (high - low) * 0.6).toStringAsFixed(2)),
            priceMax: double.parse((low + (high - low) * 0.75).toStringAsFixed(2)),
            volume: vol1,
        ));
        currentTotalSellVolume += vol1;
        if (i == 0 || i == latestCandleIndex) { // 第一条和最后一条特殊多加一个
           double vol2 = (_random.nextInt(70) + 30).toDouble();
           sellLevelEntries.add(PriceLevelEntry(
              priceMin: double.parse((low + (high - low) * 0.8).toStringAsFixed(2)),
              priceMax: double.parse((low + (high - low) * 0.95).toStringAsFixed(2)),
              volume: vol2,
          ));
          currentTotalSellVolume += vol2;
        }
      }

      // 买单区间
      if (i % 2 != 0 || i == latestCandleIndex) { // 每隔一条K线加买单数据，最后一条总是有
        double vol1 = (_random.nextInt(60) + 25).toDouble();
        buyLevelEntries.add(PriceLevelEntry(
            priceMin: double.parse((low + (high - low) * 0.25).toStringAsFixed(2)),
            priceMax: double.parse((low + (high - low) * 0.4).toStringAsFixed(2)),
            volume: vol1,
        ));
        currentTotalBuyVolume += vol1;
         if (i == 1 || i == latestCandleIndex) { // 第二条和最后一条特殊多加一个
           double vol2 = (_random.nextInt(80) + 35).toDouble();
           buyLevelEntries.add(PriceLevelEntry(
              priceMin: double.parse((low + (high - low) * 0.05).toStringAsFixed(2)),
              priceMax: double.parse((low + (high - low) * 0.2).toStringAsFixed(2)),
              volume: vol2,
          ));
          currentTotalBuyVolume += vol2;
        }
      }

      PriceLevelDetail? priceLevelData;
      if (sellLevelEntries.isNotEmpty || buyLevelEntries.isNotEmpty) {
        priceLevelData = PriceLevelDetail(
          sellLevels: sellLevelEntries.isNotEmpty ? sellLevelEntries : null, 
          buyLevels: buyLevelEntries.isNotEmpty ? buyLevelEntries : null,
          totalSellVolume: currentTotalSellVolume > 0 ? currentTotalSellVolume : null,
          totalBuyVolume: currentTotalBuyVolume > 0 ? currentTotalBuyVolume : null,
        );
      }

      // print("Candle $i (ts: $timestamp) priceLevelData: ${priceLevelData}");

      candles.add(
        CandleModel(
          ts: timestamp, 
          o: Decimal.parse(open.toStringAsFixed(2)), 
          h: Decimal.parse(high.toStringAsFixed(2)), 
          l: Decimal.parse(low.toStringAsFixed(2)), 
          c: Decimal.parse(close.toStringAsFixed(2)), 
          v: Decimal.parse((_random.nextInt(400) + 100).toString()),
          confirm: (i == latestCandleIndex) ? '1' : '0', 
          priceLevel: priceLevelData,
        ),
      );
    }

    candles.sort((a, b) => b.ts.compareTo(a.ts));
    return candles;
  }
}
