/// Volume Profile 数据模型类
class VolumeProfileData {
  final bool success;
  final String message;
  final String timestamp;
  final String symbol;
  final String startTime;
  final String endTime;
  final List<String> exchanges;
  final int totalRecords;
  final int totalPages;
  final Pagination pagination;
  final List<AggregatedData> aggregatedData;

  VolumeProfileData({
    required this.success,
    required this.message,
    required this.timestamp,
    required this.symbol,
    required this.startTime,
    required this.endTime,
    required this.exchanges,
    required this.totalRecords,
    required this.totalPages,
    required this.pagination,
    required this.aggregatedData,
  });

  factory VolumeProfileData.fromJson(Map<String, dynamic> json) {
    return VolumeProfileData(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      timestamp: json['timestamp'] ?? '',
      symbol: json['symbol'] ?? '',
      startTime: json['start_time'] ?? '',
      endTime: json['end_time'] ?? '',
      exchanges: List<String>.from(json['exchanges'] ?? []),
      totalRecords: json['total_records'] ?? 0,
      totalPages: json['total_pages'] ?? 0,
      pagination: Pagination.fromJson(json['pagination'] ?? {}),
      aggregatedData: (json['aggregated_data'] as List<dynamic>?)
          ?.map((e) => AggregatedData.fromJson(e))
          .toList() ?? [],
    );
  }
}

/// 分页信息
class Pagination {
  final int page;
  final int pageSize;
  final int totalRecords;
  final int totalPages;

  Pagination({
    required this.page,
    required this.pageSize,
    required this.totalRecords,
    required this.totalPages,
  });

  factory Pagination.fromJson(Map<String, dynamic> json) {
    return Pagination(
      page: json['page'] ?? 1,
      pageSize: json['page_size'] ?? 100,
      totalRecords: json['total_records'] ?? 0,
      totalPages: json['total_pages'] ?? 0,
    );
  }
}

/// 聚合数据
class AggregatedData {
  final String timestamp;
  final String symbol;
  final List<String> exchanges;
  final List<PriceVolumeData> buyPriceDistribution;
  final List<PriceVolumeData> sellPriceDistribution;
  final List<dynamic> exchangeDetails;

  AggregatedData({
    required this.timestamp,
    required this.symbol,
    required this.exchanges,
    required this.buyPriceDistribution,
    required this.sellPriceDistribution,
    required this.exchangeDetails,
  });

  factory AggregatedData.fromJson(Map<String, dynamic> json) {
    return AggregatedData(
      timestamp: json['timestamp'] ?? '',
      symbol: json['symbol'] ?? '',
      exchanges: List<String>.from(json['exchanges'] ?? []),
      buyPriceDistribution: (json['buy_price_distribution'] as List<dynamic>?)
          ?.map((e) => PriceVolumeData.fromJson(e))
          .toList() ?? [],
      sellPriceDistribution: (json['sell_price_distribution'] as List<dynamic>?)
          ?.map((e) => PriceVolumeData.fromJson(e))
          .toList() ?? [],
      exchangeDetails: json['exchange_details'] ?? [],
    );
  }

  // 缓存计算结果以提高性能
  Map<double, double>? _cachedTotalVolumeByPrice;
  double? _cachedPOC;
  Map<String, double>? _cachedValueArea;
  double? _lastValueAreaPercentage;

  /// 获取所有价格级别的总成交量（买+卖），带缓存优化
  Map<double, double> getTotalVolumeByPrice() {
    if (_cachedTotalVolumeByPrice != null) {
      return _cachedTotalVolumeByPrice!;
    }

    Map<double, double> result = {};
    
    // 添加买方成交量
    for (var buyData in buyPriceDistribution) {
      result[buyData.price] = (result[buyData.price] ?? 0) + buyData.volume;
    }
    
    // 添加卖方成交量
    for (var sellData in sellPriceDistribution) {
      result[sellData.price] = (result[sellData.price] ?? 0) + sellData.volume;
    }
    
    _cachedTotalVolumeByPrice = result;
    return result;
  }

  /// 获取买方成交量分布
  Map<double, double> getBuyVolumeByPrice() {
    Map<double, double> result = {};
    for (var buyData in buyPriceDistribution) {
      result[buyData.price] = buyData.volume;
    }
    return result;
  }

  /// 获取卖方成交量分布
  Map<double, double> getSellVolumeByPrice() {
    Map<double, double> result = {};
    for (var sellData in sellPriceDistribution) {
      result[sellData.price] = sellData.volume;
    }
    return result;
  }

  /// 计算成交量加权平均价格 (VWAP)
  double calculateVWAP() {
    double totalVolume = 0;
    double totalValue = 0;
    
    final totalVolumeByPrice = getTotalVolumeByPrice();
    for (var entry in totalVolumeByPrice.entries) {
      final price = entry.key;
      final volume = entry.value;
      totalVolume += volume;
      totalValue += price * volume;
    }
    
    return totalVolume > 0 ? totalValue / totalVolume : 0;
  }

  /// 优化的POC计算 - 成交量最大的价格级别，带缓存
  double calculatePOC() {
    if (_cachedPOC != null) {
      return _cachedPOC!;
    }

    final totalVolumeByPrice = getTotalVolumeByPrice();
    if (totalVolumeByPrice.isEmpty) {
      _cachedPOC = 0;
      return 0;
    }
    
    double maxVolume = 0;
    double pocPrice = 0;
    
    // 遍历找到最大成交量的价格
    for (var entry in totalVolumeByPrice.entries) {
      if (entry.value > maxVolume) {
        maxVolume = entry.value;
        pocPrice = entry.key;
      }
    }
    
    _cachedPOC = pocPrice;
    return pocPrice;
  }

  /// 优化的Value Area计算 - 使用标准Volume Profile算法
  /// 返回 {'VAH': value, 'VAL': value, 'poc_volume': volume, 'value_area_volume': volume}
  Map<String, double> calculateValueArea({double valueAreaPercentage = 0.7}) {
    // 检查缓存
    if (_cachedValueArea != null && _lastValueAreaPercentage == valueAreaPercentage) {
      return _cachedValueArea!;
    }

    final totalVolumeByPrice = getTotalVolumeByPrice();
    if (totalVolumeByPrice.isEmpty) {
      final emptyResult = {'VAH': 0.0, 'VAL': 0.0, 'poc_volume': 0.0, 'value_area_volume': 0.0};
      _cachedValueArea = emptyResult;
      _lastValueAreaPercentage = valueAreaPercentage;
      return emptyResult;
    }
    
    // 验证输入参数
    if (valueAreaPercentage <= 0 || valueAreaPercentage > 1.0) {
      valueAreaPercentage = 0.7; // 默认70%
    }
    
    // 按价格排序并预处理数据
    final sortedEntries = totalVolumeByPrice.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));
    
    if (sortedEntries.length == 1) {
      // 只有一个价格点的特殊情况
      final singlePrice = sortedEntries.first.key;
      final result = {
        'VAH': singlePrice, 
        'VAL': singlePrice, 
        'poc_volume': sortedEntries.first.value,
        'value_area_volume': sortedEntries.first.value
      };
      _cachedValueArea = result;
      _lastValueAreaPercentage = valueAreaPercentage;
      return result;
    }
    
    final totalVolume = sortedEntries.fold(0.0, (sum, entry) => sum + entry.value);
    final targetVolume = totalVolume * valueAreaPercentage;
    
    // 获取POC价格和索引
    final pocPrice = calculatePOC();
    
    // 使用更精确的POC查找算法
    int pocIndex = _findPOCIndex(sortedEntries, pocPrice);
    
    if (pocIndex == -1) {
      // 容错：找到成交量最大的索引
      pocIndex = _findMaxVolumeIndex(sortedEntries);
    }
    
    // 标准Volume Profile算法：从POC向两边扩展
    final valueAreaResult = _expandValueArea(sortedEntries, pocIndex, targetVolume, totalVolume);
    
    final result = {
      'VAL': valueAreaResult['VAL']!,
      'VAH': valueAreaResult['VAH']!,
      'poc_volume': sortedEntries[pocIndex].value,
      'value_area_volume': valueAreaResult['accumulated_volume']!,
    };
    
    _cachedValueArea = result;
    _lastValueAreaPercentage = valueAreaPercentage;
    return result;
  }

  /// 精确查找POC索引
  int _findPOCIndex(List<MapEntry<double, double>> sortedEntries, double pocPrice) {
    const double tolerance = 1e-10; // 浮点数容差
    int bestIndex = -1;
    double minDiff = double.infinity;
    
    for (int i = 0; i < sortedEntries.length; i++) {
      final diff = (sortedEntries[i].key - pocPrice).abs();
      if (diff < tolerance) {
        return i; // 精确匹配
      }
      if (diff < minDiff) {
        minDiff = diff;
        bestIndex = i;
      }
    }
    
    return bestIndex;
  }

  /// 查找成交量最大的索引（容错方法）
  int _findMaxVolumeIndex(List<MapEntry<double, double>> sortedEntries) {
    if (sortedEntries.isEmpty) return -1;
    
    int maxIndex = 0;
    double maxVolume = sortedEntries[0].value;
    
    for (int i = 1; i < sortedEntries.length; i++) {
      if (sortedEntries[i].value > maxVolume) {
        maxVolume = sortedEntries[i].value;
        maxIndex = i;
      }
    }
    
    return maxIndex;
  }

  /// 标准Volume Profile Value Area扩展算法
  Map<String, double> _expandValueArea(
    List<MapEntry<double, double>> sortedEntries, 
    int pocIndex, 
    double targetVolume,
    double totalVolume
  ) {
    double accumulatedVolume = sortedEntries[pocIndex].value;
    int lowIndex = pocIndex;
    int highIndex = pocIndex;
    
    // 验证参数
    if (pocIndex < 0 || pocIndex >= sortedEntries.length) {
      return {
        'VAL': sortedEntries.first.key,
        'VAH': sortedEntries.last.key,
        'accumulated_volume': totalVolume,
      };
    }
    
    int iterations = 0;
    const maxIterations = 1000; // 防止无限循环
    
    // 标准Volume Profile算法：从POC开始，每次向成交量更大的一边扩展一个价格级别
    while (accumulatedVolume < targetVolume && (lowIndex > 0 || highIndex < sortedEntries.length - 1) && iterations < maxIterations) {
      iterations++;
      
      double leftVolume = 0;
      double rightVolume = 0;
      
      // 检查可扩展的方向和对应的成交量
      bool canExpandLeft = lowIndex > 0;
      bool canExpandRight = highIndex < sortedEntries.length - 1;
      
      if (canExpandLeft) {
        leftVolume = sortedEntries[lowIndex - 1].value;
      }
      
      if (canExpandRight) {
        rightVolume = sortedEntries[highIndex + 1].value;
      }
      
      // 标准扩展逻辑：优先选择成交量更大的方向
      bool expandLeft = false;
      bool expandRight = false;
      
      if (canExpandLeft && canExpandRight) {
        // 两边都可以扩展：选择成交量更大的一边
        if (leftVolume > rightVolume) {
          expandLeft = true;
        } else if (rightVolume > leftVolume) {
          expandRight = true;
        } else {
          // 成交量相等时：优先向下扩展（传统Volume Profile行为）
          expandLeft = true;
        }
      } else if (canExpandLeft) {
        // 只能向左（向下）扩展
        expandLeft = true;
      } else if (canExpandRight) {
        // 只能向右（向上）扩展
        expandRight = true;
      } else {
        // 无法继续扩展
        break;
      }
      
      // 执行扩展
      if (expandLeft) {
        lowIndex--;
        accumulatedVolume += sortedEntries[lowIndex].value;
      }
      
      if (expandRight) {
        highIndex++;
        accumulatedVolume += sortedEntries[highIndex].value;
      }
      
      // 检查是否已达到目标成交量
      if (accumulatedVolume >= targetVolume) {
        break;
      }
    }
    
    final result = {
      'VAL': sortedEntries[lowIndex].key,   // 价值区域下边界
      'VAH': sortedEntries[highIndex].key,  // 价值区域上边界
      'accumulated_volume': accumulatedVolume,
    };
    
    return result;
  }

  /// 获取Value Area统计信息
  Map<String, dynamic> getValueAreaStats({double valueAreaPercentage = 0.7}) {
    final valueArea = calculateValueArea(valueAreaPercentage: valueAreaPercentage);
    final totalVolumeByPrice = getTotalVolumeByPrice();
    final totalVolume = totalVolumeByPrice.values.fold(0.0, (sum, vol) => sum + vol);
    
    // 安全计算价格范围
    double priceRange = 0.0;
    if (totalVolumeByPrice.isNotEmpty) {
      final prices = totalVolumeByPrice.keys.toList();
      final maxPrice = prices.reduce((a, b) => a > b ? a : b);
      final minPrice = prices.reduce((a, b) => a < b ? a : b);
      priceRange = maxPrice - minPrice;
    }
    
    return {
      'poc': calculatePOC(),
      'val': valueArea['VAL'],
      'vah': valueArea['VAH'],
      'poc_volume': valueArea['poc_volume'],
      'value_area_volume': valueArea['value_area_volume'],
      'value_area_percentage': totalVolume > 0 ? (valueArea['value_area_volume']! / totalVolume) : 0.0,
      'total_volume': totalVolume,
      'price_range': priceRange,
      'volume_weighted_midpoint': calculateVWAP(),
    };
  }

  /// Debug方法：验证Value Area计算的正确性
  Map<String, dynamic> debugValueAreaCalculation({double valueAreaPercentage = 0.7}) {
    final totalVolumeByPrice = getTotalVolumeByPrice();
    if (totalVolumeByPrice.isEmpty) return {};
    
    // 按价格排序
    final sortedEntries = totalVolumeByPrice.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));
    
    final totalVolume = sortedEntries.fold(0.0, (sum, entry) => sum + entry.value);
    final targetVolume = totalVolume * valueAreaPercentage;
    final pocPrice = calculatePOC();
    
    // 查找POC索引
    int pocIndex = _findPOCIndex(sortedEntries, pocPrice);
    if (pocIndex == -1) pocIndex = _findMaxVolumeIndex(sortedEntries);
    
    // 获取价格范围
    final minPrice = sortedEntries.first.key;
    final maxPrice = sortedEntries.last.key;
    
    // 计算Value Area
    final valueArea = calculateValueArea(valueAreaPercentage: valueAreaPercentage);
    
    return {
      'total_volume': totalVolume,
      'target_volume': targetVolume,
      'poc_price': pocPrice,
      'poc_index': pocIndex,
      'price_range': {'min': minPrice, 'max': maxPrice},
      'value_area': valueArea,
      'vah_position': valueArea['VAH'] == maxPrice ? 'AT_MAX_PRICE' : 'WITHIN_RANGE',
      'val_position': valueArea['VAL'] == minPrice ? 'AT_MIN_PRICE' : 'WITHIN_RANGE',
      'is_vah_problem': valueArea['VAH'] == maxPrice && valueArea['VAL'] != minPrice,
      'price_levels_count': sortedEntries.length,
    };
  }
}

/// 价格成交量数据
class PriceVolumeData {
  final double price;
  final double volume;

  PriceVolumeData({
    required this.price,
    required this.volume,
  });

  factory PriceVolumeData.fromJson(Map<String, dynamic> json) {
    return PriceVolumeData(
      price: (json['price'] ?? 0).toDouble(),
      volume: (json['volume'] ?? 0).toDouble(),
    );
  }
} 