import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/kline_data_model.dart';
import '../../kline/model/candle_model/candle_model.dart';
import 'package:decimal/decimal.dart';

/// K线数据缓存器
/// 负责K线数据的本地持久化存储、增量更新和数据合并
class KlineDataCache {
  static const int maxCacheLength = 1500; // 最大缓存长度
  static const String _cacheKeyPrefix = 'kline_cache_';
  static const String _lastTimeKeyPrefix = 'kline_last_time_';
  
  // 记录每个币种和周期的最后一根K线开盘时间
  final Map<String, int> _lastOpenTimeMap = {};
  
  /// 获取缓存数据
  /// [instrumentId] 币种ID (如 BTCUSDT)
  /// [period] 时间周期 (如 1m, 5m, 1h)
  /// 返回: Map包含 'candles' 和 'lastTime'
  Future<Map<String, dynamic>?> getCachedData(String instrumentId, String period) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _getCacheKey(instrumentId, period);
      final lastTimeKey = _getLastTimeKey(instrumentId, period);
      
      final cachedDataJson = prefs.getString(cacheKey);
      final lastTime = prefs.getInt(lastTimeKey);
      
      if (cachedDataJson == null) {
        return null;
      }
      
      final List<dynamic> candleDataList = json.decode(cachedDataJson);
      final List<CandleModel> candles = candleDataList.map((data) => _jsonToCandle(data)).toList();
      
      return {
        'candles': candles,
        'lastTime': lastTime,
      };
    } catch (e) {
      if (kDebugMode) {
        print('[KlineDataCache] 读取缓存数据失败: $e');
      }
      return null;
    }
  }
  
  /// 获取最后缓存的收盘时间
  /// 用于API请求的起始时间参数
  Future<int?> getLastCloseTime(String instrumentId, String period) async {
    try {
      final cachedData = await getCachedData(instrumentId, period);
      if (cachedData == null) return null;
      
      final List<CandleModel> candles = cachedData['candles'] as List<CandleModel>;
      if (candles.isEmpty) return null;
      
      // 找到最新的K线时间戳
      candles.sort((a, b) => b.ts.compareTo(a.ts));
      final lastTime = candles.first.ts;
      
      return lastTime;
    } catch (e) {
      if (kDebugMode) {
        print('[KlineDataCache] 获取最后收盘时间失败: $e');
      }
      return null;
    }
  }
  
  /// 合并增量数据
  /// [cachedCandles] 本地缓存的K线数据
  /// [newCandles] 从API获取的新K线数据
  /// [maxLength] 最大保留长度，默认1500
  /// 返回: 合并后的K线数据列表
  List<CandleModel> mergeIncrementalData(
    List<CandleModel> cachedCandles,
    List<CandleModel> newCandles, {
    int maxLength = maxCacheLength,
  }) {
    if (newCandles.isEmpty) {
      return cachedCandles;
    }
    
    // 如果新数据长度大于等于1500，直接使用新数据
    if (newCandles.length >= maxLength) {
      return _limitLength(newCandles, maxLength);
    }
    
    // 合并数据：缓存数据 + 增量数据
    final Map<int, CandleModel> mergedMap = {};
    
    // 先加入缓存数据
    for (final candle in cachedCandles) {
      mergedMap[candle.ts] = candle;
    }
    
    // 再加入新数据（会覆盖相同时间戳的旧数据）
    for (final candle in newCandles) {
      mergedMap[candle.ts] = candle;
    }
    
    // 转换为列表并按时间排序（最新的在前）
    final List<CandleModel> mergedList = mergedMap.values.toList();
    mergedList.sort((a, b) => b.ts.compareTo(a.ts));
    
    // 限制最大长度
    final result = _limitLength(mergedList, maxLength);
    
    return result;
  }
  
  /// 保存K线数据到缓存
  /// [instrumentId] 币种ID
  /// [period] 时间周期
  /// [candles] K线数据列表
  /// [updateLastTime] 是否更新最后时间戳
  Future<void> saveCachedData(
    String instrumentId,
    String period,
    List<CandleModel> candles, {
    bool updateLastTime = true,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _getCacheKey(instrumentId, period);
      final lastTimeKey = _getLastTimeKey(instrumentId, period);
      
      // 限制缓存长度
      final limitedCandles = _limitLength(candles, maxCacheLength);
      
      // 转换为JSON
      final candleDataList = limitedCandles.map((candle) => _candleToJson(candle)).toList();
      final candleDataJson = json.encode(candleDataList);
      
      // 保存数据
      await prefs.setString(cacheKey, candleDataJson);
      
      // 更新最后时间戳
      if (updateLastTime && limitedCandles.isNotEmpty) {
        // 按时间排序，取最新的时间戳
        limitedCandles.sort((a, b) => b.ts.compareTo(a.ts));
        await prefs.setInt(lastTimeKey, limitedCandles.first.ts);
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('[KlineDataCache] 保存缓存数据失败: $e');
      }
    }
  }
  
  /// 实时更新单条K线数据
  /// 用于WebSocket实时数据更新
  Future<void> updateSingleCandle(
    String instrumentId,
    String period,
    CandleModel updatedCandle,
  ) async {
    try {
      final cachedData = await getCachedData(instrumentId, period);
      if (cachedData == null) {
        // 如果没有缓存数据，创建新的缓存
        await saveCachedData(instrumentId, period, [updatedCandle]);
        return;
      }
      
      final List<CandleModel> candles = List.from(cachedData['candles'] as List<CandleModel>);
      
      // 查找是否存在相同时间戳的K线
      final existingIndex = candles.indexWhere((candle) => candle.ts == updatedCandle.ts);
      
      if (existingIndex >= 0) {
        // 更新现有K线
        candles[existingIndex] = updatedCandle;
      } else {
        // 添加新K线
        candles.insert(0, updatedCandle); // 插入到最前面（最新）
      }
      
      // 保存更新后的数据
      await saveCachedData(instrumentId, period, candles);
    } catch (e) {
      if (kDebugMode) {
        print('[KlineDataCache] 实时更新K线失败: $e');
      }
    }
  }
  
  /// 清除指定币种和周期的缓存
  Future<void> clearCache(String instrumentId, String period) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _getCacheKey(instrumentId, period);
      final lastTimeKey = _getLastTimeKey(instrumentId, period);
      
      await prefs.remove(cacheKey);
      await prefs.remove(lastTimeKey);
      
    } catch (e) {
      if (kDebugMode) {
        print('[KlineDataCache] 清除缓存失败: $e');
      }
    }
  }
  
  /// 清除所有缓存
  Future<void> clearAllCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      final cacheKeys = keys.where(
        (key) => key.startsWith(_cacheKeyPrefix) || key.startsWith(_lastTimeKeyPrefix)
      ).toList();
      
      for (final key in cacheKeys) {
        await prefs.remove(key);
      }
    } catch (e) {
      if (kDebugMode) {
        print('[KlineDataCache] 清除所有缓存失败: $e');
      }
    }
  }
  
  /// 获取缓存键名
  String _getCacheKey(String instrumentId, String period) {
    return '$_cacheKeyPrefix${instrumentId}_$period';
  }
  
  /// 获取最后时间键名
  String _getLastTimeKey(String instrumentId, String period) {
    return '$_lastTimeKeyPrefix${instrumentId}_$period';
  }
  
  /// 限制数据长度
  List<CandleModel> _limitLength(List<CandleModel> candles, int maxLength) {
    if (candles.length <= maxLength) return candles;
    
    // 按时间排序，保留最新的数据
    candles.sort((a, b) => b.ts.compareTo(a.ts));
    return candles.sublist(0, maxLength);
  }
  
  /// CandleModel转换为JSON
  Map<String, dynamic> _candleToJson(CandleModel candle) {
    return {
      'ts': candle.ts,
      'o': candle.o.toString(),
      'h': candle.h.toString(),
      'l': candle.l.toString(),
      'c': candle.c.toString(),
      'v': candle.v.toString(),
      'vc': candle.vc?.toString(),
      'vcq': candle.vcq?.toString(),
      'confirm': candle.confirm,
      // 注意：为了简化，这里不保存priceLevel信息
    };
  }
  
  /// JSON转换为CandleModel
  CandleModel _jsonToCandle(Map<String, dynamic> json) {
    return CandleModel(
      ts: json['ts'] as int,
      o: Decimal.parse(json['o'] as String),
      h: Decimal.parse(json['h'] as String),
      l: Decimal.parse(json['l'] as String),
      c: Decimal.parse(json['c'] as String),
      v: Decimal.parse(json['v'] as String),
      vc: json['vc'] != null ? Decimal.parse(json['vc'] as String) : null,
      vcq: json['vcq'] != null ? Decimal.parse(json['vcq'] as String) : null,
      confirm: json['confirm'] as String,
      // priceLevel不从缓存中恢复，因为这部分数据会在运行时重新生成
    );
  }

  /// 初始化缓存状态
  /// [instrumentId] 币种ID
  /// [period] 时间周期
  Future<void> initializeCacheState(String instrumentId, String period) async {
    try {
      final cachedData = await getCachedData(instrumentId, period);
      if (cachedData != null) {
        final List<CandleModel> candles = cachedData['candles'] as List<CandleModel>;
        if (candles.isNotEmpty) {
          // 按时间排序，获取最新的K线
          candles.sort((a, b) => b.ts.compareTo(a.ts));
          final latestCandle = candles.first;
          
          // 计算开盘时间（K线时间戳就是开盘时间）
          final openTime = latestCandle.ts;
          final cacheKey = _getCacheStateKey(instrumentId, period);
          _lastOpenTimeMap[cacheKey] = openTime;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[KlineDataCache] 初始化缓存状态失败: $e');
      }
    }
  }

  /// 智能更新单条K线数据
  /// 只有新开盘的K线才会执行缓存更新
  /// [instrumentId] 币种ID
  /// [period] 时间周期
  /// [updatedCandle] 更新的K线数据
  /// 返回: true表示执行了缓存更新，false表示跳过了更新
  Future<bool> smartUpdateSingleCandle(
    String instrumentId,
    String period,
    CandleModel updatedCandle,
  ) async {
    try {
      final cacheKey = _getCacheStateKey(instrumentId, period);
      final lastOpenTime = _lastOpenTimeMap[cacheKey];
      final currentOpenTime = updatedCandle.ts; // K线时间戳就是开盘时间
      
      // 判断是否是新的开盘K线
      if (lastOpenTime == null || currentOpenTime > lastOpenTime) {
        // 新开盘的K线，执行缓存更新
        await updateSingleCandle(instrumentId, period, updatedCandle);
        // 更新最后开盘时间记录
        _lastOpenTimeMap[cacheKey] = currentOpenTime;
        
        return true; // 执行了缓存更新
      } else {
        // 当前开盘K线的价格更新，跳过缓存
        return false; // 跳过了更新
      }
    } catch (e) {
      return false;
    }
  }

  /// 清除缓存状态
  /// [instrumentId] 币种ID
  /// [period] 时间周期
  void clearCacheState(String instrumentId, String period) {
    final cacheKey = _getCacheStateKey(instrumentId, period);
    _lastOpenTimeMap.remove(cacheKey);
  }

  /// 获取缓存状态键名
  String _getCacheStateKey(String instrumentId, String period) {
    return '${instrumentId}_$period';
  }
} 