import 'package:decimal/decimal.dart';
import 'package:flutter/foundation.dart';
import '../../kline/model/candle_model/candle_model.dart';
import '../../../services/clients/request_client.dart'; // 添加RequestClient引用
import '../managers/volume_profile_data_manager.dart'; // 添加Volume Profile数据管理器引用

/// K线数据管理器
/// 负责保存K线数据、计算ATR、处理成交数据、维护成交量轨迹
class KlineDataManager {
  // 每个实例的唯一标识
  final String _instanceId;
  final int? _windowIndex; // 新增：窗口索引

  // 构造函数
  KlineDataManager({String? instanceId, int? windowIndex})
    : _instanceId =
          instanceId ?? DateTime.now().millisecondsSinceEpoch.toString(),
      _windowIndex = windowIndex {
    if (kDebugMode) {
      print(
        '[KlineDataManager] 创建新实例: $_instanceId, windowIndex: $_windowIndex',
      );
    }
  }

  // 当前交易对和时间周期
  String _currentInstrumentId = '';
  String _currentPeriod = '';

  // 保存近30条K线数据
  final List<CandleModel> _recentCandles = [];

  // 当前ATR值
  double _currentAtr = 0.0;

  // 最近一根K线的时间信息
  int? _lastCandleTimestamp; // 最后一根K线开盘时间
  int? _lastCandleEndTimestamp; // 最后一根K线收盘时间
  bool _lastCandleIsComplete = false; // 最后一根K线是否完成

  // 价格精度
  int _pricePrecision = 2;

  // 用于更新回调
  Function(CandleModel)? _onCandleUpdate;

  // 价格区间的块大小（基于ATR的比例）
  double _priceBlockRatio = 0.3; // 默认为ATR的20%

  // Volume Profile数据管理器实例（共享，但通过instanceId区分数据）
  final VolumeProfileDataManager _volumeProfileDataManager =
      VolumeProfileDataManager();

  // Volume Profile Chart管理器引用（用于标记实时数据更新）
  Function()? _onVolumeProfileRealtimeUpdate;

  // 获取当前ATR值
  double get currentAtr => _currentAtr;

  // 获取近期K线数据
  List<CandleModel> get recentCandles => List.unmodifiable(_recentCandles);

  // 获取最后一根K线（用于更新图表）
  CandleModel? get latestCandle =>
      _recentCandles.isNotEmpty ? _recentCandles.first : null;

  /// 初始化数据管理器
  void initialize({
    required String instrumentId,
    required String period,
    int pricePrecision = 2,
    double priceBlockRatio = 0.2,
    Function(CandleModel)? onCandleUpdate,
    Function()? onVolumeProfileRealtimeUpdate,
  }) {
    _currentInstrumentId = instrumentId;
    _currentPeriod = period;
    _pricePrecision = pricePrecision;
    _priceBlockRatio = priceBlockRatio;
    _onCandleUpdate = onCandleUpdate;
    _onVolumeProfileRealtimeUpdate = onVolumeProfileRealtimeUpdate;

    // 清理旧数据
    _recentCandles.clear();
    _currentAtr = 0.0;
    _lastCandleTimestamp = null;
    _lastCandleEndTimestamp = null;
    _lastCandleIsComplete = false;

    if (kDebugMode) {
      print('[KlineDataManager] 数据管理器已初始化: $instrumentId, $period');
    }
  }

  /// 设置K线数据
  void setKlineData(List<CandleModel> candles) {
    // 清空现有数据
    _recentCandles.clear();

    // 只保留最近30条K线
    final recentCount = candles.length > 30 ? 30 : candles.length;
    _recentCandles.addAll(candles.sublist(0, recentCount));

    // 确保K线数据按时间降序排序（最新的在前）
    _recentCandles.sort((a, b) => b.ts.compareTo(a.ts));

    if (_recentCandles.isNotEmpty) {
      // 保存最后一根K线的完整时间信息
      final lastCandle = _recentCandles.first;
      _lastCandleTimestamp = lastCandle.ts;

      // 计算收盘时间
      final periodMs = _getPeriodMilliseconds(_currentPeriod);
      _lastCandleEndTimestamp = lastCandle.ts + periodMs;

      // 最后一根K线应该设置为可更新状态 (confirm='1')，历史K线为已完成状态 (confirm='0')
      _lastCandleIsComplete = false; // 最后一根K线允许实时更新

      // 确保最后一根K线的状态为可更新
      if (lastCandle.confirm != '1') {
        final updatedCandle = lastCandle.copyWith(confirm: '1');
        _recentCandles[0] = updatedCandle;
      }
    }

    // 计算ATR
    _calculateAtr();

    if (kDebugMode) {
      print(
        '[KlineDataManager] Set ${_recentCandles.length} candles, ATR: $_currentAtr',
      );
    }
  }

  /// 计算ATR (平均真实范围)
  void _calculateAtr() {
    if (_recentCandles.length < 2) {
      _currentAtr = 0.0;
      return;
    }

    // 默认计算14周期ATR
    final period = _recentCandles.length < 14 ? _recentCandles.length : 14;
    double sumTr = 0.0;

    for (int i = 0; i < period; i++) {
      // 确保有上一根K线用于比较
      if (i + 1 >= _recentCandles.length) break;

      final current = _recentCandles[i];
      final previous = _recentCandles[i + 1];

      // 计算真实范围 (True Range)
      // TR = max(high - low, abs(high - prev_close), abs(low - prev_close))
      final double high = current.h.toDouble();
      final double low = current.l.toDouble();
      final double prevClose = previous.c.toDouble();

      final double tr1 = high - low;
      final double tr2 = (high - prevClose).abs();
      final double tr3 = (low - prevClose).abs();

      final double tr = [
        tr1,
        tr2,
        tr3,
      ].reduce((max, value) => max > value ? max : value);
      sumTr += tr;
    }

    // 计算平均值
    _currentAtr = sumTr / period;

    if (kDebugMode) {
      print('[KlineDataManager] Calculated ATR: $_currentAtr');
    }
  }

  /// 处理成交数据（从KlineController接收）
  void processTradeData(
    double price,
    double quantity,
    bool isBuyerMaker,
    int tradeTime,
  ) {
    if (_recentCandles.isEmpty || _lastCandleTimestamp == null) {
      return;
    }

    // 检查数据是否属于当前币种（通过数据管理器的币种ID验证）
    if (_currentInstrumentId.isEmpty) {
      return;
    }

    // 实时聚合成交数据到Volume Profile缓存
    _volumeProfileDataManager.aggregateRealtimeTradeData(
      _currentInstrumentId,
      price,
      quantity,
      isBuyerMaker,
      tradeTime,
    );

    // 标记Volume Profile有实时数据更新
    _onVolumeProfileRealtimeUpdate?.call();

    // 更新最新K线
    _updateLatestCandle(price, quantity, isBuyerMaker, tradeTime);
  }

  /// 检查是否应该更新指定K线
  bool _shouldUpdateCandle(CandleModel candle, int tradeTime) {
    // 只有未完成的K线(confirm='1')才可以更新，已完成的K线(confirm='0')不能更新
    if (candle.confirm != '1') {
      return false;
    }

    // 检查时间范围
    final periodMs = _getPeriodMilliseconds(_currentPeriod);
    final candleEndTime = candle.ts + periodMs;
    return tradeTime >= candle.ts && tradeTime < candleEndTime;
  }

  /// 更新最新K线数据
  void _updateLatestCandle(
    double price,
    double quantity,
    bool isBuyerMaker,
    int tradeTime,
  ) {
    if (_recentCandles.isEmpty) return;

    // 获取最新的K线（列表中的第一个）
    final latestCandle = _recentCandles.first;

    // 如果最后一根K线已完成，直接创建新K线
    if (_lastCandleIsComplete) {
      _createNewCandle(latestCandle, price, quantity, isBuyerMaker, tradeTime);
      return;
    }

    // 使用存储的时间信息进行精确判断
    if (_lastCandleTimestamp != null && _lastCandleEndTimestamp != null) {
      // 检查是否属于当前K线时间范围
      bool isInCurrentCandle =
          tradeTime >= _lastCandleTimestamp! &&
          tradeTime < _lastCandleEndTimestamp!;

      if (isInCurrentCandle) {
        // 更新现有K线
        _updateExistingCandle(
          latestCandle,
          price,
          quantity,
          isBuyerMaker,
          tradeTime,
        );
      } else {
        // 创建新K线
        _createNewCandle(
          latestCandle,
          price,
          quantity,
          isBuyerMaker,
          tradeTime,
        );
      }
    } else {
      // 降级到原有逻辑（兼容性处理）
      final int candlePeriodMs = _getPeriodMilliseconds(_currentPeriod);
      bool isInCurrentCandle =
          tradeTime >= latestCandle.ts &&
          tradeTime < (latestCandle.ts + candlePeriodMs);

      if (isInCurrentCandle) {
        _updateExistingCandle(
          latestCandle,
          price,
          quantity,
          isBuyerMaker,
          tradeTime,
        );
      } else if (tradeTime >= (latestCandle.ts + candlePeriodMs)) {
        _createNewCandle(
          latestCandle,
          price,
          quantity,
          isBuyerMaker,
          tradeTime,
        );
      }
    }
  }

  /// 更新现有K线数据
  void _updateExistingCandle(
    CandleModel candle,
    double price,
    double quantity,
    bool isBuyerMaker,
    int tradeTime,
  ) {
    // 检查K线是否可以更新
    if (!_shouldUpdateCandle(candle, tradeTime)) {
      return;
    }
    // 计算新的OHLC值
    final double currentHigh = candle.h.toDouble();
    final double currentLow = candle.l.toDouble();
    final double currentVolume = candle.v.toDouble();

    Decimal newHigh = candle.h;
    Decimal newLow = candle.l;
    Decimal newClose = Decimal.parse(price.toStringAsFixed(_pricePrecision));
    Decimal newVolume = Decimal.parse(
      (currentVolume + quantity).toStringAsFixed(2),
    );

    // 更新最高价/最低价
    if (price > currentHigh) {
      newHigh = Decimal.parse(price.toStringAsFixed(_pricePrecision));
    }

    if (price < currentLow) {
      newLow = Decimal.parse(price.toStringAsFixed(_pricePrecision));
    }

    // 更新成交量轨迹数据
    PriceLevelDetail newPriceLevel = _updatePriceLevelDetail(
      candle.priceLevel,
      price,
      quantity,
      isBuyerMaker,
    );

    // 创建更新后的K线模型，保持未完成状态
    final updatedCandle = candle.copyWith(
      h: newHigh,
      l: newLow,
      c: newClose,
      v: newVolume,
      priceLevel: newPriceLevel,
      confirm: '1', // 保持未完成状态，允许继续更新
    );

    // 更新列表中的K线
    _recentCandles[0] = updatedCandle;

    // 更新最后一根K线的状态（仍然是未完成状态）
    _lastCandleTimestamp = updatedCandle.ts;
    final periodMs = _getPeriodMilliseconds(_currentPeriod);
    _lastCandleEndTimestamp = updatedCandle.ts + periodMs;
    _lastCandleIsComplete = false; // 更新的K线仍然是未完成状态

    // 触发回调
    _onCandleUpdate?.call(updatedCandle);
  }

  /// 创建新的K线
  void _createNewCandle(
    CandleModel lastCandle,
    double price,
    double quantity,
    bool isBuyerMaker,
    int tradeTime,
  ) {
    // 计算新K线的开始时间：上一根K线收盘时间 + 1毫秒，确保连续性
    final int newCandleTs = _lastCandleEndTimestamp! + 1;

    // 先清理可能存在的重复时间戳K线（防止历史数据中的重复）
    _recentCandles.removeWhere((candle) => candle.ts == newCandleTs);

    // 新K线的开盘价使用上一根K线的收盘价，避免跳空
    final double openPrice = lastCandle.c.toDouble();

    // 创建价格区间数据
    final PriceLevelDetail priceLevel = _createInitialPriceLevel(
      price,
      quantity,
      isBuyerMaker,
    );

    // 计算新K线的高低价：需要考虑开盘价和成交价
    final double highPrice = openPrice > price ? openPrice : price;
    final double lowPrice = openPrice < price ? openPrice : price;

    // 创建新的K线
    final newCandle = CandleModel(
      ts: newCandleTs,
      o: Decimal.parse(
        openPrice.toStringAsFixed(_pricePrecision),
      ), // 使用上一根K线收盘价作为开盘价
      h: Decimal.parse(
        highPrice.toStringAsFixed(_pricePrecision),
      ), // 开盘价和成交价中的较高者
      l: Decimal.parse(
        lowPrice.toStringAsFixed(_pricePrecision),
      ), // 开盘价和成交价中的较低者
      c: Decimal.parse(price.toStringAsFixed(_pricePrecision)), // 成交价作为收盘价
      v: Decimal.parse(quantity.toStringAsFixed(2)),
      confirm: '1', // 新K线设为未完成状态，允许实时更新
      priceLevel: priceLevel,
    );

    // 如果有之前的K线，将其设为已完成状态
    if (_recentCandles.isNotEmpty) {
      final previousCandle = _recentCandles.first;
      if (previousCandle.confirm == '1') {
        final completedCandle = previousCandle.copyWith(confirm: '0');
        _recentCandles[0] = completedCandle;
      }
    }

    // 将新K线添加到列表开头
    _recentCandles.insert(0, newCandle);

    // 如果列表超过30条，移除最旧的
    if (_recentCandles.length > 30) {
      _recentCandles.removeLast();
    }

    // 更新最后一根K线的完整时间信息
    final int periodMs = _getPeriodMilliseconds(_currentPeriod);
    _lastCandleTimestamp = newCandleTs;
    _lastCandleEndTimestamp = newCandleTs + periodMs;
    _lastCandleIsComplete = false; // 新创建的K线是未完成状态

    // 触发回调
    _onCandleUpdate?.call(newCandle);
  }

  /// 更新价格区间详情
  PriceLevelDetail _updatePriceLevelDetail(
    PriceLevelDetail? currentDetail,
    double price,
    double quantity,
    bool isBuyerMaker,
  ) {
    // 如果当前没有价格区间数据，创建新的
    if (currentDetail == null || currentDetail.isEmpty) {
      return _createInitialPriceLevel(price, quantity, isBuyerMaker);
    }

    // 获取现有的买卖区间数据
    List<PriceLevelEntry> sellLevels = currentDetail.sellLevels?.toList() ?? [];
    List<PriceLevelEntry> buyLevels = currentDetail.buyLevels?.toList() ?? [];

    // 计算总成交量
    double totalSellVolume = currentDetail.totalSellVolume ?? 0;
    double totalBuyVolume = currentDetail.totalBuyVolume ?? 0;

    // 基于ATR的价格区间大小
    double blockSize = _currentAtr * _priceBlockRatio;
    if (blockSize <= 0) blockSize = 10.0; // 默认值

    // 根据交易类型更新买卖区间
    if (isBuyerMaker) {
      // 如果买方是挂单方，则为卖出交易，更新卖出区间
      _updatePriceLevel(sellLevels, price, quantity, blockSize);
      totalSellVolume += quantity;
    } else {
      // 否则更新买入区间
      _updatePriceLevel(buyLevels, price, quantity, blockSize);
      totalBuyVolume += quantity;
    }

    // 返回更新后的价格区间详情
    return PriceLevelDetail(
      sellLevels: sellLevels.isEmpty ? null : sellLevels,
      buyLevels: buyLevels.isEmpty ? null : buyLevels,
      totalSellVolume: totalSellVolume > 0 ? totalSellVolume : null,
      totalBuyVolume: totalBuyVolume > 0 ? totalBuyVolume : null,
    );
  }

  /// 更新价格区间列表
  void _updatePriceLevel(
    List<PriceLevelEntry> levels,
    double price,
    double quantity,
    double blockSize,
  ) {
    // 计算价格区间
    final double blockMin = (price ~/ blockSize) * blockSize;
    final double blockMax = blockMin + blockSize;

    // 查找匹配的价格区间
    int matchIndex = levels.indexWhere(
      (entry) => price >= entry.priceMin && price < entry.priceMax,
    );

    if (matchIndex >= 0) {
      // 更新现有区间
      final existing = levels[matchIndex];
      levels[matchIndex] = PriceLevelEntry(
        priceMin: existing.priceMin,
        priceMax: existing.priceMax,
        volume: existing.volume + quantity,
      );
    } else {
      // 添加新区间
      levels.add(
        PriceLevelEntry(
          priceMin: blockMin,
          priceMax: blockMax,
          volume: quantity,
        ),
      );

      // 按价格排序（卖出区间按价格升序，买入区间按价格降序）
      levels.sort((a, b) => a.priceMin.compareTo(b.priceMin));
    }

    // 限制区间数量，保留成交量最大的区间
    if (levels.length > 5) {
      levels.sort((a, b) => b.volume.compareTo(a.volume));
      levels = levels.sublist(0, 5);
      // 恢复价格排序
      levels.sort((a, b) => a.priceMin.compareTo(b.priceMin));
    }
  }

  /// 创建初始价格区间
  PriceLevelDetail _createInitialPriceLevel(
    double price,
    double quantity,
    bool isBuyerMaker,
  ) {
    // 计算区间大小
    double blockSize = _currentAtr * _priceBlockRatio;
    if (blockSize <= 0) blockSize = 10.0; // 默认值

    // 计算价格区间
    final double blockMin = (price ~/ blockSize) * blockSize;
    final double blockMax = blockMin + blockSize;

    // 创建价格区间条目
    final entry = PriceLevelEntry(
      priceMin: blockMin,
      priceMax: blockMax,
      volume: quantity,
    );

    // 根据交易类型添加到对应的买卖区间
    if (isBuyerMaker) {
      return PriceLevelDetail(sellLevels: [entry], totalSellVolume: quantity);
    } else {
      return PriceLevelDetail(buyLevels: [entry], totalBuyVolume: quantity);
    }
  }

  /// 获取时间周期的毫秒数
  int _getPeriodMilliseconds(String period) {
    // 解析周期字符串 (如 "1m", "5m", "1h", "1d")
    final regExp = RegExp(r'(\d+)([mhdw])');
    final match = regExp.firstMatch(period);

    if (match == null) {
      return 60000; // 默认1分钟
    }

    final int value = int.parse(match.group(1)!);
    final String unit = match.group(2)!;

    switch (unit) {
      case 'm': // 分钟
        return value * 60 * 1000;
      case 'h': // 小时
        return value * 60 * 60 * 1000;
      case 'd': // 天
        return value * 24 * 60 * 60 * 1000;
      case 'w': // 周
        return value * 7 * 24 * 60 * 60 * 1000;
      default:
        return 60000; // 默认1分钟
    }
  }

  /// 清理旧的成交量轨迹数据（内存优化）
  void clearOldVolumeProfileData() {
    if (_recentCandles.length > 30) {
      // 只保留最近30根K线，清理旧数据
      _recentCandles.removeRange(30, _recentCandles.length);
      if (kDebugMode) {
        print('[KlineDataManager] 清理旧数据，保留 ${_recentCandles.length} 根K线');
      }
    }

    // 清理每根K线中的详细价格区间数据，只保留关键信息
    for (int i = 0; i < _recentCandles.length; i++) {
      final candle = _recentCandles[i];
      if (candle.priceLevel != null) {
        final priceLevel = candle.priceLevel!;

        // 如果买卖区间数据过多，进行精简
        List<PriceLevelEntry>? optimizedBuyLevels = priceLevel.buyLevels;
        List<PriceLevelEntry>? optimizedSellLevels = priceLevel.sellLevels;
        bool needsOptimization = false;

        if (priceLevel.buyLevels != null && priceLevel.buyLevels!.length > 10) {
          // 只保留成交量最大的10个区间
          final sortedBuyLevels = List<PriceLevelEntry>.from(
            priceLevel.buyLevels!,
          )..sort((a, b) => b.volume.compareTo(a.volume));
          optimizedBuyLevels = sortedBuyLevels.take(10).toList();
          needsOptimization = true;
        }

        if (priceLevel.sellLevels != null &&
            priceLevel.sellLevels!.length > 10) {
          final sortedSellLevels = List<PriceLevelEntry>.from(
            priceLevel.sellLevels!,
          )..sort((a, b) => b.volume.compareTo(a.volume));
          optimizedSellLevels = sortedSellLevels.take(10).toList();
          needsOptimization = true;
        }

        if (needsOptimization) {
          final optimizedPriceLevel = PriceLevelDetail(
            buyLevels: optimizedBuyLevels,
            sellLevels: optimizedSellLevels,
            totalBuyVolume: priceLevel.totalBuyVolume,
            totalSellVolume: priceLevel.totalSellVolume,
          );
          _recentCandles[i] = candle.copyWith(priceLevel: optimizedPriceLevel);
        }
      }
    }
  }

  /// 清理资源
  void dispose() {
    _recentCandles.clear();
    _onCandleUpdate = null;
  }

  /// 获取币安U本位合约近期成交(归集)数据并转换为K线
  /// symbol: 交易对，例如 "BTCUSDT"
  /// timeframe: 时间周期，例如 "1m", "5m", "1h"
  /// limit: 获取数据条数，最大1000
  Future<List<CandleModel>> fetchAggTradeData(
    String symbol,
    String timeframe,
    int limit,
  ) async {
    if (kDebugMode) {
      print(
        '[KlineDataManager] Fetching agg trade data for $symbol, timeframe $timeframe, limit $limit',
      );
    }

    // 创建RequestClient
    final requestClient = RequestClient();
    requestClient.setProxy('127.0.0.1:1087'); // 设置代理

    // 定义API URL和参数
    final String apiUrl = 'https://fapi.binance.com/fapi/v1/aggTrades';
    final Map<String, dynamic> queryParameters = {
      'symbol': symbol,
      'limit': limit,
    };

    try {
      // 发送请求获取归集成交数据
      final response = await requestClient.get(
        apiUrl,
        queryParameters: queryParameters,
      );

      if (response.statusCode == 200) {
        final List<dynamic> rawTradeData = response.data as List<dynamic>;
        if (kDebugMode) {
          print(
            '[KlineDataManager] Successfully fetched ${rawTradeData.length} agg trades.',
          );
        }

        // 按时间周期归集成交数据
        final Map<int, Map<String, dynamic>> candleMap = {};
        final int periodMs = _getPeriodMilliseconds(timeframe);

        // 处理每一条成交数据
        for (final trade in rawTradeData) {
          final int tradeTime = trade['T'] as int; // 成交时间
          final double tradePrice = double.parse(trade['p']); // 成交价格
          final double tradeQuantity = double.parse(trade['q']); // 成交数量
          final bool isBuyerMaker = trade['m'] as bool; // 是否是买方挂单

          // 计算该成交所属的K线时间戳
          final int candleTs = (tradeTime ~/ periodMs) * periodMs;

          // 如果该时间戳的K线还不存在，创建新的
          if (!candleMap.containsKey(candleTs)) {
            candleMap[candleTs] = {
              'open': tradePrice,
              'high': tradePrice,
              'low': tradePrice,
              'close': tradePrice,
              'volume': tradeQuantity,
              'buyVolume': isBuyerMaker ? 0.0 : tradeQuantity,
              'sellVolume': isBuyerMaker ? tradeQuantity : 0.0,
              'trades': 1,
            };
          } else {
            // 更新现有K线数据
            final candle = candleMap[candleTs]!;

            // 更新高低价
            if (tradePrice > candle['high']) {
              candle['high'] = tradePrice;
            }
            if (tradePrice < candle['low']) {
              candle['low'] = tradePrice;
            }

            // 更新收盘价和成交量
            candle['close'] = tradePrice;
            candle['volume'] = (candle['volume'] as double) + tradeQuantity;

            // 更新买卖方向成交量
            if (isBuyerMaker) {
              candle['sellVolume'] =
                  (candle['sellVolume'] as double) + tradeQuantity;
            } else {
              candle['buyVolume'] =
                  (candle['buyVolume'] as double) + tradeQuantity;
            }

            // 增加成交笔数
            candle['trades'] = (candle['trades'] as int) + 1;
          }
        }

        // 转换为CandleModel列表
        final List<CandleModel> candles = [];

        candleMap.forEach((timestamp, data) {
          // 创建K线模型
          final candle = CandleModel(
            ts: timestamp,
            o: Decimal.parse(
              (data['open'] as double).toStringAsFixed(_pricePrecision),
            ),
            h: Decimal.parse(
              (data['high'] as double).toStringAsFixed(_pricePrecision),
            ),
            l: Decimal.parse(
              (data['low'] as double).toStringAsFixed(_pricePrecision),
            ),
            c: Decimal.parse(
              (data['close'] as double).toStringAsFixed(_pricePrecision),
            ),
            v: Decimal.parse((data['volume'] as double).toStringAsFixed(2)),
            confirm: '1', // 已完成的K线
          );

          candles.add(candle);
        });

        // 按时间排序（从旧到新）便于计算ATR
        candles.sort((a, b) => a.ts.compareTo(b.ts));

        // 计算ATR
        double atrValue = _calculateAtrFromCandles(candles);
        _currentAtr = atrValue;
        if (kDebugMode) {
          print('[KlineDataManager] Calculated ATR for agg trades: $atrValue');
        }

        // 使用计算出的ATR添加价格区间
        final List<CandleModel> candlesWithPriceLevel =
            candles.map((candle) {
              // 获取高低价和成交量
              final candleTs = candle.ts;
              final candleData = candleMap[candleTs]!;

              // 创建价格区间数据
              final PriceLevelDetail? priceLevel =
                  _createPriceLevelFromTradeWithAtr(
                    candleData['high'] as double,
                    candleData['low'] as double,
                    candleData['buyVolume'] as double,
                    candleData['sellVolume'] as double,
                    atrValue,
                  );

              // 返回带有价格区间的K线
              return candle.copyWith(priceLevel: priceLevel);
            }).toList();

        // 按时间降序排序（最新的在前）
        candlesWithPriceLevel.sort((a, b) => b.ts.compareTo(a.ts));

        return candlesWithPriceLevel;
      } else {
        if (kDebugMode) {
          print(
            '[KlineDataManager] Failed to fetch agg trades: HTTP ${response.statusCode}',
          );
        }
        return [];
      }
    } catch (e) {
      if (kDebugMode) {
        print('[KlineDataManager] Exception during agg trade fetch: $e');
      }
      return [];
    }
  }

  /// 从K线数据计算ATR值
  /// 如果数据不足，则使用开盘-收盘的波动作为ATR
  double _calculateAtrFromCandles(List<CandleModel> candles) {
    if (candles.isEmpty) return 0.0;

    // 如果只有一条数据，使用高低价差作为ATR
    if (candles.length == 1) {
      final candle = candles.first;
      return (candle.h - candle.l).toDouble();
    }

    // 计算标准ATR
    // 默认使用14周期，或可用的最大周期
    final int period = candles.length < 14 ? candles.length : 14;
    double sumTr = 0.0;

    for (int i = 1; i < period + 1 && i < candles.length; i++) {
      final current = candles[i];
      final previous = candles[i - 1];

      // 计算真实范围 (True Range)
      // TR = max(high - low, abs(high - prev_close), abs(low - prev_close))
      final double high = current.h.toDouble();
      final double low = current.l.toDouble();
      final double prevClose = previous.c.toDouble();

      final double tr1 = high - low;
      final double tr2 = (high - prevClose).abs();
      final double tr3 = (low - prevClose).abs();

      final double tr = [
        tr1,
        tr2,
        tr3,
      ].reduce((max, value) => max > value ? max : value);
      sumTr += tr;
    }

    // 如果能计算标准ATR，则返回平均值
    if (period > 1) {
      return sumTr / period;
    } else {
      // 数据不足，使用所有可用K线的开盘-收盘波动平均值作为替代
      double totalRange = 0.0;
      for (final candle in candles) {
        totalRange += (candle.o - candle.c).abs().toDouble();
      }
      return totalRange / candles.length;
    }
  }

  /// 根据成交数据和ATR值创建价格区间分布
  PriceLevelDetail? _createPriceLevelFromTradeWithAtr(
    double high,
    double low,
    double buyVolume,
    double sellVolume,
    double atrValue,
  ) {
    if (buyVolume <= 0 && sellVolume <= 0) return null;

    // 计算价格区间大小
    double range = high - low;
    if (range <= 0) range = 0.01; // 防止除零

    // 使用传入的ATR值计算价格区块大小
    double blockSize = atrValue * _priceBlockRatio;
    if (blockSize <= 0 || !blockSize.isFinite) {
      // 如果ATR无效，使用价格范围的一部分
      blockSize = range / 5;
      // 确保至少有一个最小块大小
      if (blockSize <= 0) blockSize = 0.01;
    }

    // 创建买卖区间列表
    List<PriceLevelEntry>? buyLevels;
    List<PriceLevelEntry>? sellLevels;

    if (buyVolume > 0) {
      // 计算买单区间的分布
      final int numBlocks = (range / blockSize).ceil().clamp(1, 5);
      buyLevels = [];

      // 将买单量分配到不同价格区间
      double blockVolume = buyVolume / numBlocks;
      double startPrice = low;

      for (int i = 0; i < numBlocks; i++) {
        // 为每个区间分配一部分成交量
        double endPrice = (startPrice + blockSize).clamp(startPrice, high);
        double adjustedVolume =
            blockVolume * (1.0 + (0.5 - i / numBlocks)); // 低价区域成交量稍多

        buyLevels.add(
          PriceLevelEntry(
            priceMin: startPrice,
            priceMax: endPrice,
            volume: adjustedVolume,
          ),
        );

        startPrice = endPrice;
      }
    }

    if (sellVolume > 0) {
      // 计算卖单区间的分布
      final int numBlocks = (range / blockSize).ceil().clamp(1, 5);
      sellLevels = [];

      // 将卖单量分配到不同价格区间
      double blockVolume = sellVolume / numBlocks;
      double startPrice = high;

      for (int i = 0; i < numBlocks; i++) {
        // 为每个区间分配一部分成交量
        double endPrice = (startPrice - blockSize).clamp(low, startPrice);
        double adjustedVolume =
            blockVolume * (1.0 + (0.5 - i / numBlocks)); // 高价区域成交量稍多

        sellLevels.add(
          PriceLevelEntry(
            priceMin: endPrice,
            priceMax: startPrice,
            volume: adjustedVolume,
          ),
        );

        startPrice = endPrice;
      }

      // 确保按价格升序排序
      sellLevels.sort((a, b) => a.priceMin.compareTo(b.priceMin));
    }

    // 创建并返回价格区间详情
    return PriceLevelDetail(
      buyLevels: buyLevels,
      sellLevels: sellLevels,
      totalBuyVolume: buyVolume > 0 ? buyVolume : null,
      totalSellVolume: sellVolume > 0 ? sellVolume : null,
    );
  }

  /// 获取内存使用统计
  Map<String, dynamic> getMemoryStats() {
    return {
      'recentCandlesCount': _recentCandles.length,
      'currentAtr': _currentAtr,
      'lastCandleTimestamp': _lastCandleTimestamp,
    };
  }

  /// 强制清理内存
  void forceCleanup() {
    // 只保留最近10根K线
    if (_recentCandles.length > 10) {
      _recentCandles.removeRange(10, _recentCandles.length);
    }

    // 简化价格区间数据
    for (int i = 0; i < _recentCandles.length; i++) {
      final candle = _recentCandles[i];
      if (candle.priceLevel != null) {
        final priceLevel = candle.priceLevel!;

        // 只保留最重要的价格区间
        List<PriceLevelEntry>? simplifiedBuyLevels;
        List<PriceLevelEntry>? simplifiedSellLevels;

        if (priceLevel.buyLevels != null && priceLevel.buyLevels!.isNotEmpty) {
          final sortedBuy = List<PriceLevelEntry>.from(priceLevel.buyLevels!)
            ..sort((a, b) => b.volume.compareTo(a.volume));
          simplifiedBuyLevels = [sortedBuy.first]; // 只保留成交量最大的
        }

        if (priceLevel.sellLevels != null &&
            priceLevel.sellLevels!.isNotEmpty) {
          final sortedSell = List<PriceLevelEntry>.from(priceLevel.sellLevels!)
            ..sort((a, b) => b.volume.compareTo(a.volume));
          simplifiedSellLevels = [sortedSell.first]; // 只保留成交量最大的
        }

        final simplifiedPriceLevel = PriceLevelDetail(
          buyLevels: simplifiedBuyLevels,
          sellLevels: simplifiedSellLevels,
          totalBuyVolume: priceLevel.totalBuyVolume,
          totalSellVolume: priceLevel.totalSellVolume,
        );

        _recentCandles[i] = candle.copyWith(priceLevel: simplifiedPriceLevel);
      }
    }

    if (kDebugMode) {
      print('[KlineDataManager] 强制清理完成，剩余K线: ${_recentCandles.length}');
    }
  }
}
