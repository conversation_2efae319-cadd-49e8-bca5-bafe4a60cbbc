import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import '../config/indicator_config/volume_profile_config.dart';
import 'color_picker_widget.dart';

class VolumeProfileConfigWidget extends StatefulWidget {
  final VolumeProfileConfig config;
  final Function(VolumeProfileConfig) onConfigChanged;

  const VolumeProfileConfigWidget({
    super.key,
    required this.config,
    required this.onConfigChanged,
  });

  @override
  State<VolumeProfileConfigWidget> createState() =>
      _VolumeProfileConfigWidgetState();
}

class _VolumeProfileConfigWidgetState extends State<VolumeProfileConfigWidget> {
  late VolumeProfileConfig _config;

  @override
  void initState() {
    super.initState();
    _config = widget.config;
  }

  // 颜色getter - 使用模板主题系统
  Color get _primaryColor => context.templateColors.primary;
  Color get _textPrimaryColor => context.templateColors.textPrimary;
  Color get _textSecondaryColor => context.templateColors.textSecondary;
  Color get _cardBackgroundColor => context.templateColors.cardBackground;
  Color get _dividerColor => context.templateColors.divider;

  @override
  void didUpdateWidget(VolumeProfileConfigWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.config != widget.config) {
      _config = widget.config;
    }
  }

  void _updateConfig(VolumeProfileConfig newConfig) {
    setState(() {
      _config = newConfig;
    });
    widget.onConfigChanged(newConfig);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        const SizedBox(height: 16),
        _buildEnabledSwitch(),
        const SizedBox(height: 20),
        _buildColorSettings(),
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.bar_chart, size: 24, color: _textPrimaryColor),
        const SizedBox(width: 8),
        Text(
          '成交量轨迹设置',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: _textPrimaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildEnabledSwitch() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _cardBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _dividerColor, width: 1),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '启用成交量轨迹',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: _textPrimaryColor,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '显示K线图上的成交量分布轨迹',
                style: TextStyle(
                  fontSize: 12,
                  color: _textSecondaryColor,
                ),
              ),
            ],
          ),
          Switch(
            value: _config.enabled,
            onChanged: (value) {
              _updateConfig(_config.copyWith(enabled: value));
            },
            activeColor: _primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildColorSettings() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _cardBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _dividerColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '颜色设置',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: _textPrimaryColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildColorPicker(
            label: '上涨颜色',
            description: '买入成交量的显示颜色',
            color: _config.bullishColor,
            onColorChanged: (color) {
              _updateConfig(_config.copyWith(bullishColor: color));
            },
          ),
          const SizedBox(height: 16),
          _buildColorPicker(
            label: '下跌颜色',
            description: '卖出成交量的显示颜色',
            color: _config.bearishColor,
            onColorChanged: (color) {
              _updateConfig(_config.copyWith(bearishColor: color));
            },
          ),
        ],
      ),
    );
  }

  Widget _buildColorPicker({
    required String label,
    required String description,
    required Color color,
    required Function(Color) onColorChanged,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: _textPrimaryColor,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: _textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),
        GestureDetector(
          onTap: () => _showColorPicker(color, onColorChanged),
          child: Container(
            width: 40,
            height: 32,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: _dividerColor, width: 1),
            ),
          ),
        ),
      ],
    );
  }

  void _showColorPicker(Color currentColor, Function(Color) onColorChanged) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: _cardBackgroundColor,
            title: Text(
              '选择颜色',
              style: TextStyle(color: _textPrimaryColor),
            ),
            content: SizedBox(
              width: 320,
              height: 450,
              child: ColorPickerWidget(
                initialColor: currentColor,
                onColorChanged: onColorChanged,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  '取消',
                  style: TextStyle(color: _textSecondaryColor),
                ),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }
}
