import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import '../../../core/templates/template_theme_provider.dart';
import 'dart:math' as math;

class ColorPickerWidget extends StatefulWidget {
  final Color initialColor;
  final Function(Color) onColorChanged;

  const ColorPickerWidget({
    super.key,
    required this.initialColor,
    required this.onColorChanged,
  });

  @override
  State<ColorPickerWidget> createState() => _ColorPickerWidgetState();
}

class _ColorPickerWidgetState extends State<ColorPickerWidget> {
  late HSVColor _currentHSV;
  late Color _currentColor;

  @override
  void initState() {
    super.initState();
    _currentColor = widget.initialColor;
    _currentHSV = HSVColor.fromColor(_currentColor);
  }

  void _updateColor(HSVColor hsv) {
    setState(() {
      _currentHSV = hsv;
      _currentColor = hsv.toColor();
    });
    widget.onColorChanged(_currentColor);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 主色板区域
        _buildColorPalette(),
        const SizedBox(height: 16),

        // 色相条
        _buildHueBar(),
        const SizedBox(height: 16),

        // 当前颜色预览和RGB值
        _buildColorPreview(),
        const SizedBox(height: 16),

        // 预设颜色
        _buildPresetColors(),
      ],
    );
  }

  Widget _buildColorPalette() {
    return Container(
      width: 260,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: context.templateColors.divider),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: GestureDetector(
          onPanUpdate:
              (details) => _handlePaletteInteraction(details.localPosition),
          onTapDown:
              (details) => _handlePaletteInteraction(details.localPosition),
          child: CustomPaint(
            size: const Size(260, 200),
            painter: _ColorPalettePainter(_currentHSV.hue),
            child: Stack(
              children: [
                // 选择器指示器
                Positioned(
                  left: _currentHSV.saturation * 260 - 6,
                  top: (1 - _currentHSV.value) * 200 - 6,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.white, width: 2),
                      borderRadius: BorderRadius.circular(6),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handlePaletteInteraction(Offset position) {
    final saturation = (position.dx / 260).clamp(0.0, 1.0);
    final value = (1 - position.dy / 200).clamp(0.0, 1.0);

    _updateColor(_currentHSV.withSaturation(saturation).withValue(value));
  }

  Widget _buildHueBar() {
    return Container(
      width: 260,
      height: 20,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: context.templateColors.divider),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: GestureDetector(
          onPanUpdate:
              (details) => _handleHueInteraction(details.localPosition),
          onTapDown: (details) => _handleHueInteraction(details.localPosition),
          child: CustomPaint(
            size: const Size(260, 20),
            painter: _HueBarPainter(),
            child: Stack(
              children: [
                // 色相指示器
                Positioned(
                  left: (_currentHSV.hue / 360) * 260 - 2,
                  top: 0,
                  bottom: 0,
                  child: Container(
                    width: 4,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(2),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleHueInteraction(Offset position) {
    final hue = (position.dx / 260 * 360).clamp(0.0, 360.0);
    _updateColor(_currentHSV.withHue(hue));
  }

  Widget _buildColorPreview() {
    return Row(
      children: [
        // 当前颜色预览
        Container(
          width: 60,
          height: 40,
          decoration: BoxDecoration(
            color: _currentColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: context.templateColors.divider),
          ),
        ),
        const SizedBox(width: 16),

        // 颜色值显示
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'HEX: #${_currentColor.value.toRadixString(16).substring(2).toUpperCase()}',
                style: TextStyle(
                  fontSize: 12,
                  color: context.templateColors.textPrimary,
                  fontFamily: 'monospace',
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'RGB: ${_currentColor.red}, ${_currentColor.green}, ${_currentColor.blue}',
                style: TextStyle(
                  fontSize: 12,
                  color: context.templateColors.textSecondary,
                  fontFamily: 'monospace',
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPresetColors() {
    final presetColors = [
      Colors.red,
      Colors.pink,
      Colors.purple,
      Colors.deepPurple,
      Colors.indigo,
      Colors.blue,
      Colors.lightBlue,
      Colors.cyan,
      Colors.teal,
      Colors.green,
      Colors.lightGreen,
      Colors.lime,
      Colors.yellow,
      Colors.amber,
      Colors.orange,
      Colors.deepOrange,
      Colors.brown,
      Colors.grey,
      Colors.blueGrey,
      Colors.black,
      Colors.white,
      context.templateKlineStyles.bullCandleColor, // 模板上涨颜色
      context.templateKlineStyles.bearCandleColor, // 模板下跌颜色
      context.templateKlineStyles.themeColor, // 模板主题色
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '预设颜色',
          style: TextStyle(
            color: context.templateColors.textPrimary,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 6,
          runSpacing: 6,
          children:
              presetColors.map((color) {
                final isSelected = color.value == _currentColor.value;
                return GestureDetector(
                  onTap: () {
                    _currentColor = color;
                    _currentHSV = HSVColor.fromColor(color);
                    setState(() {});
                    widget.onColorChanged(color);
                  },
                  child: Container(
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color:
                            isSelected
                                ? context.templateColors.textPrimary
                                : context.templateColors.divider,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child:
                        isSelected
                            ? Icon(
                              Icons.check,
                              color: _getContrastColor(color),
                              size: 14,
                            )
                            : null,
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }

  Color _getContrastColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
}

class _ColorPalettePainter extends CustomPainter {
  final double hue;

  _ColorPalettePainter(this.hue);

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // 绘制饱和度渐变（左到右：白色到纯色）
    final saturationGradient = LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [Colors.white, HSVColor.fromAHSV(1.0, hue, 1.0, 1.0).toColor()],
    );

    canvas.drawRect(
      rect,
      Paint()..shader = saturationGradient.createShader(rect),
    );

    // 绘制亮度渐变（上到下：透明到黑色）
    final valueGradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [Colors.transparent, Colors.black],
    );

    canvas.drawRect(rect, Paint()..shader = valueGradient.createShader(rect));
  }

  @override
  bool shouldRepaint(_ColorPalettePainter oldDelegate) {
    return oldDelegate.hue != hue;
  }
}

class _HueBarPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    final colors = <Color>[];
    for (int i = 0; i <= 360; i += 10) {
      colors.add(HSVColor.fromAHSV(1.0, i.toDouble(), 1.0, 1.0).toColor());
    }

    final gradient = LinearGradient(colors: colors);
    canvas.drawRect(rect, Paint()..shader = gradient.createShader(rect));
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
