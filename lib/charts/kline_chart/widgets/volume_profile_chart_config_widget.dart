import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import '../config/indicator_config/volume_profile_chart_config.dart';
import 'color_picker_widget.dart';

class VolumeProfileChartConfigWidget extends StatefulWidget {
  final VolumeProfileChartConfig config;
  final Function(VolumeProfileChartConfig) onConfigChanged;

  const VolumeProfileChartConfigWidget({
    super.key,
    required this.config,
    required this.onConfigChanged,
  });

  @override
  State<VolumeProfileChartConfigWidget> createState() =>
      _VolumeProfileChartConfigWidgetState();
}

class _VolumeProfileChartConfigWidgetState
    extends State<VolumeProfileChartConfigWidget> {
  late VolumeProfileChartConfig _config;

  @override
  void initState() {
    super.initState();
    _config = widget.config;
  }

  // 颜色getter - 使用模板主题系统
  Color get _primaryColor => context.templateColors.primary;
  Color get _textPrimaryColor => context.templateColors.textPrimary;
  Color get _textSecondaryColor => context.templateColors.textSecondary;
  Color get _cardBackgroundColor => context.templateColors.cardBackground;
  Color get _dividerColor => context.templateColors.divider;

  @override
  void didUpdateWidget(VolumeProfileChartConfigWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.config != widget.config) {
      _config = widget.config;
    }
  }

  void _updateConfig(VolumeProfileChartConfig newConfig) {
    setState(() {
      _config = newConfig;
    });
    widget.onConfigChanged(newConfig);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        const SizedBox(height: 16),
        _buildEnabledSwitch(),
        const SizedBox(height: 20),
        _buildPositionSettings(),
        const SizedBox(height: 20),
        _buildDisplayModeSettings(),
        const SizedBox(height: 20),
        _buildLineSettings(),
        const SizedBox(height: 20),
        _buildVolumeColorSettings(),
        const SizedBox(height: 20),
        _buildRenderSettings(),
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.bar_chart_outlined, size: 24, color: _textPrimaryColor),
        const SizedBox(width: 8),
        Text(
          '成交量分布图设置',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: _textPrimaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildEnabledSwitch() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _cardBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _dividerColor, width: 1),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '启用成交量分布图',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: _textPrimaryColor,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '显示横向的价格成交量分布',
                style: TextStyle(fontSize: 12, color: _textSecondaryColor),
              ),
            ],
          ),
          Switch(
            value: _config.enabled,
            onChanged: (value) {
              _updateConfig(_config.copyWith(enabled: value));
            },
            activeColor: _primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildPositionSettings() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _cardBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _dividerColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '显示位置',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: _textPrimaryColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children:
                VolumeProfileChartPosition.values.map((position) {
                  final isSelected = _config.position == position;
                  return Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: GestureDetector(
                        onTap: () {
                          _updateConfig(_config.copyWith(position: position));
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          decoration: BoxDecoration(
                            color:
                                isSelected
                                    ? _primaryColor.withOpacity(0.1)
                                    : Colors.transparent,
                            border: Border.all(
                              color:
                                  isSelected
                                      ? _primaryColor
                                      : _dividerColor,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Text(
                            position.displayName,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  isSelected
                                      ? _primaryColor
                                      : _textPrimaryColor,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.w500
                                      : FontWeight.normal,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildDisplayModeSettings() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _cardBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _dividerColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '绘制方式',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: _textPrimaryColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children:
                VolumeProfileChartDisplayMode.values.map((mode) {
                  final isSelected = _config.displayMode == mode;
                  return Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: GestureDetector(
                        onTap: () {
                          _updateConfig(_config.copyWith(displayMode: mode));
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          decoration: BoxDecoration(
                            color:
                                isSelected
                                    ? _primaryColor.withOpacity(0.1)
                                    : Colors.transparent,
                            border: Border.all(
                              color:
                                  isSelected
                                      ? _primaryColor
                                      : _dividerColor,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Text(
                            mode.displayName,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  isSelected
                                      ? _primaryColor
                                      : _textPrimaryColor,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.w500
                                      : FontWeight.normal,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
          ),
          const SizedBox(height: 8),
          Text(
            _config.displayMode == VolumeProfileChartDisplayMode.total
                ? '每个价格绘制一根总成交量柱'
                : '每个价格分别绘制买入和卖出成交量柱',
            style: TextStyle(fontSize: 12, color: _textSecondaryColor),
          ),
        ],
      ),
    );
  }

  Widget _buildLineSettings() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _cardBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _dividerColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '关键线条设置',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: _textPrimaryColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildLineItem(
            'POC',
            'Point of Control - 成交量最大价格',
            _config.showPOC,
            _config.pocColor,
            (enabled) => _updateConfig(_config.copyWith(showPOC: enabled)),
            (color) => _updateConfig(_config.copyWith(pocColor: color)),
          ),
          const SizedBox(height: 12),
          _buildLineItem(
            'VAH',
            'Value Area High - 价值区间高点',
            _config.showVAH,
            _config.vahColor,
            (enabled) => _updateConfig(_config.copyWith(showVAH: enabled)),
            (color) => _updateConfig(_config.copyWith(vahColor: color)),
          ),
          const SizedBox(height: 12),
          _buildLineItem(
            'VAL',
            'Value Area Low - 价值区间低点',
            _config.showVAL,
            _config.valColor,
            (enabled) => _updateConfig(_config.copyWith(showVAL: enabled)),
            (color) => _updateConfig(_config.copyWith(valColor: color)),
          ),
        ],
      ),
    );
  }

  Widget _buildLineItem(
    String label,
    String description,
    bool enabled,
    Color color,
    Function(bool) onEnabledChanged,
    Function(Color) onColorChanged,
  ) {
    LineStyle lineStyle = LineStyle.solid;
    Function(LineStyle)? onLineStyleChanged;

    // 根据标签确定当前线段样式和回调
    switch (label) {
      case 'POC':
        lineStyle = _config.pocLineStyle;
        onLineStyleChanged =
            (style) => _updateConfig(_config.copyWith(pocLineStyle: style));
        break;
      case 'VAH':
        lineStyle = _config.vahLineStyle;
        onLineStyleChanged =
            (style) => _updateConfig(_config.copyWith(vahLineStyle: style));
        break;
      case 'VAL':
        lineStyle = _config.valLineStyle;
        onLineStyleChanged =
            (style) => _updateConfig(_config.copyWith(valLineStyle: style));
        break;
    }

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: _textPrimaryColor,
                    ),
                  ),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: _textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Switch(
              value: enabled,
              onChanged: onEnabledChanged,
              activeColor: _primaryColor,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildColorField(
                '',
                enabled ? color : Colors.grey.withOpacity(0.3),
                enabled ? onColorChanged : (_) {},
              ),
            ),
          ],
        ),
        if (enabled && onLineStyleChanged != null) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              const SizedBox(width: 16),
              Text(
                '线条样式:',
                style: TextStyle(
                  fontSize: 12,
                  color: _textSecondaryColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: _dividerColor),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: DropdownButton<LineStyle>(
                    value: lineStyle,
                    isExpanded: true,
                    underline: const SizedBox.shrink(),
                    items:
                        LineStyle.values.map((style) {
                          return DropdownMenuItem<LineStyle>(
                            value: style,
                            child: Text(
                              style.displayName,
                              style: TextStyle(
                                fontSize: 12,
                                color: _textPrimaryColor,
                              ),
                            ),
                          );
                        }).toList(),
                    onChanged: (newStyle) {
                      if (newStyle != null && onLineStyleChanged != null) {
                        onLineStyleChanged!(newStyle);
                      }
                    },
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildVolumeColorSettings() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _cardBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _dividerColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '成交量柱颜色设置',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: _textPrimaryColor,
            ),
          ),
          const SizedBox(height: 16),
          if (_config.displayMode ==
              VolumeProfileChartDisplayMode.separated) ...[
            Row(
              children: [
                Expanded(
                  child: _buildColorField('买入成交量颜色', _config.upVolumeColor, (
                    color,
                  ) {
                    _updateConfig(_config.copyWith(upVolumeColor: color));
                  }),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildColorField('卖出成交量颜色', _config.downVolumeColor, (
                    color,
                  ) {
                    _updateConfig(_config.copyWith(downVolumeColor: color));
                  }),
                ),
              ],
            ),
          ] else ...[
            _buildColorField('总成交量颜色', _config.totalVolumeColor, (color) {
              _updateConfig(_config.copyWith(totalVolumeColor: color));
            }),
          ],
        ],
      ),
    );
  }

  Widget _buildRenderSettings() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _cardBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _dividerColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '绘制设置',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: _textPrimaryColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildSliderField(
            '透明度',
            '调整Volume Profile的透明度',
            _config.opacity,
            0.1,
            1.0,
            (value) => _updateConfig(_config.copyWith(opacity: value)),
          ),
          const SizedBox(height: 16),
          _buildSliderField(
            '最大宽度',
            '调整Volume Profile的最大宽度占比',
            _config.maxWidthPercent,
            0.1,
            0.8,
            (value) => _updateConfig(_config.copyWith(maxWidthPercent: value)),
          ),
          const SizedBox(height: 16),
          _buildSliderField(
            '数据平滑',
            '调整成交量数据的平滑程度，提升小成交量的可见性',
            _config.smoothingStrength,
            0.0,
            1.0,
            (value) =>
                _updateConfig(_config.copyWith(smoothingStrength: value)),
          ),
        ],
      ),
    );
  }

  Widget _buildSliderField(
    String label,
    String description,
    double value,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: _textPrimaryColor,
              ),
            ),
            Text(
              '${(value * 100).round()}%',
              style: TextStyle(
                fontSize: 14,
                color: _primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderThemeData(
            activeTrackColor: _primaryColor,
            inactiveTrackColor: _dividerColor,
            thumbColor: _primaryColor,
            overlayColor: _primaryColor.withOpacity(0.2),
            trackHeight: 4,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
          ),
          child: Slider(
            value: value,
            min: min,
            max: max,
            divisions: 8,
            onChanged: onChanged,
          ),
        ),
        Text(
          description,
          style: TextStyle(fontSize: 12, color: _textSecondaryColor),
        ),
      ],
    );
  }

  Widget _buildColorField(
    String label,
    Color color,
    Function(Color) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 12, color: _textSecondaryColor),
        ),
        const SizedBox(height: 4),
        GestureDetector(
          onTap: () => _showColorPicker(color, onChanged),
          child: Container(
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: _dividerColor),
            ),
            child: Center(
              child: Text(
                '#${color.value.toRadixString(16).substring(2).toUpperCase()}',
                style: TextStyle(
                  fontSize: 12,
                  color: _getContrastColor(color),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Color _getContrastColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  void _showColorPicker(Color currentColor, Function(Color) onChanged) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: _cardBackgroundColor,
            title: Text(
              '选择颜色',
              style: TextStyle(color: _textPrimaryColor),
            ),
            content: SizedBox(
              width: 320,
              height: 450,
              child: ColorPickerWidget(
                initialColor: currentColor,
                onColorChanged: onChanged,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  '取消',
                  style: TextStyle(color: _textSecondaryColor),
                ),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }
}
