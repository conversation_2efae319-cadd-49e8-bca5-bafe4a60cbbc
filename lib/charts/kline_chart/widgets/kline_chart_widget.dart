import 'package:flutter/material.dart' hide Overlay;
import '../../kline/flexi_kline.dart';
import '../../services/trade_terminal/trader_terminal_service.dart';
import '../controllers/kline_controller.dart';

import 'dart:ui';
import 'package:qubic_exchange/core/index.dart';
import './depth_display_widget.dart'; // 新增导入
import './trading_panel.dart'; // 导入交易面板组件

/// K线图组件，主要的UI入口
class KlineChartWidget extends StatefulWidget {
  final double height;
  final String instrumentId; // 新增：接收币种ID
  final String period; // 新增：接收时间周期
  final Function(KlineController)? onControllerCreated; // 新增：控制器创建回调
  final String? selectedStrategyIdentification; // 新增：接收策略identification
  final int? windowIndex; // 新增：窗口索引
  final Function()? onOrderCreated; //执行订单列表更新回调方法
  final Function(double)? onLatestPriceUpdated; //执行最新价格更新回调方法
  final Function(TradeData)? onTradeDataReceived; //成交数据接收回调方法
  final Function(bool)? onFullScreenToggle; //全屏模式切换回调方法
  final Function(String?)? onStrategyChanged; //策略变化回调方法
  final bool isTrader; // 新增：用户是否为trader

  const KlineChartWidget({
    super.key,
    required this.height,
    required this.instrumentId,
    required this.period,
    this.onControllerCreated,
    this.selectedStrategyIdentification,
    this.windowIndex, // 添加到构造函数
    this.onOrderCreated,
    this.onLatestPriceUpdated,
    this.onTradeDataReceived,
    this.onFullScreenToggle, // 添加到构造函数
    this.onStrategyChanged, // 添加到构造函数
    this.isTrader = false, // 默认非trader
  });

  @override
  State<KlineChartWidget> createState() => _KlineChartWidgetState();
}

/// 定义接口，用于访问FlexiKline控制器
abstract interface class IFlexiKlinePage {
  FlexiKlineController get klineController;
}

class _KlineChartWidgetState extends State<KlineChartWidget>
    implements IFlexiKlinePage {
  late final KlineController _controller;
  double _latestPriceY = 0.0;
  double _latestPriceValue = 0.0;

  // 添加最高价和最低价记录
  double _highestPrice = 0.0;
  double _lowestPrice = 0.0;

  // 添加拖动按钮位置控制变量
  Offset _buttonPosition = const Offset(100, 100); // 按钮初始位置

  // 添加控制交易面板显示的变量
  bool _isPanelVisible = false;

  // 添加加载状态控制变量
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeController();
    _controller.flexiKlineController.onLatestPriceYPositionUpdated =
        _updateLatestPriceInfo;

    // 设置价格范围更新回调
    _controller.onPriceRangeUpdated = _updatePriceRange;

    // 通知父组件控制器已创建
    if (widget.onControllerCreated != null) {
      widget.onControllerCreated!(_controller);
    }
  }

  void _initializeController() {
    _controller = KlineController(
      mainChartHeight: widget.height - 50, // 假设为K线图本身留出的高度，可能需要调整
      initialInstrumentId: widget.instrumentId, // 使用传入的参数
      initialPeriod: widget.period, // 使用传入的参数
      windowIndex: widget.windowIndex, // 传递窗口索引
      onLoadingStateChanged: _onLoadingStateChanged, // 添加加载状态回调
      onTradeDataReceived: _onTradeDataReceived, // 添加成交数据回调
    );

    TraderTerminalService.instance.getTraderApiToken();
    // Future.delayed(const Duration(milliseconds: 2000), () {
    //   _controller.flexiKlineController.setMainSize(Size(500, 200));
    // });
  }

  @override
  void didUpdateWidget(covariant KlineChartWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.instrumentId != oldWidget.instrumentId ||
        widget.period != oldWidget.period) {
      _controller.updateCandleSubscription(widget.instrumentId, widget.period);
    }
    // 如果高度也可能变化，更新KlineController中的高度
    if (widget.height != oldWidget.height) {
      final currentWidth = _controller.flexiKlineController.mainSize.width;
      _controller.flexiKlineController.setMainSize(
        Size(currentWidth, widget.height - 50),
      );
    }
  }

  void _updateLatestPriceInfo(double yPosition, double price) {
    if (mounted) {
      // 避免在K线图还在计算布局时DepthDisplayWidget做不必要的重绘或计算
      // 可以考虑使用微任务或者检查yPosition和price是否有显著变化
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (widget.onLatestPriceUpdated != null) {
          widget.onLatestPriceUpdated!(price.toDouble());
        }
        if (mounted) {
          // Double check mounted after async gap
          setState(() {
            _latestPriceY = yPosition;
            _latestPriceValue = price;
          });
        }
      });
    }
  }

  // 处理价格范围更新的回调函数
  void _updatePriceRange(double highestPrice, double lowestPrice) {
    if (mounted) {
      setState(() {
        _highestPrice = highestPrice;
        _lowestPrice = lowestPrice;
      });
    }
  }

  // 处理加载状态变化的回调函数
  void _onLoadingStateChanged(bool isLoading) {
    if (mounted) {
      setState(() {
        _isLoading = isLoading;
      });
    }
  }

  // 处理成交数据接收的回调函数
  void _onTradeDataReceived(TradeData tradeData) {
    // 将成交数据传递给父组件的回调（如果存在）
    if (widget.onTradeDataReceived != null) {
      widget.onTradeDataReceived!(tradeData);
    }

    // 成交数据现在由KlineController直接传递给DepthDataService处理
    // 这里可以添加其他需要成交数据的组件处理逻辑
  }

  @override
  void dispose() {
    try {
      // 清理回调，避免内存泄漏
      if (_controller.flexiKlineController.onLatestPriceYPositionUpdated ==
          _updateLatestPriceInfo) {
        _controller.flexiKlineController.onLatestPriceYPositionUpdated = null;
      }
    } catch (e) {
      // 忽略已dispose对象的错误
    }

    try {
      _controller.dispose(); // 释放控制器资源
    } catch (e) {
      // 忽略dispose过程中的错误
    }

    super.dispose();
  }

  @override
  FlexiKlineController get klineController => _controller.flexiKlineController;

  @override
  Widget build(BuildContext context) {
    // 更新K线控制器的主题BuildContext以支持模板样式
    _controller.updateThemeContext(context);

    return ClipRRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
        child: Stack(
          children: [
            Container(
              width: double.infinity,
              height: widget.height,
              decoration: BoxDecoration(
                color: context.templateColors.surface.withAlpha(
                  (0.1 * 255).toInt(),
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: context.templateColors.divider,
                  width: 1.0,
                ),
              ),
              child: LayoutBuilder(
                builder: (context, constraints) {
                  // 根据总宽度动态计算深度图宽度
                  final double totalWidth = constraints.maxWidth;
                  final double minDepthWidth = 200; // 深度图最小宽度
                  final double maxDepthWidth = 230; // 深度图最大宽度

                  // 自适应计算深度图宽度：在最小和最大宽度之间按比例分配
                  double depthWidth;
                  if (totalWidth <= 600) {
                    // 小屏幕：使用最小宽度
                    depthWidth = minDepthWidth;
                  } else if (totalWidth >= 1200) {
                    // 大屏幕：使用最大宽度
                    depthWidth = maxDepthWidth;
                  } else {
                    // 中等屏幕：按比例分配（总宽度的20-25%）
                    depthWidth = (totalWidth * 0.18).clamp(
                      minDepthWidth,
                      maxDepthWidth,
                    );
                  }

                  final double klineWidth = totalWidth - depthWidth;

                  return Row(
                    children: [
                      Container(
                        width: klineWidth,
                        child: Stack(
                          children: [
                            Positioned.fill(
                              child: RepaintBoundary(
                                key: const ValueKey('OptimizedKlineChart'),
                                child: FlexiKlineWidget(
                                  controller: _controller.flexiKlineController,
                                  mainBackgroundView: const ColoredBox(
                                    color: Colors.transparent,
                                  ),
                                  onDoubleTap: _setFullScreen,
                                  autoAdaptLayout: false,
                                  isTouchDevice: false,
                                ),
                              ),
                            ),
                            // 加载状态指示器
                            if (_isLoading)
                              Positioned.fill(
                                child: Container(
                                  color: Colors.black.withValues(alpha: 0.3),
                                  child: const Center(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        CircularProgressIndicator(
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                Colors.white,
                                              ),
                                        ),
                                        SizedBox(height: 16),
                                        Text(
                                          '加载K线数据中...',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 14,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                      Container(
                        width: depthWidth,
                        child: DepthDisplayWidget(
                          klineMainCanvasHeight: widget.height - 11,
                          // 传递最新的Y坐标和价格值
                          latestPriceYFromKline: _latestPriceY,
                          latestPriceValueFromKline: _latestPriceValue,
                          symbol: widget.instrumentId,
                          highestPrice: _highestPrice,
                          lowestPrice: _lowestPrice,
                          depthService:
                              _controller.depthDataService, // 传递控制器的深度服务
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),

            // 只有当用户是trader时才显示可拖动的加号按钮
            if (widget.isTrader)
              Positioned(
                left: _buttonPosition.dx,
                top: _buttonPosition.dy,
                child: GestureDetector(
                  onPanUpdate: (details) {
                    setState(() {
                      _buttonPosition = Offset(
                        _buttonPosition.dx + details.delta.dx,
                        _buttonPosition.dy + details.delta.dy,
                      );
                    });
                  },
                  onTap: _showTradingPanel, // 添加点击事件
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.3),
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Icon(Icons.add, color: Colors.white, size: 24),
                  ),
                ),
              ),

            // 只有当用户是trader且面板可见时才显示交易面板
            if (widget.isTrader && _isPanelVisible)
              Positioned(
                left: _buttonPosition.dx,
                top: _buttonPosition.dy,
                child: GestureDetector(
                  // 处理交易面板的拖动
                  onPanUpdate: (details) {
                    setState(() {
                      _buttonPosition = Offset(
                        _buttonPosition.dx + details.delta.dx,
                        _buttonPosition.dy + details.delta.dy,
                      );
                    });
                  },
                  // child: DraggableTradingPanel(
                  //   currentPrice: _latestPriceValue,
                  //   strategyIdentification:
                  //       widget.selectedStrategyIdentification,
                  //   symbol: widget.instrumentId,
                  //   onOrder: (result) {
                  //     // 处理下单结果
                  //     widget.onOrderCreated!();
                  //   },
                  //   onStrategyChanged: widget.onStrategyChanged, // 传递策略变化回调
                  //   onClose: () {
                  //     // 关闭交易面板
                  //     setState(() => _isPanelVisible = false);
                  //   },
                  // ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 全屏切换处理
  void _setFullScreen() {
    // 实现全屏切换逻辑，简单示例
    debugPrint('双击切换全屏');
  }

  // 显示交易面板的方法（简化版）
  void _showTradingPanel() {
    // 只有trader用户才能显示交易面板
    if (widget.isTrader) {
      setState(() {
        _isPanelVisible = !_isPanelVisible; // 切换显示状态
      });
    }
  }
}
