import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qubic_exchange/services/trade_terminal/trader_terminal_service.dart';

/// 可拖动的交易面板组件
class DraggableTradingPanel extends StatefulWidget {
  final double currentPrice;
  final Function(Map<String, dynamic>)? onOrder;
  final VoidCallback? onClose;
  final String? strategyIdentification;
  final String? symbol;
  final Function(String?)? onStrategyChanged;

  const DraggableTradingPanel({
    Key? key,
    this.currentPrice = 0.0,
    this.onOrder,
    this.onClose,
    this.strategyIdentification,
    this.symbol,
    this.onStrategyChanged,
  }) : super(key: key);

  @override
  State<DraggableTradingPanel> createState() => _DraggableTradingPanelState();

  /// 清除策略数据缓存，强制重新加载
  static void clearStrategyCache() {
    _DraggableTradingPanelState._cachedStrategyList.clear();
    _DraggableTradingPanelState._cachedStrategyNameToIdentification.clear();
    _DraggableTradingPanelState._isStrategyDataLoaded = false;
    _DraggableTradingPanelState._lastSelectedStrategy = null;
  }
}

class _DraggableTradingPanelState extends State<DraggableTradingPanel>
    with SingleTickerProviderStateMixin {
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _takeProfitController = TextEditingController();
  final TextEditingController _stopLossController = TextEditingController();
  String? _strategyIdentification;
  // 添加加载状态变量
  bool _isLoading = false;

  // 策略相关变量 - 使用静态变量缓存数据
  static List<String> _cachedStrategyList = [];
  static Map<String, String> _cachedStrategyNameToIdentification = {};
  static bool _isStrategyDataLoaded = false;
  static String? _lastSelectedStrategy; // 保存上次选择的策略

  // 实例变量
  List<String> get _strategyList => _cachedStrategyList;
  String? _selectedStrategy;
  Map<String, String> get _strategyNameToIdentification =>
      _cachedStrategyNameToIdentification;

  // Tab控制器
  late TabController _tabController;

  // 定义统一的文本样式
  final TextStyle _textStyle = const TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: Colors.black,
  );

  final TextStyle _boldTextStyle = const TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.bold,
    color: Colors.black,
  );

  @override
  void initState() {
    super.initState();
    // 初始化TabController
    _tabController = TabController(length: 2, vsync: this);

    // 设置当前价格作为默认开单价
    if (widget.currentPrice > 0 && _priceController.text == '') {
      _priceController.text = widget.currentPrice.toStringAsFixed(2);
    }
    if (widget.strategyIdentification != null) {
      _strategyIdentification = widget.strategyIdentification;
    }
    _loadStrategyList();
  }

  @override
  void dispose() {
    _priceController.dispose();
    _takeProfitController.dispose();
    _stopLossController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadStrategyList() async {
    // 如果数据已经加载过，直接使用缓存数据
    if (_isStrategyDataLoaded) {
      setState(() {
        // 优先使用上次选择的策略，如果不存在则使用第一个
        if (_cachedStrategyList.isNotEmpty) {
          if (_lastSelectedStrategy != null &&
              _cachedStrategyList.contains(_lastSelectedStrategy)) {
            _selectedStrategy = _lastSelectedStrategy;
          } else {
            _selectedStrategy = _cachedStrategyList.first;
          }
          _strategyIdentification =
              _cachedStrategyNameToIdentification[_selectedStrategy];
          widget.onStrategyChanged?.call(_strategyIdentification);
        }
      });
      return;
    }

    final response = await TraderTerminalService.instance.getStrategyList();
    if (response['success']) {
      setState(() {
        // 清空现有映射
        _cachedStrategyNameToIdentification.clear();

        // 从响应数据中提取策略的中文名称
        if (response['data'] is List) {
          _cachedStrategyList =
              (response['data'] as List).map((strategy) {
                String strategyName = '未知策略';

                if (strategy['name'] is List) {
                  // 尝试查找中文名称 (zh_CN 或 zh_cn)
                  var namesList = strategy['name'] as List;
                  var zhItem = namesList.firstWhere(
                    (item) =>
                        item['lang'] == 'zh_CN' || item['lang'] == 'zh_cn',
                    orElse:
                        () =>
                            namesList.isNotEmpty
                                ? namesList.first
                                : {'text': '未知策略'},
                  );
                  strategyName = zhItem['text'].toString();
                }

                // 保存策略名称到identification的映射
                if (strategy['identification'] != null) {
                  _cachedStrategyNameToIdentification[strategyName] =
                      strategy['identification'].toString();
                }

                return strategyName;
              }).toList();

          // 标记数据已加载
          _isStrategyDataLoaded = true;

          // 设置选中的策略：优先使用上次选择的策略，如果不存在则使用第一个
          if (_cachedStrategyList.isNotEmpty) {
            if (_lastSelectedStrategy != null &&
                _cachedStrategyList.contains(_lastSelectedStrategy)) {
              _selectedStrategy = _lastSelectedStrategy;
            } else {
              _selectedStrategy = _cachedStrategyList.first;
            }
            _strategyIdentification =
                _cachedStrategyNameToIdentification[_selectedStrategy];
            widget.onStrategyChanged?.call(_strategyIdentification);
          }
        } else {
          _cachedStrategyList = [];
        }
      });
    }
  }

  void _handleOrder(String action, double size, String? type) {
    if (widget.onOrder != null) {
      try {
        double price = double.tryParse(_priceController.text) ?? 0.0;
        double takeProfit = double.tryParse(_takeProfitController.text) ?? 0.0;
        double stopLoss = double.tryParse(_stopLossController.text) ?? 0.0;

        if (price <= 0) {
          _showErrorMessage('请输入有效的开单价格');
          return;
        }

        // 显示加载状态
        setState(() {
          _isLoading = true;
        });
        double latestPrice = widget.currentPrice;

        Map<String, dynamic> params = {
          "side": action,
          "price":
              type == 'LIMIT' ? price : widget.currentPrice.toStringAsFixed(2),
          "target":
              size > 0
                  ? action == 'BUY'
                      ? latestPrice + size
                      : latestPrice - size
                  : takeProfit,
          "stop_loss":
              size > 0
                  ? action == 'BUY'
                      ? latestPrice - size
                      : latestPrice + size
                  : stopLoss,
          "symbol": widget.symbol?.replaceAll('/USDT', '') ?? 'BTC',
          "strategy": _strategyIdentification ?? '',
          "order_type": type,
          "trader_id": 1,
        };
        // 发送下单请求
        TraderTerminalService.instance
            .placeOrder(params)
            .then((response) {
              // 请求完成，关闭加载状态
              if (mounted) {
                setState(() {
                  _isLoading = false;
                });

                // 检查下单结果
                if (response['success'] == true) {
                  _showSuccessMessage('下单成功');
                  widget.onOrder!(response);
                } else {
                  _showErrorMessage('下单失败: ${response['message']}');
                }
              }
            })
            .catchError((error) {
              // 发生错误，关闭加载状态
              if (mounted) {
                setState(() {
                  _isLoading = false;
                });
                _showErrorMessage('下单出错: $error');
              }
            });
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        _showErrorMessage('输入参数无效: $e');
      }
    }
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontSize: 14)),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(fontSize: 14, color: Colors.white),
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _buildPanel();
  }

  Widget _buildPanel() {
    return Material(
      color: Colors.transparent,
      child: Stack(
        children: [
          Container(
            width: 300,
            height: 500,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.7),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题和关闭按钮
                MouseRegion(
                  cursor: SystemMouseCursors.move,
                  child: GestureDetector(
                    onPanUpdate: (details) {
                      // 向上传递拖动事件
                      if (context
                              .findAncestorWidgetOfExactType<
                                _DraggableDialogContent
                              >() !=
                          null) {
                        final draggableParent =
                            context
                                .findAncestorStateOfType<
                                  _DraggableDialogContentState
                                >();
                        draggableParent?.updatePosition(details.delta);
                      }
                    },
                    child: Container(
                      height: 48,
                      color: Colors.transparent,
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.drag_indicator, color: Colors.black54),
                              const SizedBox(width: 8),
                              Text(
                                '交易面板',
                                style: _boldTextStyle.copyWith(
                                  fontSize: 14,
                                ), // 标题稍大一点
                              ),
                            ],
                          ),
                          IconButton(
                            icon: const Icon(Icons.close, color: Colors.black),
                            onPressed: widget.onClose,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Tab栏
                Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  margin: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 0,
                  ),
                  child: TabBar(
                    controller: _tabController,
                    labelColor: Colors.white,
                    unselectedLabelColor: Colors.black54,
                    indicator: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    indicatorSize: TabBarIndicatorSize.tab,
                    labelStyle: _boldTextStyle.copyWith(fontSize: 12),
                    unselectedLabelStyle: _textStyle.copyWith(fontSize: 12),
                    tabs: const [Tab(text: '策略交易'), Tab(text: '批量交易')],
                  ),
                ),

                // Tab内容
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // 第一个tab - 策略交易
                      _buildStrategyTradingTab(),
                      // 第二个tab - 批量交易
                      _buildBatchTradingTab(),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 加载状态蒙版
          if (_isLoading)
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                      SizedBox(height: 16),
                      Text(
                        '正在下单...',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStrategyTradingTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 策略选择下拉框
          _buildStrategyDropdown(),
          const SizedBox(height: 16),

          // 输入区域
          _buildInputField('开单价格', _priceController, '当前最新价格'),
          const SizedBox(height: 16),

          // 止盈止损输入框（放在同一行）
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 止盈输入框
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('止盈价格', style: _boldTextStyle),
                    const SizedBox(height: 6),
                    TextField(
                      controller: _takeProfitController,
                      style: _textStyle,
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d*\.?\d*$'),
                        ),
                      ],
                      decoration: InputDecoration(
                        hintText: '可选',
                        hintStyle: _textStyle.copyWith(color: Colors.black38),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(5),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 2,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12), // 两个输入框之间的间距
              // 止损输入框
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('止损价格', style: _boldTextStyle),
                    const SizedBox(height: 6),
                    TextField(
                      controller: _stopLossController,
                      style: _textStyle,
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d*\.?\d*$'),
                        ),
                      ],
                      decoration: InputDecoration(
                        hintText: '可选',
                        hintStyle: _textStyle.copyWith(color: Colors.black38),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(5),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 2,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildOrderButton(
                '市价多',
                Colors.red[700]!,
                () => _handleOrder('BUY', 0, 'MARKET'),
              ),
              _buildOrderButton(
                '市价空',
                Colors.green[600]!,
                () => _handleOrder('SELL', 0, 'MARKET'),
              ),
              _buildOrderButton(
                '限价多',
                Colors.red[700]!,
                () => _handleOrder('BUY', 0, 'LIMIT'),
              ),
              _buildOrderButton(
                '限价空',
                Colors.green[600]!,
                () => _handleOrder('SELL', 0, 'LIMIT'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBatchTradingTab() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Center(
        child: Text(
          '批量交易功能\n敬请期待',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 16, color: Colors.black54),
        ),
      ),
    );
  }

  Widget _buildStrategyDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('选择策略', style: _boldTextStyle),
        const SizedBox(height: 6),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(5),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedStrategy,
              isExpanded: true,
              dropdownColor: Colors.white, // 设置弹出层背景为白色
              hint: Text(
                '请选择策略',
                style: _textStyle.copyWith(color: Colors.black38),
              ),
              style: _textStyle.copyWith(color: Colors.black), // 设置选中项文字为黑色
              items:
                  _strategyList.map((String strategy) {
                    return DropdownMenuItem<String>(
                      value: strategy,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        child: Text(
                          strategy,
                          style: _textStyle.copyWith(
                            color: Colors.black,
                          ), // 设置下拉项文字为黑色
                        ),
                      ),
                    );
                  }).toList(),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedStrategy = newValue;
                    _strategyIdentification =
                        _strategyNameToIdentification[newValue];
                    // 保存用户的选择
                    _lastSelectedStrategy = newValue;
                  });
                  widget.onStrategyChanged?.call(_strategyIdentification);
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInputField(
    String label,
    TextEditingController controller,
    String hintText,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: _boldTextStyle),
        const SizedBox(height: 6),
        TextField(
          controller: controller,
          style: _textStyle, // 设置输入文本的样式
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')),
          ],
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: _textStyle.copyWith(color: Colors.black38), // 提示文字样式
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(5)),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 2,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderButton(String text, Color color, VoidCallback onPressed) {
    return GestureDetector(
      onDoubleTap: onPressed, // 改为双击触发
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          text,
          style: const TextStyle(fontSize: 12, color: Colors.white),
        ),
      ),
    );
  }
}

/// 原始的交易面板组件 (保留为兼容，但推荐使用DraggableTradingPanel)
class TradingPanel extends StatelessWidget {
  final double currentPrice;
  final Function(Map<String, dynamic>)? onOrder;

  const TradingPanel({Key? key, this.currentPrice = 0.0, this.onOrder})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DraggableTradingPanel(
      currentPrice: currentPrice,
      onOrder: onOrder,
      onClose: () {
        Navigator.of(context).pop();
      },
    );
  }
}

/// 显示可拖动交易面板的函数
Future<void> showDraggableTradingPanel(
  BuildContext context, {
  required Offset initialPosition,
  required double currentPrice,
  required Function(Map<String, dynamic>) onOrder,
}) async {
  return showDialog(
    context: context,
    barrierColor: Colors.transparent,
    barrierDismissible: false,
    builder:
        (context) => _DraggableDialogContent(
          initialPosition: initialPosition,
          child: DraggableTradingPanel(
            currentPrice: currentPrice,
            onOrder: onOrder,
            onClose: () => Navigator.of(context).pop(),
          ),
        ),
  );
}

/// 可拖动的对话框内容容器
class _DraggableDialogContent extends StatefulWidget {
  final Widget child;
  final Offset initialPosition;

  const _DraggableDialogContent({
    Key? key,
    required this.child,
    required this.initialPosition,
  }) : super(key: key);

  @override
  _DraggableDialogContentState createState() => _DraggableDialogContentState();
}

class _DraggableDialogContentState extends State<_DraggableDialogContent> {
  late Offset _position;

  @override
  void initState() {
    super.initState();
    _position = widget.initialPosition;
  }

  void updatePosition(Offset delta) {
    setState(() {
      _position = Offset(_position.dx + delta.dx, _position.dy + delta.dy);
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    // 确保面板不会超出屏幕边界
    double left = _position.dx;
    double top = _position.dy;

    if (left < 0) left = 0;
    if (top < 0) top = 0;
    if (left > screenSize.width - 100) left = screenSize.width - 100;
    if (top > screenSize.height - 100) top = screenSize.height - 100;

    return Stack(
      children: [Positioned(left: left, top: top, child: widget.child)],
    );
  }
}
