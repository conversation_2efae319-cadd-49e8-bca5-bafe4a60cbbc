import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import '../config/indicator_config/volume_config.dart';
import 'color_picker_widget.dart';

class VolumeConfigWidget extends StatefulWidget {
  final VolumeConfig config;
  final Function(VolumeConfig) onConfigChanged;

  const VolumeConfigWidget({
    super.key,
    required this.config,
    required this.onConfigChanged,
  });

  @override
  State<VolumeConfigWidget> createState() => _VolumeConfigWidgetState();
}

class _VolumeConfigWidgetState extends State<VolumeConfigWidget> {
  late VolumeConfig _config;

  @override
  void initState() {
    super.initState();
    _config = widget.config;
  }

  // 颜色getter - 使用模板主题系统
  Color get _primaryColor => context.templateColors.primary;
  Color get _textPrimaryColor => context.templateColors.textPrimary;
  Color get _textSecondaryColor => context.templateColors.textSecondary;
  Color get _cardBackgroundColor => context.templateColors.cardBackground;
  Color get _dividerColor => context.templateColors.divider;

  void _updateConfig(VolumeConfig newConfig) {
    setState(() {
      _config = newConfig;
    });
    widget.onConfigChanged(newConfig);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        const SizedBox(height: 16),
        _buildEnabledSwitch(),
        const SizedBox(height: 20),
        _buildDisplaySettings(),
        const SizedBox(height: 20),
        _buildColorSettings(),
        const SizedBox(height: 20),
        _buildMASettings(),
      ],
    );
  }

  Widget _buildHeader() {
    return Text(
      '成交量指标',
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: _textPrimaryColor,
      ),
    );
  }

  Widget _buildEnabledSwitch() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '启用成交量指标',
          style: TextStyle(fontSize: 14, color: _textPrimaryColor),
        ),
        Switch(
          value: _config.enabled,
          onChanged: (value) {
            _updateConfig(_config.copyWith(enabled: value));
          },
          activeColor: _primaryColor,
        ),
      ],
    );
  }

  Widget _buildDisplaySettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('显示设置'),
        const SizedBox(height: 12),

        // 显示模式选择
        Row(
          children: [
            Text(
              '显示模式：',
              style: TextStyle(fontSize: 14, color: _textPrimaryColor),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Row(
                children: [
                  _buildModeRadio('area', '面积图'),
                  const SizedBox(width: 20),
                  _buildModeRadio('bars', '柱状图'),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // 透明度设置
        _buildOpacitySlider(),
      ],
    );
  }

  Widget _buildModeRadio(String value, String label) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Radio<String>(
          value: value,
          groupValue: _config.displayMode,
          onChanged: (String? newValue) {
            if (newValue != null) {
              _updateConfig(_config.copyWith(displayMode: newValue));
            }
          },
          activeColor: _primaryColor,
        ),
        Text(
          label,
          style: TextStyle(fontSize: 14, color: _textPrimaryColor),
        ),
      ],
    );
  }

  Widget _buildOpacitySlider() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '透明度',
              style: TextStyle(fontSize: 14, color: _textPrimaryColor),
            ),
            Text(
              '${(_config.opacity * 100).toInt()}%',
              style: TextStyle(
                fontSize: 12,
                color: _textSecondaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: _config.opacity,
          min: 0.1,
          max: 1.0,
          divisions: 9,
          onChanged: (value) {
            _updateConfig(_config.copyWith(opacity: value));
          },
          activeColor: _primaryColor,
        ),
      ],
    );
  }

  Widget _buildColorSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('颜色设置'),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildColorField(
                '上涨颜色',
                _config.bullishColor,
                (color) => _updateConfig(_config.copyWith(bullishColor: color)),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildColorField(
                '下跌颜色',
                _config.bearishColor,
                (color) => _updateConfig(_config.copyWith(bearishColor: color)),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMASettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('移动平均线设置'),
        const SizedBox(height: 12),

        // 显示MA开关
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '显示MA线',
              style: TextStyle(fontSize: 14, color: _textPrimaryColor),
            ),
            Switch(
              value: _config.showMA,
              onChanged: (value) {
                _updateConfig(_config.copyWith(showMA: value));
              },
              activeColor: _primaryColor,
            ),
          ],
        ),

        if (_config.showMA) ...[
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildNumberField(
                  'MA长度',
                  _config.maLength,
                  5,
                  200,
                  (value) => _updateConfig(_config.copyWith(maLength: value)),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDoubleField(
                  'MA线宽',
                  _config.maLineWidth,
                  0.5,
                  5.0,
                  (value) =>
                      _updateConfig(_config.copyWith(maLineWidth: value)),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          _buildColorField(
            'MA线颜色',
            _config.maColor,
            (color) => _updateConfig(_config.copyWith(maColor: color)),
          ),
        ],
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: _textPrimaryColor,
      ),
    );
  }

  Widget _buildNumberField(
    String label,
    int value,
    int min,
    int max,
    Function(int) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 12, color: _textSecondaryColor),
        ),
        const SizedBox(height: 4),
        Container(
          height: 40,
          decoration: BoxDecoration(
            color: _cardBackgroundColor,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: _dividerColor),
          ),
          child: TextFormField(
            initialValue: value.toString(),
            style: TextStyle(fontSize: 14, color: _textPrimaryColor),
            textAlign: TextAlign.center,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 8),
            ),
            onChanged: (text) {
              final newValue = int.tryParse(text);
              if (newValue != null && newValue >= min && newValue <= max) {
                onChanged(newValue);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDoubleField(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 12, color: _textSecondaryColor),
        ),
        const SizedBox(height: 4),
        Container(
          height: 40,
          decoration: BoxDecoration(
            color: _cardBackgroundColor,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: _dividerColor),
          ),
          child: TextFormField(
            initialValue: _formatDoubleValue(value),
            style: TextStyle(fontSize: 14, color: _textPrimaryColor),
            textAlign: TextAlign.center,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 8),
            ),
            onChanged: (text) {
              final newValue = double.tryParse(text);
              if (newValue != null && newValue >= min && newValue <= max) {
                onChanged(newValue);
              }
            },
          ),
        ),
      ],
    );
  }

  String _formatDoubleValue(double value) {
    if (value == value.toInt()) {
      return value.toInt().toString();
    }
    String formatted = value.toStringAsFixed(2);
    formatted = formatted.replaceAll(RegExp(r'\.?0+$'), '');
    return formatted;
  }

  Widget _buildColorField(
    String label,
    Color color,
    Function(Color) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 12, color: _textSecondaryColor),
        ),
        const SizedBox(height: 4),
        GestureDetector(
          onTap: () => _showColorPicker(color, onChanged),
          child: Container(
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: _dividerColor),
            ),
            child: Center(
              child: Text(
                '#${color.value.toRadixString(16).substring(2).toUpperCase()}',
                style: TextStyle(
                  fontSize: 12,
                  color: _getContrastColor(color),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Color _getContrastColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  void _showColorPicker(Color currentColor, Function(Color) onChanged) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: _cardBackgroundColor,
            title: Text(
              '选择颜色',
              style: TextStyle(color: _textPrimaryColor),
            ),
            content: SizedBox(
              width: 320,
              height: 450,
              child: ColorPickerWidget(
                initialColor: currentColor,
                onColorChanged: onChanged,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  '取消',
                  style: TextStyle(color: _textSecondaryColor),
                ),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }
}
