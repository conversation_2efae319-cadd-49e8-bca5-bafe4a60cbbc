import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../../services/data/depth_data_service.dart';
import '../themes/simple_kline_theme.dart';
import 'dart:async';

class DepthDisplayWidget extends StatefulWidget {
  final double klineMainCanvasHeight;
  final double latestPriceYFromKline;
  final double latestPriceValueFromKline;
  final String symbol;
  final double highestPrice;
  final double lowestPrice;
  final DepthDataService? depthService; // 可选的深度服务实例

  const DepthDisplayWidget({
    super.key,
    required this.klineMainCanvasHeight,
    required this.latestPriceYFromKline,
    required this.latestPriceValueFromKline,
    required this.symbol,
    this.highestPrice = 0,
    this.lowestPrice = 0,
    this.depthService, // 可选参数
  });

  @override
  State<DepthDisplayWidget> createState() => _DepthDisplayWidgetState();
}

class _DepthDisplayWidgetState extends State<DepthDisplayWidget> {
  // 档位选项列表
  final List<int> _depthLevelOptions = [
    50,
    100,
    150,
    200,
    250,
    300,
    350,
    400,
    450,
    500,
    1000,
  ];
  // 当前选择的档位数
  int _selectedDepthLevel = 100;

  // 价格聚合精度控制器
  late TextEditingController _aggregationController;
  double _priceAggregation = 1.0;

  // 深度数据服务
  late DepthDataService _depthService;

  // 深度数据
  List<DepthEntry> _depthData = [];

  // 用于滚动控制
  late ScrollController _scrollController;

  // 服务状态
  DepthDataStatus _status = DepthDataStatus.initial;
  String _errorMessage = '';

  // 鼠标悬停状态
  bool _isMouseInView = false;

  // 自动滚动防抖计时器
  Timer? _autoScrollTimer;

  // Layout constants
  static const double _headerRowHeight = 55.0;
  static const double _dividerHeight = 1.0;
  static const double _dataRowHeight = 22.0;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _aggregationController = TextEditingController(
      text: _priceAggregation.toString(),
    );

    // 使用传入的深度服务或创建新的独立实例
    _depthService =
        widget.depthService ??
        DepthDataService(
          instanceId:
              'depth_${widget.symbol}_${DateTime.now().millisecondsSinceEpoch}',
        );

    // 设置初始档位数和聚合精度
    _depthService.setMaxDepthLevels(_selectedDepthLevel);
    _depthService.setPriceAggregation(_priceAggregation);

    // 监听状态变化
    _depthService.statusStream.listen((status) {
      if (mounted) {
        setState(() {
          _status = status;
          if (status == DepthDataStatus.error) {
            _errorMessage = _depthService.errorMessage;
          } else {
            _errorMessage = '';
          }
        });
      }
    });

    // 监听数据变化
    _depthService.dataStream.listen((data) {
      if (mounted) {
        setState(() {
          _depthData = data;
        });

        // 如果鼠标不在视图内，自动滚动到当前价格位置
        if (!_isMouseInView && _depthData.isNotEmpty) {
          _scheduleAutoScroll();
        }
      }
    });

    // 如果有交易对符号，初始化数据
    if (widget.symbol.isNotEmpty) {
      _initializeDepthData();
    }
  }

  @override
  void didUpdateWidget(covariant DepthDisplayWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果交易对变化，重新初始化数据
    if (widget.symbol != oldWidget.symbol && widget.symbol.isNotEmpty) {
      _initializeDepthData();
    }
  }

  @override
  void dispose() {
    // 立即取消计时器
    _autoScrollTimer?.cancel();
    _autoScrollTimer = null;

    // 检查ScrollController是否还有clients，避免disposal时的错误
    if (_scrollController.hasClients) {
      // 停止任何正在进行的动画
      try {
        _scrollController.animateTo(
          _scrollController.offset,
          duration: Duration.zero,
          curve: Curves.linear,
        );
      } catch (e) {
        // 忽略dispose期间的滚动错误
        if (kDebugMode) {
          print('[DepthDisplayWidget] ScrollController dispose warning: $e');
        }
      }
    }

    _scrollController.dispose();
    _aggregationController.dispose();

    // 只有当我们创建了深度服务时才dispose它（没有传入外部服务）
    if (widget.depthService == null) {
      _depthService.dispose();
    }

    super.dispose();
  }

  /// 安排自动滚动（防抖处理）
  void _scheduleAutoScroll() {
    // 如果widget已经dispose，不创建新的Timer
    if (!mounted) return;

    _autoScrollTimer?.cancel();
    _autoScrollTimer = Timer(const Duration(milliseconds: 100), () {
      // 在Timer执行时再次检查mounted状态
      if (mounted) {
        _autoScrollToCurrentPrice();
      }
    });
  }

  /// 计算聚合后的价格
  double _getAggregatedPrice(double originalPrice) {
    if (_priceAggregation <= 0) return originalPrice;

    // 按照聚合精度进行价格聚合
    // 使用向下取整的方式，确保价格聚合到正确的区间
    return (originalPrice / _priceAggregation).floor() * _priceAggregation;
  }

  /// 自动滚动到当前价格位置
  void _autoScrollToCurrentPrice() {
    // 增强的安全检查
    if (!mounted ||
        _isMouseInView ||
        _depthData.isEmpty ||
        widget.latestPriceValueFromKline <= 0 ||
        !_scrollController.hasClients ||
        _scrollController.position.maxScrollExtent == 0) {
      return;
    }

    try {
      // 计算当前价格的聚合价格
      final currentAggregatedPrice = _getAggregatedPrice(
        widget.latestPriceValueFromKline,
      );

      // 查找匹配聚合价格的项目索引
      int? nearCurrentPriceIndex;
      for (int i = 0; i < _depthData.length; i++) {
        final entry = _depthData[i];
        if (entry.price == currentAggregatedPrice) {
          nearCurrentPriceIndex = i;
          break;
        }
      }

      // 如果没有找到匹配的项目，直接返回
      if (nearCurrentPriceIndex == null) {
        return;
      }

      // 计算视图相关参数
      final availableHeight =
          widget.klineMainCanvasHeight - _headerRowHeight - _dividerHeight;
      final currentScrollOffset = _scrollController.offset;
      final itemTopPosition = nearCurrentPriceIndex * _dataRowHeight;
      final itemBottomPosition = itemTopPosition + _dataRowHeight;

      // 计算当前高亮项目在视图中的相对位置
      final viewportTop = currentScrollOffset;
      final viewportBottom = currentScrollOffset + availableHeight;

      // 检查高亮项目是否完全在当前视图内
      final isItemVisible =
          itemTopPosition >= viewportTop &&
          itemBottomPosition <= viewportBottom;

      if (isItemVisible) {
        // 计算高亮项目在视图中的相对位置百分比
        final itemCenterInViewport =
            itemTopPosition + (_dataRowHeight / 2) - viewportTop;
        final relativePosition = itemCenterInViewport / availableHeight;

        // 检查是否在中间40%-60%的区域内
        const double minThreshold = 0.4; // 40%
        const double maxThreshold = 0.6; // 60%

        if (relativePosition >= minThreshold &&
            relativePosition <= maxThreshold) {
          return;
        }
      }

      // 计算滚动到中间位置的偏移量
      final middleOffset = (availableHeight / 2) - (_dataRowHeight / 2);
      final targetOffset = itemTopPosition - middleOffset;

      // 确保偏移量在有效范围内
      final maxScrollExtent = _scrollController.position.maxScrollExtent;
      final clampedOffset = targetOffset.clamp(0.0, maxScrollExtent);

      // 执行动画前的最终检查
      if (!mounted || !_scrollController.hasClients) {
        return;
      }

      // 执行滚动动画
      _scrollController
          .animateTo(
            clampedOffset,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          )
          .catchError((error) {
            // 捕获动画执行期间的错误，避免崩溃
            if (kDebugMode) {
              print('[DepthDisplayWidget] Scroll animation error: $error');
            }
          });
    } catch (e) {
      // 捕获所有可能的异常，避免影响应用稳定性
      // 在调试模式下打印错误信息
      if (kDebugMode) {
        print('[DepthDisplayWidget] Auto scroll error: $e');
      }
    }
  }

  // 初始化深度数据
  void _initializeDepthData() {
    if (widget.symbol.isEmpty) return;
    _depthService.initializeDepthData(widget.symbol);
  }

  // 处理档位变更
  void _onDepthLevelChanged(int? newLevel) {
    if (newLevel != null && newLevel != _selectedDepthLevel) {
      setState(() {
        _selectedDepthLevel = newLevel;
      });
      // 更新服务中的档位限制
      _depthService.setMaxDepthLevels(_selectedDepthLevel);
    }
  }

  // 处理聚合精度变更
  void _onAggregationChanged(String value) {
    final double? newAggregation = double.tryParse(value);
    if (newAggregation != null &&
        newAggregation > 0 &&
        newAggregation != _priceAggregation) {
      setState(() {
        _priceAggregation = newAggregation;
      });
      // 更新服务中的聚合精度
      _depthService.setPriceAggregation(_priceAggregation);
    } else {
      // 如果输入无效，恢复原值
      _aggregationController.text = _priceAggregation.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    final double availableHeightForList =
        widget.klineMainCanvasHeight - _headerRowHeight - _dividerHeight;

    return MouseRegion(
      onEnter: (_) {
        setState(() {
          _isMouseInView = true;
        });
      },
      onExit: (_) {
        setState(() {
          _isMouseInView = false;
        });
        // 鼠标离开时，如果有数据则立即滚动到当前价格
        if (_depthData.isNotEmpty) {
          _scheduleAutoScroll();
        }
      },
      child: Container(
        padding: const EdgeInsets.all(4.0),
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: Column(
          children: [
            _buildHeader(),
            const Divider(height: _dividerHeight, thickness: _dividerHeight),
            SizedBox(
              height: availableHeightForList > 0 ? availableHeightForList : 0,
              child: _buildDataList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataList() {
    // 根据状态显示不同内容
    switch (_status) {
      case DepthDataStatus.initial:
        return const Center(
          child: Text('等待初始化...', style: TextStyle(color: Colors.white54)),
        );
      case DepthDataStatus.loading:
        return const Center(child: CircularProgressIndicator(strokeWidth: 2.0));
      case DepthDataStatus.error:
        return Center(
          child: Text(
            _errorMessage,
            style: const TextStyle(color: Colors.red, fontSize: 11),
            textAlign: TextAlign.center,
          ),
        );
      case DepthDataStatus.loaded:
        if (_depthData.isEmpty) {
          return const Center(
            child: Text('无深度数据', style: TextStyle(color: Colors.white54)),
          );
        }
        return ListView.builder(
          controller: _scrollController,
          itemExtent: _dataRowHeight,
          itemCount: _depthData.length,
          itemBuilder: (context, index) {
            return _buildDataRow(_depthData[index]);
          },
        );
    }
  }

  Widget _buildHeader() {
    const headerStyle = TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 11,
      color: Colors.white70,
    );
    const subtitleStyle = TextStyle(fontSize: 9, color: Colors.white54);
    const dropdownStyle = TextStyle(fontSize: 10, color: Colors.white);

    return SizedBox(
      height: _headerRowHeight,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 1.0, horizontal: 2.0),
        child: Column(
          children: [
            // 聚合精度和档位控制器
            Container(
              margin: const EdgeInsets.only(top: 2, bottom: 2),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('深度数据:', style: subtitleStyle),
                  const SizedBox(width: 5),
                  const Text('聚合:', style: subtitleStyle),
                  const SizedBox(width: 2),
                  Container(
                    width: 40,
                    height: 20,
                    padding: const EdgeInsets.symmetric(horizontal: 2),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(3),
                      border: Border.all(
                        color: Colors.grey.withValues(alpha: 0.5),
                      ),
                    ),
                    child: TextField(
                      controller: _aggregationController,
                      style: const TextStyle(fontSize: 9, color: Colors.white),
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(vertical: 2),
                        isDense: true,
                      ),
                      keyboardType: TextInputType.number,
                      textAlign: TextAlign.center,
                      onSubmitted: _onAggregationChanged,
                    ),
                  ),
                  // const SizedBox(width: 5),
                  // const Text('档位:', style: subtitleStyle),
                  // const SizedBox(width: 2),
                  // Container(
                  //   padding: const EdgeInsets.symmetric(horizontal: 2),
                  //   decoration: BoxDecoration(
                  //     color: Colors.black54,
                  //     borderRadius: BorderRadius.circular(3),
                  //     border: Border.all(color: Colors.grey.withValues(alpha: 0.5)),
                  //   ),
                  //   child: DropdownButton<int>(
                  //     value: _selectedDepthLevel,
                  //     icon: const Icon(Icons.arrow_drop_down, color: Colors.white70, size: 12),
                  //     elevation: 16,
                  //     style: dropdownStyle.copyWith(fontSize: 9),
                  //     underline: Container(height: 0),
                  //     dropdownColor: Colors.black87,
                  //     isDense: true,
                  //     items: _depthLevelOptions.map<DropdownMenuItem<int>>((int value) {
                  //       return DropdownMenuItem<int>(value: value, child: Text('$value'));
                  //     }).toList(),
                  //     onChanged: _onDepthLevelChanged,
                  //   ),
                  // ),
                ],
              ),
            ),
            // 列标题
            Padding(
              padding: const EdgeInsets.only(top: 2),
              child: Row(
                children: [
                  // 成交数据列
                  const Expanded(
                    flex: 1,
                    child: Text(
                      '成交',
                      style: headerStyle,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  // 分隔线
                  Container(
                    width: 1,
                    height: 12,
                    color: Colors.grey.withValues(alpha: 0.5),
                  ),
                  // 价格区域 (中间)
                  const Expanded(
                    flex: 2,
                    child: Text(
                      '价格',
                      style: headerStyle,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  // 分隔线
                  Container(
                    width: 1,
                    height: 12,
                    color: Colors.grey.withValues(alpha: 0.5),
                  ),
                  // 挂单数据列
                  const Expanded(
                    flex: 1,
                    child: Text(
                      '挂单',
                      style: headerStyle,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataRow(DepthEntry entry) {
    // 获取主题颜色
    const theme = SimpleFlexiKlineTheme();
    final buyColor = theme.long; // 上涨颜色（红色）用于买盘
    final sellColor = theme.short; // 下跌颜色（绿色）用于卖盘

    // 样式定义 - 使用主题颜色
    final buyStyle = TextStyle(fontSize: 10, color: buyColor);
    final sellStyle = TextStyle(fontSize: 10, color: sellColor);
    final priceStyle = TextStyle(
      fontSize: 10,
      color: Colors.white.withValues(alpha: 0.9),
    );

    // 判断是否是当前价格附近 - 使用聚合价格精准匹配
    bool isNearCurrentPrice = false;
    if (widget.latestPriceValueFromKline > 0) {
      final currentAggregatedPrice = _getAggregatedPrice(
        widget.latestPriceValueFromKline,
      );
      isNearCurrentPrice = entry.price == currentAggregatedPrice;
    }

    // 当前价格附近的高亮样式
    Color? rowColor;
    if (isNearCurrentPrice) {
      rowColor = Colors.yellow.withValues(alpha: 0.1);
    } else if (entry.isHighlighted) {
      // 高于平均值的挂单高亮显示
      rowColor = Colors.blue.withValues(alpha: 0.1);
    }

    return Container(
      height: _dataRowHeight,
      color: rowColor,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 2.0, horizontal: 2.0),
        child: Row(
          children: [
            // 成交数据列 - 买卖成交量在一列，用颜色区分
            Expanded(
              flex: 1,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 卖方成交 (绿色) - 有数据才显示
                  if (entry.sellTradeVolume != null &&
                      entry.sellTradeVolume! > 0)
                    Flexible(
                      child: Text(
                        entry.sellTradeVolume!.toStringAsFixed(1),
                        style: sellStyle.copyWith(fontSize: 8),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  // 买方成交 (红色) - 有数据才显示
                  if (entry.buyTradeVolume != null && entry.buyTradeVolume! > 0)
                    Flexible(
                      child: Text(
                        entry.buyTradeVolume!.toStringAsFixed(1),
                        style: buyStyle.copyWith(fontSize: 8),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                ],
              ),
            ),
            // 分隔线
            Container(
              width: 1,
              height: _dataRowHeight - 4,
              color: Colors.grey.withValues(alpha: 0.3),
            ),
            // 价格区域 (中间)
            Expanded(
              flex: 2,
              child: Text(
                entry.price.toStringAsFixed(2),
                style:
                    isNearCurrentPrice
                        ? priceStyle.copyWith(
                          color: Colors.yellow,
                          fontWeight: FontWeight.bold,
                        )
                        : priceStyle,
                textAlign: TextAlign.center,
              ),
            ),
            // 分隔线
            Container(
              width: 1,
              height: _dataRowHeight - 4,
              color: Colors.grey.withValues(alpha: 0.3),
            ),
            // 挂单数据列 - 买卖挂单在一列，用颜色区分
            Expanded(
              flex: 1,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 卖方挂单 (绿色) - 有数据才显示
                  if (entry.sellVolume != null && entry.sellVolume! > 0)
                    Flexible(
                      child: Text(
                        entry.sellVolume!.toStringAsFixed(2),
                        style: sellStyle.copyWith(fontSize: 8),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  // 买方挂单 (红色) - 有数据才显示
                  if (entry.buyVolume != null && entry.buyVolume! > 0)
                    Flexible(
                      child: Text(
                        entry.buyVolume!.toStringAsFixed(2),
                        style: buyStyle.copyWith(fontSize: 8),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
