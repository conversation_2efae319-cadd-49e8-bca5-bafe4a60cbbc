import 'package:qubic_exchange/core/index.dart';
import 'package:flutter/material.dart';
import '../config/indicator_config/chart_config.dart';
import 'color_picker_widget.dart';
import '../../../core/templates/template_theme_provider.dart';

class ChartConfigWidget extends StatefulWidget {
  final ChartConfig config;
  final Function(ChartConfig) onConfigChanged;

  const ChartConfigWidget({
    super.key,
    required this.config,
    required this.onConfigChanged,
  });

  @override
  State<ChartConfigWidget> createState() => _ChartConfigWidgetState();
}

class _ChartConfigWidgetState extends State<ChartConfigWidget> {
  late ChartConfig _config;

  // Helper getters for template colors
  Color get _primaryColor => context.templateColors.primary;
  Color get _textPrimaryColor => context.templateColors.textPrimary;
  Color get _textSecondaryColor => context.templateColors.textSecondary;
  Color get _dividerColor => context.templateColors.divider;
  Color get _cardBackgroundColor => context.templateColors.surface;

  @override
  void initState() {
    super.initState();
    _config = widget.config;
  }

  void _updateConfig(ChartConfig newConfig) {
    setState(() {
      _config = newConfig;
    });
    widget.onConfigChanged(newConfig);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        const SizedBox(height: 16),
        _buildEnabledSwitch(),
        const SizedBox(height: 20),
        _buildGridSettings(),
        const SizedBox(height: 20),
        _buildScaleSettings(),
        const SizedBox(height: 20),
        _buildCandleSettings(),
        const SizedBox(height: 20),
        _buildCandleColorSettings(),
        const SizedBox(height: 20),
        _buildBackgroundSettings(),
        const SizedBox(height: 20),
        _buildProxySettings(),
        const SizedBox(height: 20),
        _buildWindowLayoutSettings(),
        const SizedBox(height: 20),
        _buildOHLCDisplaySettings(),
      ],
    );
  }

  Widget _buildHeader() {
    return Text(
      '图表配置',
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: context.templateColors.textPrimary,
      ),
    );
  }

  Widget _buildEnabledSwitch() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '启用图表配置',
          style: TextStyle(
            fontSize: 14,
            color: context.templateColors.textPrimary,
          ),
        ),
        Switch(
          value: _config.enabled,
          onChanged: (value) {
            _updateConfig(_config.copyWith(enabled: value));
          },
          activeColor: context.templateColors.primary,
        ),
      ],
    );
  }

  Widget _buildGridSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('网格线设置'),
        const SizedBox(height: 12),

        // 是否显示网格线
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '显示网格线',
              style: TextStyle(fontSize: 14, color: _textPrimaryColor),
            ),
            Switch(
              value: _config.showGridLines,
              onChanged: (value) {
                _updateConfig(_config.copyWith(showGridLines: value));
              },
              activeColor: _primaryColor,
            ),
          ],
        ),

        const SizedBox(height: 16),

        // 网格线颜色
        _buildColorField(
          '网格线颜色',
          _config.gridLineColor,
          (color) => _updateConfig(_config.copyWith(gridLineColor: color)),
        ),
      ],
    );
  }

  Widget _buildScaleSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('缩放设置'),
        const SizedBox(height: 12),

        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '全局缩放比例',
              style: TextStyle(fontSize: 14, color: _textPrimaryColor),
            ),
            Text(
              '${(_config.globalScale * 100).toInt()}%',
              style: TextStyle(fontSize: 12, color: _textSecondaryColor),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: _config.globalScale,
          min: 0.1,
          max: 1.0,
          divisions: 9,
          onChanged: (value) {
            _updateConfig(_config.copyWith(globalScale: value));
          },
          activeColor: _primaryColor,
        ),
      ],
    );
  }

  Widget _buildCandleSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('蜡烛设置'),
        const SizedBox(height: 12),

        // 成交量轨迹模式开关
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '启用成交量轨迹模式',
              style: TextStyle(fontSize: 14, color: _textPrimaryColor),
            ),
            Switch(
              value: _config.enableVolumeTracksRendering,
              onChanged: (value) {
                _updateConfig(
                  _config.copyWith(enableVolumeTracksRendering: value),
                );
              },
              activeColor: _primaryColor,
            ),
          ],
        ),

        const SizedBox(height: 16),

        // 蜡烛宽度设置
        Row(
          children: [
            Expanded(
              child: _buildNumberSlider(
                '蜡烛最大宽度',
                _config.candleMaxWidth,
                20.0,
                100.0,
                (value) =>
                    _updateConfig(_config.copyWith(candleMaxWidth: value)),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildNumberSlider(
                '蜡烛宽度（轨迹模式）',
                _config.candleWidth,
                10.0,
                50.0,
                (value) => _updateConfig(_config.copyWith(candleWidth: value)),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        Row(
          children: [
            Expanded(
              child: _buildNumberSlider(
                '蜡烛宽度（普通模式）',
                _config.normalCandleWidth,
                3.0,
                20.0,
                (value) =>
                    _updateConfig(_config.copyWith(normalCandleWidth: value)),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildNumberSlider(
                '蜡烛线宽',
                _config.candleLineWidth,
                0.5,
                3.0,
                (value) =>
                    _updateConfig(_config.copyWith(candleLineWidth: value)),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // 蜡烛间距设置
        _buildNumberSlider(
          '普通模式蜡烛间距',
          _config.normalCandleSpacingParts,
          1.0,
          10.0,
          (value) =>
              _updateConfig(_config.copyWith(normalCandleSpacingParts: value)),
        ),
      ],
    );
  }

  Widget _buildCandleColorSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('蜡烛颜色设置'),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildColorField(
                '上涨蜡烛颜色',
                _config.bullCandleColor,
                (color) =>
                    _updateConfig(_config.copyWith(bullCandleColor: color)),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildColorField(
                '下跌蜡烛颜色',
                _config.bearCandleColor,
                (color) =>
                    _updateConfig(_config.copyWith(bearCandleColor: color)),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBackgroundSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('背景设置'),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildColorField(
                '图表背景颜色',
                _config.chartBackgroundColor,
                (color) => _updateConfig(
                  _config.copyWith(chartBackgroundColor: color),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildColorField(
                '标记线条颜色',
                _config.markLineColor,
                (color) =>
                    _updateConfig(_config.copyWith(markLineColor: color)),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildProxySettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('代理设置'),
        const SizedBox(height: 12),

        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '启用代理',
              style: TextStyle(fontSize: 14, color: _textPrimaryColor),
            ),
            Switch(
              value: _config.enableProxy,
              onChanged: (value) {
                _updateConfig(_config.copyWith(enableProxy: value));
              },
              activeColor: _primaryColor,
            ),
          ],
        ),

        const SizedBox(height: 16),

        // IP地址输入框
        _buildTextField(
          'IP地址',
          _config.proxyHost,
          (value) => _updateConfig(_config.copyWith(proxyHost: value)),
        ),

        const SizedBox(height: 16),

        // 端口输入框
        _buildPortTextField(
          '端口',
          _config.proxyPort.toString(),
          (value) => _updateConfig(
            _config.copyWith(proxyPort: int.tryParse(value) ?? 0),
          ),
        ),
      ],
    );
  }

  Widget _buildWindowLayoutSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('窗口布局'),
        const SizedBox(height: 12),

        // 窗口布局模式选择
        _buildLayoutModeSelector(),
      ],
    );
  }

  Widget _buildOHLCDisplaySettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('OHLC数据显示'),
        const SizedBox(height: 12),

        // OHLC显示模式选择
        _buildOHLCModeSelector(),
      ],
    );
  }

  Widget _buildLayoutModeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '布局模式',
          style: TextStyle(fontSize: 12, color: _textSecondaryColor),
        ),
        const SizedBox(height: 8),

        ...WindowLayoutMode.values.map((mode) => _buildLayoutModeOption(mode)),
      ],
    );
  }

  Widget _buildLayoutModeOption(WindowLayoutMode mode) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Radio<WindowLayoutMode>(
            value: mode,
            groupValue: _config.windowLayoutMode,
            onChanged: (value) {
              if (value != null) {
                _updateConfig(_config.copyWith(windowLayoutMode: value));
              }
            },
            activeColor: _primaryColor,
          ),
          const SizedBox(width: 8),
          Text(
            mode.label,
            style: TextStyle(fontSize: 14, color: _textPrimaryColor),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: _textPrimaryColor,
      ),
    );
  }

  Widget _buildColorField(
    String label,
    Color color,
    Function(Color) onColorChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: TextStyle(fontSize: 12, color: _textSecondaryColor)),
        const SizedBox(height: 4),
        GestureDetector(
          onTap: () => _showColorPicker(color, onColorChanged),
          child: Container(
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: _dividerColor),
            ),
            child: Center(
              child: Text(
                '#${color.value.toRadixString(16).substring(2).toUpperCase()}',
                style: TextStyle(
                  fontSize: 12,
                  color: _getContrastColor(color),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showColorPicker(Color currentColor, Function(Color) onChanged) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: _cardBackgroundColor,
            title: Text('选择颜色', style: TextStyle(color: _textPrimaryColor)),
            content: SizedBox(
              width: 320,
              height: 450,
              child: ColorPickerWidget(
                initialColor: currentColor,
                onColorChanged: onChanged,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('取消', style: TextStyle(color: _textSecondaryColor)),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }

  Color _getContrastColor(Color color) {
    // 计算颜色的亮度，决定使用黑色或白色文字
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  Widget _buildNumberSlider(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: TextStyle(fontSize: 12, color: _textSecondaryColor),
            ),
            Text(
              value.toStringAsFixed(1),
              style: TextStyle(
                fontSize: 12,
                color: _textPrimaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Slider(
          value: value.clamp(min, max),
          min: min,
          max: max,
          onChanged: onChanged,
          activeColor: _primaryColor,
        ),
      ],
    );
  }

  Widget _buildTextField(
    String label,
    String value,
    Function(String) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: TextStyle(fontSize: 12, color: _textSecondaryColor)),
        const SizedBox(height: 4),
        TextFormField(
          keyboardType: TextInputType.text,
          textAlign: TextAlign.center,
          initialValue: value,
          decoration: InputDecoration(
            border: OutlineInputBorder(),
            hintText: '127.0.0.1',
          ),
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildPortTextField(
    String label,
    String value,
    Function(String) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: TextStyle(fontSize: 12, color: _textSecondaryColor)),
        const SizedBox(height: 4),
        TextFormField(
          keyboardType: TextInputType.number,
          textAlign: TextAlign.center,
          initialValue: value == '0' ? '' : value,
          decoration: InputDecoration(
            border: OutlineInputBorder(),
            hintText: '1080',
          ),
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildOHLCModeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '显示模式',
          style: TextStyle(fontSize: 12, color: _textSecondaryColor),
        ),
        const SizedBox(height: 8),

        ...OHLCDisplayMode.values.map((mode) => _buildOHLCModeOption(mode)),
      ],
    );
  }

  Widget _buildOHLCModeOption(OHLCDisplayMode mode) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Radio<OHLCDisplayMode>(
            value: mode,
            groupValue: _config.ohlcDisplayMode,
            onChanged: (value) {
              if (value != null) {
                _updateConfig(_config.copyWith(ohlcDisplayMode: value));
              }
            },
            activeColor: _primaryColor,
          ),
          const SizedBox(width: 8),
          Text(
            mode.label,
            style: TextStyle(fontSize: 14, color: _textPrimaryColor),
          ),
        ],
      ),
    );
  }
}
