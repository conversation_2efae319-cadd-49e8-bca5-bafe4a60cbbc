import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import '../config/indicator_config_manager.dart';
import '../config/indicator_config/chart_config.dart';
import '../config/indicator_config/vwap_config.dart';
import '../config/indicator_config/volume_profile_config.dart';
import '../config/indicator_config/volume_profile_chart_config.dart';
import '../config/indicator_config/volume_config.dart';
import 'chart_config_widget.dart';
import 'vwap_config_widget.dart';
import 'volume_profile_config_widget.dart';
import 'volume_profile_chart_config_widget.dart';
import 'volume_config_widget.dart';

class IndicatorSettingsDialog extends StatefulWidget {
  final IndicatorConfigManager configManager;
  final Function(IndicatorConfigManager) onConfigSaved;

  const IndicatorSettingsDialog({
    super.key,
    required this.configManager,
    required this.onConfigSaved,
  });

  @override
  State<IndicatorSettingsDialog> createState() =>
      _IndicatorSettingsDialogState();
}

class _IndicatorSettingsDialogState extends State<IndicatorSettingsDialog> {
  // 颜色getter - 使用模板主题系统
  Color get _primaryColor => context.templateColors.primary;
  Color get _textPrimaryColor => context.templateColors.textPrimary;
  Color get _textSecondaryColor => context.templateColors.textSecondary;
  Color get _cardBackgroundColor => context.templateColors.cardBackground;
  Color get _dividerColor => context.templateColors.divider;
  Color get _inputSecondaryColor => context.templateColors.cardBackground;
  late IndicatorConfigManager _configManager;
  IndicatorType? _selectedIndicatorType;

  @override
  void initState() {
    super.initState();
    _configManager = widget.configManager;
    // 默认选择图表配置
    _selectedIndicatorType = IndicatorType.chartConfig;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.4,
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: BoxDecoration(
          color: _cardBackgroundColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: Row(
                children: [_buildIndicatorSelector(), _buildConfigContent()],
              ),
            ),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _inputSecondaryColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '指标设置',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: _textSecondaryColor,
            ),
          ),
          IconButton(
            icon: Icon(Icons.close, color: _textSecondaryColor),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  Widget _buildIndicatorSelector() {
    return Container(
      width: 200,
      decoration: BoxDecoration(
        color: _inputSecondaryColor,
        border: Border(right: BorderSide(color: _dividerColor, width: 1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 图表配置部分
                _buildConfigItem(IndicatorType.chartConfig),

                // 分隔线
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Divider(color: _dividerColor, height: 1),
                ),

                // 可用指标部分
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                  child: Text(
                    '指标配置',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: _textSecondaryColor,
                    ),
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    itemCount: IndicatorType.values.length - 1, // 减去chartConfig
                    itemBuilder: (context, index) {
                      // 跳过chartConfig，从vwap开始显示
                      final indicatorType = IndicatorType.values[index + 1];
                      return _buildConfigItem(indicatorType);
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfigContent() {
    return Expanded(
      child:
          _selectedIndicatorType != null
              ? SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: _buildIndicatorConfig(_selectedIndicatorType!),
              )
              : _buildEmptyState(),
    );
  }

  Widget _buildConfigItem(IndicatorType indicatorType) {
    final isSelected = _selectedIndicatorType == indicatorType;
    final isEnabled = _configManager.isIndicatorEnabled(indicatorType);

    return ListTile(
      title: Text(
        indicatorType.displayName,
        style: TextStyle(
          fontSize: 14,
          color: isSelected ? _primaryColor : _textPrimaryColor,
          fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
        ),
      ),
      trailing: Container(
        width: 8,
        height: 8,
        decoration: BoxDecoration(
          color: isEnabled ? Colors.green : Colors.grey,
          shape: BoxShape.circle,
        ),
      ),
      selected: isSelected,
      selectedTileColor: _primaryColor.withOpacity(0.1),
      onTap: () {
        setState(() {
          _selectedIndicatorType = indicatorType;
        });
      },
    );
  }

  Widget _buildIndicatorConfig(IndicatorType type) {
    switch (type) {
      case IndicatorType.chartConfig:
        final config =
            _configManager.getConfig<ChartConfig>(type) ?? const ChartConfig();
        return ChartConfigWidget(
          config: config,
          onConfigChanged: (newConfig) {
            _configManager.setConfig(newConfig);
          },
        );
      case IndicatorType.vwap:
        final config =
            _configManager.getConfig<VWAPConfig>(type) ?? const VWAPConfig();
        return VWAPConfigWidget(
          config: config,
          onConfigChanged: (newConfig) {
            _configManager.setConfig(newConfig);
          },
        );
      case IndicatorType.volumeProfile:
        final config =
            _configManager.getConfig<VolumeProfileConfig>(type) ??
            const VolumeProfileConfig();
        return VolumeProfileConfigWidget(
          config: config,
          onConfigChanged: (newConfig) {
            _configManager.setConfig(newConfig);
          },
        );
      case IndicatorType.volumeProfileChart:
        final config =
            _configManager.getConfig<VolumeProfileChartConfig>(type) ??
            const VolumeProfileChartConfig();
        return VolumeProfileChartConfigWidget(
          config: config,
          onConfigChanged: (newConfig) {
            _configManager.setConfig(newConfig);
          },
        );
      case IndicatorType.volume:
        final config =
            _configManager.getConfig<VolumeConfig>(type) ??
            const VolumeConfig();
        return VolumeConfigWidget(
          config: config,
          onConfigChanged: (newConfig) {
            _configManager.setConfig(newConfig);
          },
        );
      // 后续可以添加其他指标的配置界面
      // case IndicatorType.ma:
      //   return MAConfigWidget(...);
      // case IndicatorType.rsi:
      //   return RSIConfigWidget(...);
      default:
        return _buildEmptyState();
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.settings,
            size: 64,
            color: _textSecondaryColor.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            '请选择要配置的指标',
            style: TextStyle(fontSize: 16, color: _textSecondaryColor),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _inputSecondaryColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
        border: Border(top: BorderSide(color: _dividerColor, width: 1)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextButton(
            onPressed: () {
              _configManager.resetToDefaults();
              setState(() {});
            },
            child: Text('重置为默认', style: TextStyle(color: _textSecondaryColor)),
          ),
          Row(
            children: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('取消', style: TextStyle(color: _textSecondaryColor)),
              ),
              const SizedBox(width: 12),
              ElevatedButton(
                onPressed: () async {
                  // 保存配置
                  await _configManager.saveConfigs();
                  widget.onConfigSaved(_configManager);
                  if (mounted) {
                    Navigator.of(context).pop();
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('保存'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
