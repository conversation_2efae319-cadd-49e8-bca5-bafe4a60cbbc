import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import '../config/indicator_config/vwap_config.dart';
import 'color_picker_widget.dart';

class VWAPConfigWidget extends StatefulWidget {
  final VWAPConfig config;
  final Function(VWAPConfig) onConfigChanged;

  const VWAPConfigWidget({
    super.key,
    required this.config,
    required this.onConfigChanged,
  });

  @override
  State<VWAPConfigWidget> createState() => _VWAPConfigWidgetState();
}

class _VWAPConfigWidgetState extends State<VWAPConfigWidget> {
  late VWAPConfig _config;

  @override
  void initState() {
    super.initState();
    _config = widget.config;
  }

  // 颜色getter - 使用模板主题系统
  Color get _primaryColor => context.templateColors.primary;
  Color get _textPrimaryColor => context.templateColors.textPrimary;
  Color get _textSecondaryColor => context.templateColors.textSecondary;
  Color get _cardBackgroundColor => context.templateColors.cardBackground;
  Color get _dividerColor => context.templateColors.divider;

  void _updateConfig(VWAPConfig newConfig) {
    setState(() {
      _config = newConfig;
    });
    widget.onConfigChanged(newConfig);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 启用开关
          _buildSwitchTile(
            '启用VWAP指标',
            _config.enabled,
            (value) => _updateConfig(_config.copyWith(enabled: value)),
          ),

          const SizedBox(height: 20),

          // 会话设置
          _buildSectionTitle('会话设置'),
          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildNumberField(
                  '开始小时',
                  _config.sessionStartHour,
                  0,
                  23,
                  (value) =>
                      _updateConfig(_config.copyWith(sessionStartHour: value)),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildNumberField(
                  '开始分钟',
                  _config.sessionStartMinute,
                  0,
                  59,
                  (value) => _updateConfig(
                    _config.copyWith(sessionStartMinute: value),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          _buildNumberField(
            '时区偏移',
            _config.timezoneOffset,
            -12,
            12,
            (value) => _updateConfig(_config.copyWith(timezoneOffset: value)),
          ),

          const SizedBox(height: 20),

          // 外观设置
          _buildSectionTitle('外观设置'),
          const SizedBox(height: 12),

          _buildColorField(
            'VWAP颜色',
            Color(_config.vwapColor),
            (color) => _updateConfig(_config.copyWith(vwapColor: color.value)),
          ),

          const SizedBox(height: 12),

          _buildColorField(
            '标准差带颜色',
            Color(_config.band1Color),
            (color) => _updateConfig(_config.copyWith(band1Color: color.value)),
          ),

          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildColorField(
                  '上轨颜色',
                  Color(_config.upperBandColor),
                  (color) => _updateConfig(
                    _config.copyWith(upperBandColor: color.value),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildColorField(
                  '下轨颜色',
                  Color(_config.lowerBandColor),
                  (color) => _updateConfig(
                    _config.copyWith(lowerBandColor: color.value),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildDoubleField(
                  'VWAP线宽',
                  _config.vwapLineWidth,
                  0.5,
                  5.0,
                  (value) =>
                      _updateConfig(_config.copyWith(vwapLineWidth: value)),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDoubleField(
                  '标准差带线宽',
                  _config.bandLineWidth,
                  0.5,
                  3.0,
                  (value) =>
                      _updateConfig(_config.copyWith(bandLineWidth: value)),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          _buildDoubleField(
            '填充透明度',
            _config.fillOpacity,
            0.0,
            1.0,
            (value) => _updateConfig(_config.copyWith(fillOpacity: value)),
          ),

          const SizedBox(height: 20),

          // 标准差设置
          _buildSectionTitle('标准差设置'),
          const SizedBox(height: 12),

          _buildText('上轨标准差'),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildDoubleField(
                  '第1层',
                  _config.devUp1,
                  0.1,
                  10.0,
                  (value) => _updateConfig(_config.copyWith(devUp1: value)),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDoubleField(
                  '第2层',
                  _config.devUp2,
                  0.1,
                  10.0,
                  (value) => _updateConfig(_config.copyWith(devUp2: value)),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDoubleField(
                  '第3层',
                  _config.devUp3,
                  0.1,
                  10.0,
                  (value) => _updateConfig(_config.copyWith(devUp3: value)),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildDoubleField(
                  '第4层',
                  _config.devUp4,
                  0.1,
                  10.0,
                  (value) => _updateConfig(_config.copyWith(devUp4: value)),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDoubleField(
                  '第5层',
                  _config.devUp5,
                  0.1,
                  10.0,
                  (value) => _updateConfig(_config.copyWith(devUp5: value)),
                ),
              ),
              const SizedBox(width: 8),
              const Expanded(child: SizedBox()), // 占位
            ],
          ),

          const SizedBox(height: 16),

          _buildText('下轨标准差'),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildDoubleField(
                  '第1层',
                  _config.devDn1,
                  0.1,
                  10.0,
                  (value) => _updateConfig(_config.copyWith(devDn1: value)),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDoubleField(
                  '第2层',
                  _config.devDn2,
                  0.1,
                  10.0,
                  (value) => _updateConfig(_config.copyWith(devDn2: value)),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDoubleField(
                  '第3层',
                  _config.devDn3,
                  0.1,
                  10.0,
                  (value) => _updateConfig(_config.copyWith(devDn3: value)),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildDoubleField(
                  '第4层',
                  _config.devDn4,
                  0.1,
                  10.0,
                  (value) => _updateConfig(_config.copyWith(devDn4: value)),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDoubleField(
                  '第5层',
                  _config.devDn5,
                  0.1,
                  10.0,
                  (value) => _updateConfig(_config.copyWith(devDn5: value)),
                ),
              ),
              const SizedBox(width: 8),
              const Expanded(child: SizedBox()), // 占位
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: _textPrimaryColor,
      ),
    );
  }

  Widget _buildText(String text) {
    return Text(
      text,
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: _textSecondaryColor,
      ),
    );
  }

  Widget _buildSwitchTile(String title, bool value, Function(bool) onChanged) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: _cardBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _dividerColor),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(fontSize: 14, color: _textPrimaryColor),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: _primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildNumberField(
    String label,
    int value,
    int min,
    int max,
    Function(int) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 12, color: _textSecondaryColor),
        ),
        const SizedBox(height: 4),
        Container(
          height: 40,
          decoration: BoxDecoration(
            color: _cardBackgroundColor,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: _dividerColor),
          ),
          child: TextFormField(
            initialValue: value.toString(),
            style: TextStyle(fontSize: 14, color: _textPrimaryColor),
            textAlign: TextAlign.center,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 8),
            ),
            onChanged: (text) {
              final newValue = int.tryParse(text);
              if (newValue != null && newValue >= min && newValue <= max) {
                onChanged(newValue);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDoubleField(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 12, color: _textSecondaryColor),
        ),
        const SizedBox(height: 4),
        Container(
          height: 40,
          decoration: BoxDecoration(
            color: _cardBackgroundColor,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: _dividerColor),
          ),
          child: TextFormField(
            initialValue: _formatDoubleValue(value),
            style: TextStyle(fontSize: 14, color: _textPrimaryColor),
            textAlign: TextAlign.center,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 8),
            ),
            onChanged: (text) {
              final newValue = double.tryParse(text);
              if (newValue != null && newValue >= min && newValue <= max) {
                onChanged(newValue);
              }
            },
          ),
        ),
      ],
    );
  }

  /// 格式化double值，保留适当的精度
  String _formatDoubleValue(double value) {
    // 如果是整数，显示为整数
    if (value == value.toInt()) {
      return value.toInt().toString();
    }

    // 去除多余的尾随零，最多保留3位小数
    String formatted = value.toStringAsFixed(3);
    formatted = formatted.replaceAll(RegExp(r'\.?0+$'), '');

    return formatted;
  }

  Widget _buildColorField(
    String label,
    Color color,
    Function(Color) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 12, color: _textSecondaryColor),
        ),
        const SizedBox(height: 4),
        GestureDetector(
          onTap: () => _showColorPicker(color, onChanged),
          child: Container(
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: _dividerColor),
            ),
            child: Center(
              child: Text(
                '#${color.value.toRadixString(16).substring(2).toUpperCase()}',
                style: TextStyle(
                  fontSize: 12,
                  color: _getContrastColor(color),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Color _getContrastColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  void _showColorPicker(Color currentColor, Function(Color) onChanged) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: _cardBackgroundColor,
            title: Text(
              '选择颜色',
              style: TextStyle(color: _textPrimaryColor),
            ),
            content: SizedBox(
              width: 320,
              height: 450,
              child: ColorPickerWidget(
                initialColor: currentColor,
                onColorChanged: onChanged,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  '取消',
                  style: TextStyle(color: _textSecondaryColor),
                ),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }
}
