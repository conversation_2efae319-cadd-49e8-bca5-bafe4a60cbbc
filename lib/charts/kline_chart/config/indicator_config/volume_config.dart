import 'package:flutter/material.dart';
import '../indicator_config_manager.dart';

/// 成交量指标配置
class VolumeConfig extends IndicatorConfig {
  /// 显示模式：area(面积图) 或 bars(柱状图)
  final String displayMode;

  /// 上涨颜色
  final Color bullishColor;

  /// 下跌颜色
  final Color bearishColor;

  /// 是否显示MA线
  final bool showMA;

  /// MA计算长度
  final int maLength;

  /// MA线颜色
  final Color maColor;

  /// MA线宽度
  final double maLineWidth;

  /// 透明度
  final double opacity;

  const VolumeConfig({
    super.enabled = true,
    super.renderPriority = 20, // 成交量优先级为20
    this.displayMode = 'area', // 'area' 或 'bars'
    this.bullishColor = const Color(0xFF26A69A), // 默认上涨绿色
    this.bearishColor = const Color(0xFFEF5350), // 默认下跌红色
    this.showMA = true,
    this.maLength = 20,
    this.maColor = const Color(0xFFFFEB3B), // 默认黄色MA线
    this.maLineWidth = 1.5,
    this.opacity = 0.8,
  }) : super(type: IndicatorType.volume);

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type.key,
      'enabled': enabled,
      'renderPriority': renderPriority,
      'displayMode': displayMode,
      'bullishColor': bullishColor.value,
      'bearishColor': bearishColor.value,
      'showMA': showMA,
      'maLength': maLength,
      'maColor': maColor.value,
      'maLineWidth': maLineWidth,
      'opacity': opacity,
    };
  }

  factory VolumeConfig.fromJson(Map<String, dynamic> json) {
    return VolumeConfig(
      enabled: json['enabled'] ?? true,
      renderPriority: json['renderPriority'] ?? 20,
      displayMode: json['displayMode'] ?? 'area',
      bullishColor: Color(json['bullishColor'] ?? 0xFF26A69A),
      bearishColor: Color(json['bearishColor'] ?? 0xFFEF5350),
      showMA: json['showMA'] ?? true,
      maLength: json['maLength'] ?? 20,
      maColor: Color(json['maColor'] ?? 0xFFFFEB3B),
      maLineWidth: json['maLineWidth']?.toDouble() ?? 1.5,
      opacity: json['opacity']?.toDouble() ?? 0.8,
    );
  }

  @override
  VolumeConfig copyWith({
    bool? enabled,
    int? renderPriority,
    String? displayMode,
    Color? bullishColor,
    Color? bearishColor,
    bool? showMA,
    int? maLength,
    Color? maColor,
    double? maLineWidth,
    double? opacity,
  }) {
    return VolumeConfig(
      enabled: enabled ?? this.enabled,
      renderPriority: renderPriority ?? this.renderPriority,
      displayMode: displayMode ?? this.displayMode,
      bullishColor: bullishColor ?? this.bullishColor,
      bearishColor: bearishColor ?? this.bearishColor,
      showMA: showMA ?? this.showMA,
      maLength: maLength ?? this.maLength,
      maColor: maColor ?? this.maColor,
      maLineWidth: maLineWidth ?? this.maLineWidth,
      opacity: opacity ?? this.opacity,
    );
  }
}
