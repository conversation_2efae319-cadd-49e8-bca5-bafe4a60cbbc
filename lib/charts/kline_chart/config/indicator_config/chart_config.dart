import 'package:flutter/material.dart';
import '../indicator_config_manager.dart';

/// 窗口布局模式
enum WindowLayoutMode {
  single('single', '单窗口'),
  verticalSplit('vertical', '上下两窗口'),
  horizontalSplit('horizontal', '左右两窗口'),
  fourGrid('four_grid', '四宫格');

  const WindowLayoutMode(this.value, this.label);
  final String value;
  final String label;

  static WindowLayoutMode fromValue(String value) {
    return WindowLayoutMode.values.firstWhere(
      (mode) => mode.value == value,
      orElse: () => WindowLayoutMode.single,
    );
  }
}

/// OHLC数据显示模式
enum OHLCDisplayMode {
  floating('floating', '浮动显示'),
  fixed('fixed', '固定显示');

  const OHLCDisplayMode(this.value, this.label);
  final String value;
  final String label;

  static OHLCDisplayMode fromValue(String value) {
    return OHLCDisplayMode.values.firstWhere(
      (mode) => mode.value == value,
      orElse: () => OHLCDisplayMode.floating,
    );
  }
}

/// 图表配置
class ChartConfig extends IndicatorConfig {
  /// 是否显示网格线
  final bool showGridLines;

  /// 网格线颜色
  final Color gridLineColor;

  /// 全局缩放比例 (0.1-1.0)
  final double globalScale;

  /// 上涨蜡烛颜色
  final Color bullCandleColor;

  /// 下跌蜡烛颜色
  final Color bearCandleColor;

  /// 图表背景颜色
  final Color chartBackgroundColor;

  /// 标记线条颜色
  final Color markLineColor;

  /// 蜡烛最大宽度
  final double candleMaxWidth;

  /// 蜡烛宽度（成交量轨迹模式）
  final double candleWidth;

  /// 蜡烛宽度（普通模式）
  final double normalCandleWidth;

  /// 蜡烛线宽
  final double candleLineWidth;

  /// 是否启用成交量轨迹模式
  final bool enableVolumeTracksRendering;

  /// 普通模式蜡烛间距
  final double normalCandleSpacingParts;

  /// 是否启用代理
  final bool enableProxy;

  /// 代理IP地址
  final String proxyHost;

  /// 代理端口
  final int proxyPort;

  /// 窗口布局模式
  final WindowLayoutMode windowLayoutMode;

  /// OHLC数据显示模式
  final OHLCDisplayMode ohlcDisplayMode;

  const ChartConfig({
    super.enabled = true,
    super.renderPriority = 1, // 图表配置优先级最高
    this.showGridLines = true,
    this.gridLineColor = const Color(0xFF2D344A), // 默认网格线颜色，可通过模板样式覆盖
    this.globalScale = 0.8,
    this.bullCandleColor = const Color(0xFFf45f4e), // 默认上涨红色，可通过模板样式覆盖
    this.bearCandleColor = const Color(0xFF28c99b), // 默认下跌绿色，可通过模板样式覆盖
    this.chartBackgroundColor = const Color(0xFF0E1327), // 默认图表背景色，可通过模板样式覆盖
    this.markLineColor = const Color(0xFFFBFBFB), // 默认标记线颜色，可通过模板样式覆盖
    this.candleMaxWidth = 50.0, // 默认值（不包含scale）
    this.candleWidth = 30.0, // 默认值（不包含scale）
    this.normalCandleWidth = 8.0, // 默认值（不包含scale）
    this.candleLineWidth = 1.0, // 默认值（不包含scale）
    this.enableVolumeTracksRendering = false, // 默认关闭成交量轨迹模式
    this.normalCandleSpacingParts = 3.5,
    this.enableProxy = true,
    this.proxyHost = '127.0.0.1',
    this.proxyPort = 1087,
    this.windowLayoutMode = WindowLayoutMode.single,
    this.ohlcDisplayMode = OHLCDisplayMode.floating, // 默认浮动显示
  }) : super(type: IndicatorType.chartConfig);

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type.key,
      'enabled': enabled,
      'showGridLines': showGridLines,
      'gridLineColor': gridLineColor.value,
      'globalScale': globalScale,
      'bullCandleColor': bullCandleColor.value,
      'bearCandleColor': bearCandleColor.value,
      'chartBackgroundColor': chartBackgroundColor.value,
      'markLineColor': markLineColor.value,
      'candleMaxWidth': candleMaxWidth,
      'candleWidth': candleWidth,
      'normalCandleWidth': normalCandleWidth,
      'candleLineWidth': candleLineWidth,
      'enableVolumeTracksRendering': enableVolumeTracksRendering,
      'normalCandleSpacingParts': normalCandleSpacingParts,
      'enableProxy': enableProxy,
      'proxyHost': proxyHost,
      'proxyPort': proxyPort,
      'windowLayoutMode': windowLayoutMode.value,
      'ohlcDisplayMode': ohlcDisplayMode.value,
    };
  }

  factory ChartConfig.fromJson(Map<String, dynamic> json) {
    return ChartConfig(
      enabled: json['enabled'] ?? true,
      showGridLines: json['showGridLines'] ?? true,
      gridLineColor: Color(json['gridLineColor'] ?? 0xFF2D344A),
      globalScale: json['globalScale']?.toDouble() ?? 0.8,
      bullCandleColor: Color(json['bullCandleColor'] ?? 0xFFf45f4e),
      bearCandleColor: Color(json['bearCandleColor'] ?? 0xFF28c99b),
      chartBackgroundColor: Color(json['chartBackgroundColor'] ?? 0xFF0E1327),
      markLineColor: Color(json['markLineColor'] ?? 0xFFFBFBFB),
      candleMaxWidth: json['candleMaxWidth']?.toDouble() ?? 50.0,
      candleWidth: json['candleWidth']?.toDouble() ?? 30.0,
      normalCandleWidth: json['normalCandleWidth']?.toDouble() ?? 8.0,
      candleLineWidth: json['candleLineWidth']?.toDouble() ?? 1.0,
      enableVolumeTracksRendering: json['enableVolumeTracksRendering'] ?? false,
      normalCandleSpacingParts:
          json['normalCandleSpacingParts']?.toDouble() ?? 3.5,
      enableProxy: json['enableProxy'] ?? false,
      proxyHost: json['proxyHost'] ?? '',
      proxyPort: json['proxyPort'] ?? 0,
      windowLayoutMode: WindowLayoutMode.fromValue(
        json['windowLayoutMode'] ?? 'single',
      ),
      ohlcDisplayMode: OHLCDisplayMode.fromValue(
        json['ohlcDisplayMode'] ?? 'floating',
      ),
    );
  }

  @override
  ChartConfig copyWith({
    bool? enabled,
    bool? showGridLines,
    Color? gridLineColor,
    double? globalScale,
    Color? bullCandleColor,
    Color? bearCandleColor,
    Color? chartBackgroundColor,
    Color? markLineColor,
    double? candleMaxWidth,
    double? candleWidth,
    double? normalCandleWidth,
    double? candleLineWidth,
    bool? enableVolumeTracksRendering,
    double? normalCandleSpacingParts,
    bool? enableProxy,
    String? proxyHost,
    int? proxyPort,
    WindowLayoutMode? windowLayoutMode,
    OHLCDisplayMode? ohlcDisplayMode,
  }) {
    return ChartConfig(
      enabled: enabled ?? this.enabled,
      showGridLines: showGridLines ?? this.showGridLines,
      gridLineColor: gridLineColor ?? this.gridLineColor,
      globalScale: globalScale ?? this.globalScale,
      bullCandleColor: bullCandleColor ?? this.bullCandleColor,
      bearCandleColor: bearCandleColor ?? this.bearCandleColor,
      chartBackgroundColor: chartBackgroundColor ?? this.chartBackgroundColor,
      markLineColor: markLineColor ?? this.markLineColor,
      candleMaxWidth: candleMaxWidth ?? this.candleMaxWidth,
      candleWidth: candleWidth ?? this.candleWidth,
      normalCandleWidth: normalCandleWidth ?? this.normalCandleWidth,
      candleLineWidth: candleLineWidth ?? this.candleLineWidth,
      enableVolumeTracksRendering:
          enableVolumeTracksRendering ?? this.enableVolumeTracksRendering,
      normalCandleSpacingParts:
          normalCandleSpacingParts ?? this.normalCandleSpacingParts,
      enableProxy: enableProxy ?? this.enableProxy,
      proxyHost: proxyHost ?? this.proxyHost,
      proxyPort: proxyPort ?? this.proxyPort,
      windowLayoutMode: windowLayoutMode ?? this.windowLayoutMode,
      ohlcDisplayMode: ohlcDisplayMode ?? this.ohlcDisplayMode,
    );
  }
}
