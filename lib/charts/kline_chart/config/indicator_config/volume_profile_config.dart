import 'package:flutter/material.dart';
import '../indicator_config_manager.dart';

/// 成交量轨迹配置
class VolumeProfileConfig extends IndicatorConfig {
  /// 上涨颜色
  final Color bullishColor;
  
  /// 下跌颜色  
  final Color bearishColor;

  const VolumeProfileConfig({
    bool enabled = false, // 默认不启用
    int renderPriority = 40, // Volume Profile优先级为40
    this.bullishColor = const Color(0xFF26A69A), // 默认绿色
    this.bearishColor = const Color(0xFFEF5350), // 默认红色
  }) : super(
    type: IndicatorType.volumeProfile,
    enabled: enabled,
    renderPriority: renderPriority,
  );

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type.key,
      'enabled': enabled,
      'renderPriority': renderPriority,
      'bullishColor': bullishColor.value,
      'bearishColor': bearishColor.value,
    };
  }

  static VolumeProfileConfig fromJson(Map<String, dynamic> json) {
    return VolumeProfileConfig(
      enabled: json['enabled'] ?? true,
      renderPriority: json['renderPriority'] ?? 40,
      bullishColor: Color(json['bullishColor'] ?? 0xFF26A69A),
      bearishColor: Color(json['bearishColor'] ?? 0xFFEF5350),
    );
  }

  @override
  VolumeProfileConfig copyWith({
    bool? enabled,
    int? renderPriority,
    Color? bullishColor,
    Color? bearishColor,
  }) {
    return VolumeProfileConfig(
      enabled: enabled ?? this.enabled,
      renderPriority: renderPriority ?? this.renderPriority,
      bullishColor: bullishColor ?? this.bullishColor,
      bearishColor: bearishColor ?? this.bearishColor,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VolumeProfileConfig &&
           other.enabled == enabled &&
           other.bullishColor == bullishColor &&
           other.bearishColor == bearishColor;
  }

  @override
  int get hashCode {
    return Object.hash(enabled, bullishColor, bearishColor);
  }

  @override
  String toString() {
    return 'VolumeProfileConfig(enabled: $enabled, bullishColor: $bullishColor, bearishColor: $bearishColor)';
  }
} 