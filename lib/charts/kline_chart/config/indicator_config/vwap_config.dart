import 'package:flutter/material.dart';
import '../../controllers/vwap_indicator_manager.dart';
import '../indicator_config_manager.dart';

/// VWAP指标配置
class VWAPConfig extends IndicatorConfig {
  final int sessionStartHour;
  final int sessionStartMinute;
  final int timezoneOffset;
  final int vwapColor;
  final int band1Color;
  final int upperBandColor;
  final int lowerBandColor;
  final double fillOpacity;
  final double vwapLineWidth;
  final double bandLineWidth;
  final double devUp1;
  final double devUp2;
  final double devUp3;
  final double devUp4;
  final double devUp5;
  final double devDn1;
  final double devDn2;
  final double devDn3;
  final double devDn4;
  final double devDn5;

  const VWAPConfig({
    super.enabled = true,
    super.renderPriority = 30, // VWAP优先级为30
    this.sessionStartHour = 8,
    this.sessionStartMinute = 0,
    this.timezoneOffset = 8,
    this.vwapColor = 0xFFFFFFFF,
    this.band1Color = 0xFF808080,
    this.upperBandColor = 0xFFFF0000,
    this.lowerBandColor = 0xFF00FF00,
    this.fillOpacity = 0.0,
    this.vwapLineWidth = 2.0,
    this.bandLineWidth = 1.0,
    this.devUp1 = 1.28,
    this.devUp2 = 2.01,
    this.devUp3 = 2.51,
    this.devUp4 = 3.09,
    this.devUp5 = 4.01,
    this.devDn1 = 1.28,
    this.devDn2 = 2.01,
    this.devDn3 = 2.51,
    this.devDn4 = 3.09,
    this.devDn5 = 4.01,
  }) : super(type: IndicatorType.vwap);

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type.key,
      'enabled': enabled,
      'renderPriority': renderPriority,
      'sessionStartHour': sessionStartHour,
      'sessionStartMinute': sessionStartMinute,
      'timezoneOffset': timezoneOffset,
      'vwapColor': vwapColor,
      'band1Color': band1Color,
      'upperBandColor': upperBandColor,
      'lowerBandColor': lowerBandColor,
      'fillOpacity': fillOpacity,
      'vwapLineWidth': vwapLineWidth,
      'bandLineWidth': bandLineWidth,
      'devUp1': devUp1,
      'devUp2': devUp2,
      'devUp3': devUp3,
      'devUp4': devUp4,
      'devUp5': devUp5,
      'devDn1': devDn1,
      'devDn2': devDn2,
      'devDn3': devDn3,
      'devDn4': devDn4,
      'devDn5': devDn5,
    };
  }

  factory VWAPConfig.fromJson(Map<String, dynamic> json) {
    return VWAPConfig(
      enabled: json['enabled'] ?? true,
      renderPriority: json['renderPriority'] ?? 30,
      sessionStartHour: json['sessionStartHour'] ?? 8,
      sessionStartMinute: json['sessionStartMinute'] ?? 0,
      timezoneOffset: json['timezoneOffset'] ?? 8,
      vwapColor: json['vwapColor'] ?? 0xFFFFFFFF,
      band1Color: json['band1Color'] ?? 0xFF808080,
      upperBandColor: json['upperBandColor'] ?? 0xFFFF0000,
      lowerBandColor: json['lowerBandColor'] ?? 0xFF00FF00,
      fillOpacity: json['fillOpacity']?.toDouble() ?? 0.0,
      vwapLineWidth: json['vwapLineWidth']?.toDouble() ?? 2.0,
      bandLineWidth: json['bandLineWidth']?.toDouble() ?? 1.0,
      devUp1: json['devUp1']?.toDouble() ?? 1.28,
      devUp2: json['devUp2']?.toDouble() ?? 2.01,
      devUp3: json['devUp3']?.toDouble() ?? 2.51,
      devUp4: json['devUp4']?.toDouble() ?? 3.09,
      devUp5: json['devUp5']?.toDouble() ?? 4.01,
      devDn1: json['devDn1']?.toDouble() ?? 1.28,
      devDn2: json['devDn2']?.toDouble() ?? 2.01,
      devDn3: json['devDn3']?.toDouble() ?? 2.51,
      devDn4: json['devDn4']?.toDouble() ?? 3.09,
      devDn5: json['devDn5']?.toDouble() ?? 4.01,
    );
  }

  @override
  VWAPConfig copyWith({
    bool? enabled,
    int? renderPriority,
    int? sessionStartHour,
    int? sessionStartMinute,
    int? timezoneOffset,
    int? vwapColor,
    int? band1Color,
    int? upperBandColor,
    int? lowerBandColor,
    double? fillOpacity,
    double? vwapLineWidth,
    double? bandLineWidth,
    double? devUp1,
    double? devUp2,
    double? devUp3,
    double? devUp4,
    double? devUp5,
    double? devDn1,
    double? devDn2,
    double? devDn3,
    double? devDn4,
    double? devDn5,
  }) {
    return VWAPConfig(
      enabled: enabled ?? this.enabled,
      renderPriority: renderPriority ?? this.renderPriority,
      sessionStartHour: sessionStartHour ?? this.sessionStartHour,
      sessionStartMinute: sessionStartMinute ?? this.sessionStartMinute,
      timezoneOffset: timezoneOffset ?? this.timezoneOffset,
      vwapColor: vwapColor ?? this.vwapColor,
      band1Color: band1Color ?? this.band1Color,
      upperBandColor: upperBandColor ?? this.upperBandColor,
      lowerBandColor: lowerBandColor ?? this.lowerBandColor,
      fillOpacity: fillOpacity ?? this.fillOpacity,
      vwapLineWidth: vwapLineWidth ?? this.vwapLineWidth,
      bandLineWidth: bandLineWidth ?? this.bandLineWidth,
      devUp1: devUp1 ?? this.devUp1,
      devUp2: devUp2 ?? this.devUp2,
      devUp3: devUp3 ?? this.devUp3,
      devUp4: devUp4 ?? this.devUp4,
      devUp5: devUp5 ?? this.devUp5,
      devDn1: devDn1 ?? this.devDn1,
      devDn2: devDn2 ?? this.devDn2,
      devDn3: devDn3 ?? this.devDn3,
      devDn4: devDn4 ?? this.devDn4,
      devDn5: devDn5 ?? this.devDn5,
    );
  }

  /// 转换为VWAPIndicatorConfig
  VWAPIndicatorConfig toVWAPIndicatorConfig() {
    return VWAPIndicatorConfig(
      sessionStartHour: sessionStartHour,
      sessionStartMinute: sessionStartMinute,
      timezoneOffset: timezoneOffset,
      vwapColor: Color(vwapColor),
      band1Color: Color(band1Color),
      upperBandColor: Color(upperBandColor),
      lowerBandColor: Color(lowerBandColor),
      fillOpacity: fillOpacity,
      vwapLineWidth: vwapLineWidth,
      bandLineWidth: bandLineWidth,
      devUp1: devUp1,
      devUp2: devUp2,
      devUp3: devUp3,
      devUp4: devUp4,
      devUp5: devUp5,
      devDn1: devDn1,
      devDn2: devDn2,
      devDn3: devDn3,
      devDn4: devDn4,
      devDn5: devDn5,
    );
  }
} 