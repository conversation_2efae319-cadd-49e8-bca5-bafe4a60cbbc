import 'package:flutter/material.dart';
import '../indicator_config_manager.dart';

/// 线段样式
enum LineStyle {
  solid('solid', '实线'),
  dashed('dashed', '点状');

  const LineStyle(this.value, this.displayName);
  final String value;
  final String displayName;
}

/// 成交量分布图显示位置
enum VolumeProfileChartPosition {
  left('left', '左侧'),
  right('right', '右侧');

  const VolumeProfileChartPosition(this.value, this.displayName);
  final String value;
  final String displayName;
}

/// 成交量分布图绘制模式
enum VolumeProfileChartDisplayMode {
  total('total', '总和'),
  separated('separated', '区分');

  const VolumeProfileChartDisplayMode(this.value, this.displayName);
  final String value;
  final String displayName;
}

/// 成交量分布图配置
class VolumeProfileChartConfig extends IndicatorConfig {
  /// 绘制位置
  final VolumeProfileChartPosition position;
  
  /// 是否显示POC线
  final bool showPOC;
  
  /// 是否显示VAH线
  final bool showVAH;
  
  /// 是否显示VAL线
  final bool showVAL;
  
  /// POC线颜色
  final Color pocColor;
  
  /// VAH线颜色
  final Color vahColor;
  
  /// VAL线颜色
  final Color valColor;
  
  /// POC线样式
  final LineStyle pocLineStyle;
  
  /// VAH线样式
  final LineStyle vahLineStyle;
  
  /// VAL线样式
  final LineStyle valLineStyle;
  
  /// 成交量柱绘制模式
  final VolumeProfileChartDisplayMode displayMode;
  
  /// 买入成交量颜色（区分模式用）
  final Color upVolumeColor;
  
  /// 卖出成交量颜色（区分模式用）
  final Color downVolumeColor;
  
  /// 总成交量颜色（总和模式用）
  final Color totalVolumeColor;
  
  /// 线条透明度 (0.0-1.0)
  final double opacity;
  
  /// 线条最大宽度百分比 (0.0-1.0)
  final double maxWidthPercent;
  
  /// 数据平滑强度 (0.0-1.0)，0为不平滑，1为最大平滑
  final double smoothingStrength;
  
  /// Value Area百分比 (0.0-1.0)，默认0.7表示70%的成交量
  final double valueAreaPercentage;

  const VolumeProfileChartConfig({
    bool enabled = false, // 默认不启用
    this.position = VolumeProfileChartPosition.right, // 默认右侧
    this.showPOC = true,
    this.showVAH = true,
    this.showVAL = true,
    this.pocColor = Colors.yellow,
    this.vahColor = Colors.blue,
    this.valColor = Colors.blue,
    this.pocLineStyle = LineStyle.solid,
    this.vahLineStyle = LineStyle.solid,
    this.valLineStyle = LineStyle.solid,
    this.displayMode = VolumeProfileChartDisplayMode.separated, // 默认区分模式
    this.upVolumeColor = const Color(0xFFFF5252), // 红色
    this.downVolumeColor = const Color(0xFF4CAF50), // 绿色
    this.totalVolumeColor = const Color(0xFF2196F3), // 蓝色
    this.opacity = 0.8, // 默认透明度80%
    this.maxWidthPercent = 0.3, // 默认最大宽度30%
    this.smoothingStrength = 0.5, // 默认平滑强度50%
    this.valueAreaPercentage = 0.7, // 默认Value Area为70%
  }) : super(
    type: IndicatorType.volumeProfileChart,
    enabled: enabled,
  );

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type.key,
      'enabled': enabled,
      'position': position.value,
      'showPOC': showPOC,
      'showVAH': showVAH,
      'showVAL': showVAL,
      'pocColor': pocColor.value,
      'vahColor': vahColor.value,
      'valColor': valColor.value,
      'pocLineStyle': pocLineStyle.value,
      'vahLineStyle': vahLineStyle.value,
      'valLineStyle': valLineStyle.value,
      'displayMode': displayMode.value,
      'upVolumeColor': upVolumeColor.value,
      'downVolumeColor': downVolumeColor.value,
      'totalVolumeColor': totalVolumeColor.value,
      'opacity': opacity,
      'maxWidthPercent': maxWidthPercent,
      'smoothingStrength': smoothingStrength,
      'valueAreaPercentage': valueAreaPercentage,
    };
  }

  static VolumeProfileChartConfig fromJson(Map<String, dynamic> json) {
    return VolumeProfileChartConfig(
      enabled: json['enabled'] ?? false,
      position: VolumeProfileChartPosition.values.firstWhere(
        (p) => p.value == json['position'],
        orElse: () => VolumeProfileChartPosition.right,
      ),
      showPOC: json['showPOC'] ?? true,
      showVAH: json['showVAH'] ?? true,
      showVAL: json['showVAL'] ?? true,
      pocColor: Color(json['pocColor'] ?? Colors.yellow.value),
      vahColor: Color(json['vahColor'] ?? Colors.blue.value),
      valColor: Color(json['valColor'] ?? Colors.blue.value),
      pocLineStyle: LineStyle.values.firstWhere(
        (s) => s.value == json['pocLineStyle'],
        orElse: () => LineStyle.solid,
      ),
      vahLineStyle: LineStyle.values.firstWhere(
        (s) => s.value == json['vahLineStyle'],
        orElse: () => LineStyle.solid,
      ),
      valLineStyle: LineStyle.values.firstWhere(
        (s) => s.value == json['valLineStyle'],
        orElse: () => LineStyle.solid,
      ),
      displayMode: VolumeProfileChartDisplayMode.values.firstWhere(
        (m) => m.value == json['displayMode'],
        orElse: () => VolumeProfileChartDisplayMode.separated,
      ),
      upVolumeColor: Color(json['upVolumeColor'] ?? 0xFFFF5252),
      downVolumeColor: Color(json['downVolumeColor'] ?? 0xFF4CAF50),
      totalVolumeColor: Color(json['totalVolumeColor'] ?? 0xFF2196F3),
      opacity: (json['opacity'] ?? 0.8).toDouble(),
      maxWidthPercent: (json['maxWidthPercent'] ?? 0.3).toDouble(),
      smoothingStrength: (json['smoothingStrength'] ?? 0.5).toDouble(),
      valueAreaPercentage: (json['valueAreaPercentage'] ?? 0.7).toDouble(),
    );
  }

  @override
  VolumeProfileChartConfig copyWith({
    bool? enabled,
    VolumeProfileChartPosition? position,
    bool? showPOC,
    bool? showVAH,
    bool? showVAL,
    Color? pocColor,
    Color? vahColor,
    Color? valColor,
    LineStyle? pocLineStyle,
    LineStyle? vahLineStyle,
    LineStyle? valLineStyle,
    VolumeProfileChartDisplayMode? displayMode,
    Color? upVolumeColor,
    Color? downVolumeColor,
    Color? totalVolumeColor,
    double? opacity,
    double? maxWidthPercent,
    double? smoothingStrength,
    double? valueAreaPercentage,
  }) {
    return VolumeProfileChartConfig(
      enabled: enabled ?? this.enabled,
      position: position ?? this.position,
      showPOC: showPOC ?? this.showPOC,
      showVAH: showVAH ?? this.showVAH,
      showVAL: showVAL ?? this.showVAL,
      pocColor: pocColor ?? this.pocColor,
      vahColor: vahColor ?? this.vahColor,
      valColor: valColor ?? this.valColor,
      pocLineStyle: pocLineStyle ?? this.pocLineStyle,
      vahLineStyle: vahLineStyle ?? this.vahLineStyle,
      valLineStyle: valLineStyle ?? this.valLineStyle,
      displayMode: displayMode ?? this.displayMode,
      upVolumeColor: upVolumeColor ?? this.upVolumeColor,
      downVolumeColor: downVolumeColor ?? this.downVolumeColor,
      totalVolumeColor: totalVolumeColor ?? this.totalVolumeColor,
      opacity: opacity ?? this.opacity,
      maxWidthPercent: maxWidthPercent ?? this.maxWidthPercent,
      smoothingStrength: smoothingStrength ?? this.smoothingStrength,
      valueAreaPercentage: valueAreaPercentage ?? this.valueAreaPercentage,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VolumeProfileChartConfig &&
           other.enabled == enabled &&
           other.position == position &&
           other.showPOC == showPOC &&
           other.showVAH == showVAH &&
           other.showVAL == showVAL &&
           other.pocColor == pocColor &&
           other.vahColor == vahColor &&
           other.valColor == valColor &&
           other.pocLineStyle == pocLineStyle &&
           other.vahLineStyle == vahLineStyle &&
           other.valLineStyle == valLineStyle &&
           other.displayMode == displayMode &&
           other.upVolumeColor == upVolumeColor &&
           other.downVolumeColor == downVolumeColor &&
           other.totalVolumeColor == totalVolumeColor &&
           other.opacity == opacity &&
           other.maxWidthPercent == maxWidthPercent &&
           other.smoothingStrength == smoothingStrength &&
           other.valueAreaPercentage == valueAreaPercentage;
  }

  @override
  int get hashCode {
    return Object.hash(
      enabled,
      position,
      showPOC,
      showVAH,
      showVAL,
      pocColor,
      vahColor,
      valColor,
      pocLineStyle,
      vahLineStyle,
      valLineStyle,
      displayMode,
      upVolumeColor,
      downVolumeColor,
      totalVolumeColor,
      opacity,
      maxWidthPercent,
      smoothingStrength,
      valueAreaPercentage,
    );
  }

  @override
  String toString() {
    return 'VolumeProfileChartConfig('
           'enabled: $enabled, '
           'position: $position, '
           'showPOC: $showPOC, '
           'showVAH: $showVAH, '
           'showVAL: $showVAL, '
           'displayMode: $displayMode'
           ')';
  }
} 