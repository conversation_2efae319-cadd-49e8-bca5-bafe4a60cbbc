import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'indicator_config/chart_config.dart';
import 'indicator_config/vwap_config.dart';
import 'indicator_config/volume_profile_config.dart';
import 'indicator_config/volume_profile_chart_config.dart';
import 'indicator_config/volume_config.dart';

/// 指标类型枚举
enum IndicatorType {
  chartConfig('ChartConfig', '图表配置'),
  vwap('VWAP', '成交量加权趋势'),
  volumeProfile('VolumeProfile', '成交量轨迹'),
  volumeProfileChart('VolumeProfileChart', '成交量分布图'),
  volume('Volume', '成交量'),
  // 后续可以添加更多指标
  // ma('MA', '移动平均线'),
  // rsi('RSI', '相对强弱指数'),
  ;

  const IndicatorType(this.key, this.displayName);
  final String key;
  final String displayName;
}

/// 指标配置抽象基类
abstract class IndicatorConfig {
  final IndicatorType type;
  final bool enabled;
  final int renderPriority; // 绘制优先级，数值越小优先级越高

  const IndicatorConfig({
    required this.type,
    required this.enabled,
    this.renderPriority = 100, // 默认优先级
  });

  /// 转换为JSON
  Map<String, dynamic> toJson();
  
  /// 从JSON创建实例
  static IndicatorConfig? fromJson(Map<String, dynamic> json) {
    final typeKey = json['type'] as String?;
    if (typeKey == null) return null;

    switch (typeKey) {
      case 'ChartConfig':
        return ChartConfig.fromJson(json);
      case 'VWAP':
        return VWAPConfig.fromJson(json);
      case 'VolumeProfile':
        return VolumeProfileConfig.fromJson(json);
      case 'VolumeProfileChart':
        return VolumeProfileChartConfig.fromJson(json);
      case 'Volume':
        return VolumeConfig.fromJson(json);
      // 后续添加其他指标的解析
      default:
        return null;
    }
  }
  
  /// 创建副本
  IndicatorConfig copyWith();
}



/// 指标配置管理器
class IndicatorConfigManager {
  static const String _prefsKey = 'indicator_configs';
  static const String _windowIndicatorKeyPrefix = 'window_indicator_'; // 窗口指标配置前缀
  static const int _configVersion = 1;

  final Map<IndicatorType, IndicatorConfig> _configs = {};
  final int? _windowIndex; // 窗口索引，null表示全局配置
  
  /// 构造函数
  IndicatorConfigManager({int? windowIndex}) : _windowIndex = windowIndex;
  
  /// 获取指标配置
  T? getConfig<T extends IndicatorConfig>(IndicatorType type) {
    return _configs[type] as T?;
  }

  /// 设置指标配置
  void setConfig(IndicatorConfig config) {
    _configs[config.type] = config;
  }

  /// 获取已启用的指标
  List<IndicatorConfig> getEnabledIndicators() {
    return _configs.values.where((config) => config.enabled).toList();
  }

  /// 检查指标是否启用
  bool isIndicatorEnabled(IndicatorType type) {
    final config = _configs[type];
    return config?.enabled ?? false;
  }

  /// 启用/禁用指标
  void toggleIndicator(IndicatorType type, bool enabled) {
    final config = _configs[type];
    if (config != null) {
      _configs[type] = config.copyWith();
    }
  }

  /// 从本地存储加载配置
  Future<void> loadConfigs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (kDebugMode) {
        print('[IndicatorConfigManager] 开始加载配置，窗口索引: $_windowIndex');
      }
      
      // 先加载全局图表配置
      await _loadGlobalConfigs(prefs);
      
      // 再加载窗口特定的指标配置
      if (_windowIndex != null) {
        await _loadWindowConfigs(prefs, _windowIndex);
      }
      
      // 如果没有找到配置，使用默认值
      _initializeDefaultConfigs();
      
      if (kDebugMode) {
        print('[IndicatorConfigManager] 配置加载完成，窗口索引: $_windowIndex, 已加载配置: ${_configs.keys.map((k) => k.key).toList()}');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('[IndicatorConfigManager] Error loading configs: $e');
      }
      _initializeDefaultConfigs();
    }
  }

  /// 加载全局配置（仅图表配置）
  Future<void> _loadGlobalConfigs(SharedPreferences prefs) async {
    final configsJson = prefs.getString(_prefsKey);
    
    if (configsJson != null) {
      final Map<String, dynamic> data = json.decode(configsJson);
      final int version = data['version'] ?? 0;
      
      if (version == _configVersion) {
        final Map<String, dynamic> configs = data['configs'] ?? {};
        
        // 只加载图表配置，因为它是全局共享的
        final chartConfigData = configs[IndicatorType.chartConfig.key];
        if (chartConfigData != null) {
          final config = IndicatorConfig.fromJson(chartConfigData);
          if (config != null && config.type == IndicatorType.chartConfig) {
            _configs[config.type] = config;
          }
        }
      }
    }
  }

  /// 加载窗口特定配置（指标配置）
  Future<void> _loadWindowConfigs(SharedPreferences prefs, int windowIndex) async {
    final windowConfigKey = '$_windowIndicatorKeyPrefix$windowIndex';
    final configsJson = prefs.getString(windowConfigKey);
    
    if (configsJson != null) {
      final Map<String, dynamic> data = json.decode(configsJson);
      final int version = data['version'] ?? 0;
      
      if (version == _configVersion) {
        final Map<String, dynamic> configs = data['configs'] ?? {};
        
        for (final entry in configs.entries) {
          final config = IndicatorConfig.fromJson(entry.value);
          if (config != null && config.type != IndicatorType.chartConfig) {
            _configs[config.type] = config;
          }
        }
      }
    }
  }

  /// 保存配置到本地存储
  Future<void> saveConfigs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (kDebugMode) {
        print('[IndicatorConfigManager] 开始保存配置，窗口索引: $_windowIndex');
      }
      
      // 分别保存全局配置和窗口配置
      await _saveGlobalConfigs(prefs);
      
      if (_windowIndex != null) {
        await _saveWindowConfigs(prefs, _windowIndex);
      }
      
      if (kDebugMode) {
        print('[IndicatorConfigManager] Configs saved successfully for window $_windowIndex');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[IndicatorConfigManager] Error saving configs: $e');
      }
    }
  }

  /// 保存全局配置（仅图表配置）
  Future<void> _saveGlobalConfigs(SharedPreferences prefs) async {
    final Map<String, dynamic> data = {
      'version': _configVersion,
      'configs': {},
    };
    
    // 只保存图表配置到全局配置
    final chartConfig = _configs[IndicatorType.chartConfig];
    if (chartConfig != null) {
      data['configs'][IndicatorType.chartConfig.key] = chartConfig.toJson();
    }
    
    await prefs.setString(_prefsKey, json.encode(data));
  }

  /// 保存窗口特定配置（指标配置）
  Future<void> _saveWindowConfigs(SharedPreferences prefs, int windowIndex) async {
    final windowConfigKey = '$_windowIndicatorKeyPrefix$windowIndex';
    
    final Map<String, dynamic> data = {
      'version': _configVersion,
      'configs': {},
    };
    
    // 保存除图表配置外的所有指标配置
    for (final entry in _configs.entries) {
      if (entry.key != IndicatorType.chartConfig) {
        data['configs'][entry.key.key] = entry.value.toJson();
      }
    }
    
    await prefs.setString(windowConfigKey, json.encode(data));
  }

  /// 初始化默认配置
  void _initializeDefaultConfigs() {
    // 如果没有图表配置，创建默认配置
    if (!_configs.containsKey(IndicatorType.chartConfig)) {
      _configs[IndicatorType.chartConfig] = const ChartConfig();
    }
    
    // 如果没有VWAP配置，创建默认配置
    if (!_configs.containsKey(IndicatorType.vwap)) {
      _configs[IndicatorType.vwap] = const VWAPConfig();
    }
    
    // 如果没有成交量轨迹配置，创建默认配置
    if (!_configs.containsKey(IndicatorType.volumeProfile)) {
      _configs[IndicatorType.volumeProfile] = const VolumeProfileConfig();
    }
    
    // 如果没有成交量分布图配置，创建默认配置
    if (!_configs.containsKey(IndicatorType.volumeProfileChart)) {
      _configs[IndicatorType.volumeProfileChart] = const VolumeProfileChartConfig();
    }
    
    // 如果没有成交量配置，创建默认配置
    if (!_configs.containsKey(IndicatorType.volume)) {
      _configs[IndicatorType.volume] = const VolumeConfig();
    }
  }

  /// 重置所有配置为默认值
  void resetToDefaults() {
    _configs.clear();
    _initializeDefaultConfigs();
  }

  /// 获取所有支持的指标类型
  List<IndicatorType> getAllIndicatorTypes() {
    return IndicatorType.values;
  }
} 