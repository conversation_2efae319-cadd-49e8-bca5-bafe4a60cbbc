import 'package:flutter/material.dart';
import '../../kline/flexi_kline.dart';
import '../../kline/indicators/volume/volume_indicator.dart';
import '../../kline/indicators/volume_profile/volume_profile.dart';
import '../themes/simple_kline_theme.dart';

/// 自定义配置实现，用于创建FlexiKline的各种配置对象
class DefaultConfiguration implements IConfiguration {
  @override
  final IFlexiKlineTheme theme;
  final double mainChartHeight; // 新增：主图高度参数
  final bool _enableVolumeTracksRendering; // 是否启用成交量轨迹绘制

  @override
  final String configKey = 'data_dashboard';

  DefaultConfiguration({
    required this.theme,
    required this.mainChartHeight,
    bool enableVolumeTracksRendering = false, // 默认关闭成交量轨迹绘制
  }) : _enableVolumeTracksRendering = enableVolumeTracksRendering;

  @override
  Map<String, dynamic>? getConfig(String key) {
    return null; // 简化实现，不从缓存读取配置
  }

  @override
  Future<bool> setConfig(String key, Map<String, dynamic> value) async {
    return true; // 简化实现，不保存配置
  }

  @override
  FlexiKlineConfig generateFlexiKlineConfig([Map<String, dynamic>? origin]) {
    // 创建默认配置
    return FlexiKlineConfig(
      grid: _genGridConfig(),
      setting: _genSettingConfig(),
      gesture: _genGestureConfig(),
      cross: _genCrossConfig(),
      draw: _genDrawConfig(),
      mainIndicator: _genMainIndicator(),
      sub: {},
    );
  }

  /// 生成网格配置，定义网格线的样式和间距
  GridConfig _genGridConfig() {
    return GridConfig(
      show: true,
      horizontal: GridAxis(
        show: true,
        count: 8, // 水平线条数
        line: LineConfig(
          type: LineType.dashed, // 使用虚线
          dashes: const [1, 3], // 点状虚线
          paint: PaintConfig(
            color: theme.gridLine,
            strokeWidth: 0.4 * theme.pixel,
          ), // 减小线宽
        ),
      ),
      vertical: GridAxis(
        show: true,
        count: 8, // 垂直线条数
        line: LineConfig(
          type: LineType.dashed, // 使用虚线
          dashes: const [1, 3], // 点状虚线
          paint: PaintConfig(
            color: theme.gridLine,
            strokeWidth: 0.4 * theme.pixel,
          ), // 减小线宽
        ),
      ),
      isAllowDragIndicatorHeight: true, // 允许拖动改变指标高度
      dragHitTestMinDistance: 10 * theme.scale, // 拖动最小距离
      dragLine: LineConfig(
        type: LineType.dashed, // 使用虚线
        dashes: const [3, 5], // 点状虚线
        length: 20, // 虚线长度
        paint: PaintConfig(
          color: theme.markLine,
          strokeWidth: theme.pixel * 5,
        ), // 设置虚线颜色和宽度
      ),
      dragLineOpacity: 0.1,
      ticksText: TextAreaConfig(
        style: TextStyle(
          fontSize: 12,
          overflow: TextOverflow.ellipsis,
          height: 1.2,
        ),
        textAlign: TextAlign.end,
        padding: EdgeInsets.symmetric(horizontal: 2 * theme.scale),
      ),
    );
  }

  /// 生成K线图基本设置，如蜡烛宽度等
  SettingConfig _genSettingConfig() {
    // 将主题转换为我们的具体类型以访问扩展的配置
    final customTheme =
        theme is SimpleFlexiKlineTheme ? theme as SimpleFlexiKlineTheme : null;

    return SettingConfig(
      pixel: theme.pixel,
      candleMaxWidth:
          customTheme?.candleMaxWidth ?? (50 * theme.scale), // 从主题读取蜡烛最大宽度
      candleWidth:
          customTheme?.candleWidth ?? (30 * theme.scale), // 从主题读取蜡烛宽度（成交量轨迹模式）
      normalCandleWidth:
          customTheme?.normalCandleWidth ??
          (8 * theme.scale), // 从主题读取蜡烛宽度（普通模式）
      candleHollowBarBorderWidth: 1 * theme.scale, // 空心蜡烛边框宽度
      candleLineWidth:
          customTheme?.candleLineWidth ?? (1 * theme.scale), // 从主题读取蜡烛线宽
      firstCandleInitOffset: 1.3, // 设置首个蜡烛偏移量为1.3
      opacity: 0.1, // 透明度
      loading: LoadingConfig(), // 加载状态配置
      // New configurations for price level tracks:
      priceLevelGraphicOffset: 1.0 * theme.scale, // Offset from candle body
      candleBodyProportion:
          0.30, // Candle body is 30% of candleWidth (slot width)
      trackWidthMultiplierToBody:
          2.45, // Track width is 2.45x candle body width
      sellTrackColor: Color(0xFF28c99b), // 默认卖出轨迹颜色，可通过模板样式覆盖
      buyTrackColor: Color(0xFFf45f4e), // 默认买入轨迹颜色，可通过模板样式覆盖
      // 成交量轨迹绘制开关配置
      enableVolumeTracksRendering:
          customTheme?.enableVolumeTracksRendering ??
          _enableVolumeTracksRendering, // 从主题读取成交量轨迹模式
      candleSpacingParts: 1, // 成交量轨迹模式：间距更大
      normalCandleSpacingParts:
          customTheme?.normalCandleSpacingParts ?? 3.5, // 从主题读取普通模式蜡烛间距
    );
  }

  /// 生成手势配置，控制滑动、缩放等交互行为
  GestureConfig _genGestureConfig() {
    return GestureConfig(
      isInertialPan: true, // 启用惯性滑动
      tolerance: ToleranceConfig(),
      loadMoreWhenNoEnoughCandles: 200, // 蜡烛数不足时加载更多
      scalePosition: ScalePosition.auto, // 缩放位置自动
      scaleSpeed: 10, // 缩放速度
      zoomSpeed: 1, // 放大速度
    );
  }

  /// 生成十字线配置，控制十字线的样式
  CrossConfig _genCrossConfig() {
    return CrossConfig(
      enable: true, // 启用十字线
      crosshair: LineConfig(
        paint: PaintConfig(strokeWidth: 0.5 * theme.scale), // 设置十字线宽度
        type: LineType.dashed, // 使用虚线
        dashes: const [5, 3], // 点状虚线
      ),
      crosspoint: PointConfig(
        radius: 2 * theme.scale, // 点状配置
        width: 0 * theme.scale, // 点状宽度
        borderWidth: 2 * theme.scale, // 点状边框宽度
        borderColor: theme.drawColor.withAlpha(
          (0.5 * 255).toInt(),
        ), // 使用 withAlpha 替换 withOpacity
      ),
      ticksText: TextAreaConfig(
        style: TextStyle(
          fontSize: 12,
          overflow: TextOverflow.ellipsis,
          height: 1.2,
        ),
        textAlign: TextAlign.end,
        padding: EdgeInsets.all(2 * theme.scale),
      ),
      spacing: 4, // 十字线间距
    );
  }

  /// 生成绘图配置，用于在K线图上绘制趋势线等
  DrawConfig _genDrawConfig() {
    return DrawConfig(
      enable: true, // 启用绘图功能
      crosshair: LineConfig(
        paint: PaintConfig(strokeWidth: 0.5 * theme.scale), // 设置十字线宽度
        type: LineType.dashed, // 使用虚线
        dashes: const [5, 3], // 点状虚线
      ),
      crosspoint: PointConfig(
        radius: 2 * theme.scale, // 点状配置
        width: 0 * theme.scale, // 点状宽度
        borderWidth: 2 * theme.scale, // 点状边框宽度
        borderColor: theme.drawColor.withAlpha(
          (0.5 * 255).toInt(),
        ), // 使用 withAlpha 替换 withOpacity
      ),
      drawLine: LineConfig(
        paint: PaintConfig(
          strokeWidth: 1 * theme.scale, // 设置趋势线宽度
          color: theme.drawColor, // 设置趋势线颜色
        ),
        type: LineType.solid,
        dashes: [5, 3],
      ),
      drawPoint: PointConfig(
        radius: 9 * theme.scale, // 点状配置
        width: 0 * theme.scale, // 点状宽度
        color: const Color(0xFFFFFFFF), // 点状颜色
        borderWidth: 1 * theme.scale, // 点状边框宽度
        borderColor: theme.drawColor, // 点状边框颜色
      ),
      ticksText: TextAreaConfig(
        style: TextStyle(
          color: const Color(0xFFFFFFFF), // 设置文本颜色
          fontSize: 12,
          fontWeight: FontWeight.normal,
          height: 1.2,
        ),
        padding: EdgeInsets.all(2 * theme.scale),
        border: BorderSide.none,
        borderRadius: BorderRadius.all(Radius.circular(2 * theme.scale)),
      ),
      spacing: 1 * theme.scale,
      ticksGapBgOpacity: 0.1,
      hitTestMinDistance: 10 * theme.scale,
      magnetMinDistance: 10 * theme.scale,
    );
  }

  /// 生成主图指标配置
  MainPaintObjectIndicator _genMainIndicator() {
    return MainPaintObjectIndicator(
      size: Size(double.infinity, mainChartHeight - 50), // 使用传入的高度
      padding: EdgeInsets.only(top: 50, bottom: 50), // 恢复内边距
    );
  }

  /// 生成标记配置，如最高最低价标记
  MarkConfig _genDefaultMarkConfig() {
    return MarkConfig(
      show: true, // 显示标记
      spacing: 1 * theme.scale, // 标记间距
      line: LineConfig(
        paint: PaintConfig(
          strokeWidth: 1 * theme.scale, // 设置标记线宽度
          color: theme.markLine, // 设置标记线颜色
        ),
        type: LineType.dashed, // 使用虚线
        dashes: const [3, 2], // 虚线样式
      ),
      text: TextAreaConfig(
        style: TextStyle(
          fontSize: 16,
          color: theme.textColor,
          overflow: TextOverflow.ellipsis,
          height: 2,
        ),
      ),
    );
  }

  /// 生成自定义价格标记配置
  MarkConfig _genPriceMarkConfig() {
    return MarkConfig(
      show: false, // 显示标记
      spacing: 2 * theme.scale, // 增加标记间距
      line: LineConfig(
        paint: PaintConfig(
          strokeWidth: 1.5 * theme.scale, // 增加线宽
          color: theme.gridLine, // 使用标记线颜色
        ),
        type: LineType.dashed, // 使用虚线
        dashes: const [3, 2], // 虚线样式
      ),
      text: TextAreaConfig(
        style: TextStyle(
          fontSize: 12,
          color: theme.textColor,
          overflow: TextOverflow.ellipsis,
          height: 1.2,
        ),
        padding: EdgeInsets.all(2 * theme.scale),
        border: BorderSide(color: theme.gridLine, width: 1 * theme.scale),
        borderRadius: BorderRadius.all(Radius.circular(4 * theme.scale)),
        background: theme.markLine.withAlpha(
          (0.1 * 255).toInt(),
        ), // 使用 withAlpha 替换 withOpacity
      ),
    );
  }

  @override
  IndicatorBuilder<CandleBaseIndicator> get candleIndicatorBuilder {
    return (json) {
      // 返回默认的蜡烛图指标
      return CandleIndicator(
        height: 10, // 恢复固定高度
        padding: const EdgeInsets.only(
          left: 0, // 减小左侧内边距
          top: 0, // 减小顶部内边距
          right: 50, // 减小右侧内边距, 原为 40
          bottom: 0, // 减小底部内边距
        ),
        high: _genPriceMarkConfig(), // 使用自定义价格标记
        low: _genPriceMarkConfig(), // 使用相同的自定义价格标记
        last: _genDefaultMarkConfig(), // 最后价格标记
        latest: _genDefaultMarkConfig(), // 最新价格标记
        countDown: TextAreaConfig(
          style: TextStyle(
            fontSize: 16,
            color: theme.textColor,
            overflow: TextOverflow.ellipsis,
            height: 1.2,
          ),
        ),
      );
    };
  }

  @override
  IndicatorBuilder<TimeBaseIndicator> get timeIndicatorBuilder {
    return (json) {
      // 返回默认的时间指标
      return TimeIndicator(
        height: 25, // 减小时间指标高度，为K线图留出更多空间
        padding: const EdgeInsets.only(
          left: 0, // 减小左侧内边距，与蜡烛图保持一致
          top: 0,
          right: 0, // 减小右侧内边距，与蜡烛图保持一致
          bottom: 0,
        ),
        position: DrawPosition.middle, // 位于底部
        timeTick: TextAreaConfig(
          style: TextStyle(
            fontSize: 12,
            color: theme.textColor,
            overflow: TextOverflow.ellipsis,
            height: 1.2,
          ),
        ),
      );
    };
  }

  @override
  Map<IIndicatorKey, IndicatorBuilder> get mainIndicatorBuilders => {
    const FlexiIndicatorKey('vwap_bands', label: 'VWAP Bands'):
        (json) => VWAPBandsIndicator(
          height: 300, // 主图高度
          padding: const EdgeInsets.only(left: 0, top: 0, right: 50, bottom: 0),
          // 可以从json配置中读取参数，或使用默认值
          devUp1: json?['devUp1']?.toDouble() ?? 1.28,
          devDn1: json?['devDn1']?.toDouble() ?? 1.28,
          devUp2: json?['devUp2']?.toDouble() ?? 2.01,
          devDn2: json?['devDn2']?.toDouble() ?? 2.01,
          devUp3: json?['devUp3']?.toDouble() ?? 2.51,
          devDn3: json?['devDn3']?.toDouble() ?? 2.51,
          devUp4: json?['devUp4']?.toDouble() ?? 3.09,
          devDn4: json?['devDn4']?.toDouble() ?? 3.09,
          devUp5: json?['devUp5']?.toDouble() ?? 4.01,
          devDn5: json?['devDn5']?.toDouble() ?? 4.01,
          showDv2: json?['showDv2']?.toBool() ?? true,
          showDv3: json?['showDv3']?.toBool() ?? true,
          showDv4: json?['showDv4']?.toBool() ?? false,
          showDv5: json?['showDv5']?.toBool() ?? false,
          showPrevVWAP: json?['showPrevVWAP']?.toBool() ?? false,
          vwapColor: Colors.black,
          band1Color: Colors.grey,
          upperBandColor: Colors.red,
          lowerBandColor: Colors.green,
          fillOpacity: json?['fillOpacity']?.toDouble() ?? 0.1,
          vwapLineWidth: json?['vwapLineWidth']?.toDouble() ?? 1.0,
          bandLineWidth: json?['bandLineWidth']?.toDouble() ?? 1.0,
          // 交易会话配置：中国时区，早上8点开始
          sessionStartHour: json?['sessionStartHour']?.toInt() ?? 8,
          sessionStartMinute: json?['sessionStartMinute']?.toInt() ?? 0,
          timezoneOffset: json?['timezoneOffset']?.toInt() ?? 8, // 中国时区 UTC+8
        ),
    const FlexiIndicatorKey('volume', label: 'Volume'):
        (json) => VolumeIndicator(
          height: 80, // 成交量指标高度
          zIndex: 1, // 在K线上方显示
          padding: const EdgeInsets.only(left: 0, top: 0, right: 50, bottom: 0),
          displayMode: json?['displayMode'] ?? 'area',
          bullishColor: Color(json?['bullishColor'] ?? 0xFF26A69A),
          bearishColor: Color(json?['bearishColor'] ?? 0xFFEF5350),
          showMA: json?['showMA'] ?? true,
          maLength: json?['maLength'] ?? 20,
          maColor: Color(json?['maColor'] ?? 0xFFFFEB3B),
          maLineWidth: json?['maLineWidth']?.toDouble() ?? 1.5,
          opacity: json?['opacity']?.toDouble() ?? 0.8,
        ),
    const FlexiIndicatorKey('volume_profile', label: 'Volume Profile'):
        (json) => VolumeProfileIndicator(
          height: 200, // Volume Profile指标高度
          zIndex: 0, // 在K线层级显示
          padding: const EdgeInsets.only(left: 0, top: 0, right: 50, bottom: 0),
          config: VolumeProfileConfig(
            showPOC: json?['showPOC'] ?? true,
            showVAH: json?['showVAH'] ?? true,
            showVAL: json?['showVAL'] ?? true,
            pocColor: Color(json?['pocColor'] ?? Colors.yellow.value),
            vahColor: Color(json?['vahColor'] ?? Colors.blue.value),
            valColor: Color(json?['valColor'] ?? Colors.blue.value),
            displayMode:
                VolumeProfileDisplayMode.values[json?['displayMode'] ?? 0],
            upVolumeColor: Color(json?['upVolumeColor'] ?? 0xFFFF5252),
            downVolumeColor: Color(json?['downVolumeColor'] ?? 0xFF4CAF50),
            totalVolumeColor: Color(json?['totalVolumeColor'] ?? 0xFF2196F3),
            pocLineWidth: json?['pocLineWidth']?.toDouble() ?? 1.5,
            vahLineWidth: json?['vahLineWidth']?.toDouble() ?? 1.0,
            valLineWidth: json?['valLineWidth']?.toDouble() ?? 1.0,
            volumeOpacity: json?['volumeOpacity']?.toDouble() ?? 0.8,
            lineOpacity: json?['lineOpacity']?.toDouble() ?? 1.0,
            priceZones: json?['priceZones'] ?? 100,
            valueAreaPercentage:
                json?['valueAreaPercentage']?.toDouble() ?? 0.68,
            maxWidthPercent: json?['maxWidthPercent']?.toDouble() ?? 0.3,
          ),
        ),
  }; // 主图指标构建器映射

  @override
  Map<IIndicatorKey, IndicatorBuilder> get subIndicatorBuilders => {}; // 副图指标构建器映射

  @override
  Map<IDrawType, DrawObjectBuilder> get drawObjectBuilders => {}; // 绘图对象构建器映射
}
