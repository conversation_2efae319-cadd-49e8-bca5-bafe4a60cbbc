import 'package:flutter/material.dart';
import '../../kline/flexi_kline.dart';
import '../config/indicator_config_manager.dart';
import '../config/indicator_config/chart_config.dart';
import '../../../core/templates/template_theme_provider.dart';

/// 自定义主题实现，定义K线图的所有颜色和样式
class SimpleFlexiKlineTheme implements IFlexiKlineTheme {
  final ChartConfig? _chartConfig;
  final IndicatorConfigManager? _configManager;
  final BuildContext? _context;

  // 全局配置管理器实例，用于动态读取配置
  static IndicatorConfigManager? _globalConfigManager;

  const SimpleFlexiKlineTheme({
    ChartConfig? chartConfig,
    IndicatorConfigManager? configManager,
    BuildContext? context,
  }) : _chartConfig = chartConfig,
       _configManager = configManager,
       _context = context;

  /// 设置全局配置管理器
  static void setGlobalConfigManager(IndicatorConfigManager configManager) {
    _globalConfigManager = configManager;
  }

  /// 获取全局配置管理器
  static IndicatorConfigManager? get globalConfigManager =>
      _globalConfigManager;

  /// 获取当前图表配置
  ChartConfig? get _currentChartConfig {
    if (_chartConfig != null) return _chartConfig;

    // 先尝试从传入的配置管理器读取
    if (_configManager != null) {
      return _configManager.getConfig<ChartConfig>(IndicatorType.chartConfig);
    }

    // 最后尝试从全局配置管理器读取
    try {
      return _globalConfigManager?.getConfig<ChartConfig>(
        IndicatorType.chartConfig,
      );
    } catch (e) {
      return null;
    }
  }

  @override
  double get scale => _currentChartConfig?.globalScale ?? 0.8; // 全局缩放

  @override
  double get pixel => 1.0; // 像素比

  @override
  double setDp(num size) => size.toDouble() * scale;

  @override
  double setSp(num fontSize) => fontSize.toDouble() * scale;

  @override
  Color get long =>
      _currentChartConfig?.bullCandleColor ??
      (_context?.templateKlineStyles.bullCandleColor ??
          const Color(0xFFf45f4e)); // 上涨蜡烛颜色

  @override
  Color get short =>
      _currentChartConfig?.bearCandleColor ??
      (_context?.templateKlineStyles.bearCandleColor ??
          const Color(0xFF28c99b)); // 下跌蜡烛颜色

  @override
  Color get transparent => Colors.transparent;

  @override
  Color get dragBg =>
      _context?.templateKlineStyles.dragBackgroundColor ??
      const Color(0xFF1E2030);

  @override
  Color get chartBg =>
      _currentChartConfig?.chartBackgroundColor ??
      (_context?.templateKlineStyles.chartBackgroundColor ??
          const Color(0xFF0E1327)); // 图表背景

  @override
  Color get tooltipBg =>
      _context?.templateKlineStyles.tooltipBackgroundColor ??
      const Color(0xFF303545);

  @override
  Color get crossTextBg =>
      _context?.templateKlineStyles.tooltipBackgroundColor ??
      const Color(0xFF303545);

  @override
  Color get latestPriceTextBg =>
      _context?.templateKlineStyles.latestPriceTextBackgroundColor ??
      const Color(0xFF303545);

  @override
  Color get lastPriceTextBg =>
      _context?.templateKlineStyles.lastPriceTextBackgroundColor ??
      const Color(0xFF303545);

  @override
  Color get countDownTextBg =>
      _context?.templateKlineStyles.countDownTextBackgroundColor ??
      const Color(0xFF303545);

  @override
  Color get gridLine =>
      _currentChartConfig?.showGridLines == false
          ? Colors.transparent
          : (_currentChartConfig?.gridLineColor ??
              (_context?.templateKlineStyles.gridLineColor ??
                  const Color(0xFF2D344A))); // 网格线颜色

  @override
  Color get crossColor =>
      _context?.templateKlineStyles.crossLineColor ?? const Color(0xFFDDDDDD);

  @override
  Color get drawColor =>
      _context?.templateKlineStyles.drawColor ?? const Color(0xFFDDDDDD);

  @override
  Color get markLine =>
      _currentChartConfig?.markLineColor ??
      (_context?.templateKlineStyles.markLineColor ?? const Color(0xFFFBFBFB)); // 标记线颜色

  @override
  Color get themeColor =>
      _context?.templateKlineStyles.themeColor ?? const Color(0xFF303545);

  @override
  Color get textColor =>
      _context?.templateKlineStyles.primaryTextColor ?? Colors.white;

  @override
  Color get ticksTextColor =>
      _context?.templateKlineStyles.ticksTextColor ??
      Colors.white.withValues(alpha: 0.7);

  @override
  Color get lastPriceTextColor =>
      _context?.templateKlineStyles.priceTextColor ?? Colors.white;

  @override
  Color get crossTextColor =>
      _context?.templateKlineStyles.primaryTextColor ?? Colors.white;

  @override
  Color get tooltipTextColor =>
      _context?.templateKlineStyles.tooltipTextColor ?? Colors.white;

  // 添加平均价线颜色，橙色点线
  Color get averagePriceLine =>
      _context?.templateKlineStyles.averagePriceLineColor ??
      const Color(0xFFFF9F43);

  // 新增：价格成交区间图形颜色
  @override
  Color? get priceLevelColor =>
      _context?.templateKlineStyles.priceLevelColor ??
      const Color(0xFF546E7A).withValues(alpha: 0.5); // 示例颜色：蓝灰色半透明

  // 实现扩展颜色配置
  @override
  Color? get sellTrackColor => _context?.templateKlineStyles.sellTrackColor;

  @override
  Color? get buyTrackColor => _context?.templateKlineStyles.buyTrackColor;

  @override
  Color? get averagePriceLineColor =>
      _context?.templateKlineStyles.averagePriceLineColor;

  // 蜡烛相关配置
  /// 蜡烛最大宽度
  double get candleMaxWidth =>
      (_currentChartConfig?.candleMaxWidth ?? 50.0) * scale;

  /// 蜡烛宽度（成交量轨迹模式）
  double get candleWidth => (_currentChartConfig?.candleWidth ?? 30.0) * scale;

  /// 蜡烛宽度（普通模式）
  double get normalCandleWidth =>
      (_currentChartConfig?.normalCandleWidth ?? 8.0) * scale;

  /// 蜡烛线宽
  double get candleLineWidth =>
      (_currentChartConfig?.candleLineWidth ?? 1.0) * scale;

  /// 是否启用成交量轨迹模式
  bool get enableVolumeTracksRendering =>
      _currentChartConfig?.enableVolumeTracksRendering ?? false;

  /// 普通模式蜡烛间距
  double get normalCandleSpacingParts =>
      _currentChartConfig?.normalCandleSpacingParts ?? 3.5;

  /// 是否启用代理
  bool get enableProxy => _currentChartConfig?.enableProxy ?? true;

  /// 代理主机地址
  String get proxyHost => _currentChartConfig?.proxyHost ?? '127.0.0.1';

  /// 代理端口
  int get proxyPort => _currentChartConfig?.proxyPort ?? 1087;

  /// 窗口布局模式
  WindowLayoutMode get windowLayoutMode =>
      _currentChartConfig?.windowLayoutMode ?? WindowLayoutMode.single;

  /// OHLC数据显示模式
  OHLCDisplayMode get ohlcDisplayMode =>
      _currentChartConfig?.ohlcDisplayMode ?? OHLCDisplayMode.floating;
}
