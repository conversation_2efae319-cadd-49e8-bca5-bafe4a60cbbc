import 'package:flutter/material.dart';
import '../../kline/flexi_kline.dart';

/// K线图自定义Logger实现，用于FlexiKline内部日志记录
class LoggerImpl implements ILogger {
  final String tag;
  final bool debug;

  const LoggerImpl({required this.tag, this.debug = false});

  @override
  bool get isDebug => debug;

  @override
  String? get logTag => tag;

  @override
  void logd(
    String msg, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) {
    if (isDebug) {
      // debugPrint("[$tag] DEBUG: $msg");
    }
  }

  @override
  void logi(
    String msg, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) {
    // debugPrint("[$tag] INFO: $msg");
  }

  @override
  void logw(
    String msg, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) {
    // debugPrint("[$tag] WARN: $msg");
  }

  @override
  void loge(
    String msg, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) {
    // debugPrint("[$tag] ERROR: $msg");
    // if (error != null) {
    //   debugPrint("[$tag] ERROR: $error");
    // }
    // if (stackTrace != null) {
    //   debugPrint("[$tag] STACK: $stackTrace");
    // }
  }
}
