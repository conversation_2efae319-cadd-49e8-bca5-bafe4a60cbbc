import 'package:flutter/material.dart';
import '../../kline/flexi_kline.dart';
import '../config/default_configuration.dart';
import '../themes/simple_kline_theme.dart';
import '../utils/logger.dart';
import 'dart:convert'; // For jsonEncode (pretty printing)
import 'package:flutter/foundation.dart'; // For kDebugMode
import '../models/kline_data_model.dart';
import '../config/indicator_config/chart_config.dart';

import '../services/kline_data_manager.dart';
import 'init_data.dart';
import 'init_indicator.dart';
import 'performance_optimizer.dart';
import '../optimizations/indicator_paint_optimizer.dart'; // 新增：指标绘制优化器
import '../config/indicator_config_manager.dart';
import '../config/indicator_config/volume_profile_config.dart';
import '../config/indicator_config/volume_profile_chart_config.dart';
import '../managers/volume_profile_chart_manager.dart';
import '../managers/volume_profile_data_manager.dart';
import '../../kline/indicators/volume_profile/volume_profile.dart' as flexi_vp;
import '../../../services/data/depth_data_service.dart';

/// 成交数据模型
class TradeData {
  final double price;
  final double quantity;
  final bool isBuyerMaker; // 买方是否是挂单方
  final int tradeTime; // 交易时间戳
  final String symbol; // 交易对

  TradeData({
    required this.price,
    required this.quantity,
    required this.isBuyerMaker,
    required this.tradeTime,
    required this.symbol,
  });

  /// 是否为买入成交（主动买入）
  bool get isBuy => !isBuyerMaker;

  /// 是否为卖出成交（主动卖出）
  bool get isSell => isBuyerMaker;
}

/// K线图控制器类，用于管理K线图数据和状态
class KlineController {
  late final FlexiKlineController controller;
  final double mainChartHeight;
  String _currentInstrumentId;
  String _currentPeriod;

  // 新增的成员变量
  int _pricePrecision = 2; // 默认价格精度，可以后续根据币对调整

  bool _isInitialLoad = true; //是否初始化加载

  // 添加回调函数，用于通知组件层价格范围变化
  Function(double, double)? onPriceRangeUpdated;

  // 添加加载状态回调
  Function(bool)? onLoadingStateChanged;

  // 添加成交数据回调
  Function(TradeData)? onTradeDataReceived;

  final KlineDataGenerator dataGenerator = KlineDataGenerator();

  // 添加KlineDataManager实例
  late final KlineDataManager _dataManager;

  // 数据加载器
  late final KlineDataLoader _dataLoader;

  // 指标初始化器
  late final KlineIndicatorInitializer _indicatorInitializer;

  // 性能优化器
  late final PerformanceOptimizer _performanceOptimizer;

  // 指标绘制优化器
  final IndicatorPaintOptimizer _indicatorPaintOptimizer =
      IndicatorPaintOptimizer();

  // Volume Profile Chart管理器
  late final VolumeProfileChartManager _volumeProfileChartManager;

  // Volume Profile数据管理器
  late final VolumeProfileDataManager _volumeProfileDataManager;

  // 深度数据服务 - 每个控制器独立管理
  late final DepthDataService _depthDataService;

  /// 获取FlexiKline控制器
  FlexiKlineController get flexiKlineController => controller;

  /// 获取深度数据服务
  DepthDataService get depthDataService => _depthDataService;

  /// 创建K线图控制器
  KlineController({
    required this.mainChartHeight,
    required String initialInstrumentId,
    required String initialPeriod,
    int? pricePrecision, // 允许外部传入价格精度
    int? windowIndex, // 新增：窗口索引
    this.onLoadingStateChanged, // 加载状态变化回调
    this.onTradeDataReceived, // 成交数据回调
  }) : _currentInstrumentId = initialInstrumentId,
       _currentPeriod = initialPeriod {
    if (pricePrecision != null) {
      _pricePrecision = pricePrecision;
    }

    // 创建独立的数据管理器实例
    final instanceId =
        '${initialInstrumentId}_${DateTime.now().millisecondsSinceEpoch}';
    _dataManager = KlineDataManager(
      instanceId: instanceId,
      windowIndex: windowIndex,
    );

    // 创建独立的深度数据服务实例
    _depthDataService = DepthDataService(instanceId: 'kline_depth_$instanceId');

    // 创建默认自定义主题（不带配置，但支持传递context）
    final theme = SimpleFlexiKlineTheme();
    // 创建默认配置，成交量轨迹暂时设为false
    final configuration = DefaultConfiguration(
      theme: theme,
      mainChartHeight: mainChartHeight,
      enableVolumeTracksRendering: false, // 默认关闭，稍后会根据配置更新
    );
    // 创建控制器
    controller = FlexiKlineController(
      configuration: configuration,
      logger: LoggerImpl(tag: "FlexiKline", debug: true),
    );

    // 先创建指标配置管理器来读取配置，传入窗口索引
    final tempIndicatorConfigManager = IndicatorConfigManager(
      windowIndex: windowIndex,
    );

    // 将配置传递给指标初始化器，让它在初始化时使用正确的配置
    _indicatorInitializer = KlineIndicatorInitializer(
      controller: controller,
      preloadedConfigManager: tempIndicatorConfigManager,
    );

    // 初始化数据加载器
    _dataLoader = KlineDataLoader(
      dataManager: _dataManager,
      controller: controller,
      pricePrecision: _pricePrecision,
      onLoadingStateChanged: onLoadingStateChanged,
      onPriceRangeUpdated: onPriceRangeUpdated,
    );

    // 设置加载更多K线数据的回调
    controller.onLoadMoreCandles =
        (request) => _dataLoader.onLoadMoreCandles(
          request,
          _currentInstrumentId,
          _currentPeriod,
        );

    // 初始化性能优化器
    _performanceOptimizer = PerformanceOptimizer();
    _performanceOptimizer.initialize(
      messageProcessor: _processBatchedWebSocketMessages,
    );

    // 注册内存清理任务
    _performanceOptimizer.registerCleanupTask(() {
      _dataManager.clearOldVolumeProfileData();
      _dataManager.forceCleanup(); // 添加强制清理
    });

    // 注册缓存清理任务
    _performanceOptimizer.registerCleanupTask(() {
      _volumeProfileDataManager.cleanupExpiredCache();
    });

    // 从配置管理器更新指标绘制优先级
    _indicatorPaintOptimizer.updatePrioritiesFromConfig(indicatorConfigManager);

    // 初始化Volume Profile数据管理器
    _volumeProfileDataManager = VolumeProfileDataManager();

    // 初始化Volume Profile Chart管理器
    _volumeProfileChartManager = VolumeProfileChartManager(
      controller: controller,
      onDataRequested: _onVolumeProfileChartDataRequested,
      onRenderRequested: _onVolumeProfileChartRenderRequested,
    );

    // 初始化数据管理器
    _dataManager.initialize(
      instrumentId: _currentInstrumentId,
      period: _currentPeriod,
      pricePrecision: _pricePrecision,
      onCandleUpdate: _onCandleUpdated,
      onVolumeProfileRealtimeUpdate: _onVolumeProfileRealtimeUpdate,
    );

    // 异步初始化配置和数据
    _initializeAsyncComponents();
  }

  /// 获取VWAP指标管理器
  get vwapManager => _indicatorInitializer.vwapManager;

  /// 获取成交量指标管理器
  get volumeManager => _indicatorInitializer.volumeManager;

  /// 获取Volume Profile指标管理器
  get volumeProfileManager => _indicatorInitializer.volumeProfileManager;

  /// 获取指标配置管理器
  get indicatorConfigManager => _indicatorInitializer.indicatorConfigManager;

  /// 应用指标配置更新
  void applyIndicatorConfigs() {
    // 应用图表主题配置
    _applyChartThemeConfig();

    // 先更新VolumeProfileChartManager，再更新指标管理器
    final newConfig = _indicatorInitializer.indicatorConfigManager
        .getConfig<VolumeProfileChartConfig>(IndicatorType.volumeProfileChart);
    final oldEnabled = _volumeProfileChartManager.isEnabled;

    _volumeProfileChartManager.updateConfig(newConfig);

    // 然后应用所有指标配置（包括VolumeProfileIndicatorManager）
    // 传递回调来处理Volume Profile从禁用到启用的情况
    _indicatorInitializer.applyIndicatorConfigs(
      onVolumeProfileEnabledCallback: () {
        // 当Volume Profile从禁用变为启用时，触发数据重新获取
        _volumeProfileChartManager.forceRefresh();
      },
    );

    // 如果Volume Profile从禁用变为启用，触发数据重新处理
    final newEnabled = newConfig?.enabled ?? false;
    if (!oldEnabled && newEnabled) {
      if (kDebugMode) {
        print('[KlineController] Volume Profile从禁用变为启用，触发数据重新处理');
      }
      // 强制刷新Volume Profile Chart数据
      _volumeProfileChartManager.forceRefresh();
    }

    if (kDebugMode) {
      print('[KlineController] 指标配置已应用并刷新图表');
    }
  }

  /// 应用图表主题配置
  void _applyChartThemeConfig() {
    try {
      final chartConfig = _indicatorInitializer.indicatorConfigManager
          .getConfig<ChartConfig>(IndicatorType.chartConfig);

      if (kDebugMode) {
        print(
          '[KlineController] 应用图表主题配置: 启用=${chartConfig?.enabled}, 缩放=${chartConfig?.globalScale}',
        );
      }

      // 更新全局配置管理器
      SimpleFlexiKlineTheme.setGlobalConfigManager(
        _indicatorInitializer.indicatorConfigManager,
      );

      // 获取当前主题实例
      final currentTheme = controller.theme;

      // 调用onThemeChanged方法触发重绘
      // 传入相同的主题实例，因为主题会动态读取最新的配置
      controller.onThemeChanged(currentTheme);
    } catch (e) {
      if (kDebugMode) {
        print('[KlineController] 应用图表主题配置失败: $e');
      }
    }
  }

  /// 更新主题的BuildContext以支持模板样式
  void updateThemeContext(BuildContext context) {
    try {
      // 创建新的主题实例，传入BuildContext
      final newTheme = SimpleFlexiKlineTheme(
        context: context,
        configManager: _indicatorInitializer.indicatorConfigManager,
      );

      // 更新控制器的主题
      controller.onThemeChanged(newTheme);

      if (kDebugMode) {
        print('[KlineController] 已更新主题BuildContext以支持模板样式');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[KlineController] 更新主题BuildContext失败: $e');
      }
    }
  }

  /// 获取性能优化状态（用于调试和监控）
  Map<String, dynamic> getPerformanceStatus() {
    return _performanceOptimizer.getBufferStatus();
  }

  /// 手动触发内存清理
  void forceMemoryCleanup() {
    _performanceOptimizer.forceMemoryCleanup();
  }

  /// 获取数据加载器的加载状态
  bool get isLoading => _dataLoader.isLoading;

  /// 当K线数据更新时的回调
  void _onCandleUpdated(CandleModel updatedCandle) {
    // 获取当前的请求对象
    final candleReq = CandleReq(
      instId: _currentInstrumentId.replaceAll('/', ''),
      bar: _currentPeriod,
      precision: _pricePrecision,
    );
    controller.updateKlineData(candleReq, [updatedCandle]);

    // 实时更新缓存
    //_updateCacheWithNewCandle(updatedCandle);

    // 数据有变化就通知Volume Profile Chart管理器（实时更新，使用防抖）
    _volumeProfileChartManager.handleVisibleRangeChanged(
      isInitialLoad: _isInitialLoad,
    );
    _isInitialLoad = false;
  }

  /// 实时更新缓存中的K线数据
  void _updateCacheWithNewCandle(CandleModel updatedCandle) {
    // 异步调用缓存处理器的智能更新方法
    Future.microtask(() async {
      try {
        final normalizedInstrumentId = _currentInstrumentId.replaceAll('/', '');
        final wasUpdated = await _dataLoader.klineDataCache
            .smartUpdateSingleCandle(
              normalizedInstrumentId,
              _currentPeriod,
              updatedCandle,
            );

        if (kDebugMode && wasUpdated) {
          print('[KlineController] K线缓存已更新');
        }
      } catch (e) {
        if (kDebugMode) {
          print('[KlineController] 缓存更新失败: $e');
        }
      }
    });
  }

  /// 处理WebSocket消息（使用缓冲优化）
  void _processWebSocketMessage(dynamic message) {
    // 将消息添加到缓冲区而不是直接处理
    _performanceOptimizer.bufferMessage(message);
  }

  /// 批量处理WebSocket消息
  void _processBatchedWebSocketMessages(List<dynamic> messages) {
    // 使用更短的防抖延迟，提高响应性
    _performanceOptimizer.debounce('ui_update', () {
      try {
        final String currentSymbol =
            _currentInstrumentId.replaceAll('/', '').toUpperCase();

        // 批量处理消息，减少单独处理的开销
        final List<TradeData> validTrades = [];

        for (final message in messages) {
          try {
            // 解析成交数据
            final Map<String, dynamic> tradeData =
                message is String ? json.decode(message) : message;

            // 验证币种是否匹配当前设置的币种
            final String messageSymbol =
                tradeData['s']?.toString().toUpperCase() ?? '';

            if (messageSymbol != currentSymbol) {
              continue; // 跳过不匹配的数据
            }

            // 获取交易价格和数量
            final double price = double.parse(tradeData['p']);
            final double quantity = double.parse(tradeData['q']);
            final bool isBuyerMaker = tradeData['m'] ?? false;
            final int tradeTime = tradeData['T'];

            // 创建成交数据对象
            final trade = TradeData(
              price: price,
              quantity: quantity,
              isBuyerMaker: isBuyerMaker,
              tradeTime: tradeTime,
              symbol: messageSymbol,
            );

            validTrades.add(trade);

            // 将数据传递给数据管理器处理（K线相关）
            _dataManager.processTradeData(
              price,
              quantity,
              isBuyerMaker,
              tradeTime,
            );
          } catch (e) {
            if (kDebugMode) {
              print('[KlineController] 解析单条消息失败: $e');
            }
          }
        }

        // 批量异步处理成交数据
        if (validTrades.isNotEmpty) {
          Future.microtask(() async {
            try {
              for (final trade in validTrades) {
                // 将成交数据传递给深度数据服务进行价格聚合处理
                _depthDataService.processTradeData(
                  trade.price,
                  trade.quantity,
                  trade.isBuy,
                );

                // 通过回调传递成交数据给上层组件（如果需要）
                onTradeDataReceived?.call(trade);
              }
            } catch (e) {
              if (kDebugMode) {
                print('[KlineController] 批量异步处理成交数据错误: $e');
              }
            }
          });
        }
      } catch (e) {
        if (kDebugMode) {
          print('[KlineController] 批量处理交易数据时出错: $e');
        }
      }
    }, delay: const Duration(milliseconds: 50)); // 减少防抖延迟
  }

  /// 设置是否启用成交量轨迹绘制
  void setVolumeProfileEnabled(bool enabled) {
    _dataLoader.setVolumeProfileEnabled(enabled);
  }

  /// 加载K线数据
  Future<void> loadKlineData() async {
    await _dataLoader.loadKlineData(_currentInstrumentId, _currentPeriod);

    // 数据加载完成后，初始化缓存状态
    _initializeCacheState();

    // 数据加载完成后更新VWAP会话配置
    _indicatorInitializer.updateVWAPSession();
  }

  /// 初始化缓存状态
  void _initializeCacheState() {
    Future.microtask(() async {
      try {
        final normalizedInstrumentId = _currentInstrumentId.replaceAll('/', '');
        await _dataLoader.klineDataCache.initializeCacheState(
          normalizedInstrumentId,
          _currentPeriod,
        );

        if (kDebugMode) {
          print('[KlineController] 缓存状态初始化完成');
        }
      } catch (e) {
        if (kDebugMode) {
          print('[KlineController] 初始化缓存状态失败: $e');
        }
      }
    });
  }

  /// 初始化指标配置
  Future<void> _initializeIndicatorConfigs() async {
    await _indicatorInitializer.initializeIndicatorConfigs();
  }

  /// 加载初始数据到控制器
  Future<void> _initializeAndLoadData() async {
    await loadKlineData();
  }

  /// WebSocket连接和订阅
  Future<void> _connectAndSubscribeAggTrade(String instrumentId) async {
    await _dataLoader.connectAndSubscribeAggTrade(
      instrumentId,
      _processWebSocketMessage,
    );
  }

  /// 当币种或周期改变时调用此方法
  void updateCandleSubscription(
    String newInstrumentId,
    String newPeriod,
  ) async {
    if (_currentInstrumentId == newInstrumentId &&
        _currentPeriod == newPeriod) {
      return;
    }

    final oldInstrumentId = _currentInstrumentId;

    if (kDebugMode) {
      print(
        '[KlineController] 更新订阅：[$oldInstrumentId, $_currentPeriod] -> [$newInstrumentId, $newPeriod]',
      );
    }

    // 立即断开旧的WebSocket连接，避免数据干扰
    await _dataLoader.disconnectWebSocket();

    // 清理性能优化器的消息缓冲区，防止旧币种数据被处理
    _performanceOptimizer.clearMessageBuffer();

    // 清除旧币种的缓存状态
    final oldNormalizedId = oldInstrumentId.replaceAll('/', '');
    _dataLoader.klineDataCache.clearCacheState(oldNormalizedId, _currentPeriod);

    // 更新当前币种和周期
    _currentInstrumentId = newInstrumentId;
    _currentPeriod = newPeriod;

    // 检查Volume Profile数据并处理指标移除
    await _checkAndRemoveVolumeProfileIfNeeded(newInstrumentId);

    // 更新数据管理器的配置
    _dataManager.initialize(
      instrumentId: newInstrumentId,
      period: newPeriod,
      pricePrecision: _pricePrecision,
      onCandleUpdate: _onCandleUpdated,
      onVolumeProfileRealtimeUpdate: _onVolumeProfileRealtimeUpdate,
    );

    // 加载新周期的K线数据
    await loadKlineData();

    // 重新建立独立的WebSocket连接（新币种）
    await _connectAndSubscribeAggTrade(newInstrumentId);

    // 币种或周期变化后，通知Volume Profile Chart管理器（这是初始化加载）
    _volumeProfileChartManager.handleVisibleRangeChanged(isInitialLoad: true);
  }

  /// 简化的Volume Profile检查和移除逻辑
  Future<void> _checkAndRemoveVolumeProfileIfNeeded(
    String newInstrumentId,
  ) async {
    try {
      // 直接检查新币种是否有数据
      final hasData = await _volumeProfileDataManager.hasVolumeProfileData(
        newInstrumentId,
      );

      if (!hasData) {
        // 移除Volume Profile指标（如果已添加）
        if (volumeProfileManager.isIndicatorAdded) {
          volumeProfileManager.removeIndicator();
        }

        // 禁用Volume Profile Chart配置
        final currentConfig = _indicatorInitializer.indicatorConfigManager
            .getConfig<VolumeProfileChartConfig>(
              IndicatorType.volumeProfileChart,
            );
        if (currentConfig?.enabled == true) {
          final disabledConfig = currentConfig!.copyWith(enabled: false);
          _indicatorInitializer.indicatorConfigManager.setConfig(
            disabledConfig,
          );
        }

        _volumeProfileDataManager.setInstrumentDataAvailability(
          newInstrumentId,
          false,
        );
      } else {
        _volumeProfileDataManager.setInstrumentDataAvailability(
          newInstrumentId,
          true,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('[KlineController] Volume Profile检查出错: $e');
      }
    }
  }

  /// Volume Profile Chart数据请求回调
  void _onVolumeProfileChartDataRequested(
    String instrumentId,
    int startTime,
    int endTime,
  ) async {
    try {
      // 使用数据管理器获取数据，使用正常的缓存机制
      final mergedData = await _volumeProfileDataManager.getVolumeProfileData(
        instrumentId,
        startTime,
        endTime,
      );

      if (mergedData == null) {
        return;
      }

      // 获取配置中的Value Area百分比
      final latestConfig = _indicatorInitializer.indicatorConfigManager
          .getConfig<VolumeProfileChartConfig>(
            IndicatorType.volumeProfileChart,
          );
      final valueAreaPercentage =
          latestConfig?.valueAreaPercentage ?? 0.7; // 默认70%

      // 构建渲染数据
      final renderData = {
        'buy_volume_distribution': mergedData.getBuyVolumeByPrice(),
        'sell_volume_distribution': mergedData.getSellVolumeByPrice(),
        'total_volume_distribution': mergedData.getTotalVolumeByPrice(),
        'poc': mergedData.calculatePOC(),
        'vwap': mergedData.calculateVWAP(),
        'value_area': mergedData.calculateValueArea(
          percentage: valueAreaPercentage,
        ),
        'start_time': startTime,
        'end_time': endTime,
        'symbol': instrumentId,
      };

      // 将数据传递给Volume Profile Chart管理器
      _volumeProfileChartManager.handleDataReceived(renderData);
    } catch (e) {
      if (kDebugMode) {
        print('[VolumeProfileChart] 获取数据时出错: $e');
      }
    }
  }

  /// Volume Profile Chart渲染请求回调
  void _onVolumeProfileChartRenderRequested(
    Map<String, dynamic> data,
    VolumeProfileChartConfig config,
  ) {
    // 强制从配置管理器获取最新保存的配置，这是最权威的配置源
    final latestConfig = _indicatorInitializer.indicatorConfigManager
        .getConfig<VolumeProfileChartConfig>(IndicatorType.volumeProfileChart);

    // 优先使用配置管理器中的配置，如果没有则使用传入的配置，最后才使用默认配置
    final configToUse =
        latestConfig ??
        (config.opacity > 0 && config.maxWidthPercent > 0
            ? config
            : const VolumeProfileChartConfig());

    // 确保指标配置与当前配置同步
    final flexiConfig = _convertToFlexiVolumeProfileConfig(configToUse);
    volumeProfileManager.updateConfig(flexiConfig);

    // 启用Volume Profile指标
    if (!volumeProfileManager.isIndicatorAdded) {
      volumeProfileManager.addIndicator();
    }

    try {
      // 提取数据
      final Map<double, double> buyVolumeDistribution =
          Map<double, double>.from(data['buy_volume_distribution'] ?? {});
      final Map<double, double> sellVolumeDistribution =
          Map<double, double>.from(data['sell_volume_distribution'] ?? {});
      final Map<double, double> totalVolumeDistribution =
          Map<double, double>.from(data['total_volume_distribution'] ?? {});

      final double poc = data['poc']?.toDouble() ?? 0.0;
      final double vwap = data['vwap']?.toDouble() ?? 0.0;
      final Map<String, double> valueArea = Map<String, double>.from(
        data['value_area'] ?? {},
      );
      final double val = valueArea['VAL'] ?? 0.0;
      final double vah = valueArea['VAH'] ?? 0.0;

      // 输出价格范围信息
      if (totalVolumeDistribution.isNotEmpty && kDebugMode) {
        final prices = totalVolumeDistribution.keys.toList();
        final maxPrice = prices.reduce((a, b) => a > b ? a : b);
        final minPrice = prices.reduce((a, b) => a < b ? a : b);
        print(
          '[VolumeProfile] 绘制价格范围: ${minPrice.toStringAsFixed(2)} - ${maxPrice.toStringAsFixed(2)}',
        );
      }

      // 根据配置选择要渲染的数据
      Map<double, double> renderData = totalVolumeDistribution;

      // 构建指标需要的数据格式，包含必要的配置信息
      final indicatorData = {
        'render_data': renderData,
        'buy_data': buyVolumeDistribution,
        'sell_data': sellVolumeDistribution,
        'poc': poc,
        'vwap': vwap,
        'val': val,
        'vah': vah,
        // 传递位置和显示模式信息
        'position': configToUse.position.value, // 'left' 或 'right'
        'display_mode': configToUse.displayMode.value, // 'total' 或 'separated'
        'start_time': data['start_time'],
        'end_time': data['end_time'],
        'symbol': data['symbol'],
      };

      // 传递数据给Volume Profile指标
      volumeProfileManager.updateWithData(indicatorData);

      // 强制重绘以确保数据更新生效
      volumeProfileManager.forceRedraw();
    } catch (e) {
      if (kDebugMode) {
        print('[VolumeProfileChart] 渲染时出错: $e');
      }
    }
  }

  /// 获取Volume Profile Chart管理器
  VolumeProfileChartManager get volumeProfileChartManager =>
      _volumeProfileChartManager;

  /// 当可见范围变化时调用（例如用户滚动、缩放等）
  void onVisibleRangeChanged() {
    // 用户操作导致的可见范围变化，使用防抖处理
    _volumeProfileChartManager.handleVisibleRangeChanged();

    // 定期清理过期缓存
    _volumeProfileDataManager.cleanupExpiredCache();
  }

  /// Volume Profile实时数据更新回调（WebSocket数据聚合后触发）
  void _onVolumeProfileRealtimeUpdate() {
    // 标记Volume Profile Chart管理器有实时数据更新
    _volumeProfileChartManager.markRealtimeDataUpdate();
  }

  /// 测试Volume Profile Chart功能
  void testVolumeProfileChart() {
    if (kDebugMode) {
      print('[KlineController] 测试Volume Profile Chart功能');
      print('- 管理器已初始化: ${_volumeProfileChartManager.isInitialized}');
      print('- 管理器已启用: ${_volumeProfileChartManager.isEnabled}');

      // 强制刷新一次
      _volumeProfileChartManager.forceRefresh();
    }
  }

  /// 将VolumeProfileChartConfig转换为flex_kline的VolumeProfileConfig
  flexi_vp.VolumeProfileConfig _convertToFlexiVolumeProfileConfig(
    VolumeProfileChartConfig chartConfig,
  ) {
    // 转换显示模式
    flexi_vp.VolumeProfileDisplayMode displayMode;
    switch (chartConfig.displayMode) {
      case VolumeProfileChartDisplayMode.total:
        displayMode = flexi_vp.VolumeProfileDisplayMode.total;
        break;
      case VolumeProfileChartDisplayMode.separated:
        displayMode = flexi_vp.VolumeProfileDisplayMode.bidAsk;
        break;
    }

    // 转换线段样式
    flexi_vp.VolumeProfileLineStyle convertLineStyle(LineStyle style) {
      switch (style) {
        case LineStyle.solid:
          return flexi_vp.VolumeProfileLineStyle.solid;
        case LineStyle.dashed:
          return flexi_vp.VolumeProfileLineStyle.dashed;
      }
    }

    return flexi_vp.VolumeProfileConfig(
      showPOC: chartConfig.showPOC,
      showVAH: chartConfig.showVAH,
      showVAL: chartConfig.showVAL,
      pocColor: chartConfig.pocColor,
      vahColor: chartConfig.vahColor,
      valColor: chartConfig.valColor,
      pocLineStyle: convertLineStyle(chartConfig.pocLineStyle),
      vahLineStyle: convertLineStyle(chartConfig.vahLineStyle),
      valLineStyle: convertLineStyle(chartConfig.valLineStyle),
      displayMode: displayMode,
      upVolumeColor: chartConfig.upVolumeColor,
      downVolumeColor: chartConfig.downVolumeColor,
      totalVolumeColor: chartConfig.totalVolumeColor,
      pocLineWidth: 1.5, // 使用默认值
      vahLineWidth: 1.0,
      valLineWidth: 1.0,
      volumeOpacity: chartConfig.opacity,
      lineOpacity: 1.0,
      priceZones: 100,
      valueAreaPercentage: 0.68,
      maxWidthPercent: chartConfig.maxWidthPercent,
      smoothingStrength: chartConfig.smoothingStrength,
    );
  }

  /// 手动触发性能优化
  void performManualOptimization() {
    // 强制清理内存
    _dataManager.forceCleanup();
    _volumeProfileDataManager.cleanupExpiredCache();

    // 清理指标绘制缓存
    _indicatorPaintOptimizer.cleanupExpiredCache();

    if (kDebugMode) {
      print('[KlineController] 手动性能优化完成');
    }
  }

  /// 获取性能统计信息
  Map<String, dynamic> getPerformanceStats() {
    return {
      'dataManager': _dataManager.getMemoryStats(),
      'volumeProfile': _volumeProfileDataManager.getCacheStatus(),
      'indicatorPaint': _indicatorPaintOptimizer.getPerformanceStats(),
      'gestureDebounce': controller.getGestureDebounceStats(),
    };
  }

  /// 释放控制器资源
  void dispose() {
    try {
      controller.dispose();
    } catch (e) {
      // 忽略controller dispose错误
    }

    try {
      _dataLoader.dispose(); // 释放数据加载器资源
    } catch (e) {
      // 忽略数据加载器dispose错误
    }

    try {
      _dataManager.dispose(); // 清理数据管理器资源
    } catch (e) {
      // 忽略数据管理器dispose错误
    }

    try {
      _indicatorInitializer.dispose(); // 释放指标资源
    } catch (e) {
      // 忽略指标初始化器dispose错误
    }

    try {
      _performanceOptimizer.dispose(); // 释放性能优化器资源
    } catch (e) {
      // 忽略性能优化器dispose错误
    }

    try {
      _indicatorPaintOptimizer.dispose(); // 释放指标绘制优化器资源
    } catch (e) {
      // 忽略指标绘制优化器dispose错误
    }

    try {
      _volumeProfileChartManager.dispose(); // 释放Volume Profile Chart管理器资源
    } catch (e) {
      // 忽略Volume Profile Chart管理器dispose错误
    }

    try {
      _volumeProfileDataManager.dispose(); // 释放Volume Profile数据管理器资源
    } catch (e) {
      // 忽略Volume Profile数据管理器dispose错误
    }

    try {
      _depthDataService.dispose(); // 释放深度数据服务资源
    } catch (e) {
      // 忽略深度数据服务dispose错误
    }
  }

  /// 获取缓存状态信息
  Map<String, dynamic> getVolumeProfileCacheStatus() {
    return _volumeProfileDataManager.getCacheStatus();
  }

  /// 异步初始化组件
  Future<void> _initializeAsyncComponents() async {
    try {
      // 加载指标配置
      await _indicatorInitializer.indicatorConfigManager.loadConfigs();

      // 设置全局配置管理器，让主题能够动态读取配置
      SimpleFlexiKlineTheme.setGlobalConfigManager(
        _indicatorInitializer.indicatorConfigManager,
      );

      // 从配置中获取成交量轨迹启用状态，默认为false
      final volumeProfileConfig = _indicatorInitializer.indicatorConfigManager
          .getConfig<VolumeProfileConfig>(IndicatorType.volumeProfile);
      final enableVolumeTracksRendering = volumeProfileConfig?.enabled ?? false;

      // 获取Volume Profile Chart配置并更新管理器
      final volumeProfileChartConfig = _indicatorInitializer
          .indicatorConfigManager
          .getConfig<VolumeProfileChartConfig>(
            IndicatorType.volumeProfileChart,
          );
      _volumeProfileChartManager.updateConfig(volumeProfileChartConfig);

      // 在初始化完成后，立即触发一次主题更新以应用保存的配置
      Future.microtask(() {
        try {
          final currentTheme = controller.theme;
          controller.onThemeChanged(currentTheme);

          if (kDebugMode) {
            print('[KlineController] 初始化完成，已应用保存的图表配置');
          }
        } catch (e) {
          if (kDebugMode) {
            print('[KlineController] 初始化时应用图表配置失败: $e');
          }
        }
      });

      // 如果配置中的状态与当前不同，则更新设置
      if (enableVolumeTracksRendering !=
          controller.settingConfig.enableVolumeTracksRendering) {
        final newConfig = controller.settingConfig.copyWith(
          enableVolumeTracksRendering: enableVolumeTracksRendering,
          buyTrackColor:
              volumeProfileConfig?.bullishColor ?? const Color(0xFF26A69A),
          sellTrackColor:
              volumeProfileConfig?.bearishColor ?? const Color(0xFFEF5350),
        );
        controller.settingConfig = newConfig;
      }

      // 加载指标配置并初始化
      await _initializeIndicatorConfigs();

      // 加载初始数据
      await _initializeAndLoadData();

      // 连接WebSocket
      await _connectAndSubscribeAggTrade(_currentInstrumentId);

      // 初始化Volume Profile Chart管理器
      _volumeProfileChartManager.initialize();

      final finalVolumeProfileChartConfig = _indicatorInitializer
          .indicatorConfigManager
          .getConfig<VolumeProfileChartConfig>(
            IndicatorType.volumeProfileChart,
          );
      if (finalVolumeProfileChartConfig != null) {
        _volumeProfileChartManager.updateConfig(finalVolumeProfileChartConfig);
      }
    } catch (e) {
      if (kDebugMode) {
        print('[KlineController] 异步初始化失败: $e');
      }
    }
  }
}
