import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../kline/flexi_kline.dart';

/// VWAP指标配置类
class VWAPIndicatorConfig {
  // 标准差倍数配置
  final double devUp1;
  final double devDn1;
  final double devUp2;
  final double devDn2;
  final double devUp3;
  final double devDn3;
  final double devUp4;
  final double devDn4;
  final double devUp5;
  final double devDn5;

  // 显示控制
  final bool showDv2;
  final bool showDv3;
  final bool showDv4;
  final bool showDv5;
  final bool showPrevVWAP;

  // 颜色配置
  final Color vwapColor;
  final Color band1Color;
  final Color upperBandColor;
  final Color lowerBandColor;

  // 透明度和线宽
  final double fillOpacity;
  final double vwapLineWidth;
  final double bandLineWidth;

  // 交易会话配置
  final int sessionStartHour;
  final int sessionStartMinute;
  final int timezoneOffset;

  const VWAPIndicatorConfig({
    // 默认标准差倍数
    this.devUp1 = 1.28,
    this.devDn1 = 1.28,
    this.devUp2 = 2.01,
    this.devDn2 = 2.01,
    this.devUp3 = 2.51,
    this.devDn3 = 2.51,
    this.devUp4 = 3.09,
    this.devDn4 = 3.09,
    this.devUp5 = 4.01,
    this.devDn5 = 4.01,
    
    // 默认显示控制
    this.showDv2 = true,
    this.showDv3 = true,
    this.showDv4 = true,
    this.showDv5 = false,
    this.showPrevVWAP = false,
    
    // 默认颜色配置
    this.vwapColor = const Color.fromARGB(255, 251, 251, 251),
    this.band1Color = Colors.grey,
    this.upperBandColor = Colors.red,
    this.lowerBandColor = Colors.green,
    
    // 默认透明度和线宽
    this.fillOpacity = 0.0, //线条背景颜色绘制
    this.vwapLineWidth = 2.0,
    this.bandLineWidth = 1.0,
    
    // 默认交易会话配置（中国时区8AM开始）
    this.sessionStartHour = 8,
    this.sessionStartMinute = 0,
    this.timezoneOffset = 8,
  });

  /// 复制并更新配置
  VWAPIndicatorConfig copyWith({
    double? devUp1, double? devDn1,
    double? devUp2, double? devDn2,
    double? devUp3, double? devDn3,
    double? devUp4, double? devDn4,
    double? devUp5, double? devDn5,
    bool? showDv2, bool? showDv3, bool? showDv4, bool? showDv5,
    bool? showPrevVWAP,
    Color? vwapColor, Color? band1Color,
    Color? upperBandColor, Color? lowerBandColor,
    double? fillOpacity,
    double? vwapLineWidth, double? bandLineWidth,
    int? sessionStartHour, int? sessionStartMinute, int? timezoneOffset,
  }) {
    return VWAPIndicatorConfig(
      devUp1: devUp1 ?? this.devUp1,
      devDn1: devDn1 ?? this.devDn1,
      devUp2: devUp2 ?? this.devUp2,
      devDn2: devDn2 ?? this.devDn2,
      devUp3: devUp3 ?? this.devUp3,
      devDn3: devDn3 ?? this.devDn3,
      devUp4: devUp4 ?? this.devUp4,
      devDn4: devDn4 ?? this.devDn4,
      devUp5: devUp5 ?? this.devUp5,
      devDn5: devDn5 ?? this.devDn5,
      showDv2: showDv2 ?? this.showDv2,
      showDv3: showDv3 ?? this.showDv3,
      showDv4: showDv4 ?? this.showDv4,
      showDv5: showDv5 ?? this.showDv5,
      showPrevVWAP: showPrevVWAP ?? this.showPrevVWAP,
      vwapColor: vwapColor ?? this.vwapColor,
      band1Color: band1Color ?? this.band1Color,
      upperBandColor: upperBandColor ?? this.upperBandColor,
      lowerBandColor: lowerBandColor ?? this.lowerBandColor,
      fillOpacity: fillOpacity ?? this.fillOpacity,
      vwapLineWidth: vwapLineWidth ?? this.vwapLineWidth,
      bandLineWidth: bandLineWidth ?? this.bandLineWidth,
      sessionStartHour: sessionStartHour ?? this.sessionStartHour,
      sessionStartMinute: sessionStartMinute ?? this.sessionStartMinute,
      timezoneOffset: timezoneOffset ?? this.timezoneOffset,
    );
  }
}

/// VWAP指标管理器
class VWAPIndicatorManager {
  static const FlexiIndicatorKey _vwapBandsKey = FlexiIndicatorKey('vwap_bands', label: '成交量加权平均趋势');
  
  final FlexiKlineController _controller;
  VWAPIndicatorConfig _config;
  bool _isActive = false;

  VWAPIndicatorManager({
    required FlexiKlineController controller,
    VWAPIndicatorConfig? initialConfig,
  }) : _controller = controller,
       _config = initialConfig ?? const VWAPIndicatorConfig();

  /// 获取当前配置
  VWAPIndicatorConfig get config => _config;

  /// 检查指标是否激活
  bool get isActive => _isActive;

  /// 添加VWAP标准差带指标到主图
  bool addIndicator() {
    try {
      // 检查是否已经注册了该指标
      if (!_controller.hasRegisterInMain(_vwapBandsKey)) {
        if (kDebugMode) {
          print('[VWAPIndicatorManager] VWAP Bands indicator not registered in main chart');
        }
        return false;
      }
      
      // 检查是否已经载入了该指标
      if (_controller.mainIndicatorKeys.contains(_vwapBandsKey)) {
        if (kDebugMode) {
          print('[VWAPIndicatorManager] VWAP Bands indicator already loaded in main chart');
        }
        _isActive = true;
        return true;
      }
      
      // 添加VWAP标准差带指标到主图
      _controller.addIndicatorInMain(_vwapBandsKey);
      _isActive = true;
      
      // 应用当前配置
      _applyConfig();
      
      if (kDebugMode) {
        print('[VWAPIndicatorManager] VWAP Bands indicator added to main chart');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('[VWAPIndicatorManager] Error adding VWAP Bands indicator: $e');
      }
      return false;
    }
  }

  /// 移除VWAP标准差带指标
  bool removeIndicator() {
    try {
      _controller.delIndicatorInMain(_vwapBandsKey);
      _isActive = false;
      if (kDebugMode) {
        print('[VWAPIndicatorManager] VWAP Bands indicator removed from main chart');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('[VWAPIndicatorManager] Error removing VWAP Bands indicator: $e');
      }
      return false;
    }
  }

  /// 更新配置
  bool updateConfig(VWAPIndicatorConfig newConfig) {
    _config = newConfig;
    if (_isActive) {
      return _applyConfig();
    }
    return true;
  }

  /// 快速更新颜色配置
  bool updateColors({
    Color? vwapColor,
    Color? band1Color,
    Color? upperBandColor,
    Color? lowerBandColor,
  }) {
    final updatedConfig = _config.copyWith(
      vwapColor: vwapColor,
      band1Color: band1Color,
      upperBandColor: upperBandColor,
      lowerBandColor: lowerBandColor,
    );
    return updateConfig(updatedConfig);
  }

  /// 快速更新标准差配置
  bool updateDeviations({
    double? devUp1, double? devDn1,
    double? devUp2, double? devDn2,
    double? devUp3, double? devDn3,
    double? devUp4, double? devDn4,
    double? devUp5, double? devDn5,
  }) {
    final updatedConfig = _config.copyWith(
      devUp1: devUp1,
      devDn1: devDn1,
      devUp2: devUp2,
      devDn2: devDn2,
      devUp3: devUp3,
      devDn3: devDn3,
      devUp4: devUp4,
      devDn4: devDn4,
      devUp5: devUp5,
      devDn5: devDn5,
    );
    return updateConfig(updatedConfig);
  }

  /// 快速更新显示选项
  bool updateVisibility({
    bool? showDv2,
    bool? showDv3,
    bool? showDv4,
    bool? showDv5,
    bool? showPrevVWAP,
  }) {
    final updatedConfig = _config.copyWith(
      showDv2: showDv2,
      showDv3: showDv3,
      showDv4: showDv4,
      showDv5: showDv5,
      showPrevVWAP: showPrevVWAP,
    );
    return updateConfig(updatedConfig);
  }

  /// 快速更新会话配置
  bool updateSession({
    int? sessionStartHour,
    int? sessionStartMinute,
    int? timezoneOffset,
  }) {
    final updatedConfig = _config.copyWith(
      sessionStartHour: sessionStartHour,
      sessionStartMinute: sessionStartMinute,
      timezoneOffset: timezoneOffset,
    );
    return updateConfig(updatedConfig);
  }

  /// 应用当前配置到指标
  bool _applyConfig() {
    try {
      // 获取当前指标配置
      final currentIndicator = _controller.getIndicator<VWAPBandsIndicator>(_vwapBandsKey);
      if (currentIndicator == null) {
        if (kDebugMode) {
          print('[VWAPIndicatorManager] VWAP Bands indicator not found');
        }
        return false;
      }

      // 创建新的配置
      final updatedIndicator = currentIndicator.copyWith(
        devUp1: _config.devUp1,
        devDn1: _config.devDn1,
        devUp2: _config.devUp2,
        devDn2: _config.devDn2,
        devUp3: _config.devUp3,
        devDn3: _config.devDn3,
        devUp4: _config.devUp4,
        devDn4: _config.devDn4,
        devUp5: _config.devUp5,
        devDn5: _config.devDn5,
        showDv2: _config.showDv2,
        showDv3: _config.showDv3,
        showDv4: _config.showDv4,
        showDv5: _config.showDv5,
        showPrevVWAP: _config.showPrevVWAP,
        vwapColor: _config.vwapColor,
        band1Color: _config.band1Color,
        upperBandColor: _config.upperBandColor,
        lowerBandColor: _config.lowerBandColor,
        fillOpacity: _config.fillOpacity,
        vwapLineWidth: _config.vwapLineWidth,
        bandLineWidth: _config.bandLineWidth,
        sessionStartHour: _config.sessionStartHour,
        sessionStartMinute: _config.sessionStartMinute,
        timezoneOffset: _config.timezoneOffset,
      );

      // 更新指标配置
      final success = _controller.updateIndicator(updatedIndicator);
      
      // 强制重新计算VWAP数据（触发重绘）
      if (success) {
        _controller.markRepaintChart(reset: true);
      }
      

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('[VWAPIndicatorManager] Error applying VWAP Bands config: $e');
      }
      return false;
    }
  }

  /// 创建预设配置
  static VWAPIndicatorConfig createPreset({
    required String name,
  }) {
    switch (name.toLowerCase()) {
      case 'conservative':
        return const VWAPIndicatorConfig(
          devUp1: 0.5, devDn1: 0.5,
          devUp2: 1.0, devDn2: 1.0,
          showDv2: true, showDv3: false, showDv4: false, showDv5: false,
          vwapColor: Colors.blue,
          upperBandColor: Colors.red,
          lowerBandColor: Colors.green,
        );
      case 'standard':
        return const VWAPIndicatorConfig(
          devUp1: 0.5, devDn1: 0.5,
          devUp2: 1.0, devDn2: 1.0,
          devUp3: 2.0, devDn3: 2.0,
          showDv2: true, showDv3: true, showDv4: false, showDv5: false,
          vwapColor: Colors.orange,
          upperBandColor: Colors.red,
          lowerBandColor: Colors.green,
        );
      case 'aggressive':
        return const VWAPIndicatorConfig(
          devUp1: 1.0, devDn1: 1.0,
          devUp2: 2.0, devDn2: 2.0,
          devUp3: 3.0, devDn3: 3.0,
          devUp4: 4.0, devDn4: 4.0,
          showDv2: true, showDv3: true, showDv4: true, showDv5: false,
          vwapColor: Colors.yellow,
          upperBandColor: Colors.red,
          lowerBandColor: Colors.green,
        );
      default:
        return const VWAPIndicatorConfig();
    }
  }
} 