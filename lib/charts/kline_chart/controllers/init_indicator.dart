import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../kline/flexi_kline.dart';
import 'vwap_indicator_manager.dart';
import 'volume_indicator_manager.dart';
import 'volume_profile_indicator_manager.dart';
import '../config/indicator_config_manager.dart';
import '../config/indicator_config/vwap_config.dart';
import '../config/indicator_config/volume_profile_config.dart';
import '../config/indicator_config/volume_profile_chart_config.dart';
import '../../kline/indicators/volume_profile/volume_profile.dart' as flexi_vp;
import '../config/indicator_config/volume_config.dart';

/// 指标初始化管理类
class KlineIndicatorInitializer {
  final FlexiKlineController _controller;
  late final IndicatorConfigManager _indicatorConfigManager;
  
  // 指标管理器
  late final VWAPIndicatorManager _vwapManager;
  late final VolumeIndicatorManager _volumeManager;
  late final VolumeProfileIndicatorManager _volumeProfileManager;
  
  KlineIndicatorInitializer({
    required FlexiKlineController controller,
    IndicatorConfigManager? preloadedConfigManager,
  }) : _controller = controller {
    // 使用预加载的配置管理器，如果没有则创建新的（保留预加载管理器的窗口索引）
    _indicatorConfigManager = preloadedConfigManager ?? IndicatorConfigManager();
    _initializeIndicators();
  }

  /// 获取VWAP指标管理器
  VWAPIndicatorManager get vwapManager => _vwapManager;

  /// 获取成交量指标管理器
  VolumeIndicatorManager get volumeManager => _volumeManager;

  /// 获取Volume Profile指标管理器
  VolumeProfileIndicatorManager get volumeProfileManager => _volumeProfileManager;

  /// 获取指标配置管理器
  IndicatorConfigManager get indicatorConfigManager => _indicatorConfigManager;

  /// 初始化所有指标
  void _initializeIndicators() {
    _initializeVWAPIndicator();
    _initializeVolumeIndicator();
    _initializeVolumeProfileIndicator();
  }

  /// 初始化VWAP指标
  void _initializeVWAPIndicator() {
    // 尝试从配置管理器获取保存的配置
    final savedConfig = _indicatorConfigManager.getConfig<VWAPConfig>(IndicatorType.vwap);
    
    // 如果有保存的配置，转换为VWAPIndicatorConfig；否则使用默认配置
    final vwapConfig = savedConfig != null 
        ? savedConfig.toVWAPIndicatorConfig()
        : const VWAPIndicatorConfig(
            sessionStartHour: 8,
            sessionStartMinute: 0,
            timezoneOffset: 8,
            vwapColor: Color.fromARGB(255, 251, 251, 251),
            band1Color: Colors.grey,
            upperBandColor: Colors.red,
            lowerBandColor: Colors.green,
            fillOpacity: 0.0,
            vwapLineWidth: 2.0,
            bandLineWidth: 1.0,
          );
    
    // 初始化VWAP指标管理器
    _vwapManager = VWAPIndicatorManager(
      controller: _controller,
      initialConfig: vwapConfig,
    );
    
    // 根据配置决定是否添加指标
    if (savedConfig?.enabled ?? true) {
      // 如果没有保存配置或者配置中启用了VWAP，则添加指标
      _vwapManager.addIndicator();
    }
  }

  /// 初始化成交量指标
  void _initializeVolumeIndicator() {
    // 尝试从配置管理器获取保存的配置
    final savedConfig = _indicatorConfigManager.getConfig<VolumeConfig>(IndicatorType.volume);
    
    // 如果有保存的配置，使用它；否则使用默认配置
    final volumeConfig = savedConfig ?? const VolumeConfig(
      enabled: false, // 默认禁用
      displayMode: 'area',
      bullishColor: Color(0xFF26A69A),
      bearishColor: Color(0xFFEF5350),
      showMA: true,
      maLength: 20,
      maColor: Color(0xFFFFEB3B),
      maLineWidth: 1.5,
      opacity: 0.8,
    );
    
    // 初始化成交量指标管理器
    _volumeManager = VolumeIndicatorManager(
      controller: _controller,
      initialConfig: volumeConfig,
    );
  }

  /// 初始化Volume Profile指标
  void _initializeVolumeProfileIndicator() {
    // 尝试从配置管理器获取保存的配置
    final savedConfig = _indicatorConfigManager.getConfig<VolumeProfileChartConfig>(IndicatorType.volumeProfileChart);
    
    // 如果有保存的配置，转换为flex_kline配置；否则使用默认配置
    final volumeProfileConfig = savedConfig != null 
        ? _convertToFlexiVolumeProfileConfig(savedConfig)
        : const flexi_vp.VolumeProfileConfig(
            showPOC: true,
            showVAH: true,
            showVAL: true,
            pocColor: Colors.yellow,
            vahColor: Colors.blue,
            valColor: Colors.blue,
            displayMode: flexi_vp.VolumeProfileDisplayMode.bidAsk,
            upVolumeColor: Color(0xFFFF5252),
            downVolumeColor: Color(0xFF4CAF50),
            totalVolumeColor: Color(0xFF2196F3),
            pocLineWidth: 1.5,
            vahLineWidth: 1.0,
            valLineWidth: 1.0,
            volumeOpacity: 0.8,
            lineOpacity: 1.0,
            priceZones: 100,
            valueAreaPercentage: 0.68,
            maxWidthPercent: 0.3,
          );
    
    // 初始化Volume Profile指标管理器
    _volumeProfileManager = VolumeProfileIndicatorManager(
      controller: _controller,
      initialConfig: volumeProfileConfig,
    );
  }

  /// 初始化指标配置
  Future<void> initializeIndicatorConfigs() async {
    await _indicatorConfigManager.loadConfigs();
    applyIndicatorConfigs();
  }

  /// 应用指标配置更新
  void applyIndicatorConfigs({VoidCallback? onVolumeProfileEnabledCallback}) {
    _applyVWAPConfig();
    _applyVolumeProfileConfig();
    _applyVolumeProfileChartConfig(onVolumeProfileEnabledCallback: onVolumeProfileEnabledCallback);
    _applyVolumeConfig();
  }

  /// 应用VWAP配置
  void _applyVWAPConfig() {
    final vwapConfig = _indicatorConfigManager.getConfig<VWAPConfig>(IndicatorType.vwap);
    if (vwapConfig != null) {
      final indicatorConfig = vwapConfig.toVWAPIndicatorConfig();
      _vwapManager.updateConfig(indicatorConfig);
      
      // 根据启用状态添加或移除指标
      if (vwapConfig.enabled && !_vwapManager.isActive) {
        _vwapManager.addIndicator();
      } else if (!vwapConfig.enabled && _vwapManager.isActive) {
        _vwapManager.removeIndicator();
      }
    }
  }

  /// 应用成交量轨迹配置
  void _applyVolumeProfileConfig() {
    final volumeProfileConfig = _indicatorConfigManager.getConfig<VolumeProfileConfig>(IndicatorType.volumeProfile);
    if (volumeProfileConfig != null) {
      // 更新FlexiKline的SettingConfig以同步颜色配置
      final currentConfig = _controller.settingConfig;
      final newConfig = currentConfig.copyWith(
        enableVolumeTracksRendering: volumeProfileConfig.enabled,
        buyTrackColor: volumeProfileConfig.bullishColor,
        sellTrackColor: volumeProfileConfig.bearishColor,
      );
      _controller.settingConfig = newConfig;
    }
  }

  /// 应用Volume Profile Chart配置
  void _applyVolumeProfileChartConfig({VoidCallback? onVolumeProfileEnabledCallback}) {
    final volumeProfileChartConfig = _indicatorConfigManager.getConfig<VolumeProfileChartConfig>(IndicatorType.volumeProfileChart);
    if (volumeProfileChartConfig != null) {
      // 将VolumeProfileChartConfig转换为flex_kline的VolumeProfileConfig
      final flexiConfig = _convertToFlexiVolumeProfileConfig(volumeProfileChartConfig);
      
      // 记录之前的状态
      final wasAdded = _volumeProfileManager.isIndicatorAdded;
      
      // 更新Volume Profile指标管理器的配置
      _volumeProfileManager.updateConfig(flexiConfig);
      
      // 根据启用状态添加或移除指标
      if (volumeProfileChartConfig.enabled && !_volumeProfileManager.isIndicatorAdded) {
        _volumeProfileManager.addIndicator();
        
        // 指标从禁用变为启用，触发数据重新获取
        if (!wasAdded) {
          onVolumeProfileEnabledCallback?.call();
        }
      } else if (!volumeProfileChartConfig.enabled && _volumeProfileManager.isIndicatorAdded) {
        _volumeProfileManager.removeIndicator();
      }
    }
  }

  /// 将VolumeProfileChartConfig转换为flex_kline的VolumeProfileConfig
  flexi_vp.VolumeProfileConfig _convertToFlexiVolumeProfileConfig(VolumeProfileChartConfig chartConfig) {
    // 转换显示模式
    flexi_vp.VolumeProfileDisplayMode displayMode;
    switch (chartConfig.displayMode) {
      case VolumeProfileChartDisplayMode.total:
        displayMode = flexi_vp.VolumeProfileDisplayMode.total;
        break;
      case VolumeProfileChartDisplayMode.separated:
        displayMode = flexi_vp.VolumeProfileDisplayMode.bidAsk;
        break;
    }

    // 转换线段样式
    flexi_vp.VolumeProfileLineStyle convertLineStyle(LineStyle style) {
      switch (style) {
        case LineStyle.solid:
          return flexi_vp.VolumeProfileLineStyle.solid;
        case LineStyle.dashed:
          return flexi_vp.VolumeProfileLineStyle.dashed;
      }
    }

    return flexi_vp.VolumeProfileConfig(
      showPOC: chartConfig.showPOC,
      showVAH: chartConfig.showVAH,
      showVAL: chartConfig.showVAL,
      pocColor: chartConfig.pocColor,
      vahColor: chartConfig.vahColor,
      valColor: chartConfig.valColor,
      pocLineStyle: convertLineStyle(chartConfig.pocLineStyle),
      vahLineStyle: convertLineStyle(chartConfig.vahLineStyle),
      valLineStyle: convertLineStyle(chartConfig.valLineStyle),
      displayMode: displayMode,
      upVolumeColor: chartConfig.upVolumeColor,
      downVolumeColor: chartConfig.downVolumeColor,
      totalVolumeColor: chartConfig.totalVolumeColor,
      pocLineWidth: 1.5, // 使用默认值
      vahLineWidth: 1.0,
      valLineWidth: 1.0,
      volumeOpacity: chartConfig.opacity,
      lineOpacity: 1.0,
      priceZones: 100,
      valueAreaPercentage: 0.68,
      maxWidthPercent: chartConfig.maxWidthPercent,
      smoothingStrength: chartConfig.smoothingStrength,
    );
  }

  /// 应用成交量指标配置
  void _applyVolumeConfig() {
    final volumeConfig = _indicatorConfigManager.getConfig<VolumeConfig>(IndicatorType.volume);
    if (volumeConfig != null) {
      _volumeManager.updateConfig(volumeConfig);
      
      // 根据启用状态添加或移除指标
      if (volumeConfig.enabled) {
        // 如果启用，强制重新添加指标确保正确绘制
        if (_volumeManager.isIndicatorAdded) {
          _volumeManager.removeIndicator();
        }
        _volumeManager.addIndicator();
      } else if (!volumeConfig.enabled && _volumeManager.isIndicatorAdded) {
        _volumeManager.removeIndicator();
      }
    }
  }

  /// 更新VWAP会话配置（在数据加载后调用）
  void updateVWAPSession() {
    if (_vwapManager.isActive) {
      _vwapManager.updateSession(
        sessionStartHour: 8,
        sessionStartMinute: 0,
        timezoneOffset: 8,
      );
    }
  }

  /// 释放资源
  void dispose() {
    // 清理指标资源
    if (_vwapManager.isActive) {
      _vwapManager.removeIndicator();
    }
    if (_volumeManager.isIndicatorAdded) {
      _volumeManager.removeIndicator();
    }
  }
} 