import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';

/// 性能优化管理器 - 第一阶段优化
class PerformanceOptimizer {
  // WebSocket消息缓冲相关
  final Queue<dynamic> _messageBuffer = Queue<dynamic>();
  Timer? _messageProcessTimer;
  static const int _maxBufferSize = 100;
  static const Duration _bufferProcessInterval = Duration(milliseconds: 50);
  
  // 防抖更新相关
  final Map<String, Timer?> _debounceTimers = {};
  static const Duration _debounceDelay = Duration(milliseconds: 100);
  
  // 内存清理相关
  Timer? _memoryCleanupTimer;
  static const Duration _memoryCleanupInterval = Duration(minutes: 5);
  final List<Function()> _cleanupTasks = [];
  
  // 消息处理回调
  Function(List<dynamic>)? _messageProcessor;
  
  /// 初始化性能优化器
  void initialize({Function(List<dynamic>)? messageProcessor}) {
    _messageProcessor = messageProcessor;
    _startMessageProcessTimer();
    _startMemoryCleanupTimer();
    
    if (kDebugMode) {
      print('[PerformanceOptimizer] 已初始化 - 消息缓冲: ${_bufferProcessInterval.inMilliseconds}ms, 内存清理: ${_memoryCleanupInterval.inMinutes}分钟');
    }
  }
  
  /// 添加WebSocket消息到缓冲区
  void bufferMessage(dynamic message) {
    if (_messageBuffer.length >= _maxBufferSize) {
      // 缓冲区满时移除最旧的消息
      _messageBuffer.removeFirst();
    }
    _messageBuffer.add(message);
  }
  
  /// 启动消息处理定时器
  void _startMessageProcessTimer() {
    _messageProcessTimer?.cancel();
    _messageProcessTimer = Timer.periodic(_bufferProcessInterval, (_) {
      _processBufferedMessages();
    });
  }
  
  /// 处理缓冲的消息
  void _processBufferedMessages() {
    if (_messageBuffer.isEmpty || _messageProcessor == null) return;
    
    final List<dynamic> messagesToProcess = _messageBuffer.toList();
    _messageBuffer.clear();
    
    try {
      _messageProcessor!(messagesToProcess);
    } catch (e) {
      if (kDebugMode) {
        print('[PerformanceOptimizer] 处理缓冲消息时出错: $e');
      }
    }
  }
  
  /// 防抖执行函数
  void debounce(String key, VoidCallback callback, {Duration? delay}) {
    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(delay ?? _debounceDelay, () {
      callback();
      _debounceTimers.remove(key);
    });
  }
  
  /// 立即执行防抖任务（取消防抖）
  void executeImmediately(String key, VoidCallback callback) {
    _debounceTimers[key]?.cancel();
    _debounceTimers.remove(key);
    callback();
  }
  
  /// 注册内存清理任务
  void registerCleanupTask(Function() cleanupTask) {
    _cleanupTasks.add(cleanupTask);
  }
  
  /// 启动内存清理定时器
  void _startMemoryCleanupTimer() {
    _memoryCleanupTimer?.cancel();
    _memoryCleanupTimer = Timer.periodic(_memoryCleanupInterval, (_) {
      _performMemoryCleanup();
    });
  }
  
  /// 执行内存清理
  void _performMemoryCleanup() {
    try {
      for (final cleanupTask in _cleanupTasks) {
        cleanupTask();
      }
      
      // 清理防抖计时器映射中的空引用
      _debounceTimers.removeWhere((key, timer) => timer == null || !timer.isActive);
      
      if (kDebugMode) {
        print('[PerformanceOptimizer] 内存清理完成 - 清理任务数: ${_cleanupTasks.length}, 活跃防抖计时器: ${_debounceTimers.length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[PerformanceOptimizer] 内存清理时出错: $e');
      }
    }
  }
  
  /// 手动触发内存清理
  void forceMemoryCleanup() {
    _performMemoryCleanup();
  }
  
  /// 清理消息缓冲区（币种切换时使用）
  void clearMessageBuffer() {
    _messageBuffer.clear();
    if (kDebugMode) {
      print('[PerformanceOptimizer] 已清理消息缓冲区');
    }
  }
  
  /// 获取缓冲区状态
  Map<String, dynamic> getBufferStatus() {
    return {
      'bufferSize': _messageBuffer.length,
      'maxBufferSize': _maxBufferSize,
      'activeDebounceTimers': _debounceTimers.length,
      'registeredCleanupTasks': _cleanupTasks.length,
    };
  }
  
  /// 释放资源
  void dispose() {
    _messageProcessTimer?.cancel();
    _memoryCleanupTimer?.cancel();
    
    // 取消所有防抖计时器
    for (final timer in _debounceTimers.values) {
      timer?.cancel();
    }
    _debounceTimers.clear();
    
    // 清空缓冲区和清理任务
    _messageBuffer.clear();
    _cleanupTasks.clear();
    
    if (kDebugMode) {
      print('[PerformanceOptimizer] 已释放所有资源');
    }
  }
} 