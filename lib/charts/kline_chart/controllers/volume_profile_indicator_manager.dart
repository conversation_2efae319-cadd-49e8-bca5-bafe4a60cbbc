import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../kline/flexi_kline.dart';
import '../../kline/indicators/volume_profile/volume_profile.dart';

/// Volume Profile指标管理器
class VolumeProfileIndicatorManager {
  final FlexiKlineController _controller;
  VolumeProfileConfig _config;
  
  static const FlexiIndicatorKey _volumeProfileKey = FlexiIndicatorKey('volume_profile', label: 'Volume Profile');

  VolumeProfileIndicatorManager({
    required FlexiKlineController controller,
    VolumeProfileConfig? initialConfig,
  }) : _controller = controller, _config = initialConfig ?? const VolumeProfileConfig();

  /// 获取当前配置
  VolumeProfileConfig get config => _config;

  /// 是否已添加指标
  bool get isIndicatorAdded {
    return _controller.getIndicator<VolumeProfileIndicator>(_volumeProfileKey) != null;
  }

  /// 添加Volume Profile指标到主图
  bool addIndicator() {
    try {
      // 检查是否已经注册了该指标
      if (!_controller.hasRegisterInMain(_volumeProfileKey)) {
        if (kDebugMode) {
          print('[VolumeProfileIndicatorManager] Volume Profile指标未注册');
        }
        return false;
      }

      // 检查是否已经载入了该指标
      if (_controller.mainIndicatorKeys.contains(_volumeProfileKey)) {
        if (kDebugMode) {
          print('[VolumeProfileIndicatorManager] Volume Profile指标已存在于主图中');
        }
        return true;
      }

      // 添加到主图
      _controller.addIndicatorInMain(_volumeProfileKey);
      
      // 立即应用当前配置，确保新添加的指标使用正确的样式
      _applyConfig();
      
      if (kDebugMode) {
        print('[VolumeProfileIndicatorManager] Volume Profile指标已添加并应用配置');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('[VolumeProfileIndicatorManager] 添加Volume Profile指标失败: $e');
      }
      return false;
    }
  }

  /// 移除Volume Profile指标
  bool removeIndicator() {
    try {
      _controller.delIndicatorInMain(_volumeProfileKey);
      
      if (kDebugMode) {
        print('[VolumeProfileIndicatorManager] Volume Profile指标已移除');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('[VolumeProfileIndicatorManager] 移除Volume Profile指标失败: $e');
      }
      return false;
    }
  }

  /// 更新配置
  bool updateConfig(VolumeProfileConfig newConfig) {
    try {
      _config = newConfig;
      
      // 如果指标已添加，直接更新配置而不是重新添加
      if (isIndicatorAdded) {
        return _applyConfig();
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('[VolumeProfileIndicatorManager] 更新配置失败: $e');
      }
      return false;
    }
  }

  /// 应用当前配置到指标
  bool _applyConfig() {
    try {
      // 获取当前指标配置
      final currentIndicator = _controller.getIndicator<VolumeProfileIndicator>(_volumeProfileKey);
      if (currentIndicator == null) {
        if (kDebugMode) {
          print('[VolumeProfileIndicatorManager] Volume Profile指标未找到');
        }
        return false;
      }

      // 创建新的配置
      final updatedIndicator = currentIndicator.copyWith(
        config: _config,
      );

      // 更新指标配置
      final success = _controller.updateIndicator(updatedIndicator);
      
      // 强制重新计算Volume Profile数据（触发重绘）
      if (success) {
        _controller.markRepaintChart(reset: true);
      }

      if (kDebugMode) {
        print('[VolumeProfileIndicatorManager] 配置已更新并应用');
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('[VolumeProfileIndicatorManager] 应用配置失败: $e');
      }
      return false;
    }
  }

  /// 切换指标启用状态
  bool toggleIndicator(bool enabled) {
    if (enabled) {
      return addIndicator();
    } else {
      return removeIndicator();
    }
  }

  /// 更新显示模式
  bool updateDisplayMode(VolumeProfileDisplayMode displayMode) {
    final newConfig = _config.copyWith(displayMode: displayMode);
    return updateConfig(newConfig);
  }

  /// 更新颜色配置
  bool updateColors({
    Color? pocColor,
    Color? vahColor,
    Color? valColor,
    Color? upVolumeColor,
    Color? downVolumeColor,
    Color? totalVolumeColor,
  }) {
    final newConfig = _config.copyWith(
      pocColor: pocColor,
      vahColor: vahColor,
      valColor: valColor,
      upVolumeColor: upVolumeColor,
      downVolumeColor: downVolumeColor,
      totalVolumeColor: totalVolumeColor,
    );
    return updateConfig(newConfig);
  }

  /// 更新线条显示开关
  bool updateLineVisibility({
    bool? showPOC,
    bool? showVAH,
    bool? showVAL,
  }) {
    final newConfig = _config.copyWith(
      showPOC: showPOC,
      showVAH: showVAH,
      showVAL: showVAL,
    );
    return updateConfig(newConfig);
  }

  /// 更新价格区间和Value Area配置
  bool updateAnalysisParams({
    int? priceZones,
    double? valueAreaPercentage,
  }) {
    final newConfig = _config.copyWith(
      priceZones: priceZones,
      valueAreaPercentage: valueAreaPercentage,
    );
    return updateConfig(newConfig);
  }

  /// 更新透明度配置
  bool updateOpacity({
    double? volumeOpacity,
    double? lineOpacity,
  }) {
    final newConfig = _config.copyWith(
      volumeOpacity: volumeOpacity,
      lineOpacity: lineOpacity,
    );
    return updateConfig(newConfig);
  }

  /// 强制重绘指标
  void forceRedraw() {
    if (isIndicatorAdded) {
      // 通过重新添加指标来强制重绘
      removeIndicator();
      addIndicator();
      // 重新添加后需要应用当前配置
      _applyConfig();
      
      if (kDebugMode) {
        print('[VolumeProfileIndicatorManager] 强制重绘完成，配置已重新应用');
      }
    }
  }

  /// 根据数据更新指标（当有新的Volume Profile数据时调用）
  void updateWithData(Map<String, dynamic> volumeProfileData) {
    if (!isIndicatorAdded) return;

    // 将数据传递给Volume Profile指标
    VolumeProfilePaintObject.setExternalData(volumeProfileData);
    
    // 触发重绘，但不重置配置
    _controller.markRepaintChart(reset: false);
    
    if (kDebugMode) {
      print('[VolumeProfileIndicatorManager] 已使用新数据更新指标并触发重绘');
    }
  }

  /// 获取预设配置
  static VolumeProfileConfig getPresetConfig(String presetName) {
    switch (presetName.toLowerCase()) {
      case 'minimal':
        return const VolumeProfileConfig(
          showPOC: true,
          showVAH: false,
          showVAL: false,
          displayMode: VolumeProfileDisplayMode.total,
          volumeOpacity: 0.6,
          lineOpacity: 0.8,
        );
      case 'detailed':
        return const VolumeProfileConfig(
          showPOC: true,
          showVAH: true,
          showVAL: true,
          displayMode: VolumeProfileDisplayMode.bidAsk,
          volumeOpacity: 0.8,
          lineOpacity: 1.0,
        );
      case 'trading':
        return const VolumeProfileConfig(
          showPOC: true,
          showVAH: true,
          showVAL: true,
          displayMode: VolumeProfileDisplayMode.overlay,
          volumeOpacity: 0.7,
          lineOpacity: 0.9,
          priceZones: 100,
          valueAreaPercentage: 0.68,
        );
      default:
        return const VolumeProfileConfig();
    }
  }

  /// 重置为默认配置
  bool resetToDefault() {
    return updateConfig(const VolumeProfileConfig());
  }

  /// 检查指标状态
  bool isIndicatorHealthy() {
    try {
      if (!isIndicatorAdded) return false;
      
      final indicator = _controller.getIndicator<VolumeProfileIndicator>(_volumeProfileKey);
      return indicator != null;
    } catch (e) {
      if (kDebugMode) {
        print('[VolumeProfileIndicatorManager] 健康检查失败: $e');
      }
      return false;
    }
  }

  /// 获取指标实例（如果存在）
  VolumeProfileIndicator? getIndicatorInstance() {
    try {
      return _controller.getIndicator<VolumeProfileIndicator>(_volumeProfileKey);
    } catch (e) {
      if (kDebugMode) {
        print('[VolumeProfileIndicatorManager] 获取指标实例失败: $e');
      }
      return null;
    }
  }

  /// 销毁管理器
  void dispose() {
    if (isIndicatorAdded) {
      removeIndicator();
    }
  }
} 