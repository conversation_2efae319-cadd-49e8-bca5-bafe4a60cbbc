import 'package:flutter/foundation.dart';
import 'dart:math' as math;
import 'package:decimal/decimal.dart';
import '../../kline/flexi_kline.dart';
import '../../../services/clients/request_client.dart';
import '../../../services/clients/websocket_client.dart';
import '../services/kline_data_manager.dart';
import '../cache/kline_data_cache.dart';
import '../themes/simple_kline_theme.dart';

/// 数据加载管理类
class KlineDataLoader {
  final KlineDataManager _dataManager;
  final FlexiKlineController _controller;
  int _pricePrecision; // 改为可变的，支持动态调整
  final Function(bool)? _onLoadingStateChanged;
  final Function(double, double)? _onPriceRangeUpdated;

  bool _isLoading = false;
  bool _enableVolumeProfile = false;

  // 独立的WebSocket连接实例
  WebsocketClient? _websocketClient;
  String? _currentSubscribedSymbol;

  // 价格范围记录
  double _highestPrice = 0.0;
  double _lowestPrice = double.maxFinite;

  // K线数据缓存器
  final KlineDataCache _klineDataCache = KlineDataCache();

  /// 获取K线数据缓存器（供外部访问）
  KlineDataCache get klineDataCache => _klineDataCache;

  KlineDataLoader({
    required KlineDataManager dataManager,
    required FlexiKlineController controller,
    required int pricePrecision,
    Function(bool)? onLoadingStateChanged,
    Function(double, double)? onPriceRangeUpdated,
  }) : _dataManager = dataManager,
       _controller = controller,
       _pricePrecision = pricePrecision, // 初始值
       _onLoadingStateChanged = onLoadingStateChanged,
       _onPriceRangeUpdated = onPriceRangeUpdated;

  bool get isLoading => _isLoading;

  /// 根据价格动态计算精度
  /// 如果价格大于100小于1000 精度设置为3，小于100 设置4 大于1000 设置为2
  int _calculatePricePrecision(double price) {
    if (price >= 1000) {
      return 2;
    } else if (price >= 100) {
      return 3;
    } else {
      return 4;
    }
  }

  /// 根据K线数据更新价格精度
  void _updatePricePrecisionFromData(List<CandleModel> candles) {
    if (candles.isEmpty) return;

    // 获取最后一条K线数据的收盘价
    final latestCandle = candles.first; // candles已经按时间倒序排列
    final double latestPrice = latestCandle.c.toDouble();

    // 计算新的精度
    final int newPrecision = _calculatePricePrecision(latestPrice);

    // 如果精度发生变化，则更新
    if (newPrecision != _pricePrecision) {
      final oldPrecision = _pricePrecision;
      _pricePrecision = newPrecision;

      if (kDebugMode) {
        print(
          '[KlineDataLoader] 根据价格 $latestPrice 更新精度: $oldPrecision -> $newPrecision',
        );
      }
    }
  }

  /// 设置加载状态
  void _setLoadingState(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      _onLoadingStateChanged?.call(_isLoading);
    }
  }

  /// 设置是否启用成交量轨迹绘制
  void setVolumeProfileEnabled(bool enabled) {
    _enableVolumeProfile = enabled;
  }

  /// 根据配置设置代理
  void _setProxyFromConfig(RequestClient requestClient) {
    try {
      // 从主题配置中读取代理设置
      final theme = _controller.theme;
      if (theme is SimpleFlexiKlineTheme) {
        if (theme.enableProxy &&
            theme.proxyHost.isNotEmpty &&
            theme.proxyPort > 0) {
          final proxyUrl = '${theme.proxyHost}:${theme.proxyPort}';
          requestClient.setProxy(proxyUrl);
        }
      }
      // ignore: empty_catches
    } catch (e) {}
  }

  /// 根据配置设置WebSocket代理
  void _setWebSocketProxyFromConfig(WebsocketClient websocketClient) {
    try {
      // 从主题配置中读取代理设置
      final theme = _controller.theme;
      if (theme is SimpleFlexiKlineTheme) {
        if (theme.enableProxy &&
            theme.proxyHost.isNotEmpty &&
            theme.proxyPort > 0) {
          final proxyUrl = '${theme.proxyHost}:${theme.proxyPort}';
          websocketClient.setProxy(proxyUrl);
        }
      }
      // ignore: empty_catches
    } catch (e) {}
  }

  /// WebSocket连接和订阅
  Future<void> connectAndSubscribeAggTrade(
    String instrumentId,
    Function(dynamic) onMessage,
  ) async {
    // 先断开之前的WebSocket连接
    await disconnectWebSocket();

    final normalizedSymbol = instrumentId.replaceAll('/', '').toUpperCase();

    if (kDebugMode) {
      print('[KlineDataLoader] 创建独立WebSocket连接: $normalizedSymbol');
    }

    final String streamName = '${normalizedSymbol.toLowerCase()}@aggTrade';
    final String wsUrl = 'wss://fstream.binance.com/ws/$streamName';

    _websocketClient = WebsocketClient(
      url: wsUrl,
      onConnected: () {
        if (kDebugMode) {
          print('[KlineDataLoader] WebSocket连接成功: $normalizedSymbol');
        }
      },
      onMessage: (message) {
        // 验证消息是否来自当前订阅的币种
        if (_currentSubscribedSymbol == normalizedSymbol) {
          onMessage(message);
        } else {
          if (kDebugMode) {
            print(
              '[KlineDataLoader] 忽略非当前币种的消息: 期望 $normalizedSymbol，当前订阅 $_currentSubscribedSymbol',
            );
          }
        }
      },
      onDisconnected: () {
        if (kDebugMode) {
          print('[KlineDataLoader] WebSocket连接断开: $normalizedSymbol');
        }
      },
      onError: (error) {
        if (kDebugMode) {
          print('[KlineDataLoader] WebSocket错误 $normalizedSymbol: $error');
        }
      },
    );

    // 设置代理
    _setWebSocketProxyFromConfig(_websocketClient!);

    // 记录当前订阅的币种
    _currentSubscribedSymbol = normalizedSymbol;

    // 连接WebSocket
    await _websocketClient!.connect();

    if (kDebugMode) {
      print('[KlineDataLoader] 独立WebSocket连接已建立: $normalizedSymbol -> $wsUrl');
    }
  }

  /// 断开WebSocket连接（用于币种切换）
  Future<void> disconnectWebSocket() async {
    if (_websocketClient != null) {
      final disconnectedSymbol = _currentSubscribedSymbol;

      await _websocketClient!.disconnect();
      _websocketClient = null;
      _currentSubscribedSymbol = null;

      if (kDebugMode) {
        print('[KlineDataLoader] 独立WebSocket连接已断开: $disconnectedSymbol');
      }
    }
  }

  /// 加载更多K线数据的回调
  Future<void> onLoadMoreCandles(
    CandleReq request,
    String currentInstrumentId,
    String currentPeriod,
  ) async {
    _setLoadingState(true);

    try {
      final currentDataEndTime = request.after;
      if (currentDataEndTime == null) {
        return;
      }

      final previousTradingDayRange = _calculatePreviousTradingDayRange(
        currentDataEndTime,
      );
      final startTime = previousTradingDayRange['startTime']!;
      final endTime = previousTradingDayRange['endTime']!;

      final historicalCandles = await _loadHistoricalData(
        startTime,
        endTime,
        currentInstrumentId,
        currentPeriod,
      );

      if (historicalCandles.isEmpty) {
        return;
      }

      final uniqueCandles = _removeDuplicateCandles(historicalCandles);

      // 根据最新价格更新精度
      _updatePricePrecisionFromData(uniqueCandles);

      final candleReq = CandleReq(
        instId: request.instId,
        bar: request.bar,
        precision: _pricePrecision, // 使用更新后的精度
      );

      _controller.updateKlineData(candleReq, uniqueCandles);
    } catch (e) {
      if (kDebugMode) {
        print('[KlineDataLoader] Exception during load more candles: $e');
      }
    } finally {
      _setLoadingState(false);
    }
  }

  /// 加载K线数据（支持缓存和增量更新）
  Future<void> loadKlineData(
    String currentInstrumentId,
    String currentPeriod,
  ) async {
    _setLoadingState(true);

    try {
      final String normalizedInstrumentId = currentInstrumentId.replaceAll(
        '/',
        '',
      );

      // 1. 尝试从缓存加载数据
      final cachedData = await _klineDataCache.getCachedData(
        normalizedInstrumentId,
        currentPeriod,
      );

      List<CandleModel> finalCandles = [];

      if (cachedData != null) {
        final List<CandleModel> cachedCandles =
            cachedData['candles'] as List<CandleModel>;
        final int? lastCacheTime = await _klineDataCache.getLastCloseTime(
          normalizedInstrumentId,
          currentPeriod,
        );

        if (kDebugMode) {
          print(
            '[KlineDataLoader] 发现缓存数据: ${cachedCandles.length}条, 最后时间: ${lastCacheTime != null ? DateTime.fromMillisecondsSinceEpoch(lastCacheTime) : '无'}',
          );
        }

        // 2. 计算增量数据时间范围
        final currentTime = DateTime.now().millisecondsSinceEpoch;
        final incrementalStartTime =
            lastCacheTime ?? _calculateTradingDayRange()['startTime']!;

        // 3. 获取增量数据
        final incrementalCandles = await _fetchIncrementalKlineData(
          incrementalStartTime,
          currentTime,
          normalizedInstrumentId,
          currentPeriod,
        );

        if (kDebugMode) {
          print('[KlineDataLoader] 获取增量数据: ${incrementalCandles.length}条');
        }

        // 4. 合并数据
        finalCandles = _klineDataCache.mergeIncrementalData(
          cachedCandles,
          incrementalCandles,
        );

        // 5. 保存合并后的数据到缓存
        await _klineDataCache.saveCachedData(
          normalizedInstrumentId,
          currentPeriod,
          finalCandles,
        );
      } else {
        // 没有缓存数据，加载完整数据
        if (kDebugMode) {
          print('[KlineDataLoader] 无缓存数据，加载完整数据');
        }

        finalCandles = await _loadFullKlineData(
          normalizedInstrumentId,
          currentPeriod,
        );

        // 保存到缓存
        if (finalCandles.isNotEmpty) {
          await _klineDataCache.saveCachedData(
            normalizedInstrumentId,
            currentPeriod,
            finalCandles,
          );
        }
      }

      if (finalCandles.isEmpty) {
        if (kDebugMode) {
          print('[KlineDataLoader] No kline data available');
        }
        return;
      }

      // 处理数据
      final uniqueCandles = _removeDuplicateCandles(finalCandles);
      _updatePriceRange(uniqueCandles);
      uniqueCandles.sort((a, b) => b.ts.compareTo(a.ts));

      // 根据最新价格更新精度
      _updatePricePrecisionFromData(uniqueCandles);

      if (uniqueCandles.isNotEmpty) {
        final latestCandleOriginal = uniqueCandles.first;
        final latestCandleConfirmed = latestCandleOriginal.copyWith(
          confirm: '1',
        );
        uniqueCandles[0] = latestCandleConfirmed;
      }

      final candleReq = CandleReq(
        instId: normalizedInstrumentId,
        bar: currentPeriod,
        precision: _pricePrecision, // 使用动态更新后的精度
      );

      _controller.switchKlineData(candleReq, useCacheFirst: true);
      _controller.updateKlineData(candleReq, uniqueCandles);
      _dataManager.setKlineData(uniqueCandles);

      if (kDebugMode) {
        print(
          '[KlineDataLoader] Successfully loaded ${uniqueCandles.length} kline records',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('[KlineDataLoader] Exception during kline data fetch: $e');
      }
    } finally {
      _setLoadingState(false);
    }
  }

  /// 加载完整K线数据（原有逻辑）
  Future<List<CandleModel>> _loadFullKlineData(
    String instrumentId,
    String period,
  ) async {
    final requestClient = RequestClient();
    _setProxyFromConfig(requestClient);

    final timeRange = _calculateTradingDayRange();
    final startTime = timeRange['startTime']!;
    final endTime = timeRange['endTime']!;

    final periodMs = _getPeriodMilliseconds(period);

    final List<CandleModel> allCandles = [];
    const int maxBatchSize = 1500;
    int currentStartTime = startTime;

    while (currentStartTime < endTime) {
      final batchEndTime = math.min(
        currentStartTime + (maxBatchSize * periodMs),
        endTime,
      );

      final batchCandles = await _fetchKlineBatch(
        requestClient,
        currentStartTime,
        batchEndTime,
        maxBatchSize,
        instrumentId,
        period,
      );

      if (batchCandles.isEmpty) {
        if (kDebugMode) {
          print('[KlineDataLoader] No more data available, breaking loop');
        }
        break;
      }

      allCandles.addAll(batchCandles);

      if (batchCandles.isNotEmpty) {
        currentStartTime = batchCandles.last.ts + periodMs;
      } else {
        break;
      }

      if (kDebugMode) {
        print(
          '[KlineDataLoader] Fetched batch: ${batchCandles.length} candles, total: ${allCandles.length}',
        );
      }

      if (batchCandles.isNotEmpty && batchCandles.last.ts >= endTime) {
        if (kDebugMode) {
          print('[KlineDataLoader] Reached end time, stopping data fetch');
        }
        break;
      }
    }

    return allCandles;
  }

  /// 获取增量K线数据
  Future<List<CandleModel>> _fetchIncrementalKlineData(
    int startTime,
    int endTime,
    String instrumentId,
    String period,
  ) async {
    final requestClient = RequestClient();
    _setProxyFromConfig(requestClient);

    return await _fetchKlineBatchWithTimeRange(
      requestClient,
      startTime,
      endTime,
      1500, // 最大1500条
      instrumentId,
      period,
    );
  }

  /// 根据时间范围获取K线数据
  Future<List<CandleModel>> _fetchKlineBatchWithTimeRange(
    RequestClient requestClient,
    int startTime,
    int endTime,
    int limit,
    String instrumentId,
    String period,
  ) async {
    final String apiSymbol = instrumentId;
    final String apiInterval = period;

    const String apiUrl = 'https://fapi.binance.com/fapi/v1/continuousKlines';
    final Map<String, dynamic> queryParameters = {
      'pair': apiSymbol,
      'contractType': 'PERPETUAL',
      'interval': apiInterval,
      'startTime': startTime,
      'endTime': endTime,
      'limit': limit,
    };

    try {
      final response = await requestClient.get(
        apiUrl,
        queryParameters: queryParameters,
      );

      if (response.statusCode == 200) {
        final List<dynamic> rawKlineData = response.data as List<dynamic>;
        return _convertRawDataToCandles(rawKlineData);
      }
    } catch (e) {
      if (kDebugMode) {
        print('[KlineDataLoader] Error fetching incremental batch: $e');
      }
    }

    return [];
  }

  /// 将原始数据转换为CandleModel列表
  List<CandleModel> _convertRawDataToCandles(List<dynamic> rawData) {
    return rawData.map((item) {
      final List<dynamic> dataArray = item as List<dynamic>;

      final int timestamp =
          dataArray[0] is int
              ? dataArray[0] as int
              : int.parse(dataArray[0].toString());
      final double open = double.parse(dataArray[1].toString());
      final double high = double.parse(dataArray[2].toString());
      final double low = double.parse(dataArray[3].toString());
      final double close = double.parse(dataArray[4].toString());
      final double volume = double.parse(dataArray[5].toString());
      final double quoteVolume =
          dataArray.length > 7 ? double.parse(dataArray[7].toString()) : 0.0;

      // 总是解析主动买入成交量，用于成交量指标颜色判断
      double takerBuyVolume = 0.0;
      double takerSellVolume = 0.0;
      if (dataArray.length > 9) {
        takerBuyVolume = double.parse(dataArray[9].toString());
        takerSellVolume = volume - takerBuyVolume;
      }

      // 创建PriceLevelDetail用于成交量指标颜色判断
      PriceLevelDetail? priceLevelDetail;
      if (takerBuyVolume > 0 || takerSellVolume > 0) {
        if (_enableVolumeProfile) {
          // 完整的价格区间详情（用于成交量轨迹）
          priceLevelDetail = _createPriceLevelDetail(
            open,
            high,
            low,
            close,
            takerBuyVolume,
            takerSellVolume,
          );
        } else {
          // 简化版本（仅包含总成交量信息，用于成交量指标颜色判断）
          priceLevelDetail = PriceLevelDetail(
            buyLevels: null,
            sellLevels: null,
            totalBuyVolume: takerBuyVolume > 0 ? takerBuyVolume : null,
            totalSellVolume: takerSellVolume > 0 ? takerSellVolume : null,
          );
        }
      }

      return CandleModel(
        ts: timestamp,
        o: Decimal.parse(open.toStringAsFixed(_pricePrecision)),
        h: Decimal.parse(high.toStringAsFixed(_pricePrecision)),
        l: Decimal.parse(low.toStringAsFixed(_pricePrecision)),
        c: Decimal.parse(close.toStringAsFixed(_pricePrecision)),
        v: Decimal.parse(volume.toStringAsFixed(2)),
        vc: Decimal.parse(quoteVolume.toStringAsFixed(2)),
        confirm: '1',
        priceLevel: priceLevelDetail,
      );
    }).toList();
  }

  /// 创建价格区间详情
  PriceLevelDetail? _createPriceLevelDetail(
    double open,
    double high,
    double low,
    double close,
    double takerBuyVolume,
    double takerSellVolume,
  ) {
    List<PriceLevelEntry>? buyLevels;
    List<PriceLevelEntry>? sellLevels;

    if (takerBuyVolume > 0) {
      final double minPrice =
          close > open ? math.min(low, close) : math.min(low, open);
      final double maxPrice =
          close > open ? math.min(high, close) : math.min(high, open);

      buyLevels = [
        PriceLevelEntry(
          priceMin: double.parse(minPrice.toStringAsFixed(_pricePrecision)),
          priceMax: double.parse(maxPrice.toStringAsFixed(_pricePrecision)),
          volume: takerBuyVolume,
        ),
      ];
    }

    if (takerSellVolume > 0) {
      final double minPrice =
          close > open ? math.min(low, close) : math.min(low, open);
      final double maxPrice =
          close > open ? math.min(high, close) : math.min(high, open);

      sellLevels = [
        PriceLevelEntry(
          priceMin: double.parse(minPrice.toStringAsFixed(_pricePrecision)),
          priceMax: double.parse(maxPrice.toStringAsFixed(_pricePrecision)),
          volume: takerSellVolume,
        ),
      ];
    }

    if (buyLevels != null || sellLevels != null) {
      return PriceLevelDetail(
        buyLevels: buyLevels,
        sellLevels: sellLevels,
        totalBuyVolume: takerBuyVolume > 0 ? takerBuyVolume : null,
        totalSellVolume: takerSellVolume > 0 ? takerSellVolume : null,
      );
    }

    return null;
  }

  /// 去重处理
  List<CandleModel> _removeDuplicateCandles(List<CandleModel> candles) {
    final Map<int, CandleModel> uniqueCandles = {};

    for (final candle in candles) {
      if (uniqueCandles.containsKey(candle.ts)) {
        final existing = uniqueCandles[candle.ts]!;
        if (candle.confirm == '1' || existing.confirm != '1') {
          uniqueCandles[candle.ts] = candle;
        }
      } else {
        uniqueCandles[candle.ts] = candle;
      }
    }

    final result = uniqueCandles.values.toList();
    if (kDebugMode && result.length != candles.length) {
      print(
        '[KlineDataLoader] Removed ${candles.length - result.length} duplicate candles',
      );
    }

    return result;
  }

  /// 更新价格范围
  void _updatePriceRange(List<CandleModel> candles) {
    if (candles.isEmpty) return;

    double highestPrice = 0.0;
    double lowestPrice = double.maxFinite;

    for (final candle in candles) {
      final high = candle.h.toDouble();
      final low = candle.l.toDouble();

      if (high > highestPrice) highestPrice = high;
      if (low < lowestPrice) lowestPrice = low;
    }

    _highestPrice = highestPrice;
    _lowestPrice = lowestPrice;

    _onPriceRangeUpdated?.call(_highestPrice, _lowestPrice);
  }

  /// 计算前一个交易日的时间范围
  Map<String, int> _calculatePreviousTradingDayRange(int currentDataEndTime) {
    final currentEndDate = DateTime.fromMillisecondsSinceEpoch(
      currentDataEndTime,
    );

    final currentDay8AM = DateTime(
      currentEndDate.year,
      currentEndDate.month,
      currentEndDate.day,
      8,
      0,
      0,
    );

    final previousDay8AM = currentDay8AM.subtract(const Duration(days: 1));

    final startTime =
        currentEndDate.isBefore(currentDay8AM)
            ? previousDay8AM.subtract(const Duration(days: 1))
            : previousDay8AM;

    final endTime = currentEndDate;

    return {
      'startTime': startTime.millisecondsSinceEpoch,
      'endTime': endTime.millisecondsSinceEpoch,
    };
  }

  /// 加载历史数据
  Future<List<CandleModel>> _loadHistoricalData(
    int startTime,
    int endTime,
    String instrumentId,
    String period,
  ) async {
    final requestClient = RequestClient();
    _setProxyFromConfig(requestClient);

    final List<CandleModel> allCandles = [];
    const int maxBatchSize = 1500;
    final int periodMs = _getPeriodMilliseconds(period);
    int currentStartTime = startTime;

    while (currentStartTime < endTime) {
      final batchEndTime = math.min(
        currentStartTime + (maxBatchSize * periodMs),
        endTime,
      );

      final batchCandles = await _fetchKlineBatch(
        requestClient,
        currentStartTime,
        batchEndTime,
        maxBatchSize,
        instrumentId,
        period,
      );

      if (batchCandles.isEmpty) {
        if (kDebugMode) {
          print('[KlineDataLoader] No more historical data available');
        }
        break;
      }

      allCandles.addAll(batchCandles);

      if (batchCandles.isNotEmpty) {
        currentStartTime = batchCandles.last.ts + periodMs;
      } else {
        break;
      }

      if (batchCandles.isNotEmpty && batchCandles.last.ts >= endTime) {
        break;
      }
    }

    allCandles.sort((a, b) => b.ts.compareTo(a.ts));
    return allCandles;
  }

  /// 计算交易日时间范围
  Map<String, int> _calculateTradingDayRange() {
    final now = DateTime.now();
    final today8AM = DateTime(now.year, now.month, now.day, 8, 0, 0);
    final yesterday8AM = today8AM.subtract(const Duration(days: 1));

    final timeDifference = now.difference(today8AM).inHours.abs();
    final startTime =
        (timeDifference < 3 && now.isBefore(today8AM))
            ? yesterday8AM.millisecondsSinceEpoch
            : today8AM.millisecondsSinceEpoch;

    return {'startTime': startTime, 'endTime': now.millisecondsSinceEpoch};
  }

  /// 获取时间周期的毫秒数
  int _getPeriodMilliseconds(String period) {
    final regExp = RegExp(r'(\d+)([mhdw])');
    final match = regExp.firstMatch(period);

    if (match == null) return 60000;

    final int value = int.parse(match.group(1)!);
    final String unit = match.group(2)!;

    switch (unit) {
      case 'm':
        return value * 60 * 1000;
      case 'h':
        return value * 60 * 60 * 1000;
      case 'd':
        return value * 24 * 60 * 60 * 1000;
      case 'w':
        return value * 7 * 24 * 60 * 60 * 1000;
      default:
        return 60000;
    }
  }

  /// 分批获取K线数据
  Future<List<CandleModel>> _fetchKlineBatch(
    RequestClient requestClient,
    int startTime,
    int endTime,
    int limit,
    String instrumentId,
    String period,
  ) async {
    final String apiSymbol = instrumentId.replaceAll('/', '');
    final String apiInterval = period;

    const String apiUrl = 'https://fapi.binance.com/fapi/v1/continuousKlines';
    final Map<String, dynamic> queryParameters = {
      'pair': apiSymbol,
      'contractType': 'PERPETUAL',
      'interval': apiInterval,
      // 'startTime': startTime,
      // 'endTime': endTime,
      'limit': limit,
    };

    try {
      final response = await requestClient.get(
        apiUrl,
        queryParameters: queryParameters,
      );

      if (response.statusCode == 200) {
        final List<dynamic> rawKlineData = response.data as List<dynamic>;
        return _convertRawDataToCandles(rawKlineData);
      }
    } catch (e) {
      if (kDebugMode) {
        print('[KlineDataLoader] Error fetching batch: $e');
      }
    }

    return [];
  }

  /// 释放资源
  void dispose() {
    disconnectWebSocket();
  }
}
