import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../kline/flexi_kline.dart';
import '../../kline/indicators/volume/volume_indicator.dart';
import '../config/indicator_config/volume_config.dart';

/// 成交量指标管理器
class VolumeIndicatorManager {
  final FlexiKlineController _controller;
  VolumeConfig _config;
  
  static const FlexiIndicatorKey _volumeKey = FlexiIndicatorKey('volume', label: 'Volume');

  VolumeIndicatorManager({
    required FlexiKlineController controller,
    required VolumeConfig initialConfig,
  }) : _controller = controller, _config = initialConfig;

  /// 获取当前配置
  VolumeConfig get config => _config;

  /// 添加成交量指标到主图
  bool addIndicator() {
    try {
      // 添加到主图
      _controller.addIndicatorInMain(_volumeKey);
      
      // 应用当前配置
      return _applyConfig();
    } catch (e) {
      return false;
    }
  }

  /// 移除成交量指标
  bool removeIndicator() {
    try {
      _controller.delIndicatorInMain(_volumeKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 更新配置
  bool updateConfig(VolumeConfig newConfig) {
    try {
      _config = newConfig;
      
      // 获取当前指标
      final currentIndicator = _controller.getIndicator<VolumeIndicator>(_volumeKey);
      if (currentIndicator == null) {
        // 不在这里自动添加指标，让调用方决定是否添加
        return true;
      }

      // 应用配置更新
      return _applyConfig();
    } catch (e) {
      return false;
    }
  }

  /// 切换指标启用状态
  bool toggleIndicator(bool enabled) {
    if (enabled) {
      return addIndicator();
    } else {
      return removeIndicator();
    }
  }

  /// 更新显示模式
  bool updateDisplayMode(String displayMode) {
    final newConfig = _config.copyWith(displayMode: displayMode);
    return updateConfig(newConfig);
  }

  /// 更新颜色配置
  bool updateColors({
    Color? bullishColor,
    Color? bearishColor,
    Color? maColor,
  }) {
    final newConfig = _config.copyWith(
      bullishColor: bullishColor,
      bearishColor: bearishColor,
      maColor: maColor,
    );
    return updateConfig(newConfig);
  }

  /// 更新MA设置
  bool updateMASettings({
    bool? showMA,
    int? maLength,
    double? maLineWidth,
  }) {
    final newConfig = _config.copyWith(
      showMA: showMA,
      maLength: maLength,
      maLineWidth: maLineWidth,
    );
    return updateConfig(newConfig);
  }

  /// 更新透明度
  bool updateOpacity(double opacity) {
    final newConfig = _config.copyWith(opacity: opacity);
    return updateConfig(newConfig);
  }

  /// 获取指标是否已添加
  bool get isIndicatorAdded {
    return _controller.getIndicator<VolumeIndicator>(_volumeKey) != null;
  }

  /// 应用当前配置到指标
  bool _applyConfig() {
    try {
      // 获取当前指标配置
      final currentIndicator = _controller.getIndicator<VolumeIndicator>(_volumeKey);
      if (currentIndicator == null) {
        return false;
      }

      // 创建新的配置
      final updatedIndicator = currentIndicator.copyWith(
        displayMode: _config.displayMode,
        bullishColor: _config.bullishColor,
        bearishColor: _config.bearishColor,
        showMA: _config.showMA,
        maLength: _config.maLength,
        maColor: _config.maColor,
        maLineWidth: _config.maLineWidth,
        opacity: _config.opacity,
      );

      // 更新指标配置
      final success = _controller.updateIndicator(updatedIndicator);
      
      if (success) {
        _controller.markRepaintChart(reset: true);
      }

      return success;
    } catch (e) {
      return false;
    }
  }



  /// 创建预设配置
  static VolumeConfig createPreset({
    required String name,
  }) {
    switch (name.toLowerCase()) {
      case 'simple':
        return const VolumeConfig(
          displayMode: 'bars',
          showMA: false,
          opacity: 0.9,
        );
      case 'detailed':
        return const VolumeConfig(
          displayMode: 'area',
          showMA: true,
          maLength: 20,
          opacity: 0.7,
        );
      case 'minimal':
        return const VolumeConfig(
          displayMode: 'bars',
          showMA: false,
          opacity: 0.5,
          maLength: 10,
        );
      default:
        return const VolumeConfig();
    }
  }
} 