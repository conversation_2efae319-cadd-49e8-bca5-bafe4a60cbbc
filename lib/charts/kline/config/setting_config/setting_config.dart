// Copyright 2024 Andy<PERSON>Zhao
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import 'dart:math' as math;

import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:flutter/painting.dart';

import '../../constant.dart';
import '../../framework/serializers.dart';
import '../loading_config/loading_config.dart';

part 'setting_config.g.dart';

@CopyWith()
@FlexiConfigSerializable
class SettingConfig {
  const SettingConfig({
    required this.pixel,

    /// Long/Short 浅色不透明度 [longTintColor] 和 [shortTintColor]
    this.opacity = 0.5,

    /// 内置LoadingView样式配置
    required this.loading,

    ///  如果不指定默认为设置为20*20的逻辑像素区域.
    this.mainMinSize = const Size(120, 80),
    this.subMinHeight = 30,
    this.useCandleTicksAsZoomSlideBar = true,

    /// 蜡烛图绘制配置
    this.minPaintBlankRate = 0.8,
    this.alwaysCalculateScreenOfCandlesIfEnough = false,
    required this.candleMaxWidth,
    required this.candleWidth,
    required this.normalCandleWidth,
    this.candleFixedSpacing = null,
    this.candleSpacingParts = 1.0,
    this.normalCandleSpacingParts = 1.0,
    required this.candleHollowBarBorderWidth,
    required this.candleLineWidth,
    required this.firstCandleInitOffset,

    /// 是否展示Y轴刻度.
    this.showYAxisTick = true,

    /// 副图配置
    // 副区的指标图最大数量
    this.subChartMaxCount = defaultSubChartMaxCount,

    // CONFIGURATION FOR PRICE LEVEL TRACKS
    required this.priceLevelGraphicOffset,
    required this.candleBodyProportion,
    required this.trackWidthMultiplierToBody,
    required this.sellTrackColor,
    required this.buyTrackColor,
    // 是否启用成交量轨迹绘制
    this.enableVolumeTracksRendering = true,
  });

  /// 单个像素值
  final double pixel;

  /// Long/Short 浅色不透明度 [longTintColor] 和 [shortTintColor]
  final double opacity;

  // TODO: 待适配主题管理
  /// 内置LoadingView样式配置
  final LoadingConfig loading;

  // 主区指标图的最小大小限制
  final Size mainMinSize;
  // 副区指标图的最小高度
  final double subMinHeight;

  /// 使用蜡烛图的刻度作为进行缩放拖拽滑动条
  final bool useCandleTicksAsZoomSlideBar;

  /// 绘制区域最少留白比例
  /// 例如: 当蜡烛数量不足以绘制一屏, 向右移动到末尾时, 绘制区域左边最少留白区域占可绘制区域(canvasWidth)的比例
  final double minPaintBlankRate;

  /// 如果足够总是计算一屏的蜡烛.
  /// 当滑动或初始化时会存在(minPaintBlankRate)的空白, 此时, 计算按一屏的蜡烛数量向后计算.
  final bool alwaysCalculateScreenOfCandlesIfEnough;

  /// 蜡烛配置
  // 最大蜡烛宽度[1, 50]
  final double candleMaxWidth;
    // 单根蜡烛柱的宽度（用于成交量轨迹模式）
  final double candleWidth;
    // 单根蜡烛柱的宽度（用于普通模式）
    final double normalCandleWidth;
  // 蜡烛间的固定间距
  final double? candleFixedSpacing;
  // 蜡烛间的间距按蜡烛宽度平分[candleSpacingParts]份（成交量轨迹模式）
  final double candleSpacingParts;
  // 蜡烛间的间距按蜡烛宽度平分[normalCandleSpacingParts]份（普通模式）
  final double normalCandleSpacingParts;
  // 蜡烛空心柱的边框宽度
  final double candleHollowBarBorderWidth;
  // 蜡烛高低线宽(high, low)
  final double candleLineWidth;
  // Candle 第一根Candle相对于mainRect右边的偏移
  final double firstCandleInitOffset;

  /// 是否展示Y轴刻度.
  final bool showYAxisTick;

  // 副区的指标图最大数量
  final int subChartMaxCount;

  // 新增：价格成交区间图形距离K线左侧的偏移量
  final double priceLevelGraphicOffset;
  // 新增：K线实体宽度占总槽位宽度的比例 (例如 0.30 表示 30%)
  final double candleBodyProportion;
  // 新增：成交量轨迹宽度相对于K线实体宽度的倍数 (例如 2.45 表示轨迹宽度是实体宽度的2.45倍)
  final double trackWidthMultiplierToBody;
  // 新增：卖单成交量轨迹颜色
  final Color sellTrackColor;
  // 新增：买单成交量轨迹颜色
  final Color buyTrackColor;
  // 新增：是否启用成交量轨迹绘制
  final bool enableVolumeTracksRendering;

  bool get isFixedCandleSpacing {
    return candleFixedSpacing != null && candleFixedSpacing! > pixel;
  }

  double get spacingCandleParts {
    final double effectiveParts;
    if (enableVolumeTracksRendering) {
      effectiveParts = candleSpacingParts;
    } else {
      effectiveParts = normalCandleSpacingParts;
    }
    return effectiveParts.clamp(0.1, math.max(0.1, candleWidth));
  }

  factory SettingConfig.fromJson(Map<String, dynamic> json) =>
      _$SettingConfigFromJson(json);

  Map<String, dynamic> toJson() => _$SettingConfigToJson(this);
}
