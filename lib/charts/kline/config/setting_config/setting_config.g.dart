// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'setting_config.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$SettingConfigCWProxy {
  SettingConfig pixel(double pixel);

  SettingConfig opacity(double opacity);

  SettingConfig loading(LoadingConfig loading);

  SettingConfig mainMinSize(Size mainMinSize);

  SettingConfig subMinHeight(double subMinHeight);

  SettingConfig useCandleTicksAsZoomSlideBar(bool useCandleTicksAsZoomSlideBar);

  SettingConfig minPaintBlankRate(double minPaintBlankRate);

  SettingConfig alwaysCalculateScreenOfCandlesIfEnough(
    bool alwaysCalculateScreenOfCandlesIfEnough,
  );

  SettingConfig candleMaxWidth(double candleMaxWidth);

  SettingConfig candleWidth(double candleWidth);

  SettingConfig normalCandleWidth(double normalCandleWidth);

  SettingConfig candleFixedSpacing(double? candleFixedSpacing);

  SettingConfig candleSpacingParts(double candleSpacingParts);

  SettingConfig normalCandleSpacingParts(double normalCandleSpacingParts);

  SettingConfig candleHollowBarBorderWidth(double candleHollowBarBorderWidth);

  SettingConfig candleLineWidth(double candleLineWidth);

  SettingConfig firstCandleInitOffset(double firstCandleInitOffset);

  SettingConfig showYAxisTick(bool showYAxisTick);

  SettingConfig subChartMaxCount(int subChartMaxCount);

  SettingConfig priceLevelGraphicOffset(double priceLevelGraphicOffset);

  SettingConfig candleBodyProportion(double candleBodyProportion);

  SettingConfig trackWidthMultiplierToBody(double trackWidthMultiplierToBody);

  SettingConfig sellTrackColor(Color sellTrackColor);

  SettingConfig buyTrackColor(Color buyTrackColor);

  SettingConfig enableVolumeTracksRendering(bool enableVolumeTracksRendering);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `SettingConfig(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// SettingConfig(...).copyWith(id: 12, name: "My name")
  /// ````
  SettingConfig call({
    double pixel,
    double opacity,
    LoadingConfig loading,
    Size mainMinSize,
    double subMinHeight,
    bool useCandleTicksAsZoomSlideBar,
    double minPaintBlankRate,
    bool alwaysCalculateScreenOfCandlesIfEnough,
    double candleMaxWidth,
    double candleWidth,
    double normalCandleWidth,
    double? candleFixedSpacing,
    double candleSpacingParts,
    double normalCandleSpacingParts,
    double candleHollowBarBorderWidth,
    double candleLineWidth,
    double firstCandleInitOffset,
    bool showYAxisTick,
    int subChartMaxCount,
    double priceLevelGraphicOffset,
    double candleBodyProportion,
    double trackWidthMultiplierToBody,
    Color sellTrackColor,
    Color buyTrackColor,
    bool enableVolumeTracksRendering,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfSettingConfig.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfSettingConfig.copyWith.fieldName(...)`
class _$SettingConfigCWProxyImpl implements _$SettingConfigCWProxy {
  const _$SettingConfigCWProxyImpl(this._value);

  final SettingConfig _value;

  @override
  SettingConfig pixel(double pixel) => this(pixel: pixel);

  @override
  SettingConfig opacity(double opacity) => this(opacity: opacity);

  @override
  SettingConfig loading(LoadingConfig loading) => this(loading: loading);

  @override
  SettingConfig mainMinSize(Size mainMinSize) => this(mainMinSize: mainMinSize);

  @override
  SettingConfig subMinHeight(double subMinHeight) =>
      this(subMinHeight: subMinHeight);

  @override
  SettingConfig useCandleTicksAsZoomSlideBar(
    bool useCandleTicksAsZoomSlideBar,
  ) => this(useCandleTicksAsZoomSlideBar: useCandleTicksAsZoomSlideBar);

  @override
  SettingConfig minPaintBlankRate(double minPaintBlankRate) =>
      this(minPaintBlankRate: minPaintBlankRate);

  @override
  SettingConfig alwaysCalculateScreenOfCandlesIfEnough(
    bool alwaysCalculateScreenOfCandlesIfEnough,
  ) => this(
    alwaysCalculateScreenOfCandlesIfEnough:
        alwaysCalculateScreenOfCandlesIfEnough,
  );

  @override
  SettingConfig candleMaxWidth(double candleMaxWidth) =>
      this(candleMaxWidth: candleMaxWidth);

  @override
  SettingConfig candleWidth(double candleWidth) =>
      this(candleWidth: candleWidth);

  @override
  SettingConfig normalCandleWidth(double normalCandleWidth) =>
      this(normalCandleWidth: normalCandleWidth);

  @override
  SettingConfig candleFixedSpacing(double? candleFixedSpacing) =>
      this(candleFixedSpacing: candleFixedSpacing);

  @override
  SettingConfig candleSpacingParts(double candleSpacingParts) =>
      this(candleSpacingParts: candleSpacingParts);

  @override
  SettingConfig normalCandleSpacingParts(double normalCandleSpacingParts) =>
      this(normalCandleSpacingParts: normalCandleSpacingParts);

  @override
  SettingConfig candleHollowBarBorderWidth(double candleHollowBarBorderWidth) =>
      this(candleHollowBarBorderWidth: candleHollowBarBorderWidth);

  @override
  SettingConfig candleLineWidth(double candleLineWidth) =>
      this(candleLineWidth: candleLineWidth);

  @override
  SettingConfig firstCandleInitOffset(double firstCandleInitOffset) =>
      this(firstCandleInitOffset: firstCandleInitOffset);

  @override
  SettingConfig showYAxisTick(bool showYAxisTick) =>
      this(showYAxisTick: showYAxisTick);

  @override
  SettingConfig subChartMaxCount(int subChartMaxCount) =>
      this(subChartMaxCount: subChartMaxCount);

  @override
  SettingConfig priceLevelGraphicOffset(double priceLevelGraphicOffset) =>
      this(priceLevelGraphicOffset: priceLevelGraphicOffset);

  @override
  SettingConfig candleBodyProportion(double candleBodyProportion) =>
      this(candleBodyProportion: candleBodyProportion);

  @override
  SettingConfig trackWidthMultiplierToBody(double trackWidthMultiplierToBody) =>
      this(trackWidthMultiplierToBody: trackWidthMultiplierToBody);

  @override
  SettingConfig sellTrackColor(Color sellTrackColor) =>
      this(sellTrackColor: sellTrackColor);

  @override
  SettingConfig buyTrackColor(Color buyTrackColor) =>
      this(buyTrackColor: buyTrackColor);

  @override
  SettingConfig enableVolumeTracksRendering(bool enableVolumeTracksRendering) =>
      this(enableVolumeTracksRendering: enableVolumeTracksRendering);

  @override
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `SettingConfig(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// SettingConfig(...).copyWith(id: 12, name: "My name")
  /// ````
  SettingConfig call({
    Object? pixel = const $CopyWithPlaceholder(),
    Object? opacity = const $CopyWithPlaceholder(),
    Object? loading = const $CopyWithPlaceholder(),
    Object? mainMinSize = const $CopyWithPlaceholder(),
    Object? subMinHeight = const $CopyWithPlaceholder(),
    Object? useCandleTicksAsZoomSlideBar = const $CopyWithPlaceholder(),
    Object? minPaintBlankRate = const $CopyWithPlaceholder(),
    Object? alwaysCalculateScreenOfCandlesIfEnough =
        const $CopyWithPlaceholder(),
    Object? candleMaxWidth = const $CopyWithPlaceholder(),
    Object? candleWidth = const $CopyWithPlaceholder(),
    Object? normalCandleWidth = const $CopyWithPlaceholder(),
    Object? candleFixedSpacing = const $CopyWithPlaceholder(),
    Object? candleSpacingParts = const $CopyWithPlaceholder(),
    Object? normalCandleSpacingParts = const $CopyWithPlaceholder(),
    Object? candleHollowBarBorderWidth = const $CopyWithPlaceholder(),
    Object? candleLineWidth = const $CopyWithPlaceholder(),
    Object? firstCandleInitOffset = const $CopyWithPlaceholder(),
    Object? showYAxisTick = const $CopyWithPlaceholder(),
    Object? subChartMaxCount = const $CopyWithPlaceholder(),
    Object? priceLevelGraphicOffset = const $CopyWithPlaceholder(),
    Object? candleBodyProportion = const $CopyWithPlaceholder(),
    Object? trackWidthMultiplierToBody = const $CopyWithPlaceholder(),
    Object? sellTrackColor = const $CopyWithPlaceholder(),
    Object? buyTrackColor = const $CopyWithPlaceholder(),
    Object? enableVolumeTracksRendering = const $CopyWithPlaceholder(),
  }) {
    return SettingConfig(
      pixel:
          pixel == const $CopyWithPlaceholder()
              ? _value.pixel
              // ignore: cast_nullable_to_non_nullable
              : pixel as double,
      opacity:
          opacity == const $CopyWithPlaceholder()
              ? _value.opacity
              // ignore: cast_nullable_to_non_nullable
              : opacity as double,
      loading:
          loading == const $CopyWithPlaceholder()
              ? _value.loading
              // ignore: cast_nullable_to_non_nullable
              : loading as LoadingConfig,
      mainMinSize:
          mainMinSize == const $CopyWithPlaceholder()
              ? _value.mainMinSize
              // ignore: cast_nullable_to_non_nullable
              : mainMinSize as Size,
      subMinHeight:
          subMinHeight == const $CopyWithPlaceholder()
              ? _value.subMinHeight
              // ignore: cast_nullable_to_non_nullable
              : subMinHeight as double,
      useCandleTicksAsZoomSlideBar:
          useCandleTicksAsZoomSlideBar == const $CopyWithPlaceholder()
              ? _value.useCandleTicksAsZoomSlideBar
              // ignore: cast_nullable_to_non_nullable
              : useCandleTicksAsZoomSlideBar as bool,
      minPaintBlankRate:
          minPaintBlankRate == const $CopyWithPlaceholder()
              ? _value.minPaintBlankRate
              // ignore: cast_nullable_to_non_nullable
              : minPaintBlankRate as double,
      alwaysCalculateScreenOfCandlesIfEnough:
          alwaysCalculateScreenOfCandlesIfEnough == const $CopyWithPlaceholder()
              ? _value.alwaysCalculateScreenOfCandlesIfEnough
              // ignore: cast_nullable_to_non_nullable
              : alwaysCalculateScreenOfCandlesIfEnough as bool,
      candleMaxWidth:
          candleMaxWidth == const $CopyWithPlaceholder()
              ? _value.candleMaxWidth
              // ignore: cast_nullable_to_non_nullable
              : candleMaxWidth as double,
      candleWidth:
          candleWidth == const $CopyWithPlaceholder()
              ? _value.candleWidth
              // ignore: cast_nullable_to_non_nullable
              : candleWidth as double,
      normalCandleWidth:
          normalCandleWidth == const $CopyWithPlaceholder()
              ? _value.normalCandleWidth
              // ignore: cast_nullable_to_non_nullable
              : normalCandleWidth as double,
      candleFixedSpacing:
          candleFixedSpacing == const $CopyWithPlaceholder()
              ? _value.candleFixedSpacing
              // ignore: cast_nullable_to_non_nullable
              : candleFixedSpacing as double?,
      candleSpacingParts:
          candleSpacingParts == const $CopyWithPlaceholder()
              ? _value.candleSpacingParts
              // ignore: cast_nullable_to_non_nullable
              : candleSpacingParts as double,
      normalCandleSpacingParts:
          normalCandleSpacingParts == const $CopyWithPlaceholder()
              ? _value.normalCandleSpacingParts
              // ignore: cast_nullable_to_non_nullable
              : normalCandleSpacingParts as double,
      candleHollowBarBorderWidth:
          candleHollowBarBorderWidth == const $CopyWithPlaceholder()
              ? _value.candleHollowBarBorderWidth
              // ignore: cast_nullable_to_non_nullable
              : candleHollowBarBorderWidth as double,
      candleLineWidth:
          candleLineWidth == const $CopyWithPlaceholder()
              ? _value.candleLineWidth
              // ignore: cast_nullable_to_non_nullable
              : candleLineWidth as double,
      firstCandleInitOffset:
          firstCandleInitOffset == const $CopyWithPlaceholder()
              ? _value.firstCandleInitOffset
              // ignore: cast_nullable_to_non_nullable
              : firstCandleInitOffset as double,
      showYAxisTick:
          showYAxisTick == const $CopyWithPlaceholder()
              ? _value.showYAxisTick
              // ignore: cast_nullable_to_non_nullable
              : showYAxisTick as bool,
      subChartMaxCount:
          subChartMaxCount == const $CopyWithPlaceholder()
              ? _value.subChartMaxCount
              // ignore: cast_nullable_to_non_nullable
              : subChartMaxCount as int,
      priceLevelGraphicOffset:
          priceLevelGraphicOffset == const $CopyWithPlaceholder()
              ? _value.priceLevelGraphicOffset
              // ignore: cast_nullable_to_non_nullable
              : priceLevelGraphicOffset as double,
      candleBodyProportion:
          candleBodyProportion == const $CopyWithPlaceholder()
              ? _value.candleBodyProportion
              // ignore: cast_nullable_to_non_nullable
              : candleBodyProportion as double,
      trackWidthMultiplierToBody:
          trackWidthMultiplierToBody == const $CopyWithPlaceholder()
              ? _value.trackWidthMultiplierToBody
              // ignore: cast_nullable_to_non_nullable
              : trackWidthMultiplierToBody as double,
      sellTrackColor:
          sellTrackColor == const $CopyWithPlaceholder()
              ? _value.sellTrackColor
              // ignore: cast_nullable_to_non_nullable
              : sellTrackColor as Color,
      buyTrackColor:
          buyTrackColor == const $CopyWithPlaceholder()
              ? _value.buyTrackColor
              // ignore: cast_nullable_to_non_nullable
              : buyTrackColor as Color,
      enableVolumeTracksRendering:
          enableVolumeTracksRendering == const $CopyWithPlaceholder()
              ? _value.enableVolumeTracksRendering
              // ignore: cast_nullable_to_non_nullable
              : enableVolumeTracksRendering as bool,
    );
  }
}

extension $SettingConfigCopyWith on SettingConfig {
  /// Returns a callable class that can be used as follows: `instanceOfSettingConfig.copyWith(...)` or like so:`instanceOfSettingConfig.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$SettingConfigCWProxy get copyWith => _$SettingConfigCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SettingConfig _$SettingConfigFromJson(
  Map<String, dynamic> json,
) => SettingConfig(
  pixel: (json['pixel'] as num).toDouble(),
  opacity: (json['opacity'] as num?)?.toDouble() ?? 0.5,
  loading: LoadingConfig.fromJson(json['loading'] as Map<String, dynamic>),
  mainMinSize:
      json['mainMinSize'] == null
          ? const Size(120, 80)
          : const SizeConverter().fromJson(
            json['mainMinSize'] as Map<String, dynamic>,
          ),
  subMinHeight: (json['subMinHeight'] as num?)?.toDouble() ?? 30,
  useCandleTicksAsZoomSlideBar:
      json['useCandleTicksAsZoomSlideBar'] as bool? ?? true,
  minPaintBlankRate: (json['minPaintBlankRate'] as num?)?.toDouble() ?? 0.8,
  alwaysCalculateScreenOfCandlesIfEnough:
      json['alwaysCalculateScreenOfCandlesIfEnough'] as bool? ?? false,
  candleMaxWidth: (json['candleMaxWidth'] as num).toDouble(),
  candleWidth: (json['candleWidth'] as num).toDouble(),
  normalCandleWidth: (json['normalCandleWidth'] as num).toDouble(),
  candleFixedSpacing: (json['candleFixedSpacing'] as num?)?.toDouble() ?? null,
  candleSpacingParts: (json['candleSpacingParts'] as num?)?.toDouble() ?? 1.0,
  normalCandleSpacingParts:
      (json['normalCandleSpacingParts'] as num?)?.toDouble() ?? 1.0,
  candleHollowBarBorderWidth:
      (json['candleHollowBarBorderWidth'] as num).toDouble(),
  candleLineWidth: (json['candleLineWidth'] as num).toDouble(),
  firstCandleInitOffset: (json['firstCandleInitOffset'] as num).toDouble(),
  showYAxisTick: json['showYAxisTick'] as bool? ?? true,
  subChartMaxCount:
      (json['subChartMaxCount'] as num?)?.toInt() ?? defaultSubChartMaxCount,
  priceLevelGraphicOffset: (json['priceLevelGraphicOffset'] as num).toDouble(),
  candleBodyProportion: (json['candleBodyProportion'] as num).toDouble(),
  trackWidthMultiplierToBody:
      (json['trackWidthMultiplierToBody'] as num).toDouble(),
  sellTrackColor: const ColorConverter().fromJson(
    json['sellTrackColor'] as String,
  ),
  buyTrackColor: const ColorConverter().fromJson(
    json['buyTrackColor'] as String,
  ),
  enableVolumeTracksRendering:
      json['enableVolumeTracksRendering'] as bool? ?? true,
);

Map<String, dynamic> _$SettingConfigToJson(SettingConfig instance) =>
    <String, dynamic>{
      'pixel': instance.pixel,
      'opacity': instance.opacity,
      'loading': instance.loading.toJson(),
      'mainMinSize': const SizeConverter().toJson(instance.mainMinSize),
      'subMinHeight': instance.subMinHeight,
      'useCandleTicksAsZoomSlideBar': instance.useCandleTicksAsZoomSlideBar,
      'minPaintBlankRate': instance.minPaintBlankRate,
      'alwaysCalculateScreenOfCandlesIfEnough':
          instance.alwaysCalculateScreenOfCandlesIfEnough,
      'candleMaxWidth': instance.candleMaxWidth,
      'candleWidth': instance.candleWidth,
      'normalCandleWidth': instance.normalCandleWidth,
      if (instance.candleFixedSpacing case final value?)
        'candleFixedSpacing': value,
      'candleSpacingParts': instance.candleSpacingParts,
      'normalCandleSpacingParts': instance.normalCandleSpacingParts,
      'candleHollowBarBorderWidth': instance.candleHollowBarBorderWidth,
      'candleLineWidth': instance.candleLineWidth,
      'firstCandleInitOffset': instance.firstCandleInitOffset,
      'showYAxisTick': instance.showYAxisTick,
      'subChartMaxCount': instance.subChartMaxCount,
      'priceLevelGraphicOffset': instance.priceLevelGraphicOffset,
      'candleBodyProportion': instance.candleBodyProportion,
      'trackWidthMultiplierToBody': instance.trackWidthMultiplierToBody,
      'sellTrackColor': const ColorConverter().toJson(instance.sellTrackColor),
      'buyTrackColor': const ColorConverter().toJson(instance.buyTrackColor),
      'enableVolumeTracksRendering': instance.enableVolumeTracksRendering,
    };
