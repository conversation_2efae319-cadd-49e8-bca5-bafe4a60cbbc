// Copyright 2024 <PERSON><PERSON>Zhao
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import 'core/core.dart';

class FlexiKlineController extends KlineBindingBase
    with
        SettingBinding,
        StateBinding,
        GridBinding,
        ChartBinding,
        CrossBinding,
        DrawBinding {
  FlexiKlineController({
    required super.configuration,
    super.autoSave,
    super.logger,
    super.klineDataCacheCapacity,
  });

  // Callback for when the latest price Y position is updated in the chart
  Function(double yPosition, double price)? onLatestPriceYPositionUpdated;
}
