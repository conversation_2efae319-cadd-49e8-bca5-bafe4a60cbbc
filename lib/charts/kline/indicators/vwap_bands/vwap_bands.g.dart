// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vwap_bands.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$VWAPBandsIndicatorCWProxy {
  VWAPBandsIndicator zIndex(int zIndex);

  VWAPBandsIndicator height(double height);

  VWAPBandsIndicator padding(EdgeInsets padding);

  VWAPBandsIndicator devUp1(double devUp1);

  VWAPBandsIndicator devDn1(double devDn1);

  VWAPBandsIndicator devUp2(double devUp2);

  VWAPBandsIndicator devDn2(double devDn2);

  VWAPBandsIndicator devUp3(double devUp3);

  VWAPBandsIndicator devDn3(double devDn3);

  VWAPBandsIndicator devUp4(double devUp4);

  VWAPBandsIndicator devDn4(double devDn4);

  VWAPBandsIndicator devUp5(double devUp5);

  VWAPBandsIndicator devDn5(double devDn5);

  VWAPBandsIndicator showDv2(bool showDv2);

  VWAPBandsIndicator showDv3(bool showDv3);

  VWAPBandsIndicator showDv4(bool showDv4);

  VWAPBandsIndicator showDv5(bool showDv5);

  VWAPBandsIndicator showPrevVWAP(bool showPrevVWAP);

  VWAPBandsIndicator vwapColor(Color vwapColor);

  VWAPBandsIndicator band1Color(Color band1Color);

  VWAPBandsIndicator upperBandColor(Color upperBandColor);

  VWAPBandsIndicator lowerBandColor(Color lowerBandColor);

  VWAPBandsIndicator fillOpacity(double fillOpacity);

  VWAPBandsIndicator vwapLineWidth(double vwapLineWidth);

  VWAPBandsIndicator bandLineWidth(double bandLineWidth);

  VWAPBandsIndicator sessionStartHour(int sessionStartHour);

  VWAPBandsIndicator sessionStartMinute(int sessionStartMinute);

  VWAPBandsIndicator timezoneOffset(int timezoneOffset);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `VWAPBandsIndicator(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// VWAPBandsIndicator(...).copyWith(id: 12, name: "My name")
  /// ````
  VWAPBandsIndicator call({
    int zIndex,
    double height,
    EdgeInsets padding,
    double devUp1,
    double devDn1,
    double devUp2,
    double devDn2,
    double devUp3,
    double devDn3,
    double devUp4,
    double devDn4,
    double devUp5,
    double devDn5,
    bool showDv2,
    bool showDv3,
    bool showDv4,
    bool showDv5,
    bool showPrevVWAP,
    Color vwapColor,
    Color band1Color,
    Color upperBandColor,
    Color lowerBandColor,
    double fillOpacity,
    double vwapLineWidth,
    double bandLineWidth,
    int sessionStartHour,
    int sessionStartMinute,
    int timezoneOffset,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfVWAPBandsIndicator.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfVWAPBandsIndicator.copyWith.fieldName(...)`
class _$VWAPBandsIndicatorCWProxyImpl implements _$VWAPBandsIndicatorCWProxy {
  const _$VWAPBandsIndicatorCWProxyImpl(this._value);

  final VWAPBandsIndicator _value;

  @override
  VWAPBandsIndicator zIndex(int zIndex) => this(zIndex: zIndex);

  @override
  VWAPBandsIndicator height(double height) => this(height: height);

  @override
  VWAPBandsIndicator padding(EdgeInsets padding) => this(padding: padding);

  @override
  VWAPBandsIndicator devUp1(double devUp1) => this(devUp1: devUp1);

  @override
  VWAPBandsIndicator devDn1(double devDn1) => this(devDn1: devDn1);

  @override
  VWAPBandsIndicator devUp2(double devUp2) => this(devUp2: devUp2);

  @override
  VWAPBandsIndicator devDn2(double devDn2) => this(devDn2: devDn2);

  @override
  VWAPBandsIndicator devUp3(double devUp3) => this(devUp3: devUp3);

  @override
  VWAPBandsIndicator devDn3(double devDn3) => this(devDn3: devDn3);

  @override
  VWAPBandsIndicator devUp4(double devUp4) => this(devUp4: devUp4);

  @override
  VWAPBandsIndicator devDn4(double devDn4) => this(devDn4: devDn4);

  @override
  VWAPBandsIndicator devUp5(double devUp5) => this(devUp5: devUp5);

  @override
  VWAPBandsIndicator devDn5(double devDn5) => this(devDn5: devDn5);

  @override
  VWAPBandsIndicator showDv2(bool showDv2) => this(showDv2: showDv2);

  @override
  VWAPBandsIndicator showDv3(bool showDv3) => this(showDv3: showDv3);

  @override
  VWAPBandsIndicator showDv4(bool showDv4) => this(showDv4: showDv4);

  @override
  VWAPBandsIndicator showDv5(bool showDv5) => this(showDv5: showDv5);

  @override
  VWAPBandsIndicator showPrevVWAP(bool showPrevVWAP) =>
      this(showPrevVWAP: showPrevVWAP);

  @override
  VWAPBandsIndicator vwapColor(Color vwapColor) => this(vwapColor: vwapColor);

  @override
  VWAPBandsIndicator band1Color(Color band1Color) =>
      this(band1Color: band1Color);

  @override
  VWAPBandsIndicator upperBandColor(Color upperBandColor) =>
      this(upperBandColor: upperBandColor);

  @override
  VWAPBandsIndicator lowerBandColor(Color lowerBandColor) =>
      this(lowerBandColor: lowerBandColor);

  @override
  VWAPBandsIndicator fillOpacity(double fillOpacity) =>
      this(fillOpacity: fillOpacity);

  @override
  VWAPBandsIndicator vwapLineWidth(double vwapLineWidth) =>
      this(vwapLineWidth: vwapLineWidth);

  @override
  VWAPBandsIndicator bandLineWidth(double bandLineWidth) =>
      this(bandLineWidth: bandLineWidth);

  @override
  VWAPBandsIndicator sessionStartHour(int sessionStartHour) =>
      this(sessionStartHour: sessionStartHour);

  @override
  VWAPBandsIndicator sessionStartMinute(int sessionStartMinute) =>
      this(sessionStartMinute: sessionStartMinute);

  @override
  VWAPBandsIndicator timezoneOffset(int timezoneOffset) =>
      this(timezoneOffset: timezoneOffset);

  @override
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `VWAPBandsIndicator(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// VWAPBandsIndicator(...).copyWith(id: 12, name: "My name")
  /// ````
  VWAPBandsIndicator call({
    Object? zIndex = const $CopyWithPlaceholder(),
    Object? height = const $CopyWithPlaceholder(),
    Object? padding = const $CopyWithPlaceholder(),
    Object? devUp1 = const $CopyWithPlaceholder(),
    Object? devDn1 = const $CopyWithPlaceholder(),
    Object? devUp2 = const $CopyWithPlaceholder(),
    Object? devDn2 = const $CopyWithPlaceholder(),
    Object? devUp3 = const $CopyWithPlaceholder(),
    Object? devDn3 = const $CopyWithPlaceholder(),
    Object? devUp4 = const $CopyWithPlaceholder(),
    Object? devDn4 = const $CopyWithPlaceholder(),
    Object? devUp5 = const $CopyWithPlaceholder(),
    Object? devDn5 = const $CopyWithPlaceholder(),
    Object? showDv2 = const $CopyWithPlaceholder(),
    Object? showDv3 = const $CopyWithPlaceholder(),
    Object? showDv4 = const $CopyWithPlaceholder(),
    Object? showDv5 = const $CopyWithPlaceholder(),
    Object? showPrevVWAP = const $CopyWithPlaceholder(),
    Object? vwapColor = const $CopyWithPlaceholder(),
    Object? band1Color = const $CopyWithPlaceholder(),
    Object? upperBandColor = const $CopyWithPlaceholder(),
    Object? lowerBandColor = const $CopyWithPlaceholder(),
    Object? fillOpacity = const $CopyWithPlaceholder(),
    Object? vwapLineWidth = const $CopyWithPlaceholder(),
    Object? bandLineWidth = const $CopyWithPlaceholder(),
    Object? sessionStartHour = const $CopyWithPlaceholder(),
    Object? sessionStartMinute = const $CopyWithPlaceholder(),
    Object? timezoneOffset = const $CopyWithPlaceholder(),
  }) {
    return VWAPBandsIndicator(
      zIndex:
          zIndex == const $CopyWithPlaceholder()
              ? _value.zIndex
              // ignore: cast_nullable_to_non_nullable
              : zIndex as int,
      height:
          height == const $CopyWithPlaceholder()
              ? _value.height
              // ignore: cast_nullable_to_non_nullable
              : height as double,
      padding:
          padding == const $CopyWithPlaceholder()
              ? _value.padding
              // ignore: cast_nullable_to_non_nullable
              : padding as EdgeInsets,
      devUp1:
          devUp1 == const $CopyWithPlaceholder()
              ? _value.devUp1
              // ignore: cast_nullable_to_non_nullable
              : devUp1 as double,
      devDn1:
          devDn1 == const $CopyWithPlaceholder()
              ? _value.devDn1
              // ignore: cast_nullable_to_non_nullable
              : devDn1 as double,
      devUp2:
          devUp2 == const $CopyWithPlaceholder()
              ? _value.devUp2
              // ignore: cast_nullable_to_non_nullable
              : devUp2 as double,
      devDn2:
          devDn2 == const $CopyWithPlaceholder()
              ? _value.devDn2
              // ignore: cast_nullable_to_non_nullable
              : devDn2 as double,
      devUp3:
          devUp3 == const $CopyWithPlaceholder()
              ? _value.devUp3
              // ignore: cast_nullable_to_non_nullable
              : devUp3 as double,
      devDn3:
          devDn3 == const $CopyWithPlaceholder()
              ? _value.devDn3
              // ignore: cast_nullable_to_non_nullable
              : devDn3 as double,
      devUp4:
          devUp4 == const $CopyWithPlaceholder()
              ? _value.devUp4
              // ignore: cast_nullable_to_non_nullable
              : devUp4 as double,
      devDn4:
          devDn4 == const $CopyWithPlaceholder()
              ? _value.devDn4
              // ignore: cast_nullable_to_non_nullable
              : devDn4 as double,
      devUp5:
          devUp5 == const $CopyWithPlaceholder()
              ? _value.devUp5
              // ignore: cast_nullable_to_non_nullable
              : devUp5 as double,
      devDn5:
          devDn5 == const $CopyWithPlaceholder()
              ? _value.devDn5
              // ignore: cast_nullable_to_non_nullable
              : devDn5 as double,
      showDv2:
          showDv2 == const $CopyWithPlaceholder()
              ? _value.showDv2
              // ignore: cast_nullable_to_non_nullable
              : showDv2 as bool,
      showDv3:
          showDv3 == const $CopyWithPlaceholder()
              ? _value.showDv3
              // ignore: cast_nullable_to_non_nullable
              : showDv3 as bool,
      showDv4:
          showDv4 == const $CopyWithPlaceholder()
              ? _value.showDv4
              // ignore: cast_nullable_to_non_nullable
              : showDv4 as bool,
      showDv5:
          showDv5 == const $CopyWithPlaceholder()
              ? _value.showDv5
              // ignore: cast_nullable_to_non_nullable
              : showDv5 as bool,
      showPrevVWAP:
          showPrevVWAP == const $CopyWithPlaceholder()
              ? _value.showPrevVWAP
              // ignore: cast_nullable_to_non_nullable
              : showPrevVWAP as bool,
      vwapColor:
          vwapColor == const $CopyWithPlaceholder()
              ? _value.vwapColor
              // ignore: cast_nullable_to_non_nullable
              : vwapColor as Color,
      band1Color:
          band1Color == const $CopyWithPlaceholder()
              ? _value.band1Color
              // ignore: cast_nullable_to_non_nullable
              : band1Color as Color,
      upperBandColor:
          upperBandColor == const $CopyWithPlaceholder()
              ? _value.upperBandColor
              // ignore: cast_nullable_to_non_nullable
              : upperBandColor as Color,
      lowerBandColor:
          lowerBandColor == const $CopyWithPlaceholder()
              ? _value.lowerBandColor
              // ignore: cast_nullable_to_non_nullable
              : lowerBandColor as Color,
      fillOpacity:
          fillOpacity == const $CopyWithPlaceholder()
              ? _value.fillOpacity
              // ignore: cast_nullable_to_non_nullable
              : fillOpacity as double,
      vwapLineWidth:
          vwapLineWidth == const $CopyWithPlaceholder()
              ? _value.vwapLineWidth
              // ignore: cast_nullable_to_non_nullable
              : vwapLineWidth as double,
      bandLineWidth:
          bandLineWidth == const $CopyWithPlaceholder()
              ? _value.bandLineWidth
              // ignore: cast_nullable_to_non_nullable
              : bandLineWidth as double,
      sessionStartHour:
          sessionStartHour == const $CopyWithPlaceholder()
              ? _value.sessionStartHour
              // ignore: cast_nullable_to_non_nullable
              : sessionStartHour as int,
      sessionStartMinute:
          sessionStartMinute == const $CopyWithPlaceholder()
              ? _value.sessionStartMinute
              // ignore: cast_nullable_to_non_nullable
              : sessionStartMinute as int,
      timezoneOffset:
          timezoneOffset == const $CopyWithPlaceholder()
              ? _value.timezoneOffset
              // ignore: cast_nullable_to_non_nullable
              : timezoneOffset as int,
    );
  }
}

extension $VWAPBandsIndicatorCopyWith on VWAPBandsIndicator {
  /// Returns a callable class that can be used as follows: `instanceOfVWAPBandsIndicator.copyWith(...)` or like so:`instanceOfVWAPBandsIndicator.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$VWAPBandsIndicatorCWProxy get copyWith =>
      _$VWAPBandsIndicatorCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VWAPBandsIndicator _$VWAPBandsIndicatorFromJson(
  Map<String, dynamic> json,
) => VWAPBandsIndicator(
  zIndex: (json['zIndex'] as num?)?.toInt() ?? -1,
  height: (json['height'] as num).toDouble(),
  padding:
      json['padding'] == null
          ? defaultMainIndicatorPadding
          : const EdgeInsetsConverter().fromJson(
            json['padding'] as Map<String, dynamic>,
          ),
  devUp1: (json['devUp1'] as num?)?.toDouble() ?? 1.28,
  devDn1: (json['devDn1'] as num?)?.toDouble() ?? 1.28,
  devUp2: (json['devUp2'] as num?)?.toDouble() ?? 2.01,
  devDn2: (json['devDn2'] as num?)?.toDouble() ?? 2.01,
  devUp3: (json['devUp3'] as num?)?.toDouble() ?? 2.51,
  devDn3: (json['devDn3'] as num?)?.toDouble() ?? 2.51,
  devUp4: (json['devUp4'] as num?)?.toDouble() ?? 3.09,
  devDn4: (json['devDn4'] as num?)?.toDouble() ?? 3.09,
  devUp5: (json['devUp5'] as num?)?.toDouble() ?? 4.01,
  devDn5: (json['devDn5'] as num?)?.toDouble() ?? 4.01,
  showDv2: json['showDv2'] as bool? ?? true,
  showDv3: json['showDv3'] as bool? ?? true,
  showDv4: json['showDv4'] as bool? ?? false,
  showDv5: json['showDv5'] as bool? ?? false,
  showPrevVWAP: json['showPrevVWAP'] as bool? ?? false,
  vwapColor:
      json['vwapColor'] == null
          ? Colors.black
          : const ColorConverter().fromJson(json['vwapColor'] as String),
  band1Color:
      json['band1Color'] == null
          ? Colors.grey
          : const ColorConverter().fromJson(json['band1Color'] as String),
  upperBandColor:
      json['upperBandColor'] == null
          ? Colors.red
          : const ColorConverter().fromJson(json['upperBandColor'] as String),
  lowerBandColor:
      json['lowerBandColor'] == null
          ? Colors.green
          : const ColorConverter().fromJson(json['lowerBandColor'] as String),
  fillOpacity: (json['fillOpacity'] as num?)?.toDouble() ?? 0.1,
  vwapLineWidth: (json['vwapLineWidth'] as num?)?.toDouble() ?? 1.0,
  bandLineWidth: (json['bandLineWidth'] as num?)?.toDouble() ?? 1.0,
  sessionStartHour: (json['sessionStartHour'] as num?)?.toInt() ?? 8,
  sessionStartMinute: (json['sessionStartMinute'] as num?)?.toInt() ?? 0,
  timezoneOffset: (json['timezoneOffset'] as num?)?.toInt() ?? 8,
);

Map<String, dynamic> _$VWAPBandsIndicatorToJson(VWAPBandsIndicator instance) =>
    <String, dynamic>{
      'height': instance.height,
      'padding': const EdgeInsetsConverter().toJson(instance.padding),
      'zIndex': instance.zIndex,
      'devUp1': instance.devUp1,
      'devDn1': instance.devDn1,
      'devUp2': instance.devUp2,
      'devDn2': instance.devDn2,
      'devUp3': instance.devUp3,
      'devDn3': instance.devDn3,
      'devUp4': instance.devUp4,
      'devDn4': instance.devDn4,
      'devUp5': instance.devUp5,
      'devDn5': instance.devDn5,
      'showDv2': instance.showDv2,
      'showDv3': instance.showDv3,
      'showDv4': instance.showDv4,
      'showDv5': instance.showDv5,
      'showPrevVWAP': instance.showPrevVWAP,
      'vwapColor': const ColorConverter().toJson(instance.vwapColor),
      'band1Color': const ColorConverter().toJson(instance.band1Color),
      'upperBandColor': const ColorConverter().toJson(instance.upperBandColor),
      'lowerBandColor': const ColorConverter().toJson(instance.lowerBandColor),
      'fillOpacity': instance.fillOpacity,
      'vwapLineWidth': instance.vwapLineWidth,
      'bandLineWidth': instance.bandLineWidth,
      'sessionStartHour': instance.sessionStartHour,
      'sessionStartMinute': instance.sessionStartMinute,
      'timezoneOffset': instance.timezoneOffset,
    };
