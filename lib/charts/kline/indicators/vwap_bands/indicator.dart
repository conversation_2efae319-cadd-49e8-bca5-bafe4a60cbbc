// Copyright 2024 Andy<PERSON>Zhao
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

part of 'vwap_bands.dart';

/// VWAP标准差带计算数据
class VWAPBandsData {
  VWAPBandsData({
    required this.vwap,
    required this.stdDev,
    required this.upperBands,
    required this.lowerBands,
    this.prevVWAP,
  });

  final double vwap;
  final double stdDev;
  final List<double> upperBands; // 对应5个上带
  final List<double> lowerBands; // 对应5个下带
  final double? prevVWAP;
}

/// VWAP标准差带指标配置
@CopyWith()
@FlexiIndicatorSerializable
class VWAPBandsIndicator extends PaintObjectIndicator {
  VWAPBandsIndicator({
    super.zIndex = -1,
    required super.height,
    super.padding = defaultMainIndicatorPadding,

    // 标准差倍数配置
    this.devUp1 = 1.28,
    this.devDn1 = 1.28,
    this.devUp2 = 2.01, 
    this.devDn2 = 2.01,
    this.devUp3 = 2.51,
    this.devDn3 = 2.51,
    this.devUp4 = 3.09,
    this.devDn4 = 3.09,
    this.devUp5 = 4.01,
    this.devDn5 = 4.01,

    // 显示开关
    this.showDv2 = true,
    this.showDv3 = true,
    this.showDv4 = false,
    this.showDv5 = false,
    this.showPrevVWAP = false,

    // 颜色配置
    this.vwapColor = Colors.black,
    this.band1Color = Colors.grey,
    this.upperBandColor = Colors.red,
    this.lowerBandColor = Colors.green,
    this.fillOpacity = 0.1,

    // 线条配置
    this.vwapLineWidth = 1.0,
    this.bandLineWidth = 1.0,

    // 交易会话配置
    this.sessionStartHour = 8, // 交易日开始小时（0-23）
    this.sessionStartMinute = 0, // 交易日开始分钟（0-59）
    this.timezoneOffset = 8, // 时区偏移（小时，相对于UTC，中国为+8）
  }) : super(key: const FlexiIndicatorKey('vwap_bands', label: 'VWAP Bands'));

  // 标准差倍数
  final double devUp1, devDn1;
  final double devUp2, devDn2;
  final double devUp3, devDn3;
  final double devUp4, devDn4;
  final double devUp5, devDn5;

  // 显示开关
  final bool showDv2, showDv3, showDv4, showDv5;
  final bool showPrevVWAP;

  // 颜色配置
  final Color vwapColor;
  final Color band1Color;
  final Color upperBandColor;
  final Color lowerBandColor;
  final double fillOpacity;

  // 线条配置
  final double vwapLineWidth;
  final double bandLineWidth;

  // 交易会话配置
  final int sessionStartHour;
  final int sessionStartMinute;
  final int timezoneOffset;

  @override
  dynamic get calcParam => {
    'devUp1': devUp1, 'devDn1': devDn1,
    'devUp2': devUp2, 'devDn2': devDn2,
    'devUp3': devUp3, 'devDn3': devDn3,
    'devUp4': devUp4, 'devDn4': devDn4,
    'devUp5': devUp5, 'devDn5': devDn5,
  };

  @override
  VWAPBandsPaintObject createPaintObject(IPaintContext context) {
    return VWAPBandsPaintObject(context: context, indicator: this);
  }

  factory VWAPBandsIndicator.fromJson(Map<String, dynamic> json) =>
      _$VWAPBandsIndicatorFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$VWAPBandsIndicatorToJson(this);
}

class VWAPBandsPaintObject<T extends VWAPBandsIndicator>
    extends PaintObjectBox<T> with PaintYAxisTicksOnCrossMixin {
  VWAPBandsPaintObject({
    required super.context,
    required super.indicator,
  });

  // VWAP计算相关变量
  double _vwapSum = 0.0;
  double _volumeSum = 0.0;
  double _v2Sum = 0.0;
  double? _lastSessionStart;
  double? _prevVWAP;

  List<VWAPBandsData> _calculatedData = [];

  @override
  MinMax? initState(int start, int end) {
    if (!klineData.canPaintChart) return null;

    // 计算VWAP数据
    _calculateVWAPBands(start, end);

    // 计算显示范围的最值
    if (_calculatedData.isEmpty) return null;

    double minValue = double.infinity;
    double maxValue = double.negativeInfinity;

    for (int i = start; i < end && i < _calculatedData.length; i++) {
      final data = _calculatedData[i];
      
      // VWAP本身
      minValue = math.min(minValue, data.vwap);
      maxValue = math.max(maxValue, data.vwap);

      // 第一组带（始终显示）
      if (data.upperBands.isNotEmpty) {
        minValue = math.min(minValue, data.lowerBands[0]);
        maxValue = math.max(maxValue, data.upperBands[0]);
      }

      // 其他组带
      for (int j = 1; j < data.upperBands.length; j++) {
        bool shouldShow = false;
        switch (j) {
          case 1: shouldShow = indicator.showDv2; break;
          case 2: shouldShow = indicator.showDv3; break;
          case 3: shouldShow = indicator.showDv4; break;
          case 4: shouldShow = indicator.showDv5; break;
        }
        
        if (shouldShow) {
          minValue = math.min(minValue, data.lowerBands[j]);
          maxValue = math.max(maxValue, data.upperBands[j]);
        }
      }

      // 前日VWAP
      if (indicator.showPrevVWAP && data.prevVWAP != null) {
        minValue = math.min(minValue, data.prevVWAP!);
        maxValue = math.max(maxValue, data.prevVWAP!);
      }
    }

    if (minValue.isInfinite || maxValue.isInfinite) return null;

    return MinMax(
      min: BagNum.fromNum(minValue),
      max: BagNum.fromNum(maxValue),
    );
  }

  void _calculateVWAPBands(int start, int end) {
    final list = klineData.list;
    if (list.isEmpty) {
      _calculatedData.clear();
      return;
    }



    // 初始化计算数据数组，保持与K线数据相同的长度
    _calculatedData = List<VWAPBandsData>.generate(list.length, (index) =>
      VWAPBandsData(vwap: 0, stdDev: 0, upperBands: [], lowerBands: [], prevVWAP: null));

    // 重置会话变量
    _vwapSum = 0.0;
    _volumeSum = 0.0;
    _v2Sum = 0.0;
    _lastSessionStart = null;
    _prevVWAP = null;

    // 按时间顺序遍历所有数据（从旧到新，需要反向遍历因为数据是降序存储的）
    for (int i = list.length - 1; i >= 0; i--) {
      final candle = list[i];
      final timestamp = candle.ts.toDouble();
      
      // 检查是否新会话（新的一天）
      final sessionStart = _getSessionStart(timestamp);
      final isNewSession = _lastSessionStart != sessionStart;
      
      if (isNewSession) {
        if (_lastSessionStart != null && _volumeSum > 0) {
          // 保存前一日VWAP
          _prevVWAP = _vwapSum / _volumeSum;
        }
        
        // 重置VWAP计算
        _vwapSum = 0.0;
        _volumeSum = 0.0;
        _v2Sum = 0.0;
        _lastSessionStart = sessionStart;
      }

      // 计算HL2价格
      final hl2 = (candle.high.toDouble() + candle.low.toDouble()) / 2.0;
      final volume = candle.vol.toDouble();

      // 累计VWAP计算变量
      _vwapSum += hl2 * volume;
      _volumeSum += volume;
      _v2Sum += volume * hl2 * hl2;

      // 计算VWAP和标准差
      final vwap = _volumeSum > 0 ? _vwapSum / _volumeSum : hl2;
      final variance = _volumeSum > 0 
          ? math.max(_v2Sum / _volumeSum - vwap * vwap, 0)
          : 0.0;
      final stdDev = math.sqrt(variance);

      // 计算各组标准差带
      final upperBands = [
        vwap + indicator.devUp1 * stdDev,
        vwap + indicator.devUp2 * stdDev,
        vwap + indicator.devUp3 * stdDev,
        vwap + indicator.devUp4 * stdDev,
        vwap + indicator.devUp5 * stdDev,
      ];

      final lowerBands = [
        vwap - indicator.devDn1 * stdDev,
        vwap - indicator.devDn2 * stdDev,
        vwap - indicator.devDn3 * stdDev,
        vwap - indicator.devDn4 * stdDev,
        vwap - indicator.devDn5 * stdDev,
      ];

      // 将结果存储在对应索引位置（保持与K线数据的索引对应关系）
      _calculatedData[i] = VWAPBandsData(
        vwap: vwap,
        stdDev: stdDev,
        upperBands: upperBands,
        lowerBands: lowerBands,
        prevVWAP: _prevVWAP,
      );
    }
  }

  double _getSessionStart(double timestamp) {
    // 获取UTC时间戳对应的UTC DateTime
    final utcDate = DateTime.fromMillisecondsSinceEpoch(timestamp.toInt(), isUtc: true);
    
    // 转换到中国时区（UTC+8），创建本地时间（非UTC）
    final localDate = DateTime(
      utcDate.year,
      utcDate.month, 
      utcDate.day,
      utcDate.hour + indicator.timezoneOffset,
      utcDate.minute,
      utcDate.second,
      utcDate.millisecond
    );
    
    // 如果小时数超过24，需要处理日期进位
    final normalizedLocalDate = localDate.hour >= 24 
        ? DateTime(localDate.year, localDate.month, localDate.day + 1, localDate.hour - 24, localDate.minute, localDate.second, localDate.millisecond)
        : localDate;
    
    // 计算当天的交易开始时间（本地时区）
    DateTime tradingDayStart = DateTime(
      normalizedLocalDate.year, 
      normalizedLocalDate.month, 
      normalizedLocalDate.day, 
      indicator.sessionStartHour, 
      indicator.sessionStartMinute, 
      0
    );
    
    // 如果当前本地时间早于当天的交易开始时间，使用前一交易日
    if (normalizedLocalDate.isBefore(tradingDayStart)) {
      tradingDayStart = tradingDayStart.subtract(const Duration(days: 1));
    }
    
    // 转换回UTC时间戳：本地时间减去时区偏移
    final utcSessionStart = DateTime.utc(
      tradingDayStart.year,
      tradingDayStart.month, 
      tradingDayStart.day,
      tradingDayStart.hour - indicator.timezoneOffset,
      tradingDayStart.minute,
      tradingDayStart.second
    );
    
    // 如果小时数小于0，需要处理日期退位
    final normalizedUtcSessionStart = utcSessionStart.hour < 0
        ? DateTime.utc(utcSessionStart.year, utcSessionStart.month, utcSessionStart.day - 1, utcSessionStart.hour + 24, utcSessionStart.minute, utcSessionStart.second)
        : utcSessionStart;
    

    
    return normalizedUtcSessionStart.millisecondsSinceEpoch.toDouble();
  }

  @override
  void paintChart(Canvas canvas, Size size) {
    if (!klineData.canPaintChart || _calculatedData.isEmpty) return;

    final list = klineData.list;
    final start = klineData.start;
    final end = klineData.end;
    final offset = startCandleDx;

    // 创建路径用于绘制填充区域
    final List<Path> fillPaths = List.generate(10, (_) => Path()); // 5个上下填充区域

    // 绘制数据点
    for (int i = start; i < end && i < _calculatedData.length; i++) {
      final data = _calculatedData[i];
      final dx = offset - (i - start) * candleActualWidth;

      // 计算Y坐标
      final vwapY = valueToDy(BagNum.fromNum(data.vwap));
      final upperBandYs = data.upperBands.map((value) => valueToDy(BagNum.fromNum(value))).toList();
      final lowerBandYs = data.lowerBands.map((value) => valueToDy(BagNum.fromNum(value))).toList();

      // 构建填充路径
      _buildFillPaths(fillPaths, dx, vwapY, upperBandYs, lowerBandYs, i == start);

      // 检查是否同一交易会话
      bool isSameSession = true;
      if (i > start) {
        final candle = list[i];
        final prevCandle = list[i - 1];
        
        // 检查当前K线和前一根K线是否在同一交易会话
        final currentSessionStart = _getSessionStart(candle.ts.toDouble());
        final prevSessionStart = _getSessionStart(prevCandle.ts.toDouble());
        isSameSession = currentSessionStart == prevSessionStart;
      }

      // 绘制VWAP线
      if (i > start && isSameSession) {
        final prevData = _calculatedData[i - 1];
        final prevDx = offset - ((i - 1) - start) * candleActualWidth;
        final prevVwapY = valueToDy(BagNum.fromNum(prevData.vwap));
        
        canvas.drawLine(
          Offset(prevDx, prevVwapY),
          Offset(dx, vwapY),
          Paint()
            ..color = indicator.vwapColor
            ..strokeWidth = indicator.vwapLineWidth
            ..style = PaintingStyle.stroke,
        );
      }

      // 绘制标准差带线
      _drawBandLines(canvas, dx, i, start, offset, upperBandYs, lowerBandYs, isSameSession);

      // 绘制前日VWAP
      if (indicator.showPrevVWAP && data.prevVWAP != null) {
        final prevVwapY = valueToDy(BagNum.fromNum(data.prevVWAP!));
        final currentClose = list[i].close.toDouble();
        final color = currentClose > data.prevVWAP! ? indicator.lowerBandColor : indicator.upperBandColor;
        
        canvas.drawCircle(
          Offset(dx, prevVwapY),
          2.0,
          Paint()
            ..color = color
            ..style = PaintingStyle.fill,
        );
      }
    }

    // 绘制填充区域
    _drawFillAreas(canvas, fillPaths);
  }

  void _buildFillPaths(List<Path> paths, double dx, double vwapY, 
                      List<double> upperBandYs, List<double> lowerBandYs, bool isFirst) {
    if (isFirst) {
      // 初始化路径起点
      paths[0].moveTo(dx, vwapY);           // 中心到上带1
      paths[1].moveTo(dx, vwapY);           // 中心到下带1
      
      if (indicator.showDv2) {
        paths[2].moveTo(dx, upperBandYs[0]); // 上带1到上带2
        paths[3].moveTo(dx, lowerBandYs[0]); // 下带1到下带2
      }
      
      if (indicator.showDv3) {
        paths[4].moveTo(dx, upperBandYs[1]); // 上带2到上带3
        paths[5].moveTo(dx, lowerBandYs[1]); // 下带2到下带3
      }
      
      if (indicator.showDv4) {
        paths[6].moveTo(dx, upperBandYs[2]); // 上带3到上带4
        paths[7].moveTo(dx, lowerBandYs[2]); // 下带3到下带4
      }
      
      if (indicator.showDv5) {
        paths[8].moveTo(dx, upperBandYs[3]); // 上带4到上带5
        paths[9].moveTo(dx, lowerBandYs[3]); // 下带4到下带5
      }
    }

    // 添加路径点
    paths[0].lineTo(dx, upperBandYs[0]);
    paths[1].lineTo(dx, lowerBandYs[0]);
    
    if (indicator.showDv2) {
      paths[2].lineTo(dx, upperBandYs[1]);
      paths[3].lineTo(dx, lowerBandYs[1]);
    }
    
    if (indicator.showDv3) {
      paths[4].lineTo(dx, upperBandYs[2]);
      paths[5].lineTo(dx, lowerBandYs[2]);
    }
    
    if (indicator.showDv4) {
      paths[6].lineTo(dx, upperBandYs[3]);
      paths[7].lineTo(dx, lowerBandYs[3]);
    }
    
    if (indicator.showDv5) {
      paths[8].lineTo(dx, upperBandYs[4]);
      paths[9].lineTo(dx, lowerBandYs[4]);
    }
  }

  void _drawBandLines(Canvas canvas, double dx, int i, int start, double offset,
                     List<double> upperBandYs, List<double> lowerBandYs, bool isSameSession) {
    if (i > start && isSameSession) {
      final prevData = _calculatedData[i - 1];
      final prevDx = offset - ((i - 1) - start) * candleActualWidth;
      
      final prevUpperBandYs = prevData.upperBands.map((value) => valueToDy(BagNum.fromNum(value))).toList();
      final prevLowerBandYs = prevData.lowerBands.map((value) => valueToDy(BagNum.fromNum(value))).toList();

      // 第一组带（灰色）
      _drawBandLine(canvas, prevDx, dx, prevUpperBandYs[0], upperBandYs[0], indicator.band1Color);
      _drawBandLine(canvas, prevDx, dx, prevLowerBandYs[0], lowerBandYs[0], indicator.band1Color);

      // 其他组带
      final showFlags = [indicator.showDv2, indicator.showDv3, indicator.showDv4, indicator.showDv5];
      for (int j = 1; j < upperBandYs.length && j < showFlags.length + 1; j++) {
        if (showFlags[j - 1]) {
          _drawBandLine(canvas, prevDx, dx, prevUpperBandYs[j], upperBandYs[j], indicator.upperBandColor);
          _drawBandLine(canvas, prevDx, dx, prevLowerBandYs[j], lowerBandYs[j], indicator.lowerBandColor);
        }
      }
    }
  }

  void _drawBandLine(Canvas canvas, double x1, double x2, double y1, double y2, Color color) {
    canvas.drawLine(
      Offset(x1, y1),
      Offset(x2, y2),
      Paint()
        ..color = color
        ..strokeWidth = indicator.bandLineWidth
        ..style = PaintingStyle.stroke,
    );
  }

  void _drawFillAreas(Canvas canvas, List<Path> paths) {
    final fillColors = [
      indicator.band1Color.withOpacity(indicator.fillOpacity),     // 中心到上带1
      indicator.band1Color.withOpacity(indicator.fillOpacity),     // 中心到下带1
      indicator.upperBandColor.withOpacity(indicator.fillOpacity), // 上带1到上带2
      indicator.lowerBandColor.withOpacity(indicator.fillOpacity), // 下带1到下带2
      indicator.upperBandColor.withOpacity(indicator.fillOpacity), // 上带2到上带3
      indicator.lowerBandColor.withOpacity(indicator.fillOpacity), // 下带2到下带3
      indicator.upperBandColor.withOpacity(indicator.fillOpacity), // 上带3到上带4
      indicator.lowerBandColor.withOpacity(indicator.fillOpacity), // 下带3到下带4
      indicator.upperBandColor.withOpacity(indicator.fillOpacity), // 上带4到上带5
      indicator.lowerBandColor.withOpacity(indicator.fillOpacity), // 下带4到下带5
    ];

    for (int i = 0; i < paths.length; i++) {
      if (paths[i].getBounds().isEmpty) continue;
      
      canvas.drawPath(
        paths[i],
        Paint()
          ..color = fillColors[i]
          ..style = PaintingStyle.fill,
      );
    }
  }

  @override
  void paintExtraAboveChart(Canvas canvas, Size size) {
    /// 绘制VWAP指标y轴价格标签
    paintVWAPPriceLabels(canvas, size);
  }

  /// 绘制VWAP各线条的y轴价格标签
  void paintVWAPPriceLabels(Canvas canvas, Size size) {
    if (!klineData.canPaintChart || _calculatedData.isEmpty) return;

    // 获取最新的VWAP数据（第一个元素，因为数据按时间降序排列）
    final latestData = _calculatedData[0];
    
    // 绘制VWAP价格标签
    _drawPriceLabel(canvas, latestData.vwap, indicator.vwapColor, 'VWAP', isVWAP: true);

    // 绘制第一组标准差带标签（始终显示）
    _drawPriceLabel(canvas, latestData.upperBands[0], indicator.band1Color, '上轨1');
    _drawPriceLabel(canvas, latestData.lowerBands[0], indicator.band1Color, '下轨1');

    // 绘制其他组带标签
    final showFlags = [indicator.showDv2, indicator.showDv3, indicator.showDv4, indicator.showDv5];
    final labels = ['上轨2', '上轨3', '上轨4', '上轨5'];
    final lowerLabels = ['下轨2', '下轨3', '下轨4', '下轨5'];
    
    for (int i = 0; i < showFlags.length && i + 1 < latestData.upperBands.length; i++) {
      if (showFlags[i]) {
        _drawPriceLabel(canvas, latestData.upperBands[i + 1], indicator.upperBandColor, labels[i]);
        _drawPriceLabel(canvas, latestData.lowerBands[i + 1], indicator.lowerBandColor, lowerLabels[i]);
      }
    }

    // 绘制前日VWAP标签
    if (indicator.showPrevVWAP && latestData.prevVWAP != null) {
      final currentClose = klineData.list[0].close.toDouble();
      final color = currentClose > latestData.prevVWAP! ? indicator.lowerBandColor : indicator.upperBandColor;
      _drawPriceLabel(canvas, latestData.prevVWAP!, color, '前日');
    }
  }

  /// 绘制单个价格标签
  void _drawPriceLabel(Canvas canvas, double price, Color color, String label, {bool isVWAP = false}) {
    final dy = valueToDy(BagNum.fromNum(price));
    
    // 检查Y坐标是否在可绘制范围内
    if (dy < drawableRect.top || dy > drawableRect.bottom) return;

    final priceText = price.toStringAsFixed(klineData.precision);

    // 为VWAP线设置不同的文字颜色，其他使用白色
    final textColor = isVWAP ? Colors.black : Colors.white;

    final labelConfig = defTicksTextConfig.of(
      textColor: textColor,
      background: color.withOpacity(0.7), // 设置半透明背景
      borderColor: color.withOpacity(0.5), // 设置半透明边框
    );

    // 绘制价格标签在右侧
    canvas.drawTextArea(
      offset: Offset(
        chartRect.right - 2.0, // 距离右边缘2像素
        dy - labelConfig.areaHeight / 2, // 垂直居中
      ),
      drawDirection: DrawDirection.rtl,
      drawableRect: drawableRect,
      text: priceText,
      textConfig: labelConfig,
    );
  }

  @override
  void onCross(Canvas canvas, Offset offset) {
    // VWAP指标不需要特殊的十字线处理，避免干扰K线的十字线显示
    // 仅绘制y轴价格刻度
    paintYAxisTicksOnCross(
      canvas,
      offset,
      precision: klineData.precision,
    );
  }

  // onCross时, 格式化Y轴上的标记值.
  @override
  String formatTicksValueOnCross(BagNum value, {required int precision}) {
    return value.toDouble().toStringAsFixed(precision);
  }

  @override
  Size? paintTips(
    Canvas canvas, {
    CandleModel? model,
    Offset? offset,
    Rect? tipsRect,
  }) {
    if (model == null) return null;

    final index = klineData.list.indexOf(model);
    if (index < 0 || index >= _calculatedData.length) return null;

    final data = _calculatedData[index];

    // 构建提示文本
    final vwapStr = '成交量加权平均趋势: ${data.vwap.toStringAsFixed(2)}';
    final stdDevStr = '标准差: ${data.stdDev.toStringAsFixed(4)}';
    
    // 构建成对的标准差带显示
    List<String> bandPairs = [];
    
    // 第一组带（始终显示）
    final up1 = data.upperBands[0].toStringAsFixed(2);
    final dn1 = data.lowerBands[0].toStringAsFixed(2);
    bandPairs.add('Up1:$up1 - Dn1:$dn1');
    
    // 其他组带（根据显示开关）
    final showFlags = [indicator.showDv2, indicator.showDv3, indicator.showDv4, indicator.showDv5];
    final bandNumbers = ['2', '3', '4', '5'];
    
    for (int i = 0; i < showFlags.length && i + 1 < data.upperBands.length; i++) {
      if (showFlags[i]) {
        final upValue = data.upperBands[i + 1].toStringAsFixed(2);
        final dnValue = data.lowerBands[i + 1].toStringAsFixed(2);
        final num = bandNumbers[i];
        bandPairs.add('Up$num:$upValue - Dn$num:$dnValue');
      }
    }
    
    // 组合所有文本
    String tipsText = '$vwapStr  $stdDevStr';
    
    // 添加标准差带对
    if (bandPairs.isNotEmpty) {
      tipsText = '$tipsText  ${bandPairs.join('  ')}';
    }
    
    // 添加前日VWAP
    if (indicator.showPrevVWAP && data.prevVWAP != null) {
      final prevVwapStr = 'Prev: ${data.prevVWAP!.toStringAsFixed(2)}';
      tipsText = '$tipsText  $prevVwapStr';
    }

    // 绘制提示文本
    final textConfig = defTicksTextConfig.of(
      textColor: theme.textColor,
    );

    final size = canvas.drawTextArea(
      offset: tipsRect?.topLeft ?? Offset.zero,
      text: tipsText,
      textConfig: textConfig,
    );

    return size;
  }

  @override
  void precompute(Range range, {bool reset = false}) {
    // VWAP需要全量计算，不支持部分预计算
    if (reset) {
      _calculatedData.clear();
      _vwapSum = 0.0;
      _volumeSum = 0.0;
      _v2Sum = 0.0;
      _lastSessionStart = null;
      _prevVWAP = null;
    }
  }
} 