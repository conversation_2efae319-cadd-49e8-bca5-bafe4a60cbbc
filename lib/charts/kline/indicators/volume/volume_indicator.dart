import 'package:flutter/material.dart';
import '../../flexi_kline.dart';
import 'dart:math' as math;

/// 成交量数据
class VolumeData {
  final double volume;
  final double? ma;
  final bool isBullish;

  VolumeData({
    required this.volume,
    this.ma,
    required this.isBullish,
  });
}

class _VolumePoint {
  final double x;
  final double y;
  final bool isBullish;
  final double volume;

  _VolumePoint({
    required this.x,
    required this.y,
    required this.isBullish,
    required this.volume,
  });
}

/// 成交量指标
class VolumeIndicator extends PaintObjectIndicator {
  /// 显示模式：area(面积图) 或 bars(柱状图)
  final String displayMode;
  
  /// 上涨颜色
  final Color bullishColor;
  
  /// 下跌颜色
  final Color bearishColor;
  
  /// 是否显示MA线
  final bool showMA;
  
  /// MA计算长度
  final int maLength;
  
  /// MA线颜色
  final Color maColor;
  
  /// MA线宽度
  final double maLineWidth;
  
  /// 透明度
  final double opacity;

  VolumeIndicator({
    super.zIndex = 1,
    required super.height,
    super.padding = defaultMainIndicatorPadding,
    this.displayMode = 'area',
    this.bullishColor = const Color(0xFF26A69A),
    this.bearishColor = const Color(0xFFEF5350),
    this.showMA = true,
    this.maLength = 20,
    this.maColor = const Color(0xFFFFEB3B),
    this.maLineWidth = 1.5,
    this.opacity = 0.8,
  }) : super(key: const FlexiIndicatorKey('volume', label: 'Volume'));

  @override
  dynamic get calcParam => {
    'maLength': maLength,
    'showMA': showMA,
  };

  @override
  VolumePaintObject createPaintObject(IPaintContext context) {
    return VolumePaintObject(context: context, indicator: this);
  }

  @override
  VolumeIndicator copyWith({
    int? zIndex,
    double? height,
    EdgeInsets? padding,
    String? displayMode,
    Color? bullishColor,
    Color? bearishColor,
    bool? showMA,
    int? maLength,
    Color? maColor,
    double? maLineWidth,
    double? opacity,
  }) {
    return VolumeIndicator(
      zIndex: zIndex ?? this.zIndex,
      height: height ?? this.height,
      padding: padding ?? this.padding,
      displayMode: displayMode ?? this.displayMode,
      bullishColor: bullishColor ?? this.bullishColor,
      bearishColor: bearishColor ?? this.bearishColor,
      showMA: showMA ?? this.showMA,
      maLength: maLength ?? this.maLength,
      maColor: maColor ?? this.maColor,
      maLineWidth: maLineWidth ?? this.maLineWidth,
      opacity: opacity ?? this.opacity,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'displayMode': displayMode,
      'bullishColor': bullishColor.value,
      'bearishColor': bearishColor.value,
      'showMA': showMA,
      'maLength': maLength,
      'maColor': maColor.value,
      'maLineWidth': maLineWidth,
      'opacity': opacity,
    };
  }

  factory VolumeIndicator.fromJson(Map<String, dynamic> json) {
    return VolumeIndicator(
      height: 100,
      displayMode: json['displayMode'] ?? 'area',
      bullishColor: Color(json['bullishColor'] ?? 0xFF26A69A),
      bearishColor: Color(json['bearishColor'] ?? 0xFFEF5350),
      showMA: json['showMA'] ?? true,
      maLength: json['maLength'] ?? 20,
      maColor: Color(json['maColor'] ?? 0xFFFFEB3B),
      maLineWidth: json['maLineWidth']?.toDouble() ?? 1.5,
      opacity: json['opacity']?.toDouble() ?? 0.8,
    );
  }
}

/// 成交量绘制对象
class VolumePaintObject<T extends VolumeIndicator> extends PaintObjectBox<T> {
  VolumePaintObject({
    required super.context,
    required super.indicator,
  });

  List<VolumeData> _calculatedData = [];
  double? _maxVolume;

  @override
  MinMax? initState(int start, int end) {
    if (!klineData.canPaintChart) return null;

    _calculateVolumeData(start, end);

    if (_calculatedData.isEmpty) return null;

    // 成交量指标不需要返回价格范围，因为它使用自己的缩放
    return null;
  }

  @override
  void precompute(Range range, {bool reset = false}) {
    super.precompute(range, reset: reset);

    // 当数据更新时重新计算成交量数据
    _calculateVolumeData(range.start, range.end);
  }

  void _calculateVolumeData(int start, int end) {
    final list = klineData.list;

    if (list.isEmpty) {
      _calculatedData.clear();
      _maxVolume = null;
      return;
    }

    // 确保_calculatedData长度与数据长度一致
    if (_calculatedData.length != list.length) {
      _calculatedData = List<VolumeData>.generate(list.length,
        (index) => VolumeData(volume: 0, ma: null, isBullish: true));
    }
    



    // 计算MA - 数据是倒序排列的（最新在前），需要倒序计算MA
    final maValues = <double?>[];
    if (indicator.showMA) {
      // 因为数据是倒序的，我们需要从后往前计算MA
      for (int i = 0; i < list.length; i++) {
        // 对于倒序数据，需要确保有足够的历史数据来计算MA
        int historyEndIndex = list.length - 1 - i; // 对应的历史位置
        if (historyEndIndex < indicator.maLength - 1) {
          maValues.add(null); // 没有足够的历史数据
        } else {
          double sum = 0;
          // 从历史数据往前计算MA
          for (int j = historyEndIndex - indicator.maLength + 1; j <= historyEndIndex; j++) {
            sum += list[list.length - 1 - j].vol.toDouble(); // 转换回正序索引
          }
          maValues.add(sum / indicator.maLength);
        }
      }
    } else {
      maValues.addAll(List.filled(list.length, null));
    }

    // 计算最大成交量
    double maxVol = 0;
    for (int i = 0; i < list.length; i++) {
      final candle = list[i];
      final volume = candle.vol.toDouble();
      
      // 基于主动买入成交量比例判断颜色，而不是价格涨跌
      bool isBullish = true; // 默认为上涨颜色
      if (candle.priceLevel != null) {
        final totalBuyVolume = candle.priceLevel!.totalBuyVolume ?? 0.0;
        final totalSellVolume = candle.priceLevel!.totalSellVolume ?? 0.0;
        final totalVolume = totalBuyVolume + totalSellVolume;
        
        if (totalVolume > 0) {
          // 如果主动买入成交量占比超过50%，显示上涨颜色，否则显示下跌颜色
          isBullish = (totalBuyVolume / totalVolume) >= 0.5;
        } else {
          // 如果没有成交量数据，回退到价格判断
          isBullish = candle.close >= candle.open;
        }
      } else {
        // 如果没有priceLevel数据，回退到价格判断
        isBullish = candle.close >= candle.open;
      }
      
      maxVol = math.max(maxVol, volume);
      if (maValues[i] != null) {
        maxVol = math.max(maxVol, maValues[i]!);
      }

      _calculatedData[i] = VolumeData(
        volume: volume,
        ma: maValues[i],
        isBullish: isBullish,
      );
    }

    _maxVolume = maxVol * 1.1; // 留10%的顶部空间
  }

  @override
  void paintChart(Canvas canvas, Size size) {
    if (_calculatedData.isEmpty || _maxVolume == null || _maxVolume == 0) return;

    // 计算可见范围内的最大成交量
    final visibleMaxVolume = _calculateVisibleMaxVolume();
    if (visibleMaxVolume <= 0) return;

    // 限制成交量指标的最大高度为总高度的30%
    final maxVolumeHeight = size.height * 0.3;

    if (indicator.displayMode == 'area') {
      _paintAreaChart(canvas, size, maxVolumeHeight, visibleMaxVolume);
    } else {
      _paintBarChart(canvas, size, maxVolumeHeight, visibleMaxVolume);
    }

    if (indicator.showMA) {
      _paintMALine(canvas, size, maxVolumeHeight, visibleMaxVolume);
    }
  }

  /// 计算可见范围内的最大成交量
  double _calculateVisibleMaxVolume() {
    final start = klineData.start;
    final end = klineData.end;

    double maxVol = 0;
    for (int i = start; i < end && i < _calculatedData.length; i++) {
      final data = _calculatedData[i];
      maxVol = math.max(maxVol, data.volume);
      if (data.ma != null) {
        maxVol = math.max(maxVol, data.ma!);
      }
    }

    return maxVol * 1.1; // 留10%的顶部空间
  }

  /// 绘制面积图
  void _paintAreaChart(Canvas canvas, Size size, double maxVolumeHeight, double visibleMaxVolume) {
    // 使用FlexiKline的可见区域数据
    final start = klineData.start;
    final end = klineData.end;
    final offset = startCandleDx;

    if (start >= end || start >= _calculatedData.length) return;
    
    // 收集所有需要绘制的点
    final List<_VolumePoint> points = [];
    
    for (int i = start; i < end && i < _calculatedData.length; i++) {
      final data = _calculatedData[i];
      final x = offset - (i - start) * candleActualWidth;
      
      // 温和的缩放算法：保持比例关系 + 小幅提升可见性
      final normalizedVolume = (data.volume / visibleMaxVolume).clamp(0.0, 1.0);
      
      // 使用温和的缩放：0.8次方，比线性稍微提升小值可见性
      final smoothedVolume = math.pow(normalizedVolume, 0.8).toDouble();
      
      // 适中的最小高度：3%
      final minHeight = maxVolumeHeight * 0.03;
      
      final volumeHeight = math.max(minHeight, smoothedVolume * maxVolumeHeight);
      final y = size.height - volumeHeight;
      
      points.add(_VolumePoint(x: x, y: y, isBullish: data.isBullish, volume: data.volume));
    }
    
    if (points.isEmpty) return;
    
    // 分段绘制不同颜色的区域，确保完全连续
    int segmentStart = 0;
    bool currentTrend = points[0].isBullish;
    
    for (int i = 1; i <= points.length; i++) {
      bool shouldEndSegment = (i == points.length) || 
                              (i < points.length && points[i].isBullish != currentTrend);
      
      if (shouldEndSegment) {
        // 绘制当前段
        final segmentPath = Path();
        
        // 从底部开始
        segmentPath.moveTo(points[segmentStart].x, size.height);
        
        // 连接所有段内的点
        for (int j = segmentStart; j < i; j++) {
          segmentPath.lineTo(points[j].x, points[j].y);
        }
        
        // 回到底部闭合
        segmentPath.lineTo(points[i - 1].x, size.height);
        segmentPath.close();
        
        // 选择颜色并绘制
        final color = currentTrend ? indicator.bullishColor : indicator.bearishColor;
        canvas.drawPath(segmentPath, Paint()
          ..color = color.withOpacity(indicator.opacity)
          ..style = PaintingStyle.fill);
        
        // 开始新段 - 注意新段应该从前一段的结束点开始
        if (i < points.length) {
          segmentStart = i - 1; // 新段从前一段的最后一个点开始，确保连续性
          currentTrend = points[i].isBullish;
        }
      }
    }
  }

  /// 绘制柱状图
  void _paintBarChart(Canvas canvas, Size size, double maxVolumeHeight, double visibleMaxVolume) {
    // 使用FlexiKline的可见区域数据
    final start = klineData.start;
    final end = klineData.end;
    final offset = startCandleDx;

    // 使用与K线一致的宽度，稍微缩小以避免重叠
    final barWidth = candleWidth * 0.7;
    
    for (int i = start; i < end && i < _calculatedData.length; i++) {
      final data = _calculatedData[i];
      
      // 使用FlexiKline的坐标系统计算x位置
      final x = offset - (i - start) * candleActualWidth;
      
      // 温和的缩放算法：保持比例关系 + 小幅提升可见性
      final normalizedVolume = (data.volume / visibleMaxVolume).clamp(0.0, 1.0);
      
      // 使用温和的缩放：0.8次方，比线性稍微提升小值可见性
      final smoothedVolume = math.pow(normalizedVolume, 0.8).toDouble();
      
      // 适中的最小高度：3%
      final minHeight = maxVolumeHeight * 0.03;
      
      final barHeight = math.max(minHeight, smoothedVolume * maxVolumeHeight);
      
      final color = data.isBullish ? indicator.bullishColor : indicator.bearishColor;
      
      final rect = Rect.fromLTWH(
        x - barWidth / 2,
        size.height - barHeight,
        barWidth,
        barHeight,
      );
      
      canvas.drawRect(rect, Paint()
        ..color = color.withOpacity(indicator.opacity)
        ..style = PaintingStyle.fill);
    }
  }

  /// 绘制MA线
  void _paintMALine(Canvas canvas, Size size, double maxVolumeHeight, double visibleMaxVolume) {
    final path = Path();
    bool started = false;
    
    // 使用FlexiKline的可见区域数据
    final start = klineData.start;
    final end = klineData.end;
    final offset = startCandleDx;
    
    for (int i = start; i < end && i < _calculatedData.length; i++) {
      final data = _calculatedData[i];
      if (data.ma == null) continue;
      
      // 使用FlexiKline的坐标系统计算x位置
      final x = offset - (i - start) * candleActualWidth;
      
      // 对MA线也应用相同的温和缩放算法
      final normalizedMA = (data.ma! / visibleMaxVolume).clamp(0.0, 1.0);
      
      // 使用温和的缩放：0.8次方
      final smoothedMA = math.pow(normalizedMA, 0.8).toDouble();
      
      // 适中的最小高度：3%
      final minHeight = maxVolumeHeight * 0.03;
      
      final maHeight = math.max(minHeight, smoothedMA * maxVolumeHeight);
      final y = size.height - maHeight;
      
      if (!started) {
        path.moveTo(x, y);
        started = true;
      } else {
        path.lineTo(x, y);
      }
    }
    
    if (started) {
      canvas.drawPath(path, Paint()
        ..color = indicator.maColor
        ..strokeWidth = indicator.maLineWidth
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round
        ..strokeJoin = StrokeJoin.round);
    }
  }

  /// 格式化成交量显示
  String _formatVolume(double volume) {
    if (volume >= 1000000000) {
      return '${(volume / 1000000000).toStringAsFixed(1)}B';
    } else if (volume >= 1000000) {
      return '${(volume / 1000000).toStringAsFixed(1)}M';
    } else if (volume >= 1000) {
      return '${(volume / 1000).toStringAsFixed(1)}K';
    } else {
      return volume.toStringAsFixed(0);
    }
  }



  @override
  void onCross(Canvas canvas, Offset offset) {
    // 成交量指标的十字线处理（暂时简化）
  }

  @override
  Size? paintTips(
    Canvas canvas, {
    CandleModel? model,
    Offset? offset,
    Rect? tipsRect,
  }) {
    if (model == null) return null;
    
    final volume = model.vol.toDouble();
    String volumeText = 'Vol: ${_formatVolume(volume)}';
    
    // 计算买卖量信息并拼接到volumeText中
    double buyVolume = 0.0;
    double sellVolume = 0.0;
    
    if (model.priceLevel != null) {
      buyVolume = model.priceLevel!.totalBuyVolume ?? 0.0;
      sellVolume = model.priceLevel!.totalSellVolume ?? 0.0;
      
      // 如果没有买卖量数据，根据涨跌估算
      if (buyVolume == 0.0 && sellVolume == 0.0) {
        if (model.close >= model.open) {
          buyVolume = volume * 0.6; // 上涨时假设60%为买入
          sellVolume = volume * 0.4;
        } else {
          buyVolume = volume * 0.4; // 下跌时假设40%为买入
          sellVolume = volume * 0.6;
        }
      }
    } else {
      // 没有priceLevel数据时的估算
      if (model.close >= model.open) {
        buyVolume = volume * 0.6;
        sellVolume = volume * 0.4;
      } else {
        buyVolume = volume * 0.4;
        sellVolume = volume * 0.6;
      }
    }
    
    // 拼接买卖量信息
    volumeText += '  买: ${_formatVolume(buyVolume)}  卖: ${_formatVolume(sellVolume)}';
    
    if (indicator.showMA && offset != null && klineData.canPaintChart) {
      // 简化处理，使用当前数据的MA
      final start = klineData.start;
      if (start < _calculatedData.length) {
        final data = _calculatedData[start];
        if (data.ma != null) {
          volumeText += '  MA${indicator.maLength}: ${_formatVolume(data.ma!)}';
        }
      }
    }
    
    const textStyle = TextStyle(
      color: Colors.white,
      fontSize: 12,
    );
    
    final textPainter = TextPainter(
      text: TextSpan(text: volumeText, style: textStyle),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    
    if (tipsRect != null) {
      textPainter.paint(canvas, tipsRect.topLeft);
    }
    
    return textPainter.size;
  }
} 