// Copyright 2024 Andy.Zhao
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

part of 'candle.dart';

@CopyWith()
@FlexiIndicatorSerializable
class CandleIndicator extends CandleBaseIndicator {
  CandleIndicator({
    super.zIndex = -1,
    required super.height,
    super.padding = defaultMainIndicatorPadding,

    // 最高价
    required this.high,
    // 最低价
    required this.low,
    // 最后价: 当最新蜡烛不在可视区域时使用.
    required this.last,
    // 最新价: 当最新蜡烛在可视区域时使用.
    required this.latest,
    this.useCandleColorAsLatestBg = true,
    // 倒计时, 在latest最新价之下展示
    this.showCountDown = true,
    required this.countDown,
  });

  // 最高价
  final MarkConfig high;
  // 最低价
  final MarkConfig low;
  // 最后价: 当最新蜡烛不在可视区域时使用.
  final MarkConfig last;
  // 最新价: 当最新蜡烛在可视区域时使用.
  final MarkConfig latest;
  // 使用蜡烛颜色做为Latest的背景
  final bool useCandleColorAsLatestBg;
  // 倒计时, 在latest最新价之下展示
  final bool showCountDown;
  final TextAreaConfig countDown;

  @override
  CandlePaintObject createPaintObject(IPaintContext context) {
    return CandlePaintObject(context: context, indicator: this);
  }

  factory CandleIndicator.fromJson(Map<String, dynamic> json) =>
      _$CandleIndicatorFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$CandleIndicatorToJson(this);
}

class CandlePaintObject<T extends CandleIndicator>
    extends CandleBasePaintObject<T> with PaintYAxisTicksOnCrossMixin {
  CandlePaintObject({
    required super.context,
    required super.indicator,
  });

  BagNum? _maxHigh, _minLow;
  // 用于存储上一次回调的最新价格Y坐标和值，以减少不必要的回调
  double _previousLatestPriceY = -1.0;
  double _previousLatestPriceValue = -1.0;

  @override
  MinMax? initState(int start, int end) {
    if (!klineData.canPaintChart) return null;

    MinMax? minmax = klineData.calculateMinmax(start, end);
    _maxHigh = minmax?.max;
    _minLow = minmax?.min;
    return minmax;
  }

  @override
  void paintChart(Canvas canvas, Size size) {
    /// 绘制蜡烛图
    paintCandleChart(canvas, size);

    /// 绘制价钱刻度数据
    if (settingConfig.showYAxisTick) {
      paintYAxisPriceTick(canvas, size);
    }
  }

  @override
  void paintExtraAboveChart(Canvas canvas, Size size) {
    /// 绘制最新价刻度线与价钱标记
    paintLatestPriceMark(canvas, size);
  }

  @override
  void onCross(Canvas canvas, Offset offset) {
    /// 绘制Cross Y轴价钱刻度
    paintYAxisTicksOnCross(
      canvas,
      offset,
      precision: klineData.precision,
    );
  }

  // onCross时, 格式化Y轴上的标记值.
  @override
  String formatTicksValueOnCross(BagNum value, {required int precision}) {
    return formatPrice(
      value.toDecimal(),
      precision: klineData.precision,
      cutInvalidZero: false,
    );
  }

  /// 绘制蜡烛图
  void paintCandleChart(Canvas canvas, Size size) {
    if (!klineData.canPaintChart) {
      logw('paintCandleChart Data.list is empty or Index is out of bounds');
      return;
    }

    // Width calculation logic - different for volume tracks vs normal candles
    final double visualCandleBodyWidth;
    final double visualCandleBodyHalfWidth;
    final double visualTrackWidth;
    
    if (settingConfig.enableVolumeTracksRendering) {
      // For volume tracks: use proportional width calculation
      visualCandleBodyWidth = this.candleWidth * settingConfig.candleBodyProportion;
      visualCandleBodyHalfWidth = visualCandleBodyWidth / 2.0;
      visualTrackWidth = visualCandleBodyWidth * settingConfig.trackWidthMultiplierToBody;
    } else {
      // For normal candles: use full candleWidth (now automatically returns normalCandleWidth)
      visualCandleBodyWidth = this.candleWidth;
      visualCandleBodyHalfWidth = visualCandleBodyWidth / 2.0;
      visualTrackWidth = 0.0; // Not used for normal candles
    }

    // Define paint for the bottom separator line
    final Paint separatorLinePaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    // Define font properties as constants/variables for direct use
    const double _volumeTextFontSize = 10.0;
    const ui.FontWeight _volumeTextFontWeight = ui.FontWeight.normal;
    // const String? _volumeTextFontFamily = null; // Only if you have a specific font family name

    const double _totalVolumeTextFontSize = 10.0;
    const ui.FontWeight _totalVolumeTextFontWeight = ui.FontWeight.normal;
    // const String? _totalVolumeTextFontFamily = null; // Only if you have a specific font family name

    // This TextStyle is pushed to ParagraphBuilder, so its properties are used directly by the builder
    final ui.TextStyle volumeTextStyle = ui.TextStyle(
      color: Colors.white,
      fontSize: _volumeTextFontSize,
      fontWeight: _volumeTextFontWeight,
      // fontFamily: _volumeTextFontFamily, 
    );

    // Ensure totalVolumeTextStyle is defined for use with Delta and overall Total text
    final ui.TextStyle totalVolumeTextStyle = ui.TextStyle(
        color: Colors.white, 
        fontSize: _totalVolumeTextFontSize,
        fontWeight: _totalVolumeTextFontWeight,
    );

    final list = klineData.list;
    int start = klineData.start;
    int end = klineData.end;

    final offset = startCandleDx; 

    Offset? maxHihgOffset, minLowOffset;
    bool hasEnough = paintDxOffset > 0;
    BagNum maxHigh = list[start].high;
    BagNum minLow = list[start].low;
    CandleModel m;
    for (var i = start; i < end; i++) {
      m = list[i];
      final dx = offset - (i - start) * candleActualWidth; // candleActualWidth = this.candleWidth + this.candleSpacing
      final isLong = m.close >= m.open;

      final highOff = Offset(dx, valueToDy(m.high));
      final lowOff = Offset(dx, valueToDy(m.low));
      canvas.drawLine(
        highOff,
        lowOff,
        isLong ? defLongLinePaint : defShortLinePaint,
      );

      final openOff = Offset(dx, valueToDy(m.open));
      final closeOff = Offset(dx, valueToDy(m.close));

      if (settingConfig.enableVolumeTracksRendering) {
        // For volume tracks: use custom paint with proportional width
        final Paint originalPaint = isLong ? defLongBarPaint : defShortBarPaint;
        final Paint candleBodyPaint = Paint()
          ..color = originalPaint.color
          ..style = originalPaint.style
          ..strokeCap = originalPaint.strokeCap
          ..strokeWidth = visualCandleBodyWidth;

        canvas.drawLine(
          openOff,
          closeOff,
          candleBodyPaint,
        );
      } else {
        // For normal candles: use original FlexiKline drawing method
        canvas.drawLine(
          openOff,
          closeOff,
          isLong ? defLongBarPaint : defShortBarPaint,
        );
      }

      // 检查是否启用成交量轨迹绘制
      if (settingConfig.enableVolumeTracksRendering && m.priceLevel != null && m.priceLevel!.isNotEmpty) {
        final double graphicOffset = settingConfig.priceLevelGraphicOffset;
        final double actualEntityLeftEdgeX = dx - visualCandleBodyHalfWidth;
        final double actualEntityRightEdgeX = dx + visualCandleBodyHalfWidth;

        // --- 绘制卖单轨迹和其总成交量 ---
        double sellTracksBottomY = 0; // 用于记录卖单轨迹最低点，以在其下方绘制总数
        if (m.priceLevel!.sellLevels != null && m.priceLevel!.sellLevels!.isNotEmpty) {
          final Paint sellPaint = Paint()..color = settingConfig.sellTrackColor;
          for (PriceLevelEntry entry in m.priceLevel!.sellLevels!) {
            double priceMin = entry.priceMin;
            double priceMax = entry.priceMax;
            double volume = entry.volume;
            double levelYTop = valueToDy(BagNum.fromNum(priceMax));
            double levelYBottom = valueToDy(BagNum.fromNum(priceMin));
            if (levelYTop > levelYBottom) { double temp = levelYTop; levelYTop = levelYBottom; levelYBottom = temp; }
            double rectRightBound = actualEntityLeftEdgeX - graphicOffset;
            double rectLeftBound = rectRightBound - visualTrackWidth;
            final Rect clippingRect = chartRect;
            double clippedRectLeft = math.max(rectLeftBound, clippingRect.left);
            double clippedRectRight = math.min(rectRightBound, clippingRect.right);
            double clippedLevelYTop = math.max(levelYTop, clippingRect.top);
            double clippedLevelYBottom = math.min(levelYBottom, clippingRect.bottom);
            if (clippedRectLeft < clippedRectRight && clippedLevelYTop < clippedLevelYBottom) {
              Rect priceLevelRect = Rect.fromLTRB(clippedRectLeft, clippedLevelYTop, clippedRectRight, clippedLevelYBottom);
              canvas.drawRect(priceLevelRect, sellPaint);

              // Draw bottom separator line for this specific sell price level block
              if (clippedLevelYBottom < chartRect.bottom && clippedLevelYBottom > chartRect.top) {
                canvas.drawLine(
                    Offset(clippedRectLeft, clippedLevelYBottom),
                    Offset(clippedRectRight, clippedLevelYBottom),
                    separatorLinePaint);
              }

              // 绘制区间成交量文本 for sellLevels
              if (priceLevelRect.width > 5 && priceLevelRect.height > 5) {
                final String volumeText = volume.toStringAsFixed(0);
                final ui.ParagraphBuilder pb = ui.ParagraphBuilder(ui.ParagraphStyle(
                  textAlign: TextAlign.center,
                  // No font properties here, pushStyle handles it
                ));
                pb.pushStyle(volumeTextStyle); // volumeTextStyle already has all props
                pb.addText(volumeText);
                final ui.Paragraph paragraph = pb.build();
                paragraph.layout(ui.ParagraphConstraints(width: priceLevelRect.width));
                final Offset textOffset = Offset(
                  priceLevelRect.left + (priceLevelRect.width - paragraph.width) / 2,
                  priceLevelRect.top + (priceLevelRect.height - paragraph.height) / 2,
                );
                canvas.drawParagraph(paragraph, textOffset);
              }

              // Update sellTracksBottomY
              if (levelYBottom > sellTracksBottomY) sellTracksBottomY = levelYBottom;
            }
          }

          // 绘制总卖出成交量 (在卖单轨迹区域下方)
          if (m.priceLevel!.totalSellVolume != null && m.priceLevel!.totalSellVolume! > 0) {
            final String totalSellText = m.priceLevel!.totalSellVolume!.toStringAsFixed(0);
            final ui.ParagraphBuilder pbSell = ui.ParagraphBuilder(ui.ParagraphStyle(
              textAlign: TextAlign.center,
            ));
            pbSell.pushStyle(ui.TextStyle(
              color: settingConfig.sellTrackColor,
              fontSize: _totalVolumeTextFontSize,
              fontWeight: _totalVolumeTextFontWeight,
            ));
            pbSell.addText(totalSellText);
            final ui.Paragraph pSell = pbSell.build();
            pSell.layout(ui.ParagraphConstraints(width: visualTrackWidth));
            double sellTotalTextX = actualEntityLeftEdgeX - graphicOffset - visualTrackWidth / 2 - pSell.width / 2;
            double sellTotalTextY = sellTracksBottomY +10.0;
            sellTotalTextY = math.min(sellTotalTextY, chartRect.bottom - (_totalVolumeTextFontSize) - 2);
            canvas.drawParagraph(pSell, Offset(sellTotalTextX, sellTotalTextY));
          }
        }

        // --- 绘制买单轨迹和其总成交量 ---
        double buyTracksBottomY = 0; // 用于记录买单轨迹最低点
        if (m.priceLevel!.buyLevels != null && m.priceLevel!.buyLevels!.isNotEmpty) {
          final Paint buyPaint = Paint()..color = settingConfig.buyTrackColor;
          for (PriceLevelEntry entry in m.priceLevel!.buyLevels!) {
            double priceMin = entry.priceMin;
            double priceMax = entry.priceMax;
            double volume = entry.volume;
            double levelYTop = valueToDy(BagNum.fromNum(priceMax));
            double levelYBottom = valueToDy(BagNum.fromNum(priceMin));
            if (levelYTop > levelYBottom) { double temp = levelYTop; levelYTop = levelYBottom; levelYBottom = temp; }
            double rectLeftBound = actualEntityRightEdgeX + graphicOffset;
            double rectRightBound = rectLeftBound + visualTrackWidth;
            final Rect clippingRect = chartRect;
            double clippedRectLeft = math.max(rectLeftBound, clippingRect.left);
            double clippedRectRight = math.min(rectRightBound, clippingRect.right);
            double clippedLevelYTop = math.max(levelYTop, clippingRect.top);
            double clippedLevelYBottom = math.min(levelYBottom, clippingRect.bottom);
            if (clippedRectLeft < clippedRectRight && clippedLevelYTop < clippedLevelYBottom) {
                Rect priceLevelRect = Rect.fromLTRB(clippedRectLeft, clippedLevelYTop, clippedRectRight, clippedLevelYBottom);
                canvas.drawRect(priceLevelRect, buyPaint);

                // Draw bottom separator line for this specific buy price level block
                if (clippedLevelYBottom < chartRect.bottom && clippedLevelYBottom > chartRect.top) {
                    canvas.drawLine(
                        Offset(clippedRectLeft, clippedLevelYBottom),
                        Offset(clippedRectRight, clippedLevelYBottom),
                        separatorLinePaint);
                }

                // 绘制区间成交量文本 for buyLevels
                if (priceLevelRect.width > 5 && priceLevelRect.height > 5) {
                  final String volumeText = volume.toStringAsFixed(0);
                  final ui.ParagraphBuilder pb = ui.ParagraphBuilder(ui.ParagraphStyle(
                    textAlign: TextAlign.center,
                    // No font properties here, pushStyle handles it
                  ));
                  pb.pushStyle(volumeTextStyle); // volumeTextStyle already has all props
                  pb.addText(volumeText);
                  final ui.Paragraph paragraph = pb.build();
                  paragraph.layout(ui.ParagraphConstraints(width: priceLevelRect.width));
                  final Offset textOffset = Offset(
                    priceLevelRect.left + (priceLevelRect.width - paragraph.width) / 2,
                    priceLevelRect.top + (priceLevelRect.height - paragraph.height) / 2,
                  );
                  canvas.drawParagraph(paragraph, textOffset);
                }

                // Update buyTracksBottomY
                if (levelYBottom > buyTracksBottomY) buyTracksBottomY = levelYBottom;
            }
          }

          // 绘制总买入成交量 (在买单轨迹区域下方)
          if (m.priceLevel!.totalBuyVolume != null && m.priceLevel!.totalBuyVolume! > 0) {
            final String totalBuyText = m.priceLevel!.totalBuyVolume!.toStringAsFixed(0);
            final ui.ParagraphBuilder pbBuy = ui.ParagraphBuilder(ui.ParagraphStyle(
              textAlign: TextAlign.center,
            ));
            pbBuy.pushStyle(ui.TextStyle(
              color: settingConfig.buyTrackColor,
              fontSize: _totalVolumeTextFontSize,
              fontWeight: _totalVolumeTextFontWeight,
            ));
            pbBuy.addText(totalBuyText);
            final ui.Paragraph pBuy = pbBuy.build();
            pBuy.layout(ui.ParagraphConstraints(width: visualTrackWidth));
            double buyTotalTextX = actualEntityRightEdgeX + graphicOffset + visualTrackWidth / 2 - pBuy.width / 2;
            double buyTotalTextY = buyTracksBottomY + 10.0;
            buyTotalTextY = math.min(buyTotalTextY, chartRect.bottom - (_totalVolumeTextFontSize) - 2);
            canvas.drawParagraph(pBuy, Offset(buyTotalTextX, buyTotalTextY));
          }
        }

        // --- 绘制K线下方的 Delta 和 总成交量 ---
        double candleBodyBottomY = valueToDy(m.low);
        double yPosForDelta = candleBodyBottomY + 5.0; 
        // 为两行文本（Delta 和 Total）及其间距留出估算高度
        double estimatedTwoLinesHeight = (_totalVolumeTextFontSize * 2) + 5.0 /*间距*/ + 2.0 /*底部padding*/;
        yPosForDelta = math.min(yPosForDelta, chartRect.bottom - estimatedTwoLinesHeight);

        // 计算文本布局的总宽度
        double textBlockLayoutWidth = (visualTrackWidth * 2) + (settingConfig.priceLevelGraphicOffset * 2) + visualCandleBodyWidth;

        // 1. Delta 值 (BuyVol - SellVol)
        double sellVol = m.priceLevel!.totalSellVolume ?? 0;
        double buyVol = m.priceLevel!.totalBuyVolume ?? 0;
        double delta = buyVol - sellVol;
        final String deltaText = "D: ${delta.toStringAsFixed(0)}";
        final ui.ParagraphBuilder pbDelta = ui.ParagraphBuilder(ui.ParagraphStyle(textAlign: TextAlign.center));
        pbDelta.pushStyle(totalVolumeTextStyle); 
        pbDelta.addText(deltaText);
        final ui.Paragraph pDelta = pbDelta.build();
        pDelta.layout(ui.ParagraphConstraints(width: textBlockLayoutWidth)); // 使用计算的总布局宽度
        double deltaTextX = dx - pDelta.width / 2; // 仍然相对于K线中心dx居中
        canvas.drawParagraph(pDelta, Offset(deltaTextX, yPosForDelta));

        // 2. 总成交量 (BuyVol + SellVol)
        double totalVolumeOverall = buyVol + sellVol;
        final String totalOverallText = "T: ${totalVolumeOverall.toStringAsFixed(0)}"; // "Total: "保留
        final ui.ParagraphBuilder pbTotal = ui.ParagraphBuilder(ui.ParagraphStyle(textAlign: TextAlign.center));
        pbTotal.pushStyle(totalVolumeTextStyle);
        pbTotal.addText(totalOverallText);
        final ui.Paragraph pTotal = pbTotal.build();
        pTotal.layout(ui.ParagraphConstraints(width: textBlockLayoutWidth)); // 使用计算的总布局宽度
        double totalOverallTextX = dx - pTotal.width / 2; // 仍然相对于K线中心dx居中
        double yPosForTotalOverall = yPosForDelta + _totalVolumeTextFontSize + 10.0; 
        // yPosForTotalOverall 的边界检查已在 yPosForDelta 中考虑 estimatedTwoLinesHeight 时处理，这里确保它不超过底部
        yPosForTotalOverall = math.min(yPosForTotalOverall, chartRect.bottom - _totalVolumeTextFontSize - 2.0);

        canvas.drawParagraph(pTotal, Offset(totalOverallTextX, yPosForTotalOverall));
      }

      if (indicator.high.show) {
        if (hasEnough) {
          // 满足一屏, 根据initData中的最大最小值来记录最大最小偏移量.
          if (m.high == _maxHigh) {
            maxHihgOffset = highOff;
            maxHigh = _maxHigh!;
          }
        } else if (dx > 0) {
          // 如果当前绘制不足一屏, 最大最小绘制仅限可见区域.
          if (m.high >= maxHigh) {
            maxHihgOffset = highOff;
            maxHigh = m.high;
          }
        }
      }

      if (indicator.low.show) {
        if (hasEnough) {
          // 满足一屏, 根据initData中的最大最小值来记录最大最小偏移量.
          if (m.low == _minLow) {
            minLowOffset = lowOff;
            minLow = _minLow!;
          }
        } else {
          // 如果当前绘制不足一屏, 最大最小绘制仅限可见区域.
          if (m.low <= minLow) {
            minLowOffset = lowOff;
            minLow = m.low;
          }
        }
      }
    }

    // 最后绘制在蜡烛图中的最大价钱标记
    if (maxHihgOffset != null && maxHigh > BagNum.zero) {
      paintPriceMark(canvas, maxHihgOffset, maxHigh, indicator.high);
    }
    // 最后绘制在蜡烛图中的最小价钱标记
    if (minLowOffset != null && minLow > BagNum.zero) {
      paintPriceMark(canvas, minLowOffset, minLow, indicator.low);
    }
  }

  /// 绘制蜡烛图上最大最小值价钱标记.
  void paintPriceMark(
    Canvas canvas,
    Offset offset,
    BagNum val,
    MarkConfig markConfig,
  ) {
    final flag = offset.dx > chartRectWidthHalf ? -1 : 1;
    Offset endOffset = Offset(
      offset.dx + markConfig.lineLength * flag,
      offset.dy,
    );
    canvas.drawLine(
      offset,
      endOffset,
      markConfig.line.of(paintColor: theme.markLine).linePaint,
    );

    final markText = markConfig.text.of(textColor: theme.textColor);

    endOffset = Offset(
      endOffset.dx + flag * markConfig.spacing,
      endOffset.dy - (markText.areaHeight) / 2,
    );

    final text = formatPrice(val.toDecimal(), precision: klineData.precision);

    canvas.drawTextArea(
      offset: endOffset,
      drawDirection: flag < 0 ? DrawDirection.rtl : DrawDirection.ltr,
      text: text,
      textConfig: markText,
    );
  }

  /// 缓存价钱刻度文本区域大小, 用于定位缩放拖拽条区域
  Size? _zoomSlideBarize;

  /// 绘制蜡烛图右侧价钱刻度
  /// 根据Grid horizontal配置来绘制, 保证在grid.horizontal线之上.
  void paintYAxisPriceTick(Canvas canvas, Size size) {
    final dyStep = drawableRect.height / gridConfig.horizontal.count;
    final dx = chartRect.right;
    double dy = 0;
    for (int i = 1; i <= gridConfig.horizontal.count; i++) {
      dy = i * dyStep;
      final price = dyToValue(dy);
      if (price == null) continue;

      final text = formatPrice(
        price.toDecimal(),
        precision: klineData.precision,
        cutInvalidZero: false,
        enableGrouping: true,
      );

      final ticksText = defTicksTextConfig;

      final size = canvas.drawTextArea(
        offset: Offset(
          dx,
          dy - ticksText.areaHeight, // 绘制在刻度线之上
        ),
        drawDirection: DrawDirection.rtl,
        drawableRect: drawableRect,
        text: text,
        textConfig: ticksText,
      );
      if (settingConfig.useCandleTicksAsZoomSlideBar) {
        final newSize = Size(size.width, drawableRect.height);
        if (newSize != _zoomSlideBarize) {
          _zoomSlideBarize = newSize;
          updateZoomSlideBarRect(Rect.fromLTWH(
            drawableRect.right - newSize.width,
            drawableRect.top,
            newSize.width,
            newSize.height,
          ));
        }
      }
    }
  }

  /// 缓存latest文本相对于屏幕右侧的负偏移量
  double _latestTextOffset = 0.0;

  /// 缓存last文本所占大小
  Size? _lastTextSize;

  /// 最后价文本区域位置, 用于后续点击事件命中测试.
  Rect? get lastTextAreaRect {
    if (_lastTextSize == null) return null;
    final model = klineData.latest;
    if (model == null) return null;
    // 计算最新价YAxis位置.
    double dy;
    if (model.close >= minMax.max) {
      dy = drawableRect.top; // 画板顶部展示.
    } else if (model.close <= minMax.min) {
      dy = drawableRect.bottom; // 画板底部展示.
    } else {
      dy = clampDyInChart(valueToDy(model.close));
    }
    return Rect.fromLTWH(
      chartRect.right +
          _latestTextOffset -
          indicator.last.spacing -
          _lastTextSize!.width,
      dy,
      _lastTextSize!.width,
      _lastTextSize!.height,
    );
  }

  /// 绘制最新价刻度线与价钱标记
  /// 1. 价钱标记始终展示在画板最右边.
  /// 2. 最新价向右移出屏幕后, 刻度线横穿整屏.
  ///    且展示在指定价钱区间内, 如超出边界, 则停靠在最高最低线上.
  /// 3. 最新价向左移动后, 刻度线根据最新价蜡烛线平行移动.
  void paintLatestPriceMark(Canvas canvas, Size size) {
    if (!indicator.latest.show || !indicator.last.show) return;
    final model = klineData.latest;
    if (model == null) {
      logd('paintLatestPriceMark > on data!');
      return;
    }

    // 计算最新价YAxis位置.
    double dy = clampDyInChart(valueToDy(model.close));
    // if (model.close >= minMax.max) {
    //   dy = chartRect.top; // 画板顶部展示.
    // } else if (model.close <= minMax.min) {
    //   dy = chartRect.bottom; // 画板底部展示.
    // } else {
    //   dy = clampDyInChart(valueToDy(model.close));
    // }

    // 计算最新价XAxis位置.
    final rdx = chartRect.right;
    double ldx = 0; // 计算最新价刻度线lineTo参数X轴的dx值. 默认0: 代表橫穿整个Canvas.

    if (paintDxOffset < _latestTextOffset) {
      _lastTextSize = null;
      // 绘制最新价和倒计时
      final latest = indicator.latest;
      if (!latest.show) return;

      ldx = startCandleDx;

      TextAreaConfig textConfig;
      if (indicator.useCandleColorAsLatestBg) {
        textConfig = latest.text.copyWith(
          style: latest.text.style.copyWith(color: const Color(0xFFFFFFFF)),
          background: model.close >= model.open ? longColor : shortColor,
          border: BorderSide.none,
        );
      } else {
        textConfig = latest.text.of(
          textColor: theme.textColor,
          background: theme.latestPriceTextBg,
          borderColor: theme.markLine,
        );
      }

      Color? background = textConfig.background;
      BorderRadius? borderRadius = textConfig.borderRadius;

      final halfHeight = textConfig.areaHeight / 2;
      // 修正dy位置
      dy = dy.clamp(
        drawableRect.top + halfHeight,
        drawableRect.bottom - halfHeight,
      );

      /// 绘制首根蜡烛到rdx的刻度线.
      final latestPath = Path();
      latestPath.moveTo(rdx, dy);
      latestPath.lineTo(ldx, dy);
      canvas.drawLineByConfig(
        latestPath,
        latest.line.of(paintColor: theme.markLine),
      );

      /// 最新价文本和样式配置
      final text = formatPrice(
        model.close.toDecimal(),
        precision: klineData.req.precision,
        cutInvalidZero: false,
      );

      /// 倒计时Text
      String? countDownText;
      if (indicator.showCountDown) {
        final nextUpdateDateTime = model.nextUpdateDateTime(klineData.req.bar);
        if (nextUpdateDateTime != null) {
          countDownText = formatTimeDiff(nextUpdateDateTime);
        }
      }
      if (countDownText != null) {
        // 如果展示倒计时, 最新价仅保留顶部radius
        borderRadius = borderRadius?.copyWith(
          topLeft: borderRadius.topLeft,
          topRight: borderRadius.topRight,
          bottomLeft: const Radius.circular(0),
          bottomRight: const Radius.circular(0),
        );
      }

      final offset = Offset(
        rdx - latest.spacing,
        dy - halfHeight, // 垂直居中
      );

      /// 绘制最新价标记
      final size = canvas.drawTextArea(
        offset: offset,
        drawDirection: DrawDirection.rtl,
        drawableRect: drawableRect,
        text: text,
        textConfig: textConfig,
        backgroundColor: background,
        borderRadius: borderRadius,
      );
      _latestTextOffset = -(size.width + latest.spacing);

      if (countDownText != null) {
        TextAreaConfig countDown;
        if (indicator.useCandleColorAsLatestBg) {
          countDown = indicator.countDown.of(
            textColor: theme.textColor,
            background: theme.countDownTextBg,
          );
        } else {
          countDown = indicator.countDown.of(
            textColor: theme.textColor,
            background: theme.countDownTextBg,
            borderColor: theme.markLine,
          );
        }

        // 展示倒计时, 倒计时radius始终使用最新价的, 且保留底部radius
        borderRadius = borderRadius?.copyWith(
          topLeft: const Radius.circular(0),
          topRight: const Radius.circular(0),
          bottomLeft: borderRadius.topLeft,
          bottomRight: borderRadius.topRight,
        );

        /// 绘制倒计时标记
        canvas.drawText(
          offset: Offset(
            offset.dx,
            offset.dy + size.height - 0.5,
          ),
          drawDirection: DrawDirection.rtl,
          drawableRect: drawableRect,
          text: countDownText,
          style: countDown.style.copyWith(
            // 修正倒计时文本区域高度:
            // 由于倒计时使用了固定宽度(最新价的size.width), 保持与最新价同宽.
            // 此处无法再为countDown设置padding, 固在此处修正
            height: (countDown.areaHeight - 1) / countDown.textHeight,
          ),
          textAlign: countDown.textAlign,
          textWidth: size.width,
          // padding: countDown.padding,
          backgroundColor: countDown.background,
          borderRadius: borderRadius,
          borderSide: countDown.border,
          maxLines: countDown.maxLines ?? 1,
        );
      }

      // Invoke the callback with the Y position and price
      if (this.context is FlexiKlineController) {
        final flexiController = this.context as FlexiKlineController;
        final double currentPrice = model.close.toDouble();
        final double currentY = dy; // dy 就是计算出的Y坐标

        // 只有当Y坐标或价格发生实际变化时才触发回调
        // 可以根据需要加入一个小的容差 (epsilon) 来比较 double 值
        bool priceChanged = (currentPrice - _previousLatestPriceValue).abs() > 0.00001; 
        bool yPositionChanged = (currentY - _previousLatestPriceY).abs() > 0.5; 

        if (priceChanged || yPositionChanged) {
          if (flexiController.onLatestPriceYPositionUpdated != null) {
            flexiController.onLatestPriceYPositionUpdated!(currentY, currentPrice);
            _previousLatestPriceY = currentY;
            _previousLatestPriceValue = currentPrice;
          }
        }
      }
    } else {
      /// 绘制最后价
      final last = indicator.last;
      if (!last.show) return;

      ldx = 0;

      final lastText = last.text.of(
        textColor: theme.lastPriceTextColor,
        background: theme.lastPriceTextBg,
      );

      final halfHeight = lastText.areaHeight / 2;
      // 修正dy位置
      dy = dy.clamp(
        drawableRect.top + halfHeight,
        drawableRect.bottom - halfHeight,
      );

      /// 绘制横穿画板的最后价刻度线.
      final lastPath = Path();
      if (_lastTextSize != null) {
        final lastTextAreaRight = rdx + _latestTextOffset - last.spacing;
        lastPath.moveTo(rdx, dy);
        lastPath.lineTo(lastTextAreaRight, dy);
        lastPath.moveTo(lastTextAreaRight - _lastTextSize!.width, dy);
        lastPath.lineTo(ldx, dy);
      } else {
        lastPath.moveTo(rdx, dy);
        lastPath.lineTo(ldx, dy);
      }
      canvas.drawLineByConfig(
        lastPath,
        last.line.of(paintColor: theme.markLine),
      );

      final text = formatPrice(
        model.close.toDecimal(),
        precision: klineData.precision,
        cutInvalidZero: true,
      );

      /// 绘制最后价标记
      _lastTextSize = canvas.drawTextArea(
        offset: Offset(
          rdx + _latestTextOffset - last.spacing,
          dy - halfHeight, // 居中
        ),
        drawDirection: DrawDirection.rtl,
        drawableRect: drawableRect,
        text: '$text ▸', // ➤➤▹►▸▶︎≻
        textConfig: lastText,
      );

      // Invoke the callback with the Y position and price
      if (this.context is FlexiKlineController) {
        final flexiController = this.context as FlexiKlineController;
        final double currentPrice = model.close.toDouble();
        final double currentY = dy; // dy 就是计算出的Y坐标

        // 同样应用变化检查逻辑
        bool priceChanged = (currentPrice - _previousLatestPriceValue).abs() > 0.00001;
        bool yPositionChanged = (currentY - _previousLatestPriceY).abs() > 0.5;

        if (priceChanged || yPositionChanged) {
          if (flexiController.onLatestPriceYPositionUpdated != null) {
            flexiController.onLatestPriceYPositionUpdated!(currentY, currentPrice);
            _previousLatestPriceY = currentY;
            _previousLatestPriceValue = currentPrice;
          }
        }
      }
    }
  }

  @override
  Size? paintTips(
    Canvas canvas, {
    CandleModel? model,
    Offset? offset,
    Rect? tipsRect,
  }) {
    return null;
  }

  @override
  bool handleTap(Offset position) {
    final lastTxtRect = lastTextAreaRect?.inflate(indicator.last.hitTestMargin);
    if (lastTxtRect != null && lastTxtRect.include(position)) {
      // 命中最后价区域, 此时应该移动到蜡烛图初始位置
      moveToInitialPosition();
      return true;
    }
    return false;
  }
}
