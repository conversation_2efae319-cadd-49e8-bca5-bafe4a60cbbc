// Copyright 2024 Andy<PERSON>Zhao
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

part of 'time.dart';

/// 时间刻度指标图
@CopyWith()
@FlexiIndicatorSerializable
class TimeIndicator extends TimeBaseIndicator {
  TimeIndicator({
    super.zIndex = 0,
    super.height = defaultTimeIndicatorHeight,
    super.padding = EdgeInsets.zero,
    super.position = DrawPosition.middle,
    // 时间刻度.
    required this.timeTick,
  });

  // 时间刻度.
  final TextAreaConfig timeTick;

  @override
  TimePaintObject createPaintObject(IPaintContext context) {
    return TimePaintObject(context: context, indicator: this);
  }

  factory TimeIndicator.fromJson(Map<String, dynamic> json) =>
      _$TimeIndicatorFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$TimeIndicatorToJson(this);
}

class TimePaintObject<T extends TimeIndicator> extends TimeBasePaintObject<T> {
  TimePaintObject({
    required super.context,
    required super.indicator,
  });

  /// 两个时间刻度间隔的蜡烛数
  int get timeTickIntervalCount {
    // 获取配置的文本宽度，如果为null则使用一个默认估计值，例如60或80
    // 这个值应该大致代表一个时间标签（如 "HH:mm" 或 "MM/DD"）的像素宽度
    double estimatedTextPixelWidth = indicator.timeTick.textWidth ?? 70.0; // 假设默认70像素
    
    // 确保 estimatedTextPixelWidth 不小于一个最小值，防止除以过小的数
    estimatedTextPixelWidth = math.max(40.0, estimatedTextPixelWidth); 

    // 计算需要多少根K线的宽度才能容纳一个时间标签而不重叠
    // 如果 candleActualWidth 小于等于0，会导致除零或负数，所以要处理
    if (candleActualWidth <= 0) return 1000; // 或者一个很大的数，避免绘制

    int calculatedInterval = (estimatedTextPixelWidth / candleActualWidth).ceil();
    
    // 确保间隔至少为1。为了避免过于密集，可以设置一个最小间隔，例如2或3。
    // 如果K线很窄，calculatedInterval可能会很大。如果K线很宽，calculatedInterval可能为1。
    // 我们希望在K线较宽时，也不要每根都画，除非文本确实很短。
    int interval = math.max(1, calculatedInterval);
    
    // 增加一个调试打印，观察这些值的变化
    // print('[TimePaintObject] timeTick.textWidth: ${indicator.timeTick.textWidth}, candleActualWidth: $candleActualWidth, estimatedTextPixelWidth: $estimatedTextPixelWidth, calculatedInterval: $calculatedInterval, final interval: $interval');
    
    // 返回最终的间隔。可以根据实际效果调整这里的逻辑，比如增加一个最小间隔限制
    // 例如，如果希望标签间至少空出半个标签的宽度：
    // interval = (estimatedTextPixelWidth * 1.5 / candleActualWidth).ceil().clamp(1, 1000).toInt();
    // 为了解决当前过于密集的问题，我们先强制一个最小间隔，比如2或3
    return math.max(2, interval); // 强制最小间隔为2 (即每隔1根K线绘制，或者更稀疏)
  }

  @override
  MinMax? initState(int start, int end) {
    return null;
  }

  @override
  void paintChart(Canvas canvas, Size size) {
    final data = klineData;
    if (data.list.isEmpty) return;
    int start = data.start;
    int end = data.end;

    final offset = startCandleDx - candleWidthHalf;
    final bar = data.timeBar;

    for (var i = start; i < end; i++) {
      final model = data.list[i];
      final dx = offset - (i - start) * candleActualWidth;
      if (bar != null && i % timeTickIntervalCount == 0) {
        final offset = Offset(dx, chartRect.top);
        // 绘制时间刻度.
        final timeTick = indicator.timeTick.of(
          textColor: theme.ticksTextColor,
        );
        final dyCenterOffset = (height - timeTick.areaHeight) / 2;
        canvas.drawTextArea(
          offset: Offset(
            offset.dx,
            offset.dy + dyCenterOffset,
          ),
          drawDirection: DrawDirection.center,
          text: model.formatDateTime(bar),
          textConfig: timeTick,
        );
      }
    }
  }

  @override
  void onCross(Canvas canvas, Offset offset) {
    final model = offsetToCandle(offset);
    final timeBar = klineData.timeBar;
    if (model == null || timeBar == null) return;

    final time = model.formatDateTime(timeBar);
    // final time = formatyyMMddHHMMss(model.dateTime);

    final ticksText = crossConfig.ticksText.of(
      textColor: theme.crossTextColor,
      background: theme.crossTextBg,
    );

    final dyCenterOffset = (height - ticksText.areaHeight) / 2;
    canvas.drawTextArea(
      offset: Offset(
        offset.dx,
        chartRect.top + dyCenterOffset,
      ),
      drawDirection: DrawDirection.center,
      text: time,
      textConfig: ticksText,
    );
  }

  @override
  Size? paintTips(
    Canvas canvas, {
    CandleModel? model,
    Offset? offset,
    Rect? tipsRect,
  }) {
    return null;
  }
}
