// Copyright 2024 Andy<PERSON>Zhao
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

part of 'volume_profile.dart';

/// 线段样式
enum VolumeProfileLineStyle {
  /// 实线
  solid,

  /// 点状
  dashed,
}

/// Volume Profile 显示模式
enum VolumeProfileDisplayMode {
  /// 涨跌分别显示
  bidAsk,

  /// 总量显示
  total,

  /// 涨跌叠加显示
  overlay,
}

/// Volume Profile 指标配置
@CopyWith()
class VolumeProfileConfig {
  const VolumeProfileConfig({
    // 线条显示开关
    this.showPOC = true,
    this.showVAH = true,
    this.showVAL = true,

    // 线条颜色配置
    this.pocColor = Colors.yellow,
    this.vahColor = Colors.blue,
    this.valColor = Colors.blue,

    // 线条样式配置
    this.pocLineStyle = VolumeProfileLineStyle.solid,
    this.vahLineStyle = VolumeProfileLineStyle.solid,
    this.valLineStyle = VolumeProfileLineStyle.solid,

    // 柱状图显示模式
    this.displayMode = VolumeProfileDisplayMode.bidAsk,

    // 柱状图颜色配置
    this.upVolumeColor = const Color(0xFFFF5252), // 红色 - 可通过模板样式覆盖
    this.downVolumeColor = const Color(0xFF4CAF50), // 绿色 - 可通过模板样式覆盖
    this.totalVolumeColor = const Color(0xFF2196F3), // 蓝色 - 可通过模板样式覆盖
    // 线条样式配置
    this.pocLineWidth = 1.5,
    this.vahLineWidth = 1.0,
    this.valLineWidth = 1.0,

    // 透明度配置
    this.volumeOpacity = 0.8,
    this.lineOpacity = 1.0,

    // 价格区间配置
    this.priceZones = 100, // 默认分100个价格区间
    // Value Area 配置
    this.valueAreaPercentage = 0.68, // Value Area 覆盖68%的成交量
    // 绘制参数配置
    this.maxWidthPercent = 0.3, // 最大宽度百分比
    // 数据平滑配置
    this.smoothingStrength = 0.6, // 平滑强度 (0.0-1.0)，0为不平滑，1为最大平滑
  });

  // 线条显示开关
  final bool showPOC; // Point of Control
  final bool showVAH; // Value Area High
  final bool showVAL; // Value Area Low

  // 线条颜色配置
  final Color pocColor;
  final Color vahColor;
  final Color valColor;

  // 线条样式配置
  final VolumeProfileLineStyle pocLineStyle;
  final VolumeProfileLineStyle vahLineStyle;
  final VolumeProfileLineStyle valLineStyle;

  // 柱状图显示模式
  final VolumeProfileDisplayMode displayMode;

  // 柱状图颜色配置
  final Color upVolumeColor; // 上涨成交量颜色
  final Color downVolumeColor; // 下跌成交量颜色
  final Color totalVolumeColor; // 总成交量颜色

  // 线条样式配置
  final double pocLineWidth;
  final double vahLineWidth;
  final double valLineWidth;

  // 透明度配置
  final double volumeOpacity;
  final double lineOpacity;

  // 价格区间配置
  final int priceZones;

  // Value Area 配置
  final double valueAreaPercentage;

  // 绘制参数配置
  final double maxWidthPercent;

  // 数据平滑配置
  final double smoothingStrength;

  VolumeProfileConfig copyWith({
    bool? showPOC,
    bool? showVAH,
    bool? showVAL,
    Color? pocColor,
    Color? vahColor,
    Color? valColor,
    VolumeProfileLineStyle? pocLineStyle,
    VolumeProfileLineStyle? vahLineStyle,
    VolumeProfileLineStyle? valLineStyle,
    VolumeProfileDisplayMode? displayMode,
    Color? upVolumeColor,
    Color? downVolumeColor,
    Color? totalVolumeColor,
    double? pocLineWidth,
    double? vahLineWidth,
    double? valLineWidth,
    double? volumeOpacity,
    double? lineOpacity,
    int? priceZones,
    double? valueAreaPercentage,
    double? maxWidthPercent,
    double? smoothingStrength,
  }) {
    return VolumeProfileConfig(
      showPOC: showPOC ?? this.showPOC,
      showVAH: showVAH ?? this.showVAH,
      showVAL: showVAL ?? this.showVAL,
      pocColor: pocColor ?? this.pocColor,
      vahColor: vahColor ?? this.vahColor,
      valColor: valColor ?? this.valColor,
      pocLineStyle: pocLineStyle ?? this.pocLineStyle,
      vahLineStyle: vahLineStyle ?? this.vahLineStyle,
      valLineStyle: valLineStyle ?? this.valLineStyle,
      displayMode: displayMode ?? this.displayMode,
      upVolumeColor: upVolumeColor ?? this.upVolumeColor,
      downVolumeColor: downVolumeColor ?? this.downVolumeColor,
      totalVolumeColor: totalVolumeColor ?? this.totalVolumeColor,
      pocLineWidth: pocLineWidth ?? this.pocLineWidth,
      vahLineWidth: vahLineWidth ?? this.vahLineWidth,
      valLineWidth: valLineWidth ?? this.valLineWidth,
      volumeOpacity: volumeOpacity ?? this.volumeOpacity,
      lineOpacity: lineOpacity ?? this.lineOpacity,
      priceZones: priceZones ?? this.priceZones,
      valueAreaPercentage: valueAreaPercentage ?? this.valueAreaPercentage,
      maxWidthPercent: maxWidthPercent ?? this.maxWidthPercent,
      smoothingStrength: smoothingStrength ?? this.smoothingStrength,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'showPOC': showPOC,
      'showVAH': showVAH,
      'showVAL': showVAL,
      'pocColor': pocColor.value,
      'vahColor': vahColor.value,
      'valColor': valColor.value,
      'pocLineStyle': pocLineStyle.index,
      'vahLineStyle': vahLineStyle.index,
      'valLineStyle': valLineStyle.index,
      'displayMode': displayMode.index,
      'upVolumeColor': upVolumeColor.value,
      'downVolumeColor': downVolumeColor.value,
      'totalVolumeColor': totalVolumeColor.value,
      'pocLineWidth': pocLineWidth,
      'vahLineWidth': vahLineWidth,
      'valLineWidth': valLineWidth,
      'volumeOpacity': volumeOpacity,
      'lineOpacity': lineOpacity,
      'priceZones': priceZones,
      'valueAreaPercentage': valueAreaPercentage,
      'maxWidthPercent': maxWidthPercent,
      'smoothingStrength': smoothingStrength,
    };
  }

  factory VolumeProfileConfig.fromJson(Map<String, dynamic> json) {
    return VolumeProfileConfig(
      showPOC: json['showPOC'] ?? true,
      showVAH: json['showVAH'] ?? true,
      showVAL: json['showVAL'] ?? true,
      pocColor: Color(json['pocColor'] ?? Colors.yellow.value),
      vahColor: Color(json['vahColor'] ?? Colors.blue.value),
      valColor: Color(json['valColor'] ?? Colors.blue.value),
      pocLineStyle: VolumeProfileLineStyle.values[json['pocLineStyle'] ?? 0],
      vahLineStyle: VolumeProfileLineStyle.values[json['vahLineStyle'] ?? 0],
      valLineStyle: VolumeProfileLineStyle.values[json['valLineStyle'] ?? 0],
      displayMode: VolumeProfileDisplayMode.values[json['displayMode'] ?? 0],
      upVolumeColor: Color(
        json['upVolumeColor'] ?? const Color(0xFFFF5252).value,
      ),
      downVolumeColor: Color(
        json['downVolumeColor'] ?? const Color(0xFF4CAF50).value,
      ),
      totalVolumeColor: Color(
        json['totalVolumeColor'] ?? const Color(0xFF2196F3).value,
      ),
      pocLineWidth: json['pocLineWidth']?.toDouble() ?? 1.5,
      vahLineWidth: json['vahLineWidth']?.toDouble() ?? 1.0,
      valLineWidth: json['valLineWidth']?.toDouble() ?? 1.0,
      volumeOpacity: json['volumeOpacity']?.toDouble() ?? 0.8,
      lineOpacity: json['lineOpacity']?.toDouble() ?? 1.0,
      priceZones: json['priceZones'] ?? 100,
      valueAreaPercentage: json['valueAreaPercentage']?.toDouble() ?? 0.68,
      maxWidthPercent: json['maxWidthPercent']?.toDouble() ?? 0.3,
      smoothingStrength: json['smoothingStrength']?.toDouble() ?? 0.6,
    );
  }
}
