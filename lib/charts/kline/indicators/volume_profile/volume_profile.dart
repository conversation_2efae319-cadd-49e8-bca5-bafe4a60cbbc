// Copyright 2024 <PERSON><PERSON>Zhao
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

library;

import 'dart:math' as math;
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/material.dart';

import '../../kline_controller.dart';
import '../../config/export.dart';
import '../../constant.dart';
import '../../core/core.dart';
import '../../extension/export.dart';
import '../../model/export.dart';
import '../../utils/export.dart';
import '../../framework/export.dart';

part 'volume_profile.g.dart';
part 'indicator.dart';
part 'volume_profile_config.dart';
part 'volume_profile_data.dart'; 