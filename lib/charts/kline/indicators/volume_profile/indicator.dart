// Copyright 2024 Andy<PERSON>Zhao
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

part of 'volume_profile.dart';

/// Volume Profile 指标
@CopyWith()
@FlexiIndicatorSerializable
class VolumeProfileIndicator extends PaintObjectIndicator {
  VolumeProfileIndicator({
    super.zIndex = 0,
    required super.height,
    super.padding = defaultMainIndicatorPadding,
    this.config = const VolumeProfileConfig(),
  }) : super(key: const FlexiIndicatorKey('volume_profile', label: 'Volume Profile'));

  /// 指标配置
  final VolumeProfileConfig config;

  @override
  dynamic get calcParam => {
    'priceZones': config.priceZones,
    'valueAreaPercentage': config.valueAreaPercentage,
  };

  @override
  VolumeProfilePaintObject createPaintObject(IPaintContext context) {
    return VolumeProfilePaintObject(context: context, indicator: this);
  }

  factory VolumeProfileIndicator.fromJson(Map<String, dynamic> json) =>
      _$VolumeProfileIndicatorFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$VolumeProfileIndicatorToJson(this);
}

/// Volume Profile 绘制对象
class VolumeProfilePaintObject<T extends VolumeProfileIndicator>
    extends PaintObjectBox<T> with PaintYAxisTicksOnCrossMixin {
  VolumeProfilePaintObject({
    required super.context,
    required super.indicator,
  });

  // 外部数据存储
  static Map<String, dynamic>? _externalData;
  
  /// 设置外部数据（从VolumeProfileChartManager传入）
  static void setExternalData(Map<String, dynamic> data) {
    _externalData = data;
  }
  
  /// 获取外部数据
  static Map<String, dynamic>? getExternalData() {
    return _externalData;
  }

  /// 清理外部数据（币种切换时使用）
  static void clearExternalData() {
    _externalData = null;
  }

  @override
  MinMax? initState(int start, int end) {
    if (!klineData.canPaintChart) return null;

    // 从外部数据获取价格范围
    final data = getExternalData();
    if (data == null) return null;

    try {
      // 获取买卖成交量分布数据
      final Map<double, double> buyData = Map<double, double>.from(data['buy_data'] ?? {});
      final Map<double, double> sellData = Map<double, double>.from(data['sell_data'] ?? {});
      
      if (buyData.isEmpty && sellData.isEmpty) return null;

      // 计算价格范围
      final Set<double> allPrices = {...buyData.keys, ...sellData.keys};
      if (allPrices.isEmpty) return null;

      final minPrice = allPrices.reduce((a, b) => a < b ? a : b);
      final maxPrice = allPrices.reduce((a, b) => a > b ? a : b);

      return MinMax(
        min: BagNum.fromNum(minPrice),
        max: BagNum.fromNum(maxPrice),
      );
    } catch (e) {
      debugPrint('Volume Profile initState 错误: $e');
      return null;
    }
  }

  @override
  void paintChart(Canvas canvas, Size size) {
    final data = getExternalData();
    if (data == null) return;

    final config = indicator.config;
    
    try {
      // 获取数据
      final Map<double, double> buyData = Map<double, double>.from(data['buy_data'] ?? {});
      final Map<double, double> sellData = Map<double, double>.from(data['sell_data'] ?? {});
      final Map<double, double> totalData = Map<double, double>.from(data['render_data'] ?? {});
      
      if (totalData.isEmpty) return;

      // 获取POC/VAL/VAH数据
      final double poc = data['poc']?.toDouble() ?? 0.0;
      final double val = data['val']?.toDouble() ?? 0.0;
      final double vah = data['vah']?.toDouble() ?? 0.0;

      // 获取显示位置配置（从外部数据中获取，这个配置来自业务层）
      final String position = data['position']?.toString() ?? 'right';
      final bool isLeftSide = position == 'left';

      // 绘制成交量柱状图
      _paintVolumeProfile(canvas, size, buyData, sellData, totalData, isLeftSide);
      
      // 绘制POC线
      if (config.showPOC && poc > 0) {
        _paintPOCLine(canvas, size, poc);
      }
      
      // 绘制VAH线
      if (config.showVAH && vah > 0) {
        _paintVAHLine(canvas, size, vah);
      }
      
      // 绘制VAL线
      if (config.showVAL && val > 0) {
        _paintVALLine(canvas, size, val);
      }
      
    } catch (e) {
      debugPrint('Volume Profile paintChart 错误: $e');
    }
  }

  /// 绘制Volume Profile柱状图（横向）
  void _paintVolumeProfile(
    Canvas canvas, 
    Size size, 
    Map<double, double> buyData, 
    Map<double, double> sellData, 
    Map<double, double> totalData,
    bool isLeftSide,
  ) {
    if (totalData.isEmpty) return;

    final data = getExternalData();
    if (data == null) return;

    // 计算最大成交量和最小成交量用于平滑处理
    final maxVolume = totalData.values.reduce((a, b) => a > b ? a : b);
    final minVolume = totalData.values.reduce((a, b) => a < b ? a : b);
    if (maxVolume <= 0) return;

    // 计算柱状图的最大宽度（占图表宽度的比例）
    final maxBarWidth = size.width * indicator.config.maxWidthPercent;
    
    for (final entry in totalData.entries) {
      final price = entry.key;
      final totalVolume = entry.value;
      
      if (totalVolume <= 0) continue;

      // 计算价格对应的Y坐标
      final y = valueToDy(BagNum.fromNum(price));
      if (y == null) continue;

      // 使用平滑算法计算柱状图宽度
      final barWidth = _calculateSmoothedBarWidth(totalVolume, maxVolume, minVolume, maxBarWidth);
      
      // 计算柱状图高度（价格级别的厚度）
      final barHeight = 2.0; // 固定高度，可以根据价格密度调整

      // 根据指标配置选择显示模式
      switch (indicator.config.displayMode) {
        case VolumeProfileDisplayMode.bidAsk:
          _paintBidAskBars(canvas, price, y, barWidth, barHeight,
                           buyData[price] ?? 0.0, sellData[price] ?? 0.0,
                           totalVolume, maxVolume, isLeftSide);
          break;
        case VolumeProfileDisplayMode.total:
          _paintTotalBar(canvas, y, barWidth, barHeight, totalVolume, isLeftSide);
          break;
        case VolumeProfileDisplayMode.overlay:
          _paintOverlayBars(canvas, price, y, barWidth, barHeight,
                           buyData[price] ?? 0.0, sellData[price] ?? 0.0,
                           totalVolume, maxVolume, isLeftSide);
          break;
      }
    }
  }

  /// 计算平滑后的柱状图宽度
  /// 使用平方根缩放来改善视觉效果
  double _calculateSmoothedBarWidth(double volume, double maxVolume, double minVolume, double maxBarWidth) {
    if (volume <= 0 || maxVolume <= 0) return 0.0;
    
    // 获取平滑强度配置 (0.0-1.0)
    final smoothingStrength = indicator.config.smoothingStrength.clamp(0.0, 1.0);
    
    // 设置最小显示宽度（确保小的成交量也能看到）
    final minBarWidth = maxBarWidth * 0.05; // 最小宽度为最大宽度的5%
    
    if (smoothingStrength == 0.0) {
      // 不应用平滑，使用线性缩放
      final linearRatio = volume / maxVolume;
      return minBarWidth + (maxBarWidth - minBarWidth) * linearRatio;
    }
    
    // 计算线性比例（无平滑）
    final linearRatio = volume / maxVolume;
    
    // 使用平方根函数进行平滑
    // 平方根函数特点：压缩大值增长速度，提升小值可见性
    final sqrtRatio = math.sqrt(linearRatio);
    
    // 根据平滑强度在线性比例和平方根比例之间插值
    final finalRatio = linearRatio * (1.0 - smoothingStrength) + sqrtRatio * smoothingStrength;
    
    // 计算最终宽度，确保在最小宽度和最大宽度之间
    final calculatedWidth = minBarWidth + (maxBarWidth - minBarWidth) * finalRatio;
    
    return math.max(minBarWidth, math.min(maxBarWidth, calculatedWidth));
  }

  /// 绘制买卖分离的柱状图
  void _paintBidAskBars(
    Canvas canvas, 
    double price, 
    double y, 
    double totalBarWidth, 
    double barHeight,
    double buyVolume, 
    double sellVolume, 
    double totalVolume,
    double maxVolume,
    bool isLeftSide,
  ) {
    final data = getExternalData();
    if (data == null || totalVolume <= 0) return;

    // 使用指标配置的颜色和透明度
    final Color upVolumeColor = indicator.config.upVolumeColor;
    final Color downVolumeColor = indicator.config.downVolumeColor;
    final double opacity = indicator.config.volumeOpacity;

    // 计算买卖比例
    final buyRatio = buyVolume / totalVolume;
    final sellRatio = sellVolume / totalVolume;

    // 计算买卖柱的宽度
    final buyBarWidth = totalBarWidth * buyRatio;
    final sellBarWidth = totalBarWidth * sellRatio;

    // 计算X坐标
    final double baseX;
    if (isLeftSide) {
      // 左侧显示：从左边开始
      baseX = 0;
    } else {
      // 右侧显示：从右边开始
      baseX = chartRect.width - totalBarWidth;
    }

    // 绘制买入柱（绿色）
    if (buyVolume > 0) {
      final buyPaint = Paint()
        ..color = upVolumeColor.withOpacity(opacity)
        ..style = PaintingStyle.fill;
      
      final buyRect = Rect.fromLTWH(
        baseX,
        y - barHeight / 2,
        buyBarWidth,
        barHeight,
      );
      canvas.drawRect(buyRect, buyPaint);
    }

    // 绘制卖出柱（红色）
    if (sellVolume > 0) {
      final sellPaint = Paint()
        ..color = downVolumeColor.withOpacity(opacity)
        ..style = PaintingStyle.fill;
      
      final sellRect = Rect.fromLTWH(
        baseX + buyBarWidth,
        y - barHeight / 2,
        sellBarWidth,
        barHeight,
      );
      canvas.drawRect(sellRect, sellPaint);
    }
  }

  /// 绘制总量柱状图
  void _paintTotalBar(
    Canvas canvas, 
    double y, 
    double barWidth, 
    double barHeight, 
    double volume,
    bool isLeftSide,
  ) {
    final data = getExternalData();
    if (data == null || volume <= 0) return;

    // 使用指标配置的颜色和透明度
    final Color totalVolumeColor = indicator.config.totalVolumeColor;
    final double opacity = indicator.config.volumeOpacity;

    // 计算X坐标
    final double x;
    if (isLeftSide) {
      x = 0;
    } else {
      x = chartRect.width - barWidth;
    }

    final paint = Paint()
      ..color = totalVolumeColor.withOpacity(opacity)
      ..style = PaintingStyle.fill;
    
    final rect = Rect.fromLTWH(x, y - barHeight / 2, barWidth, barHeight);
    canvas.drawRect(rect, paint);
  }

  /// 绘制叠加柱状图
  void _paintOverlayBars(
    Canvas canvas, 
    double price, 
    double y, 
    double totalBarWidth, 
    double barHeight,
    double buyVolume, 
    double sellVolume, 
    double totalVolume,
    double maxVolume,
    bool isLeftSide,
  ) {
    final data = getExternalData();
    if (data == null) return;
    
    // 使用指标配置的颜色和透明度
    final Color upVolumeColor = indicator.config.upVolumeColor;
    final Color downVolumeColor = indicator.config.downVolumeColor;
    final Color totalVolumeColor = indicator.config.totalVolumeColor;
    final double opacity = indicator.config.volumeOpacity;
    
    // 计算X坐标
    final double x;
    if (isLeftSide) {
      x = 0;
    } else {
      x = chartRect.width - totalBarWidth;
    }

    // 获取所有数据用于平滑计算
    final allBuyData = Map<double, double>.from(data['buy_data'] ?? {});
    final allSellData = Map<double, double>.from(data['sell_data'] ?? {});
    
    final maxBuyVolume = allBuyData.values.isNotEmpty ? allBuyData.values.reduce((a, b) => a > b ? a : b) : 0.0;
    final maxSellVolume = allSellData.values.isNotEmpty ? allSellData.values.reduce((a, b) => a > b ? a : b) : 0.0;
    final minBuyVolume = allBuyData.values.isNotEmpty ? allBuyData.values.reduce((a, b) => a < b ? a : b) : 0.0;
    final minSellVolume = allSellData.values.isNotEmpty ? allSellData.values.reduce((a, b) => a < b ? a : b) : 0.0;

    // 再绘制买入柱（叠加在上面）
    if (buyVolume > 0 && maxBuyVolume > 0) {
      final buyBarWidth = _calculateSmoothedBarWidth(buyVolume, maxBuyVolume, minBuyVolume, totalBarWidth);
      
      final buyPaint = Paint()
        ..color = upVolumeColor.withOpacity(opacity * 0.7)
        ..style = PaintingStyle.fill;
      
      final buyRect = Rect.fromLTWH(x, y - barHeight / 2, buyBarWidth, barHeight);
      canvas.drawRect(buyRect, buyPaint);
    }

    // 最后绘制卖出柱（叠加在最上面）
    if (sellVolume > 0 && maxSellVolume > 0) {
      final sellBarWidth = _calculateSmoothedBarWidth(sellVolume, maxSellVolume, minSellVolume, totalBarWidth);
      
      final sellPaint = Paint()
        ..color = downVolumeColor.withOpacity(opacity * 0.7)
        ..style = PaintingStyle.fill;
      
      final sellRect = Rect.fromLTWH(x, y - barHeight / 2, sellBarWidth, barHeight);
      canvas.drawRect(sellRect, sellPaint);
    }
  }

  /// 绘制POC线
  void _paintPOCLine(Canvas canvas, Size size, double pocPrice) {
    final data = getExternalData();
    if (data == null) return;
    
    // 使用指标配置
    if (!indicator.config.showPOC) return;
    
    final y = valueToDy(BagNum.fromNum(pocPrice));
    if (y == null) return;

    final Color pocColor = indicator.config.pocColor;
    final paint = Paint()
      ..color = pocColor.withOpacity(indicator.config.lineOpacity)
      ..strokeWidth = indicator.config.pocLineWidth
      ..style = PaintingStyle.stroke;

    // 根据线段样式选择绘制方式
    if (indicator.config.pocLineStyle == VolumeProfileLineStyle.solid) {
      // 绘制实线
      canvas.drawLine(
        Offset(0, y),
        Offset(chartRect.width, y),
        paint,
      );
    } else {
      // 绘制虚线
      _drawDashedLine(canvas, Offset(0, y), Offset(chartRect.width, y), paint, [5, 3]);
    }
  }

  /// 绘制VAH线
  void _paintVAHLine(Canvas canvas, Size size, double vahPrice) {
    final data = getExternalData();
    if (data == null) return;
    
    // 使用指标配置
    if (!indicator.config.showVAH) return;
    
    final y = valueToDy(BagNum.fromNum(vahPrice));
    if (y == null) return;

    final Color vahColor = indicator.config.vahColor;
    final paint = Paint()
      ..color = vahColor.withOpacity(indicator.config.lineOpacity)
      ..strokeWidth = indicator.config.vahLineWidth
      ..style = PaintingStyle.stroke;

    // 根据线段样式选择绘制方式
    if (indicator.config.vahLineStyle == VolumeProfileLineStyle.solid) {
      // 绘制实线
      canvas.drawLine(
        Offset(0, y),
        Offset(chartRect.width, y),
        paint,
      );
    } else {
      // 绘制虚线
      _drawDashedLine(canvas, Offset(0, y), Offset(chartRect.width, y), paint, [5, 3]);
    }
  }

  /// 绘制VAL线
  void _paintVALLine(Canvas canvas, Size size, double valPrice) {
    final data = getExternalData();
    if (data == null) return;
    
    // 使用指标配置
    if (!indicator.config.showVAL) return;
    
    final y = valueToDy(BagNum.fromNum(valPrice));
    if (y == null) return;

    final Color valColor = indicator.config.valColor;
    final paint = Paint()
      ..color = valColor.withOpacity(indicator.config.lineOpacity)
      ..strokeWidth = indicator.config.valLineWidth
      ..style = PaintingStyle.stroke;

    // 根据线段样式选择绘制方式
    if (indicator.config.valLineStyle == VolumeProfileLineStyle.solid) {
      // 绘制实线
      canvas.drawLine(
        Offset(0, y),
        Offset(chartRect.width, y),
        paint,
      );
    } else {
      // 绘制虚线
      _drawDashedLine(canvas, Offset(0, y), Offset(chartRect.width, y), paint, [5, 3]);
    }
  }

  /// 绘制虚线的辅助方法
  void _drawDashedLine(Canvas canvas, Offset start, Offset end, Paint paint, List<double> pattern) {
    final path = Path();
    path.moveTo(start.dx, start.dy);
    
    double distance = 0;
    final totalDistance = (end - start).distance;
    final direction = (end - start) / totalDistance;
    
    bool isDrawing = true;
    int patternIndex = 0;
    
    while (distance < totalDistance) {
      final segmentLength = pattern[patternIndex % pattern.length];
      final nextDistance = math.min(distance + segmentLength, totalDistance);
      final nextPoint = start + direction * nextDistance;
      
      if (isDrawing) {
        path.lineTo(nextPoint.dx, nextPoint.dy);
      } else {
        path.moveTo(nextPoint.dx, nextPoint.dy);
      }
      
      distance = nextDistance;
      isDrawing = !isDrawing;
      patternIndex++;
    }
    
    canvas.drawPath(path, paint);
  }

  @override
  void onCross(Canvas canvas, Offset offset) {
    // Volume Profile的十字线交互
  }

  @override
  Size? paintTips(
    Canvas canvas, {
    CandleModel? model,
    Offset? offset,
    Rect? tipsRect,
  }) {
    final data = getExternalData();
    if (data == null) return null;

    final poc = data['poc']?.toDouble() ?? 0.0;
    final val = data['val']?.toDouble() ?? 0.0;
    final vah = data['vah']?.toDouble() ?? 0.0;
    final vwap = data['vwap']?.toDouble() ?? 0.0;

    String tipText = 'Volume Profile';
    if (poc > 0) tipText += ' POC: ${poc.toStringAsFixed(2)}';
    if (val > 0) tipText += ' VAL: ${val.toStringAsFixed(2)}';
    if (vah > 0) tipText += ' VAH: ${vah.toStringAsFixed(2)}';
    if (vwap > 0) tipText += ' VWAP: ${vwap.toStringAsFixed(2)}';

    const textStyle = TextStyle(
      color: Colors.white,
      fontSize: 12,
    );
    
    final textPainter = TextPainter(
      text: TextSpan(text: tipText, style: textStyle),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    
    if (tipsRect != null) {
      textPainter.paint(canvas, tipsRect.topLeft);
    }
    
    return textPainter.size;
  }
} 