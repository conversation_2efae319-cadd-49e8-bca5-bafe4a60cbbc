// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'volume_profile.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$VolumeProfileIndicatorCWProxy {
  VolumeProfileIndicator zIndex(int zIndex);

  VolumeProfileIndicator height(double height);

  VolumeProfileIndicator padding(EdgeInsets padding);

  VolumeProfileIndicator config(VolumeProfileConfig config);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `VolumeProfileIndicator(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// VolumeProfileIndicator(...).copyWith(id: 12, name: "My name")
  /// ````
  VolumeProfileIndicator call({
    int zIndex,
    double height,
    EdgeInsets padding,
    VolumeProfileConfig config,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfVolumeProfileIndicator.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfVolumeProfileIndicator.copyWith.fieldName(...)`
class _$VolumeProfileIndicatorCWProxyImpl
    implements _$VolumeProfileIndicatorCWProxy {
  const _$VolumeProfileIndicatorCWProxyImpl(this._value);

  final VolumeProfileIndicator _value;

  @override
  VolumeProfileIndicator zIndex(int zIndex) => this(zIndex: zIndex);

  @override
  VolumeProfileIndicator height(double height) => this(height: height);

  @override
  VolumeProfileIndicator padding(EdgeInsets padding) => this(padding: padding);

  @override
  VolumeProfileIndicator config(VolumeProfileConfig config) =>
      this(config: config);

  @override
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `VolumeProfileIndicator(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// VolumeProfileIndicator(...).copyWith(id: 12, name: "My name")
  /// ````
  VolumeProfileIndicator call({
    Object? zIndex = const $CopyWithPlaceholder(),
    Object? height = const $CopyWithPlaceholder(),
    Object? padding = const $CopyWithPlaceholder(),
    Object? config = const $CopyWithPlaceholder(),
  }) {
    return VolumeProfileIndicator(
      zIndex:
          zIndex == const $CopyWithPlaceholder()
              ? _value.zIndex
              // ignore: cast_nullable_to_non_nullable
              : zIndex as int,
      height:
          height == const $CopyWithPlaceholder()
              ? _value.height
              // ignore: cast_nullable_to_non_nullable
              : height as double,
      padding:
          padding == const $CopyWithPlaceholder()
              ? _value.padding
              // ignore: cast_nullable_to_non_nullable
              : padding as EdgeInsets,
      config:
          config == const $CopyWithPlaceholder()
              ? _value.config
              // ignore: cast_nullable_to_non_nullable
              : config as VolumeProfileConfig,
    );
  }
}

extension $VolumeProfileIndicatorCopyWith on VolumeProfileIndicator {
  /// Returns a callable class that can be used as follows: `instanceOfVolumeProfileIndicator.copyWith(...)` or like so:`instanceOfVolumeProfileIndicator.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$VolumeProfileIndicatorCWProxy get copyWith =>
      _$VolumeProfileIndicatorCWProxyImpl(this);
}

abstract class _$VolumeProfileConfigCWProxy {
  VolumeProfileConfig showPOC(bool showPOC);

  VolumeProfileConfig showVAH(bool showVAH);

  VolumeProfileConfig showVAL(bool showVAL);

  VolumeProfileConfig pocColor(Color pocColor);

  VolumeProfileConfig vahColor(Color vahColor);

  VolumeProfileConfig valColor(Color valColor);

  VolumeProfileConfig pocLineStyle(VolumeProfileLineStyle pocLineStyle);

  VolumeProfileConfig vahLineStyle(VolumeProfileLineStyle vahLineStyle);

  VolumeProfileConfig valLineStyle(VolumeProfileLineStyle valLineStyle);

  VolumeProfileConfig displayMode(VolumeProfileDisplayMode displayMode);

  VolumeProfileConfig upVolumeColor(Color upVolumeColor);

  VolumeProfileConfig downVolumeColor(Color downVolumeColor);

  VolumeProfileConfig totalVolumeColor(Color totalVolumeColor);

  VolumeProfileConfig pocLineWidth(double pocLineWidth);

  VolumeProfileConfig vahLineWidth(double vahLineWidth);

  VolumeProfileConfig valLineWidth(double valLineWidth);

  VolumeProfileConfig volumeOpacity(double volumeOpacity);

  VolumeProfileConfig lineOpacity(double lineOpacity);

  VolumeProfileConfig priceZones(int priceZones);

  VolumeProfileConfig valueAreaPercentage(double valueAreaPercentage);

  VolumeProfileConfig maxWidthPercent(double maxWidthPercent);

  VolumeProfileConfig smoothingStrength(double smoothingStrength);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `VolumeProfileConfig(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// VolumeProfileConfig(...).copyWith(id: 12, name: "My name")
  /// ````
  VolumeProfileConfig call({
    bool showPOC,
    bool showVAH,
    bool showVAL,
    Color pocColor,
    Color vahColor,
    Color valColor,
    VolumeProfileLineStyle pocLineStyle,
    VolumeProfileLineStyle vahLineStyle,
    VolumeProfileLineStyle valLineStyle,
    VolumeProfileDisplayMode displayMode,
    Color upVolumeColor,
    Color downVolumeColor,
    Color totalVolumeColor,
    double pocLineWidth,
    double vahLineWidth,
    double valLineWidth,
    double volumeOpacity,
    double lineOpacity,
    int priceZones,
    double valueAreaPercentage,
    double maxWidthPercent,
    double smoothingStrength,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfVolumeProfileConfig.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfVolumeProfileConfig.copyWith.fieldName(...)`
class _$VolumeProfileConfigCWProxyImpl implements _$VolumeProfileConfigCWProxy {
  const _$VolumeProfileConfigCWProxyImpl(this._value);

  final VolumeProfileConfig _value;

  @override
  VolumeProfileConfig showPOC(bool showPOC) => this(showPOC: showPOC);

  @override
  VolumeProfileConfig showVAH(bool showVAH) => this(showVAH: showVAH);

  @override
  VolumeProfileConfig showVAL(bool showVAL) => this(showVAL: showVAL);

  @override
  VolumeProfileConfig pocColor(Color pocColor) => this(pocColor: pocColor);

  @override
  VolumeProfileConfig vahColor(Color vahColor) => this(vahColor: vahColor);

  @override
  VolumeProfileConfig valColor(Color valColor) => this(valColor: valColor);

  @override
  VolumeProfileConfig pocLineStyle(VolumeProfileLineStyle pocLineStyle) =>
      this(pocLineStyle: pocLineStyle);

  @override
  VolumeProfileConfig vahLineStyle(VolumeProfileLineStyle vahLineStyle) =>
      this(vahLineStyle: vahLineStyle);

  @override
  VolumeProfileConfig valLineStyle(VolumeProfileLineStyle valLineStyle) =>
      this(valLineStyle: valLineStyle);

  @override
  VolumeProfileConfig displayMode(VolumeProfileDisplayMode displayMode) =>
      this(displayMode: displayMode);

  @override
  VolumeProfileConfig upVolumeColor(Color upVolumeColor) =>
      this(upVolumeColor: upVolumeColor);

  @override
  VolumeProfileConfig downVolumeColor(Color downVolumeColor) =>
      this(downVolumeColor: downVolumeColor);

  @override
  VolumeProfileConfig totalVolumeColor(Color totalVolumeColor) =>
      this(totalVolumeColor: totalVolumeColor);

  @override
  VolumeProfileConfig pocLineWidth(double pocLineWidth) =>
      this(pocLineWidth: pocLineWidth);

  @override
  VolumeProfileConfig vahLineWidth(double vahLineWidth) =>
      this(vahLineWidth: vahLineWidth);

  @override
  VolumeProfileConfig valLineWidth(double valLineWidth) =>
      this(valLineWidth: valLineWidth);

  @override
  VolumeProfileConfig volumeOpacity(double volumeOpacity) =>
      this(volumeOpacity: volumeOpacity);

  @override
  VolumeProfileConfig lineOpacity(double lineOpacity) =>
      this(lineOpacity: lineOpacity);

  @override
  VolumeProfileConfig priceZones(int priceZones) =>
      this(priceZones: priceZones);

  @override
  VolumeProfileConfig valueAreaPercentage(double valueAreaPercentage) =>
      this(valueAreaPercentage: valueAreaPercentage);

  @override
  VolumeProfileConfig maxWidthPercent(double maxWidthPercent) =>
      this(maxWidthPercent: maxWidthPercent);

  @override
  VolumeProfileConfig smoothingStrength(double smoothingStrength) =>
      this(smoothingStrength: smoothingStrength);

  @override
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `VolumeProfileConfig(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// VolumeProfileConfig(...).copyWith(id: 12, name: "My name")
  /// ````
  VolumeProfileConfig call({
    Object? showPOC = const $CopyWithPlaceholder(),
    Object? showVAH = const $CopyWithPlaceholder(),
    Object? showVAL = const $CopyWithPlaceholder(),
    Object? pocColor = const $CopyWithPlaceholder(),
    Object? vahColor = const $CopyWithPlaceholder(),
    Object? valColor = const $CopyWithPlaceholder(),
    Object? pocLineStyle = const $CopyWithPlaceholder(),
    Object? vahLineStyle = const $CopyWithPlaceholder(),
    Object? valLineStyle = const $CopyWithPlaceholder(),
    Object? displayMode = const $CopyWithPlaceholder(),
    Object? upVolumeColor = const $CopyWithPlaceholder(),
    Object? downVolumeColor = const $CopyWithPlaceholder(),
    Object? totalVolumeColor = const $CopyWithPlaceholder(),
    Object? pocLineWidth = const $CopyWithPlaceholder(),
    Object? vahLineWidth = const $CopyWithPlaceholder(),
    Object? valLineWidth = const $CopyWithPlaceholder(),
    Object? volumeOpacity = const $CopyWithPlaceholder(),
    Object? lineOpacity = const $CopyWithPlaceholder(),
    Object? priceZones = const $CopyWithPlaceholder(),
    Object? valueAreaPercentage = const $CopyWithPlaceholder(),
    Object? maxWidthPercent = const $CopyWithPlaceholder(),
    Object? smoothingStrength = const $CopyWithPlaceholder(),
  }) {
    return VolumeProfileConfig(
      showPOC:
          showPOC == const $CopyWithPlaceholder()
              ? _value.showPOC
              // ignore: cast_nullable_to_non_nullable
              : showPOC as bool,
      showVAH:
          showVAH == const $CopyWithPlaceholder()
              ? _value.showVAH
              // ignore: cast_nullable_to_non_nullable
              : showVAH as bool,
      showVAL:
          showVAL == const $CopyWithPlaceholder()
              ? _value.showVAL
              // ignore: cast_nullable_to_non_nullable
              : showVAL as bool,
      pocColor:
          pocColor == const $CopyWithPlaceholder()
              ? _value.pocColor
              // ignore: cast_nullable_to_non_nullable
              : pocColor as Color,
      vahColor:
          vahColor == const $CopyWithPlaceholder()
              ? _value.vahColor
              // ignore: cast_nullable_to_non_nullable
              : vahColor as Color,
      valColor:
          valColor == const $CopyWithPlaceholder()
              ? _value.valColor
              // ignore: cast_nullable_to_non_nullable
              : valColor as Color,
      pocLineStyle:
          pocLineStyle == const $CopyWithPlaceholder()
              ? _value.pocLineStyle
              // ignore: cast_nullable_to_non_nullable
              : pocLineStyle as VolumeProfileLineStyle,
      vahLineStyle:
          vahLineStyle == const $CopyWithPlaceholder()
              ? _value.vahLineStyle
              // ignore: cast_nullable_to_non_nullable
              : vahLineStyle as VolumeProfileLineStyle,
      valLineStyle:
          valLineStyle == const $CopyWithPlaceholder()
              ? _value.valLineStyle
              // ignore: cast_nullable_to_non_nullable
              : valLineStyle as VolumeProfileLineStyle,
      displayMode:
          displayMode == const $CopyWithPlaceholder()
              ? _value.displayMode
              // ignore: cast_nullable_to_non_nullable
              : displayMode as VolumeProfileDisplayMode,
      upVolumeColor:
          upVolumeColor == const $CopyWithPlaceholder()
              ? _value.upVolumeColor
              // ignore: cast_nullable_to_non_nullable
              : upVolumeColor as Color,
      downVolumeColor:
          downVolumeColor == const $CopyWithPlaceholder()
              ? _value.downVolumeColor
              // ignore: cast_nullable_to_non_nullable
              : downVolumeColor as Color,
      totalVolumeColor:
          totalVolumeColor == const $CopyWithPlaceholder()
              ? _value.totalVolumeColor
              // ignore: cast_nullable_to_non_nullable
              : totalVolumeColor as Color,
      pocLineWidth:
          pocLineWidth == const $CopyWithPlaceholder()
              ? _value.pocLineWidth
              // ignore: cast_nullable_to_non_nullable
              : pocLineWidth as double,
      vahLineWidth:
          vahLineWidth == const $CopyWithPlaceholder()
              ? _value.vahLineWidth
              // ignore: cast_nullable_to_non_nullable
              : vahLineWidth as double,
      valLineWidth:
          valLineWidth == const $CopyWithPlaceholder()
              ? _value.valLineWidth
              // ignore: cast_nullable_to_non_nullable
              : valLineWidth as double,
      volumeOpacity:
          volumeOpacity == const $CopyWithPlaceholder()
              ? _value.volumeOpacity
              // ignore: cast_nullable_to_non_nullable
              : volumeOpacity as double,
      lineOpacity:
          lineOpacity == const $CopyWithPlaceholder()
              ? _value.lineOpacity
              // ignore: cast_nullable_to_non_nullable
              : lineOpacity as double,
      priceZones:
          priceZones == const $CopyWithPlaceholder()
              ? _value.priceZones
              // ignore: cast_nullable_to_non_nullable
              : priceZones as int,
      valueAreaPercentage:
          valueAreaPercentage == const $CopyWithPlaceholder()
              ? _value.valueAreaPercentage
              // ignore: cast_nullable_to_non_nullable
              : valueAreaPercentage as double,
      maxWidthPercent:
          maxWidthPercent == const $CopyWithPlaceholder()
              ? _value.maxWidthPercent
              // ignore: cast_nullable_to_non_nullable
              : maxWidthPercent as double,
      smoothingStrength:
          smoothingStrength == const $CopyWithPlaceholder()
              ? _value.smoothingStrength
              // ignore: cast_nullable_to_non_nullable
              : smoothingStrength as double,
    );
  }
}

extension $VolumeProfileConfigCopyWith on VolumeProfileConfig {
  /// Returns a callable class that can be used as follows: `instanceOfVolumeProfileConfig.copyWith(...)` or like so:`instanceOfVolumeProfileConfig.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$VolumeProfileConfigCWProxy get copyWith =>
      _$VolumeProfileConfigCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VolumeProfileIndicator _$VolumeProfileIndicatorFromJson(
  Map<String, dynamic> json,
) => VolumeProfileIndicator(
  zIndex: (json['zIndex'] as num?)?.toInt() ?? 0,
  height: (json['height'] as num).toDouble(),
  padding:
      json['padding'] == null
          ? defaultMainIndicatorPadding
          : const EdgeInsetsConverter().fromJson(
            json['padding'] as Map<String, dynamic>,
          ),
  config:
      json['config'] == null
          ? const VolumeProfileConfig()
          : VolumeProfileConfig.fromJson(
            json['config'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$VolumeProfileIndicatorToJson(
  VolumeProfileIndicator instance,
) => <String, dynamic>{
  'height': instance.height,
  'padding': const EdgeInsetsConverter().toJson(instance.padding),
  'zIndex': instance.zIndex,
  'config': instance.config.toJson(),
};
