// Copyright 2024 Andy<PERSON>Zhao
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

part of 'volume_profile.dart';

/// 价格区间成交量数据
class PriceVolumeData {
  PriceVolumeData({
    required this.price,
    required this.upVolume,
    required this.downVolume,
  });

  final double price;      // 价格
  final double upVolume;   // 上涨成交量
  final double downVolume; // 下跌成交量

  /// 总成交量
  double get totalVolume => upVolume + downVolume;

  /// 买卖力量比
  double get buyRatio => totalVolume > 0 ? upVolume / totalVolume : 0.0;

  Map<String, dynamic> toJson() {
    return {
      'price': price,
      'upVolume': upVolume,
      'downVolume': downVolume,
    };
  }

  factory PriceVolumeData.fromJson(Map<String, dynamic> json) {
    return PriceVolumeData(
      price: json['price']?.toDouble() ?? 0.0,
      upVolume: json['upVolume']?.toDouble() ?? 0.0,
      downVolume: json['downVolume']?.toDouble() ?? 0.0,
    );
  }
}

/// Volume Profile 计算结果数据
class VolumeProfileData {
  VolumeProfileData({
    required this.priceVolumeList,
    required this.poc,
    required this.vah,
    required this.val,
    required this.maxVolume,
    required this.totalVolume,
  });

  final List<PriceVolumeData> priceVolumeList; // 价格区间成交量列表
  final double poc;        // Point of Control (最大成交量价格)
  final double vah;        // Value Area High
  final double val;        // Value Area Low
  final double maxVolume;  // 最大单一价格成交量
  final double totalVolume; // 总成交量

  /// 获取指定价格的成交量数据
  PriceVolumeData? getVolumeAtPrice(double price) {
    for (final data in priceVolumeList) {
      if ((data.price - price).abs() < 0.0001) {
        return data;
      }
    }
    return null;
  }

  /// 获取价格范围内的总成交量
  double getVolumeInRange(double lowPrice, double highPrice) {
    double volume = 0.0;
    for (final data in priceVolumeList) {
      if (data.price >= lowPrice && data.price <= highPrice) {
        volume += data.totalVolume;
      }
    }
    return volume;
  }

  Map<String, dynamic> toJson() {
    return {
      'priceVolumeList': priceVolumeList.map((e) => e.toJson()).toList(),
      'poc': poc,
      'vah': vah,
      'val': val,
      'maxVolume': maxVolume,
      'totalVolume': totalVolume,
    };
  }

  factory VolumeProfileData.fromJson(Map<String, dynamic> json) {
    return VolumeProfileData(
      priceVolumeList: (json['priceVolumeList'] as List?)
          ?.map((e) => PriceVolumeData.fromJson(e))
          .toList() ?? [],
      poc: json['poc']?.toDouble() ?? 0.0,
      vah: json['vah']?.toDouble() ?? 0.0,
      val: json['val']?.toDouble() ?? 0.0,
      maxVolume: json['maxVolume']?.toDouble() ?? 0.0,
      totalVolume: json['totalVolume']?.toDouble() ?? 0.0,
    );
  }
}

/// Volume Profile API 请求参数
class VolumeProfileRequest {
  VolumeProfileRequest({
    required this.symbol,
    required this.startTime,
    required this.endTime,
    this.priceZones = 100,
  });

  final String symbol;     // 交易符号
  final int startTime;     // 开始时间戳(毫秒)
  final int endTime;       // 结束时间戳(毫秒)
  final int priceZones;    // 价格区间数量

  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'startTime': startTime,
      'endTime': endTime,
      'priceZones': priceZones,
    };
  }

  factory VolumeProfileRequest.fromJson(Map<String, dynamic> json) {
    return VolumeProfileRequest(
      symbol: json['symbol'] ?? '',
      startTime: json['startTime'] ?? 0,
      endTime: json['endTime'] ?? 0,
      priceZones: json['priceZones'] ?? 100,
    );
  }
} 