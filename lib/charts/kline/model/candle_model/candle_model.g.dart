// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'candle_model.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$CandleModelCWProxy {
  CandleModel ts(int ts);

  CandleModel o(Decimal o);

  CandleModel h(Decimal h);

  CandleModel l(Decimal l);

  CandleModel c(Decimal c);

  CandleModel v(Decimal v);

  CandleModel vc(Decimal? vc);

  CandleModel vcq(Decimal? vcq);

  CandleModel confirm(String confirm);

  CandleModel priceLevel(PriceLevelDetail? priceLevel);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `CandleModel(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// CandleModel(...).copyWith(id: 12, name: "My name")
  /// ````
  CandleModel call({
    int ts,
    Decimal o,
    Decimal h,
    Decimal l,
    Decimal c,
    Decimal v,
    Decimal? vc,
    Decimal? vcq,
    String confirm,
    PriceLevelDetail? priceLevel,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfCandleModel.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfCandleModel.copyWith.fieldName(...)`
class _$CandleModelCWProxyImpl implements _$CandleModelCWProxy {
  const _$CandleModelCWProxyImpl(this._value);

  final CandleModel _value;

  @override
  CandleModel ts(int ts) => this(ts: ts);

  @override
  CandleModel o(Decimal o) => this(o: o);

  @override
  CandleModel h(Decimal h) => this(h: h);

  @override
  CandleModel l(Decimal l) => this(l: l);

  @override
  CandleModel c(Decimal c) => this(c: c);

  @override
  CandleModel v(Decimal v) => this(v: v);

  @override
  CandleModel vc(Decimal? vc) => this(vc: vc);

  @override
  CandleModel vcq(Decimal? vcq) => this(vcq: vcq);

  @override
  CandleModel confirm(String confirm) => this(confirm: confirm);

  @override
  CandleModel priceLevel(PriceLevelDetail? priceLevel) =>
      this(priceLevel: priceLevel);

  @override
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `CandleModel(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// CandleModel(...).copyWith(id: 12, name: "My name")
  /// ````
  CandleModel call({
    Object? ts = const $CopyWithPlaceholder(),
    Object? o = const $CopyWithPlaceholder(),
    Object? h = const $CopyWithPlaceholder(),
    Object? l = const $CopyWithPlaceholder(),
    Object? c = const $CopyWithPlaceholder(),
    Object? v = const $CopyWithPlaceholder(),
    Object? vc = const $CopyWithPlaceholder(),
    Object? vcq = const $CopyWithPlaceholder(),
    Object? confirm = const $CopyWithPlaceholder(),
    Object? priceLevel = const $CopyWithPlaceholder(),
  }) {
    return CandleModel(
      ts:
          ts == const $CopyWithPlaceholder()
              ? _value.ts
              // ignore: cast_nullable_to_non_nullable
              : ts as int,
      o:
          o == const $CopyWithPlaceholder()
              ? _value.o
              // ignore: cast_nullable_to_non_nullable
              : o as Decimal,
      h:
          h == const $CopyWithPlaceholder()
              ? _value.h
              // ignore: cast_nullable_to_non_nullable
              : h as Decimal,
      l:
          l == const $CopyWithPlaceholder()
              ? _value.l
              // ignore: cast_nullable_to_non_nullable
              : l as Decimal,
      c:
          c == const $CopyWithPlaceholder()
              ? _value.c
              // ignore: cast_nullable_to_non_nullable
              : c as Decimal,
      v:
          v == const $CopyWithPlaceholder()
              ? _value.v
              // ignore: cast_nullable_to_non_nullable
              : v as Decimal,
      vc:
          vc == const $CopyWithPlaceholder()
              ? _value.vc
              // ignore: cast_nullable_to_non_nullable
              : vc as Decimal?,
      vcq:
          vcq == const $CopyWithPlaceholder()
              ? _value.vcq
              // ignore: cast_nullable_to_non_nullable
              : vcq as Decimal?,
      confirm:
          confirm == const $CopyWithPlaceholder()
              ? _value.confirm
              // ignore: cast_nullable_to_non_nullable
              : confirm as String,
      priceLevel:
          priceLevel == const $CopyWithPlaceholder()
              ? _value.priceLevel
              // ignore: cast_nullable_to_non_nullable
              : priceLevel as PriceLevelDetail?,
    );
  }
}

extension $CandleModelCopyWith on CandleModel {
  /// Returns a callable class that can be used as follows: `instanceOfCandleModel.copyWith(...)` or like so:`instanceOfCandleModel.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$CandleModelCWProxy get copyWith => _$CandleModelCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PriceLevelEntry _$PriceLevelEntryFromJson(Map<String, dynamic> json) =>
    PriceLevelEntry(
      priceMin: (json['priceMin'] as num).toDouble(),
      priceMax: (json['priceMax'] as num).toDouble(),
      volume: (json['volume'] as num).toDouble(),
    );

Map<String, dynamic> _$PriceLevelEntryToJson(PriceLevelEntry instance) =>
    <String, dynamic>{
      'priceMin': instance.priceMin,
      'priceMax': instance.priceMax,
      'volume': instance.volume,
    };

PriceLevelDetail _$PriceLevelDetailFromJson(Map<String, dynamic> json) =>
    PriceLevelDetail(
      sellLevels:
          (json['sellLevels'] as List<dynamic>?)
              ?.map((e) => PriceLevelEntry.fromJson(e as Map<String, dynamic>))
              .toList(),
      buyLevels:
          (json['buyLevels'] as List<dynamic>?)
              ?.map((e) => PriceLevelEntry.fromJson(e as Map<String, dynamic>))
              .toList(),
      totalSellVolume: (json['totalSellVolume'] as num?)?.toDouble(),
      totalBuyVolume: (json['totalBuyVolume'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$PriceLevelDetailToJson(PriceLevelDetail instance) =>
    <String, dynamic>{
      'sellLevels': instance.sellLevels?.map((e) => e.toJson()).toList(),
      'buyLevels': instance.buyLevels?.map((e) => e.toJson()).toList(),
      'totalSellVolume': instance.totalSellVolume,
      'totalBuyVolume': instance.totalBuyVolume,
    };

CandleModel _$CandleModelFromJson(Map<String, dynamic> json) => CandleModel(
  ts: valueToInt(json['ts']),
  o: Decimal.fromJson(json['o'] as String),
  h: Decimal.fromJson(json['h'] as String),
  l: Decimal.fromJson(json['l'] as String),
  c: Decimal.fromJson(json['c'] as String),
  v: Decimal.fromJson(json['v'] as String),
  vc: json['vc'] == null ? null : Decimal.fromJson(json['vc'] as String),
  vcq: json['vcq'] == null ? null : Decimal.fromJson(json['vcq'] as String),
  confirm: json['confirm'] as String? ?? '1',
  priceLevel:
      json['priceLevel'] == null
          ? null
          : PriceLevelDetail.fromJson(
            json['priceLevel'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$CandleModelToJson(CandleModel instance) =>
    <String, dynamic>{
      'ts': intToString(instance.ts),
      'o': instance.o.toJson(),
      'h': instance.h.toJson(),
      'l': instance.l.toJson(),
      'c': instance.c.toJson(),
      'v': instance.v.toJson(),
      'vc': instance.vc?.toJson(),
      'vcq': instance.vcq?.toJson(),
      'confirm': instance.confirm,
      'priceLevel': instance.priceLevel?.toJson(),
    };
