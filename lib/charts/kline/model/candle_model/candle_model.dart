// Copyright 2024 Andy<PERSON>Zhao
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:decimal/decimal.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../constant.dart';
import '../../extension/export.dart';
import '../../framework/serializers.dart';
import '../../utils/export.dart';
import '../bag_num.dart';

part 'candle_helper.dart';
part 'candle_model.g.dart';

// 新的辅助类，用于存储价格区间及其成交量
@JsonSerializable()
class PriceLevelEntry {
  final double priceMin;
  final double priceMax;
  final double volume; // 该价格区间的成交量

  PriceLevelEntry({
    required this.priceMin,
    required this.priceMax,
    required this.volume,
  });

  factory PriceLevelEntry.fromJson(Map<String, dynamic> json) => _$PriceLevelEntryFromJson(json);
  Map<String, dynamic> toJson() => _$PriceLevelEntryToJson(this);
}

// 用于存储买卖成交量轨迹的详细数据结构
@JsonSerializable(explicitToJson: true)
class PriceLevelDetail {
  final List<PriceLevelEntry>? sellLevels; // 修改后的类型
  final List<PriceLevelEntry>? buyLevels;  // 修改后的类型
  final double? totalSellVolume;          // 新增：卖单总成交量
  final double? totalBuyVolume;           // 新增：买单总成交量

  PriceLevelDetail({
    this.sellLevels,
    this.buyLevels,
    this.totalSellVolume,
    this.totalBuyVolume,
  });

  // 方便判断是否有任何数据
  bool get isEmpty => (sellLevels == null || sellLevels!.isEmpty) && (buyLevels == null || buyLevels!.isEmpty);
  bool get isNotEmpty => !isEmpty;

  factory PriceLevelDetail.fromJson(Map<String, dynamic> json) => _$PriceLevelDetailFromJson(json);
  Map<String, dynamic> toJson() => _$PriceLevelDetailToJson(this);

  @override
  String toString() {
    return 'PriceLevelDetail(sellEntries: ${sellLevels?.length ?? 0}, buyEntries: ${buyLevels?.length ?? 0}, totalSellVol: $totalSellVolume, totalBuyVol: $totalBuyVolume)';
  }
}

@CopyWith()
@JsonSerializable(explicitToJson: true)
class CandleModel implements Comparable<CandleModel> {
  CandleModel({
    required this.ts,
    required this.o,
    required this.h,
    required this.l,
    required this.c,
    required this.v,
    this.vc,
    this.vcq,
    this.confirm = '1',
    this.priceLevel,
  });

  /// 开始时间，Unix时间戳的毫秒数格式，如 1597026383085
  @JsonKey(fromJson: valueToInt, toJson: intToString)
  final int ts;
  // @JsonKey(fromJson: valueToDateTime, toJson: dateTimeToInt)
  // DateTime? datetime, // 从timestamp转换为dateTime;

  /// 开盘价格
  @JsonKey()
  final Decimal o;

  /// 最高价格
  @JsonKey()
  final Decimal h;

  ///最低价格
  @JsonKey()
  final Decimal l;

  /// 收盘价格
  @JsonKey()
  final Decimal c;

  /// 交易量，以张为单位: 如果是衍生品合约，数值为合约的张数。如果是币币/币币杠杆，数值为交易货币的数量。
  @JsonKey()
  final Decimal v;

  /// 交易量(成交额)，以币为单位: 如果是衍生品合约，数值为交易货币的数量。如果是币币/币币杠杆，数值为计价货币的数量。
  @JsonKey()
  Decimal? vc;

  ///交易量(成交额)，以计价货币为单位: 如 BTC-USDT和BTC-USDT-SWAP，单位均是USDT。BTC-USD-SWAP单位是USD。
  @JsonKey()
  Decimal? vcq;

  /// K线状态:  0：K线未完结  1：K线已完结
  @JsonKey()
  String confirm;

  /// 新增：价格成交区间 [ [minPrice1, maxPrice1], [minPrice2, maxPrice2], ... ]
  final PriceLevelDetail? priceLevel;

  CalculateData? _calcuData;

  CalculateData get calcuData => _calcuData!;

  @override
  int compareTo(CandleModel other) {
    return other.ts - ts;
  }

  static CandleModel? fromList(List<dynamic> data) {
    if (data.isEmpty || data.length < 6) return null;
    final ts = parseInt(data.getItem(0));
    if (ts == null) return null;
    final o = parseDecimal(data.getItem(1));
    if (o == null) return null;
    final h = parseDecimal(data.getItem(2));
    if (h == null) return null;
    final l = parseDecimal(data.getItem(3));
    if (l == null) return null;
    final c = parseDecimal(data.getItem(4));
    if (c == null) return null;
    final v = parseDecimal(data.getItem(5));
    if (v == null) return null;
    return CandleModel(
      ts: ts,
      o: o,
      h: h,
      l: l,
      c: c,
      v: v,
      vc: parseDecimal(data.getItem(6)),
      vcq: parseDecimal(data.getItem(7)),
      confirm: data.getItem(8)?.toString() ?? '1',
      priceLevel: null,
    );
  }

  factory CandleModel.fromJson(Map<String, dynamic> json) =>
      _$CandleModelFromJson(json);

  Map<String, dynamic> toJson() => _$CandleModelToJson(this);

  BagNum? _open;
  BagNum get open => _open ??= BagNum.fromDecimal(o);
  BagNum? _high;
  BagNum get high => _high ??= BagNum.fromDecimal(h);
  BagNum? _low;
  BagNum get low => _low ??= BagNum.fromDecimal(l);
  BagNum? _close;
  BagNum get close => _close ??= BagNum.fromDecimal(c);
  BagNum? _vol;
  BagNum get vol => _vol ??= BagNum.fromDecimal(v);
  BagNum? _volCcy;
  BagNum? get volCcy {
    if (vc != null) return _volCcy ??= BagNum.fromDecimal(vc!);
    return null;
  }

  BagNum? _volCcyQuote;
  BagNum? get volCcyQuote {
    if (vcq != null) return _volCcyQuote ??= BagNum.fromDecimal(vcq!);
    return null;
  }

  void initBasicData(
    ComputeMode mode,
    int indicatorCount, {
    bool reset = false,
  }) {
    if (reset ||
        _open == null ||
        _high == null ||
        _low == null ||
        _close == null ||
        _vol == null) {
      if (mode == ComputeMode.fast) {
        _open = BagNum.fromNum(o.toDouble());
        _high = BagNum.fromNum(h.toDouble());
        _low = BagNum.fromNum(l.toDouble());
        _close = BagNum.fromNum(c.toDouble());
        _vol = BagNum.fromNum(v.toDouble());
        _volCcy = vc != null ? BagNum.fromNum(vc!.toDouble()) : null;
        _volCcyQuote = vcq != null ? BagNum.fromNum(vcq!.toDouble()) : null;
      } else {
        _open = BagNum.fromDecimal(o);
        _high = BagNum.fromDecimal(h);
        _low = BagNum.fromDecimal(l);
        _close = BagNum.fromDecimal(c);
        _vol = BagNum.fromDecimal(v);
        _volCcy = vc != null ? BagNum.fromDecimal(vc!) : null;
        _volCcyQuote = vcq != null ? BagNum.fromDecimal(vcq!) : null;
      }
    }
    _calcuData = CalculateData.init(indicatorCount);
  }

  CandleModel copyWith({
    int? ts,
    Decimal? o,
    Decimal? h,
    Decimal? l,
    Decimal? c,
    Decimal? v,
    Decimal? vc,
    Decimal? vcq,
    String? confirm,
    PriceLevelDetail? priceLevel,
  }) {
    return CandleModel(
      ts: ts ?? this.ts,
      o: o ?? this.o,
      h: h ?? this.h,
      l: l ?? this.l,
      c: c ?? this.c,
      v: v ?? this.v,
      vc: vc ?? this.vc,
      vcq: vcq ?? this.vcq,
      confirm: confirm ?? this.confirm,
      priceLevel: priceLevel ?? this.priceLevel,
    );
  }

  @override
  String toString() {
    return 'CandleModel{ts: $ts, o: $o, h: $h, l: $l, c: $c, v: $v, priceLevel: $priceLevel}';
  }
}
