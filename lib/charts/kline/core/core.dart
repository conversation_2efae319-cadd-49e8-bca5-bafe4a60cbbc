// Copyright 2024 <PERSON><PERSON>Zhao
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

library;

import 'dart:async';
import 'dart:math' as math;

import 'package:flexi_formatter/flexi_formatter.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/scheduler.dart';

import '../config/export.dart';
import '../constant.dart';
import '../data/kline_data.dart';
import '../extension/export.dart';
import '../framework/export.dart';
import '../model/export.dart';
import '../utils/platform_util.dart';
import '../../kline_chart/optimizations/gesture_debounce_optimizer.dart';
import '../../kline_chart/config/indicator_config/chart_config.dart';
import '../../kline_chart/themes/simple_kline_theme.dart';

part 'binding_base.dart';
part 'chart.dart';
part 'state.dart';
part 'setting.dart';
part 'cross.dart';
part 'common.dart';
part 'draw.dart';
part 'grid.dart';
