// Copyright 2024 Andy<PERSON>Zhao
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import 'dart:math' as math;
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

import '../core/core.dart';
import '../framework/configuration.dart';
import '../framework/logger.dart';
import '../kline_controller.dart';
import '../utils/platform_util.dart';
import 'non_touch_gesture_detector.dart';
import 'touch_gesture_detector.dart';

typedef MagnifierDecorationShapeBuilder = ShapeBorder Function(
  BuildContext context,
  BorderSide side,
);

class FlexiKlineWidget extends StatefulWidget {
  FlexiKlineWidget({
    super.key,
    required this.controller,
    this.alignment,
    this.decoration,
    this.foregroundDecoration,
    this.mainSize,
    this.mainForegroundViewBuilder,
    this.mainBackgroundView,
    bool? autoAdaptLayout,
    bool? isTouchDevice,
    this.onDoubleTap,
    this.drawToolbar,
    this.drawToolbarInitHeight = 50,
    this.magnifierDecorationShapeBuilder,
    this.exitZoomButtonBuilder,
    this.exitZoomButtonAlignment = AlignmentDirectional.bottomEnd,
    this.exitZoomButtonPadding = const EdgeInsetsDirectional.all(12),
  })  : isTouchDevice = isTouchDevice ?? PlatformUtil.isTouch,
        autoAdaptLayout = autoAdaptLayout ?? !PlatformUtil.isMobile;

  final FlexiKlineController controller;

  /// Container属性配置
  final AlignmentGeometry? alignment;
  final BoxDecoration? decoration;
  final Decoration? foregroundDecoration;

  /// 主区初始大小. 注: 仅在首次加载有效
  final Size? mainSize;

  /// 主区前台View构造器
  /// 用于扩展定制Loading/自定义按钮等
  final WidgetBuilder? mainForegroundViewBuilder;

  /// 主区后台View
  /// 用于扩展展示Logo/watermark等静态View
  final Widget? mainBackgroundView;

  /// 整个图表双击事件
  final GestureTapCallback? onDoubleTap;

  /// 绘制工具条仅在绘制完成或选中某个DrawOverlay时展示.
  final Widget? drawToolbar;

  /// 用于计算[drawToolbar]初始展示的位置向对于canvas底部的位置.
  final double drawToolbarInitHeight;

  /// 是否自动适配所在布局约束.
  /// 在可以动态调整窗口大小的设备上, 此值为true, 将会动态适配窗口的调整; 否则, 请自行控制.
  /// 非移动设备默认为true.
  final bool autoAdaptLayout;

  /// 是否是触摸设备.
  final bool isTouchDevice;

  /// 绘制点指针放大镜DecorationShape.
  final MagnifierDecorationShapeBuilder? magnifierDecorationShapeBuilder;

  /// 自定义退出指标缩放按钮
  final WidgetBuilder? exitZoomButtonBuilder;

  /// 退出指标缩放按钮Alignment
  final AlignmentGeometry exitZoomButtonAlignment;

  /// 缩放按钮Padding
  final EdgeInsetsGeometry exitZoomButtonPadding;

  @override
  State<FlexiKlineWidget> createState() => _FlexiKlineWidgetState();
}

class _FlexiKlineWidgetState extends State<FlexiKlineWidget>
    with WidgetsBindingObserver, KlineLog {
  @override
  String get logTag => 'FlexiKlineWidget';

  /// 绘制工具条globalKey: 用于获取其大小
  final GlobalKey _drawToolbarKey = GlobalKey();

  /// 绘制工具条位置
  late final ValueNotifier<Offset> _drawToolbarPosition;
  Offset get drawToolbarPosition => _drawToolbarPosition.value;

  FlexiKlineController get controller => widget.controller;

  IConfiguration get configuration => controller.configuration;

  IFlexiKlineTheme get theme => configuration.theme;

  @override
  void initState() {
    super.initState();

    loggerDelegate = controller.loggerDelegate;

    controller.initState();
    if (widget.mainSize != null) {
      controller.setMainSize(widget.mainSize!);
    }

    _drawToolbarPosition = ValueNotifier(
      configuration.getDrawToolbarPosition(),
    );
  }

  @override
  void didUpdateWidget(covariant FlexiKlineWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    logd('didUpdateWidget');
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    logd('didChangeDependencies');
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    logd('didChangeAppLifecycleState($state)');
    // if (state == AppLifecycleState.resumed) {
    // } else {
    // }
  }

  @override
  void didHaveMemoryPressure() {
    controller.cleanUnlessKlineData();
  }

  @override
  void dispose() {
    configuration.saveDrawToolbarPosition(drawToolbarPosition);
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 总是使用 LayoutBuilder 来获取父级约束
    return LayoutBuilder(
      builder: (context, constraints) {
        // 确定新的画布宽度
        // 如果父级提供了有界宽度，则使用它；否则，如果 controller 已有宽度且非无限，则保持；否则，可能需要一个默认值或抛错
        double newCanvasWidth;
        if (constraints.hasBoundedWidth) {
          newCanvasWidth = constraints.maxWidth;
        } else if (controller.canvasRect.width.isFinite && controller.canvasRect.width > 0) {
          newCanvasWidth = controller.canvasRect.width; // 父级无界，但 controller 有有效宽度，保持
          print('FlexiKlineWidget WARNING: Parent has unbounded width, but autoAdaptLayout is ${widget.autoAdaptLayout}. Using current controller width: $newCanvasWidth');
        } else {
          newCanvasWidth = 300; // 父级无界，controller也无有效宽度，使用一个默认最小宽度
          print('FlexiKlineWidget ERROR: Parent has unbounded width and controller has no valid width. Defaulting to $newCanvasWidth. autoAdaptLayout is ${widget.autoAdaptLayout}.');
        }

        // 高度处理类似，或使用 controller 现有高度
        // double newCanvasHeight = constraints.hasBoundedHeight ? constraints.maxHeight : controller.canvasRect.height;
        // 为了简化，我们假设高度的计算在 controller 内部是合理的，或者由 widget.mainSize (如果提供) 控制
        // 这里主要关注宽度的无限问题

        // 根据 widget.autoAdaptLayout 的原意来决定是否强制同步 mainSize
        // 但无论如何，我们都将获取到的宽度信息传递给 controller
        // 这需要 controller 的 set...Mode 方法能够处理好宽度的更新
        // 并正确更新其内部的 canvasRect 和 mainRect
        final currentLayoutMode = controller.layoutMode;
        final currentMainHeight = currentLayoutMode.mainSize?.height ?? controller.mainRect.height; // 获取当前主区域高度

        if (currentLayoutMode is FixedLayoutMode) {
          controller.setFixedLayoutMode(Size(
            newCanvasWidth,
            currentLayoutMode.fixedSize.height, // 保持原固定高度
          ));
        } else { // NormalLayoutMode or AdaptLayoutMode
          controller.setAdaptLayoutMode(
            Size(
              newCanvasWidth,
              currentMainHeight.isFinite && currentMainHeight > 0 ? currentMainHeight : 300, // 若高度无效也给个默认值
            ),
            // 当 autoAdaptLayout 为 true 时，我们期望 mainSize 也同步屏幕宽度变化
            // 当 autoAdaptLayout 为 false 时，mainSize 是否应该同步取决于具体需求，
            // 但至少 canvas 的宽度应该被更新
            syncMainSize: widget.autoAdaptLayout, 
          );
        }
        
        // 在调用 _buildKlineContainer 之前，controller 内部的 canvasRect 和 mainRect 应该已经被更新了
        return _buildKlineContainer(context);
      },
    );
  }

  Widget _buildKlineContainer(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: controller.canvasSizeChangeListener,
      builder: (context, canvasRect, child) {
        if (controller.drawState.isEditing) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _updateDrawToolbarPosition(drawToolbarPosition);
          });
        }
        return _buildKlineContent(context, canvasRect);
      },
    );
  }

  Widget _buildKlineContent(BuildContext context, Rect canvasRect) {
    // final canvasRect = controller.canvasRect;
    final canvasSize = canvasRect.size;
    final mainRect = controller.mainRect;

    // 调试打印
    // print('FlexiKlineWidget DEBUG: autoAdaptLayout=${widget.autoAdaptLayout}');
    // print('FlexiKlineWidget DEBUG: canvasRect=$canvasRect');
    // print('FlexiKlineWidget DEBUG: mainRect=$mainRect');

    return Container(
      alignment: widget.alignment,
      width: canvasRect.width,
      height: canvasRect.height,
      decoration: widget.decoration,
      foregroundDecoration: widget.foregroundDecoration,
      child: Stack(
        children: <Widget>[
          if (widget.mainBackgroundView != null) (() {
            Rect safeMainRect = mainRect;
            // 如果 mainRect 宽度无效，但 canvasRect 宽度有效，则尝试修正
            if (!mainRect.width.isFinite && canvasRect.width.isFinite) {
              print('FlexiKlineWidget WARNING: mainRect.width was not finite (${mainRect.width}). Correcting using canvasRect.width (${canvasRect.width}). Original mainRect: $mainRect');
              safeMainRect = Rect.fromLTWH(
                mainRect.left.isFinite ? mainRect.left : 0,
                mainRect.top.isFinite ? mainRect.top : 0,
                canvasRect.width, // 使用 canvasRect.width 作为宽度
                mainRect.height.isFinite ? mainRect.height : canvasRect.height, // 高度也做类似处理
              );
            }
            // 如果修正后的rect仍然无效（例如canvasRect本身也无效），则不渲染背景以避免崩溃
            if (!safeMainRect.width.isFinite || !safeMainRect.height.isFinite) {
              print('FlexiKlineWidget WARNING: safeMainRect is still not finite. Skipping mainBackgroundView. safeMainRect: $safeMainRect');
              return const SizedBox.shrink(); // 或者其他占位符
            }
            return Positioned.fromRect(
              key: const ValueKey('MainBackground'),
              rect: safeMainRect,
              child: widget.mainBackgroundView!,
            );
          })(),
          RepaintBoundary(
            key: const ValueKey('GridAndChartLayer'),
            child: CustomPaint(
              size: canvasSize,
              painter: GridPainter(
                controller: controller,
              ),
              foregroundPainter: ChartPainter(
                controller: controller,
              ),
              isComplex: true,
            ),
          ),
          RepaintBoundary(
            key: const ValueKey('DrawAndCrossLayer'),
            child: CustomPaint(
              size: canvasSize,
              painter: DrawPainter(
                controller: controller,
              ),
              foregroundPainter: CrossPainter(
                controller: controller,
              ),
              isComplex: true,
            ),
          ),
          widget.isTouchDevice
              ? TouchGestureDetector(
                  key: const ValueKey('TouchGestureDetector'),
                  controller: controller,
                  canvasSize: canvasSize,
                  onDoubleTap: widget.onDoubleTap,
                )
              : NonTouchGestureDetector(
                  key: const ValueKey('NonTouchGestureDetector'),
                  controller: controller,
                  onDoubleTap: widget.onDoubleTap,
                ),
          _buildMagnifier(context, canvasRect),
          Positioned.fromRect(
            rect: mainRect,
            child: _buildExitZoomButton(context, mainRect),
          ),
          _buildDrawToolbar(context, canvasRect),
          Positioned.fromRect(
            rect: mainRect,
            child: _buildMainForgroundView(context),
          ),
        ],
      ),
    );
  }

  Widget _buildMainForgroundView(BuildContext context) {
    if (widget.mainForegroundViewBuilder != null) {
      return widget.mainForegroundViewBuilder!(context);
    }

    return ValueListenableBuilder(
      valueListenable: controller.candleRequestListener,
      builder: (context, request, child) {
        return Offstage(
          offstage: !request.state.showLoading,
          child: Center(
            key: const ValueKey('loadingView'),
            child: SizedBox.square(
              dimension: controller.settingConfig.loading.size,
              child: CircularProgressIndicator(
                strokeWidth: controller.settingConfig.loading.strokeWidth,
                backgroundColor: controller.settingConfig.loading.background,
                valueColor: AlwaysStoppedAnimation<Color>(
                  controller.settingConfig.loading.valueColor,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  bool _updateDrawToolbarPosition(Offset newPosition) {
    final size = _drawToolbarKey.currentContext?.size;
    if (size != null && !size.isEmpty) {
      final canvasRect = controller.canvasRect;
      _drawToolbarPosition.value = Offset(
        newPosition.dx.clamp(
          canvasRect.left,
          math.max(canvasRect.left, canvasRect.right - size.width),
        ),
        newPosition.dy.clamp(
          canvasRect.top,
          math.max(canvasRect.top, canvasRect.bottom - size.height),
        ),
      );
      return true;
    }
    return false;
  }

  /// 绘制DrawToolBar
  Widget _buildDrawToolbar(BuildContext context, Rect canvasRect) {
    if (widget.drawToolbar == null) return const SizedBox.shrink();
    return ValueListenableBuilder(
      valueListenable: controller.drawStateListener,
      builder: (context, state, child) => Visibility(
        visible: state.isEditing,
        child: ValueListenableBuilder(
          valueListenable: _drawToolbarPosition,
          builder: (context, position, child) {
            if (position == Offset.infinite || !canvasRect.contains(position)) {
              // 如果position无效, 则重置其为当前canvas区域左下角.
              position = Offset(
                0,
                canvasRect.height - widget.drawToolbarInitHeight,
              );
            }
            return Positioned(
              left: position.dx,
              top: position.dy,
              child: MouseRegion(
                cursor: SystemMouseCursors.move,
                child: GestureDetector(
                  onPanUpdate: (DragUpdateDetails details) {
                    _updateDrawToolbarPosition(
                      position + details.delta,
                    );
                  },
                  onPanEnd: (event) {
                    configuration.saveDrawToolbarPosition(position);
                  },
                  child: SizedBox(
                    key: _drawToolbarKey,
                    child: widget.drawToolbar,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// 放大镜
  Widget _buildMagnifier(BuildContext context, Rect drawRect) {
    final config = controller.drawConfig.magnifier;
    if (!config.enable || config.size.isEmpty) {
      return const SizedBox.shrink();
    }

    return ValueListenableBuilder(
      valueListenable: controller.drawPointerListener,
      builder: (context, pointer, child) {
        bool visible = false;
        final pointerOffset = pointer?.offset;
        Offset focalPosition = Offset.zero;
        AlignmentGeometry alignment = AlignmentDirectional.topStart;
        EdgeInsets margin = config.margin;
        if (pointerOffset != null && pointerOffset.isFinite) {
          visible = true;
          Offset position;
          if (pointerOffset.dx > drawRect.width * 0.5) {
            alignment = AlignmentDirectional.topStart;
            position = config.size.center(margin.topLeft);
            position = Offset(
              drawRect.left + position.dx,
              drawRect.top + position.dy,
            );
          } else {
            alignment = AlignmentDirectional.topEnd;
            final valueTxtWidth =
                controller.drawState.object?.valueTicksSize?.width ?? 0;
            margin = margin.copyWith(right: margin.right + valueTxtWidth);
            position = Offset(
              drawRect.right - margin.right - config.size.width / 2,
              drawRect.top + margin.top + config.size.height / 2,
            );
          }
          focalPosition = pointerOffset - position;
        }
        return Visibility(
          key: const ValueKey('MagnifierVisibility'),
          visible: visible,
          child: Container(
            key: const ValueKey('MagnifierContainer'),
            alignment: alignment,
            margin: margin,
            child: RawMagnifier(
              key: const ValueKey('KlineRawMagnifier'),
              decoration: MagnifierDecoration(
                opacity: config.decorationOpactity,
                shadows: config.decorationShadows,
                shape: widget.magnifierDecorationShapeBuilder?.call(
                      context,
                      config.shapeSide.copyWith(color: theme.gridLine),
                    ) ??
                    CircleBorder(
                      side: config.shapeSide.copyWith(color: theme.gridLine),
                    ),
              ),
              size: config.size,
              focalPointOffset: focalPosition,
              magnificationScale: config.magnificationScale,
            ),
          ),
        );
      },
    );
  }

  /// 退出Zoom缩放按钮
  Widget _buildExitZoomButton(BuildContext context, Rect mainRect) {
    return ValueListenableBuilder(
      valueListenable: controller.chartStartZoomListener,
      builder: (context, isStartZomming, child) => Visibility(
        visible: isStartZomming,
        child: Container(
          alignment: widget.exitZoomButtonAlignment,
          padding: widget.exitZoomButtonPadding,
          child: widget.exitZoomButtonBuilder?.call(context) ??
              IconButton(
                onPressed: controller.exitChartZoom,
                constraints: const BoxConstraints(),
                style: IconButton.styleFrom(
                  padding: EdgeInsets.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  fixedSize: Size(20, 20),
                  foregroundColor: theme.tooltipTextColor,
                  backgroundColor: theme.tooltipBg.withAlpha(
                    (0xFF * 0.8).round(),
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                  side: BorderSide(color: theme.gridLine, width: 1),
                ),
                icon: Text('A', style: TextStyle(fontSize: 12)),
              ),
        ),
      ),
    );
  }
}

class GridPainter extends CustomPainter {
  GridPainter({
    required this.controller,
  }) : super(repaint: controller.repaintGridBg);

  final FlexiKlineController controller;

  @override
  void paint(Canvas canvas, Size size) {
    try {
      controller.paintGrid(canvas, size);
    } catch (e) {
      // 忽略绘制错误
      if (kDebugMode) {
        print('GridPainter error: $e');
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is! GridPainter) return true;
    return oldDelegate.controller != controller;
  }
}

class ChartPainter extends CustomPainter {
  ChartPainter({
    required this.controller,
  }) : super(repaint: controller.repaintChart);

  final FlexiKlineController controller;

  @override
  void paint(Canvas canvas, Size size) {
    Timeline.startSync("Flexi-PaintChart");

    try {
      // 只在数据可用时绘制
      if (controller.curKlineData.canPaintChart) {
        controller.paintChart(canvas, size);
      }
    } catch (e) {
      // 忽略绘制错误，避免崩溃
      if (kDebugMode) {
        print('ChartPainter error: $e');
      }
    } finally {
      Timeline.finishSync();
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    // 优化重绘判断
    if (oldDelegate is! ChartPainter) return true;
    return oldDelegate.controller != controller;
  }
}

class DrawPainter extends CustomPainter {
  DrawPainter({
    required this.controller,
  }) : super(repaint: controller.repaintDraw);

  final FlexiKlineController controller;

  @override
  void paint(Canvas canvas, Size size) {
    if (!controller.isDrawVisibility) return;

    try {
      canvas.save();
      canvas.clipRect(controller.mainRect);

      controller.paintDraw(canvas, size);
      controller.drawStateAxisTicksText(canvas, size);
    } catch (e) {
      // 忽略绘制错误，继续正常流程
    } finally {
      canvas.restore();
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is! DrawPainter) return true;
    return oldDelegate.controller != controller ||
           controller.isDrawVisibility != (oldDelegate.controller.isDrawVisibility);
  }
}

class CrossPainter extends CustomPainter {
  CrossPainter({
    required this.controller,
  }) : super(repaint: controller.repaintCross);

  final FlexiKlineController controller;

  @override
  void paint(Canvas canvas, Size size) {
    // 只在十字线可见时绘制
    if (!controller.isCrossing) return;

    try {
      canvas.save();
      canvas.clipRect(controller.canvasRect);

      controller.paintCross(canvas, size);
    } catch (e) {
      // 忽略绘制错误
      if (kDebugMode) {
        print('CrossPainter error: $e');
      }
    } finally {
      canvas.restore();
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is! CrossPainter) return true;
    return oldDelegate.controller != controller ||
           controller.isCrossing != (oldDelegate.controller.isCrossing);
  }
}
