// Copyright 2024 <PERSON><PERSON>Zhao
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

library flexi_kline;

export 'core/core.dart';
export 'constant.dart';
export 'config/export.dart';
export 'data/kline_data.dart';
export 'extension/export.dart';
export 'framework/export.dart'
    hide
        PaintDelegateExt,
        MainPaintDelegateExt,
        MainPaintManagerExt,
        IConfigurationExt;
export 'indicators/export.dart';
export 'model/export.dart' hide GestureData;
export 'utils/export.dart';
export 'view/flexi_kline_widget.dart';
export 'kline_controller.dart';
export 'flexi_kline_page.dart';
