/*
  语言选择底部弹窗
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/utils/index.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:remixicon/remixicon.dart';
import '../managers/language_manager.dart';
import '../generated/app_localizations.dart';

class LanguageSwitcher {
  // 显示语言选择底部弹窗
  static Future<void> show(
    BuildContext context, {
    bool showHeader = true,
  }) async {
    final localizations = AppLocalizations.of(context);

    await BottomSheetWidget.show(
      context: context,
      title: "语言", // TODO: 添加到ARB文件
      showCloseButton: true,
      showBottomButton: false,
      showHeader: showHeader,
      showHeaderBorder: false,
      child: _LanguageSelectionContent(),
    );
  }
}

class _LanguageSelectionContent extends StatefulWidget {
  @override
  _LanguageSelectionContentState createState() =>
      _LanguageSelectionContentState();
}

class _LanguageSelectionContentState extends State<_LanguageSelectionContent> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: ScreenUtil.getScreenInfo(context)['screenHeight'] * 0.7,
      padding: EdgeInsets.only(top: UiConstants.spacing20),
      child: Consumer<LanguageManager>(
        builder: (context, languageManager, child) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 语言选项列表
              ...languageManager.supportedLanguages.map((locale) {
                return _buildLanguageItem(locale, languageManager);
              }),

              SizedBox(height: UiConstants.spacing16),
            ],
          );
        },
      ),
    );
  }

  // 构建语言选项
  Widget _buildLanguageItem(Locale locale, LanguageManager languageManager) {
    final isSelected = languageManager.isCurrentLanguage(locale);

    return InkWellWidget(
      onTap: () async {
        if (!isSelected) {
          try {
            await languageManager.changeLanguage(locale);

            // 关闭语言选择弹窗
            if (mounted) {
              Navigator.of(context).pop();
            }
          } catch (e) {
            // 显示错误信息
            debugPrint('切换失败 $e');
          }
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: UiConstants.spacing16),
        child: Row(
          children: [
            // 语言名称
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 原生名称
                  Text(
                    languageManager.getLanguageNativeName(locale),
                    style: context.templateStyle.text.bodyText,
                  ),
                ],
              ),
            ),

            // 选中状态图标
            if (isSelected)
              Icon(
                RemixIcons.check_line,
                size: UiConstants.iconSize20,
                color: context.templateColors.primary,
              ),
          ],
        ),
      ),
    );
  }
}
