import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 语言管理器
/// 
/// 负责管理应用的多语言设置，包括：
/// - 语言切换
/// - 语言持久化存储
/// - 支持的语言列表管理
class LanguageManager extends ChangeNotifier {
  static const String _languageKey = 'selected_language';
  static const String _countryKey = 'selected_country';

  Locale _currentLocale = const Locale('zh'); // 默认简体中文

  // 单例模式
  static final LanguageManager _instance = LanguageManager._internal();
  factory LanguageManager() => _instance;
  LanguageManager._internal();

  /// 获取当前语言
  Locale get currentLocale => _currentLocale;

  /// 支持的语言列表
  static const List<Locale> supportedLocales = [
    Locale('zh'),      // 简体中文
    Locale('zh', 'TW'), // 繁体中文
    Locale('en'),      // 英语
  ];

  /// 获取支持的语言列表
  List<Locale> get supportedLanguages => supportedLocales;

  /// 初始化语言设置
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString(_languageKey);
    final countryCode = prefs.getString(_countryKey);

    if (languageCode != null) {
      _currentLocale = Locale(languageCode, countryCode);
      notifyListeners();
    }
  }

  /// 切换语言
  Future<void> changeLanguage(Locale locale) async {
    if (_currentLocale == locale) return;

    _currentLocale = locale;

    // 保存到本地存储
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, locale.languageCode);
    if (locale.countryCode != null) {
      await prefs.setString(_countryKey, locale.countryCode!);
    } else {
      await prefs.remove(_countryKey);
    }

    notifyListeners();
  }

  /// 获取语言原生名称
  String getLanguageNativeName(Locale locale) {
    final localeString = locale.countryCode != null 
        ? '${locale.languageCode}_${locale.countryCode}'
        : locale.languageCode;
        
    switch (localeString) {
      case 'zh':
        return '简体中文';
      case 'zh_TW':
        return '繁體中文';
      case 'en':
        return 'English';
      default:
        return locale.toString();
    }
  }

  /// 是否为当前语言
  bool isCurrentLanguage(Locale locale) {
    return _currentLocale.languageCode == locale.languageCode &&
        _currentLocale.countryCode == locale.countryCode;
  }
}
