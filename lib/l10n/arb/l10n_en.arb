{"@@locale": "en", "appname": "ZhiFang Exchange", "welcomeTitle": "Welcome to\\nZhiFang Exchange", "welcomeSubtitle": "Smart trading, intelligent transformation", "agreeAndContinue": "Agree and continue", "continueMeansAgree": "By continuing, you agree to the", "userAgreement": "User Agreement", "and": "and", "privacyPolicy": "Privacy Policy.", "disagreeCloseApp": "If you disagree, please close the application", "welcomeBack": "Welcome back!", "register": "Register", "emailOrPhone": "Email / Phone Number", "subAccount": "Sub-account", "password": "Password", "forgotPassword": "Forgot your password?", "nextStep": "Next", "login": "<PERSON><PERSON>", "loginViaOther": "Or login with", "switchCaptchaMethod": "Switch CAPTCHA method", "switchVerificationMethod": "Switch verification method", "geetestVerification": "Geetest Verification", "googleVerification": "Google Authenticator", "welcomeToApp": "Welcome to {appname}", "email": "Email", "phoneNumber": "Phone Number", "enterPhoneNumber": "Please enter your phone number", "enterEmail": "Please enter your email address", "inviteCodeOptional": "Invitation Code (Optional)", "enterInviteCode": "Enter invitation code (optional)", "paste": "Paste", "agreeToTerms": "I have read and agree to {appname}'s", "createAccount": "Create Account", "pleaseAgreeToTerms": "Please read and accept the terms first", "bigGift": "{amount}{currency} welcome bonus, claim upon registration!", "registerOrLogin": "Geetest started", "viewDetails": "View Details", "guaranteeAndService": "Guarantee & Services", "viewMore": "View More", "goNow": "Go Now", "homePage": "Home", "market": "Market", "trade": "Trade", "wealthManagement": "<PERSON><PERSON><PERSON>", "assets": "Assets", "welcomeMain": "Welcome", "tradeSlogan": "Trade and exchange cryptocurrencies on {appname}", "quickEntry": "Quick Access", "helpCenter": "Help Center", "customerSupport": "Support", "academyEntry": "Academy", "protectionFund": "Protection Fund", "aboutAppname": "About {appname}", "preferences": "Preferences", "language": "Language", "langSimplified": "Simplified Chinese", "langTraditional": "Traditional Chinese", "langEnglish": "English", "fiatCurrency": "Fiat Currency", "theme": "Theme", "themeModeLight": "Light Mode", "themeModeDark": "Dark Mode", "themeModeSystem": "Follow System", "cancel": "Cancel", "priceColorMode": "Price Color Mode", "colorRedUp": "Red Up, Green Down", "colorGreenUp": "Green Up, Red Down", "changeBasis": "P/L Calculation Basis", "changeBasisNotice": "Switching timezones will recalculate price changes (%) in spot, margin, and futures markets. K-line charts will remain unchanged.", "last24h": "Last 24 Hours", "currentTimezone": "Current Timezone", "watchlist": "Watchlist", "onchain": "On-chain", "opportunity": "Opportunities", "insight": "Insights", "spotMarket": "Spot", "futuresMarket": "Futures", "leverageMarket": "Leverage", "liveMonitor": "Live Watch", "priceAlert": "Price Alert", "logoSetting": "Logo Setting", "token": "Token", "tradeVolume": "Trading Volume", "latestPrice": "Latest Price", "changePercent": "Change %", "comingSoon": "Coming Soon", "onlineTime": "Listing Time", "addToWatchlist": "Add to Watchlist", "hotNewsImpact": "Hot topic triggers {crypto}, speculative buying surges, token price soars.", "featured": "Featured", "newlyListed": "New", "totalMarketCap": "Total Market Cap", "totalVolume": "Total Volume", "holderCount": "Holders", "onchainVolume": "On-chain Volume", "price": "Price", "priceChange": "Change", "passwordLengthRule": "8–32 characters", "passwordUppercaseRule": "At least one uppercase letter", "passwordNumberRule": "At least one number", "passwordSymbolRule": "At least one special character (allowed: `!@#\\$%^&*()_+-=[]|;:<>?/`)", "passwordInvalidFormat": "Password format is incorrect or contains unsupported characters. Please try again.", "mustAgreeToPolicy": "Please agree to the User Agreement and Privacy Policy first", "enterValidEmail": "Please enter a valid email address", "enterPhone": "Please enter your phone number", "enterValidPhone": "Please enter a valid phone number", "enterPassword": "Please enter your password", "passwordLengthError": "Password must be 8–32 characters long", "passwordMissingUppercase": "Password must contain at least one uppercase letter", "passwordMissingNumber": "Password must contain at least one number", "passwordMissingSymbol": "Password must contain at least one special character", "iAgreeTo": "I have read and agree to the", "userAgreementOne": "User Agreement", "privacyPolicyOne": "Privacy Policy"}