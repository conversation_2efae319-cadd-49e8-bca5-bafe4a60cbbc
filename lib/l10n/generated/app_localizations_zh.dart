/// Generated file. Do not edit.
///
/// This file is generated by the Flutter Intl plugin.
/// For more information, see: https://flutter.dev/docs/development/accessibility-and-localization/internationalization

// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appname => '智方交易所';

  @override
  String get welcomeTitle => '欢迎来到\n智方交易所';

  @override
  String get welcomeSubtitle => '智方交易，交易\"智\"变';

  @override
  String get agreeAndContinue => '同意并继续';

  @override
  String get continueMeansAgree => '继续使用即代表您同意';

  @override
  String get userAgreement => '用户协议';

  @override
  String get and => '和';

  @override
  String get privacyPolicy => '个人隐私政策。';

  @override
  String get disagreeCloseApp => '如不同意，请关闭应用程序';

  @override
  String get welcomeBack => '欢迎回来！';

  @override
  String get register => '注册';

  @override
  String get emailOrPhone => '邮箱/手机号码';

  @override
  String get subAccount => '子账户';

  @override
  String get password => '密码';

  @override
  String get forgotPassword => '忘记您的密码了吗？';

  @override
  String get nextStep => '下一步';

  @override
  String get login => '登录';

  @override
  String get loginViaOther => '或通过以下方式';

  @override
  String get switchCaptchaMethod => '切换人机验证方法';

  @override
  String get switchVerificationMethod => '切换验证方式';

  @override
  String get geetestVerification => '极验验证';

  @override
  String get googleVerification => '谷歌验证';

  @override
  String welcomeToApp(Object appname) {
    return '欢迎来到$appname';
  }

  @override
  String get email => '邮箱';

  @override
  String get phoneNumber => '手机号码';

  @override
  String get enterPhoneNumber => '请输入您的手机号码';

  @override
  String get enterEmail => '请输入您的电子邮箱';

  @override
  String get inviteCodeOptional => '邀请码（可选）';

  @override
  String get enterInviteCode => '请输入邀请码（选填）';

  @override
  String get paste => '粘贴';

  @override
  String agreeToTerms(Object appname) {
    return '我已阅读并同意 $appname 的';
  }

  @override
  String get createAccount => '创建账号';

  @override
  String get pleaseAgreeToTerms => '请先阅读协议并勾选同意';

  @override
  String bigGift(Object amount, Object currency) {
    return '$amount$currency 大礼包，注册即刻领取！';
  }

  @override
  String get registerOrLogin => '注册/登录';

  @override
  String get viewDetails => '查看详情';

  @override
  String get guaranteeAndService => '保障与服务';

  @override
  String get viewMore => '查看更多';

  @override
  String get goNow => '立即前往';

  @override
  String get homePage => '首页';

  @override
  String get market => '行情';

  @override
  String get trade => '交易';

  @override
  String get wealthManagement => '理财';

  @override
  String get assets => '资产';

  @override
  String get welcomeMain => '欢迎';

  @override
  String tradeSlogan(Object appname) {
    return '在 $appname 进行加密货币买卖和交易';
  }

  @override
  String get quickEntry => '快捷入口';

  @override
  String get helpCenter => '帮助中心';

  @override
  String get customerSupport => '客服';

  @override
  String get academyEntry => '学院';

  @override
  String get protectionFund => '保护基金';

  @override
  String aboutAppname(Object appname) {
    return '关于 $appname';
  }

  @override
  String get preferences => '偏好设置';

  @override
  String get language => '语言';

  @override
  String get langSimplified => '简体中文';

  @override
  String get langTraditional => '繁体中文';

  @override
  String get langEnglish => '英语';

  @override
  String get fiatCurrency => '计价币种';

  @override
  String get theme => '主题';

  @override
  String get themeModeLight => '白天模式';

  @override
  String get themeModeDark => '夜间模式';

  @override
  String get themeModeSystem => '跟随系统';

  @override
  String get cancel => '取消';

  @override
  String get priceColorMode => '涨跌颜色';

  @override
  String get colorRedUp => '红涨绿跌';

  @override
  String get colorGreenUp => '绿涨红跌';

  @override
  String get changeBasis => '涨跌幅基准';

  @override
  String get changeBasisNotice =>
      '切换到新时区后，现货、保证金、合约市场的报价变化（%）将会重新计算，但不会影响 K 线。';

  @override
  String get last24h => '最近 24 小时';

  @override
  String get currentTimezone => '当前时区';

  @override
  String get watchlist => '自选';

  @override
  String get onchain => '链上交易';

  @override
  String get opportunity => '机会';

  @override
  String get insight => '观点';

  @override
  String get spotMarket => '现货';

  @override
  String get futuresMarket => '合约';

  @override
  String get leverageMarket => '杠杆';

  @override
  String get liveMonitor => '实时盯盘';

  @override
  String get priceAlert => '价格提醒';

  @override
  String get logoSetting => 'Logo 设置';

  @override
  String get token => '币种';

  @override
  String get tradeVolume => '交易额';

  @override
  String get latestPrice => '最新价';

  @override
  String get changePercent => '涨跌幅';

  @override
  String get comingSoon => '即将开盘';

  @override
  String get onlineTime => '上线时间';

  @override
  String get addToWatchlist => '加入自选';

  @override
  String hotNewsImpact(Object crypto) {
    return '热议引发 $crypto，投机买入多，代币价飙升。';
  }

  @override
  String get featured => '精选';

  @override
  String get newlyListed => '新上';

  @override
  String get totalMarketCap => '总市值';

  @override
  String get totalVolume => '成交额';

  @override
  String get holderCount => '持有人数';

  @override
  String get onchainVolume => '链上成交额';

  @override
  String get price => '价格';

  @override
  String get priceChange => '涨跌';

  @override
  String get passwordLengthRule => '8-32个字符';

  @override
  String get passwordUppercaseRule => '至少一个大写字母';

  @override
  String get passwordNumberRule => '至少一个数字';

  @override
  String get passwordSymbolRule =>
      '至少一个特殊符号 (仅支持：`!@#\\\$%^&*()_+-=[]|;:<>?/`)';

  @override
  String get passwordInvalidFormat => '密码格式错误或包含不支持的特殊字符，请重新输入';

  @override
  String get mustAgreeToPolicy => '请先同意用户协议和隐私政策';

  @override
  String get enterValidEmail => '请输入有效的邮箱地址';

  @override
  String get enterPhone => '请输入手机号码';

  @override
  String get enterValidPhone => '请输入有效的手机号码';

  @override
  String get enterPassword => '请输入密码';

  @override
  String get passwordLengthError => '密码长度必须为8-32个字符';

  @override
  String get passwordMissingUppercase => '密码必须包含至少一个大写字母';

  @override
  String get passwordMissingNumber => '密码必须包含至少一个数字';

  @override
  String get passwordMissingSymbol => '密码必须包含至少一个特殊符号';

  @override
  String get iAgreeTo => '我已阅读并同意';

  @override
  String get userAgreementOne => '用户协议';

  @override
  String get privacyPolicyOne => '隐私政策';
}

/// The translations for Chinese, as used in Taiwan (`zh_TW`).
class AppLocalizationsZhTw extends AppLocalizationsZh {
  AppLocalizationsZhTw() : super('zh_TW');

  @override
  String get appname => '智方交易所';

  @override
  String get welcomeTitle => '歡迎來到\\n智方交易所';

  @override
  String get welcomeSubtitle => '智方交易，交易「智」變';

  @override
  String get agreeAndContinue => '同意並繼續';

  @override
  String get continueMeansAgree => '繼續使用即代表您同意';

  @override
  String get userAgreement => '用戶協議';

  @override
  String get and => '和';

  @override
  String get privacyPolicy => '個人隱私政策。';

  @override
  String get disagreeCloseApp => '如不同意，請關閉應用程式';

  @override
  String get welcomeBack => '歡迎回來！';

  @override
  String get register => '註冊';

  @override
  String get emailOrPhone => '郵箱 / 手機號碼';

  @override
  String get subAccount => '子帳號';

  @override
  String get password => '密碼';

  @override
  String get forgotPassword => '忘記您的密碼了嗎？';

  @override
  String get nextStep => '下一步';

  @override
  String get login => '登入';

  @override
  String get loginViaOther => '或透過以下方式登入';

  @override
  String get switchCaptchaMethod => '切換驗證方式';

  @override
  String get switchVerificationMethod => '切換驗證方式';

  @override
  String get geetestVerification => '極驗驗證';

  @override
  String get googleVerification => 'Google 驗證';

  @override
  String welcomeToApp(Object appname) {
    return '歡迎來到$appname';
  }

  @override
  String get email => '郵箱';

  @override
  String get phoneNumber => '手機號碼';

  @override
  String get enterPhoneNumber => '請輸入您的手機號碼';

  @override
  String get enterEmail => '請輸入您的電子郵箱';

  @override
  String get inviteCodeOptional => '邀請碼（選填）';

  @override
  String get enterInviteCode => '請輸入邀請碼（選填）';

  @override
  String get paste => '貼上';

  @override
  String agreeToTerms(Object appname) {
    return '我已閱讀並同意 $appname 的';
  }

  @override
  String get createAccount => '創建帳號';

  @override
  String get pleaseAgreeToTerms => '請先閱讀協議並勾選同意';

  @override
  String bigGift(Object amount, Object currency) {
    return '$amount$currency 大禮包，註冊即刻領取！';
  }

  @override
  String get registerOrLogin => '註冊 / 登入';

  @override
  String get viewDetails => '查看詳情';

  @override
  String get guaranteeAndService => '保障與服務';

  @override
  String get viewMore => '查看更多';

  @override
  String get goNow => '立即前往';

  @override
  String get homePage => '首頁';

  @override
  String get market => '行情';

  @override
  String get trade => '交易';

  @override
  String get wealthManagement => '理財';

  @override
  String get assets => '資產';

  @override
  String get welcomeMain => '歡迎';

  @override
  String tradeSlogan(Object appname) {
    return '在 $appname 進行加密貨幣買賣和交易';
  }

  @override
  String get quickEntry => '快捷入口';

  @override
  String get helpCenter => '幫助中心';

  @override
  String get customerSupport => '客服';

  @override
  String get academyEntry => '學院';

  @override
  String get protectionFund => '保障基金';

  @override
  String aboutAppname(Object appname) {
    return '關於 $appname';
  }

  @override
  String get preferences => '偏好設置';

  @override
  String get language => '語言';

  @override
  String get langSimplified => '簡體中文';

  @override
  String get langTraditional => '繁體中文';

  @override
  String get langEnglish => '英文';

  @override
  String get fiatCurrency => '計價幣種';

  @override
  String get theme => '主題';

  @override
  String get themeModeLight => '白天模式';

  @override
  String get themeModeDark => '夜間模式';

  @override
  String get themeModeSystem => '跟隨系統';

  @override
  String get cancel => '取消';

  @override
  String get priceColorMode => '漲跌顏色';

  @override
  String get colorRedUp => '紅漲綠跌';

  @override
  String get colorGreenUp => '綠漲紅跌';

  @override
  String get changeBasis => '漲跌幅基準';

  @override
  String get changeBasisNotice => '切換到新時區後，現貨、保證金、合約市場的報價變化（%）將會重新計算，但不影響 K 線。';

  @override
  String get last24h => '最近 24 小時';

  @override
  String get currentTimezone => '當前時區';

  @override
  String get watchlist => '自選';

  @override
  String get onchain => '鏈上交易';

  @override
  String get opportunity => '機會';

  @override
  String get insight => '觀點';

  @override
  String get spotMarket => '現貨';

  @override
  String get futuresMarket => '合約';

  @override
  String get leverageMarket => '槓桿';

  @override
  String get liveMonitor => '實時盯盤';

  @override
  String get priceAlert => '價格提醒';

  @override
  String get logoSetting => 'Logo 設定';

  @override
  String get token => '幣種';

  @override
  String get tradeVolume => '交易額';

  @override
  String get latestPrice => '最新價格';

  @override
  String get changePercent => '漲跌幅';

  @override
  String get comingSoon => '即將開盤';

  @override
  String get onlineTime => '上線時間';

  @override
  String get addToWatchlist => '加入自選';

  @override
  String hotNewsImpact(Object crypto) {
    return '熱議引發 $crypto，投機買入多，代幣價格飆升。';
  }

  @override
  String get featured => '精選';

  @override
  String get newlyListed => '新上';

  @override
  String get totalMarketCap => '總市值';

  @override
  String get totalVolume => '成交額';

  @override
  String get holderCount => '持有人數';

  @override
  String get onchainVolume => '鏈上成交額';

  @override
  String get price => '價格';

  @override
  String get priceChange => '漲跌';

  @override
  String get passwordLengthRule => '8-32個字元';

  @override
  String get passwordUppercaseRule => '至少一個大寫字母';

  @override
  String get passwordNumberRule => '至少一個數字';

  @override
  String get passwordSymbolRule => '至少一個特殊符號（僅支援：`!@#\\\$%^&*()_+-=[]|;:<>?/`）';

  @override
  String get passwordInvalidFormat => '密碼格式錯誤或包含不支援的特殊字元，請重新輸入';

  @override
  String get mustAgreeToPolicy => '請先同意使用者協議和隱私政策';

  @override
  String get enterValidEmail => '請輸入有效的電子郵件地址';

  @override
  String get enterPhone => '請輸入手機號碼';

  @override
  String get enterValidPhone => '請輸入有效的手機號碼';

  @override
  String get enterPassword => '請輸入密碼';

  @override
  String get passwordLengthError => '密碼長度必須為8-32個字元';

  @override
  String get passwordMissingUppercase => '密碼必須包含至少一個大寫字母';

  @override
  String get passwordMissingNumber => '密碼必須包含至少一個數字';

  @override
  String get passwordMissingSymbol => '密碼必須包含至少一個特殊符號';

  @override
  String get iAgreeTo => '我已閱讀並同意';

  @override
  String get userAgreementOne => '使用者協議';

  @override
  String get privacyPolicyOne => '隱私政策';
}
