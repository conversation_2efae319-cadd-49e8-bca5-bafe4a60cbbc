/// Generated file. Do not edit.
///
/// This file is generated by the Flutter Intl plugin.
/// For more information, see: https://flutter.dev/docs/development/accessibility-and-localization/internationalization

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('zh'),
    Locale('zh', 'TW'),
  ];

  /// No description provided for @appname.
  ///
  /// In zh, this message translates to:
  /// **'智方交易所'**
  String get appname;

  /// No description provided for @welcomeTitle.
  ///
  /// In zh, this message translates to:
  /// **'欢迎来到\n智方交易所'**
  String get welcomeTitle;

  /// No description provided for @welcomeSubtitle.
  ///
  /// In zh, this message translates to:
  /// **'智方交易，交易\"智\"变'**
  String get welcomeSubtitle;

  /// No description provided for @agreeAndContinue.
  ///
  /// In zh, this message translates to:
  /// **'同意并继续'**
  String get agreeAndContinue;

  /// No description provided for @continueMeansAgree.
  ///
  /// In zh, this message translates to:
  /// **'继续使用即代表您同意'**
  String get continueMeansAgree;

  /// No description provided for @userAgreement.
  ///
  /// In zh, this message translates to:
  /// **'用户协议'**
  String get userAgreement;

  /// No description provided for @and.
  ///
  /// In zh, this message translates to:
  /// **'和'**
  String get and;

  /// No description provided for @privacyPolicy.
  ///
  /// In zh, this message translates to:
  /// **'个人隐私政策。'**
  String get privacyPolicy;

  /// No description provided for @disagreeCloseApp.
  ///
  /// In zh, this message translates to:
  /// **'如不同意，请关闭应用程序'**
  String get disagreeCloseApp;

  /// No description provided for @welcomeBack.
  ///
  /// In zh, this message translates to:
  /// **'欢迎回来！'**
  String get welcomeBack;

  /// No description provided for @register.
  ///
  /// In zh, this message translates to:
  /// **'注册'**
  String get register;

  /// No description provided for @emailOrPhone.
  ///
  /// In zh, this message translates to:
  /// **'邮箱/手机号码'**
  String get emailOrPhone;

  /// No description provided for @subAccount.
  ///
  /// In zh, this message translates to:
  /// **'子账户'**
  String get subAccount;

  /// No description provided for @password.
  ///
  /// In zh, this message translates to:
  /// **'密码'**
  String get password;

  /// No description provided for @forgotPassword.
  ///
  /// In zh, this message translates to:
  /// **'忘记您的密码了吗？'**
  String get forgotPassword;

  /// No description provided for @nextStep.
  ///
  /// In zh, this message translates to:
  /// **'下一步'**
  String get nextStep;

  /// No description provided for @login.
  ///
  /// In zh, this message translates to:
  /// **'登录'**
  String get login;

  /// No description provided for @loginViaOther.
  ///
  /// In zh, this message translates to:
  /// **'或通过以下方式'**
  String get loginViaOther;

  /// No description provided for @switchCaptchaMethod.
  ///
  /// In zh, this message translates to:
  /// **'切换人机验证方法'**
  String get switchCaptchaMethod;

  /// No description provided for @switchVerificationMethod.
  ///
  /// In zh, this message translates to:
  /// **'切换验证方式'**
  String get switchVerificationMethod;

  /// No description provided for @geetestVerification.
  ///
  /// In zh, this message translates to:
  /// **'极验验证'**
  String get geetestVerification;

  /// No description provided for @googleVerification.
  ///
  /// In zh, this message translates to:
  /// **'谷歌验证'**
  String get googleVerification;

  /// No description provided for @welcomeToApp.
  ///
  /// In zh, this message translates to:
  /// **'欢迎来到{appname}'**
  String welcomeToApp(Object appname);

  /// No description provided for @email.
  ///
  /// In zh, this message translates to:
  /// **'邮箱'**
  String get email;

  /// No description provided for @phoneNumber.
  ///
  /// In zh, this message translates to:
  /// **'手机号码'**
  String get phoneNumber;

  /// No description provided for @enterPhoneNumber.
  ///
  /// In zh, this message translates to:
  /// **'请输入您的手机号码'**
  String get enterPhoneNumber;

  /// No description provided for @enterEmail.
  ///
  /// In zh, this message translates to:
  /// **'请输入您的电子邮箱'**
  String get enterEmail;

  /// No description provided for @inviteCodeOptional.
  ///
  /// In zh, this message translates to:
  /// **'邀请码（可选）'**
  String get inviteCodeOptional;

  /// No description provided for @enterInviteCode.
  ///
  /// In zh, this message translates to:
  /// **'请输入邀请码（选填）'**
  String get enterInviteCode;

  /// No description provided for @paste.
  ///
  /// In zh, this message translates to:
  /// **'粘贴'**
  String get paste;

  /// No description provided for @agreeToTerms.
  ///
  /// In zh, this message translates to:
  /// **'我已阅读并同意 {appname} 的'**
  String agreeToTerms(Object appname);

  /// No description provided for @createAccount.
  ///
  /// In zh, this message translates to:
  /// **'创建账号'**
  String get createAccount;

  /// No description provided for @pleaseAgreeToTerms.
  ///
  /// In zh, this message translates to:
  /// **'请先阅读协议并勾选同意'**
  String get pleaseAgreeToTerms;

  /// No description provided for @bigGift.
  ///
  /// In zh, this message translates to:
  /// **'{amount}{currency} 大礼包，注册即刻领取！'**
  String bigGift(Object amount, Object currency);

  /// No description provided for @registerOrLogin.
  ///
  /// In zh, this message translates to:
  /// **'注册/登录'**
  String get registerOrLogin;

  /// No description provided for @viewDetails.
  ///
  /// In zh, this message translates to:
  /// **'查看详情'**
  String get viewDetails;

  /// No description provided for @guaranteeAndService.
  ///
  /// In zh, this message translates to:
  /// **'保障与服务'**
  String get guaranteeAndService;

  /// No description provided for @viewMore.
  ///
  /// In zh, this message translates to:
  /// **'查看更多'**
  String get viewMore;

  /// No description provided for @goNow.
  ///
  /// In zh, this message translates to:
  /// **'立即前往'**
  String get goNow;

  /// No description provided for @homePage.
  ///
  /// In zh, this message translates to:
  /// **'首页'**
  String get homePage;

  /// No description provided for @market.
  ///
  /// In zh, this message translates to:
  /// **'行情'**
  String get market;

  /// No description provided for @trade.
  ///
  /// In zh, this message translates to:
  /// **'交易'**
  String get trade;

  /// No description provided for @wealthManagement.
  ///
  /// In zh, this message translates to:
  /// **'理财'**
  String get wealthManagement;

  /// No description provided for @assets.
  ///
  /// In zh, this message translates to:
  /// **'资产'**
  String get assets;

  /// No description provided for @welcomeMain.
  ///
  /// In zh, this message translates to:
  /// **'欢迎'**
  String get welcomeMain;

  /// No description provided for @tradeSlogan.
  ///
  /// In zh, this message translates to:
  /// **'在 {appname} 进行加密货币买卖和交易'**
  String tradeSlogan(Object appname);

  /// No description provided for @quickEntry.
  ///
  /// In zh, this message translates to:
  /// **'快捷入口'**
  String get quickEntry;

  /// No description provided for @helpCenter.
  ///
  /// In zh, this message translates to:
  /// **'帮助中心'**
  String get helpCenter;

  /// No description provided for @customerSupport.
  ///
  /// In zh, this message translates to:
  /// **'客服'**
  String get customerSupport;

  /// No description provided for @academyEntry.
  ///
  /// In zh, this message translates to:
  /// **'学院'**
  String get academyEntry;

  /// No description provided for @protectionFund.
  ///
  /// In zh, this message translates to:
  /// **'保护基金'**
  String get protectionFund;

  /// No description provided for @aboutAppname.
  ///
  /// In zh, this message translates to:
  /// **'关于 {appname}'**
  String aboutAppname(Object appname);

  /// No description provided for @preferences.
  ///
  /// In zh, this message translates to:
  /// **'偏好设置'**
  String get preferences;

  /// No description provided for @language.
  ///
  /// In zh, this message translates to:
  /// **'语言'**
  String get language;

  /// No description provided for @langSimplified.
  ///
  /// In zh, this message translates to:
  /// **'简体中文'**
  String get langSimplified;

  /// No description provided for @langTraditional.
  ///
  /// In zh, this message translates to:
  /// **'繁体中文'**
  String get langTraditional;

  /// No description provided for @langEnglish.
  ///
  /// In zh, this message translates to:
  /// **'英语'**
  String get langEnglish;

  /// No description provided for @fiatCurrency.
  ///
  /// In zh, this message translates to:
  /// **'计价币种'**
  String get fiatCurrency;

  /// No description provided for @theme.
  ///
  /// In zh, this message translates to:
  /// **'主题'**
  String get theme;

  /// No description provided for @themeModeLight.
  ///
  /// In zh, this message translates to:
  /// **'白天模式'**
  String get themeModeLight;

  /// No description provided for @themeModeDark.
  ///
  /// In zh, this message translates to:
  /// **'夜间模式'**
  String get themeModeDark;

  /// No description provided for @themeModeSystem.
  ///
  /// In zh, this message translates to:
  /// **'跟随系统'**
  String get themeModeSystem;

  /// No description provided for @cancel.
  ///
  /// In zh, this message translates to:
  /// **'取消'**
  String get cancel;

  /// No description provided for @priceColorMode.
  ///
  /// In zh, this message translates to:
  /// **'涨跌颜色'**
  String get priceColorMode;

  /// No description provided for @colorRedUp.
  ///
  /// In zh, this message translates to:
  /// **'红涨绿跌'**
  String get colorRedUp;

  /// No description provided for @colorGreenUp.
  ///
  /// In zh, this message translates to:
  /// **'绿涨红跌'**
  String get colorGreenUp;

  /// No description provided for @changeBasis.
  ///
  /// In zh, this message translates to:
  /// **'涨跌幅基准'**
  String get changeBasis;

  /// No description provided for @changeBasisNotice.
  ///
  /// In zh, this message translates to:
  /// **'切换到新时区后，现货、保证金、合约市场的报价变化（%）将会重新计算，但不会影响 K 线。'**
  String get changeBasisNotice;

  /// No description provided for @last24h.
  ///
  /// In zh, this message translates to:
  /// **'最近 24 小时'**
  String get last24h;

  /// No description provided for @currentTimezone.
  ///
  /// In zh, this message translates to:
  /// **'当前时区'**
  String get currentTimezone;

  /// No description provided for @watchlist.
  ///
  /// In zh, this message translates to:
  /// **'自选'**
  String get watchlist;

  /// No description provided for @onchain.
  ///
  /// In zh, this message translates to:
  /// **'链上交易'**
  String get onchain;

  /// No description provided for @opportunity.
  ///
  /// In zh, this message translates to:
  /// **'机会'**
  String get opportunity;

  /// No description provided for @insight.
  ///
  /// In zh, this message translates to:
  /// **'观点'**
  String get insight;

  /// No description provided for @spotMarket.
  ///
  /// In zh, this message translates to:
  /// **'现货'**
  String get spotMarket;

  /// No description provided for @futuresMarket.
  ///
  /// In zh, this message translates to:
  /// **'合约'**
  String get futuresMarket;

  /// No description provided for @leverageMarket.
  ///
  /// In zh, this message translates to:
  /// **'杠杆'**
  String get leverageMarket;

  /// No description provided for @liveMonitor.
  ///
  /// In zh, this message translates to:
  /// **'实时盯盘'**
  String get liveMonitor;

  /// No description provided for @priceAlert.
  ///
  /// In zh, this message translates to:
  /// **'价格提醒'**
  String get priceAlert;

  /// No description provided for @logoSetting.
  ///
  /// In zh, this message translates to:
  /// **'Logo 设置'**
  String get logoSetting;

  /// No description provided for @token.
  ///
  /// In zh, this message translates to:
  /// **'币种'**
  String get token;

  /// No description provided for @tradeVolume.
  ///
  /// In zh, this message translates to:
  /// **'交易额'**
  String get tradeVolume;

  /// No description provided for @latestPrice.
  ///
  /// In zh, this message translates to:
  /// **'最新价'**
  String get latestPrice;

  /// No description provided for @changePercent.
  ///
  /// In zh, this message translates to:
  /// **'涨跌幅'**
  String get changePercent;

  /// No description provided for @comingSoon.
  ///
  /// In zh, this message translates to:
  /// **'即将开盘'**
  String get comingSoon;

  /// No description provided for @onlineTime.
  ///
  /// In zh, this message translates to:
  /// **'上线时间'**
  String get onlineTime;

  /// No description provided for @addToWatchlist.
  ///
  /// In zh, this message translates to:
  /// **'加入自选'**
  String get addToWatchlist;

  /// No description provided for @hotNewsImpact.
  ///
  /// In zh, this message translates to:
  /// **'热议引发 {crypto}，投机买入多，代币价飙升。'**
  String hotNewsImpact(Object crypto);

  /// No description provided for @featured.
  ///
  /// In zh, this message translates to:
  /// **'精选'**
  String get featured;

  /// No description provided for @newlyListed.
  ///
  /// In zh, this message translates to:
  /// **'新上'**
  String get newlyListed;

  /// No description provided for @totalMarketCap.
  ///
  /// In zh, this message translates to:
  /// **'总市值'**
  String get totalMarketCap;

  /// No description provided for @totalVolume.
  ///
  /// In zh, this message translates to:
  /// **'成交额'**
  String get totalVolume;

  /// No description provided for @holderCount.
  ///
  /// In zh, this message translates to:
  /// **'持有人数'**
  String get holderCount;

  /// No description provided for @onchainVolume.
  ///
  /// In zh, this message translates to:
  /// **'链上成交额'**
  String get onchainVolume;

  /// No description provided for @price.
  ///
  /// In zh, this message translates to:
  /// **'价格'**
  String get price;

  /// No description provided for @priceChange.
  ///
  /// In zh, this message translates to:
  /// **'涨跌'**
  String get priceChange;

  /// No description provided for @passwordLengthRule.
  ///
  /// In zh, this message translates to:
  /// **'8-32个字符'**
  String get passwordLengthRule;

  /// No description provided for @passwordUppercaseRule.
  ///
  /// In zh, this message translates to:
  /// **'至少一个大写字母'**
  String get passwordUppercaseRule;

  /// No description provided for @passwordNumberRule.
  ///
  /// In zh, this message translates to:
  /// **'至少一个数字'**
  String get passwordNumberRule;

  /// No description provided for @passwordSymbolRule.
  ///
  /// In zh, this message translates to:
  /// **'至少一个特殊符号 (仅支持：`!@#\\\$%^&*()_+-=[]|;:<>?/`)'**
  String get passwordSymbolRule;

  /// No description provided for @passwordInvalidFormat.
  ///
  /// In zh, this message translates to:
  /// **'密码格式错误或包含不支持的特殊字符，请重新输入'**
  String get passwordInvalidFormat;

  /// No description provided for @mustAgreeToPolicy.
  ///
  /// In zh, this message translates to:
  /// **'请先同意用户协议和隐私政策'**
  String get mustAgreeToPolicy;

  /// No description provided for @enterValidEmail.
  ///
  /// In zh, this message translates to:
  /// **'请输入有效的邮箱地址'**
  String get enterValidEmail;

  /// No description provided for @enterPhone.
  ///
  /// In zh, this message translates to:
  /// **'请输入手机号码'**
  String get enterPhone;

  /// No description provided for @enterValidPhone.
  ///
  /// In zh, this message translates to:
  /// **'请输入有效的手机号码'**
  String get enterValidPhone;

  /// No description provided for @enterPassword.
  ///
  /// In zh, this message translates to:
  /// **'请输入密码'**
  String get enterPassword;

  /// No description provided for @passwordLengthError.
  ///
  /// In zh, this message translates to:
  /// **'密码长度必须为8-32个字符'**
  String get passwordLengthError;

  /// No description provided for @passwordMissingUppercase.
  ///
  /// In zh, this message translates to:
  /// **'密码必须包含至少一个大写字母'**
  String get passwordMissingUppercase;

  /// No description provided for @passwordMissingNumber.
  ///
  /// In zh, this message translates to:
  /// **'密码必须包含至少一个数字'**
  String get passwordMissingNumber;

  /// No description provided for @passwordMissingSymbol.
  ///
  /// In zh, this message translates to:
  /// **'密码必须包含至少一个特殊符号'**
  String get passwordMissingSymbol;

  /// No description provided for @iAgreeTo.
  ///
  /// In zh, this message translates to:
  /// **'我已阅读并同意'**
  String get iAgreeTo;

  /// No description provided for @userAgreementOne.
  ///
  /// In zh, this message translates to:
  /// **'用户协议'**
  String get userAgreementOne;

  /// No description provided for @privacyPolicyOne.
  ///
  /// In zh, this message translates to:
  /// **'隐私政策'**
  String get privacyPolicyOne;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when language+country codes are specified.
  switch (locale.languageCode) {
    case 'zh':
      {
        switch (locale.countryCode) {
          case 'TW':
            return AppLocalizationsZhTw();
        }
        break;
      }
  }

  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
