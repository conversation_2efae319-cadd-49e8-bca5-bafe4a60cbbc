/// Generated file. Do not edit.
///
/// This file is generated by the Flutter Intl plugin.
/// For more information, see: https://flutter.dev/docs/development/accessibility-and-localization/internationalization

// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appname => 'ZhiFang Exchange';

  @override
  String get welcomeTitle => 'Welcome to\\nZhiFang Exchange';

  @override
  String get welcomeSubtitle => 'Smart trading, intelligent transformation';

  @override
  String get agreeAndContinue => 'Agree and continue';

  @override
  String get continueMeansAgree => 'By continuing, you agree to the';

  @override
  String get userAgreement => 'User Agreement';

  @override
  String get and => 'and';

  @override
  String get privacyPolicy => 'Privacy Policy.';

  @override
  String get disagreeCloseApp =>
      'If you disagree, please close the application';

  @override
  String get welcomeBack => 'Welcome back!';

  @override
  String get register => 'Register';

  @override
  String get emailOrPhone => 'Email / Phone Number';

  @override
  String get subAccount => 'Sub-account';

  @override
  String get password => 'Password';

  @override
  String get forgotPassword => 'Forgot your password?';

  @override
  String get nextStep => 'Next';

  @override
  String get login => 'Login';

  @override
  String get loginViaOther => 'Or login with';

  @override
  String get switchCaptchaMethod => 'Switch CAPTCHA method';

  @override
  String get switchVerificationMethod => 'Switch verification method';

  @override
  String get geetestVerification => 'Geetest Verification';

  @override
  String get googleVerification => 'Google Authenticator';

  @override
  String welcomeToApp(Object appname) {
    return 'Welcome to $appname';
  }

  @override
  String get email => 'Email';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get enterPhoneNumber => 'Please enter your phone number';

  @override
  String get enterEmail => 'Please enter your email address';

  @override
  String get inviteCodeOptional => 'Invitation Code (Optional)';

  @override
  String get enterInviteCode => 'Enter invitation code (optional)';

  @override
  String get paste => 'Paste';

  @override
  String agreeToTerms(Object appname) {
    return 'I have read and agree to $appname\'s';
  }

  @override
  String get createAccount => 'Create Account';

  @override
  String get pleaseAgreeToTerms => 'Please read and accept the terms first';

  @override
  String bigGift(Object amount, Object currency) {
    return '$amount$currency welcome bonus, claim upon registration!';
  }

  @override
  String get registerOrLogin => 'Geetest started';

  @override
  String get viewDetails => 'View Details';

  @override
  String get guaranteeAndService => 'Guarantee & Services';

  @override
  String get viewMore => 'View More';

  @override
  String get goNow => 'Go Now';

  @override
  String get homePage => 'Home';

  @override
  String get market => 'Market';

  @override
  String get trade => 'Trade';

  @override
  String get wealthManagement => 'Earn';

  @override
  String get assets => 'Assets';

  @override
  String get welcomeMain => 'Welcome';

  @override
  String tradeSlogan(Object appname) {
    return 'Trade and exchange cryptocurrencies on $appname';
  }

  @override
  String get quickEntry => 'Quick Access';

  @override
  String get helpCenter => 'Help Center';

  @override
  String get customerSupport => 'Support';

  @override
  String get academyEntry => 'Academy';

  @override
  String get protectionFund => 'Protection Fund';

  @override
  String aboutAppname(Object appname) {
    return 'About $appname';
  }

  @override
  String get preferences => 'Preferences';

  @override
  String get language => 'Language';

  @override
  String get langSimplified => 'Simplified Chinese';

  @override
  String get langTraditional => 'Traditional Chinese';

  @override
  String get langEnglish => 'English';

  @override
  String get fiatCurrency => 'Fiat Currency';

  @override
  String get theme => 'Theme';

  @override
  String get themeModeLight => 'Light Mode';

  @override
  String get themeModeDark => 'Dark Mode';

  @override
  String get themeModeSystem => 'Follow System';

  @override
  String get cancel => 'Cancel';

  @override
  String get priceColorMode => 'Price Color Mode';

  @override
  String get colorRedUp => 'Red Up, Green Down';

  @override
  String get colorGreenUp => 'Green Up, Red Down';

  @override
  String get changeBasis => 'P/L Calculation Basis';

  @override
  String get changeBasisNotice =>
      'Switching timezones will recalculate price changes (%) in spot, margin, and futures markets. K-line charts will remain unchanged.';

  @override
  String get last24h => 'Last 24 Hours';

  @override
  String get currentTimezone => 'Current Timezone';

  @override
  String get watchlist => 'Watchlist';

  @override
  String get onchain => 'On-chain';

  @override
  String get opportunity => 'Opportunities';

  @override
  String get insight => 'Insights';

  @override
  String get spotMarket => 'Spot';

  @override
  String get futuresMarket => 'Futures';

  @override
  String get leverageMarket => 'Leverage';

  @override
  String get liveMonitor => 'Live Watch';

  @override
  String get priceAlert => 'Price Alert';

  @override
  String get logoSetting => 'Logo Setting';

  @override
  String get token => 'Token';

  @override
  String get tradeVolume => 'Trading Volume';

  @override
  String get latestPrice => 'Latest Price';

  @override
  String get changePercent => 'Change %';

  @override
  String get comingSoon => 'Coming Soon';

  @override
  String get onlineTime => 'Listing Time';

  @override
  String get addToWatchlist => 'Add to Watchlist';

  @override
  String hotNewsImpact(Object crypto) {
    return 'Hot topic triggers $crypto, speculative buying surges, token price soars.';
  }

  @override
  String get featured => 'Featured';

  @override
  String get newlyListed => 'New';

  @override
  String get totalMarketCap => 'Total Market Cap';

  @override
  String get totalVolume => 'Total Volume';

  @override
  String get holderCount => 'Holders';

  @override
  String get onchainVolume => 'On-chain Volume';

  @override
  String get price => 'Price';

  @override
  String get priceChange => 'Change';

  @override
  String get passwordLengthRule => '8–32 characters';

  @override
  String get passwordUppercaseRule => 'At least one uppercase letter';

  @override
  String get passwordNumberRule => 'At least one number';

  @override
  String get passwordSymbolRule =>
      'At least one special character (allowed: `!@#\\\$%^&*()_+-=[]|;:<>?/`)';

  @override
  String get passwordInvalidFormat =>
      'Password format is incorrect or contains unsupported characters. Please try again.';

  @override
  String get mustAgreeToPolicy =>
      'Please agree to the User Agreement and Privacy Policy first';

  @override
  String get enterValidEmail => 'Please enter a valid email address';

  @override
  String get enterPhone => 'Please enter your phone number';

  @override
  String get enterValidPhone => 'Please enter a valid phone number';

  @override
  String get enterPassword => 'Please enter your password';

  @override
  String get passwordLengthError => 'Password must be 8–32 characters long';

  @override
  String get passwordMissingUppercase =>
      'Password must contain at least one uppercase letter';

  @override
  String get passwordMissingNumber =>
      'Password must contain at least one number';

  @override
  String get passwordMissingSymbol =>
      'Password must contain at least one special character';

  @override
  String get iAgreeTo => 'I have read and agree to the';

  @override
  String get userAgreementOne => 'User Agreement';

  @override
  String get privacyPolicyOne => 'Privacy Policy';
}
