/*
*  国家区号数据模型和工具类
*/

class CountryCode {
  final String cn; // 中文简体
  final String tw; // 中文繁体
  final String en; // 英语
  final String code;
  final String dialCode;

  const CountryCode({
    required this.cn,
    required this.tw,
    required this.en,
    required this.code,
    required this.dialCode,
  });

  /// 根据语言代码获取对应的国家名称
  String getNameByLanguage(String languageCode, [String? countryCode]) {
    final locale = '${languageCode}_${countryCode ?? ''}';
    switch (locale) {
      case 'zh_CN':
        return cn;
      case 'zh_TW':
        return tw;
      case 'en_US':
      case 'en_':
        return en;
      default:
        return cn; // 默认返回简体中文
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CountryCode &&
          runtimeType == other.runtimeType &&
          code == other.code;

  @override
  int get hashCode => code.hashCode;

  @override
  String toString() {
    return 'CountryCode{cn: $cn, code: $code, dialCode: $dialCode}';
  }
}

class CountryCodes {
  static const List<CountryCode> countries = [
    CountryCode(
      cn: "中国大陆",
      tw: "中國大陸",
      en: "China",
      code: "cn",
      dialCode: "+86",
    ),
    CountryCode(
      cn: "美国",
      tw: "美國",
      en: "United States",
      code: "US",
      dialCode: "+1",
    ),
    CountryCode(
      cn: "英国",
      tw: "英國",
      en: "United Kingdom",
      code: "GB",
      dialCode: "+44",
    ),
    CountryCode(cn: "日本", tw: "日本", en: "Japan", code: "JP", dialCode: "+81"),
    CountryCode(
      cn: "韩国",
      tw: "韓國",
      en: "South Korea",
      code: "KR",
      dialCode: "+82",
    ),
    CountryCode(cn: "德国", tw: "德國", en: "Germany", code: "DE", dialCode: "+49"),
    CountryCode(cn: "法国", tw: "法國", en: "France", code: "FR", dialCode: "+33"),
    CountryCode(cn: "意大利", tw: "意大利", en: "Italy", code: "IT", dialCode: "+39"),
    CountryCode(cn: "西班牙", tw: "西班牙", en: "Spain", code: "ES", dialCode: "+34"),
    CountryCode(cn: "俄罗斯", tw: "俄羅斯", en: "Russia", code: "RU", dialCode: "+7"),
    CountryCode(cn: "加拿大", tw: "加拿大", en: "Canada", code: "CA", dialCode: "+1"),
    CountryCode(
      cn: "澳大利亚",
      tw: "澳大利亞",
      en: "Australia",
      code: "AU",
      dialCode: "+61",
    ),
    CountryCode(
      cn: "新西兰",
      tw: "新西蘭",
      en: "New Zealand",
      code: "NZ",
      dialCode: "+64",
    ),
    CountryCode(cn: "印度", tw: "印度", en: "India", code: "IN", dialCode: "+91"),
    CountryCode(cn: "巴西", tw: "巴西", en: "Brazil", code: "BR", dialCode: "+55"),
    CountryCode(
      cn: "阿根廷",
      tw: "阿根廷",
      en: "Argentina",
      code: "AR",
      dialCode: "+54",
    ),
    CountryCode(
      cn: "墨西哥",
      tw: "墨西哥",
      en: "Mexico",
      code: "MX",
      dialCode: "+52",
    ),
    CountryCode(cn: "智利", tw: "智利", en: "Chile", code: "CL", dialCode: "+56"),
    CountryCode(
      cn: "哥伦比亚",
      tw: "哥倫比亞",
      en: "Colombia",
      code: "CO",
      dialCode: "+57",
    ),
    CountryCode(cn: "秘鲁", tw: "秘魯", en: "Peru", code: "PE", dialCode: "+51"),
    CountryCode(
      cn: "委内瑞拉",
      tw: "委內瑞拉",
      en: "Venezuela",
      code: "VE",
      dialCode: "+58",
    ),
    CountryCode(
      cn: "南非",
      tw: "南非",
      en: "South Africa",
      code: "ZA",
      dialCode: "+27",
    ),
    CountryCode(cn: "埃及", tw: "埃及", en: "Egypt", code: "EG", dialCode: "+20"),
    CountryCode(
      cn: "尼日利亚",
      tw: "尼日利亞",
      en: "Nigeria",
      code: "NG",
      dialCode: "+234",
    ),
    CountryCode(
      cn: "肯尼亚",
      tw: "肯尼亞",
      en: "Kenya",
      code: "KE",
      dialCode: "+254",
    ),
    CountryCode(
      cn: "摩洛哥",
      tw: "摩洛哥",
      en: "Morocco",
      code: "MA",
      dialCode: "+212",
    ),
    CountryCode(
      cn: "阿尔及利亚",
      tw: "阿爾及利亞",
      en: "Algeria",
      code: "DZ",
      dialCode: "+213",
    ),
    CountryCode(
      cn: "突尼斯",
      tw: "突尼斯",
      en: "Tunisia",
      code: "TN",
      dialCode: "+216",
    ),
    CountryCode(
      cn: "利比亚",
      tw: "利比亞",
      en: "Libya",
      code: "LY",
      dialCode: "+218",
    ),
    CountryCode(cn: "苏丹", tw: "蘇丹", en: "Sudan", code: "SD", dialCode: "+249"),
    CountryCode(
      cn: "埃塞俄比亚",
      tw: "埃塞俄比亞",
      en: "Ethiopia",
      code: "ET",
      dialCode: "+251",
    ),
    CountryCode(
      cn: "乌干达",
      tw: "烏干達",
      en: "Uganda",
      code: "UG",
      dialCode: "+256",
    ),
    CountryCode(
      cn: "坦桑尼亚",
      tw: "坦桑尼亞",
      en: "Tanzania",
      code: "TZ",
      dialCode: "+255",
    ),
    CountryCode(cn: "加纳", tw: "加納", en: "Ghana", code: "GH", dialCode: "+233"),
    CountryCode(
      cn: "象牙海岸",
      tw: "象牙海岸",
      en: "Ivory Coast",
      code: "CI",
      dialCode: "+225",
    ),
    CountryCode(
      cn: "塞内加尔",
      tw: "塞內加爾",
      en: "Senegal",
      code: "SN",
      dialCode: "+221",
    ),
    CountryCode(cn: "马里", tw: "馬里", en: "Mali", code: "ML", dialCode: "+223"),
    CountryCode(
      cn: "布基纳法索",
      tw: "布基納法索",
      en: "Burkina Faso",
      code: "BF",
      dialCode: "+226",
    ),
    CountryCode(
      cn: "尼日尔",
      tw: "尼日爾",
      en: "Niger",
      code: "NE",
      dialCode: "+227",
    ),
    CountryCode(
      cn: "几内亚",
      tw: "幾內亞",
      en: "Guinea",
      code: "GN",
      dialCode: "+224",
    ),
    CountryCode(
      cn: "塞拉利昂",
      tw: "塞拉利昂",
      en: "Sierra Leone",
      code: "SL",
      dialCode: "+232",
    ),
    CountryCode(
      cn: "利比里亚",
      tw: "利比里亞",
      en: "Liberia",
      code: "LR",
      dialCode: "+231",
    ),
    CountryCode(
      cn: "土耳其",
      tw: "土耳其",
      en: "Turkey",
      code: "TR",
      dialCode: "+90",
    ),
    CountryCode(
      cn: "沙特阿拉伯",
      tw: "沙特阿拉伯",
      en: "Saudi Arabia",
      code: "SA",
      dialCode: "+966",
    ),
    CountryCode(
      cn: "阿联酋",
      tw: "阿聯酋",
      en: "United Arab Emirates",
      code: "AE",
      dialCode: "+971",
    ),
    CountryCode(
      cn: "以色列",
      tw: "以色列",
      en: "Israel",
      code: "IL",
      dialCode: "+972",
    ),
    CountryCode(cn: "伊朗", tw: "伊朗", en: "Iran", code: "IR", dialCode: "+98"),
    CountryCode(cn: "伊拉克", tw: "伊拉克", en: "Iraq", code: "IQ", dialCode: "+964"),
    CountryCode(cn: "约旦", tw: "約旦", en: "Jordan", code: "JO", dialCode: "+962"),
    CountryCode(
      cn: "黎巴嫩",
      tw: "黎巴嫩",
      en: "Lebanon",
      code: "LB",
      dialCode: "+961",
    ),
    CountryCode(
      cn: "叙利亚",
      tw: "敘利亞",
      en: "Syria",
      code: "SY",
      dialCode: "+963",
    ),
    CountryCode(
      cn: "科威特",
      tw: "科威特",
      en: "Kuwait",
      code: "KW",
      dialCode: "+965",
    ),
    CountryCode(
      cn: "卡塔尔",
      tw: "卡塔爾",
      en: "Qatar",
      code: "QA",
      dialCode: "+974",
    ),
    CountryCode(
      cn: "巴林",
      tw: "巴林",
      en: "Bahrain",
      code: "BH",
      dialCode: "+973",
    ),
    CountryCode(cn: "阿曼", tw: "阿曼", en: "Oman", code: "OM", dialCode: "+968"),
    CountryCode(cn: "也门", tw: "也門", en: "Yemen", code: "YE", dialCode: "+967"),
    CountryCode(
      cn: "阿富汗",
      tw: "阿富汗",
      en: "Afghanistan",
      code: "AF",
      dialCode: "+93",
    ),
    CountryCode(
      cn: "巴基斯坦",
      tw: "巴基斯坦",
      en: "Pakistan",
      code: "PK",
      dialCode: "+92",
    ),
    CountryCode(
      cn: "孟加拉国",
      tw: "孟加拉國",
      en: "Bangladesh",
      code: "BD",
      dialCode: "+880",
    ),
    CountryCode(
      cn: "斯里兰卡",
      tw: "斯里蘭卡",
      en: "Sri Lanka",
      code: "LK",
      dialCode: "+94",
    ),
    CountryCode(
      cn: "尼泊尔",
      tw: "尼泊爾",
      en: "Nepal",
      code: "NP",
      dialCode: "+977",
    ),
    CountryCode(cn: "不丹", tw: "不丹", en: "Bhutan", code: "BT", dialCode: "+975"),
    CountryCode(
      cn: "马尔代夫",
      tw: "馬爾代夫",
      en: "Maldives",
      code: "MV",
      dialCode: "+960",
    ),
    CountryCode(
      cn: "泰国",
      tw: "泰國",
      en: "Thailand",
      code: "TH",
      dialCode: "+66",
    ),
    CountryCode(cn: "越南", tw: "越南", en: "Vietnam", code: "VN", dialCode: "+84"),
    CountryCode(
      cn: "马来西亚",
      tw: "馬來西亞",
      en: "Malaysia",
      code: "MY",
      dialCode: "+60",
    ),
    CountryCode(
      cn: "新加坡",
      tw: "新加坡",
      en: "Singapore",
      code: "SG",
      dialCode: "+65",
    ),
    CountryCode(
      cn: "印度尼西亚",
      tw: "印度尼西亞",
      en: "Indonesia",
      code: "ID",
      dialCode: "+62",
    ),
    CountryCode(
      cn: "菲律宾",
      tw: "菲律賓",
      en: "Philippines",
      code: "PH",
      dialCode: "+63",
    ),
    CountryCode(cn: "缅甸", tw: "緬甸", en: "Myanmar", code: "MM", dialCode: "+95"),
    CountryCode(
      cn: "柬埔寨",
      tw: "柬埔寨",
      en: "Cambodia",
      code: "KH",
      dialCode: "+855",
    ),
    CountryCode(cn: "老挝", tw: "老撾", en: "Laos", code: "LA", dialCode: "+856"),
    CountryCode(cn: "文莱", tw: "文萊", en: "Brunei", code: "BN", dialCode: "+673"),
    CountryCode(
      cn: "东帝汶",
      tw: "東帝汶",
      en: "East Timor",
      code: "TL",
      dialCode: "+670",
    ),
    CountryCode(
      cn: "蒙古",
      tw: "蒙古",
      en: "Mongolia",
      code: "MN",
      dialCode: "+976",
    ),
    CountryCode(
      cn: "哈萨克斯坦",
      tw: "哈薩克斯坦",
      en: "Kazakhstan",
      code: "KZ",
      dialCode: "+7",
    ),
    CountryCode(
      cn: "乌兹别克斯坦",
      tw: "烏茲別克斯坦",
      en: "Uzbekistan",
      code: "UZ",
      dialCode: "+998",
    ),
    CountryCode(
      cn: "土库曼斯坦",
      tw: "土庫曼斯坦",
      en: "Turkmenistan",
      code: "TM",
      dialCode: "+993",
    ),
    CountryCode(
      cn: "塔吉克斯坦",
      tw: "塔吉克斯坦",
      en: "Tajikistan",
      code: "TJ",
      dialCode: "+992",
    ),
    CountryCode(
      cn: "吉尔吉斯斯坦",
      tw: "吉爾吉斯斯坦",
      en: "Kyrgyzstan",
      code: "KG",
      dialCode: "+996",
    ),
    CountryCode(
      cn: "荷兰",
      tw: "荷蘭",
      en: "Netherlands",
      code: "NL",
      dialCode: "+31",
    ),
    CountryCode(
      cn: "比利时",
      tw: "比利時",
      en: "Belgium",
      code: "BE",
      dialCode: "+32",
    ),
    CountryCode(
      cn: "瑞士",
      tw: "瑞士",
      en: "Switzerland",
      code: "CH",
      dialCode: "+41",
    ),
    CountryCode(
      cn: "奥地利",
      tw: "奧地利",
      en: "Austria",
      code: "AT",
      dialCode: "+43",
    ),
    CountryCode(cn: "丹麦", tw: "丹麥", en: "Denmark", code: "DK", dialCode: "+45"),
    CountryCode(cn: "瑞典", tw: "瑞典", en: "Sweden", code: "SE", dialCode: "+46"),
    CountryCode(cn: "挪威", tw: "挪威", en: "Norway", code: "NO", dialCode: "+47"),
    CountryCode(
      cn: "芬兰",
      tw: "芬蘭",
      en: "Finland",
      code: "FI",
      dialCode: "+358",
    ),
    CountryCode(
      cn: "冰岛",
      tw: "冰島",
      en: "Iceland",
      code: "IS",
      dialCode: "+354",
    ),
    CountryCode(
      cn: "爱尔兰",
      tw: "愛爾蘭",
      en: "Ireland",
      code: "IE",
      dialCode: "+353",
    ),
    CountryCode(
      cn: "葡萄牙",
      tw: "葡萄牙",
      en: "Portugal",
      code: "PT",
      dialCode: "+351",
    ),
    CountryCode(cn: "希腊", tw: "希臘", en: "Greece", code: "GR", dialCode: "+30"),
    CountryCode(cn: "波兰", tw: "波蘭", en: "Poland", code: "PL", dialCode: "+48"),
    CountryCode(
      cn: "捷克",
      tw: "捷克",
      en: "Czech Republic",
      code: "CZ",
      dialCode: "+420",
    ),
    CountryCode(
      cn: "斯洛伐克",
      tw: "斯洛伐克",
      en: "Slovakia",
      code: "SK",
      dialCode: "+421",
    ),
    CountryCode(
      cn: "匈牙利",
      tw: "匈牙利",
      en: "Hungary",
      code: "HU",
      dialCode: "+36",
    ),
    CountryCode(
      cn: "罗马尼亚",
      tw: "羅馬尼亞",
      en: "Romania",
      code: "RO",
      dialCode: "+40",
    ),
    CountryCode(
      cn: "保加利亚",
      tw: "保加利亞",
      en: "Bulgaria",
      code: "BG",
      dialCode: "+359",
    ),
    CountryCode(
      cn: "克罗地亚",
      tw: "克羅地亞",
      en: "Croatia",
      code: "HR",
      dialCode: "+385",
    ),
    CountryCode(
      cn: "塞尔维亚",
      tw: "塞爾維亞",
      en: "Serbia",
      code: "RS",
      dialCode: "+381",
    ),
    CountryCode(
      cn: "波黑",
      tw: "波黑",
      en: "Bosnia and Herzegovina",
      code: "BA",
      dialCode: "+387",
    ),
    CountryCode(
      cn: "黑山",
      tw: "黑山",
      en: "Montenegro",
      code: "ME",
      dialCode: "+382",
    ),
    CountryCode(
      cn: "北马其顿",
      tw: "北馬其頓",
      en: "North Macedonia",
      code: "MK",
      dialCode: "+389",
    ),
    CountryCode(
      cn: "阿尔巴尼亚",
      tw: "阿爾巴尼亞",
      en: "Albania",
      code: "AL",
      dialCode: "+355",
    ),
    CountryCode(
      cn: "斯洛文尼亚",
      tw: "斯洛文尼亞",
      en: "Slovenia",
      code: "SI",
      dialCode: "+386",
    ),
    CountryCode(
      cn: "立陶宛",
      tw: "立陶宛",
      en: "Lithuania",
      code: "LT",
      dialCode: "+370",
    ),
    CountryCode(
      cn: "拉脱维亚",
      tw: "拉脫維亞",
      en: "Latvia",
      code: "LV",
      dialCode: "+371",
    ),
    CountryCode(
      cn: "爱沙尼亚",
      tw: "愛沙尼亞",
      en: "Estonia",
      code: "EE",
      dialCode: "+372",
    ),
    CountryCode(
      cn: "白俄罗斯",
      tw: "白俄羅斯",
      en: "Belarus",
      code: "BY",
      dialCode: "+375",
    ),
    CountryCode(
      cn: "乌克兰",
      tw: "烏克蘭",
      en: "Ukraine",
      code: "UA",
      dialCode: "+380",
    ),
    CountryCode(
      cn: "摩尔多瓦",
      tw: "摩爾多瓦",
      en: "Moldova",
      code: "MD",
      dialCode: "+373",
    ),
    CountryCode(
      cn: "格鲁吉亚",
      tw: "格魯吉亞",
      en: "Georgia",
      code: "GE",
      dialCode: "+995",
    ),
    CountryCode(
      cn: "亚美尼亚",
      tw: "亞美尼亞",
      en: "Armenia",
      code: "AM",
      dialCode: "+374",
    ),
    CountryCode(
      cn: "阿塞拜疆",
      tw: "阿塞拜疆",
      en: "Azerbaijan",
      code: "AZ",
      dialCode: "+994",
    ),
    CountryCode(
      cn: "塞浦路斯",
      tw: "塞浦路斯",
      en: "Cyprus",
      code: "CY",
      dialCode: "+357",
    ),
    CountryCode(
      cn: "马耳他",
      tw: "馬耳他",
      en: "Malta",
      code: "MT",
      dialCode: "+356",
    ),
    CountryCode(
      cn: "卢森堡",
      tw: "盧森堡",
      en: "Luxembourg",
      code: "LU",
      dialCode: "+352",
    ),
    CountryCode(
      cn: "摩纳哥",
      tw: "摩納哥",
      en: "Monaco",
      code: "MC",
      dialCode: "+377",
    ),
    CountryCode(
      cn: "安道尔",
      tw: "安道爾",
      en: "Andorra",
      code: "AD",
      dialCode: "+376",
    ),
    CountryCode(
      cn: "圣马力诺",
      tw: "聖馬力諾",
      en: "San Marino",
      code: "SM",
      dialCode: "+378",
    ),
    CountryCode(
      cn: "梵蒂冈",
      tw: "梵蒂岡",
      en: "Vatican City",
      code: "VA",
      dialCode: "+39",
    ),
    CountryCode(
      cn: "列支敦士登",
      tw: "列支敦士登",
      en: "Liechtenstein",
      code: "LI",
      dialCode: "+423",
    ),
    // 亚洲其他国家和地区
    CountryCode(
      cn: "中国香港",
      tw: "中國香港",
      en: "Hong Kong",
      code: "HK",
      dialCode: "+852",
    ),
    CountryCode(
      cn: "中国澳门",
      tw: "中國澳門",
      en: "Macau",
      code: "MO",
      dialCode: "+853",
    ),
    CountryCode(
      cn: "中国台湾",
      tw: "中國台灣",
      en: "Taiwan",
      code: "tw",
      dialCode: "+886",
    ),
    CountryCode(
      cn: "朝鲜",
      tw: "朝鮮",
      en: "North Korea",
      code: "KP",
      dialCode: "+850",
    ),
    // 中东其他国家
    CountryCode(
      cn: "巴勒斯坦",
      tw: "巴勒斯坦",
      en: "Palestine",
      code: "PS",
      dialCode: "+970",
    ),
    // 南美洲其他国家
    CountryCode(
      cn: "乌拉圭",
      tw: "烏拉圭",
      en: "Uruguay",
      code: "UY",
      dialCode: "+598",
    ),
    CountryCode(
      cn: "巴拉圭",
      tw: "巴拉圭",
      en: "Paraguay",
      code: "PY",
      dialCode: "+595",
    ),
    CountryCode(
      cn: "玻利维亚",
      tw: "玻利維亞",
      en: "Bolivia",
      code: "BO",
      dialCode: "+591",
    ),
    CountryCode(
      cn: "厄瓜多尔",
      tw: "厄瓜多爾",
      en: "Ecuador",
      code: "EC",
      dialCode: "+593",
    ),
    CountryCode(
      cn: "圭亚那",
      tw: "圭亞那",
      en: "Guyana",
      code: "GY",
      dialCode: "+592",
    ),
    CountryCode(
      cn: "苏里南",
      tw: "蘇里南",
      en: "Suriname",
      code: "SR",
      dialCode: "+597",
    ),
    // 北美洲其他国家
    CountryCode(cn: "古巴", tw: "古巴", en: "Cuba", code: "CU", dialCode: "+53"),
    CountryCode(
      cn: "牙买加",
      tw: "牙買加",
      en: "Jamaica",
      code: "JM",
      dialCode: "+1",
    ),
    CountryCode(cn: "海地", tw: "海地", en: "Haiti", code: "HT", dialCode: "+509"),
    CountryCode(
      cn: "多米尼加",
      tw: "多米尼加",
      en: "Dominican Republic",
      code: "DO",
      dialCode: "+1",
    ),
    CountryCode(
      cn: "危地马拉",
      tw: "危地馬拉",
      en: "Guatemala",
      code: "GT",
      dialCode: "+502",
    ),
    CountryCode(
      cn: "哥斯达黎加",
      tw: "哥斯達黎加",
      en: "Costa Rica",
      code: "CR",
      dialCode: "+506",
    ),
    CountryCode(
      cn: "巴拿马",
      tw: "巴拿馬",
      en: "Panama",
      code: "PA",
      dialCode: "+507",
    ),
  ];

  static List<CountryCode> searchCountries(
    String query, [
    String? languageCode,
    String? countryCode,
  ]) {
    if (query.isEmpty) return countries;

    return countries.where((country) {
      final queryLower = query.toLowerCase();

      // 搜索当前语言的名称
      final currentLanguageName = country.getNameByLanguage(
        languageCode ?? 'zh',
        countryCode,
      );

      return currentLanguageName.toLowerCase().contains(queryLower) ||
          country.cn.toLowerCase().contains(queryLower) ||
          country.tw.toLowerCase().contains(queryLower) ||
          country.en.toLowerCase().contains(queryLower) ||
          country.dialCode.contains(query) ||
          country.code.toLowerCase().contains(queryLower);
    }).toList();
  }
}
