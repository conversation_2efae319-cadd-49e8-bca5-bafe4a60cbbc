import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/pages/auth/index.dart';

/// 登录检查工具类
///
/// 提供各种登录状态检查的便捷方法
class LoginChecker {
  /// 检查是否已登录（静态方法）
  static bool isLoggedIn(BuildContext context) {
    return LoginStatusService.isLoggedIn(context);
  }

  /// 检查是否已登录并显示提示
  static bool checkLoginWithPrompt(BuildContext context, {String? message, VoidCallback? onLoginRequired}) {
    if (isLoggedIn(context)) {
      return true;
    }

    // 显示登录提示
    _showLoginPrompt(context, message: message, onLoginRequired: onLoginRequired);
    return false;
  }

  /// 检查VIP状态
  static bool isVip(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    return authProvider.userInfo?.isVip ?? false;
  }

  /// 检查是否已登录且为VIP
  static bool isLoggedInVip(BuildContext context) {
    return isLoggedIn(context) && isVip(context);
  }

  /// 获取用户ID
  static int? getUserId(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    return authProvider.userInfo?.id;
  }

  /// 获取用户昵称
  static String? getUserNickname(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    return authProvider.userInfo?.username;
  }

  /// 获取用户邮箱
  static String? getUserEmail(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    return authProvider.userInfo?.email;
  }

  /// 显示登录提示对话框
  static void _showLoginPrompt(BuildContext context, {String? message, VoidCallback? onLoginRequired}) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('需要登录'),
          content: Text(message ?? '此功能需要登录后才能使用，是否前往登录？'),
          actions: [
            TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('取消')),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (onLoginRequired != null) {
                  onLoginRequired();
                } else {
                  LoginStatusService.requireLogin(context);
                }
              },
              child: const Text('去登录'),
            ),
          ],
        );
      },
    );
  }
}

/// 登录状态监听器
///
/// 用于监听登录状态变化
class LoginStatusListener extends StatefulWidget {
  final Widget child;
  final VoidCallback? onLogin;
  final VoidCallback? onLogout;

  const LoginStatusListener({super.key, required this.child, this.onLogin, this.onLogout});

  @override
  State<LoginStatusListener> createState() => _LoginStatusListenerState();
}

class _LoginStatusListenerState extends State<LoginStatusListener> {
  bool? _previousLoginStatus;

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        final currentLoginStatus = LoginStatusService.isLoggedInStatic(authProvider);

        // 检查登录状态变化
        if (_previousLoginStatus != null && _previousLoginStatus != currentLoginStatus) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (currentLoginStatus && widget.onLogin != null) {
              widget.onLogin!();
            } else if (!currentLoginStatus && widget.onLogout != null) {
              widget.onLogout!();
            }
          });
        }

        _previousLoginStatus = currentLoginStatus;
        return widget.child;
      },
    );
  }
}
