import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:io';

// 应用信息工具类
class AppInfoUtil {
  AppInfoUtil._();

  // 应用基本信息
  static String get appName => 'Qubic Exchange';
  static String get appVersion => '1.0.0';
  static String get buildNumber => '1';
  static String get fullVersion => '$appVersion+$buildNumber';
  static String get packageName => 'com.qubic.exchange';

  // 平台信息
  static bool get isAndroid => Platform.isAndroid;
  static bool get isIOS => Platform.isIOS;
  static bool get isWeb => kIsWeb;
  static bool get isDesktop =>
      Platform.isWindows || Platform.isMacOS || Platform.isLinux;
  static bool get isMobile => Platform.isAndroid || Platform.isIOS;

  static String get platformName {
    if (kIsWeb) return 'Web';
    if (Platform.isAndroid) return 'Android';
    if (Platform.isIOS) return 'iOS';
    if (Platform.isWindows) return 'Windows';
    if (Platform.isMacOS) return 'macOS';
    if (Platform.isLinux) return 'Linux';
    return 'Unknown';
  }

  static String get operatingSystemVersion => Platform.operatingSystemVersion;

  // 运行环境信息
  static bool get isDebugMode => kDebugMode;
  static bool get isReleaseMode => kReleaseMode;
  static bool get isProfileMode => kProfileMode;

  static String get buildMode {
    if (kDebugMode) return 'Debug';
    if (kReleaseMode) return 'Release';
    if (kProfileMode) return 'Profile';
    return 'Unknown';
  }

  // Flutter信息
  static String get flutterVersion => '3.7.2+';
  static String get dartVersion => Platform.version.split(' ').first;

  // 应用状态信息
  static DateTime? _appStartTime;

  static void setAppStartTime() {
    _appStartTime ??= DateTime.now();
  }

  static Duration get appRuntime {
    if (_appStartTime == null) return Duration.zero;
    return DateTime.now().difference(_appStartTime!);
  }

  static String get appRuntimeFormatted {
    final runtime = appRuntime;
    final hours = runtime.inHours;
    final minutes = runtime.inMinutes % 60;
    final seconds = runtime.inSeconds % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  // 内存信息
  static String get memoryUsage {
    if (!kDebugMode) return 'N/A (Release Mode)';
    return 'Debug Mode - Memory info available in DevTools';
  }

  // 综合信息方法
  static Map<String, dynamic> getAppInfo() {
    return {
      'appName': appName,
      'appVersion': appVersion,
      'buildNumber': buildNumber,
      'fullVersion': fullVersion,
      'packageName': packageName,
      'platform': platformName,
      'isAndroid': isAndroid,
      'isIOS': isIOS,
      'isWeb': isWeb,
      'isDesktop': isDesktop,
      'isMobile': isMobile,
      'operatingSystemVersion': operatingSystemVersion,
      'buildMode': buildMode,
      'isDebugMode': isDebugMode,
      'isReleaseMode': isReleaseMode,
      'isProfileMode': isProfileMode,
      'flutterVersion': flutterVersion,
      'dartVersion': dartVersion,
      'appRuntime': appRuntimeFormatted,
      'memoryUsage': memoryUsage,
    };
  }

  static String getAppInfoFormatted() {
    final info = getAppInfo();
    final buffer = StringBuffer();

    buffer.writeln('=== 应用信息 ===');
    buffer.writeln('应用名称: ${info['appName']}');
    buffer.writeln('应用版本: ${info['fullVersion']}');
    buffer.writeln('包名: ${info['packageName']}');
    buffer.writeln('平台: ${info['platform']}');
    buffer.writeln('系统版本: ${info['operatingSystemVersion']}');
    buffer.writeln('构建模式: ${info['buildMode']}');
    buffer.writeln('Flutter版本: ${info['flutterVersion']}');
    buffer.writeln('Dart版本: ${info['dartVersion']}');
    buffer.writeln('运行时长: ${info['appRuntime']}');
    buffer.writeln('内存使用: ${info['memoryUsage']}');
    buffer.writeln('================');

    return buffer.toString();
  }

  static void printAppInfo() {
    if (kDebugMode) {
      debugPrint(getAppInfoFormatted());
    }
  }

  static Future<void> copyAppInfoToClipboard() async {
    try {
      await Clipboard.setData(ClipboardData(text: getAppInfoFormatted()));
    } catch (e) {
      debugPrint('复制应用信息失败: $e');
    }
  }
}
