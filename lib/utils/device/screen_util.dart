import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// 屏幕信息工具类
class ScreenUtil {
  ScreenUtil._();

  // 屏幕基本信息
  static double screenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  static double screenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  static Size screenSize(BuildContext context) {
    return MediaQuery.of(context).size;
  }

  static double pixelRatio(BuildContext context) {
    return MediaQuery.of(context).devicePixelRatio;
  }

  static double physicalWidth(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final ratio = MediaQuery.of(context).devicePixelRatio;
    return size.width * ratio;
  }

  static double physicalHeight(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final ratio = MediaQuery.of(context).devicePixelRatio;
    return size.height * ratio;
  }

  static Size physicalSize(BuildContext context) {
    return Size(physicalWidth(context), physicalHeight(context));
  }

  // 屏幕方向信息
  static Orientation orientation(BuildContext context) {
    return MediaQuery.of(context).orientation;
  }

  static bool isPortrait(BuildContext context) {
    return orientation(context) == Orientation.portrait;
  }

  static bool isLandscape(BuildContext context) {
    return orientation(context) == Orientation.landscape;
  }

  static String orientationName(BuildContext context) {
    return isPortrait(context) ? '竖屏' : '横屏';
  }

  // 安全区域信息
  static EdgeInsets safeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  static double statusBarHeight(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }

  static double bottomSafeHeight(BuildContext context) {
    return MediaQuery.of(context).padding.bottom;
  }

  static double availableHeight(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final padding = MediaQuery.of(context).padding;
    return size.height - padding.top - padding.bottom;
  }

  static double availableWidth(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final padding = MediaQuery.of(context).padding;
    return size.width - padding.left - padding.right;
  }

  // 设备类型判断
  static bool isTablet(BuildContext context) {
    return screenWidth(context) >= 600;
  }

  static bool isPhone(BuildContext context) {
    return !isTablet(context);
  }

  static String deviceType(BuildContext context) {
    return isTablet(context) ? '平板' : '手机';
  }

  // 响应式布局
  static int getColumnsForWidth(
    BuildContext context, {
    int phoneColumns = 1,
    int tabletColumns = 2,
  }) {
    return isTablet(context) ? tabletColumns : phoneColumns;
  }

  // 文本缩放
  static double textScaleFactor(BuildContext context) {
    return MediaQuery.of(context).textScaler.scale(1.0);
  }

  static bool isTextScaled(BuildContext context) {
    return textScaleFactor(context) != 1.0;
  }

  // 键盘信息
  static double keyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }

  static bool isKeyboardVisible(BuildContext context) {
    return keyboardHeight(context) > 0;
  }

  // 综合信息方法
  static Map<String, dynamic> getScreenInfo(BuildContext context) {
    final size = screenSize(context);
    final physicalSz = physicalSize(context);
    final padding = safeAreaPadding(context);

    return {
      'screenWidth': size.width,
      'screenHeight': size.height,
      'physicalWidth': physicalSz.width,
      'physicalHeight': physicalSz.height,
      'pixelRatio': pixelRatio(context),
      'orientation': orientationName(context),
      'isPortrait': isPortrait(context),
      'isLandscape': isLandscape(context),
      'statusBarHeight': padding.top,
      'bottomSafeHeight': padding.bottom,
      'availableWidth': availableWidth(context),
      'availableHeight': availableHeight(context),
      'deviceType': deviceType(context),
      'isTablet': isTablet(context),
      'isPhone': isPhone(context),
      'textScaleFactor': textScaleFactor(context),
      'isTextScaled': isTextScaled(context),
      'keyboardHeight': keyboardHeight(context),
      'isKeyboardVisible': isKeyboardVisible(context),
    };
  }

  static String getScreenInfoFormatted(BuildContext context) {
    final info = getScreenInfo(context);
    final buffer = StringBuffer();

    buffer.writeln('=== 屏幕信息 ===');
    buffer.writeln(
      '屏幕尺寸: ${info['screenWidth'].toStringAsFixed(0)} x ${info['screenHeight'].toStringAsFixed(0)}',
    );
    buffer.writeln(
      '物理尺寸: ${info['physicalWidth'].toStringAsFixed(0)} x ${info['physicalHeight'].toStringAsFixed(0)}',
    );
    buffer.writeln('像素密度: ${info['pixelRatio']}');
    buffer.writeln('屏幕方向: ${info['orientation']}');
    buffer.writeln('状态栏高度: ${info['statusBarHeight']}');
    buffer.writeln('底部安全区: ${info['bottomSafeHeight']}');
    buffer.writeln(
      '可用尺寸: ${info['availableWidth'].toStringAsFixed(0)} x ${info['availableHeight'].toStringAsFixed(0)}',
    );
    buffer.writeln('设备类型: ${info['deviceType']}');
    buffer.writeln('文本缩放: ${info['textScaleFactor']}');
    buffer.writeln('键盘状态: ${info['isKeyboardVisible'] ? '显示' : '隐藏'}');
    buffer.writeln('================');

    return buffer.toString();
  }

  static void printScreenInfo(BuildContext context) {
    debugPrint(getScreenInfoFormatted(context));
  }

  static Future<void> copyScreenInfoToClipboard(BuildContext context) async {
    try {
      await Clipboard.setData(
        ClipboardData(text: getScreenInfoFormatted(context)),
      );
    } catch (e) {
      debugPrint('复制屏幕信息失败: $e');
    }
  }
}
