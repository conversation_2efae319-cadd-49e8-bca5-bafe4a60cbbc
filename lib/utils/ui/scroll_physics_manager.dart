/*
* 动态滚动物理效果管理工具
* 
* 功能：
* - 监听滚动变化，动态调整滚动物理效果
* - 默认使用 ClampingScrollPhysics
* - 向下滚动超过阈值后切换为 BouncingScrollPhysics
* - 记录切换位置，回滚到该位置以下时恢复为 ClampingScrollPhysics
*/

import 'package:flutter/material.dart';

/// 滚动物理效果管理器
class ScrollPhysicsManager {
  /// 滚动控制器
  final ScrollController _scrollController;
  
  /// 当前物理效果
  ScrollPhysics _currentPhysics;
  
  /// 切换到回弹效果时记录的位置
  double? _bounceThresholdPosition;
  
  /// 滚动阈值（像素）
  final double scrollThreshold;
  
  /// 默认物理效果
  final ScrollPhysics defaultPhysics;
  
  /// 回弹物理效果
  final ScrollPhysics bouncePhysics;
  
  /// 物理效果改变时的回调
  final VoidCallback? onPhysicsChanged;
  
  /// 是否启用调试日志
  final bool enableDebugLog;

  ScrollPhysicsManager({
    required ScrollController scrollController,
    this.scrollThreshold = 100.0,
    this.defaultPhysics = const ClampingScrollPhysics(),
    this.bouncePhysics = const BouncingScrollPhysics(),
    this.onPhysicsChanged,
    this.enableDebugLog = false,
  }) : _scrollController = scrollController,
       _currentPhysics = const ClampingScrollPhysics();

  /// 当前使用的物理效果
  ScrollPhysics get currentPhysics => _currentPhysics;
  
  /// 是否处于回弹状态
  bool get isBouncing => _currentPhysics.runtimeType == bouncePhysics.runtimeType;
  
  /// 切换位置（如果有的话）
  double? get thresholdPosition => _bounceThresholdPosition;

  /// 初始化监听器
  void initialize() {
    _scrollController.addListener(_onScrollChanged);
    if (enableDebugLog) {
      print('ScrollPhysicsManager: 初始化完成，阈值=$scrollThreshold');
    }
  }

  /// 移除监听器
  void dispose() {
    _scrollController.removeListener(_onScrollChanged);
    if (enableDebugLog) {
      print('ScrollPhysicsManager: 已清理');
    }
  }

  /// 手动更新物理效果
  void updatePhysics(ScrollPhysics newPhysics) {
    if (_currentPhysics.runtimeType != newPhysics.runtimeType) {
      _currentPhysics = newPhysics;
      onPhysicsChanged?.call();
      
      if (enableDebugLog) {
        print('ScrollPhysicsManager: 手动更新物理效果为 ${newPhysics.runtimeType}');
      }
    }
  }

  /// 重置到默认状态
  void reset() {
    _currentPhysics = defaultPhysics;
    _bounceThresholdPosition = null;
    onPhysicsChanged?.call();
    
    if (enableDebugLog) {
      print('ScrollPhysicsManager: 重置到默认状态');
    }
  }

  /// 监听滚动变化
  void _onScrollChanged() {
    final double currentOffset = _scrollController.offset;
    
    if (enableDebugLog) {
      print('ScrollPhysicsManager: 滚动位置=$currentOffset, 阈值=$scrollThreshold, 当前物理=${_currentPhysics.runtimeType}');
    }
    
    // 向下滚动超过阈值，切换到回弹效果
    if (currentOffset > scrollThreshold && 
        _currentPhysics.runtimeType == defaultPhysics.runtimeType) {
      _currentPhysics = bouncePhysics;
      _bounceThresholdPosition = currentOffset;
      
      if (enableDebugLog) {
        print('ScrollPhysicsManager: 切换到回弹效果，记录位置=$currentOffset');
      }
      
      onPhysicsChanged?.call();
    }
    // 回滚到切换位置以下，恢复为默认物理效果
    else if (_bounceThresholdPosition != null && 
             currentOffset < _bounceThresholdPosition! && 
             _currentPhysics.runtimeType == bouncePhysics.runtimeType) {
      _currentPhysics = defaultPhysics;
      _bounceThresholdPosition = null;
      
      if (enableDebugLog) {
        print('ScrollPhysicsManager: 恢复到默认效果');
      }
      
      onPhysicsChanged?.call();
    }
  }
}

/// 滚动物理效果工具类
class ScrollPhysicsUtils {
  /// 创建标准的动态滚动物理效果管理器
  static ScrollPhysicsManager createStandard({
    required ScrollController scrollController,
    VoidCallback? onPhysicsChanged,
    bool enableDebugLog = false,
  }) {
    return ScrollPhysicsManager(
      scrollController: scrollController,
      scrollThreshold: 100.0,
      defaultPhysics: const ClampingScrollPhysics(),
      bouncePhysics: const BouncingScrollPhysics(),
      onPhysicsChanged: onPhysicsChanged,
      enableDebugLog: enableDebugLog,
    );
  }

  /// 创建自定义的动态滚动物理效果管理器
  static ScrollPhysicsManager createCustom({
    required ScrollController scrollController,
    required double scrollThreshold,
    required ScrollPhysics defaultPhysics,
    required ScrollPhysics bouncePhysics,
    VoidCallback? onPhysicsChanged,
    bool enableDebugLog = false,
  }) {
    return ScrollPhysicsManager(
      scrollController: scrollController,
      scrollThreshold: scrollThreshold,
      defaultPhysics: defaultPhysics,
      bouncePhysics: bouncePhysics,
      onPhysicsChanged: onPhysicsChanged,
      enableDebugLog: enableDebugLog,
    );
  }

  /// 为页面快速设置动态滚动效果
  /// 返回一个包含 physics 和 manager 的 Map
  static Map<String, dynamic> setupForPage({
    required ScrollController scrollController,
    VoidCallback? onPhysicsChanged,
    bool enableDebugLog = false,
  }) {
    final manager = createStandard(
      scrollController: scrollController,
      onPhysicsChanged: onPhysicsChanged,
      enableDebugLog: enableDebugLog,
    );
    
    manager.initialize();
    
    return {
      'manager': manager,
      'physics': manager.currentPhysics,
    };
  }
}

/// Mixin 用于简化在 StatefulWidget 中的使用
mixin DynamicScrollPhysicsMixin<T extends StatefulWidget> on State<T> {
  ScrollPhysicsManager? _physicsManager;
  
  /// 滚动阈值，子类可以重写
  double get scrollThreshold => 100.0;
  
  /// 是否启用调试日志，子类可以重写
  bool get enableScrollDebugLog => false;
  
  /// 当前物理效果
  ScrollPhysics get currentScrollPhysics => 
      _physicsManager?.currentPhysics ?? const ClampingScrollPhysics();
  
  /// 初始化动态滚动物理效果
  void initDynamicScrollPhysics(ScrollController controller) {
    _physicsManager = ScrollPhysicsManager(
      scrollController: controller,
      scrollThreshold: scrollThreshold,
      onPhysicsChanged: () {
        setState(() {});
        onScrollPhysicsChanged();
      },
      enableDebugLog: enableScrollDebugLog,
    );
    _physicsManager!.initialize();
  }
  
  /// 物理效果改变时的回调，子类可以重写
  void onScrollPhysicsChanged() {}
  
  /// 清理动态滚动物理效果
  void disposeDynamicScrollPhysics() {
    _physicsManager?.dispose();
    _physicsManager = null;
  }
  
  /// 重置滚动物理效果
  void resetScrollPhysics() {
    _physicsManager?.reset();
  }
}
