import 'package:flutter/material.dart';

// Widget信息工具类
class WidgetUtil {
  WidgetUtil._();

  // Widget尺寸信息
  static Size? getWidgetSize(GlobalKey key) {
    final RenderBox? renderBox =
        key.currentContext?.findRenderObject() as RenderBox?;
    return renderBox?.size;
  }

  static double? getWidgetWidth(GlobalKey key) {
    return getWidgetSize(key)?.width;
  }

  static double? getWidgetHeight(GlobalKey key) {
    return getWidgetSize(key)?.height;
  }

  // Widget位置信息
  static Offset? getWidgetGlobalPosition(GlobalKey key) {
    final RenderBox? renderBox =
        key.currentContext?.findRenderObject() as RenderBox?;
    return renderBox?.localToGlobal(Offset.zero);
  }

  static Rect? getWidgetBounds(GlobalKey key) {
    final size = getWidgetSize(key);
    final position = getWidgetGlobalPosition(key);

    if (size != null && position != null) {
      return Rect.fromLTWH(position.dx, position.dy, size.width, size.height);
    }
    return null;
  }

  static Offset? getWidgetCenter(GlobalKey key) {
    final bounds = getWidgetBounds(key);
    return bounds?.center;
  }

  // Widget可见性检测
  static bool isWidgetVisible(GlobalKey key, BuildContext context) {
    final bounds = getWidgetBounds(key);
    if (bounds == null) return false;

    final screenSize = MediaQuery.of(context).size;
    final screenRect = Rect.fromLTWH(0, 0, screenSize.width, screenSize.height);

    return screenRect.overlaps(bounds);
  }

  static bool isWidgetFullyVisible(GlobalKey key, BuildContext context) {
    final bounds = getWidgetBounds(key);
    if (bounds == null) return false;

    final screenSize = MediaQuery.of(context).size;
    final screenRect = Rect.fromLTWH(0, 0, screenSize.width, screenSize.height);

    return screenRect.contains(bounds.topLeft) &&
        screenRect.contains(bounds.bottomRight);
  }

  // Widget状态信息
  static bool isWidgetMounted(GlobalKey key) {
    return key.currentContext != null;
  }

  static bool hasRenderObject(GlobalKey key) {
    return key.currentContext?.findRenderObject() != null;
  }

  static BuildContext? getWidgetContext(GlobalKey key) {
    return key.currentContext;
  }

  // 滚动相关
  static Future<void> scrollToWidget(
    GlobalKey key, {
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) async {
    final context = key.currentContext;
    if (context == null) return;

    await Scrollable.ensureVisible(context, duration: duration, curve: curve);
  }

  // 获取Widget基本信息
  static Map<String, dynamic>? getWidgetInfo(
    GlobalKey key,
    BuildContext context,
  ) {
    if (!isWidgetMounted(key)) return null;

    final size = getWidgetSize(key);
    final position = getWidgetGlobalPosition(key);
    final bounds = getWidgetBounds(key);
    final center = getWidgetCenter(key);

    return {
      'isMounted': isWidgetMounted(key),
      'hasRenderObject': hasRenderObject(key),
      'width': size?.width,
      'height': size?.height,
      'globalX': position?.dx,
      'globalY': position?.dy,
      'centerX': center?.dx,
      'centerY': center?.dy,
      'boundsLeft': bounds?.left,
      'boundsTop': bounds?.top,
      'boundsRight': bounds?.right,
      'boundsBottom': bounds?.bottom,
      'isVisible': isWidgetVisible(key, context),
      'isFullyVisible': isWidgetFullyVisible(key, context),
    };
  }

  static void printWidgetInfo(
    GlobalKey key,
    BuildContext context, {
    String? widgetName,
  }) {
    final info = getWidgetInfo(key, context);
    if (info != null) {
      final name = widgetName ?? 'Widget';
      debugPrint('=== $name 信息 ===');
      debugPrint('已挂载: ${info['isMounted']}');
      debugPrint(
        '尺寸: ${info['width']?.toStringAsFixed(1)} x ${info['height']?.toStringAsFixed(1)}',
      );
      debugPrint(
        '全局位置: (${info['globalX']?.toStringAsFixed(1)}, ${info['globalY']?.toStringAsFixed(1)})',
      );
      debugPrint('可见性: ${info['isVisible'] ? '可见' : '不可见'}');
      debugPrint('================');
    } else {
      debugPrint('无法获取Widget信息: Widget未挂载或无效');
    }
  }
}
