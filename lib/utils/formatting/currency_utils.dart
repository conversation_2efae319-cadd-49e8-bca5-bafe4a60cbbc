/*
 * 货币工具类
 * 
 * 功能：
 * - 提供货币符号获取的便捷方法
 * - 价格格式化工具
 * - 货币转换和显示工具
 * - 确保全局货币符号使用的一致性
 */

import '../../services/data/currency_service.dart';
import '../../core/config/static/currency_constants.dart';
import '../../core/models/currency_model.dart';

/// 货币工具类
class CurrencyUtils {
  CurrencyUtils._();

  // ========== 货币符号获取 ==========

  /// 获取当前选择的货币符号
  static String getCurrentSymbol() {
    return CurrencyService.instance.currentCurrencySymbol;
  }

  /// 获取当前选择的货币代码
  static String getCurrentCode() {
    return CurrencyService.instance.currentCurrencyCode;
  }

  /// 获取当前选择的货币名称
  static String getCurrentName() {
    return CurrencyService.instance.currentCurrencyName;
  }

  /// 获取当前选择的货币模型
  static CurrencyModel getCurrentCurrency() {
    return CurrencyService.instance.currentCurrency;
  }

  /// 根据货币代码获取符号
  static String getSymbolByCode(String code) {
    return CurrencyConfig.getCurrencySymbol(code);
  }

  /// 根据货币代码获取名称
  static String getNameByCode(String code) {
    return CurrencyConfig.getCurrencyName(code);
  }

  // ========== 价格格式化 ==========

  /// 格式化价格（使用当前货币符号）
  static String formatPrice(double price, {int decimalPlaces = 2}) {
    return CurrencyService.instance.formatPrice(
      price,
      decimalPlaces: decimalPlaces,
    );
  }

  /// 格式化价格值（不带符号）
  static String formatPriceValue(double price, {int decimalPlaces = 2}) {
    return CurrencyService.instance.formatPriceValue(
      price,
      decimalPlaces: decimalPlaces,
    );
  }

  /// 格式化价格（指定货币代码）
  static String formatPriceWithCode(
    double price,
    String currencyCode, {
    int decimalPlaces = 2,
  }) {
    if (price.isNaN || price.isInfinite) {
      final symbol = getSymbolByCode(currencyCode);
      return '$symbol${'0.${'0' * decimalPlaces}'}';
    }

    final symbol = getSymbolByCode(currencyCode);
    return '$symbol${price.toStringAsFixed(decimalPlaces)}';
  }

  /// 智能格式化加密货币价格
  /// 根据价格大小自动选择合适的小数位数
  static String formatCryptoPrice(double price, {String? currencyCode}) {
    if (price.isNaN || price.isInfinite) return formatPrice(0.0);

    int decimalPlaces;
    if (price >= 1000) {
      decimalPlaces = 1;
    } else if (price >= 1) {
      decimalPlaces = 2;
    } else if (price >= 0.01) {
      decimalPlaces = 4;
    } else {
      decimalPlaces = 6;
    }

    if (currencyCode != null) {
      return formatPriceWithCode(
        price,
        currencyCode,
        decimalPlaces: decimalPlaces,
      );
    } else {
      return formatPrice(price, decimalPlaces: decimalPlaces);
    }
  }

  // ========== 货币验证和查询 ==========

  /// 验证货币代码是否支持
  static bool isSupportedCurrency(String code) {
    return CurrencyConfig.isSupportedCurrency(code);
  }

  /// 获取所有支持的货币
  static List<CurrencyModel> getSupportedCurrencies() {
    return CurrencyConfig.supportedCurrencies;
  }

  /// 搜索货币
  static List<CurrencyModel> searchCurrencies(String query) {
    return CurrencyConfig.searchCurrencies(query);
  }

  // ========== 货币切换 ==========

  /// 切换到指定货币
  static Future<bool> changeCurrency(String currencyCode) {
    return CurrencyService.instance.changeCurrency(currencyCode);
  }

  /// 切换到指定货币模型
  static Future<bool> changeCurrencyModel(CurrencyModel currency) {
    return CurrencyService.instance.changeCurrencyModel(currency);
  }

  /// 重置为默认货币
  static Future<bool> resetToDefault() {
    return CurrencyService.instance.resetToDefault();
  }

  // ========== 特殊格式化方法 ==========

  /// 格式化市值显示
  /// 自动添加单位（万、亿）
  static String formatMarketCap(double value, {String? currencyCode}) {
    if (value.isNaN || value.isInfinite) {
      return currencyCode != null
          ? formatPriceWithCode(0.0, currencyCode)
          : formatPrice(0.0);
    }

    String formattedValue;
    if (value >= 1e8) {
      formattedValue = '${(value / 1e8).toStringAsFixed(2)}亿';
    } else if (value >= 1e4) {
      formattedValue = '${(value / 1e4).toStringAsFixed(2)}万';
    } else {
      formattedValue = value.toStringAsFixed(2);
    }

    final symbol =
        currencyCode != null
            ? getSymbolByCode(currencyCode)
            : getCurrentSymbol();

    return '$symbol$formattedValue';
  }

  /// 格式化成交量显示
  static String formatVolume(
    double volume,
    String unit, {
    String? currencyCode,
  }) {
    if (volume.isNaN || volume.isInfinite) {
      final symbol =
          currencyCode != null
              ? getSymbolByCode(currencyCode)
              : getCurrentSymbol();
      return '${symbol}0$unit';
    }

    String formattedVolume;
    if (volume >= 10000) {
      formattedVolume = volume.toStringAsFixed(0);
    } else if (volume >= 1000) {
      formattedVolume = volume.toStringAsFixed(1);
    } else if (volume >= 100) {
      formattedVolume = volume.toStringAsFixed(1);
    } else {
      formattedVolume = volume.toStringAsFixed(2);
    }

    final symbol =
        currencyCode != null
            ? getSymbolByCode(currencyCode)
            : getCurrentSymbol();

    return '$symbol$formattedVolume$unit';
  }

  // ========== 便捷方法 ==========

  /// 获取带货币符号的空字符串（用于占位）
  static String getEmptyPriceString({int decimalPlaces = 2}) {
    return formatPrice(0.0, decimalPlaces: decimalPlaces);
  }

  /// 获取货币符号文本（不带数值）
  static String getSymbolText() {
    return getCurrentSymbol();
  }

  /// 检查是否为人民币
  static bool isCNY([String? currencyCode]) {
    final code = currencyCode ?? getCurrentCode();
    return code.toUpperCase() == 'CNY';
  }

  /// 检查是否为美元
  static bool isUSD([String? currencyCode]) {
    final code = currencyCode ?? getCurrentCode();
    return code.toUpperCase() == 'USD';
  }

  /// 获取货币显示文本（名称 + 符号）
  static String getCurrencyDisplayText([String? currencyCode]) {
    final code = currencyCode ?? getCurrentCode();
    final currency = CurrencyConfig.getCurrencyByCode(code);
    if (currency == null) return code;

    return '${currency.name} ${currency.symbol}';
  }

  /// 获取货币完整信息文本（名称 + 符号 + 代码）
  static String getCurrencyFullText([String? currencyCode]) {
    final code = currencyCode ?? getCurrentCode();
    final currency = CurrencyConfig.getCurrencyByCode(code);
    if (currency == null) return code;

    return '${currency.name} ${currency.symbol} (${currency.code})';
  }
}
