import 'package:intl/intl.dart';

// 数字格式化工具类
class NumberFormatUtil {
  NumberFormatUtil._();

  // 格式化数字，添加千位分隔符
  static String formatWithComma(dynamic number, {int decimalDigits = 2}) {
    if (number == null) return '--';

    try {
      final formatter = NumberFormat.currency(symbol: '', decimalDigits: decimalDigits);
      return formatter.format(number);
    } catch (e) {
      return number.toString();
    }
  }

  // 格式化货币
  static String formatCurrency(dynamic number, {String symbol = '\$', int decimalDigits = 2}) {
    if (number == null) return '--';

    try {
      final formatter = NumberFormat.currency(symbol: symbol, decimalDigits: decimalDigits);
      return formatter.format(number);
    } catch (e) {
      return '$symbol${number.toString()}';
    }
  }

  // 格式化百分比
  static String formatPercentage(dynamic number, {int decimalDigits = 2}) {
    if (number == null) return '--';

    try {
      final formatter = NumberFormat.percentPattern();
      formatter.minimumFractionDigits = decimalDigits;
      formatter.maximumFractionDigits = decimalDigits;
      return formatter.format(number / 100);
    } catch (e) {
      return '${number.toString()}%';
    }
  }

  // 格式化紧凑数字（如1K, 1M）
  static String formatCompact(dynamic number) {
    if (number == null) return '--';

    try {
      final formatter = NumberFormat.compact();
      return formatter.format(number);
    } catch (e) {
      return number.toString();
    }
  }

  // 格式化小数位数
  static String formatDecimal(dynamic number, int decimalDigits) {
    if (number == null) return '--';

    try {
      final formatter = NumberFormat();
      formatter.minimumFractionDigits = decimalDigits;
      formatter.maximumFractionDigits = decimalDigits;
      return formatter.format(number);
    } catch (e) {
      return number.toString();
    }
  }

  // === 加密货币专用格式化方法 ===

  /// 格式化加密货币价格显示
  /// 根据价格大小自动选择合适的小数位数
  static String formatCryptoPrice(double price) {
    if (price.isNaN || price.isInfinite) return '0.00';

    if (price >= 1000) {
      return price.toStringAsFixed(1);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else if (price >= 0.01) {
      return price.toStringAsFixed(4);
    } else {
      return price.toStringAsFixed(6);
    }
  }

  /// 格式化人民币价格显示
  static String formatCnyPrice(double price) {
    if (price.isNaN || price.isInfinite) return '0.00';

    if (price >= 10000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1000) {
      return price.toStringAsFixed(1);
    } else {
      return price.toStringAsFixed(2);
    }
  }

  /// 格式化涨跌幅百分比显示
  /// [showSign] 是否显示正号
  /// [decimalPlaces] 小数位数，默认2位
  static String formatChangePercentage(double percentage, {bool showSign = true, int decimalPlaces = 2}) {
    if (percentage.isNaN || percentage.isInfinite) return '0.00%';

    final sign = showSign && percentage > 0 ? '+' : '';
    return '$sign${percentage.toStringAsFixed(decimalPlaces)}%';
  }

  /// 格式化成交量显示
  /// 自动添加单位（万、亿）
  static String formatVolume(double volume, String unit) {
    if (volume.isNaN || volume.isInfinite) return '0$unit';

    if (volume >= 10000) {
      return '${volume.toStringAsFixed(0)}$unit';
    } else if (volume >= 1000) {
      return '${volume.toStringAsFixed(1)}$unit';
    } else if (volume >= 100) {
      return '${volume.toStringAsFixed(1)}$unit';
    } else {
      return '${volume.toStringAsFixed(2)}$unit';
    }
  }

  /// 安全的数字转换
  /// 处理 null 和异常情况
  static double safeToDouble(dynamic value, {double defaultValue = 0.0}) {
    if (value == null) return defaultValue;

    try {
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) {
        final parsed = double.tryParse(value);
        return parsed ?? defaultValue;
      }
      return defaultValue;
    } catch (e) {
      return defaultValue;
    }
  }

  /// 判断数字是否有效
  static bool isValidNumber(double? value) {
    return value != null && !value.isNaN && !value.isInfinite;
  }
}
