import 'dart:convert';
import 'package:qubic_exchange/routes/index.dart';
import 'package:flutter/material.dart';
import 'package:qubic_exchange/services/config/api_route.dart';
import 'package:qubic_exchange/services/clients/dio_request.dart';
import 'package:qubic_exchange/models/user/user_info.dart';
import 'package:qubic_exchange/models/currency/index.dart';
import 'package:qubic_exchange/services/data/currency_service.dart';
import 'package:qubic_exchange/services/common/common_service.dart';
import 'package:qubic_exchange/services/network/websocket_service.dart';
import 'package:qubic_exchange/services/assets/index.dart';
import 'package:qubic_exchange/services/storage/storage_service.dart';

/// 身份验证状态管理提供者
class AuthProvider extends ChangeNotifier {
  // 用户信息
  UserInfo? _userInfo;

  // 访问令牌
  String? _accessToken;

  // 刷新令牌
  String? _refreshToken;

  // 令牌过期时间
  DateTime? _expireAt;

  // 是否已初始化
  bool _isInitialized = false;

  // 用户信息请求相关
  DateTime? _lastUserInfoRequestTime;
  static const Duration _userInfoRequestInterval = Duration(minutes: 1);

  // 货币相关属性
  SupportedCurrency? _selectedCurrency;
  Map<String, String> _exchangeRates = {};
  List<SupportedCurrency> _supportedCurrencies = [];
  double _currentRate = 1.0;

  /// 构造函数，自动初始化
  AuthProvider() {
    _initializeAuth();
    WidgetsBinding.instance.addObserver(_AppLifecycleObserver(this));
  }

  /// 初始化认证数据
  Future<void> _initializeAuth() async {
    try {
      await loadAuthDataFromStorage();

      // 设置DioRequest的AuthProvider实例
      DioRequest.setAuthProvider(this);

      // 异步连接WebSocket
      _initializeWebSocket();

      // 如果是登录状态，异步上报IP位置信息和获取用户信息
      if (isLoggedIn) {
        _reportIpLocation();
        _fetchUserInfo();
        _initializeAssets();
      }

      initializeCurrency(); // 异步执行，不等待完成
      _isInitialized = true;
      debugPrint('🔐 AuthProvider 初始化完成 - 登录状态: $isLoggedIn');
    } catch (e) {
      _isInitialized = true;
      debugPrint('❌ AuthProvider 初始化失败: $e');
    }
  }

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  // 获取用户信息
  UserInfo? get userInfo => _userInfo;

  // 获取访问令牌
  String? get accessToken => _accessToken;

  // 获取刷新令牌
  String? get refreshToken => _refreshToken;

  // 获取令牌过期时间
  DateTime? get expireAt => _expireAt;

  // 检查令牌是否过期
  bool get isTokenExpired {
    if (_expireAt == null) return true;
    return DateTime.now().isAfter(_expireAt!);
  }

  // 检查令牌是否即将过期（30分钟内）
  bool get isTokenExpiringSoon {
    if (_expireAt == null) return true;
    final now = DateTime.now();
    final thirtyMinutesFromNow = now.add(const Duration(minutes: 30));
    return _expireAt!.isBefore(thirtyMinutesFromNow);
  }

  // 检查用户是否已登录
  bool get isLoggedIn => _accessToken != null;

  /// 初始化，从存储加载认证数据
  Future<void> init() async {
    await loadAuthDataFromStorage();
  }

  // 设置认证数据
  Future<void> setAuthData({UserInfo? userInfo, String? accessToken, String? refreshToken, DateTime? expireAt}) async {
    final wasLoggedIn = isLoggedIn;
    _userInfo = userInfo;
    _accessToken = accessToken;
    _refreshToken = refreshToken;
    _expireAt = expireAt;
    await _saveAuthDataToStorage();

    // 如果从未登录变为已登录，获取用户信息和资产数据
    if (!wasLoggedIn && isLoggedIn) {
      _fetchUserInfo();
      _initializeAssets();
    }

    notifyListeners();
  }

  // 更新用户信息
  Future<void> updateUserInfo(UserInfo userInfo) async {
    _userInfo = userInfo;
    await _saveAuthDataToStorage();
    notifyListeners();
  }

  // 更新访问令牌
  Future<void> updateAccessToken(String accessToken, DateTime expireAt) async {
    _accessToken = accessToken;
    _expireAt = expireAt;
    await _saveAuthDataToStorage();
    notifyListeners();
  }

  // 清除认证数据（登出）
  Future<void> clearAuthData() async {
    _userInfo = null;
    _accessToken = null;
    _refreshToken = null;
    _expireAt = null;

    // 重置资产服务
    AssetsService.instance.reset();

    await StorageService.instance.remove('auth_data');
    notifyListeners();
  }

  // 从持久化存储加载认证数据
  Future<void> loadAuthDataFromStorage() async {
    try {
      final authDataString = StorageService.instance.getString('auth_data');
      if (authDataString != null) {
        final authData = json.decode(authDataString);
        if (authData['user_info'] != null) {
          _userInfo = UserInfo.fromJson(authData['user_info']);
        }

        _accessToken = authData['access_token'];
        _refreshToken = authData['refresh_token'];

        if (authData['expire_at'] != null) {
          _expireAt = DateTime.parse(authData['expire_at']);
        }
        // 如果令牌已过期，清除数据
        if (isTokenExpired) {
          debugPrint('⏰ 令牌已过期，清除认证数据');
          await clearAuthData();
        } else {
          debugPrint('✅ 令牌有效，保持登录状态');
          notifyListeners();
        }
      } else {
        debugPrint('📭 未找到存储的认证数据');
      }
    } catch (e) {
      debugPrint('❌ 加载认证数据失败: $e');
      // 发生错误时清除数据
      await clearAuthData();
    }
  }

  // 将认证数据保存到持久化存储
  Future<void> _saveAuthDataToStorage() async {
    try {
      final authData = {
        'user_info': _userInfo?.toJson(),
        'access_token': _accessToken,
        'refresh_token': _refreshToken,
        'expire_at': _expireAt?.toIso8601String(),
      };
      await StorageService.instance.setString('auth_data', json.encode(authData));
    } catch (e) {
      debugPrint('❌ 保存认证数据失败: $e');
      // 保存失败时忽略错误
    }
  }

  // 增加一个快速检查方法，只检查本地令牌是否存在和有效，不发网络请求
  bool isLocallyLoggedIn() {
    return _accessToken != null && !isTokenExpired;
  }

  /// 自动刷新token
  /// 返回值：true 表示刷新成功，false 表示刷新失败
  Future<bool> refreshTokenIfNeeded() async {
    try {
      // 如果没有刷新令牌，无法刷新
      if (_refreshToken == null) {
        return false;
      }

      // 如果令牌还没有过期且不即将过期，不需要刷新
      if (!isTokenExpired && !isTokenExpiringSoon) {
        return true;
      }
      debugPrint("刷新令牌--------");
      // 调用刷新token API
      final dioRequest = DioRequest.instance;
      final response = await dioRequest.post(ApiRoute.refreshToken, body: {'refresh_token': _refreshToken});

      if (response.success && response.data != null) {
        final data = response.data as Map<String, dynamic>;

        // 更新token信息
        _accessToken = data['access_token'];
        if (data['refresh_token'] != null) {
          _refreshToken = data['refresh_token'];
        }

        // 计算新的过期时间
        final expiresIn = data['expires_in'] as int? ?? 3600; // 默认1小时
        _expireAt = DateTime.now().add(Duration(seconds: expiresIn));

        // 保存到本地存储
        await _saveAuthDataToStorage();
        notifyListeners();

        return true;
      }

      // 刷新失败，清除认证数据
      await clearAuthData();
      return false;
    } catch (e) {
      // 刷新失败，清除认证数据
      await clearAuthData();
      return false;
    }
  }

  /// 智能登录状态检查
  /// 会自动尝试刷新token，如果刷新失败则返回false
  Future<bool> checkAndRefreshLoginStatus() async {
    try {
      // 如果没有访问令牌，直接返回未登录
      if (_accessToken == null) {
        return false;
      }

      // 如果令牌已过期，尝试刷新
      if (isTokenExpired) {
        return await refreshTokenIfNeeded();
      }

      // 如果令牌即将过期，主动刷新
      if (isTokenExpiringSoon) {
        await refreshTokenIfNeeded();
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  Future<void> handleLogout(BuildContext context) async {
    try {
      await clearAuthData();

      // 跳转到登录界面
      if (context.mounted) {
        Navigator.of(context).pushNamedAndRemoveUntil(AppRoutes.login, (Route<dynamic> route) => false);
      }
    } catch (e) {
      // 处理错误情况
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('退出登录失败: $e'), backgroundColor: Colors.red));
      }
    }
  }

  // ========== 货币相关方法 ==========
  /// 货币相关getter
  SupportedCurrency? get selectedCurrency => _selectedCurrency;
  Map<String, String> get exchangeRates => _exchangeRates;
  List<SupportedCurrency> get supportedCurrencies => _supportedCurrencies;
  double get currentRate => _currentRate;

  /// 初始化货币
  Future<void> initializeCurrency() async {
    try {
      // 先从缓存加载货币数据
      await _loadCachedCurrencyData();

      // 然后获取最新数据
      final currencyResponse = await CurrencyService.instance.fetchCurrencyData();
      if (currencyResponse != null) {
        _supportedCurrencies = currencyResponse.supported;
        _exchangeRates = currencyResponse.rates;

        // 如果有缓存的选择货币，在新数据中查找匹配的
        if (_selectedCurrency != null) {
          final foundCurrency = _supportedCurrencies.firstWhere(
            (currency) => currency.name == _selectedCurrency!.name,
            orElse: () => _supportedCurrencies.isNotEmpty ? _supportedCurrencies.first : _selectedCurrency!,
          );
          _selectedCurrency = foundCurrency;
          _updateCurrentRate();
        } else if (_supportedCurrencies.isNotEmpty) {
          _selectedCurrency = _supportedCurrencies.first;
          _updateCurrentRate();
        }

        notifyListeners();
      }
    } catch (e) {
      debugPrint('初始化货币失败: $e');
    }
  }

  /// 从缓存加载货币数据
  Future<void> _loadCachedCurrencyData() async {
    try {
      // 加载选择的货币
      final savedCurrencyJson = StorageService.instance.getString('selected_currency_data');
      if (savedCurrencyJson != null) {
        final currencyMap = Map<String, dynamic>.from(jsonDecode(savedCurrencyJson));
        _selectedCurrency = SupportedCurrency.fromJson(currencyMap);
      }

      // 加载汇率数据
      final savedRatesJson = StorageService.instance.getString('exchange_rates_data');
      if (savedRatesJson != null) {
        _exchangeRates = Map<String, String>.from(jsonDecode(savedRatesJson));
        _updateCurrentRate();
      }

      // 加载当前汇率
      _currentRate = StorageService.instance.getDouble('current_rate') ?? 1.0;
    } catch (e) {
      debugPrint('加载缓存货币数据失败: $e');
    }
  }

  /// 更新当前汇率
  void _updateCurrentRate() {
    if (_selectedCurrency?.value != null && _exchangeRates.containsKey(_selectedCurrency!.value)) {
      _currentRate = double.tryParse(_exchangeRates[_selectedCurrency!.value]!) ?? 1.0;
    }
  }

  /// 更新选择的货币
  Future<void> updateSelectedCurrency(SupportedCurrency currency) async {
    try {
      _selectedCurrency = currency;
      _updateCurrentRate();

      // 保存完整的货币数据到本地缓存
      // 保存货币完整信息
      await StorageService.instance.setString('selected_currency_data', jsonEncode(currency.toJson()));

      // 保存汇率数据
      await StorageService.instance.setString('exchange_rates_data', jsonEncode(_exchangeRates));

      // 保存当前汇率
      await StorageService.instance.setDouble('current_rate', _currentRate);

      await CurrencyService.instance.setSelectedCurrency(currency);
      notifyListeners();
    } catch (e) {
      debugPrint('更新货币失败: $e');
    }
  }

  // ========== IP位置上报 ==========
  /// 异步上报IP位置信息
  Future<void> _reportIpLocation() async {
    try {
      final ipInfo = await CommonService.instance.getIpInfo();
      if (ipInfo != null) {
        final locationData = {
          "country": ipInfo['country'] ?? '',
          "province": ipInfo['region'] ?? '',
          "area": ipInfo['city'] ?? '',
          'ip': ipInfo['ip'] ?? '',
        };

        await DioRequest.instance.post(ApiRoute.reportIpLocation, body: locationData, requireAuth: true);
      }
    } catch (e) {
      debugPrint('上报IP位置失败: $e');
    }
  }

  // ========== 资产管理 ==========
  /// 初始化资产服务
  Future<void> _initializeAssets() async {
    try {
      debugPrint('💰 开始初始化用户资产服务...');
      await AssetsService.instance.initialize();
      debugPrint('✅ 用户资产服务初始化完成');
    } catch (e) {
      debugPrint('❌ 初始化用户资产服务失败: $e');
    }
  }

  // ========== WebSocket管理 ==========
  /// 初始化WebSocket连接
  Future<void> _initializeWebSocket() async {
    try {
      // 异步连接WebSocket
      WebSocketService.instance.connect();

      // 监听WebSocket连接状态
      WebSocketService.instance.connectionStream.listen((isConnected) {
        if (isConnected && isLoggedIn) {
          // WebSocket连接成功且用户已登录，发送认证消息
          WebSocketService.instance.authenticate(this);
        }
      });
    } catch (e) {
      debugPrint('初始化WebSocket失败: $e');
    }
  }

  /// 重写notifyListeners以监听登录状态变化
  @override
  void notifyListeners() {
    super.notifyListeners();

    // 如果WebSocket已连接且用户已登录，发送认证消息
    if (WebSocketService.instance.isConnected && isLoggedIn) {
      WebSocketService.instance.authenticate(this);
    }
  }

  /// 获取用户信息
  Future<void> _fetchUserInfo() async {
    if (!isLoggedIn) return;

    try {
      final response = await DioRequest.instance.get(ApiRoute.userInfo, requireAuth: true);
      if (response.success && response.data != null) {
        final userInfo = UserInfo.fromJson(response.data);
        await updateUserInfo(userInfo);
        _lastUserInfoRequestTime = DateTime.now();
      } else {
        // 接口请求失败，清空登录状态
        await clearAuthData();
      }
    } catch (e) {
      debugPrint('获取用户信息失败: $e');
      // 数据异常，清空登录状态
      await clearAuthData();
    }
  }

  /// 检查是否需要刷新用户信息
  void _checkAndRefreshUserInfo() {
    if (!isLoggedIn) return;

    final now = DateTime.now();
    if (_lastUserInfoRequestTime == null || now.difference(_lastUserInfoRequestTime!) > _userInfoRequestInterval) {
      _fetchUserInfo();
    }
  }

  /// 处理应用从后台切换回前台
  void _onAppResumed() {
    _checkAndRefreshUserInfo();
  }
}

/// 应用生命周期监听器
class _AppLifecycleObserver with WidgetsBindingObserver {
  final AuthProvider _authProvider;

  _AppLifecycleObserver(this._authProvider);

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _authProvider._onAppResumed();
    }
  }
}
