// 路由配置管理
import 'package:flutter/material.dart';
import '../constants/app_routes.dart';
import '../constants/pages_path.dart';

/// 路由转场动画类型
enum RouteTransitionType {
  slide, // 滑动（默认）
  fade, // 淡入淡出
  scale, // 缩放
  slideUp, // 向上滑动
}

/// 路由配置模型
class RouteConfig {
  /// 路由路径
  final String path;

  /// 页面构建器
  final Widget Function(Map<String, dynamic>? args) builder;

  /// 默认转场动画类型
  final RouteTransitionType defaultTransition;

  /// 是否需要认证
  final bool requiresAuth;

  /// 所需权限列表
  final List<String> permissions;

  /// 路由描述
  final String description;

  /// 是否缓存页面
  final bool maintainState;

  const RouteConfig({
    required this.path,
    required this.builder,
    this.defaultTransition = RouteTransitionType.slide,
    this.requiresAuth = false,
    this.permissions = const [],
    this.description = '',
    this.maintainState = true,
  });
}

/// 路由配置管理器
class RouteConfigManager {
  RouteConfigManager._();

  /// 路由配置映射表
  static final Map<String, RouteConfig> _routeConfigs = {
    // ========== 系统路由 ==========
    AppRoutes.splash: RouteConfig(
      path: AppRoutes.splash,
      builder: (args) => const SplashPage(),
      defaultTransition: RouteTransitionType.fade,
      description: '启动页面',
    ),

    AppRoutes.mainTabbarScreen: RouteConfig(
      path: AppRoutes.mainTabbarScreen,
      builder: (args) {
        final isElevatedMode = args?['isElevatedMode'] as bool? ?? true;
        return MainTabPage(
          key: MainTabPage.globalKey,
          isElevatedMode: isElevatedMode,
        );
      },
      description: '主标签页',
    ),

    AppRoutes.notFound: RouteConfig(
      path: AppRoutes.notFound,
      builder: (args) => _build404Page(args),
      description: '404页面',
    ),

    // ========== 认证路由 ==========
    AppRoutes.login: RouteConfig(
      path: AppRoutes.login,
      builder: (args) => const LoginPage(),
      defaultTransition: RouteTransitionType.slideUp,
      description: '登录页面',
    ),
    AppRoutes.register: RouteConfig(
      path: AppRoutes.register,
      builder: (args) => const RegisterPage(),
      defaultTransition: RouteTransitionType.slideUp,
      description: '注册页面',
    ),

    AppRoutes.forgotPassword: RouteConfig(
      path: AppRoutes.forgotPassword,
      builder: (args) => const ForgotPasswordPage(),
      description: '忘记密码页面',
    ),

    AppRoutes.verifyCode: RouteConfig(
      path: AppRoutes.verifyCode,
      builder: (args) => const VerifyCodePage(),
      description: '验证码页面',
    ),

    AppRoutes.countryCode: RouteConfig(
      path: AppRoutes.countryCode,
      builder: (args) => const CountryCodePage(),
      description: '国家代码选择页面',
    ),

    AppRoutes.authFaceid: RouteConfig(
      path: AppRoutes.authFaceid,
      builder: (args) => const AuthFaceidPage(),
      defaultTransition: RouteTransitionType.slideUp,
      description: 'Face ID设置页面',
    ),

    // ========== 用户路由 ==========
    AppRoutes.profilePage: RouteConfig(
      path: AppRoutes.profilePage,
      builder: (args) => const UserCenterScreen(),
      requiresAuth: true,
      description: '个人中心页面',
    ),

    AppRoutes.setting: RouteConfig(
      path: AppRoutes.setting,
      builder: (args) => const SettingsPage(),
      requiresAuth: true,
      description: '设置页面',
    ),

    // ========== 市场路由 ==========
    AppRoutes.searchCrypto: RouteConfig(
      path: AppRoutes.searchCrypto,
      builder: (args) => const SearchCryptoPage(),
      description: '加密货币搜索页面',
    ),

    // ========== 交易路由 ==========
    AppRoutes.klinePage: RouteConfig(
      path: AppRoutes.klinePage,
      builder: (args) => const KlinePage(),
      requiresAuth: true,
      description: 'K线图页面',
    ),

    AppRoutes.priceAlert: RouteConfig(
      path: AppRoutes.priceAlert,
      builder: (args) => const PriceAlert(),
      requiresAuth: true,
      description: '价格提醒页面',
    ),

    AppRoutes.spotOrders: RouteConfig(
      path: AppRoutes.spotOrders,
      builder: (args) => const SpotOrdersPage(),
      requiresAuth: true,
      description: '现货订单页面',
    ),
  };

  /// 获取路由配置
  static RouteConfig? getConfig(String routeName) {
    return _routeConfigs[routeName];
  }

  /// 检查路由是否存在
  static bool hasRoute(String routeName) {
    return _routeConfigs.containsKey(routeName);
  }

  /// 获取所有路由配置
  static Map<String, RouteConfig> getAllConfigs() {
    return Map.unmodifiable(_routeConfigs);
  }

  /// 获取需要认证的路由列表
  static List<String> getProtectedRoutes() {
    return _routeConfigs.entries
        .where((entry) => entry.value.requiresAuth)
        .map((entry) => entry.key)
        .toList();
  }

  /// 获取公开路由列表
  static List<String> getPublicRoutes() {
    return _routeConfigs.entries
        .where((entry) => !entry.value.requiresAuth)
        .map((entry) => entry.key)
        .toList();
  }

  /// 构建404页面
  static Widget _build404Page(Map<String, dynamic>? args) {
    final originalRoute = args?['originalRoute'] as String?;

    return Scaffold(
      appBar: AppBar(
        title: const Text('页面未找到'),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Builder(
        builder:
            (context) => Center(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 404图标
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // 404标题
                    const Text(
                      '404',
                      style: TextStyle(
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // 错误描述
                    Text(
                      '抱歉，您访问的页面不存在',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 8),

                    // 原始路由信息（如果有）
                    if (originalRoute != null) ...[
                      Text(
                        '请求的路由: $originalRoute',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[500],
                          fontFamily: 'monospace',
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                    ],

                    Text(
                      '请检查URL或返回首页',
                      style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 32),

                    // 操作按钮
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 返回按钮
                        OutlinedButton.icon(
                          onPressed: () {
                            Navigator.of(context).canPop()
                                ? Navigator.of(context).pop()
                                : Navigator.of(context).pushNamedAndRemoveUntil(
                                  AppRoutes.mainTabbarScreen,
                                  (route) => false,
                                );
                          },
                          icon: const Icon(Icons.arrow_back),
                          label: const Text('返回'),
                        ),

                        const SizedBox(width: 16),

                        // 首页按钮
                        ElevatedButton.icon(
                          onPressed: () {
                            Navigator.of(context).pushNamedAndRemoveUntil(
                              AppRoutes.mainTabbarScreen,
                              (route) => false,
                            );
                          },
                          icon: const Icon(Icons.home),
                          label: const Text('返回首页'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
      ),
    );
  }
}
