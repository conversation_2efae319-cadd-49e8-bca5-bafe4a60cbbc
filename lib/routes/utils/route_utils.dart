// 路由工具类
import 'package:flutter/material.dart';
import '../groups/route_groups.dart';
import '../config/route_config.dart';
import '../core/navigation_service.dart';

/// 路由工具类
///
/// 提供路由相关的便捷方法和工具函数
class RouteUtils {
  RouteUtils._();

  /// 检查路由是否有效
  static bool isValidRoute(String route) {
    return RouteConfigManager.hasRoute(route);
  }

  /// 获取路由的完整信息
  static Map<String, dynamic> getRouteInfo(String route) {
    final config = RouteConfigManager.getConfig(route);

    return {
      'route': route,
      'isValid': config != null,
      'group': RouteGroups.getRouteGroup(route),
      'displayName': RouteGroups.getRouteDisplayName(route),
      'requiresAuth': RouteGroups.requiresAuth(route),
      'isPublic': RouteGroups.isPublicRoute(route),
      'defaultTransition': config?.defaultTransition.name ?? 'slide',
      'description': config?.description ?? '',
      'breadcrumb': RouteGroups.getRouteBreadcrumb(route),
    };
  }

  /// 获取所有路由的信息
  static List<Map<String, dynamic>> getAllRoutesInfo() {
    final allConfigs = RouteConfigManager.getAllConfigs();
    return allConfigs.keys.map((route) => getRouteInfo(route)).toList();
  }

  /// 根据分组获取路由信息
  static List<Map<String, dynamic>> getRoutesByGroupInfo(String groupName) {
    final routes = RouteGroups.getRoutesByGroup(groupName);
    return routes.map((route) => getRouteInfo(route)).toList();
  }

  /// 搜索路由
  static List<Map<String, dynamic>> searchRoutes(String query) {
    final allRoutes = getAllRoutesInfo();
    final lowerQuery = query.toLowerCase();

    return allRoutes.where((routeInfo) {
      final route = routeInfo['route'] as String;
      final displayName = routeInfo['displayName'] as String;
      final description = routeInfo['description'] as String;

      return route.toLowerCase().contains(lowerQuery) ||
          displayName.toLowerCase().contains(lowerQuery) ||
          description.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// 获取面包屑导航组件
  static Widget buildBreadcrumb(
    String currentRoute, {
    TextStyle? textStyle,
    String separator = ' > ',
    Function(String)? onTap,
  }) {
    final breadcrumb = RouteGroups.getRouteBreadcrumb(currentRoute);

    return Row(
      children:
          breadcrumb.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final isLast = index == breadcrumb.length - 1;

            return Row(
              children: [
                GestureDetector(
                  onTap: onTap != null && !isLast ? () => onTap(item) : null,
                  child: Text(
                    item,
                    style:
                        textStyle?.copyWith(
                          color: isLast ? null : Colors.blue,
                          decoration: isLast ? null : TextDecoration.underline,
                        ) ??
                        TextStyle(
                          color: isLast ? Colors.black : Colors.blue,
                          decoration: isLast ? null : TextDecoration.underline,
                        ),
                  ),
                ),
                if (!isLast) Text(separator, style: textStyle),
              ],
            );
          }).toList(),
    );
  }

  /// 获取路由统计信息
  static Map<String, dynamic> getRouteStatistics() {
    final allRoutes = getAllRoutesInfo();
    final groupStats = RouteGroups.getGroupStats();

    return {
      'totalRoutes': allRoutes.length,
      'groupStats': groupStats,
      'authRequiredRoutes':
          allRoutes.where((r) => r['requiresAuth'] == true).length,
      'publicRoutes': allRoutes.where((r) => r['isPublic'] == true).length,
      'transitionTypes': _getTransitionTypeStats(allRoutes),
    };
  }

  /// 获取转场动画类型统计
  static Map<String, int> _getTransitionTypeStats(
    List<Map<String, dynamic>> routes,
  ) {
    final stats = <String, int>{};

    for (final route in routes) {
      final transition = route['defaultTransition'] as String;
      stats[transition] = (stats[transition] ?? 0) + 1;
    }

    return stats;
  }

  /// 验证路由跳转
  static Future<bool> validateNavigation(String targetRoute) async {
    // 检查路由是否存在
    if (!isValidRoute(targetRoute)) {
      debugPrint('❌ 路由不存在: $targetRoute');
      return false;
    }

    // 检查是否需要认证
    if (RouteGroups.requiresAuth(targetRoute)) {
      // 这里可以添加实际的认证检查逻辑
      // 例如：检查用户是否已登录
      debugPrint('🔐 路由需要认证: $targetRoute');
      // return AuthService.isLoggedIn();
    }

    return true;
  }

  /// 安全导航（带验证）
  static Future<bool> safeNavigateTo(
    String route, {
    Object? arguments,
    bool checkPermission = true,
  }) async {
    if (await validateNavigation(route)) {
      final result = await NavigationService().navigateTo(
        route,
        arguments: arguments,
        checkPermission: checkPermission,
      );
      return result != null;
    }
    return false;
  }

  /// 获取当前路由的相关路由推荐
  static List<String> getRelatedRoutes(String currentRoute) {
    final currentGroup = RouteGroups.getRouteGroup(currentRoute);
    final groupRoutes = RouteGroups.getRoutesByGroup(currentGroup);

    // 返回同组的其他路由
    return groupRoutes.where((route) => route != currentRoute).toList();
  }

  /// 检查路由跳转历史
  static List<String> getNavigationHistory() {
    // 这里可以实现导航历史记录功能
    // 需要在NavigationService中添加历史记录功能
    return [];
  }

  /// 清理无效路由
  static List<String> findInvalidRoutes(List<String> routes) {
    return routes.where((route) => !isValidRoute(route)).toList();
  }

  /// 生成路由调试信息
  static String generateDebugInfo() {
    final stats = getRouteStatistics();
    final buffer = StringBuffer();

    buffer.writeln('=== 路由系统调试信息 ===');
    buffer.writeln('总路由数: ${stats['totalRoutes']}');
    buffer.writeln('需要认证的路由: ${stats['authRequiredRoutes']}');
    buffer.writeln('公开路由: ${stats['publicRoutes']}');
    buffer.writeln();

    buffer.writeln('分组统计:');
    final groupStats = stats['groupStats'] as Map<String, int>;
    groupStats.forEach((group, count) {
      buffer.writeln('  $group: $count');
    });

    buffer.writeln();
    buffer.writeln('转场动画统计:');
    final transitionStats = stats['transitionTypes'] as Map<String, int>;
    transitionStats.forEach((type, count) {
      buffer.writeln('  $type: $count');
    });

    return buffer.toString();
  }

  /// 打印路由调试信息
  static void printDebugInfo() {
    debugPrint(generateDebugInfo());
  }
}
