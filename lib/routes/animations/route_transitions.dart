import 'package:flutter/material.dart';

/// 路由过渡动画类型枚举
enum RouteTransitionType {
  /// 默认滑动（从右到左）
  slide,

  /// 从底部向上滑动
  slideUp,

  /// 从上往下滑动（向下滑出）
  slideDown,

  /// 从左到右滑动
  slideLeft,

  /// 从右到左滑动
  slideRight,

  /// 淡入淡出
  fade,

  /// 缩放
  scale,

  /// 旋转
  rotation,

  /// 翻转
  flip,

  /// 弹性
  elastic,

  /// 无动画
  none,

  /// iOS风格
  cupertino,

  /// 自定义
  custom,
}

/// 路由过渡动画构建器
class RouteTransitions {
  RouteTransitions._();

  /// 构建路由过渡动画
  static Route<T> buildTransition<T extends Object?>(
    Widget page, {
    required RouteSettings settings,
    RouteTransitionType type = RouteTransitionType.slide,
    Duration duration = const Duration(milliseconds: 300),
    Duration reverseDuration = const Duration(milliseconds: 250),
    Curve curve = Curves.easeInOut,
    Curve reverseCurve = Curves.easeInOut,
    bool maintainState = true,
    bool fullscreenDialog = false,
  }) {
    switch (type) {
      case RouteTransitionType.slide:
        return _buildSlideTransition<T>(
          page,
          settings: settings,
          duration: duration,
          reverseDuration: reverseDuration,
          curve: curve,
          maintainState: maintainState,
        );

      case RouteTransitionType.slideUp:
        return _buildSlideUpTransition<T>(
          page,
          settings: settings,
          duration: duration,
          reverseDuration: reverseDuration,
          curve: curve,
          maintainState: maintainState,
        );

      case RouteTransitionType.slideDown:
        return _buildSlideDownTransition<T>(
          page,
          settings: settings,
          duration: duration,
          reverseDuration: reverseDuration,
          curve: curve,
          maintainState: maintainState,
        );

      case RouteTransitionType.slideLeft:
        return _buildSlideLeftTransition<T>(
          page,
          settings: settings,
          duration: duration,
          reverseDuration: reverseDuration,
          curve: curve,
          maintainState: maintainState,
        );

      case RouteTransitionType.slideRight:
        return _buildSlideRightTransition<T>(
          page,
          settings: settings,
          duration: duration,
          reverseDuration: reverseDuration,
          curve: curve,
          maintainState: maintainState,
        );

      case RouteTransitionType.fade:
        return _buildFadeTransition<T>(
          page,
          settings: settings,
          duration: duration,
          reverseDuration: reverseDuration,
          curve: curve,
          maintainState: maintainState,
        );

      case RouteTransitionType.scale:
        return _buildScaleTransition<T>(
          page,
          settings: settings,
          duration: duration,
          reverseDuration: reverseDuration,
          curve: curve,
          maintainState: maintainState,
        );

      case RouteTransitionType.rotation:
        return _buildRotationTransition<T>(
          page,
          settings: settings,
          duration: duration,
          reverseDuration: reverseDuration,
          curve: curve,
          maintainState: maintainState,
        );

      case RouteTransitionType.flip:
        return _buildFlipTransition<T>(
          page,
          settings: settings,
          duration: duration,
          reverseDuration: reverseDuration,
          curve: curve,
          maintainState: maintainState,
        );

      case RouteTransitionType.elastic:
        return _buildElasticTransition<T>(
          page,
          settings: settings,
          duration: duration,
          reverseDuration: reverseDuration,
          maintainState: maintainState,
        );

      case RouteTransitionType.none:
        return _buildNoTransition<T>(
          page,
          settings: settings,
          maintainState: maintainState,
        );

      case RouteTransitionType.cupertino:
        return _buildCupertinoTransition<T>(
          page,
          settings: settings,
          maintainState: maintainState,
        );

      case RouteTransitionType.custom:
        return MaterialPageRoute<T>(
          settings: settings,
          builder: (context) => page,
          maintainState: maintainState,
          fullscreenDialog: fullscreenDialog,
        );
    }
  }

  /// 默认滑动过渡（从右到左）
  static Route<T> _buildSlideTransition<T extends Object?>(
    Widget page, {
    required RouteSettings settings,
    Duration duration = const Duration(milliseconds: 300),
    Duration reverseDuration = const Duration(milliseconds: 250),
    Curve curve = Curves.easeInOut,
    bool maintainState = true,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: reverseDuration,
      maintainState: maintainState,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        final tween = Tween(begin: begin, end: end);
        final offsetAnimation = animation.drive(
          tween.chain(CurveTween(curve: curve)),
        );

        return SlideTransition(position: offsetAnimation, child: child);
      },
    );
  }

  /// 从底部向上滑动过渡
  static Route<T> _buildSlideUpTransition<T extends Object?>(
    Widget page, {
    required RouteSettings settings,
    Duration duration = const Duration(milliseconds: 350),
    Duration reverseDuration = const Duration(milliseconds: 250),
    Curve curve = Curves.easeOut,
    bool maintainState = true,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: reverseDuration,
      maintainState: maintainState,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        final tween = Tween(begin: begin, end: end);
        final offsetAnimation = animation.drive(
          tween.chain(CurveTween(curve: curve)),
        );

        return SlideTransition(position: offsetAnimation, child: child);
      },
    );
  }

  /// 从上往下滑动过渡（向下滑出）
  static Route<T> _buildSlideDownTransition<T extends Object?>(
    Widget page, {
    required RouteSettings settings,
    Duration duration = const Duration(milliseconds: 350),
    Duration reverseDuration = const Duration(milliseconds: 250),
    Curve curve = Curves.easeIn,
    bool maintainState = true,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: reverseDuration,
      maintainState: maintainState,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // 进入时从正常位置开始，退出时向下滑出
        const begin = Offset.zero;
        const end = Offset(0.0, 1.0); // 向下滑出到屏幕底部
        final tween = Tween(begin: begin, end: end);
        final offsetAnimation = secondaryAnimation.drive(
          tween.chain(CurveTween(curve: curve)),
        );

        return SlideTransition(position: offsetAnimation, child: child);
      },
    );
  }

  /// 从左到右滑动过渡
  static Route<T> _buildSlideLeftTransition<T extends Object?>(
    Widget page, {
    required RouteSettings settings,
    Duration duration = const Duration(milliseconds: 300),
    Duration reverseDuration = const Duration(milliseconds: 250),
    Curve curve = Curves.easeInOut,
    bool maintainState = true,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: reverseDuration,
      maintainState: maintainState,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(-1.0, 0.0);
        const end = Offset.zero;
        final tween = Tween(begin: begin, end: end);
        final offsetAnimation = animation.drive(
          tween.chain(CurveTween(curve: curve)),
        );

        return SlideTransition(position: offsetAnimation, child: child);
      },
    );
  }

  /// 从右到左滑动过渡
  static Route<T> _buildSlideRightTransition<T extends Object?>(
    Widget page, {
    required RouteSettings settings,
    Duration duration = const Duration(milliseconds: 300),
    Duration reverseDuration = const Duration(milliseconds: 250),
    Curve curve = Curves.easeInOut,
    bool maintainState = true,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: reverseDuration,
      maintainState: maintainState,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        final tween = Tween(begin: begin, end: end);
        final offsetAnimation = animation.drive(
          tween.chain(CurveTween(curve: curve)),
        );

        return SlideTransition(position: offsetAnimation, child: child);
      },
    );
  }

  /// 淡入淡出过渡
  static Route<T> _buildFadeTransition<T extends Object?>(
    Widget page, {
    required RouteSettings settings,
    Duration duration = const Duration(milliseconds: 300),
    Duration reverseDuration = const Duration(milliseconds: 250),
    Curve curve = Curves.easeInOut,
    bool maintainState = true,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: reverseDuration,
      maintainState: maintainState,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation.drive(CurveTween(curve: curve)),
          child: child,
        );
      },
    );
  }

  /// 缩放过渡
  static Route<T> _buildScaleTransition<T extends Object?>(
    Widget page, {
    required RouteSettings settings,
    Duration duration = const Duration(milliseconds: 300),
    Duration reverseDuration = const Duration(milliseconds: 250),
    Curve curve = Curves.easeInOut,
    bool maintainState = true,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: reverseDuration,
      maintainState: maintainState,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return ScaleTransition(
          scale: animation.drive(CurveTween(curve: curve)),
          child: child,
        );
      },
    );
  }

  /// 旋转过渡
  static Route<T> _buildRotationTransition<T extends Object?>(
    Widget page, {
    required RouteSettings settings,
    Duration duration = const Duration(milliseconds: 400),
    Duration reverseDuration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
    bool maintainState = true,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: reverseDuration,
      maintainState: maintainState,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return RotationTransition(
          turns: animation.drive(CurveTween(curve: curve)),
          child: FadeTransition(opacity: animation, child: child),
        );
      },
    );
  }

  /// 翻转过渡
  static Route<T> _buildFlipTransition<T extends Object?>(
    Widget page, {
    required RouteSettings settings,
    Duration duration = const Duration(milliseconds: 600),
    Duration reverseDuration = const Duration(milliseconds: 400),
    Curve curve = Curves.easeInOut,
    bool maintainState = true,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: reverseDuration,
      maintainState: maintainState,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return AnimatedBuilder(
          animation: animation,
          builder: (context, child) {
            final rotateAnim = Tween(
              begin: 0.0,
              end: 1.0,
            ).animate(CurvedAnimation(parent: animation, curve: curve));

            if (rotateAnim.value < 0.5) {
              return Transform(
                alignment: Alignment.center,
                transform:
                    Matrix4.identity()
                      ..setEntry(3, 2, 0.001)
                      ..rotateY(rotateAnim.value * 3.14159),
                child: Container(),
              );
            } else {
              return Transform(
                alignment: Alignment.center,
                transform:
                    Matrix4.identity()
                      ..setEntry(3, 2, 0.001)
                      ..rotateY((1 - rotateAnim.value) * 3.14159),
                child: child,
              );
            }
          },
          child: child,
        );
      },
    );
  }

  /// 弹性过渡
  static Route<T> _buildElasticTransition<T extends Object?>(
    Widget page, {
    required RouteSettings settings,
    Duration duration = const Duration(milliseconds: 800),
    Duration reverseDuration = const Duration(milliseconds: 400),
    bool maintainState = true,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: reverseDuration,
      maintainState: maintainState,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return ScaleTransition(
          scale: animation.drive(CurveTween(curve: Curves.elasticOut)),
          child: FadeTransition(opacity: animation, child: child),
        );
      },
    );
  }

  /// 无过渡动画
  static Route<T> _buildNoTransition<T extends Object?>(
    Widget page, {
    required RouteSettings settings,
    bool maintainState = true,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: Duration.zero,
      reverseTransitionDuration: Duration.zero,
      maintainState: maintainState,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return child;
      },
    );
  }

  /// iOS风格过渡
  static Route<T> _buildCupertinoTransition<T extends Object?>(
    Widget page, {
    required RouteSettings settings,
    bool maintainState = true,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 400),
      reverseTransitionDuration: const Duration(milliseconds: 400),
      maintainState: maintainState,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return SlideTransition(
          position: animation.drive(
            Tween(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).chain(CurveTween(curve: Curves.linearToEaseOut)),
          ),
          child: SlideTransition(
            position: secondaryAnimation.drive(
              Tween(
                begin: Offset.zero,
                end: const Offset(-0.3, 0.0),
              ).chain(CurveTween(curve: Curves.linearToEaseOut)),
            ),
            child: child,
          ),
        );
      },
    );
  }
}
