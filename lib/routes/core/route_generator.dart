import 'package:flutter/material.dart';
import '../constants/app_routes.dart';
import '../config/route_config.dart';

// 路由生成器
class RouteGenerator {
  RouteGenerator._();

  // 生成路由
  static Route<dynamic> generateRoute(RouteSettings settings) {
    final String routeName = settings.name ?? '';
    final args = settings.arguments as Map<String, dynamic>?;

    // 获取路由配置
    final routeConfig = RouteConfigManager.getConfig(routeName);

    if (routeConfig != null) {
      // 使用配置中的转场动画类型，可被参数覆盖
      final transitionType =
          args?['transitionType'] as RouteTransitionType? ??
          routeConfig.defaultTransition;

      return _buildRoute(
        routeConfig.builder(args),
        settings: settings,
        transitionType: transitionType,
      );
    }

    // 未找到路由配置，返回404页面
    return _buildRoute(
      RouteConfigManager.getConfig(
            AppRoutes.notFound,
          )?.builder({'originalRoute': routeName}) ??
          _buildFallback404Page(),
      settings: RouteSettings(
        name: AppRoutes.notFound,
        arguments: {'originalRoute': routeName},
      ),
      transitionType: RouteTransitionType.fade,
    );
  }

  // 构建路由
  static Route<dynamic> _buildRoute(
    Widget page, {
    required RouteSettings settings,
    RouteTransitionType transitionType = RouteTransitionType.slide,
  }) {
    switch (transitionType) {
      case RouteTransitionType.fade:
        return PageRouteBuilder(
          settings: settings,
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 300),
        );

      case RouteTransitionType.scale:
        return PageRouteBuilder(
          settings: settings,
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return ScaleTransition(scale: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 300),
        );

      case RouteTransitionType.slide:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => page,
        );

      case RouteTransitionType.slideUp:
        return PageRouteBuilder(
          settings: settings,
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.0, 1.0), // 从屏幕底部开始 (y=1.0 表示向下偏移一个屏幕高度)
                end: Offset.zero, // 结束在正常位置
              ).animate(
                CurvedAnimation(parent: animation, curve: Curves.easeOut),
              ),
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 350),
          reverseTransitionDuration: const Duration(milliseconds: 250),
        );
    }
  }

  // 构建备用404页面（当RouteConfig中的404页面也无法加载时使用）
  static Widget _buildFallback404Page() {
    return Scaffold(
      appBar: AppBar(title: const Text('页面未找到'), centerTitle: true),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            const Text(
              '404',
              style: TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '抱歉，您访问的页面不存在\n请检查URL或返回首页',
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                // 返回首页逻辑
              },
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    );
  }
}
