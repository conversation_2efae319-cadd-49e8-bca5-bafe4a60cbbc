import 'package:flutter/material.dart';
import '../constants/app_routes.dart';
import '../middleware/route_middleware.dart';
import '../config/route_config.dart';
import '../constants/pages_path.dart';

// 路由观察者，用于跟踪路由栈
class RouteObserver extends NavigatorObserver {
  static final RouteObserver _instance = RouteObserver._internal();
  factory RouteObserver() => _instance;
  RouteObserver._internal();

  final List<String> _routeStack = [];

  List<String> get routeStack => List.unmodifiable(_routeStack);

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    final routeName = route.settings.name;
    if (routeName != null) {
      _routeStack.add(routeName);
    }
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    final routeName = route.settings.name;
    if (routeName != null && _routeStack.isNotEmpty) {
      _routeStack.removeLast();
    }
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    final oldRouteName = oldRoute?.settings.name;
    final newRouteName = newRoute?.settings.name;

    if (oldRouteName != null && _routeStack.isNotEmpty) {
      final index = _routeStack.lastIndexOf(oldRouteName);
      if (index != -1) {
        _routeStack.removeAt(index);
      }
    }

    if (newRouteName != null) {
      _routeStack.add(newRouteName);
    }
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didRemove(route, previousRoute);
    final routeName = route.settings.name;
    if (routeName != null && _routeStack.isNotEmpty) {
      _routeStack.remove(routeName);
    }
  }

  // 获取上一个路由
  String? getPreviousRoute() {
    if (_routeStack.length >= 2) {
      return _routeStack[_routeStack.length - 2];
    }
    return null;
  }

  // 获取当前路由
  String? getCurrentRoute() {
    if (_routeStack.isNotEmpty) {
      return _routeStack.last;
    }
    return null;
  }

  // 检查上一个路由是否是指定路由
  bool isPreviousRoute(String routeName) {
    final previousRoute = getPreviousRoute();
    return previousRoute == routeName;
  }
}

// 导航服务
class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  BuildContext? get currentContext => navigatorKey.currentContext;
  NavigatorState? get currentState => navigatorKey.currentState;

  // 路由观察者实例
  static final RouteObserver routeObserver = RouteObserver();

  // 跳转到指定路由
  Future<T?> navigateTo<T extends Object?>(
    String routeName, {
    Object? arguments,
    bool checkPermission = true,
    String? transition, // 过渡效果：'slideUp', 'slideLeft', 'slideRight', null(默认)
  }) async {
    final context = currentContext;
    if (context == null) return null;

    // 路由观察者会自动跟踪路由变化

    // 权限检查
    if (checkPermission) {
      final accessResult = await RouteMiddleware.checkRouteAccess(
        routeName,
        context,
      );

      if (!accessResult.isAllowed) {
        if (accessResult.redirectRoute != null && context.mounted) {
          RouteMiddleware.handleRouteInterception(
            context,
            routeName,
            accessResult.redirectRoute!,
            accessResult.reason ?? '',
          );
        }
        return null;
      }
    }

    RouteMiddleware.logRouteAccess(routeName);

    // 如果指定了过渡效果，使用自定义路由
    if (transition != null) {
      return _navigateWithTransition<T>(routeName, arguments, transition);
    }

    return currentState?.pushNamed(routeName, arguments: arguments);
  }

  // 替换当前路由
  Future<T?> navigateToReplacement<T extends Object?, TO extends Object?>(
    String routeName, {
    Object? arguments,
    TO? result,
    String? transition,
  }) async {
    RouteMiddleware.logRouteAccess(routeName);

    // 如果指定了过渡效果，使用自定义路由
    if (transition != null) {
      final context = currentContext;
      if (context == null) return null;

      Widget? targetPage = _getPageForRoute(routeName, arguments);
      if (targetPage == null) {
        return currentState?.pushReplacementNamed(
          routeName,
          arguments: arguments,
          result: result,
        );
      }

      Route<T> route;
      switch (transition) {
        case 'slideUp':
          route = _createSlideUpRoute<T>(targetPage, routeName, arguments);
          break;
        case 'slideLeft':
          route = _createSlideLeftRoute<T>(targetPage, routeName, arguments);
          break;
        case 'slideRight':
          route = _createSlideRightRoute<T>(targetPage, routeName, arguments);
          break;
        default:
          return currentState?.pushReplacementNamed(
            routeName,
            arguments: arguments,
            result: result,
          );
      }

      return currentState?.pushReplacement(route, result: result);
    }

    return currentState?.pushReplacementNamed(
      routeName,
      arguments: arguments,
      result: result,
    );
  }

  // 清空路由栈并跳转
  Future<T?> navigateToAndClearStack<T extends Object?>(
    String routeName, {
    Object? arguments,
    String?
    transition, // 过渡效果：'slideUp', 'slideDown', 'slideLeft', 'slideRight', null(默认)
  }) async {
    RouteMiddleware.logRouteAccess(routeName);

    // 如果指定了过渡动画，使用自定义路由
    if (transition != null) {
      final context = currentContext;
      if (context == null) return null;

      // 获取目标页面
      Widget? targetPage = _getPageForRoute(routeName, arguments);
      if (targetPage == null) return null;

      // 根据过渡类型创建路由
      Route<T> route;
      switch (transition) {
        case 'slideDown':
          route = _createSlideDownRoute<T>(targetPage, routeName, arguments);
          break;
        case 'slideUp':
          route = _createSlideUpRoute<T>(targetPage, routeName, arguments);
          break;
        case 'slideLeft':
          route = _createSlideLeftRoute<T>(targetPage, routeName, arguments);
          break;
        case 'slideRight':
          route = _createSlideRightRoute<T>(targetPage, routeName, arguments);
          break;
        default:
          // 默认使用系统路由
          return currentState?.pushNamedAndRemoveUntil(
            routeName,
            (route) => false,
            arguments: arguments,
          );
      }

      // 清空路由栈并推入新路由
      return currentState?.pushAndRemoveUntil(route, (route) => false);
    }

    // 默认行为
    return currentState?.pushNamedAndRemoveUntil(
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }

  // 返回上一页
  void goBack<T extends Object?>([T? result]) {
    if (canGoBack()) {
      currentState?.pop(result);
    }
  }

  // 返回到指定路由
  void goBackTo(String routeName) {
    currentState?.popUntil(ModalRoute.withName(routeName));
  }

  // 检查是否可以返回
  bool canGoBack() {
    return currentState?.canPop() ?? false;
  }

  // 跳转到首页
  Future<void> goToHome({bool? isElevatedMode}) async {
    final arguments =
        isElevatedMode != null ? {'isElevatedMode': isElevatedMode} : null;
    await navigateToAndClearStack(
      AppRoutes.mainTabbarScreen,
      arguments: arguments,
    );
  }

  // 跳转到应用加载页
  Future<void> goToAppLoading() async {
    await navigateTo(AppRoutes.appLoading);
  }

  // 跳转到404页面
  Future<void> goTo404() async {
    await navigateToReplacement(AppRoutes.notFound);
  }

  // 便捷方法：从底部滑入
  Future<T?> navigateSlideUp<T extends Object?>(
    String routeName, {
    Object? arguments,
    bool checkPermission = true,
  }) async {
    return navigateTo<T>(
      routeName,
      arguments: arguments,
      checkPermission: checkPermission,
      transition: 'slideUp',
    );
  }

  // 便捷方法：从左侧滑入
  Future<T?> navigateSlideLeft<T extends Object?>(
    String routeName, {
    Object? arguments,
    bool checkPermission = true,
  }) async {
    return navigateTo<T>(
      routeName,
      arguments: arguments,
      checkPermission: checkPermission,
      transition: 'slideLeft',
    );
  }

  // 便捷方法：从右侧滑入
  Future<T?> navigateSlideRight<T extends Object?>(
    String routeName, {
    Object? arguments,
    bool checkPermission = true,
  }) async {
    return navigateTo<T>(
      routeName,
      arguments: arguments,
      checkPermission: checkPermission,
      transition: 'slideRight',
    );
  }

  // 显示确认对话框
  Future<bool?> showConfirmDialog({
    required String title,
    required String content,
    String confirmText = '确认',
    String cancelText = '取消',
  }) async {
    final context = currentContext;
    if (context == null) return null;

    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(content),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(cancelText),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(confirmText),
            ),
          ],
        );
      },
    );
  }

  // 显示信息对话框
  Future<void> showInfoDialog({
    required String title,
    required String content,
    String buttonText = '确定',
  }) async {
    final context = currentContext;
    if (context == null) return;

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(content),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(buttonText),
            ),
          ],
        );
      },
    );
  }

  // 显示底部弹窗
  Future<T?> showBottomSheet<T>({
    required Widget child,
    bool isScrollControlled = false,
    bool isDismissible = true,
  }) async {
    final context = currentContext;
    if (context == null) return null;

    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: isScrollControlled,
      isDismissible: isDismissible,
      builder: (BuildContext context) => child,
    );
  }

  // 显示SnackBar
  void showSnackBar({
    required String message,
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
  }) {
    final context = currentContext;
    if (context == null) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), duration: duration, action: action),
    );
  }

  // 获取当前路由名称
  String? getCurrentRouteName() {
    try {
      final context = currentContext;
      if (context == null) return null;

      final route = ModalRoute.of(context);
      if (route == null) return null;

      return route.settings.name;
    } catch (e) {
      return null;
    }
  }

  // 检查当前是否在指定路由
  bool isCurrentRoute(String routeName) {
    return getCurrentRouteName() == routeName;
  }

  // 获取上一个页面的路由名称
  String? getPreviousRouteName() {
    return routeObserver.getPreviousRoute();
  }

  // 检查上一个页面是否是指定路由
  bool isPreviousRoute(String routeName) {
    return routeObserver.isPreviousRoute(routeName);
  }

  // 使用自定义过渡效果进行导航
  Future<T?> _navigateWithTransition<T extends Object?>(
    String routeName,
    Object? arguments,
    String transition,
  ) async {
    final context = currentContext;
    if (context == null) return null;

    // 获取目标页面
    Widget? targetPage = _getPageForRoute(routeName, arguments);
    if (targetPage == null) return null;

    // 根据过渡类型创建路由
    Route<T> route;
    switch (transition) {
      case 'slideUp':
        route = _createSlideUpRoute<T>(targetPage, routeName, arguments);
        break;
      case 'slideLeft':
        route = _createSlideLeftRoute<T>(targetPage, routeName, arguments);
        break;
      case 'slideRight':
        route = _createSlideRightRoute<T>(targetPage, routeName, arguments);
        break;
      default:
        // 默认使用系统路由
        return currentState?.pushNamed(routeName, arguments: arguments);
    }

    return currentState?.push(route);
  }

  // 获取路由对应的页面
  Widget? _getPageForRoute(String routeName, Object? arguments) {
    // 从路由配置中获取页面构建器
    final routeConfig = RouteConfigManager.getConfig(routeName);
    if (routeConfig != null) {
      return routeConfig.builder(arguments as Map<String, dynamic>?);
    }

    // 如果路由配置中没有找到，使用简单的映射作为备用
    switch (routeName) {
      case AppRoutes.login:
        return const LoginPage();
      case AppRoutes.register:
        return const RegisterPage();
      case AppRoutes.verifyCode:
        return const VerifyCodePage();
      case AppRoutes.forgotPassword:
        return const ForgotPasswordPage();
      case AppRoutes.countryCode:
        return const CountryCodePage();
      case AppRoutes.searchCrypto:
        return const SearchCryptoPage();
      case AppRoutes.klinePage:
        return const KlinePage();
      case AppRoutes.priceAlert:
        return const PriceAlert();
      case AppRoutes.spotOrders:
        return const SpotOrdersPage();
      // 添加更多路由映射
      default:
        return null;
    }
  }

  // 创建从下方滑入的路由
  Route<T> _createSlideUpRoute<T extends Object?>(
    Widget page,
    String routeName,
    Object? arguments,
  ) {
    return PageRouteBuilder<T>(
      settings: RouteSettings(name: routeName, arguments: arguments),
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 350),
      reverseTransitionDuration: const Duration(milliseconds: 250),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0); // 从屏幕底部开始
        const end = Offset.zero;
        const curve = Curves.easeOut;

        var tween = Tween(
          begin: begin,
          end: end,
        ).chain(CurveTween(curve: curve));

        return SlideTransition(position: animation.drive(tween), child: child);
      },
    );
  }

  // 创建向下滑出的路由
  Route<T> _createSlideDownRoute<T extends Object?>(
    Widget page,
    String routeName,
    Object? arguments,
  ) {
    return PageRouteBuilder<T>(
      settings: RouteSettings(name: routeName, arguments: arguments),
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 350),
      reverseTransitionDuration: const Duration(milliseconds: 250),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // 新页面正常进入（从正常位置开始）
        const enterBegin = Offset.zero;
        const enterEnd = Offset.zero;
        const enterCurve = Curves.easeOut;

        var enterTween = Tween(
          begin: enterBegin,
          end: enterEnd,
        ).chain(CurveTween(curve: enterCurve));

        return SlideTransition(
          position: animation.drive(enterTween),
          child: child,
        );
      },
    );
  }

  // 创建从左侧滑入的路由
  Route<T> _createSlideLeftRoute<T extends Object?>(
    Widget page,
    String routeName,
    Object? arguments,
  ) {
    return PageRouteBuilder<T>(
      settings: RouteSettings(name: routeName, arguments: arguments),
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 300),
      reverseTransitionDuration: const Duration(milliseconds: 250),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(-1.0, 0.0); // 从屏幕左侧开始
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween = Tween(
          begin: begin,
          end: end,
        ).chain(CurveTween(curve: curve));

        return SlideTransition(position: animation.drive(tween), child: child);
      },
    );
  }

  // 创建从右侧滑入的路由
  Route<T> _createSlideRightRoute<T extends Object?>(
    Widget page,
    String routeName,
    Object? arguments,
  ) {
    return PageRouteBuilder<T>(
      settings: RouteSettings(name: routeName, arguments: arguments),
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 300),
      reverseTransitionDuration: const Duration(milliseconds: 250),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0); // 从屏幕右侧开始
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween = Tween(
          begin: begin,
          end: end,
        ).chain(CurveTween(curve: curve));

        return SlideTransition(position: animation.drive(tween), child: child);
      },
    );
  }

  // 清理导航服务
  void dispose() {
    // 清理资源
  }
}
