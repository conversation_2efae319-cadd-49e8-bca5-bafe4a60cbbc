// 应用路由常量定义
class AppRoutes {
  AppRoutes._();

  // 页面路由
  static const String splash = '/splash';
  static const String appLoading = '/app-loading';
  static const String mainTabbarScreen = '/mainTabbarScreen';
  static const String languageTest = '/language-test';
  static const String languageSelection = '/language-selection';
  static const String profilePage = '/profile';
  static const String setting = '/settings';
  static const String searchCrypto = '/search-crypto';
  static const String login = '/login';
  static const String register = '/register';
  static const String countryCode = '/country-code';
  static const String forgotPassword = '/forgot-password';
  static const String verifyCode = '/verify-code';
  static const String authFaceid = '/auth-faceid';
  static const String klinePage = '/kline-page';
  static const String priceAlert = '/price-alert';
  static const String spotOrders = '/spot-orders';
  static const String notFound = '/404';

  // 路由参数键名
  static const String paramId = 'id';
  static const String paramType = 'type';
  static const String paramTitle = 'title';
  static const String paramData = 'data';
  static const String paramSymbol = 'symbol';

  // 公开访问的路由组
  static const List<String> publicRoutes = [
    splash,
    appLoading,
    mainTabbarScreen,
    languageTest,
    languageSelection,
    notFound,
  ];
}
