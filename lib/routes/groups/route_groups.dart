// 路由分组管理
import '../constants/app_routes.dart';

/// 路由分组管理类
/// 将路由按功能模块进行分组，便于管理和权限控制
class RouteGroups {
  RouteGroups._();

  // ========== 认证相关路由 ==========
  /// 认证相关路由组
  static const List<String> authRoutes = [
    AppRoutes.login,
    AppRoutes.register,
    AppRoutes.forgotPassword,
    AppRoutes.verifyCode,
    AppRoutes.countryCode,
  ];

  // ========== 交易相关路由 ==========
  /// 交易相关路由组
  static const List<String> tradingRoutes = [
    AppRoutes.klinePage,
    AppRoutes.spotOrders,
    // 可以添加更多交易相关路由
    // AppRoutes.spotTrade,
    // AppRoutes.contractTrade,
  ];

  // ========== 市场相关路由 ==========
  /// 市场相关路由组
  static const List<String> marketRoutes = [
    AppRoutes.searchCrypto,
    // 可以添加更多市场相关路由
  ];

  // ========== 用户相关路由 ==========
  /// 用户相关路由组
  static const List<String> userRoutes = [
    AppRoutes.profilePage,
    AppRoutes.setting,
  ];

  // ========== 系统相关路由 ==========
  /// 系统相关路由组
  static const List<String> systemRoutes = [
    AppRoutes.splash,
    AppRoutes.appLoading,
    AppRoutes.mainTabbarScreen,
    AppRoutes.notFound,
  ];

  // ========== 需要认证的路由 ==========
  /// 需要用户登录才能访问的路由
  static const List<String> protectedRoutes = [
    AppRoutes.profilePage,
    AppRoutes.setting,
    ...tradingRoutes,
  ];

  // ========== 公开访问的路由 ==========
  /// 无需认证即可访问的路由（继承原有定义）
  static const List<String> publicRoutes = AppRoutes.publicRoutes;

  // ========== 路由分组查询方法 ==========

  /// 检查路由是否属于认证相关
  static bool isAuthRoute(String route) {
    return authRoutes.contains(route);
  }

  /// 检查路由是否属于交易相关
  static bool isTradingRoute(String route) {
    return tradingRoutes.contains(route);
  }

  /// 检查路由是否属于市场相关
  static bool isMarketRoute(String route) {
    return marketRoutes.contains(route);
  }

  /// 检查路由是否属于用户相关
  static bool isUserRoute(String route) {
    return userRoutes.contains(route);
  }

  /// 检查路由是否需要认证
  static bool requiresAuth(String route) {
    return protectedRoutes.contains(route);
  }

  /// 检查路由是否为公开访问
  static bool isPublicRoute(String route) {
    return publicRoutes.contains(route);
  }

  /// 获取路由所属的分组名称
  static String getRouteGroup(String route) {
    if (isAuthRoute(route)) return 'auth';
    if (isTradingRoute(route)) return 'trading';
    if (isMarketRoute(route)) return 'market';
    if (isUserRoute(route)) return 'user';
    if (systemRoutes.contains(route)) return 'system';
    return 'unknown';
  }

  /// 获取所有路由分组信息
  static Map<String, List<String>> getAllGroups() {
    return {
      'auth': authRoutes,
      'trading': tradingRoutes,
      'market': marketRoutes,
      'user': userRoutes,
      'system': systemRoutes,
    };
  }

  /// 获取分组统计信息
  static Map<String, int> getGroupStats() {
    final groups = getAllGroups();
    return groups.map((key, value) => MapEntry(key, value.length));
  }

  /// 获取指定分组的所有路由
  static List<String> getRoutesByGroup(String groupName) {
    final groups = getAllGroups();
    return groups[groupName] ?? [];
  }

  /// 检查路由是否需要特定权限
  static bool requiresPermission(String route, String permission) {
    // 这里可以根据实际需求扩展权限检查逻辑
    // 例如：管理员权限、VIP权限等
    switch (permission) {
      case 'admin':
        return isUserRoute(route);
      case 'trading':
        return isTradingRoute(route);
      default:
        return false;
    }
  }

  /// 获取路由的默认转场动画类型
  static String getDefaultTransition(String route) {
    if (isAuthRoute(route)) {
      return 'slideUp';
    } else if (isTradingRoute(route)) {
      return 'slide';
    } else if (systemRoutes.contains(route)) {
      return 'fade';
    }
    return 'slide';
  }

  /// 检查路由是否应该在底部导航中显示
  static bool shouldShowInBottomNav(String route) {
    return [
      AppRoutes.mainTabbarScreen,
      // 可以添加其他应该在底部导航显示的路由
    ].contains(route);
  }

  /// 获取路由的显示名称（用于面包屑导航等）
  static String getRouteDisplayName(String route) {
    const routeNames = {
      AppRoutes.login: '登录',
      AppRoutes.register: '注册',
      AppRoutes.forgotPassword: '忘记密码',
      AppRoutes.verifyCode: '验证码',
      AppRoutes.countryCode: '选择国家',
      AppRoutes.profilePage: '个人中心',
      AppRoutes.setting: '设置',
      AppRoutes.searchCrypto: '搜索',
      AppRoutes.klinePage: 'K线图',
      AppRoutes.mainTabbarScreen: '首页',
      AppRoutes.splash: '启动页',
      AppRoutes.notFound: '页面未找到',
    };

    return routeNames[route] ?? route;
  }

  /// 获取路由层级（用于面包屑导航）
  static List<String> getRouteBreadcrumb(String route) {
    if (isAuthRoute(route)) {
      return ['首页', '认证', getRouteDisplayName(route)];
    } else if (isTradingRoute(route)) {
      return ['首页', '交易', getRouteDisplayName(route)];
    } else if (isUserRoute(route)) {
      return ['首页', '用户', getRouteDisplayName(route)];
    } else if (isMarketRoute(route)) {
      return ['首页', '市场', getRouteDisplayName(route)];
    }

    return ['首页', getRouteDisplayName(route)];
  }
}
