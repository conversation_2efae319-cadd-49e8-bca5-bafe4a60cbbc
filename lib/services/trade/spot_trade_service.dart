
import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../clients/dio_request.dart';
import '../storage/storage_service.dart';
import '../config/api_route.dart';
import '../assets/assets_service.dart';
import '../assets/models/asset_balance_model.dart';
import '../market/market_service.dart';
import '../market/models/currency_model.dart';
import 'models/spot_trade_config.dart';
import 'models/spot_post_form.dart';
import 'models/spot_condition_order.dart';

/// 现货交易处理逻辑类
class SpotTradeService {
  static final SpotTradeService _instance = SpotTradeService._internal();
  factory SpotTradeService() => _instance;
  SpotTradeService._internal();

  static SpotTradeService get instance => _instance;

  static const String _cacheKey = 'spot_trade_config';

  List<SpotTradeConfig> _tradeConfigs = [];
  bool _isInitialized = false;

  /// 当前交易表单数据
  SpotPostForm? _currentForm;

  /// 当前委托订单表单数据
  SpotConditionOrder? _currentConditionOrder;

  // 防抖相关
  Timer? _debounceTimer;
  bool _isPlacingOrder = false;

  // 表单清空回调
  VoidCallback? _onFormCleared;

  List<SpotTradeConfig> get tradeConfigs => List.unmodifiable(_tradeConfigs);
  bool get isInitialized => _isInitialized;
  SpotPostForm? get currentForm => _currentForm;
  SpotConditionOrder? get currentConditionOrder => _currentConditionOrder;

  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadCachedConfigs();
      _isInitialized = true;
      debugPrint('✅ SpotTradeService 初始化完成');
    } catch (e) {
      debugPrint('❌ SpotTradeService 初始化失败: $e');
      _isInitialized = true;
    }
  }

  /// 获取交易配置
  Future<List<SpotTradeConfig>?> fetchTradeConfig(int currencyId) async {
    try {
      final response = await DioRequest.instance.get(
        ApiRoute.spotTradeConfig,
        queryParams: {'currency_id': currencyId},
        requireAuth: true,
      );

      if (response.success && response.data != null) {
        final List<dynamic> dataList = response.data as List<dynamic>;
        final configs = dataList.map((json) => SpotTradeConfig.fromJson(json)).toList();

        await _cacheConfigs(currencyId, configs);
        _updateLocalConfigs(configs);

        return configs;
      }
      return null;
    } catch (e) {
      debugPrint('❌ 获取交易配置失败: $e');
      return null;
    }
  }

  /// 从缓存加载配置
  Future<void> _loadCachedConfigs() async {
    try {
      final cachedData = StorageService.instance.getString(_cacheKey);
      if (cachedData != null) {
        final Map<String, dynamic> cacheMap = json.decode(cachedData);
        final List<SpotTradeConfig> allConfigs = [];

        cacheMap.forEach((currencyId, configsJson) {
          final List<dynamic> configsList = configsJson as List<dynamic>;
          final configs = configsList.map((json) => SpotTradeConfig.fromJson(json)).toList();
          allConfigs.addAll(configs);
        });

        _tradeConfigs = allConfigs;
        debugPrint('📦 已加载缓存的交易配置，共 ${_tradeConfigs.length} 条');
      }
    } catch (e) {
      debugPrint('❌ 加载缓存配置失败: $e');
    }
  }

  /// 缓存配置到本地
  Future<void> _cacheConfigs(int currencyId, List<SpotTradeConfig> configs) async {
    try {
      final cachedData = StorageService.instance.getString(_cacheKey);
      Map<String, dynamic> cacheMap = {};

      if (cachedData != null) {
        cacheMap = json.decode(cachedData);
      }

      cacheMap[currencyId.toString()] = configs.map((config) => config.toJson()).toList();

      await StorageService.instance.setString(_cacheKey, json.encode(cacheMap));
      debugPrint('💾 已缓存币种 $currencyId 的交易配置');
    } catch (e) {
      debugPrint('❌ 缓存配置失败: $e');
    }
  }

  /// 更新本地配置列表
  void _updateLocalConfigs(List<SpotTradeConfig> newConfigs) {
    for (final newConfig in newConfigs) {
      final existingIndex = _tradeConfigs.indexWhere(
        (config) => config.currencyId == newConfig.currencyId && config.marketType == newConfig.marketType,
      );

      if (existingIndex != -1) {
        _tradeConfigs[existingIndex] = newConfig;
      } else {
        _tradeConfigs.add(newConfig);
      }
    }
  }

  /// 根据币种ID获取配置
  SpotTradeConfig? getConfigByCurrencyId(int currencyId, {int marketType = 1}) {
    try {
      return _tradeConfigs.firstWhere(
        (config) => config.currencyId == currencyId && config.marketType == marketType,
      );
    } catch (e) {
      return null;
    }
  }

  /// 获取币种的可用资产余额
  CurrencyBalance? getCurrencyBalance(int currencyId, int marketType,{bool quote = false}) {
    try {
      final assetsService = AssetsService.instance;
      final marketService = MarketService.instance;

      // 根据币种ID获取币种信息
      final currency = marketService.currencyModels.firstWhere(
        (c) => c.id == currencyId,
        orElse: () => throw StateError('Currency not found'),
      );

      // 根据市场类型确定账户类型
      AccountType accountType;
      switch (marketType) {
        case 1: // 现货
          accountType = AccountType.spot;
          break;
        case 2: // 合约
          accountType = AccountType.futures;
          break;
        case 3: // 全仓杠杆
          accountType = AccountType.crossMargin;
          break;
        case 7: // 逐仓杠杆
          accountType = AccountType.isolatedMargin;
          break;
        default:
          accountType = AccountType.spot;
      }

      // 获取对应账户的币种余额
      return assetsService.getCurrencyBalanceById(accountType, quote == false ? currency.id : currency.quoteAssetsId);
    } catch (e) {
      debugPrint('❌ 获取币种余额失败: $e');
      return null;
    }
  }

  /// 从MarketService获取币种数据
  CurrencyModel? getCurrencyData(int currencyId) {
    try {
      final marketService = MarketService.instance;
      return marketService.currencyModels.firstWhere(
        (currency) => currency.id == currencyId,
      );
    } catch (e) {
      debugPrint('❌ 获取币种数据失败: $e');
      return null;
    }
  }

  /// 获取币种的ticker数据
  TickerModel? getCurrencyTicker(int currencyId, {int marketType = 1}) {
    try {
      final currency = getCurrencyData(currencyId);
      if (currency == null) return null;

      return currency.tickers[marketType.toString()];
    } catch (e) {
      debugPrint('❌ 获取币种ticker数据失败: $e');
      return null;
    }
  }

  /// 初始化交易表单
  void initializeForm(int currencyId) {
    _currentForm = SpotPostForm(
      currencyId: currencyId,
      quantity: 0.0,
      side: 'buy',
      type: 'limit',
    );
    debugPrint('📝 初始化交易表单: ${_currentForm.toString()}');
  }

  /// 更新交易方向 (buy/sell)
  void updateSide(String side) {
    if (_currentForm == null) return;

    _currentForm = SpotPostForm(
      currencyId: _currentForm!.currencyId,
      price: _currentForm!.price,
      quantity: _currentForm!.quantity,
      side: side,
      stopLossPrice: _currentForm!.stopLossPrice,
      takeProfitPrice: _currentForm!.takeProfitPrice,
      timeInForce: _currentForm!.timeInForce,
      type: _currentForm!.type,
    );
    debugPrint('🔄 更新交易方向: $side');
  }

  /// 更新订单类型 (limit/market)
  void updateOrderType(String type) {
    if (_currentForm == null) return;

    // 只处理限价单和市价单
    if (type != 'limit' && type != 'market') return;

    _currentForm = SpotPostForm(
      currencyId: _currentForm!.currencyId,
      price: type == 'market' ? null : _currentForm!.price, // 市价单不需要价格
      quantity: _currentForm!.quantity,
      side: _currentForm!.side,
      stopLossPrice: _currentForm!.stopLossPrice,
      takeProfitPrice: _currentForm!.takeProfitPrice,
      timeInForce: _currentForm!.timeInForce,
      type: type,
    );
    debugPrint('🔄 更新订单类型: $type');
  }

  /// 更新价格
  void updatePrice(double? price) {
    if (_currentForm == null) return;

    _currentForm = SpotPostForm(
      currencyId: _currentForm!.currencyId,
      price: price,
      quantity: _currentForm!.quantity,
      side: _currentForm!.side,
      stopLossPrice: _currentForm!.stopLossPrice,
      takeProfitPrice: _currentForm!.takeProfitPrice,
      timeInForce: _currentForm!.timeInForce,
      type: _currentForm!.type,
    );
    debugPrint('🔄 更新价格: $price');
  }

  /// 更新数量
  void updateQuantity(double quantity) {
    if (_currentForm == null) return;

    _currentForm = SpotPostForm(
      currencyId: _currentForm!.currencyId,
      price: _currentForm!.price,
      quantity: quantity,
      side: _currentForm!.side,
      stopLossPrice: _currentForm!.stopLossPrice,
      takeProfitPrice: _currentForm!.takeProfitPrice,
      timeInForce: _currentForm!.timeInForce,
      type: _currentForm!.type,
    );
  }

  /// 根据交易额计算并更新数量
  void updateQuantityByAmount(double amount) {
    if (_currentForm == null || _currentForm!.price == null || _currentForm!.price == 0) {
      debugPrint('❌ 无法计算数量：价格为空或为0');
      return;
    }

    final quantity = amount / _currentForm!.price!;
    updateQuantity(quantity);
    // debugPrint('💰 根据交易额 $amount 计算数量: $quantity');
  }

  /// 更新时间有效性 (gtc/ioc/fok)
  void updateTimeInForce(String? timeInForce) {
    if (_currentForm == null) return;

    _currentForm = SpotPostForm(
      currencyId: _currentForm!.currencyId,
      price: _currentForm!.price,
      quantity: _currentForm!.quantity,
      side: _currentForm!.side,
      stopLossPrice: _currentForm!.stopLossPrice,
      takeProfitPrice: _currentForm!.takeProfitPrice,
      timeInForce: timeInForce,
      type: _currentForm!.type,
    );
    debugPrint('🔄 更新时间有效性: $timeInForce');
  }

  /// 获取当前表单的交易额
  double? getCurrentAmount() {
    if (_currentForm == null || _currentForm!.price == null) return null;
    return _currentForm!.quantity * _currentForm!.price!;
  }

  /// 重置表单
  void resetForm() {
    if (_currentForm != null) {
      initializeForm(_currentForm!.currencyId);
    }
  }

  /// 设置表单清空回调
  void setFormClearedCallback(VoidCallback? callback) {
    _onFormCleared = callback;
  }

  /// 初始化委托订单表单
  void initializeConditionOrder(int currencyId) {
    _currentConditionOrder = SpotConditionOrder(
      currencyId: currencyId.toString(),
      side: '1', // 默认买入
      orderType: '1', // 默认止盈止损
      triggerType: '1', // 默认限价
    );
    debugPrint('🔄 初始化委托订单表单: currencyId=$currencyId');
  }

  /// 更新委托订单字段
  void updateConditionOrder({
    String? amount,
    String? orderType,
    String? placePrice,
    String? side,
    String? triggerCondition,
    String? triggerPrice,
    String? triggerType,
  }) {
    if (_currentConditionOrder == null) return;

    _currentConditionOrder = _currentConditionOrder!.copyWith(
      amount: amount,
      orderType: orderType,
      placePrice: placePrice,
      side: side,
      triggerCondition: triggerCondition,
      triggerPrice: triggerPrice,
      triggerType: triggerType,
    );

    debugPrint('🔄 更新委托订单: $_currentConditionOrder');
  }

  /// 下单方法
  Future<bool> placeOrder({
    required SpotTradeConfig? tradeConfig,
    required CurrencyBalance? baseBalance,
    required CurrencyBalance? quoteBalance,
    required double? currentMarketPrice,
  }) async {
    // 防抖处理
    if (_isPlacingOrder) {
      debugPrint('⏳ 正在下单中，请勿重复提交');
      return false;
    }

    _isPlacingOrder = true;

    try {
      if (_currentForm == null) {
        debugPrint('❌ 下单失败：表单数据为空');
        return false;
      }

      // 验证表单基础数据
      if (!_validateOrderForm(_currentForm!)) {
        return false;
      }

      // 通过交易对配置验证
      if (!_validateWithTradeConfig(_currentForm!, tradeConfig)) {
        return false;
      }

      // 验证余额
      if (!_validateBalance(_currentForm!, baseBalance, quoteBalance, currentMarketPrice)) {
        return false;
      }

      // 验证通过，打印下单数据
      _printOrderData(_currentForm!, currentMarketPrice);

      // 调用下单接口
      final success = await _submitOrder(_currentForm!);

      if (success) {
        // 清空表单数据
        _clearFormData();
      }

      return success;
    } catch (e) {
      debugPrint('❌ 下单异常: $e');
      return false;
    } finally {
      // 延迟重置防抖标志，防止用户快速点击
      _debounceTimer?.cancel();
      _debounceTimer = Timer(const Duration(milliseconds: 1000), () {
        _isPlacingOrder = false;
      });
    }
  }

  /// 验证表单基础数据
  bool _validateOrderForm(SpotPostForm form) {
    // 验证币种ID
    if (form.currencyId <= 0) {
      debugPrint('❌ 验证失败：币种ID无效 (${form.currencyId})');
      return false;
    }

    // 验证数量
    if (form.quantity <= 0) {
      debugPrint('❌ 验证失败：数量必须大于0 (${form.quantity})');
      return false;
    }

    // 验证买卖方向
    if (form.side != 'buy' && form.side != 'sell') {
      debugPrint('❌ 验证失败：买卖方向无效 (${form.side})');
      return false;
    }

    // 验证订单类型
    if (form.type != 'limit' && form.type != 'market') {
      debugPrint('❌ 验证失败：订单类型无效 (${form.type})');
      return false;
    }

    // 限价单必须有价格
    if (form.type == 'limit' && (form.price == null || form.price! <= 0)) {
      debugPrint('❌ 验证失败：限价单必须设置价格 (${form.price})');
      return false;
    }

    debugPrint('✅ 表单基础数据验证通过');
    return true;
  }

  /// 通过交易对配置验证
  bool _validateWithTradeConfig(SpotPostForm form, SpotTradeConfig? tradeConfig) {
    if (tradeConfig == null) {
      debugPrint('❌ 验证失败：交易配置未加载');
      return false;
    }

    // 验证最小交易数量
    if (form.quantity < tradeConfig.minTradeNum) {
      debugPrint('❌ 验证失败：数量小于最小交易数量 (${form.quantity} < ${tradeConfig.minTradeNum})');
      return false;
    }

    // 验证最大交易数量
    if (form.quantity > tradeConfig.maxTradeNum) {
      debugPrint('❌ 验证失败：数量超过最大交易数量 (${form.quantity} > ${tradeConfig.maxTradeNum})');
      return false;
    }

    // 限价单验证价格范围
    if (form.type == 'limit' && form.price != null) {
      if (form.price! < tradeConfig.minTradePrice) {
        debugPrint('❌ 验证失败：价格低于最小交易价格 (${form.price} < ${tradeConfig.minTradePrice})');
        return false;
      }

      if (form.price! > tradeConfig.maxTradePrice) {
        debugPrint('❌ 验证失败：价格超过最大交易价格 (${form.price} > ${tradeConfig.maxTradePrice})');
        return false;
      }
    }

    debugPrint('✅ 交易配置验证通过');
    return true;
  }

  /// 验证余额是否充足
  bool _validateBalance(SpotPostForm form, CurrencyBalance? baseBalance, CurrencyBalance? quoteBalance, double? currentMarketPrice) {
    final balance = form.side == 'buy' ? quoteBalance : baseBalance;

    if (balance == null) {
      debugPrint('❌ 验证失败：余额数据未加载');
      return false;
    }

    double requiredAmount;

    if (form.side == 'buy') {
      // 买入需要计价货币余额
      if (form.type == 'market') {
        // 市价单：需要交易额
        final currentPrice = currentMarketPrice ?? 0.0;
        requiredAmount = form.quantity * currentPrice;
      } else {
        // 限价单：需要交易额
        requiredAmount = form.quantity * (form.price ?? 0.0);
      }
    } else {
      // 卖出需要基础货币余额
      requiredAmount = form.quantity;
    }

    if (balance.availableAmount < requiredAmount) {
      debugPrint('❌ 验证失败：余额不足 (可用: ${balance.availableAmount}, 需要: $requiredAmount)');
      return false;
    }

    debugPrint('✅ 余额验证通过 (可用: ${balance.availableAmount}, 需要: $requiredAmount)');
    return true;
  }

  /// 打印下单数据
  void _printOrderData(SpotPostForm form, double? currentMarketPrice) {
    debugPrint('');
    debugPrint('🚀 ========== 下单数据 ==========');
    debugPrint('📋 币种ID: ${form.currencyId}');
    debugPrint('💰 价格: ${form.price ?? '市价'}');
    debugPrint('📊 数量: ${form.quantity}');
    debugPrint('🔄 买卖方向: ${form.side == 'buy' ? '买入' : '卖出'}');
    debugPrint('📝 订单类型: ${form.type == 'limit' ? '限价单' : '市价单'}');
    if (form.timeInForce != null) {
      debugPrint('⏰ 时间有效性: ${form.timeInForce}');
    }
    if (form.stopLossPrice != null) {
      debugPrint('🛑 止损价格: ${form.stopLossPrice}');
    }
    if (form.takeProfitPrice != null) {
      debugPrint('🎯 止盈价格: ${form.takeProfitPrice}');
    }

    // 计算交易额
    final amount = form.type == 'market'
        ? form.quantity * (currentMarketPrice ?? 0.0)
        : form.quantity * (form.price ?? 0.0);
    debugPrint('💵 预计交易额: ${amount.toStringAsFixed(6)}');

    // 显示配置信息
    final config = getConfigByCurrencyId(form.currencyId);
    if (config != null) {
      debugPrint('⚙️ 配置限制: 数量(${config.minTradeNum}-${config.maxTradeNum}), 价格(${config.minTradePrice}-${config.maxTradePrice})');
    }

    debugPrint('================================');
    debugPrint('');
  }

  /// 提交下单请求
  Future<bool> _submitOrder(SpotPostForm form) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> requestData = {
        'currency_id': form.currencyId,
        'side': form.side,
        'type': form.type,
        'quantity': form.quantity,
        'time_in_force': form.timeInForce ?? 'gtc',
      };

      // 限价单必须提交price，市价单不能提交price
      if (form.type == 'limit' && form.price != null) {
        requestData['price'] = form.price;
      }

      // 止盈止损价格，有值才提交
      if (form.takeProfitPrice != null) {
        requestData['take_profit_price'] = form.takeProfitPrice;
      }
      if (form.stopLossPrice != null) {
        requestData['stop_loss_price'] = form.stopLossPrice;
      }

      debugPrint('📤 下单请求参数: $requestData');

      // 调用下单接口
      final response = await DioRequest.instance.post(
        ApiRoute.spotPlaceOrder,
        body: requestData,
        requireAuth: true,
      );

      if (response.success) {
        debugPrint('✅ 下单接口调用成功: ${response.data}');

        // 下单成功后更新相关币种余额

        return true;
      } else {
        debugPrint('❌ 下单接口调用失败: ${response.message}');
        // 接口错误消息将在UI层处理
        return false;
      }
    } catch (e) {
      debugPrint('❌ 下单接口异常: $e');
      return false;
    }
  }

  /// 清空表单数据
  void _clearFormData() {
    if (_currentForm != null) {
      // 保留币种ID，重新初始化表单
      final currencyId = _currentForm!.currencyId;
      initializeForm(currencyId);

      // 通知视图清空表单数据
      _onFormCleared?.call();

      debugPrint('🧹 表单数据已清空');
    }
  }

  /// 委托单下单方法
  Future<bool> placeConditionOrder() async {
    if (_isPlacingOrder) {
      debugPrint('⏳ 正在下单中，请勿重复提交');
      return false;
    }

    _isPlacingOrder = true;

    try {
      if (_currentConditionOrder == null) {
        debugPrint('❌ 委托单下单失败：表单数据为空');
        return false;
      }

      // 调用委托单下单接口
      final response = await DioRequest.instance.post(
        ApiRoute.spotCommissionPlaceOrder,
        body: _currentConditionOrder!.toJson(),
        requireAuth: true,
      );

      if (response.success) {
        debugPrint('✅ 委托单下单成功: ${response.data}');
        return true;
      } else {
        debugPrint('❌ 委托单下单失败: ${response.message}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ 委托单下单异常: $e');
      return false;
    } finally {
      _debounceTimer?.cancel();
      _debounceTimer = Timer(const Duration(milliseconds: 1000), () {
        _isPlacingOrder = false;
      });
    }
  }
}