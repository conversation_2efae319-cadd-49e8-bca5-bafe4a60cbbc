/// 现货交易配置模型
class SpotTradeConfig {
  final int id;
  final int currencyId;
  final int marketType;
  final double minTradeNum;
  final double maxTradeNum;
  final double minTradePrice;
  final double maxTradePrice;
  final int limitPriceRate;
  final double makerLimit;
  final double limitLimit;
  final int orderLimit;
  final int triggerProtect;
  final double liquidationFee;
  final double? tickSize;
  final String createdAt;
  final String updatedAt;

  const SpotTradeConfig({
    required this.id,
    required this.currencyId,
    required this.marketType,
    required this.minTradeNum,
    required this.maxTradeNum,
    required this.minTradePrice,
    required this.maxTradePrice,
    required this.limitPriceRate,
    required this.makerLimit,
    required this.limitLimit,
    required this.orderLimit,
    required this.triggerProtect,
    required this.liquidationFee,
    this.tickSize,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SpotTradeConfig.fromJson(Map<String, dynamic> json) {
    return SpotTradeConfig(
      id: json['id'] ?? 0,
      currencyId: json['currency_id'] ?? 0,
      marketType: json['market_type'] ?? 1,
      minTradeNum: (json['min_trade_num'] as num?)?.toDouble() ?? 0.0,
      maxTradeNum: (json['max_trade_num'] as num?)?.toDouble() ?? 0.0,
      minTradePrice: (json['min_trade_price'] as num?)?.toDouble() ?? 0.0,
      maxTradePrice: (json['max_trade_price'] as num?)?.toDouble() ?? 0.0,
      limitPriceRate: json['limit_price_rate'] ?? 0,
      makerLimit: (json['maker_limit'] as num?)?.toDouble() ?? 0.0,
      limitLimit: (json['limit_limit'] as num?)?.toDouble() ?? 0.0,
      orderLimit: json['order_limit'] ?? 0,
      triggerProtect: json['trigger_protect'] ?? 0,
      liquidationFee: (json['liquidation_fee'] as num?)?.toDouble() ?? 0.0,
      tickSize: (json['tick_size'] as num?)?.toDouble(),
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'currency_id': currencyId,
      'market_type': marketType,
      'min_trade_num': minTradeNum,
      'max_trade_num': maxTradeNum,
      'min_trade_price': minTradePrice,
      'max_trade_price': maxTradePrice,
      'limit_price_rate': limitPriceRate,
      'maker_limit': makerLimit,
      'limit_limit': limitLimit,
      'order_limit': orderLimit,
      'trigger_protect': triggerProtect,
      'liquidation_fee': liquidationFee,
      'tick_size': tickSize,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  String toString() {
    return 'SpotTradeConfig(id: $id, currencyId: $currencyId, marketType: $marketType)';
  }
}
