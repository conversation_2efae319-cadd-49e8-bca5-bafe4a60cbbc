class SpotPostForm {
    
    ///币种id
    int currencyId;
    
    ///价格
    double? price;
    
    ///买卖数量
    double quantity;
    
    ///buy|sell
    String side;
    
    ///可选：止损委托价格
    String? stopLossPrice;
    
    ///可选：止盈委托价格
    String? takeProfitPrice;
    
    ///gtc|ioc|fok
    String? timeInForce;
    
    ///market,limit
    String type;

    SpotPostForm({
        required this.currencyId,
        this.price,
        required this.quantity,
        required this.side,
        this.stopLossPrice,
        this.takeProfitPrice,
        this.timeInForce,
        required this.type,
    });

    @override
    String toString() {
        return 'SpotPostForm(currencyId: $currencyId, price: $price, quantity: $quantity, side: $side, type: $type, timeInForce: $timeInForce)';
    }
}