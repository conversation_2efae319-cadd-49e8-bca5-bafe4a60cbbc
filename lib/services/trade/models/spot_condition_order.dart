/*
 * 现货委托订单模型
 */

class SpotConditionOrder {
  /// 委托数量
  final String? amount;
  
  /// 币种id
  final String? currencyId;
  
  /// 1 止盈止损 2 计划委托 3 追踪委托
  final String? orderType;
  
  /// 执行价格 【* 限价】
  final String? placePrice;
  
  /// 1买 -1 卖
  final String? side;
  
  /// 1大于等于 2 小于等于
  final String? triggerCondition;
  
  /// 触发价格
  final String? triggerPrice;
  
  /// 1限价 2市价
  final String? triggerType;

  const SpotConditionOrder({
    this.amount,
    this.currencyId,
    this.orderType,
    this.placePrice,
    this.side,
    this.triggerCondition,
    this.triggerPrice,
    this.triggerType,
  });

  /// 复制并修改属性
  SpotConditionOrder copyWith({
    String? amount,
    String? currencyId,
    String? orderType,
    String? placePrice,
    String? side,
    String? triggerCondition,
    String? triggerPrice,
    String? triggerType,
  }) {
    return SpotConditionOrder(
      amount: amount ?? this.amount,
      currencyId: currencyId ?? this.currencyId,
      orderType: orderType ?? this.orderType,
      placePrice: placePrice ?? this.placePrice,
      side: side ?? this.side,
      triggerCondition: triggerCondition ?? this.triggerCondition,
      triggerPrice: triggerPrice ?? this.triggerPrice,
      triggerType: triggerType ?? this.triggerType,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    
    if (currencyId != null) data['currency_id'] = currencyId;
    if (side != null) data['side'] = side;
    if (orderType != null) data['order_type'] = orderType;
    if (triggerCondition != null) data['trigger_condition'] = triggerCondition;
    if (triggerPrice != null) data['trigger_price'] = triggerPrice;
    if (amount != null) data['amount'] = amount;
    if (triggerType != null) data['trigger_type'] = triggerType;
    if (placePrice != null) data['place_price'] = placePrice;
    
    return data;
  }

  @override
  String toString() {
    return 'SpotConditionOrder(currencyId: $currencyId, side: $side, orderType: $orderType, triggerPrice: $triggerPrice, amount: $amount)';
  }
}
