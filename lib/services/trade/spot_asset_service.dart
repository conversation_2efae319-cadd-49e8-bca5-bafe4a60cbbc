/*
*  现货资产服务
*
*  功能：
*  - 管理现货资产数据
*  - 提供资产查询和更新方法
*  - 处理价格类型切换
*  - 缓存和状态管理
*/

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:qubic_exchange/models/trade/spot_asset_model.dart';

/// 现货资产服务
class SpotAssetService extends ChangeNotifier {
  static final SpotAssetService _instance = SpotAssetService._internal();
  factory SpotAssetService() => _instance;
  SpotAssetService._internal();

  /// 当前资产列表数据
  SpotAssetListModel? _assetListData;

  /// 当前选中的价格类型
  String _selectedPriceType = 'cost_asset';

  /// 是否只显示有余额的资产
  bool _showOnlyWithBalance = false;

  /// 排序类型
  String _sortType = 'symbol'; // symbol, value, pnl

  /// 排序方向（true: 升序, false: 降序）
  bool _sortAscending = true;

  /// 是否正在加载
  bool _isLoading = false;

  /// 错误信息
  String? _errorMessage;

  /// 获取当前资产列表数据
  SpotAssetListModel? get assetListData => _assetListData;

  /// 获取当前选中的价格类型
  String get selectedPriceType => _selectedPriceType;

  /// 获取是否只显示有余额的资产
  bool get showOnlyWithBalance => _showOnlyWithBalance;

  /// 获取排序类型
  String get sortType => _sortType;

  /// 获取排序方向
  bool get sortAscending => _sortAscending;

  /// 获取是否正在加载
  bool get isLoading => _isLoading;

  /// 获取错误信息
  String? get errorMessage => _errorMessage;

  /// 获取过滤后的资产列表
  List<SpotAssetModel> get filteredAssets {
    if (_assetListData == null) return [];

    List<SpotAssetModel> assets = _assetListData!.assets;

    // 过滤：只显示有余额的资产
    if (_showOnlyWithBalance) {
      assets = assets.where((asset) => asset.hasBalance).toList();
    }

    // 排序
    assets.sort((a, b) {
      int comparison;
      switch (_sortType) {
        case 'value':
          final aValue = a.totalValue ?? 0;
          final bValue = b.totalValue ?? 0;
          comparison = aValue.compareTo(bValue);
          break;
        case 'pnl':
          final aPnL = a.totalPnL ?? 0;
          final bPnL = b.totalPnL ?? 0;
          comparison = aPnL.compareTo(bPnL);
          break;
        case 'symbol':
        default:
          comparison = a.symbol.compareTo(b.symbol);
          break;
      }
      // 根据排序方向返回结果
      return _sortAscending ? comparison : -comparison;
    });

    return assets;
  }

  /// 加载资产数据
  Future<void> loadAssets() async {
    _setLoading(true);
    _setError(null);

    try {
      // TODO: 实际的API调用
      await Future.delayed(Duration(milliseconds: 500)); // 模拟网络延迟

      // 模拟数据
      final mockData = _generateMockData();
      _assetListData = mockData;

      notifyListeners();
    } catch (e) {
      _setError('加载资产数据失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 刷新资产数据
  Future<void> refreshAssets() async {
    await loadAssets();
  }

  /// 切换价格类型
  void switchPriceType(String priceType) {
    if (_selectedPriceType != priceType) {
      _selectedPriceType = priceType;
      notifyListeners();
    }
  }

  /// 切换只显示有余额的资产
  void toggleShowOnlyWithBalance() {
    _showOnlyWithBalance = !_showOnlyWithBalance;
    notifyListeners();
  }

  /// 设置排序类型
  void setSortType(String sortType) {
    if (_sortType != sortType) {
      _sortType = sortType;
      _sortAscending = true; // 新排序类型默认升序
      notifyListeners();
    }
  }

  /// 切换排序方向
  void toggleSortDirection() {
    _sortAscending = !_sortAscending;
    notifyListeners();
  }

  /// 按总额估值排序（切换升降序）
  void sortByValue() {
    if (_sortType == 'value') {
      // 如果已经是按总额排序，则切换方向
      toggleSortDirection();
    } else {
      // 如果不是按总额排序，则设置为按总额排序（默认降序，因为通常想看最高的）
      _sortType = 'value';
      _sortAscending = false; // 总额排序默认降序
      notifyListeners();
    }
  }

  /// 获取指定资产
  SpotAssetModel? getAsset(String symbol) {
    if (_assetListData == null) return null;
    try {
      return _assetListData!.assets.firstWhere(
        (asset) => asset.symbol.toLowerCase() == symbol.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  /// 更新单个资产数据
  void updateAsset(SpotAssetModel updatedAsset) {
    if (_assetListData == null) return;

    final index = _assetListData!.assets.indexWhere(
      (asset) => asset.assetId == updatedAsset.assetId,
    );

    if (index != -1) {
      final updatedAssets = List<SpotAssetModel>.from(_assetListData!.assets);
      updatedAssets[index] = updatedAsset;

      _assetListData = _assetListData!.copyWith(
        assets: updatedAssets,
        lastUpdated: DateTime.now(),
      );

      notifyListeners();
    }
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// 生成模拟数据
  SpotAssetListModel _generateMockData() {
    final now = DateTime.now();

    return SpotAssetListModel(
      assets: [
        SpotAssetModel(
          assetId: 'btc_001',
          symbol: 'BTC',
          fullName: 'Bitcoin',
          iconName: 'btc',
          totalAmount: 0.0002,
          frozenAmount: 0.0,
          availableAmount: 0.0002,
          totalPnL: 125.50,
          totalPnLRate: 15.25,
          costPrice: 65000.0,
          breakevenPrice: 64500.0,
          currentPrice: 67000.0,
          totalValue: 13.4,
          fiatValue: 94.38,
          fiatSymbol: '¥',
          lastUpdated: now,
        ),
        SpotAssetModel(
          assetId: 'eth_001',
          symbol: 'ETH',
          fullName: 'Ethereum',
          iconName: 'eth',
          totalAmount: 0.5,
          frozenAmount: 0.1,
          availableAmount: 0.4,
          totalPnL: -25.30,
          totalPnLRate: -5.12,
          costPrice: 3200.0,
          breakevenPrice: 3150.0,
          currentPrice: 3050.0,
          totalValue: 1525.0,
          fiatValue: 10725.0,
          fiatSymbol: '¥',
          lastUpdated: now,
        ),
        SpotAssetModel(
          assetId: 'usdt_001',
          symbol: 'USDT',
          fullName: 'Tether USD',
          iconName: 'usdt',
          totalAmount: 1000.0,
          frozenAmount: 200.0,
          availableAmount: 800.0,
          totalPnL: 0.0,
          totalPnLRate: 0.0,
          costPrice: 1.0,
          breakevenPrice: 1.0,
          currentPrice: 1.0,
          totalValue: 1000.0,
          fiatValue: 7030.0,
          fiatSymbol: '¥',
          lastUpdated: now,
        ),
        // 添加没有盈亏数据的资产（新持仓）
        SpotAssetModel(
          assetId: 'ada_001',
          symbol: 'ADA',
          fullName: 'Cardano',
          iconName: 'ada',
          totalAmount: 100.0,
          frozenAmount: 0.0,
          availableAmount: 100.0,
          totalPnL: null, // 没有盈亏数据
          totalPnLRate: null, // 没有盈亏率数据
          costPrice: 0.45,
          breakevenPrice: 0.44,
          currentPrice: 0.45,
          totalValue: 45.0,
          fiatValue: 316.35,
          fiatSymbol: '¥',
          lastUpdated: now,
        ),
      ],
      totalValue: 2538.4,
      totalPnL: 100.20,
      totalPnLRate: 4.11,
      lastUpdated: now,
    );
  }

  /// 清理资源
  @override
  void dispose() {
    super.dispose();
  }
}

/// 扩展方法：为 SpotAssetListModel 添加 copyWith 方法
extension SpotAssetListModelExtension on SpotAssetListModel {
  SpotAssetListModel copyWith({
    List<SpotAssetModel>? assets,
    double? totalValue,
    double? totalPnL,
    double? totalPnLRate,
    DateTime? lastUpdated,
  }) {
    return SpotAssetListModel(
      assets: assets ?? this.assets,
      totalValue: totalValue ?? this.totalValue,
      totalPnL: totalPnL ?? this.totalPnL,
      totalPnLRate: totalPnLRate ?? this.totalPnLRate,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}
