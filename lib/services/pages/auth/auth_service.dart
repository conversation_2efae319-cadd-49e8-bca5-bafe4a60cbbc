import '../../../models/pages/auth/login/login_request.dart';
import '../../../models/pages/auth/login/login_response.dart';
import '../../../models/pages/auth/verify_code/verify_code_request.dart';
import 'mock_auth_service.dart';

/// 认证服务接口
///
/// 提供统一的认证服务接口，可以在开发和生产环境中切换不同的实现
abstract class AuthService {
  /// 登录
  Future<LoginResponse> login(LoginRequest request);

  /// 验证码验证
  Future<bool> verifyCode(VerifyCodeRequest request);
}

/// 认证服务工厂
///
/// 根据环境配置返回相应的认证服务实现
class AuthServiceFactory {
  static AuthService _instance = MockAuthService();

  /// 获取认证服务实例
  static AuthService get instance => _instance;

  /// 设置认证服务实现（用于测试或切换环境）
  static void setInstance(AuthService service) {
    _instance = service;
  }

  /// 使用模拟服务（开发环境）
  static void useMockService() {
    _instance = MockAuthService();
  }

  /// 使用真实服务（生产环境）
  // static void useRealService() {
  //   _instance = RealAuthService();
  // }
}
