/*
*  会话管理服务
*  
*  负责管理用户登录状态、自动刷新token、会话保持等功能
*/

import 'dart:async';
import 'package:flutter/material.dart';
import '../../../providers/auth_provider.dart';

/// 会话管理服务
class SessionManager {
  static final SessionManager _instance = SessionManager._internal();
  factory SessionManager() => _instance;
  SessionManager._internal();

  /// 获取单例实例
  static SessionManager get instance => _instance;

  /// 认证提供者
  AuthProvider? _authProvider;

  /// 自动刷新定时器
  Timer? _refreshTimer;

  /// 是否已初始化
  bool _isInitialized = false;

  /// 初始化会话管理器
  void initialize(AuthProvider authProvider) {
    _authProvider = authProvider;
    _isInitialized = true;
    _startAutoRefreshTimer();
  }

  /// 销毁会话管理器
  void dispose() {
    _stopAutoRefreshTimer();
    _authProvider = null;
    _isInitialized = false;
  }

  /// 启动自动刷新定时器
  void _startAutoRefreshTimer() {
    _stopAutoRefreshTimer();

    // 每5分钟检查一次token状态
    _refreshTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _checkAndRefreshToken();
    });
  }

  /// 停止自动刷新定时器
  void _stopAutoRefreshTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// 检查并刷新token
  Future<void> _checkAndRefreshToken() async {
    if (!_isInitialized || _authProvider == null) return;

    try {
      // 如果用户已登录且token即将过期，自动刷新
      if (_authProvider!.isLoggedIn && _authProvider!.isTokenExpiringSoon) {
        await _authProvider!.refreshTokenIfNeeded();
      }
    } catch (e) {
      // 刷新失败时的处理
      debugPrint('自动刷新token失败: $e');
    }
  }

  /// 手动刷新token
  Future<bool> refreshToken() async {
    if (!_isInitialized || _authProvider == null) return false;

    try {
      return await _authProvider!.refreshTokenIfNeeded();
    } catch (e) {
      debugPrint('手动刷新token失败: $e');
      return false;
    }
  }

  /// 检查登录状态
  bool isLoggedIn() {
    if (!_isInitialized || _authProvider == null) return false;
    return _authProvider!.isLoggedIn && !_authProvider!.isTokenExpired;
  }

  /// 智能登录状态检查（会自动刷新token）
  Future<bool> checkLoginStatus() async {
    if (!_isInitialized || _authProvider == null) return false;

    try {
      return await _authProvider!.checkAndRefreshLoginStatus();
    } catch (e) {
      debugPrint('检查登录状态失败: $e');
      return false;
    }
  }

  /// 登出
  Future<void> logout() async {
    if (!_isInitialized || _authProvider == null) return;

    try {
      await _authProvider!.clearAuthData();
      _stopAutoRefreshTimer();
    } catch (e) {
      debugPrint('登出失败: $e');
    }
  }

  /// 获取用户信息
  dynamic getUserInfo() {
    if (!_isInitialized || _authProvider == null) return null;
    return _authProvider!.userInfo;
  }

  /// 获取访问令牌
  String? getAccessToken() {
    if (!_isInitialized || _authProvider == null) return null;
    return _authProvider!.accessToken;
  }

  /// 设置认证数据
  Future<void> setAuthData({
    dynamic userInfo,
    String? accessToken,
    String? refreshToken,
    DateTime? expireAt,
  }) async {
    if (!_isInitialized || _authProvider == null) return;

    try {
      await _authProvider!.setAuthData(
        userInfo: userInfo,
        accessToken: accessToken,
        refreshToken: refreshToken,
        expireAt: expireAt,
      );

      // 重新启动自动刷新定时器
      _startAutoRefreshTimer();
    } catch (e) {
      debugPrint('设置认证数据失败: $e');
    }
  }

  /// 获取调试信息
  Map<String, dynamic> getDebugInfo() {
    if (!_isInitialized || _authProvider == null) {
      return {
        'isInitialized': false,
        'error': 'SessionManager not initialized',
      };
    }

    return {
      'isInitialized': _isInitialized,
      'isLoggedIn': _authProvider!.isLoggedIn,
      'isTokenExpired': _authProvider!.isTokenExpired,
      'isTokenExpiringSoon': _authProvider!.isTokenExpiringSoon,
      'hasRefreshTimer': _refreshTimer != null,
      'expireAt': _authProvider!.expireAt?.toIso8601String(),
      'userInfo': _authProvider!.userInfo?.toJson(),
    };
  }

  /// 打印调试信息
  void printDebugInfo() {
    final info = getDebugInfo();
    debugPrint('=== SessionManager 调试信息 ===');
    info.forEach((key, value) {
      debugPrint('$key: $value');
    });
    debugPrint('==============================');
  }
}

/// 会话状态监听器
class SessionStateListener extends StatefulWidget {
  final Widget child;
  final VoidCallback? onSessionExpired;
  final VoidCallback? onSessionRefreshed;

  const SessionStateListener({
    super.key,
    required this.child,
    this.onSessionExpired,
    this.onSessionRefreshed,
  });

  @override
  State<SessionStateListener> createState() => _SessionStateListenerState();
}

class _SessionStateListenerState extends State<SessionStateListener>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // 当应用从后台回到前台时，检查登录状态
    if (state == AppLifecycleState.resumed) {
      _checkSessionOnResume();
    }
  }

  /// 应用恢复时检查会话状态
  Future<void> _checkSessionOnResume() async {
    try {
      final sessionManager = SessionManager.instance;
      final isLoggedIn = await sessionManager.checkLoginStatus();

      if (!isLoggedIn && widget.onSessionExpired != null) {
        widget.onSessionExpired!();
      }
    } catch (e) {
      debugPrint('检查会话状态失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// 使用说明：
/// 
/// 1. **在应用启动时初始化**：
/// ```dart
/// // 在 main.dart 或应用初始化时
/// final authProvider = Provider.of<AuthProvider>(context, listen: false);
/// SessionManager.instance.initialize(authProvider);
/// ```
/// 
/// 2. **包装应用根组件**：
/// ```dart
/// SessionStateListener(
///   onSessionExpired: () {
///     // 会话过期时的处理
///     Navigator.pushNamedAndRemoveUntil(context, '/login', (route) => false);
///   },
///   child: MyApp(),
/// )
/// ```
/// 
/// 3. **检查登录状态**：
/// ```dart
/// final isLoggedIn = await SessionManager.instance.checkLoginStatus();
/// ```
/// 
/// 4. **手动刷新token**：
/// ```dart
/// final success = await SessionManager.instance.refreshToken();
/// ```
/// 
/// 5. **获取调试信息**：
/// ```dart
/// SessionManager.instance.printDebugInfo();
/// ```
