import 'dart:math';
import 'package:flutter/foundation.dart';
import '../../../models/pages/auth/login/login_request.dart';
import '../../../models/pages/auth/login/login_response.dart';
import '../../../models/pages/auth/verify_code/verify_code_request.dart';
import '../../../models/user/user_info.dart';
import 'auth_service.dart';

/// 模拟认证服务
///
/// 用于开发和测试阶段的模拟登录功能
class MockAuthService implements AuthService {
  static final MockAuthService _instance = MockAuthService._internal();
  factory MockAuthService() => _instance;
  MockAuthService._internal();

  // 模拟的测试账户数据
  static const Map<String, Map<String, String>> _mockAccounts = {
    'main': {'<EMAIL>': 'password123', '***********': 'password123', '<EMAIL>': '123456', '<EMAIL>': 'admin123'},
    'sub': {'testuser': 'password123', 'demouser': '123456', 'subadmin': 'admin123'},
  };

  // 模拟用户数据
  static const Map<String, Map<String, dynamic>> _mockUserData = {
    '<EMAIL>': {'id': '1001', 'email': '<EMAIL>', 'nickname': '测试用户', 'avatar': '', 'isVip': false},
    '***********': {'id': '1002', 'phone': '***********', 'nickname': '手机用户', 'avatar': '', 'isVip': true},
    '<EMAIL>': {'id': '1003', 'email': '<EMAIL>', 'nickname': 'Demo用户', 'avatar': '', 'isVip': false},
    'testuser': {'id': '2001', 'subAccount': 'testuser', 'nickname': '子账户测试', 'avatar': '', 'isVip': false},
  };

  /// 模拟登录
  @override
  Future<LoginResponse> login(LoginRequest request) async {
    debugPrint('🔐 MockAuthService: 开始模拟登录 - ${request.toString()}');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 800));

    // 验证账户类型
    if (!_mockAccounts.containsKey(request.accountType)) {
      return LoginResponse.failure('不支持的账户类型');
    }

    final accounts = _mockAccounts[request.accountType]!;

    // 验证账户和密码
    if (!accounts.containsKey(request.account)) {
      return LoginResponse.failure('账户不存在');
    }

    if (accounts[request.account] != request.password) {
      return LoginResponse.failure('密码错误');
    }

    // 生成模拟会话ID
    final sessionId = _generateSessionId();
    final userData = _mockUserData[request.account] ?? {};

    debugPrint('✅ MockAuthService: 登录成功 - sessionId: $sessionId');

    return LoginResponse.success(sessionId: sessionId, userData: userData);
  }

  /// 模拟验证码验证
  @override
  Future<bool> verifyCode(VerifyCodeRequest request) async {
    debugPrint('📱 MockAuthService: 开始验证码验证 - ${request.toString()}');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));

    // 模拟验证码验证逻辑
    // 在模拟环境中，我们接受特定的验证码
    const validCodes = ['123456', '000000', '111111'];

    bool isEmailValid = validCodes.contains(request.emailCode);
    bool isPhoneValid = validCodes.contains(request.phoneCode);
    bool isGoogleValid = validCodes.contains(request.googleCode);

    bool isValid = isEmailValid && isPhoneValid && isGoogleValid;

    debugPrint('📱 MockAuthService: 验证码验证结果 - $isValid');
    debugPrint('   邮箱验证码: ${request.emailCode} -> $isEmailValid');
    debugPrint('   手机验证码: ${request.phoneCode} -> $isPhoneValid');
    debugPrint('   谷歌验证码: ${request.googleCode} -> $isGoogleValid');

    return isValid;
  }

  /// 生成模拟会话ID
  String _generateSessionId() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomNum = random.nextInt(999999);
    return 'mock_session_${timestamp}_$randomNum';
  }

  /// 获取模拟用户信息
  UserInfo? getMockUserInfo(String account) {
    final userData = _mockUserData[account];
    if (userData == null) return null;

    return UserInfo(
      id: userData['id'],
      email: userData['email'] as String?,
      username: userData['nickname'] as String,
      avatar: userData['avatar'] as String,
      isVip: userData['isVip'] as bool,
    );
  }

  /// 获取支持的测试账户列表（用于开发调试）
  Map<String, List<String>> getSupportedTestAccounts() {
    return {'main': _mockAccounts['main']!.keys.toList(), 'sub': _mockAccounts['sub']!.keys.toList()};
  }
}
