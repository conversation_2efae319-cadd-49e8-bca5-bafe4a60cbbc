import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../pages/auth/widgets/captcha_switch_dialog.dart';

/// 验证方式服务
///
/// 管理全局的验证方式状态，确保在不同页面间保持一致
class CaptchaService extends ChangeNotifier {
  static const String _captchaTypeKey = 'selected_captcha_type';

  // 单例模式
  static final CaptchaService _instance = CaptchaService._internal();
  factory CaptchaService() => _instance;
  CaptchaService._internal();

  // 当前选中的验证方式
  CaptchaType _currentType = CaptchaType.geetest;

  /// 获取当前验证方式
  CaptchaType get currentType => _currentType;

  /// 初始化验证方式设置
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final savedType = prefs.getString(_captchaTypeKey);

    if (savedType != null) {
      // 根据保存的字符串恢复验证方式
      for (final type in CaptchaType.values) {
        if (type.key == savedType) {
          _currentType = type;
          break;
        }
      }
    }

    debugPrint('🔐 CaptchaService: 初始化完成，当前验证方式: ${_currentType.displayName}');
  }

  /// 切换验证方式
  Future<void> switchCaptchaType(CaptchaType newType) async {
    if (_currentType == newType) return;

    _currentType = newType;
    notifyListeners();

    // 保存到本地存储
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_captchaTypeKey, newType.key);

    debugPrint('🔐 CaptchaService: 切换验证方式为: ${newType.displayName}');
  }

  /// 获取当前验证方式的显示名称
  String get currentDisplayName => _currentType.displayName;

  /// 获取当前验证方式的键值
  String get currentKey => _currentType.key;

  /// 是否为极验验证
  bool get isGeetest => _currentType == CaptchaType.geetest;

  /// 是否为谷歌验证
  bool get isGoogle => _currentType == CaptchaType.google;

  /// 重置为默认验证方式
  Future<void> resetToDefault() async {
    await switchCaptchaType(CaptchaType.geetest);
  }

  /// 清除保存的验证方式设置
  Future<void> clearSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_captchaTypeKey);
    _currentType = CaptchaType.geetest;
    notifyListeners();

    debugPrint('🔐 CaptchaService: 已清除验证方式设置');
  }
}
