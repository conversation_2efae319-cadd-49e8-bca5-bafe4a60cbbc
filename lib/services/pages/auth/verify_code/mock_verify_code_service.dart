import 'dart:math';
import 'package:flutter/foundation.dart';
import '../../../../models/pages/auth/verify_code/index.dart';
import 'verify_code_service.dart';

/// 模拟验证码服务实现
///
/// 用于开发和测试阶段的模拟验证码功能
class MockVerifyCodeService implements VerifyCodeService {
  static final MockVerifyCodeService _instance =
      MockVerifyCodeService._internal();
  factory MockVerifyCodeService() => _instance;
  MockVerifyCodeService._internal();

  // 有效的验证码（用于测试）
  static const Set<String> _validCodes = {
    '123456',
    '000000',
    '111111',
    '888888',
    '666666',
  };

  // 模拟验证码发送记录
  final Map<String, DateTime> _codeSentTimes = {};

  @override
  Future<VerifyCodeResponse> verifyCode(VerifyCodeRequest request) async {
    debugPrint('🔐 MockVerifyCodeService: 开始验证码验证 - ${request.toString()}');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 800));

    // 验证所有验证码，但跳过默认值 '123456'（表示不需要验证的类型）
    bool isEmailValid =
        request.emailCode == '123456' ||
        await isCodeValid(request.emailCode, 'email');
    bool isPhoneValid =
        request.phoneCode == '123456' ||
        await isCodeValid(request.phoneCode, 'phone');
    bool isGoogleValid =
        request.googleCode == '123456' ||
        await isCodeValid(request.googleCode, 'google');

    debugPrint('📱 验证结果:');
    debugPrint(
      '   邮箱验证码: ${request.emailCode} -> $isEmailValid ${request.emailCode == '123456' ? '(跳过)' : ''}',
    );
    debugPrint(
      '   手机验证码: ${request.phoneCode} -> $isPhoneValid ${request.phoneCode == '123456' ? '(跳过)' : ''}',
    );
    debugPrint(
      '   谷歌验证码: ${request.googleCode} -> $isGoogleValid ${request.googleCode == '123456' ? '(跳过)' : ''}',
    );

    if (!isEmailValid || !isPhoneValid || !isGoogleValid) {
      return VerifyCodeResponse.failure('验证码错误，请检查后重试');
    }

    // 生成模拟令牌
    final accessToken = _generateAccessToken();
    final refreshToken = _generateRefreshToken();
    final expireAt = DateTime.now().add(const Duration(hours: 24));

    // 创建模拟用户数据（使用正确的字段名格式）
    final userData = {
      'id': 'verified_user_${DateTime.now().millisecondsSinceEpoch}',
      'nickname': '已验证用户',
      'avatar': '',
      'is_vip': false, // 使用下划线格式
      'is_trader': false, // 添加缺失的字段
      'verifiedAt': DateTime.now().toIso8601String(),
    };

    debugPrint('✅ MockVerifyCodeService: 验证成功');

    return VerifyCodeResponse.success(
      accessToken: accessToken,
      refreshToken: refreshToken,
      expireAt: expireAt,
      userData: userData,
    );
  }

  @override
  Future<bool> sendEmailCode(String email, String sessionId) async {
    debugPrint('📧 MockVerifyCodeService: 发送邮箱验证码到 $email');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));

    // 检查发送频率限制
    final key = 'email_$sessionId';
    final lastSentTime = _codeSentTimes[key];
    if (lastSentTime != null) {
      final timeDiff = DateTime.now().difference(lastSentTime).inSeconds;
      if (timeDiff < 60) {
        debugPrint('❌ 发送过于频繁，请等待 ${60 - timeDiff} 秒');
        return false;
      }
    }

    // 记录发送时间
    _codeSentTimes[key] = DateTime.now();

    debugPrint('✅ 邮箱验证码发送成功（模拟）');
    debugPrint('💡 提示：测试验证码为 ${_validCodes.first}');

    return true;
  }

  @override
  Future<bool> sendPhoneCode(
    String phone,
    String countryCode,
    String sessionId,
  ) async {
    debugPrint('📱 MockVerifyCodeService: 发送手机验证码到 $countryCode$phone');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));

    // 检查发送频率限制
    final key = 'phone_$sessionId';
    final lastSentTime = _codeSentTimes[key];
    if (lastSentTime != null) {
      final timeDiff = DateTime.now().difference(lastSentTime).inSeconds;
      if (timeDiff < 60) {
        debugPrint('❌ 发送过于频繁，请等待 ${60 - timeDiff} 秒');
        return false;
      }
    }

    // 记录发送时间
    _codeSentTimes[key] = DateTime.now();

    debugPrint('✅ 手机验证码发送成功（模拟）');
    debugPrint('💡 提示：测试验证码为 ${_validCodes.first}');

    return true;
  }

  @override
  Future<bool> isCodeValid(String code, String type) async {
    // 模拟验证延迟
    await Future.delayed(const Duration(milliseconds: 100));

    final isValid = _validCodes.contains(code);
    debugPrint('🔍 验证码检查: $code ($type) -> $isValid');
    return isValid;
  }

  @override
  Future<int> getCodeRemainingTime(String sessionId, String type) async {
    final key = '${type}_$sessionId';
    final lastSentTime = _codeSentTimes[key];

    if (lastSentTime == null) {
      return 0;
    }

    final timeDiff = DateTime.now().difference(lastSentTime).inSeconds;
    final remainingTime = 60 - timeDiff;

    return remainingTime > 0 ? remainingTime : 0;
  }

  /// 生成模拟访问令牌
  String _generateAccessToken() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomNum = random.nextInt(999999);
    return 'mock_access_token_${timestamp}_$randomNum';
  }

  /// 生成模拟刷新令牌
  String _generateRefreshToken() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomNum = random.nextInt(999999);
    return 'mock_refresh_token_${timestamp}_$randomNum';
  }

  /// 获取有效的测试验证码（用于开发调试）
  Set<String> getValidTestCodes() {
    return _validCodes;
  }

  /// 清除验证码发送记录（用于测试）
  void clearSentTimes() {
    _codeSentTimes.clear();
  }
}
