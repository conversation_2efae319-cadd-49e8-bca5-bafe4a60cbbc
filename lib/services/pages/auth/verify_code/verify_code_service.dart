import '../../../../models/pages/auth/verify_code/index.dart';

/// 验证码服务接口
///
/// 定义验证码相关的业务逻辑接口
abstract class VerifyCodeService {
  /// 验证验证码
  Future<VerifyCodeResponse> verifyCode(VerifyCodeRequest request);

  /// 发送邮箱验证码
  Future<bool> sendEmailCode(String email, String sessionId);

  /// 发送手机验证码
  Future<bool> sendPhoneCode(
    String phone,
    String countryCode,
    String sessionId,
  );

  /// 检查验证码是否有效
  Future<bool> isCodeValid(String code, String type);

  /// 获取验证码剩余时间
  Future<int> getCodeRemainingTime(String sessionId, String type);
}
