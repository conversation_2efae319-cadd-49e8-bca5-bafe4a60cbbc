import 'verify_code_service.dart';
import 'mock_verify_code_service.dart';

/// 验证码服务工厂
/// 
/// 根据环境配置返回相应的验证码服务实现
class VerifyCodeServiceFactory {
  static VerifyCodeService _instance = MockVerifyCodeService();
  
  /// 获取验证码服务实例
  static VerifyCodeService get instance => _instance;
  
  /// 设置验证码服务实现（用于测试或切换环境）
  static void setInstance(VerifyCodeService service) {
    _instance = service;
  }
  
  /// 使用模拟服务（开发环境）
  static void useMockService() {
    _instance = MockVerifyCodeService();
  }
  
  /// 使用真实服务（生产环境）
  // static void useRealService() {
  //   _instance = RealVerifyCodeService();
  // }
}
