import 'dart:math';
import 'package:flutter/foundation.dart';
import '../../../../models/pages/auth/register/index.dart';
import '../login/mock_login_service.dart';
import 'register_service.dart';

/// 模拟注册服务实现
///
/// 用于开发和测试阶段的模拟注册功能
class MockRegisterService implements RegisterService {
  static final MockRegisterService _instance = MockRegisterService._internal();
  factory MockRegisterService() => _instance;
  MockRegisterService._internal();

  // 模拟已存在的账户（用于检查重复）
  static const Set<String> _existingEmails = {
    '<EMAIL>', // 默认管理员账号
  };

  static const Set<String> _existingPhones = {
    '13800138000', // 默认管理员手机号
  };

  // 有效的邀请码
  static const Set<String> _validInviteCodes = {'INVITE123', 'WELCOME2024', 'NEWUSER', 'DEMO'};

  @override
  Future<RegisterResponse> register(RegisterRequest request) async {
    debugPrint('📝 MockRegisterService: 开始模拟注册 - ${request.toString()}');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 1000));

    // 验证请求数据
    if (!request.isValid) {
      return RegisterResponse.failure('注册信息不完整或格式错误');
    }

    // 检查账户是否已存在
    if (request.registrationType == 'email') {
      if (await checkEmailExists(request.email!)) {
        return RegisterResponse.failure('该邮箱已被注册');
      }
    } else if (request.registrationType == 'phone') {
      if (await checkPhoneExists(request.phone!, request.countryCode!)) {
        return RegisterResponse.failure('该手机号已被注册');
      }
    }

    // 验证邀请码（如果提供）
    if (request.inviteCode != null && request.inviteCode!.isNotEmpty) {
      if (!await validateInviteCode(request.inviteCode!)) {
        return RegisterResponse.failure('邀请码无效');
      }
    }

    // 生成模拟会话ID和用户ID
    final sessionId = _generateSessionId();
    final userId = _generateUserId();

    // 创建模拟用户数据
    final userData = {
      'id': userId,
      'registrationType': request.registrationType,
      if (request.email != null) 'email': request.email,
      if (request.phone != null) 'phone': request.phone,
      if (request.countryCode != null) 'countryCode': request.countryCode,
      'nickname': _generateNickname(request),
      'avatar': '',
      'isVip': false,
      'hasGoogleAuth': false, // 新注册用户默认未绑定谷歌验证器
      'registeredAt': DateTime.now().toIso8601String(),
      'inviteCode': request.inviteCode,
    };

    // 注册流程需要双重验证，所以添加对应的绑定信息
    if (request.registrationType == 'email') {
      // 邮箱注册时，需要验证手机号
      userData['phone'] = '138****8888'; // 模拟绑定的手机号
      userData['countryCode'] = '+86';
    } else {
      // 手机号注册时，需要验证邮箱
      userData['email'] = 'user****@example.com'; // 模拟绑定的邮箱
    }

    debugPrint('✅ MockRegisterService: 注册成功 - sessionId: $sessionId, userId: $userId');
    debugPrint('📝 MockRegisterService: 用户数据 - $userData');

    // 将注册成功的账号添加到登录服务中
    final accountType = request.registrationType;
    final account = request.registrationType == 'email' ? request.email! : request.phone!;

    MockLoginService.addRegisteredAccount(accountType: accountType, account: account, password: request.password, userData: userData);

    return RegisterResponse.needsVerification(sessionId: sessionId, message: '注册成功，请完成邮箱和手机验证');
  }

  @override
  Future<bool> checkEmailExists(String email) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 300));

    final exists = _existingEmails.contains(email.toLowerCase());
    debugPrint('📧 MockRegisterService: 检查邮箱 $email - ${exists ? '已存在' : '可用'}');
    return exists;
  }

  @override
  Future<bool> checkPhoneExists(String phone, String countryCode) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 300));

    final exists = _existingPhones.contains(phone);
    debugPrint('📱 MockRegisterService: 检查手机号 $countryCode$phone - ${exists ? '已存在' : '可用'}');
    return exists;
  }

  @override
  Future<bool> validateInviteCode(String inviteCode) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 200));

    final isValid = _validInviteCodes.contains(inviteCode.toUpperCase());
    debugPrint('🎫 MockRegisterService: 验证邀请码 $inviteCode - ${isValid ? '有效' : '无效'}');
    return isValid;
  }

  @override
  List<String> getSupportedRegistrationTypes() {
    return ['email', 'phone'];
  }

  @override
  Future<bool> sendEmailCode(String email, String type) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(seconds: 1));

    // 模拟发送成功
    debugPrint('📧 模拟发送邮箱验证码: $email, 类型: $type');
    return true;
  }

  @override
  Future<bool> sendSmsCode(String phone, String countryCode, String type) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(seconds: 1));

    // 模拟发送成功
    debugPrint('📱 模拟发送短信验证码: +$countryCode $phone, 类型: $type');
    return true;
  }

  /// 生成模拟会话ID
  String _generateSessionId() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomNum = random.nextInt(999999);
    return 'mock_register_session_${timestamp}_$randomNum';
  }

  /// 生成模拟用户ID
  String _generateUserId() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomNum = random.nextInt(9999);
    return 'user_${timestamp}_$randomNum';
  }

  /// 生成模拟昵称
  String _generateNickname(RegisterRequest request) {
    if (request.registrationType == 'email') {
      final emailPrefix = request.email!.split('@')[0];
      return '用户_$emailPrefix';
    } else {
      final phoneLastFour = request.phone!.substring(request.phone!.length - 4);
      return '用户_$phoneLastFour';
    }
  }

  /// 获取有效的邀请码列表（用于开发调试）
  Set<String> getValidInviteCodes() {
    return _validInviteCodes;
  }

  /// 获取已存在的测试账户（用于开发调试）
  Map<String, Set<String>> getExistingTestAccounts() {
    return {'emails': _existingEmails, 'phones': _existingPhones};
  }

  /// 获取调试信息
  void printDebugInfo() {
    debugPrint('📊 MockRegisterService 调试信息:');
    debugPrint('  有效邀请码: $_validInviteCodes');
    debugPrint('  已存在邮箱: $_existingEmails');
    debugPrint('  已存在手机: $_existingPhones');
  }
}
