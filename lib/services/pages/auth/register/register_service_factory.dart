import 'package:flutter/foundation.dart';
import 'register_service.dart';
import 'mock_register_service.dart';
import '../../../clients/dio_request.dart';
import '../../../config/api_route.dart';

/// 注册服务工厂
///
/// 根据环境配置返回相应的注册服务实现
class RegisterServiceFactory {
  static RegisterService _instance = MockRegisterService();

  /// 获取注册服务实例
  static RegisterService get instance => _instance;

  /// 设置注册服务实现（用于测试或切换环境）
  static void setInstance(RegisterService service) {
    _instance = service;
  }

  /// 使用模拟服务（开发环境）
  static void useMockService() {
    _instance = MockRegisterService();
  }

  /// 使用真实服务（生产环境）
  // static void useRealService() {
  //   _instance = RealRegisterService();
  // }

  /// 发送邮箱验证码
  static Future<Map<String, dynamic>> sendEmailCode(String email, String type) async {
    try {
      final response = await DioRequest.instance.post(ApiRoute.sendEmailCode, body: {'email': email, 'type': type});

      if (response.success) {
        return {'success': true, 'message': '邮箱验证码发送成功'};
      } else {
        return {'success': false, 'message': response.message, 'code': response.statusCode};
      }
    } catch (e) {
      debugPrint('❌ 发送邮箱验证码失败: $e');
      return {'success': false, 'message': '网络错误，请稍后重试'};
    }
  }

  /// 发送短信验证码
  static Future<Map<String, dynamic>> sendSmsCode(String phone, String countryCode, String type) async {
    try {
      final response = await DioRequest.instance.post(ApiRoute.sendSmsCode, body: {'phone': phone, 'code': countryCode, 'type': type});

      if (response.success) {
        return {'success': true, 'message': '短信验证码发送成功'};
      } else {
        return {'success': false, 'message': response.message, 'code': response.statusCode};
      }
    } catch (e) {
      debugPrint('❌ 发送短信验证码失败: $e');
      return {'success': false, 'message': '网络错误，请稍后重试'};
    }
  }

  /// 邮箱注册
  static Future<Map<String, dynamic>> registerByEmail({
    required String email,
    required String emailCode,
    required String password,
    String? inviteCode,
  }) async {
    try {
      final response = await DioRequest.instance.post(
        ApiRoute.registerByEmail,
        body: {
          'email': email,
          'email_code': emailCode,
          'invite_code': inviteCode ?? '',
          'password': password,
          'confirm_password': password,
        },
      );

      if (response.success) {
        return {'success': true, 'message': '注册成功', 'data': response.data};
      } else {
        return {'success': false, 'message': response.message, 'code': response.statusCode};
      }
    } catch (e) {
      debugPrint('❌ 邮箱注册失败: $e');
      return {'success': false, 'message': '网络错误，请稍后重试'};
    }
  }

  /// 手机注册
  static Future<Map<String, dynamic>> registerByPhone({
    required String phone,
    required String countryCode,
    required String smsCode,
    required String password,
    String? inviteCode,
  }) async {
    try {
      final response = await DioRequest.instance.post(
        ApiRoute.registerByPhone,
        body: {
          'phone': phone,
          'code': countryCode,
          'sms_code': smsCode,
          'invite_code': inviteCode ?? '',
          'password': password,
          'confirm_password': password,
        },
      );

      if (response.success) {
        return {'success': true, 'message': '注册成功', 'data': response.data};
      } else {
        return {'success': false, 'message': response.message, 'code': response.statusCode};
      }
    } catch (e) {
      debugPrint('❌ 手机注册失败: $e');
      return {'success': false, 'message': '网络错误，请稍后重试'};
    }
  }

  /// OAuth第三方登录
  static Future<Map<String, dynamic>> oauthLogin({
    required String provider,
    required String token,
    String? displayName,
    String? avatar,
    String? inviteCode,
  }) async {
    try {
      final response = await DioRequest.instance.post(
        ApiRoute.oauthLogin,
        body: {
          'provider': provider,
          'token': token,
          if (displayName != null && displayName.isNotEmpty) 'display_name': displayName,
          if (avatar != null && avatar.isNotEmpty) 'avatar': avatar,
          if (inviteCode != null && inviteCode.isNotEmpty) 'invite_code': inviteCode,
        },
      );

      if (response.success) {
        return {'success': true, 'message': 'OAuth登录成功', 'data': response.data};
      } else {
        return {'success': false, 'message': response.message, 'code': response.statusCode};
      }
    } catch (e) {
      debugPrint('❌ OAuth登录失败: $e');
      return {'success': false, 'message': '网络错误，请稍后重试'};
    }
  }
}
