import '../../../../models/pages/auth/register/index.dart';

/// 注册服务接口
///
/// 定义注册相关的业务逻辑接口
abstract class RegisterService {
  /// 执行注册
  Future<RegisterResponse> register(RegisterRequest request);

  /// 检查邮箱是否已存在
  Future<bool> checkEmailExists(String email);

  /// 检查手机号是否已存在
  Future<bool> checkPhoneExists(String phone, String countryCode);

  /// 验证邀请码
  Future<bool> validateInviteCode(String inviteCode);

  /// 获取支持的注册方式
  List<String> getSupportedRegistrationTypes();

  /// 发送邮箱验证码
  Future<bool> sendEmailCode(String email, String type);

  /// 发送短信验证码
  Future<bool> sendSmsCode(String phone, String countryCode, String type);
}
