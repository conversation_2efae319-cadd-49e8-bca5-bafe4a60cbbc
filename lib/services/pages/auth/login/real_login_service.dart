import 'package:flutter/foundation.dart';
import '../../../clients/dio_request.dart';
import '../../../config/api_route.dart';
import '../../../../models/pages/auth/login/index.dart';
import 'login_service.dart';

/// 真实登录服务实现
///
/// 调用后端API进行用户登录
class RealLoginService implements LoginService {
  @override
  Future<LoginResponse> login(LoginRequest request) async {
    try {
      debugPrint('🔐 开始真实登录: ${request.account}');

      // 组装请求参数
      final requestData = {'username': request.account, 'password': request.password};

      debugPrint('📤 登录请求参数: $requestData');

      // 调用登录接口
      final response = await DioRequest.instance.post(ApiRoute.login, body: requestData);

      if (response.success) {
        debugPrint('✅ 登录接口调用成功');

        // 解析返回数据
        final data = response.data as Map<String, dynamic>?;

        if (data != null) {
          // 检查是否需要验证
          final needVerify = data['need_verify'] == true;

          if (needVerify) {
            debugPrint('🔐 登录成功但需要验证');
            return LoginResponse(
              success: true,
              message: '需要验证',
              userData: data,
              sessionId: data['session_id']?.toString(), // 验证时可能需要sessionId
            );
          } else {
            debugPrint('🎉 登录成功无需验证');
            return LoginResponse(success: true, message: '登录成功', userData: data, sessionId: null);
          }
        } else {
          return LoginResponse(success: false, message: '登录数据异常', userData: null, sessionId: null);
        }
      } else {
        debugPrint('❌ 登录失败: ${response.message}');
        return LoginResponse(success: false, message: response.message, userData: null, sessionId: null);
      }
    } catch (e) {
      debugPrint('💥 登录异常: $e');
      return LoginResponse(success: false, message: '网络错误，请稍后重试', userData: null, sessionId: null);
    }
  }

  @override
  Future<bool> validateCredentials(String accountType, String account, String password) async {
    // 真实环境下可以调用验证接口
    // 这里简单返回true，实际验证在login方法中进行
    return true;
  }

  @override
  List<String> getSupportedAccountTypes() {
    return ['email', 'phone', 'username'];
  }
}
