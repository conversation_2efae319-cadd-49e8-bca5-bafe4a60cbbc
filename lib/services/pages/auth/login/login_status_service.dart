import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../providers/auth_provider.dart';
import '../../../../routes/index.dart';
import '../../../../models/user/user_info.dart';

/// 登录状态服务
///
/// 提供统一的登录状态检查和控制功能
class LoginStatusService {
  static final LoginStatusService _instance = LoginStatusService._internal();
  factory LoginStatusService() => _instance;
  LoginStatusService._internal();

  /// 检查用户是否已登录
  static bool isLoggedIn(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    return authProvider.isLoggedIn && !authProvider.isTokenExpired;
  }

  /// 检查用户是否已登录（不依赖context）
  static bool isLoggedInStatic(AuthProvider authProvider) {
    return authProvider.isLoggedIn && !authProvider.isTokenExpired;
  }

  /// 获取用户信息
  static UserInfo? getUserInfo(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    return authProvider.userInfo;
  }

  /// 要求登录 - 如果未登录则跳转到登录页面
  static Future<bool> requireLogin(
    BuildContext context, {
    String? redirectRoute,
    Map<String, dynamic>? redirectArguments,
  }) async {
    if (isLoggedIn(context)) {
      return true;
    }

    // 保存重定向信息
    if (redirectRoute != null) {
      await _saveRedirectInfo(redirectRoute, redirectArguments);
    }

    // 跳转到登录页面
    NavigationService().navigateTo(AppRoutes.login);
    return false;
  }

  /// 登录成功后的重定向处理
  static Future<void> handleLoginSuccess(BuildContext context) async {
    final redirectInfo = await _getRedirectInfo();

    if (redirectInfo != null) {
      // 清除重定向信息
      await _clearRedirectInfo();

      // 跳转到原来要访问的页面
      NavigationService().navigateToAndClearStack(
        redirectInfo['route'] as String,
        arguments: redirectInfo['arguments'] as Map<String, dynamic>?,
      );
    } else {
      // 默认跳转到主页
      NavigationService().goToHome();
    }
  }

  /// 登出
  static Future<void> logout(BuildContext context) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.clearAuthData();

    // 清除重定向信息
    await _clearRedirectInfo();

    // 跳转到登录页面
    NavigationService().navigateToAndClearStack(AppRoutes.login);
  }

  /// 保存重定向信息
  static Future<void> _saveRedirectInfo(
    String route,
    Map<String, dynamic>? arguments,
  ) async {
    // 这里可以使用SharedPreferences或其他存储方式
    // 暂时使用内存存储
    _redirectRoute = route;
    _redirectArguments = arguments;
  }

  /// 获取重定向信息
  static Future<Map<String, dynamic>?> _getRedirectInfo() async {
    if (_redirectRoute != null) {
      return {'route': _redirectRoute!, 'arguments': _redirectArguments};
    }
    return null;
  }

  /// 清除重定向信息
  static Future<void> _clearRedirectInfo() async {
    _redirectRoute = null;
    _redirectArguments = null;
  }

  // 临时存储重定向信息（实际项目中应该使用持久化存储）
  static String? _redirectRoute;
  static Map<String, dynamic>? _redirectArguments;
}

/// 登录状态检查装饰器
///
/// 用于包装需要登录的Widget
class LoginRequired extends StatelessWidget {
  final Widget child;
  final Widget? fallback;
  final String? redirectRoute;
  final Map<String, dynamic>? redirectArguments;

  const LoginRequired({
    super.key,
    required this.child,
    this.fallback,
    this.redirectRoute,
    this.redirectArguments,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        if (LoginStatusService.isLoggedInStatic(authProvider)) {
          return child;
        }

        // 未登录时的处理
        if (fallback != null) {
          return fallback!;
        }

        // 自动跳转到登录页面
        WidgetsBinding.instance.addPostFrameCallback((_) {
          LoginStatusService.requireLogin(
            context,
            redirectRoute: redirectRoute,
            redirectArguments: redirectArguments,
          );
        });

        return const Center(child: CircularProgressIndicator());
      },
    );
  }
}

/// 登录状态构建器
///
/// 根据登录状态构建不同的Widget
class LoginStatusBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, UserInfo userInfo)
  loggedInBuilder;
  final Widget Function(BuildContext context) loggedOutBuilder;

  const LoginStatusBuilder({
    super.key,
    required this.loggedInBuilder,
    required this.loggedOutBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        if (LoginStatusService.isLoggedInStatic(authProvider) &&
            authProvider.userInfo != null) {
          return loggedInBuilder(context, authProvider.userInfo!);
        } else {
          return loggedOutBuilder(context);
        }
      },
    );
  }
}
