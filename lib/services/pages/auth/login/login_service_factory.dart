import 'login_service.dart';
import 'mock_login_service.dart';
import 'real_login_service.dart';

/// 登录服务工厂
///
/// 根据环境配置返回相应的登录服务实现
class LoginServiceFactory {
  static LoginService _instance = RealLoginService();

  /// 获取登录服务实例
  static LoginService get instance => _instance;

  /// 设置登录服务实现（用于测试或切换环境）
  static void setInstance(LoginService service) {
    _instance = service;
  }

  /// 使用模拟服务（开发环境）
  static void useMockService() {
    _instance = MockLoginService();
  }

  /// 使用真实服务（生产环境）
  static void useRealService() {
    _instance = RealLoginService();
  }
}
