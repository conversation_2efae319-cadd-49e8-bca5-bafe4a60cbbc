import 'dart:math';
import 'package:flutter/foundation.dart';
import '../../../../models/pages/auth/login/index.dart';
import 'login_service.dart';

/// 模拟登录服务实现
///
/// 用于开发和测试阶段的模拟登录功能
class MockLoginService implements LoginService {
  static final MockLoginService _instance = MockLoginService._internal();
  factory MockLoginService() => _instance;
  MockLoginService._internal();

  // 模拟的测试账户数据 - 使用非const以支持动态添加
  static final Map<String, Map<String, String>> _mockAccounts = {
    'email': {'<EMAIL>': 'admin1234'},
    'phone': {'***********': 'admin1234'},
  };

  // 模拟用户数据 - 使用非const以支持动态添加
  static final Map<String, Map<String, dynamic>> _mockUserData = {
    '<EMAIL>': {
      'id': '1001',
      'email': '<EMAIL>',
      'phone': '***********', // 绑定了手机号
      'countryCode': '+86',
      'nickname': '管理员',
      'avatar': '',
      'isVip': true,
      'hasGoogleAuth': true, // 绑定了谷歌验证器
      'registeredAt': '2024-01-01T00:00:00.000Z',
    },
    '***********': {
      'id': '1002',
      'phone': '***********',
      'email': '<EMAIL>', // 绑定了邮箱
      'countryCode': '+86',
      'nickname': '管理员',
      'avatar': '',
      'isVip': true,
      'hasGoogleAuth': true, // 绑定了谷歌验证器
      'registeredAt': '2024-01-01T00:00:00.000Z',
    },
  };

  @override
  Future<LoginResponse> login(LoginRequest request) async {
    debugPrint('🔐 MockLoginService: 开始模拟登录 - ${request.toString()}');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 800));

    // 验证账户类型
    if (!_mockAccounts.containsKey(request.accountType)) {
      return LoginResponse.failure('不支持的账户类型');
    }

    final accounts = _mockAccounts[request.accountType]!;

    // 验证账户和密码
    if (!accounts.containsKey(request.account)) {
      return LoginResponse.failure('账户不存在');
    }

    if (accounts[request.account] != request.password) {
      return LoginResponse.failure('密码错误');
    }

    // 生成模拟会话ID
    final sessionId = _generateSessionId();
    final userData = _mockUserData[request.account] ?? {};

    debugPrint('✅ MockLoginService: 登录成功 - sessionId: $sessionId');

    return LoginResponse.success(sessionId: sessionId, userData: userData);
  }

  @override
  Future<bool> validateCredentials(
    String accountType,
    String account,
    String password,
  ) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 300));

    final accounts = _mockAccounts[accountType];
    if (accounts == null) return false;

    return accounts[account] == password;
  }

  @override
  List<String> getSupportedAccountTypes() {
    return _mockAccounts.keys.toList();
  }

  /// 添加注册成功的账号到登录数据中
  static void addRegisteredAccount({
    required String accountType,
    required String account,
    required String password,
    required Map<String, dynamic> userData,
  }) {
    debugPrint('📝 MockLoginService: 添加注册账号到登录数据 - $accountType: $account');

    // 添加到账户列表
    if (!_mockAccounts.containsKey(accountType)) {
      _mockAccounts[accountType] = {};
    }
    _mockAccounts[accountType]![account] = password;

    // 添加用户数据
    _mockUserData[account] = userData;

    debugPrint('✅ MockLoginService: 账号添加成功，现在可以使用该账号登录');
  }

  /// 生成模拟会话ID
  String _generateSessionId() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomNum = random.nextInt(999999);
    return 'mock_login_session_${timestamp}_$randomNum';
  }

  /// 获取支持的测试账户列表（用于开发调试）
  Map<String, List<String>> getSupportedTestAccounts() {
    return {
      'email': _mockAccounts['email']!.keys.toList(),
      'phone': _mockAccounts['phone']!.keys.toList(),
    };
  }

  /// 获取模拟用户数据
  Map<String, dynamic>? getMockUserData(String account) {
    return _mockUserData[account];
  }

  /// 获取调试信息
  static void printDebugInfo() {
    debugPrint('📊 MockLoginService 调试信息:');
    debugPrint('  支持的账户类型: ${_mockAccounts.keys.toList()}');
    for (final accountType in _mockAccounts.keys) {
      debugPrint(
        '  $accountType 账户: ${_mockAccounts[accountType]!.keys.toList()}',
      );
    }
    debugPrint('  用户数据数量: ${_mockUserData.length}');
  }
}
