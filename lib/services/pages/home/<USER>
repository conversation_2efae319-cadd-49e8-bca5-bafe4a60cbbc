/*
 * 首页数据服务
 */

import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../../../models/pages/home/<USER>';
import '../../../models/pages/home/<USER>';
import '../../../models/pages/home/<USER>';

/// 首页数据服务
class HomeDataService {
  static final HomeDataService _instance = HomeDataService._internal();
  factory HomeDataService() => _instance;
  HomeDataService._internal();

  Timer? _refreshTimer;
  final StreamController<HomeDataModel> _dataController = StreamController<HomeDataModel>.broadcast();

  /// 数据流
  Stream<HomeDataModel> get dataStream => _dataController.stream;

  /// 当前数据
  HomeDataModel _currentData = const HomeDataModel();
  HomeDataModel get currentData => _currentData;

  /// 配置
  HomeConfigModel _config = const HomeConfigModel();
  HomeConfigModel get config => _config;

  /// 初始化服务
  Future<void> initialize({HomeConfigModel? config}) async {
    if (config != null) {
      _config = config;
    }

    debugPrint('🏠 HomeDataService 初始化');

    // 如果是首次初始化或者没有数据，则加载数据
    if (_currentData.status == HomeDataStatus.initial || !_currentData.hasData) {
      await loadData();
    }

    // 始终启动自动刷新（确保页面重新进入时恢复刷新）
    _startAutoRefresh();
  }

  /// 加载数据
  Future<void> loadData({bool isRefresh = false}) async {
    try {
      // 检查服务是否已销毁
      if (_dataController.isClosed) return;

      _updateData(isRefresh ? _currentData.toRefreshing() : _currentData.toLoading());

      // 模拟网络请求延迟
      await Future.delayed(const Duration(milliseconds: 800));

      // 再次检查服务是否已销毁（异步操作后）
      if (_dataController.isClosed) return;

      final tokenBoardData = await _fetchTokenBoardData();
      _updateData(_currentData.toLoaded(tokenBoardData));

      debugPrint('🏠 首页数据加载${isRefresh ? '刷新' : ''}完成');
    } catch (error) {
      debugPrint('❌ 首页数据加载失败: $error');
      // 检查服务是否已销毁再更新错误状态
      if (!_dataController.isClosed) {
        _updateData(_currentData.toError(error.toString()));
      }
    }
  }

  /// 刷新数据
  Future<void> refreshData() async {
    await loadData(isRefresh: true);
  }

  /// 获取代币看板数据
  Future<TokenBoardDataModel> _fetchTokenBoardData() async {
    // 模拟从API获取数据
    final boards = <TokenBoardType, TokenBoardModel>{};

    for (final boardType in _config.enabledBoardTypes) {
      boards[boardType] = await _fetchTokenBoardByType(boardType);
    }

    return TokenBoardDataModel(boards: boards);
  }

  /// 根据类型获取代币看板数据
  Future<TokenBoardModel> _fetchTokenBoardByType(TokenBoardType boardType) async {
    final tokensByType = <TokenTradeType, List<TokenModel>>{};

    // 根据看板类型生成不同的数据
    switch (boardType) {
      case TokenBoardType.favorite:
        tokensByType[TokenTradeType.spot] = _generateSpotTokens().take(2).toList();
        tokensByType[TokenTradeType.contract] = _generateContractTokens().take(1).toList();
        break;
      case TokenBoardType.hot:
        tokensByType[TokenTradeType.spot] = _generateSpotTokens();
        tokensByType[TokenTradeType.contract] = _generateContractTokens();
        // tokensByType[TokenTradeType.onChain] = _generateOnChainTokens();
        break;
      case TokenBoardType.gainers:
        tokensByType[TokenTradeType.spot] = _generateSpotTokens().where((token) => token.isUp).toList();
        tokensByType[TokenTradeType.contract] = _generateContractTokens().where((token) => token.isUp).toList();
        //tokensByType[TokenTradeType.onChain] = _generateOnChainTokens().where((token) => token.isUp).toList();
        break;
      case TokenBoardType.newCoins:
        tokensByType[TokenTradeType.spot] = _generateSpotTokens().take(2).toList();
        tokensByType[TokenTradeType.contract] = _generateContractTokens().take(1).toList();
        //tokensByType[TokenTradeType.onChain] = _generateOnChainTokens().take(1).toList();
        break;
      case TokenBoardType.stocks:
        tokensByType[TokenTradeType.spot] = _generateSpotTokens().take(2).toList();
      case TokenBoardType.forex:
        tokensByType[TokenTradeType.spot] = _generateSpotTokens().take(2).toList();
      case TokenBoardType.option:
        tokensByType[TokenTradeType.spot] = _generateSpotTokens().take(2).toList();
    }

    return TokenBoardModel(boardType: boardType, tokensByType: tokensByType);
  }

  /// 生成现货代币数据
  List<TokenModel> _generateSpotTokens() {
    return [
      _createTokenData(
        symbol: 'BTC',
        name: 'Bitcoin',
        price: '${43000 + Random().nextInt(1000)}.00',
        changeAmount: 1200 + Random().nextInt(100).toDouble(),
        changePercentage: 2.5 + Random().nextDouble(),
        volume: '2.${Random().nextInt(9)}B',
        isUp: true,
        onlineTime: '2009-01-03',
        openingGain: '+${15 + Random().nextInt(5)}.2%',
        marketCap: '850.2B',
      ),
    ];
  }

  /// 生成合约代币数据
  List<TokenModel> _generateContractTokens() {
    return [
      _createTokenData(
        symbol: 'BTC',
        name: 'BTC永续',
        price: '${43200 + Random().nextInt(100)}.50',
        changeAmount: 1250 + Random().nextInt(50).toDouble(),
        changePercentage: 2.8 + Random().nextDouble(),
        volume: '8.${Random().nextInt(5)}B',
        isUp: true,
        marketCap: '850.2B',
      ),
      _createTokenData(
        symbol: 'ETH',
        name: 'ETH永续',
        price: '${2650 + Random().nextInt(50)}.20',
        changeAmount: -(65 + Random().nextInt(15).toDouble()),
        changePercentage: -(2.3 + Random().nextDouble()),
        volume: '4.${Random().nextInt(3)}B',
        isUp: false,
        marketCap: '318.5B',
      ),
    ];
  }

  /// 生成链上交易代币数据
  List<TokenModel> _generateOnChainTokens() {
    return [
      _createTokenData(
        symbol: 'UNI',
        name: 'Uniswap',
        price: '${8 + Random().nextInt(2)}.45',
        changeAmount: 0.6 + Random().nextInt(10) / 100,
        changePercentage: 7 + Random().nextDouble(),
        volume: '${400 + Random().nextInt(100)}M',
        isUp: true,
        marketCap: '5.2B',
        onChainVolume: '${100 + Random().nextInt(50)}M',
      ),
      _createTokenData(
        symbol: 'AAVE',
        name: 'Aave',
        price: '${90 + Random().nextInt(10)}.20',
        changeAmount: 5 + Random().nextInt(2).toDouble(),
        changePercentage: 6 + Random().nextDouble(),
        volume: '${250 + Random().nextInt(50)}M',
        isUp: true,
        marketCap: '1.8B',
        onChainVolume: '${80 + Random().nextInt(20)}M',
      ),
    ];
  }

  /// 创建代币数据
  TokenModel _createTokenData({
    required String symbol,
    required String name,
    required String price,
    required double changeAmount,
    required double changePercentage,
    required String volume,
    required bool isUp,
    String quoteCurrency = 'USDT',
    String? onlineTime,
    String? openingGain,
    String? marketCap,
    String? onChainVolume,
  }) {
    return TokenModel(
      symbol: symbol,
      name: name,
      price: price,
      changeAmount: changeAmount,
      changePercentage: changePercentage,
      volume: volume,
      quoteCurrency: quoteCurrency,
      isUp: isUp,
      onlineTime: onlineTime,
      openingGain: openingGain,
      marketCap: marketCap,
      onChainVolume: onChainVolume,
    );
  }

  /// 更新数据
  void _updateData(HomeDataModel newData) {
    _currentData = newData;
    // 检查StreamController是否已关闭
    if (!_dataController.isClosed) {
      _dataController.add(_currentData);
    }
  }

  /// 开始自动刷新
  void _startAutoRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(Duration(seconds: _config.refreshIntervalSeconds), (_) {
      // 检查服务是否已销毁
      if (!_dataController.isClosed) {
        refreshData();
      }
    });
  }

  /// 停止自动刷新
  void stopAutoRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// 更新配置
  void updateConfig(HomeConfigModel newConfig) {
    _config = newConfig;
    _startAutoRefresh();
  }

  /// 销毁服务
  void dispose() {
    _refreshTimer?.cancel();
    _dataController.close();
    debugPrint('🏠 HomeDataService 已销毁');
  }
}
