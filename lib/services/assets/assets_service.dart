/*
 * 资产服务类
 * 
 * 功能：
 * - 管理用户资产数据
 * - 提供全局资产访问接口
 * - 支持资产数据缓存和更新
 */

import 'dart:async';
import 'package:flutter/foundation.dart';
import '../clients/dio_request.dart';
import '../config/api_route.dart';
import 'models/index.dart';

/// 资产服务类
class AssetsService {
  // 私有构造函数
  AssetsService._();

  // 单例实例
  static final AssetsService _instance = AssetsService._();

  // 获取单例实例
  static AssetsService get instance => _instance;

  // 当前资产数据
  AssetBalanceResponse? _currentAssets;

  // 数据流控制器
  final StreamController<AssetBalanceResponse?> _assetsController = 
      StreamController<AssetBalanceResponse?>.broadcast();

  // 是否已初始化
  bool _isInitialized = false;

  // 是否正在加载
  bool _isLoading = false;

  // 最后更新时间
  DateTime? _lastUpdated;

  /// 获取当前资产数据
  AssetBalanceResponse? get currentAssets => _currentAssets;

  /// 获取资产数据流
  Stream<AssetBalanceResponse?> get assetsStream => _assetsController.stream;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 最后更新时间
  DateTime? get lastUpdated => _lastUpdated;

  /// 是否有资产数据
  bool get hasAssets => _currentAssets != null && _currentAssets!.hasAnyBalance;

  /// 初始化服务
  Future<void> initialize() async {
    try {
      debugPrint('💰 正在初始化 AssetsService...');
      await loadAssets();
      _isInitialized = true;
      debugPrint('✅ AssetsService 初始化完成');
    } catch (e) {
      debugPrint('❌ AssetsService 初始化失败: $e');
      _isInitialized = true; // 即使失败也标记为已初始化，避免重复尝试
    }
  }

  /// 加载资产数据
  Future<void> loadAssets({bool forceRefresh = false}) async {
    // 避免重复加载
    if (_isLoading && !forceRefresh) {
      debugPrint('💰 资产数据正在加载中，跳过重复请求');
      return;
    }

    _isLoading = true;

    try {
      debugPrint('💰 开始加载用户资产数据...');

      // 发送API请求
      final response = await DioRequest.instance.get<Map<String, dynamic>>(
        ApiRoute.assetBalance,
        requireAuth: true,
        dataConverter: (data) => data as Map<String, dynamic>,
      );

      if (response.success && response.data != null) {
        // 解析资产数据
        final assetBalance = AssetBalanceResponse.fromJson(response.data!);
        
        // 更新数据
        _updateAssets(assetBalance);
        
        debugPrint('✅ 用户资产数据加载成功，账户数量: ${assetBalance.accounts.length}');
      } else {
        throw Exception('API请求失败: ${response.message}');
      }
    } catch (e) {
      debugPrint('❌ 加载用户资产数据失败: $e');
      
      // 如果是首次加载失败，设置空数据避免阻塞
      if (_currentAssets == null) {
        _updateAssets(const AssetBalanceResponse(accounts: []));
      }
      
      rethrow;
    } finally {
      _isLoading = false;
    }
  }

  /// 刷新资产数据
  Future<void> refreshAssets() async {
    await loadAssets(forceRefresh: true);
  }

  /// 更新资产数据
  void _updateAssets(AssetBalanceResponse assets) {
    _currentAssets = assets;
    _lastUpdated = DateTime.now();
    
    // 通知监听者
    if (!_assetsController.isClosed) {
      _assetsController.add(_currentAssets);
    }
  }

  /// 获取指定账户类型的余额
  AccountBalance? getAccountBalance(AccountType accountType) {
    return _currentAssets?.getAccountBalance(accountType);
  }

  /// 获取指定币种在所有账户中的总余额
  double getTotalBalanceForSymbol(String symbol) {
    return _currentAssets?.getTotalBalanceForSymbol(symbol) ?? 0.0;
  }

  /// 获取指定币种在指定账户中的余额
  CurrencyBalance? getCurrencyBalance(AccountType accountType, String symbol) {
    final account = getAccountBalance(accountType);
    return account?.getCurrencyBalance(symbol);
  }

  ///通过币种id获取余额
  CurrencyBalance ? getCurrencyBalanceById(AccountType accountType,int currencyId){
    final account = getAccountBalance(accountType);
    return account?.getCurrencyBalanceById(currencyId);
  }

  /// 获取现货账户USDT余额
  double get spotUsdtBalance {
    final currency = getCurrencyBalance(AccountType.spot, 'USDT');
    return currency?.totalAmount ?? 0.0;
  }

  /// 获取合约账户USDT余额
  double get futuresUsdtBalance {
    final currency = getCurrencyBalance(AccountType.futures, 'USDT');
    return currency?.totalAmount ?? 0.0;
  }

  /// 获取资金账户USDT余额
  double get fundingUsdtBalance {
    final currency = getCurrencyBalance(AccountType.funding, 'USDT');
    return currency?.totalAmount ?? 0.0;
  }

  /// 获取全仓杠杆账户USDT余额
  double get crossMarginUsdtBalance {
    final currency = getCurrencyBalance(AccountType.crossMargin, 'USDT');
    return currency?.totalAmount ?? 0.0;
  }

  /// 获取逐仓杠杆账户USDT余额
  double get isolatedMarginUsdtBalance {
    final currency = getCurrencyBalance(AccountType.isolatedMargin, 'USDT');
    return currency?.totalAmount ?? 0.0;
  }

  /// 获取链上交易账户USDT余额
  double get onchainUsdtBalance {
    final currency = getCurrencyBalance(AccountType.onchain, 'USDT');
    return currency?.totalAmount ?? 0.0;
  }

  /// 获取跟单账户USDT余额
  double get copyTradingUsdtBalance {
    final currency = getCurrencyBalance(AccountType.copyTrading, 'USDT');
    return currency?.totalAmount ?? 0.0;
  }

  /// 获取理财账户USDT余额
  double get earnUsdtBalance {
    final currency = getCurrencyBalance(AccountType.earn, 'USDT');
    return currency?.totalAmount ?? 0.0;
  }

  /// 获取所有账户USDT总余额
  double get totalUsdtBalance {
    return getTotalBalanceForSymbol('USDT');
  }

  /// 清理资源
  void dispose() {
    _assetsController.close();
    _currentAssets = null;
    _isInitialized = false;
    debugPrint('💰 AssetsService 已清理');
  }

  /// 重置服务状态
  void reset() {
    _currentAssets = null;
    _isInitialized = false;
    _isLoading = false;
    _lastUpdated = null;
    debugPrint('💰 AssetsService 状态已重置');
  }

  @override
  String toString() {
    return 'AssetsService(isInitialized: $_isInitialized, hasAssets: $hasAssets, lastUpdated: $_lastUpdated)';
  }

  /// 更新特定币种的余额
  Future<bool> updateCurrencyBalance(int currencyId, int accountType) async {
    try {
      debugPrint('🔄 更新币种余额: currencyId=$currencyId, accountType=$accountType');

      // 调用接口获取特定币种资产
      final response = await DioRequest.instance.get(
        '${ApiRoute.assetCurrency}/$currencyId?currency_id=$currencyId',
        requireAuth: true,
      );

      if (response.success && response.data != null) {
        final data = response.data as Map<String, dynamic>;

        // 解析返回数据
        final currencyIdFromResponse = data['currency_id'] as int;
        final symbol = data['symbol'] as String;
        final totalBalance = data['total_balance'] as String;
        final accounts = data['accounts'] as List<dynamic>;

        debugPrint('📊 接收到币种数据: $symbol, 总余额: $totalBalance');

        // 查找指定账户类型的数据
        Map<String, dynamic>? targetAccount;
        for (final account in accounts) {
          final accountMap = account as Map<String, dynamic>;
          if (accountMap['account_type'] == accountType) {
            targetAccount = accountMap;
            break;
          }
        }

        if (targetAccount == null) {
          debugPrint('❌ 未找到账户类型 $accountType 的数据');
          return false;
        }

        final accountName = targetAccount['account_name'] as String;
        final available = targetAccount['available'] as String;
        final frozen = targetAccount['frozen'] as String;

        debugPrint('💰 账户信息: $accountName, 可用: $available, 冻结: $frozen');

        // 更新单例中保存的资产数据
        _updateAssetBalance(currencyIdFromResponse, accountType, available, frozen);

        debugPrint('✅ 币种余额更新成功');
        return true;
      } else {
        debugPrint('❌ 获取币种余额失败: ${response.message}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ 更新币种余额异常: $e');
      return false;
    }
  }

  /// 更新资产余额数据到单例中
  void _updateAssetBalance(int currencyId, int accountType, String available, String frozen) {
    if (_currentAssets == null) {
      debugPrint('⚠️ 资产数据为空，无法更新');
      return;
    }

    // 获取所有账户
    final accounts = _currentAssets!.accounts;

    // 查找对应的账户
    for (final account in accounts) {
      if (account.accountType.value == accountType) {
        // 查找对应的币种
        for (int i = 0; i < account.currencies.length; i++) {
          final currency = account.currencies[i];
          if (currency.currencyId == currencyId) {
            // 更新余额数据
            final updatedCurrency = CurrencyBalance(
              currencyId: currency.currencyId,
              symbol: currency.symbol,
              logo: currency.logo,
              available: available,
              frozen: frozen,
              marginQuote: currency.marginQuote,
              marginFrozen: currency.marginFrozen,
            );

            // 更新币种列表
            account.currencies[i] = updatedCurrency;

            debugPrint('🔄 已更新资产数据: ${currency.symbol} - ${account.accountName}');
            return;
          }
        }

        debugPrint('⚠️ 未找到币种ID $currencyId 的数据');
        return;
      }
    }

    debugPrint('⚠️ 未找到账户类型 $accountType 的数据');
  }
}
