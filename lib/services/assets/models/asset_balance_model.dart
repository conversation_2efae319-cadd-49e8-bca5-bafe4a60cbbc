/*
 * 资产余额数据模型
 */

/// 账户类型枚举
enum AccountType {
  /// 资金账户
  funding(0, '资金账户'),
  /// 现货账户
  spot(1, '现货账户'),
  /// 合约账户
  futures(2, '合约账户'),
  /// 全仓杠杆账户
  crossMargin(3, '全仓杠杆账户'),
  /// 链上交易账户
  onchain(4, '链上交易账户'),
  /// 跟单账户
  copyTrading(5, '跟单账户'),
  /// 理财账户
  earn(6, '理财账户'),
  /// 逐仓杠杆账户
  isolatedMargin(7, '逐仓杠杆账户');

  const AccountType(this.value, this.displayName);

  final int value;
  final String displayName;

  /// 从数值获取账户类型
  static AccountType fromValue(int value) {
    return AccountType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => AccountType.funding,
    );
  }
}

/// 币种余额模型
class CurrencyBalance {
  /// 币种ID
  final int currencyId;

  /// 币种符号
  final String symbol;

  /// 币种图标URL
  final String? logo;

  /// 可用余额
  final String available;

  /// 冻结余额
  final String frozen;

  /// 保证金余额（逐仓杠杆专用）
  final String? marginQuote;

  /// 保证金冻结（逐仓杠杆专用）
  final String? marginFrozen;

  const CurrencyBalance({
    required this.currencyId,
    required this.symbol,
    this.logo,
    required this.available,
    required this.frozen,
    this.marginQuote,
    this.marginFrozen,
  });

  /// 获取可用余额数值
  double get availableAmount => double.tryParse(available) ?? 0.0;

  /// 获取冻结余额数值
  double get frozenAmount => double.tryParse(frozen) ?? 0.0;

  /// 获取保证金余额数值
  double get marginQuoteAmount => double.tryParse(marginQuote ?? '0') ?? 0.0;

  /// 获取保证金冻结数值
  double get marginFrozenAmount => double.tryParse(marginFrozen ?? '0') ?? 0.0;

  /// 获取总余额数值
  double get totalAmount => availableAmount + frozenAmount;

  /// 获取总保证金数值
  double get totalMarginAmount => marginQuoteAmount + marginFrozenAmount;

  /// 是否有余额
  bool get hasBalance => totalAmount > 0 || totalMarginAmount > 0;

  /// 是否为逐仓杠杆交易对
  bool get isIsolatedMarginPair => marginQuote != null || marginFrozen != null;

  /// 从JSON创建实例
  factory CurrencyBalance.fromJson(Map<String, dynamic> json) {
    return CurrencyBalance(
      currencyId: json['currency_id'] as int,
      symbol: json['symbol'] as String,
      logo: json['logo'] as String?,
      available: json['available'] as String,
      frozen: json['frozen'] as String,
      marginQuote: json['margin_quote'] as String?,
      marginFrozen: json['margin_frozen'] as String?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'currency_id': currencyId,
      'symbol': symbol,
      'logo': logo,
      'available': available,
      'frozen': frozen,
      if (marginQuote != null) 'margin_quote': marginQuote,
      if (marginFrozen != null) 'margin_frozen': marginFrozen,
    };
  }

  @override
  String toString() {
    return 'CurrencyBalance(currencyId: $currencyId, symbol: $symbol, available: $available, frozen: $frozen, marginQuote: $marginQuote, marginFrozen: $marginFrozen)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CurrencyBalance &&
        other.currencyId == currencyId &&
        other.symbol == symbol &&
        other.available == available &&
        other.frozen == frozen &&
        other.marginQuote == marginQuote &&
        other.marginFrozen == marginFrozen;
  }

  @override
  int get hashCode {
    return Object.hash(currencyId, symbol, available, frozen, marginQuote, marginFrozen);
  }
}

/// 账户余额模型
class AccountBalance {
  /// 账户类型
  final AccountType accountType;
  
  /// 账户名称
  final String accountName;
  
  /// 币种余额列表
  final List<CurrencyBalance> currencies;

  const AccountBalance({
    required this.accountType,
    required this.accountName,
    required this.currencies,
  });

  /// 获取指定币种的余额
  CurrencyBalance? getCurrencyBalance(String symbol) {
    try {
      return currencies.firstWhere((currency) => currency.symbol == symbol);
    } catch (e) {
      return null;
    }
  }

  //通过币种id获取余额
  CurrencyBalance ? getCurrencyBalanceById(int currencyId){
    try {
      return currencies.firstWhere((currency) => currency.currencyId == currencyId);
    } catch (e) {
      return null;
    }
  }

  /// 获取有余额的币种列表
  List<CurrencyBalance> get currenciesWithBalance {
    return currencies.where((currency) => currency.hasBalance).toList();
  }

  /// 是否有余额
  bool get hasBalance => currenciesWithBalance.isNotEmpty;

  /// 从JSON创建实例
  factory AccountBalance.fromJson(Map<String, dynamic> json) {
    final accountType = AccountType.fromValue(json['account_type'] as int);
    final currenciesList = (json['currencies'] as List)
        .map((currencyJson) => CurrencyBalance.fromJson(currencyJson))
        .toList();

    return AccountBalance(
      accountType: accountType,
      accountName: json['account_name'] as String,
      currencies: currenciesList,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'account_type': accountType.value,
      'account_name': accountName,
      'currencies': currencies.map((currency) => currency.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'AccountBalance(accountType: $accountType, accountName: $accountName, currencies: ${currencies.length})';
  }
}

/// 资产余额响应模型
class AssetBalanceResponse {
  /// 账户余额列表
  final List<AccountBalance> accounts;

  const AssetBalanceResponse({
    required this.accounts,
  });

  /// 获取指定类型的账户余额
  AccountBalance? getAccountBalance(AccountType accountType) {
    try {
      return accounts.firstWhere((account) => account.accountType == accountType);
    } catch (e) {
      return null;
    }
  }

  /// 获取指定币种在所有账户中的总余额
  double getTotalBalanceForSymbol(String symbol) {
    double total = 0.0;
    for (final account in accounts) {
      final currency = account.getCurrencyBalance(symbol);
      if (currency != null) {
        total += currency.totalAmount;
      }
    }
    return total;
  }

  /// 获取有余额的账户列表
  List<AccountBalance> get accountsWithBalance {
    return accounts.where((account) => account.hasBalance).toList();
  }

  /// 是否有任何余额
  bool get hasAnyBalance => accountsWithBalance.isNotEmpty;

  /// 从JSON创建实例
  factory AssetBalanceResponse.fromJson(Map<String, dynamic> json) {
    final accountsList = (json['accounts'] as List)
        .map((accountJson) => AccountBalance.fromJson(accountJson))
        .toList();

    return AssetBalanceResponse(
      accounts: accountsList,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'accounts': accounts.map((account) => account.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'AssetBalanceResponse(accounts: ${accounts.length})';
  }
}
