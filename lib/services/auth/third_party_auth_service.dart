import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

/// 第三方登录服务
class ThirdPartyAuthService {
  static final ThirdPartyAuthService _instance = ThirdPartyAuthService._internal();
  factory ThirdPartyAuthService() => _instance;
  ThirdPartyAuthService._internal();

  // Google登录实例
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: [
      'email',
      'profile',
    ],
  );

  /// Google登录
  Future<Map<String, dynamic>?> signInWithGoogle() async {
    try {
      // 检查是否已经登录
      final GoogleSignInAccount? currentUser = _googleSignIn.currentUser;
      
      GoogleSignInAccount? account;
      if (currentUser != null) {
        account = currentUser;
      } else {
        // 执行登录
        account = await _googleSignIn.signIn();
      }

      if (account == null) {
        debugPrint('Google登录被用户取消');
        return null;
      }

      // 获取认证信息
      final GoogleSignInAuthentication auth = await account.authentication;

      // 返回用户信息
      return {
        'type': 'google',
        'id': account.id,
        'email': account.email,
        'displayName': account.displayName,
        'photoUrl': account.photoUrl,
        'accessToken': auth.accessToken,
        'idToken': auth.idToken,
      };
    } catch (e) {
      debugPrint('Google登录失败: $e');
      return null;
    }
  }

  /// Apple登录
  Future<Map<String, dynamic>?> signInWithApple() async {
    try {
      // 检查Apple登录是否可用
      if (!await SignInWithApple.isAvailable()) {
        debugPrint('Apple登录不可用');
        return null;
      }

      // 执行Apple登录
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // 返回用户信息
      return {
        'type': 'apple',
        'userIdentifier': credential.userIdentifier,
        'email': credential.email,
        'givenName': credential.givenName,
        'familyName': credential.familyName,
        'identityToken': credential.identityToken,
        'authorizationCode': credential.authorizationCode,
      };
    } catch (e) {
      debugPrint('Apple登录失败: $e');
      return null;
    }
  }

  /// Google登出
  Future<void> signOutGoogle() async {
    try {
      await _googleSignIn.signOut();
      debugPrint('Google登出成功');
    } catch (e) {
      debugPrint('Google登出失败: $e');
    }
  }

  /// 检查Google登录状态
  Future<bool> isGoogleSignedIn() async {
    return await _googleSignIn.isSignedIn();
  }

  /// 获取当前Google用户
  GoogleSignInAccount? getCurrentGoogleUser() {
    return _googleSignIn.currentUser;
  }

  /// 断开Google连接
  Future<void> disconnectGoogle() async {
    try {
      await _googleSignIn.disconnect();
      debugPrint('Google连接断开成功');
    } catch (e) {
      debugPrint('Google连接断开失败: $e');
    }
  }
}
