/*
 * 货币管理服务
 * 
 * 功能：
 * - 管理用户选择的货币设置
 * - 提供货币切换和状态管理
 * - 本地存储持久化
 * - 全局货币状态通知
 */

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/config/static/app_constants.dart';
import '../../core/config/static/currency_constants.dart';
import '../../core/models/currency_model.dart';
import '../../models/currency/index.dart';
import '../../services/config/api_route.dart';
import '../../services/clients/dio_request.dart';

/// 货币管理服务
class CurrencyService extends ChangeNotifier {
  static final CurrencyService _instance = CurrencyService._internal();
  static CurrencyService get instance => _instance;
  CurrencyService._internal();

  // ========== 私有属性 ==========
  CurrencyModel _currentCurrency = CurrencyConfig.defaultCurrency;
  bool _isInitialized = false;

  // 新增属性
  SupportedCurrency? _selectedCurrency;
  Map<String, String> _exchangeRates = {};
  List<SupportedCurrency> _supportedCurrencies = [];

  // ========== 公共属性 ==========
  /// 当前选择的货币
  CurrencyModel get currentCurrency => _currentCurrency;

  /// 当前货币代码
  String get currentCurrencyCode => _currentCurrency.code;

  /// 当前货币符号
  String get currentCurrencySymbol => _currentCurrency.symbol;

  /// 当前货币名称
  String get currentCurrencyName => _currentCurrency.name;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 选择的货币
  SupportedCurrency? get selectedCurrency => _selectedCurrency;

  /// 汇率数据
  Map<String, String> get exchangeRates => _exchangeRates;

  /// 支持的货币列表
  List<SupportedCurrency> get supportedCurrencies => _supportedCurrencies;

  // ========== 初始化方法 ==========
  /// 初始化货币服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final savedCurrencyCode = prefs.getString(AppConfig.currencyKey);

      if (savedCurrencyCode != null) {
        final currency = CurrencyConfig.getCurrencyByCode(savedCurrencyCode);
        if (currency != null) {
          _currentCurrency = currency;
        }
      }

      _isInitialized = true;
      notifyListeners();

      debugPrint('✅ 货币服务初始化完成');
      debugPrint('   - 当前货币: ${_currentCurrency.code} (${_currentCurrency.name})');
    } catch (e) {
      debugPrint('❌ 货币服务初始化失败: $e');
      _isInitialized = true;
      notifyListeners();
    }
  }

  // ========== 货币管理方法 ==========
  /// 切换货币
  Future<bool> changeCurrency(String currencyCode) async {
    final currency = CurrencyConfig.getCurrencyByCode(currencyCode);
    if (currency == null) {
      debugPrint('❌ 不支持的货币代码: $currencyCode');
      return false;
    }

    if (_currentCurrency.code == currency.code) {
      debugPrint('ℹ️ 货币未发生变化: $currencyCode');
      return true;
    }

    try {
      _currentCurrency = currency;
      await _saveCurrencyToStorage();
      notifyListeners();

      debugPrint('✅ 货币切换成功: ${currency.code} (${currency.name})');
      return true;
    } catch (e) {
      debugPrint('❌ 货币切换失败: $e');
      return false;
    }
  }

  /// 切换货币（使用货币模型）
  Future<bool> changeCurrencyModel(CurrencyModel currency) async {
    return await changeCurrency(currency.code);
  }

  /// 重置为默认货币
  Future<bool> resetToDefault() async {
    return await changeCurrency(CurrencyConfig.defaultCurrency.code);
  }

  // ========== 货币查询方法 ==========
  /// 获取所有支持的货币
  List<CurrencyModel> getSupportedCurrencies() {
    return CurrencyConfig.supportedCurrencies;
  }

  /// 搜索货币
  List<CurrencyModel> searchCurrencies(String query) {
    return CurrencyConfig.searchCurrencies(query);
  }

  /// 验证货币代码
  bool isSupportedCurrency(String code) {
    return CurrencyConfig.isSupportedCurrency(code);
  }

  /// 根据代码获取货币信息
  CurrencyModel? getCurrencyByCode(String code) {
    return CurrencyConfig.getCurrencyByCode(code);
  }

  // ========== 格式化方法 ==========
  /// 格式化价格显示（带货币符号）
  String formatPrice(double price, {int decimalPlaces = 2}) {
    if (price.isNaN || price.isInfinite) {
      return '${_currentCurrency.symbol}0.${'0' * decimalPlaces}';
    }

    return '${_currentCurrency.symbol}${price.toStringAsFixed(decimalPlaces)}';
  }

  /// 格式化价格显示（不带货币符号）
  String formatPriceValue(double price, {int decimalPlaces = 2}) {
    if (price.isNaN || price.isInfinite) {
      return '0.${'0' * decimalPlaces}';
    }

    return price.toStringAsFixed(decimalPlaces);
  }

  /// 获取带货币符号的文本
  String getSymbolText() {
    return _currentCurrency.symbol;
  }

  // ========== 私有方法 ==========
  /// 保存货币设置到本地存储
  Future<void> _saveCurrencyToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConfig.currencyKey, _currentCurrency.code);
    } catch (e) {
      debugPrint('❌ 货币设置保存失败: $e');
      rethrow;
    }
  }

  // ========== 新增方法 ==========
  /// 获取货币数据
  Future<CurrencyResponseModel?> fetchCurrencyData() async {
    try {
      final response = await DioRequest.instance.get(ApiRoute.usdtRate);
      if (response.success && response.data != null) {
        final currencyResponse = CurrencyResponseModel.fromJson(response.data);
        _supportedCurrencies = currencyResponse.supported;
        _exchangeRates = currencyResponse.rates;
        return currencyResponse;
      }
      return null;
    } catch (e) {
      debugPrint('获取货币数据失败: $e');
      return null;
    }
  }

  /// 设置选择的货币
  Future<void> setSelectedCurrency(SupportedCurrency currency) async {
    try {
      _selectedCurrency = currency;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('selected_currency', currency.toJson().toString());

      notifyListeners();
    } catch (e) {
      debugPrint('保存货币设置失败: $e');
    }
  }

  /// 获取当前汇率
  double getCurrentRate() {
    if (_selectedCurrency?.value != null && _exchangeRates.containsKey(_selectedCurrency!.value)) {
      return double.tryParse(_exchangeRates[_selectedCurrency!.value]!) ?? 1.0;
    }
    return 1.0;
  }

  /// 缓存汇率数据
  Future<void> cacheRates(Map<String, String> rates) async {
    try {
      _exchangeRates = rates;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('exchange_rates', rates.toString());
    } catch (e) {
      debugPrint('缓存汇率失败: $e');
    }
  }

  // ========== 调试方法 ==========
  /// 获取当前状态信息
  Map<String, dynamic> getDebugInfo() {
    return {
      'isInitialized': _isInitialized,
      'currentCurrency': _currentCurrency.toJson(),
      'supportedCurrenciesCount': _supportedCurrencies.length,
      'selectedCurrency': _selectedCurrency?.toJson(),
      'exchangeRatesCount': _exchangeRates.length,
    };
  }

  /// 打印当前状态
  void printDebugInfo() {
    final info = getDebugInfo();
    debugPrint('🔍 货币服务状态:');
    info.forEach((key, value) {
      debugPrint('   - $key: $value');
    });
  }
}
