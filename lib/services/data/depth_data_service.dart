import '../clients/request_client.dart';
import '../clients/websocket_client.dart';
import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;

/// 支持的交易所枚举
enum Exchange {
  binance('Binance'),
  okx('OKX');

  const Exchange(this.displayName);
  final String displayName;
}

/// 交易所配置
class ExchangeConfig {
  final Exchange exchange;
  final String apiBaseUrl;
  final String wsBaseUrl;
  final String depthEndpoint;
  final String contractInfoEndpoint; // 新增：合约信息接口
  final String Function(String) formatSymbol;
  final String Function(String) getDepthWsUrl;
  final String Function(String) getContractInfoUrl; // 新增：获取合约信息URL

  const ExchangeConfig({
    required this.exchange,
    required this.apiBaseUrl,
    required this.wsBaseUrl,
    required this.depthEndpoint,
    required this.contractInfoEndpoint,
    required this.formatSymbol,
    required this.getDepthWsUrl,
    required this.getContractInfoUrl,
  });

  static const Map<Exchange, ExchangeConfig> configs = {
    Exchange.binance: ExchangeConfig(
      exchange: Exchange.binance,
      apiBaseUrl: 'https://fapi.binance.com',
      wsBaseUrl: 'wss://fstream.binance.com',
      depthEndpoint: '/fapi/v1/depth',
      contractInfoEndpoint: '/fapi/v1/exchangeInfo', // 币安合约信息接口
      formatSymbol: _formatBinanceSymbol,
      getDepthWsUrl: _getBinanceDepthWsUrl,
      getContractInfoUrl: _getBinanceContractInfoUrl,
    ),
    Exchange.okx: ExchangeConfig(
      exchange: Exchange.okx,
      apiBaseUrl: 'https://www.okx.com',
      wsBaseUrl: 'wss://ws.okx.com:8443',
      depthEndpoint: '/api/v5/market/books',
      contractInfoEndpoint: '/api/v5/public/instruments', // OKX合约信息接口
      formatSymbol: _formatOkxSymbol,
      getDepthWsUrl: _getOkxDepthWsUrl,
      getContractInfoUrl: _getOkxContractInfoUrl,
    ),
  };

  static String _formatBinanceSymbol(String symbol) {
    return symbol.replaceAll('/', '').toUpperCase();
  }

  static String _formatOkxSymbol(String symbol) {
    return '${symbol.toUpperCase()}-SWAP'.replaceAll("/", "-");
  }

  static String _getBinanceDepthWsUrl(String symbol) {
    final formattedSymbol = symbol.replaceAll('/', '').toLowerCase();
    return 'wss://fstream.binance.com/ws/$formattedSymbol@depth';
  }

  static String _getOkxDepthWsUrl(String symbol) {
    // OKX使用统一的WebSocket端点，具体订阅在连接后发送
    return 'wss://ws.okx.com:8443/ws/v5/public';
  }

  static String _getBinanceContractInfoUrl(String symbol) {
    // 币安合约信息URL（币安期货合约面值固定为1，不需要特殊处理）
    return 'https://fapi.binance.com/fapi/v1/exchangeInfo';
  }

  static String _getOkxContractInfoUrl(String symbol) {
    // OKX合约信息URL
    final formattedSymbol = _formatOkxSymbol(symbol);
    return 'https://www.okx.com/api/v5/public/instruments?instType=SWAP&instId=$formattedSymbol';
  }
}

/// 原始成交数据条目
class RawTradeEntry {
  final double price;
  final double quantity;
  final bool isBuy;
  final int timestamp;
  final Exchange exchange; // 添加交易所标识

  RawTradeEntry({
    required this.price,
    required this.quantity,
    required this.isBuy,
    required this.timestamp,
    required this.exchange,
  });
}

/// 简化的深度数据条目模型
class DepthEntry {
  final double price;
  final double? buyVolume;
  final double? sellVolume;
  final double? buyTradeVolume; // 买入成交量
  final double? sellTradeVolume; // 卖出成交量
  final bool isHighlighted; // 是否高于平均值需要高亮

  DepthEntry({
    required this.price,
    this.buyVolume,
    this.sellVolume,
    this.buyTradeVolume,
    this.sellTradeVolume,
    this.isHighlighted = false,
  });
}

/// 深度数据服务状态
enum DepthDataStatus { initial, loading, loaded, error }

/// 简化的深度数据服务
class DepthDataService {
  // 构造函数 - 移除单例模式，支持多实例
  DepthDataService({String? instanceId, List<Exchange>? enabledExchanges})
    : _instanceId = instanceId ?? 'default',
      _enabledExchanges = enabledExchanges ?? [Exchange.binance, Exchange.okx] {
    // 设置HTTP代理
    _requestClient.setProxy('127.0.0.1:1087');

    // 启动定期清理任务
    _startPeriodicCleanup();
  }

  // 实例标识符
  final String _instanceId;

  // 启用的交易所列表
  final List<Exchange> _enabledExchanges;

  // API请求客户端
  final RequestClient _requestClient = RequestClient();

  // 多个WebSocket客户端，每个交易所一个
  final Map<Exchange, WebsocketClient> _wsClients = {};

  // 存储各交易所的合约面值
  final Map<Exchange, double> _contractValues = {};

  // 状态控制器
  final StreamController<DepthDataStatus> _statusController =
      StreamController<DepthDataStatus>.broadcast();
  Stream<DepthDataStatus> get statusStream => _statusController.stream;
  DepthDataStatus _status = DepthDataStatus.initial;

  // 数据控制器
  final StreamController<List<DepthEntry>> _dataController =
      StreamController<List<DepthEntry>>.broadcast();
  Stream<List<DepthEntry>> get dataStream => _dataController.stream;

  // 深度数据存储 - 使用Map以价格为key快速查找更新
  final Map<double, DepthEntry> _depthDataMap = {};
  List<DepthEntry> _sortedDepthData = [];

  // 多交易所原始深度数据存储 - 用于重新聚合
  final Map<Exchange, Map<double, double>> _originalBids = {};
  final Map<Exchange, Map<double, double>> _originalAsks = {};

  // 原始成交数据存储 - 保留最近的成交数据用于重新聚合
  final List<RawTradeEntry> _rawTradeData = [];
  final int _maxRawTradeCount = 10000; // 最多保留1万条原始成交数据
  final int _maxTradeDataAgeMs = 3600000; // 1小时内的成交数据

  // 成交量数据存储 - 按价格聚合成交量
  final Map<double, double> _buyTradeVolumes = {};
  final Map<double, double> _sellTradeVolumes = {};

  // 上一次处理的聚合价格，用于检测价格变化
  double? _lastAggregatedPrice;

  // 交易对
  String _symbol = '';

  // 错误信息
  String _errorMessage = '';
  String get errorMessage => _errorMessage;

  // 最大档位数
  int _maxDepthLevels = 100;

  // 价格聚合精度
  double _priceAggregation = 1.0;

  // 重新聚合期间的数据缓存
  final List<Map<String, dynamic>> _pendingUpdates = [];
  bool _isReaggregating = false;

  // 定期清理定时器
  Timer? _cleanupTimer;

  // 平均值计算定时器
  Timer? _averageCalculationTimer;

  // 挂单平均值
  double _buyVolumeAverage = 0.0;
  double _sellVolumeAverage = 0.0;

  // 数据更新优化相关
  Timer? _aggregationTimer;
  bool _hasPendingAggregation = false;
  final int _aggregationDelayMs = 100; // 聚合延迟100ms

  Timer? _uiUpdateTimer;
  bool _hasPendingUIUpdate = false;
  final int _uiUpdateDelayMs = 200; // UI更新延迟200ms

  // 批量更新缓存
  final Map<Exchange, Map<double, double>> _pendingBidUpdates = {};
  final Map<Exchange, Map<double, double>> _pendingAskUpdates = {};

  /// 启动定期清理任务
  void _startPeriodicCleanup() {
    // 每30分钟清理一次过期的成交数据
    _cleanupTimer = Timer.periodic(const Duration(minutes: 30), (timer) {
      _cleanupExpiredTradeData();
    });

    // 每5秒计算一次挂单平均值并更新高亮状态
    _averageCalculationTimer = Timer.periodic(const Duration(seconds: 5), (
      timer,
    ) {
      _calculateVolumeAveragesAndUpdateHighlight();
    });
  }

  /// 延迟聚合数据（防抖）
  void _scheduleAggregation() {
    if (_hasPendingAggregation) return;

    _hasPendingAggregation = true;
    _aggregationTimer?.cancel();
    _aggregationTimer = Timer(Duration(milliseconds: _aggregationDelayMs), () {
      _performBatchAggregation();
      _hasPendingAggregation = false;
    });
  }

  /// 延迟UI更新（防抖）
  void _scheduleUIUpdate() {
    if (_hasPendingUIUpdate) return;

    _hasPendingUIUpdate = true;
    _uiUpdateTimer?.cancel();
    _uiUpdateTimer = Timer(Duration(milliseconds: _uiUpdateDelayMs), () {
      _applyDepthLimit();
      _updateSortedList();
      _hasPendingUIUpdate = false;
    });
  }

  /// 执行批量聚合
  void _performBatchAggregation() {
    if (_pendingBidUpdates.isEmpty && _pendingAskUpdates.isEmpty) return;

    // 批量应用所有待处理的更新
    _pendingBidUpdates.forEach((exchange, updates) {
      _originalBids[exchange] ??= {};
      updates.forEach((price, volume) {
        if (volume == 0) {
          _originalBids[exchange]!.remove(price);
        } else {
          _originalBids[exchange]![price] = volume;
        }
      });
    });

    _pendingAskUpdates.forEach((exchange, updates) {
      _originalAsks[exchange] ??= {};
      updates.forEach((price, volume) {
        if (volume == 0) {
          _originalAsks[exchange]!.remove(price);
        } else {
          _originalAsks[exchange]![price] = volume;
        }
      });
    });

    // 清空待处理更新
    _pendingBidUpdates.clear();
    _pendingAskUpdates.clear();

    // 重新聚合所有交易所数据
    _aggregateMultiExchangeData();

    // 安排UI更新
    _scheduleUIUpdate();
  }

  /// 清理过期的成交数据
  void _cleanupExpiredTradeData() {
    final now = DateTime.now().millisecondsSinceEpoch;
    final cutoffTime = now - _maxTradeDataAgeMs;

    // 移除过期的原始成交数据
    _rawTradeData.removeWhere((trade) => trade.timestamp < cutoffTime);
  }

  /// 计算挂单平均值并更新高亮状态
  void _calculateVolumeAveragesAndUpdateHighlight() {
    if (_depthDataMap.isEmpty) return;

    // 收集所有有效的买单和卖单挂单量
    final List<double> buyVolumes = [];
    final List<double> sellVolumes = [];

    for (final entry in _depthDataMap.values) {
      if (entry.buyVolume != null && entry.buyVolume! > 0) {
        buyVolumes.add(entry.buyVolume!);
      }
      if (entry.sellVolume != null && entry.sellVolume! > 0) {
        sellVolumes.add(entry.sellVolume!);
      }
    }

    // 计算平均值
    _buyVolumeAverage =
        buyVolumes.isNotEmpty
            ? buyVolumes.reduce((a, b) => a + b) / buyVolumes.length
            : 0.0;
    _sellVolumeAverage =
        sellVolumes.isNotEmpty
            ? sellVolumes.reduce((a, b) => a + b) / sellVolumes.length
            : 0.0;

    // 更新所有条目的高亮状态
    final Map<double, DepthEntry> updatedEntries = {};
    _depthDataMap.forEach((price, entry) {
      final bool shouldHighlight = _shouldHighlightEntry(entry);

      updatedEntries[price] = DepthEntry(
        price: entry.price,
        buyVolume: entry.buyVolume,
        sellVolume: entry.sellVolume,
        buyTradeVolume: entry.buyTradeVolume,
        sellTradeVolume: entry.sellTradeVolume,
        isHighlighted: shouldHighlight,
      );
    });

    // 更新深度数据映射
    _depthDataMap.clear();
    _depthDataMap.addAll(updatedEntries);

    // 更新排序列表并通知UI
    _updateSortedList();
  }

  /// 判断条目是否应该高亮（挂单量高于平均值）
  bool _shouldHighlightEntry(DepthEntry entry) {
    bool buyAboveAverage = false;
    bool sellAboveAverage = false;

    if (entry.buyVolume != null &&
        entry.buyVolume! > 0 &&
        _buyVolumeAverage > 0) {
      buyAboveAverage = entry.buyVolume! > _buyVolumeAverage * 2;
    }

    if (entry.sellVolume != null &&
        entry.sellVolume! > 0 &&
        _sellVolumeAverage > 0) {
      sellAboveAverage = entry.sellVolume! > _sellVolumeAverage * 2;
    }

    // 只要买单或卖单中有一个高于平均值就高亮
    return buyAboveAverage || sellAboveAverage;
  }

  /// 设置最大深度档位数
  void setMaxDepthLevels(int levels) {
    _maxDepthLevels = levels;
    if (_depthDataMap.isNotEmpty) {
      _applyDepthLimit();
      _updateSortedList();
    }
  }

  /// 设置价格聚合精度
  void setPriceAggregation(double aggregation) {
    if (aggregation <= 0) return;

    _priceAggregation = aggregation;
    // 重置上次聚合价格，触发下次成交数据处理时的清理
    _lastAggregatedPrice = null;

    if (_originalBids.isNotEmpty ||
        _originalAsks.isNotEmpty ||
        _rawTradeData.isNotEmpty) {
      // 开始重新聚合，缓存后续的WebSocket更新
      _startReaggregation();
    }
  }

  /// 处理成交数据，更新对应价位的成交量（优化版）
  void processTradeData(
    double price,
    double quantity,
    bool isBuy, {
    Exchange exchange = Exchange.binance,
  }) {
    final now = DateTime.now().millisecondsSinceEpoch;

    // 对OKX的成交数据进行单位转换（张数转币本位）
    double convertedQuantity = quantity;
    if (exchange == Exchange.okx) {
      final ctVal = _contractValues[Exchange.okx] ?? 1.0;
      convertedQuantity = quantity * ctVal;
    }

    // 保存原始成交数据（使用转换后的数量）
    _addRawTradeData(price, convertedQuantity, isBuy, now, exchange);

    // 进行价格聚合
    final aggregatedPrice = _getAggregatedPrice(price);

    // 检查聚合价格是否发生变化
    if (_lastAggregatedPrice != null &&
        _lastAggregatedPrice != aggregatedPrice) {
      // 聚合价格发生变化，清空其他价格的成交量数据
      _clearTradeVolumesExceptPrice(aggregatedPrice);
    }

    // 更新当前聚合价格的成交量（使用转换后的数量）
    if (isBuy) {
      _buyTradeVolumes[aggregatedPrice] =
          (_buyTradeVolumes[aggregatedPrice] ?? 0) + convertedQuantity;
    } else {
      _sellTradeVolumes[aggregatedPrice] =
          (_sellTradeVolumes[aggregatedPrice] ?? 0) + convertedQuantity;
    }

    // 更新上次聚合价格
    _lastAggregatedPrice = aggregatedPrice;

    // 更新对应价位的DepthEntry
    _updateDepthEntryWithTradeVolume(aggregatedPrice);

    // 使用延迟UI更新而不是立即更新
    _scheduleUIUpdate();
  }

  /// 添加原始成交数据
  void _addRawTradeData(
    double price,
    double quantity,
    bool isBuy,
    int timestamp,
    Exchange exchange,
  ) {
    // 添加新的原始成交数据
    _rawTradeData.add(
      RawTradeEntry(
        price: price,
        quantity: quantity,
        isBuy: isBuy,
        timestamp: timestamp,
        exchange: exchange,
      ),
    );

    // 控制原始数据量，移除最旧的数据
    if (_rawTradeData.length > _maxRawTradeCount) {
      _rawTradeData.removeAt(0);
    }

    // 移除过期数据
    final cutoffTime = timestamp - _maxTradeDataAgeMs;
    _rawTradeData.removeWhere((trade) => trade.timestamp < cutoffTime);
  }

  /// 更新DepthEntry的成交量信息
  void _updateDepthEntryWithTradeVolume(double aggregatedPrice) {
    final existing = _depthDataMap[aggregatedPrice];

    if (existing != null) {
      _depthDataMap[aggregatedPrice] = DepthEntry(
        price: existing.price,
        buyVolume: existing.buyVolume,
        sellVolume: existing.sellVolume,
        buyTradeVolume: _buyTradeVolumes[aggregatedPrice],
        sellTradeVolume: _sellTradeVolumes[aggregatedPrice],
        isHighlighted: existing.isHighlighted,
      );
    } else {
      // 如果该价位没有深度数据，创建一个只包含成交量的条目
      _depthDataMap[aggregatedPrice] = DepthEntry(
        price: aggregatedPrice,
        buyVolume: null,
        sellVolume: null,
        buyTradeVolume: _buyTradeVolumes[aggregatedPrice],
        sellTradeVolume: _sellTradeVolumes[aggregatedPrice],
        isHighlighted: false,
      );
    }
  }

  /// 开始重新聚合过程
  void _startReaggregation() async {
    // 设置重新聚合标志，暂停直接处理WebSocket更新
    _isReaggregating = true;
    _pendingUpdates.clear();

    // 基于原始数据重新聚合
    _reAggregateFromOriginalDataSync();

    // 重新聚合成交数据
    _reAggregateTradeData();

    // 应用缓存的WebSocket更新
    _applyPendingUpdates();

    // 恢复正常的WebSocket更新流程
    _isReaggregating = false;
  }

  /// 同步执行重新聚合（不更新UI）
  void _reAggregateFromOriginalDataSync() {
    bool hasData = false;
    for (final exchange in _enabledExchanges) {
      if ((_originalBids[exchange]?.isNotEmpty ?? false) ||
          (_originalAsks[exchange]?.isNotEmpty ?? false)) {
        hasData = true;
        break;
      }
    }

    if (!hasData) return;

    // 清空现有聚合数据
    _depthDataMap.clear();

    // 重新聚合原始数据
    _aggregateMultiExchangeData();

    // 应用档位限制并更新排序列表
    _applyDepthLimit();
    _updateSortedList();
  }

  /// 聚合多交易所原始深度数据
  void _aggregateMultiExchangeData() {
    // 聚合买单数据
    final Map<double, double> aggregatedBids = {};
    for (final exchange in _enabledExchanges) {
      final exchangeBids = _originalBids[exchange] ?? {};
      exchangeBids.forEach((price, volume) {
        final aggregatedPrice = _getAggregatedPrice(price);
        aggregatedBids[aggregatedPrice] =
            (aggregatedBids[aggregatedPrice] ?? 0) + volume;
      });
    }

    // 聚合卖单数据
    final Map<double, double> aggregatedAsks = {};
    for (final exchange in _enabledExchanges) {
      final exchangeAsks = _originalAsks[exchange] ?? {};
      exchangeAsks.forEach((price, volume) {
        final aggregatedPrice = _getAggregatedPrice(price);
        aggregatedAsks[aggregatedPrice] =
            (aggregatedAsks[aggregatedPrice] ?? 0) + volume;
      });
    }

    // 合并到深度数据Map
    final Set<double> allPrices = {
      ...aggregatedBids.keys,
      ...aggregatedAsks.keys,
    };

    for (final price in allPrices) {
      _depthDataMap[price] = DepthEntry(
        price: price,
        buyVolume: aggregatedBids[price],
        sellVolume: aggregatedAsks[price],
        buyTradeVolume: _buyTradeVolumes[price],
        sellTradeVolume: _sellTradeVolumes[price],
        isHighlighted: false,
      );
    }
  }

  /// 重新聚合成交数据
  void _reAggregateTradeData() {
    if (_rawTradeData.isEmpty) return;

    // 清空现有的成交量聚合数据
    _buyTradeVolumes.clear();
    _sellTradeVolumes.clear();

    // 重新按新精度聚合所有原始成交数据
    for (final trade in _rawTradeData) {
      final aggregatedPrice = _getAggregatedPrice(trade.price);

      if (trade.isBuy) {
        _buyTradeVolumes[aggregatedPrice] =
            (_buyTradeVolumes[aggregatedPrice] ?? 0) + trade.quantity;
      } else {
        _sellTradeVolumes[aggregatedPrice] =
            (_sellTradeVolumes[aggregatedPrice] ?? 0) + trade.quantity;
      }
    }

    // 更新所有受影响的DepthEntry
    final allAggregatedPrices = {
      ..._buyTradeVolumes.keys,
      ..._sellTradeVolumes.keys,
    };
    for (final price in allAggregatedPrices) {
      _updateDepthEntryWithTradeVolume(price);
    }
  }

  /// 计算聚合后的价格
  double _getAggregatedPrice(double originalPrice) {
    if (_priceAggregation <= 0) return originalPrice;

    // 按照聚合精度进行价格聚合
    // 使用向下取整的方式，确保价格聚合到正确的区间
    return (originalPrice / _priceAggregation).floor() * _priceAggregation;
  }

  /// 连接多个交易所的WebSocket接收实时深度更新
  void _connectWebSockets() {
    if (_symbol.isEmpty) return;

    for (final exchange in _enabledExchanges) {
      _connectExchangeWebSocket(exchange);
    }
  }

  /// 连接单个交易所的WebSocket
  void _connectExchangeWebSocket(Exchange exchange) {
    try {
      final config = ExchangeConfig.configs[exchange];
      if (config == null) return;

      final String wsUrl = config.getDepthWsUrl(_symbol);

      late WebsocketClient wsClient;
      wsClient = WebsocketClient(
        url: wsUrl,
        onConnected: () {
          // OKX需要发送订阅消息
          if (exchange == Exchange.okx) {
            _sendOkxSubscription(wsClient);
          }
        },
        onMessage: (message) {
          _handleDepthUpdate(message, exchange);
        },
        onDisconnected: () {},
        onError: (error) {},
      );

      // 设置WebSocket代理
      wsClient.setProxy('127.0.0.1:1087');

      _wsClients[exchange] = wsClient;
      wsClient.connect();

      // ignore: empty_catches
    } catch (e) {}
  }

  /// 发送OKX订阅消息
  void _sendOkxSubscription(WebsocketClient wsClient) {
    final formattedSymbol = ExchangeConfig.configs[Exchange.okx]!.formatSymbol(
      _symbol,
    );
    final subscribeMessage = {
      'op': 'subscribe',
      'args': [
        {'channel': 'books', 'instId': formattedSymbol},
      ],
    };

    wsClient.sendMessage(jsonEncode(subscribeMessage));
  }

  /// 处理WebSocket深度数据更新
  void _handleDepthUpdate(dynamic message, Exchange exchange) {
    try {
      final data = jsonDecode(message.toString());

      if (exchange == Exchange.binance) {
        _handleBinanceDepthUpdate(data, exchange);
      } else if (exchange == Exchange.okx) {
        _handleOkxDepthUpdate(data, exchange);
      }
    } catch (e) {
      // 忽略解析错误
    }
  }

  /// 处理币安深度数据更新（优化版）
  void _handleBinanceDepthUpdate(Map<String, dynamic> data, Exchange exchange) {
    if (data.containsKey('e') && data['e'] == 'depthUpdate') {
      // 如果正在重新聚合，将更新数据缓存到队列中
      if (_isReaggregating) {
        _cacheWebSocketUpdate(data, exchange);
        return;
      }

      // 初始化批量更新缓存
      _pendingBidUpdates[exchange] ??= {};
      _pendingAskUpdates[exchange] ??= {};

      // 处理买单更新 - 添加到批量更新缓存
      final List<List<dynamic>> bids = List<List<dynamic>>.from(
        data['b'] ?? [],
      );
      for (final bid in bids) {
        final double price = double.parse(bid[0]);
        final double volume = double.parse(bid[1]);
        _pendingBidUpdates[exchange]![price] = volume;
      }

      // 处理卖单更新 - 添加到批量更新缓存
      final List<List<dynamic>> asks = List<List<dynamic>>.from(
        data['a'] ?? [],
      );
      for (final ask in asks) {
        final double price = double.parse(ask[0]);
        final double volume = double.parse(ask[1]);
        _pendingAskUpdates[exchange]![price] = volume;
      }

      // 安排延迟聚合
      _scheduleAggregation();
    }
  }

  /// 处理OKX深度数据更新（优化版）
  void _handleOkxDepthUpdate(Map<String, dynamic> data, Exchange exchange) {
    // 检查是否是订阅确认消息
    if (data.containsKey('event')) {
      return;
    }

    // 检查是否有错误
    if (data.containsKey('code') && data['code'] != '0') {
      return;
    }

    // 处理深度数据
    if (data.containsKey('data') && data['data'] is List) {
      final List<dynamic> dataList = data['data'];

      // 如果正在重新聚合，将更新数据缓存到队列中
      if (_isReaggregating) {
        _cacheWebSocketUpdate(data, exchange);
        return;
      }

      // 初始化批量更新缓存
      _pendingBidUpdates[exchange] ??= {};
      _pendingAskUpdates[exchange] ??= {};

      for (final item in dataList) {
        if (item is Map<String, dynamic> &&
            item.containsKey('bids') &&
            item.containsKey('asks')) {
          // 获取OKX合约面值，用于单位转换
          final ctVal = _contractValues[Exchange.okx] ?? 1.0;

          // 处理买单更新 - 添加到批量更新缓存，张数转换为币本位
          final List<List<dynamic>> bids = List<List<dynamic>>.from(
            item['bids'] ?? [],
          );
          for (final bid in bids) {
            final double price = double.parse(bid[0]);
            final double volume = double.parse(bid[1]) * ctVal; // 张数 * 合约面值
            _pendingBidUpdates[exchange]![price] = volume;
          }

          // 处理卖单更新 - 添加到批量更新缓存，张数转换为币本位
          final List<List<dynamic>> asks = List<List<dynamic>>.from(
            item['asks'] ?? [],
          );
          for (final ask in asks) {
            final double price = double.parse(ask[0]);
            final double volume = double.parse(ask[1]) * ctVal; // 张数 * 合约面值
            _pendingAskUpdates[exchange]![price] = volume;
          }
        }
      }

      // 安排延迟聚合
      _scheduleAggregation();
    }
  }

  /// 缓存WebSocket更新数据
  void _cacheWebSocketUpdate(Map<String, dynamic> data, Exchange exchange) {
    if (exchange == Exchange.binance) {
      _cacheBinanceUpdate(data, exchange);
    } else if (exchange == Exchange.okx) {
      _cacheOkxUpdate(data, exchange);
    }
  }

  /// 缓存币安更新数据
  void _cacheBinanceUpdate(Map<String, dynamic> data, Exchange exchange) {
    // 处理买单更新
    final List<List<dynamic>> bids = List<List<dynamic>>.from(data['b'] ?? []);
    for (final bid in bids) {
      final double price = double.parse(bid[0]);
      final double volume = double.parse(bid[1]);

      _pendingUpdates.add({
        'price': price,
        'volume': volume,
        'isBid': true,
        'exchange': exchange,
      });
    }

    // 处理卖单更新
    final List<List<dynamic>> asks = List<List<dynamic>>.from(data['a'] ?? []);
    for (final ask in asks) {
      final double price = double.parse(ask[0]);
      final double volume = double.parse(ask[1]);

      _pendingUpdates.add({
        'price': price,
        'volume': volume,
        'isBid': false,
        'exchange': exchange,
      });
    }
  }

  /// 缓存OKX更新数据
  void _cacheOkxUpdate(Map<String, dynamic> data, Exchange exchange) {
    if (data.containsKey('data') && data['data'] is List) {
      final List<dynamic> dataList = data['data'];
      // 获取OKX合约面值，用于单位转换
      final ctVal = _contractValues[Exchange.okx] ?? 1.0;

      for (final item in dataList) {
        if (item is Map<String, dynamic> &&
            item.containsKey('bids') &&
            item.containsKey('asks')) {
          // 处理买单更新 - 张数转换为币本位
          final List<List<dynamic>> bids = List<List<dynamic>>.from(
            item['bids'] ?? [],
          );
          for (final bid in bids) {
            final double price = double.parse(bid[0]);
            final double volume = double.parse(bid[1]) * ctVal; // 张数 * 合约面值

            _pendingUpdates.add({
              'price': price,
              'volume': volume,
              'isBid': true,
              'exchange': exchange,
            });
          }

          // 处理卖单更新 - 张数转换为币本位
          final List<List<dynamic>> asks = List<List<dynamic>>.from(
            item['asks'] ?? [],
          );
          for (final ask in asks) {
            final double price = double.parse(ask[0]);
            final double volume = double.parse(ask[1]) * ctVal; // 张数 * 合约面值

            _pendingUpdates.add({
              'price': price,
              'volume': volume,
              'isBid': false,
              'exchange': exchange,
            });
          }
        }
      }
    }
  }

  /// 应用档位限制
  void _applyDepthLimit() {
    if (_maxDepthLevels <= 0 || _depthDataMap.isEmpty) return;

    // 分别收集买单和卖单
    final List<DepthEntry> buyEntries = [];
    final List<DepthEntry> sellEntries = [];

    for (final entry in _depthDataMap.values) {
      if (entry.buyVolume != null && entry.buyVolume! > 0) {
        buyEntries.add(entry);
      }
      if (entry.sellVolume != null && entry.sellVolume! > 0) {
        sellEntries.add(entry);
      }
    }

    // 按价格排序
    buyEntries.sort((a, b) => b.price.compareTo(a.price)); // 买单从高到低
    sellEntries.sort((a, b) => a.price.compareTo(b.price)); // 卖单从低到高

    // 保留指定档位数量
    final Set<double> pricesToKeep = {};

    // 保留买单前N档
    for (int i = 0; i < math.min(_maxDepthLevels, buyEntries.length); i++) {
      pricesToKeep.add(buyEntries[i].price);
    }

    // 保留卖单前N档
    for (int i = 0; i < math.min(_maxDepthLevels, sellEntries.length); i++) {
      pricesToKeep.add(sellEntries[i].price);
    }

    // 移除不在保留列表中的价格档位
    _depthDataMap.removeWhere((price, _) => !pricesToKeep.contains(price));
  }

  /// 更新排序列表并通知数据变化
  void _updateSortedList() {
    _sortedDepthData = _depthDataMap.values.toList();
    _sortedDepthData.sort((a, b) => b.price.compareTo(a.price)); // 按价格降序排序

    // 通知数据更新
    _dataController.add(_sortedDepthData);
  }

  /// 断开所有WebSocket连接
  void _disconnectWebSockets() {
    for (final wsClient in _wsClients.values) {
      wsClient.disconnect();
    }
    _wsClients.clear();
  }

  /// 设置状态
  void _setStatus(DepthDataStatus status) {
    _status = status;
    _statusController.add(_status);
  }

  /// 释放资源
  void dispose() {
    // 取消定期清理任务
    _cleanupTimer?.cancel();
    _cleanupTimer = null;

    // 取消平均值计算定时器
    _averageCalculationTimer?.cancel();
    _averageCalculationTimer = null;

    // 取消优化相关定时器
    _aggregationTimer?.cancel();
    _aggregationTimer = null;

    _uiUpdateTimer?.cancel();
    _uiUpdateTimer = null;

    // 断开WebSocket连接
    _disconnectWebSockets();

    // 清理所有数据
    _depthDataMap.clear();
    _sortedDepthData.clear();
    _originalBids.clear();
    _originalAsks.clear();
    _rawTradeData.clear();
    _buyTradeVolumes.clear();
    _sellTradeVolumes.clear();
    _pendingUpdates.clear();
    _pendingBidUpdates.clear();
    _pendingAskUpdates.clear();

    // 关闭流控制器
    _statusController.close();
    _dataController.close();
  }

  /// 应用缓存的WebSocket更新
  void _applyPendingUpdates() {
    if (_pendingUpdates.isEmpty) return;

    for (final update in _pendingUpdates) {
      _processCachedUpdate(update);
    }

    // 清空缓存
    _pendingUpdates.clear();

    // 重新应用档位限制并更新UI
    _applyDepthLimit();
    _updateSortedList();
  }

  /// 处理缓存的单个更新
  void _processCachedUpdate(Map<String, dynamic> update) {
    final double price = update['price'];
    final double volume = update['volume'];
    final bool isBid = update['isBid'];
    final Exchange exchange = update['exchange'];

    // 确保交易所数据映射存在
    _originalBids[exchange] ??= {};
    _originalAsks[exchange] ??= {};

    // 更新原始数据
    if (isBid) {
      if (volume == 0) {
        _originalBids[exchange]!.remove(price);
      } else {
        _originalBids[exchange]![price] = volume;
      }
    } else {
      if (volume == 0) {
        _originalAsks[exchange]!.remove(price);
      } else {
        _originalAsks[exchange]![price] = volume;
      }
    }
  }

  /// 初始化并获取深度数据
  Future<void> initializeDepthData(String symbol) async {
    if (symbol.isEmpty) return;

    _symbol = symbol;
    _setStatus(DepthDataStatus.loading);

    try {
      // 先断开可能存在的WebSocket连接
      _disconnectWebSockets();

      // 取消所有待处理的定时器
      _aggregationTimer?.cancel();
      _aggregationTimer = null;
      _uiUpdateTimer?.cancel();
      _uiUpdateTimer = null;

      // 重置状态标志
      _hasPendingAggregation = false;
      _hasPendingUIUpdate = false;
      _isReaggregating = false;

      // 清理所有数据（切换交易对时）
      _depthDataMap.clear();
      _sortedDepthData.clear();
      _originalBids.clear();
      _originalAsks.clear();
      _rawTradeData.clear();
      _buyTradeVolumes.clear();
      _sellTradeVolumes.clear();
      _pendingUpdates.clear();
      _pendingBidUpdates.clear();
      _pendingAskUpdates.clear();

      // 重置价格相关状态
      _lastAggregatedPrice = null;

      // 1. 首先获取所有交易所的合约信息（特别是OKX的ctVal）
      await _fetchAllContractInfo();

      // 2. 然后获取所有启用交易所的全量深度数据
      await _fetchAllExchangesDepthData();

      // 3. 最后连接WebSocket接收实时更新
      _connectWebSockets();

      _setStatus(DepthDataStatus.loaded);
    } catch (e) {
      _errorMessage = '初始化深度数据失败: $e';
      _setStatus(DepthDataStatus.error);
    }
  }

  /// 获取所有启用交易所的合约信息
  Future<void> _fetchAllContractInfo() async {
    // 并行获取所有交易所的合约信息
    final List<Future<void>> futures = [];
    for (final exchange in _enabledExchanges) {
      futures.add(_fetchExchangeContractInfo(exchange));
    }

    await Future.wait(futures);
  }

  /// 获取单个交易所的合约信息
  Future<void> _fetchExchangeContractInfo(Exchange exchange) async {
    try {
      final config = ExchangeConfig.configs[exchange];
      if (config == null) return;

      if (exchange == Exchange.binance) {
        // 币安期货合约面值固定为1，不需要API请求
        _contractValues[exchange] = 1.0;
      } else if (exchange == Exchange.okx) {
        await _fetchOkxContractInfo();
      }
    } catch (e) {
      // 如果获取失败，使用默认值1.0
      _contractValues[exchange] = 1.0;
    }
  }

  /// 获取OKX合约信息
  Future<void> _fetchOkxContractInfo() async {
    try {
      final formattedSymbol = ExchangeConfig.configs[Exchange.okx]!
          .formatSymbol(_symbol);
      final url = 'https://www.okx.com/api/v5/public/instruments';
      final queryParameters = {
        'instType': 'SWAP',
        'instId': formattedSymbol, // 如: BTC-USDT-SWAP
      };

      final response = await _requestClient.get(
        url,
        queryParameters: queryParameters,
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData =
            response.data as Map<String, dynamic>;

        if (responseData.containsKey('data') && responseData['data'] is List) {
          final List<dynamic> dataList = responseData['data'];
          if (dataList.isNotEmpty) {
            final Map<String, dynamic> contractInfo = dataList[0];
            if (contractInfo.containsKey('ctVal')) {
              final ctVal = double.parse(contractInfo['ctVal'].toString());
              _contractValues[Exchange.okx] = ctVal;
              return;
            }
          }
        }
      }

      // 如果解析失败，使用默认值
      _contractValues[Exchange.okx] = 1.0;
    } catch (e) {
      // 如果请求失败，使用默认值
      _contractValues[Exchange.okx] = 1.0;
    }
  }

  /// 获取所有启用交易所的全量深度数据
  Future<void> _fetchAllExchangesDepthData() async {
    // 并行获取所有交易所的深度数据
    final List<Future<void>> futures = [];
    for (final exchange in _enabledExchanges) {
      futures.add(_fetchExchangeDepthData(exchange));
    }

    await Future.wait(futures);

    // 聚合所有交易所的数据
    _aggregateMultiExchangeData();

    // 应用档位限制并更新排序列表
    _applyDepthLimit();
    _updateSortedList();
  }

  /// 获取单个交易所的深度数据
  Future<void> _fetchExchangeDepthData(Exchange exchange) async {
    try {
      final config = ExchangeConfig.configs[exchange];
      if (config == null) return;

      final String formattedSymbol = config.formatSymbol(_symbol);
      final String apiUrl = '${config.apiBaseUrl}${config.depthEndpoint}';

      Map<String, dynamic> queryParameters;
      if (exchange == Exchange.binance) {
        queryParameters = {'symbol': formattedSymbol, 'limit': 1000};
      } else if (exchange == Exchange.okx) {
        queryParameters = {
          'instId': formattedSymbol,
          'sz': '400', // OKX最大400档
        };
      } else {
        return;
      }

      final response = await _requestClient.get(
        apiUrl,
        queryParameters: queryParameters,
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> depthData =
            response.data as Map<String, dynamic>;

        // 确保交易所数据映射存在
        _originalBids[exchange] ??= {};
        _originalAsks[exchange] ??= {};

        if (exchange == Exchange.binance) {
          _parseBinanceDepthData(depthData, exchange);
        } else if (exchange == Exchange.okx) {
          _parseOkxDepthData(depthData, exchange);
        }
      } else {
        throw Exception('${exchange.displayName} HTTP ${response.statusCode}');
      }
    } catch (e) {
      // 不抛出异常，允许其他交易所继续获取数据
    }
  }

  /// 解析币安深度数据
  void _parseBinanceDepthData(
    Map<String, dynamic> depthData,
    Exchange exchange,
  ) {
    // 解析买单数据
    final List<List<dynamic>> bids = List<List<dynamic>>.from(
      depthData['bids'] ?? [],
    );
    for (final bid in bids) {
      final double price = double.parse(bid[0]);
      final double volume = double.parse(bid[1]);
      _originalBids[exchange]![price] = volume;
    }

    // 解析卖单数据
    final List<List<dynamic>> asks = List<List<dynamic>>.from(
      depthData['asks'] ?? [],
    );
    for (final ask in asks) {
      final double price = double.parse(ask[0]);
      final double volume = double.parse(ask[1]);
      _originalAsks[exchange]![price] = volume;
    }
  }

  /// 解析OKX深度数据
  void _parseOkxDepthData(Map<String, dynamic> depthData, Exchange exchange) {
    if (depthData.containsKey('data') && depthData['data'] is List) {
      final List<dynamic> dataList = depthData['data'];
      if (dataList.isNotEmpty) {
        final Map<String, dynamic> data = dataList[0];

        // 获取OKX合约面值，用于单位转换
        final ctVal = _contractValues[Exchange.okx] ?? 1.0;

        // 解析买单数据 - 张数转换为币本位
        final List<List<dynamic>> bids = List<List<dynamic>>.from(
          data['bids'] ?? [],
        );
        for (final bid in bids) {
          final double price = double.parse(bid[0]);
          final double volume = double.parse(bid[1]) * ctVal; // 张数 * 合约面值
          _originalBids[exchange]![price] = volume;
        }

        // 解析卖单数据 - 张数转换为币本位
        final List<List<dynamic>> asks = List<List<dynamic>>.from(
          data['asks'] ?? [],
        );
        for (final ask in asks) {
          final double price = double.parse(ask[0]);
          final double volume = double.parse(ask[1]) * ctVal; // 张数 * 合约面值
          _originalAsks[exchange]![price] = volume;
        }
      }
    }
  }

  /// 获取原始成交数据统计信息（用于调试）
  Map<String, dynamic> getTradeDataStats() {
    final now = DateTime.now().millisecondsSinceEpoch;
    final recentCount =
        _rawTradeData
            .where(
              (trade) => now - trade.timestamp < 300000, // 最近5分钟
            )
            .length;

    return {
      'total_raw_trades': _rawTradeData.length,
      'recent_trades_5min': recentCount,
      'buy_aggregated_levels': _buyTradeVolumes.length,
      'sell_aggregated_levels': _sellTradeVolumes.length,
      'max_raw_trade_count': _maxRawTradeCount,
      'max_trade_age_hours': _maxTradeDataAgeMs / 3600000,
    };
  }

  /// 获取性能优化统计信息
  Map<String, dynamic> getPerformanceStats() {
    int totalPendingUpdates = 0;
    _pendingBidUpdates.forEach((exchange, updates) {
      totalPendingUpdates += updates.length;
    });
    _pendingAskUpdates.forEach((exchange, updates) {
      totalPendingUpdates += updates.length;
    });

    return {
      'enabled_exchanges': _enabledExchanges.map((e) => e.displayName).toList(),
      'aggregation_delay_ms': _aggregationDelayMs,
      'ui_update_delay_ms': _uiUpdateDelayMs,
      'pending_aggregation': _hasPendingAggregation,
      'pending_ui_update': _hasPendingUIUpdate,
      'pending_batch_updates': totalPendingUpdates,
      'depth_data_levels': _depthDataMap.length,
      'websocket_connections': _wsClients.length,
      'active_exchanges': _wsClients.keys.map((e) => e.displayName).toList(),
    };
  }

  /// 清空除指定价格外的所有成交量数据
  void _clearTradeVolumesExceptPrice(double keepPrice) {
    // 保留指定价格的成交量
    final keepBuyVolume = _buyTradeVolumes[keepPrice];
    final keepSellVolume = _sellTradeVolumes[keepPrice];

    // 清空所有成交量数据
    _buyTradeVolumes.clear();
    _sellTradeVolumes.clear();

    // 恢复指定价格的成交量
    if (keepBuyVolume != null) {
      _buyTradeVolumes[keepPrice] = keepBuyVolume;
    }
    if (keepSellVolume != null) {
      _sellTradeVolumes[keepPrice] = keepSellVolume;
    }

    // 过滤原始成交数据，只保留指定聚合价格的
    _rawTradeData.removeWhere(
      (trade) => _getAggregatedPrice(trade.price) != keepPrice,
    );

    // 更新所有深度条目的成交量数据
    final updatedEntries = <double, DepthEntry>{};
    _depthDataMap.forEach((price, entry) {
      updatedEntries[price] = DepthEntry(
        price: entry.price,
        buyVolume: entry.buyVolume,
        sellVolume: entry.sellVolume,
        buyTradeVolume: _buyTradeVolumes[price],
        sellTradeVolume: _sellTradeVolumes[price],
        isHighlighted: entry.isHighlighted,
      );
    });

    // 更新深度数据映射
    _depthDataMap.clear();
    _depthDataMap.addAll(updatedEntries);
  }

  /// 获取实例标识符
  String get instanceId => _instanceId;
}
