final class ApiRoute {
  //单例模式
  static final ApiRoute instance = ApiRoute._();
  ApiRoute._();

  static const isDev = true;

  //基础URL
  static const String devBaseUrl = 'http://192.168.31.27:9501';
  //生产环境URL
  static const String prodBaseUrl = 'https://api.algoquant.org';

  //网页端打开域名
  static const String webUrl = "https://algoquant.org";

  //websocket url
  static const String wsUrl = "ws://192.168.31.27:9502/ws";

  static const String userInfo = "/api/user/info"; // 用户信息接口

  static const String refreshToken = "/api/user/refresh/token"; // 刷新令牌接口

  // 验证码相关接口
  static const String sendEmailCode = "/api/user/sendEmailCode"; // 发送邮箱验证码
  static const String sendSmsCode = "/api/user/sendSmsCode"; // 发送短信验证码

  // 登录相关接口
  static const String login = "/api/user/login"; // 用户登录
  static const String verifyCodeLogin = "/api/user/verifyCodeLogin"; // 验证码登录

  // 注册相关接口
  static const String registerByEmail = "/api/user/registerByEmail"; // 邮箱注册
  static const String registerByPhone = "/api/user/registerByPhone"; // 手机注册

  // OAuth登录接口
  static const String oauthLogin = "/api/user/oauthLogin"; // 第三方登录

  // 用户相关接口
  static const String reportIpLocation = "/api/user/reportIpLocation"; // 上报IP位置

  // 首页相关接口
  static const String homeMainTab = "/api/v1/home/<USER>"; // 首页主标签数据

  // 市场行情相关接口
  static const String marketCurrency = "/api/v1/market/currency"; // 获取币种数据
  static const String marketUpdateCurrency = "/api/v1/market/update-currency"; // 增量更新币种数据
  static const String marketCurrencyCate = "/api/v1/market/currency-cate"; // 币种栏目数据
  static const String marketType = "/api/v1/market/market-type"; // 交易市场类型
  static const String usdtRate = "/api/v1/market/usdt-rate"; // USDT汇率数据

  // 热门话题相关接口
  static const String hotTopicList = "/api/hottopic/hotTopic/list"; // 热门话题列表

  // 社区动态相关接口
  static const String dynamicsList = "/api/dynamics/dynamics/list"; // 社区动态列表

  // 分类栏目相关接口
  static const String categoryList = "/api/category/category/list"; // 分类栏目列表

  // 公告相关接口
  static const String noticeList = "/api/notice/notice/list"; // 公告列表

  // 文章相关接口
  static const String articleList = "/api/article/article/list"; // 文章列表

  // AI推荐相关接口
  static const String aiRecommend = "/api/v1/common/ai-recommend"; // AI推荐接口

  // 交易事件相关接口
  static const String eventData = "/api/v1/common/event-data"; // 交易事件数据

  // 资产相关接口
  static const String assetBalance = "/api/v1/asset/balance"; // 用户资产余额
  static const String assetCurrency = "/api/v1/asset/currency"; // 获取特定币种资产

  // 现货交易相关接口
  static const String spotTradeConfig = "/api/v1/spot/trade-config"; // 现货交易配置
  static const String spotPlaceOrder = "/api/v1/spot/place-order"; // 现货下单
  static const String spotCommissionPlaceOrder = "/api/v1/spot/commission/place-order"; // 现货委托下单
}
