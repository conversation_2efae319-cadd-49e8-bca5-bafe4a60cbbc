import 'dart:io';
import 'dart:ui' as ui;
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../config/api_route.dart';
import 'package:qubic_exchange/core/models/request/api_response.dart';

/// 使用Dio的HTTP请求类
class DioRequest {
  // 私有构造函数，防止外部实例化
  DioRequest._();

  // 单例实例
  static final DioRequest _instance = DioRequest._();

  // 获取单例实例
  static DioRequest get instance => _instance;

  // API基础URL
  static const String _baseUrl =
      ApiRoute.isDev ? ApiRoute.devBaseUrl : ApiRoute.prodBaseUrl;

  // 请求超时时间（秒）
  static const int _timeoutSeconds = 10;

  // 设备信息
  static String? _deviceInfo;

  // Dio实例
  final Dio _dio = Dio(
    BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: Duration(seconds: _timeoutSeconds),
      receiveTimeout: Duration(seconds: _timeoutSeconds),
      contentType: 'application/json',
      responseType: ResponseType.json,
    ),
  );

  // 全局上下文，用于获取Provider
  static BuildContext? _globalContext;

  // 全局认证提供者
  static AuthProvider? _authProvider;

  // 设置全局上下文
  static void setContext(BuildContext context) {
    _globalContext = context;
    _initAuthProvider();
  }

  // 设置全局认证提供者
  static void setAuthProvider(AuthProvider authProvider) {
    _authProvider = authProvider;
  }

  // 初始化认证提供者
  static void _initAuthProvider() {
    if (_globalContext != null && _authProvider == null) {
      try {
        _authProvider = Provider.of<AuthProvider>(
          _globalContext!,
          listen: false,
        );
      } catch (e) {
        debugPrint('初始化AuthProvider失败: $e');
      }
    }
  }

  // 初始化设备信息
  static void initDeviceInfo() {
    if (_deviceInfo != null) return;

    try {
      // Web平台直接设置为Web
      if (kIsWeb) {
        _deviceInfo = 'Web Browser';
        return;
      }

      // 桌面和移动平台获取具体设备信息
      if (Platform.isAndroid) {
        // Android设备信息
        final version = Platform.operatingSystemVersion;
        _deviceInfo = 'Android Device $version';
      } else if (Platform.isIOS) {
        // iOS设备信息 - 通过屏幕尺寸和系统版本判断设备类型
        final version = Platform.operatingSystemVersion;
        _deviceInfo = _getIOSDeviceInfo(version);
      } else if (Platform.isMacOS) {
        final version = Platform.operatingSystemVersion;
        _deviceInfo = 'macOS $version';
      } else if (Platform.isWindows) {
        final version = Platform.operatingSystemVersion;
        _deviceInfo = 'Windows $version';
      } else if (Platform.isLinux) {
        final version = Platform.operatingSystemVersion;
        _deviceInfo = 'Linux $version';
      } else {
        _deviceInfo =
            'Unknown ${Platform.operatingSystem} ${Platform.operatingSystemVersion}';
      }
    } catch (e) {
      debugPrint('获取设备信息失败: $e');
      // Web平台或获取失败时的默认值
      _deviceInfo = kIsWeb ? 'Web Browser' : 'Unknown Device';
    }
  }

  // 获取设备信息
  static String get deviceInfo {
    if (_deviceInfo == null) {
      initDeviceInfo();
    }
    return _deviceInfo ?? 'Unknown Device';
  }

  // 获取iOS设备详细信息
  static String _getIOSDeviceInfo(String version) {
    try {
      // 通过dart:ui获取屏幕信息
      final window = ui.PlatformDispatcher.instance.views.first;
      final size = window.physicalSize;
      final devicePixelRatio = window.devicePixelRatio;

      // 计算逻辑分辨率
      final logicalWidth = size.width / devicePixelRatio;
      final logicalHeight = size.height / devicePixelRatio;

      // 根据屏幕尺寸判断设备类型
      if (logicalWidth >= 1024 || logicalHeight >= 1024) {
        // iPad系列
        if (logicalWidth >= 1366 || logicalHeight >= 1366) {
          return 'iPad Pro $version';
        } else if (logicalWidth >= 1080 || logicalHeight >= 1080) {
          return 'iPad Air $version';
        } else {
          return 'iPad $version';
        }
      } else {
        // iPhone系列
        if (logicalHeight >= 926) {
          return 'iPhone Pro Max $version';
        } else if (logicalHeight >= 896) {
          return 'iPhone Plus/Max $version';
        } else if (logicalHeight >= 844) {
          return 'iPhone Pro $version';
        } else if (logicalHeight >= 812) {
          return 'iPhone X/11/12/13/14 $version';
        } else if (logicalHeight >= 736) {
          return 'iPhone Plus $version';
        } else if (logicalHeight >= 667) {
          return 'iPhone 6/7/8 $version';
        } else {
          return 'iPhone SE/Mini $version';
        }
      }
    } catch (e) {
      debugPrint('获取iOS设备详细信息失败: $e');
      return 'iOS Device $version';
    }
  }

  // 获取认证提供者
  static AuthProvider? _getAuthProvider() {
    if (_authProvider != null) {
      return _authProvider;
    }

    if (_globalContext != null) {
      try {
        return Provider.of<AuthProvider>(_globalContext!, listen: false);
      } catch (e) {
        debugPrint('获取AuthProvider失败: $e');
        return null;
      }
    }
    return null;
  }

  /// 构建请求头
  Map<String, dynamic> _buildHeaders({bool requireAuth = false}) {
    // 获取当前时间戳
    final requestTime = DateTime.now().millisecond;

    // 如果设备信息为空，初始化它
    if (_deviceInfo == null) {
      initDeviceInfo();
    }

    // 基础头部
    final headers = <String, dynamic>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Request-Time': requestTime, // 添加请求时间
      'X-Device-Info': _deviceInfo ?? 'Unknown Device', // 添加设备信息
      'User-Agent': 'cpx_exchange/${_deviceInfo ?? 'Unknown'}', // 添加User-Agent
    };
    // 如果需要认证，添加认证令牌
    if (requireAuth) {
      final authProvider = _getAuthProvider();
      final accessToken = authProvider?.accessToken;
      if (accessToken != null) {
        headers['Authorization'] = 'Bearer $accessToken';
      } else {
        // 如果需要认证但没有令牌，抛出异常
        throw ApiException('未登录或会话已过期', 401);
      }
    }
    return headers;
  }

  /// 处理API响应
  ApiResponse<T> _handleResponse<T>(
    Response response,
    T Function(dynamic)? dataConverter,
  ) {
    try {
      final data = response.data;

      if (response.statusCode != null &&
          response.statusCode! >= 200 &&
          response.statusCode! < 300) {
        bool success = true;
        String message = '请求成功';
        dynamic responseData;

        if (data is Map<String, dynamic>) {
          if (data.containsKey('code')) {
            success = data['code'] == 200 || data['code'] == 0;
            message = data['message'] ?? data['msg'] ?? '请求成功';
            responseData = data['data'];
          } else if (data.containsKey('success')) {
            success = data['success'] == true;
            message = data['message'] ?? '请求成功';
            responseData = data['data'];
          } else {
            message = data['message'] ?? '请求成功';
            responseData = data;
          }
        } else {
          responseData = data;
        }

        return ApiResponse<T>(
          success: success,
          message: message,
          data:
              dataConverter != null && responseData != null
                  ? dataConverter(responseData)
                  : responseData as T?,
          statusCode: response.statusCode ?? 200,
        );
      } else {
        // 错误响应
        String errorMessage = '请求失败';
        if (data is Map<String, dynamic>) {
          errorMessage = data['message'] ?? data['msg'] ?? errorMessage;
        }
        return ApiResponse<T>(
          success: false,
          message: errorMessage,
          statusCode: response.statusCode ?? 500,
        );
      }
    } catch (e) {
      debugPrint('响应处理错误: $e');
      return ApiResponse<T>(
        success: false,
        message: '响应解析错误: ${e.toString()}',
        statusCode: response.statusCode ?? 500,
      );
    }
  }

  /// GET请求
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParams,
    bool requireAuth = false,
    T Function(dynamic)? dataConverter,
  }) async {
    try {
      // 构建请求选项
      final options = Options(headers: _buildHeaders(requireAuth: requireAuth));

      // 发送请求
      final response = await _dio.get(
        endpoint,
        queryParameters: queryParams,
        options: options,
      );

      // 处理响应
      return _handleResponse<T>(response, dataConverter);
    } on DioException catch (e) {
      debugPrint('Dio错误: ${e.message}');
      return ApiResponse<T>(
        success: false,
        message: e.message ?? '请求失败',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      debugPrint('请求错误: $e');
      return ApiResponse<T>(
        success: false,
        message: '请求错误: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  /// POST请求
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    bool requireAuth = false,
    T Function(dynamic)? dataConverter,
  }) async {
    try {
      // 构建请求选项
      final options = Options(headers: _buildHeaders(requireAuth: requireAuth));
      // 发送请求
      final response = await _dio.post(endpoint, data: body, options: options);

      // 处理响应
      return _handleResponse<T>(response, dataConverter);
    } on DioException catch (e) {
      debugPrint('Dio错误: ${e.message}');
      return ApiResponse<T>(
        success: false,
        message: e.message ?? '请求失败',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      debugPrint('请求错误: $e');
      return ApiResponse<T>(
        success: false,
        message: '请求错误: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  /// PUT请求
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    bool requireAuth = false,
    T Function(dynamic)? dataConverter,
  }) async {
    try {
      // 构建请求选项
      final options = Options(headers: _buildHeaders(requireAuth: requireAuth));

      // 发送请求
      final response = await _dio.put(endpoint, data: body, options: options);

      // 处理响应
      return _handleResponse<T>(response, dataConverter);
    } on DioException catch (e) {
      debugPrint('Dio错误: ${e.message}');
      return ApiResponse<T>(
        success: false,
        message: e.message ?? '请求失败',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      debugPrint('请求错误: $e');
      return ApiResponse<T>(
        success: false,
        message: '请求错误: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  /// DELETE请求
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    bool requireAuth = false,
    T Function(dynamic)? dataConverter,
  }) async {
    try {
      // 构建请求选项
      final options = Options(headers: _buildHeaders(requireAuth: requireAuth));

      // 发送请求
      final response = await _dio.delete(
        endpoint,
        data: body,
        options: options,
      );

      // 处理响应
      return _handleResponse<T>(response, dataConverter);
    } on DioException catch (e) {
      debugPrint('Dio错误: ${e.message}');
      return ApiResponse<T>(
        success: false,
        message: e.message ?? '请求失败',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      debugPrint('请求错误: $e');
      return ApiResponse<T>(
        success: false,
        message: '请求错误: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  /// 上传文件
  Future<ApiResponse<T>> uploadFile<T>(
    String endpoint, {
    required dynamic file, // 改为dynamic以支持Web平台的不同文件类型
    required String fieldName,
    Map<String, dynamic>? fields,
    bool requireAuth = false,
    T Function(dynamic)? dataConverter,
  }) async {
    try {
      // Web平台暂不支持文件上传
      if (kIsWeb) {
        return ApiResponse<T>(
          success: false,
          message: 'Web平台暂不支持文件上传功能',
          statusCode: 501,
        );
      }

      // 构建请求选项
      final options = Options(headers: _buildHeaders(requireAuth: requireAuth));

      // 创建FormData
      final formData = FormData();

      // 添加文件 - 仅在非Web平台
      if (file is File) {
        formData.files.add(
          MapEntry(fieldName, await MultipartFile.fromFile(file.path)),
        );
      } else {
        return ApiResponse<T>(
          success: false,
          message: '无效的文件类型',
          statusCode: 400,
        );
      }

      // 添加其他字段
      if (fields != null) {
        fields.forEach((key, value) {
          formData.fields.add(MapEntry(key, value.toString()));
        });
      }

      // 发送请求
      final response = await _dio.post(
        endpoint,
        data: formData,
        options: options,
      );

      // 处理响应
      return _handleResponse<T>(response, dataConverter);
    } on DioException catch (e) {
      debugPrint('Dio错误: ${e.message}');
      return ApiResponse<T>(
        success: false,
        message: e.message ?? '请求失败',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      debugPrint('请求错误: $e');
      return ApiResponse<T>(
        success: false,
        message: '请求错误: ${e.toString()}',
        statusCode: 500,
      );
    }
  }
}
