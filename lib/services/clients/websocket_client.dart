import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart'; // For kIsWeb
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

typedef OnConnectedCallback = void Function();
typedef OnMessageCallback = void Function(dynamic message);
typedef OnDisconnectedCallback = void Function();
typedef OnErrorCallback = void Function(dynamic error);

class WebsocketClient {
  final String url;
  final OnConnectedCallback? onConnected;
  final OnMessageCallback? onMessage;
  final OnDisconnectedCallback? onDisconnected;
  final OnErrorCallback? onError;

  WebSocketChannel? _channel;
  StreamSubscription? _streamSubscription;
  String? _proxyAddress; // e.g., '127.0.0.1:1087'
  bool _isConnected = false;
  Timer? _reconnectTimer;
  bool _manuallyClosed = false;
  HttpClient? _customHttpClient;

  bool get isConnected => _isConnected;

  WebsocketClient({
    required this.url,
    this.onConnected,
    this.onMessage,
    this.onDisconnected,
    this.onError,
  });

  void setProxy(String? proxyAddress) {
    if (kIsWeb && proxyAddress != null && proxyAddress.isNotEmpty) {
      debugPrint(
        'Warning: WebSocket Proxy setting is not directly supported for web platform. Relying on browser/system settings for WebSocket handshake via HTTP.',
      );
    }
    _proxyAddress = proxyAddress;
  }

  Future<void> connect() async {
    _manuallyClosed = false;
    if (_isConnected) {
      debugPrint('WebSocket already connected.');
      return;
    }
    debugPrint('Connecting to WebSocket: $url');

    try {
      if (kIsWeb) {
        _channel = WebSocketChannel.connect(Uri.parse(url));
      } else {
        _customHttpClient = HttpClient();
        if (_proxyAddress != null && _proxyAddress!.isNotEmpty) {
          debugPrint(
            'WebSocket: Configuring HTTP Proxy for handshake: $_proxyAddress',
          );
          // It should return "PROXY host:port;" for a specific proxy.
          _customHttpClient!.findProxy = (uri) {
            // Ensure the proxy address is correctly formatted.
            // Example: "PROXY myproxy.example.com:8888"
            // Or if your proxy needs credentials: "PROXY myuser:<EMAIL>:8888"
            return "PROXY $_proxyAddress";
          };
        }
        // Use IOWebSocketChannel.connect with the custom HttpClient
        _channel = IOWebSocketChannel.connect(
          Uri.parse(url),
          customClient: _customHttpClient,
        );
      }

      _isConnected = true;
      debugPrint('WebSocket connected successfully to $url');
      onConnected?.call();
      _listenToMessages();
      _reconnectTimer?.cancel();
    } catch (e) {
      _isConnected = false;
      debugPrint('WebSocket connection failed for $url: $e');
      onError?.call(e);
      _handleDisconnection();
    }
  }

  void _listenToMessages() {
    _streamSubscription?.cancel();
    _streamSubscription = _channel?.stream.listen(
      (message) {
        onMessage?.call(message);
      },
      onDone: () {
        _isConnected = false;
        debugPrint('WebSocket connection closed by server for $url.');
        if (!_manuallyClosed) {
          onDisconnected?.call();
          _handleDisconnection();
        }
      },
      onError: (error) {
        _isConnected = false;
        debugPrint('WebSocket error for $url: $error');
        if (!_manuallyClosed) {
          onError?.call(error);
          _handleDisconnection();
        }
      },
      cancelOnError: true,
    );
  }

  void _handleDisconnection() {
    if (_manuallyClosed) return;

    debugPrint('Attempting to reconnect WebSocket to $url...');
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(Duration(seconds: 5), () {
      if (!_manuallyClosed && !_isConnected) {
        connect(); // connect uses the instance's url
      }
    });
  }

  void sendMessage(dynamic message) {
    if (_isConnected && _channel != null) {
      _channel!.sink.add(message);
      debugPrint('WebSocket message sent via $url: $message');
    } else {
      debugPrint('Cannot send message via $url: WebSocket not connected.');
    }
  }

  Future<void> disconnect() async {
    debugPrint('Disconnecting WebSocket manually from $url...');
    _manuallyClosed = true;
    _reconnectTimer?.cancel();
    _streamSubscription?.cancel();
    _channel?.sink.close();
    _customHttpClient?.close(force: true); // Close the custom http client
    _isConnected = false;
    // Do not call onDisconnected here as this is a manual action.
    // Or, if onDisconnected should always be called, remove the _manuallyClosed check for it.
  }

  void dispose() {
    // dispose can be an alias for disconnect or a more thorough cleanup
    disconnect();
    // If there were other resources tied to WebsocketClient instance itself, clean them here.
  }
}

// Example Usage (for testing - can be removed or commented out)
/*
void main() async {
  final wsClient = WebsocketClient(
    url: 'wss://echo.websocket.org', // Public echo server
    onConnected: () {
      print("Main: Connected!");
    },
    onMessage: (message) {
      print('Main: Received WebSocket message: $message');
    },
    onDisconnected: () {
      print("Main: Disconnected!");
    },
    onError: (error) {
      print("Main: Error: $error");
    }
  );

  // Set proxy if needed (and not on web)
  // if (!kIsWeb) {
  //   wsClient.setProxy('127.0.0.1:1087');
  // }

  await wsClient.connect();

  await Future.delayed(Duration(seconds: 3));
  if (wsClient.isConnected) {
    wsClient.sendMessage('Hello from Flutter WebSocket Client via main!');
  }

  await Future.delayed(Duration(seconds: 10));
  await wsClient.disconnect(); // or wsClient.dispose();
}
*/
