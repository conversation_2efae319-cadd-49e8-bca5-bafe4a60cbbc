import 'dart:io';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

/// 代理类型
enum ProxyType {
  /// 不使用代理
  none,

  /// HTTP代理
  http,

  /// SOCKS代理
  socks5,
}

/// 定义API响应结果类
class ExternalResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final int statusCode;
  final Map<String, List<String>>? headers;

  ExternalResponse({
    required this.success,
    required this.message,
    this.data,
    required this.statusCode,
    this.headers,
  });
}

/// 自定义异常
class ExternalRequestException implements Exception {
  final String message;
  final int statusCode;

  ExternalRequestException(this.message, this.statusCode);

  @override
  String toString() => 'ExternalRequestException: $statusCode - $message';
}

/// 使用Dio处理外部链接请求的类，支持HTTP和SOCKS代理
class ExternalRequest {
  // 私有构造函数，防止外部实例化
  ExternalRequest._();

  // 单例实例
  static final ExternalRequest _instance = ExternalRequest._();

  // 获取单例实例
  static ExternalRequest get instance => _instance;

  // 请求超时时间（秒）
  static const int _connectTimeoutSeconds = 15;
  static const int _receiveTimeoutSeconds = 30;
  static const int _sendTimeoutSeconds = 15;

  // 代理配置
  ProxyType _proxyType = ProxyType.none;
  String _proxyHost = '';
  int _proxyPort = 0;
  String? _proxyUsername;
  String? _proxyPassword;

  // Dio实例
  final Dio _dio = Dio(
    BaseOptions(
      connectTimeout: Duration(seconds: _connectTimeoutSeconds),
      receiveTimeout: Duration(seconds: _receiveTimeoutSeconds),
      sendTimeout: Duration(seconds: _sendTimeoutSeconds),
      contentType: 'application/json',
      responseType: ResponseType.json,
    ),
  );

  /// 设置HTTP代理
  void setHttpProxy({
    required String host,
    required int port,
    String? username,
    String? password,
  }) {
    _proxyType = ProxyType.http;
    _proxyHost = host;
    _proxyPort = port;
    _proxyUsername = username;
    _proxyPassword = password;

    // 设置Dio的HTTP代理
    _dio.httpClientAdapter = IOHttpClientAdapter(
      createHttpClient: () {
        final client = HttpClient();
        client.findProxy = (uri) {
          return 'PROXY $host:$port';
        };

        // 如果有用户名和密码，设置代理认证
        if (username != null && password != null) {
          client.authenticate = (Uri url, String scheme, String? realm) async {
            // 设置认证凭据
            client.addCredentials(
              url,
              realm ?? '',
              HttpClientBasicCredentials(username, password),
            );
            return true; // 返回Future<bool>表示认证成功
          };
        }

        return client;
      },
    );

    debugPrint('已设置HTTP代理: $host:$port');
  }

  /// 设置SOCKS5代理
  /// 注意：当前使用系统级别的SOCKS代理设置
  void setSocks5Proxy({
    required String host,
    required int port,
    String? username,
    String? password,
  }) {
    _proxyType = ProxyType.socks5;
    _proxyHost = host;
    _proxyPort = port;
    _proxyUsername = username;
    _proxyPassword = password;

    try {
      // 设置Dio使用SOCKS5代理
      // 注意：这种方式在某些环境下可能不工作，取决于系统配置
      _dio.httpClientAdapter = IOHttpClientAdapter(
        createHttpClient: () {
          final client = HttpClient();

          client.findProxy = (uri) {
            // 尝试使用系统级别的SOCKS代理设置
            if (username != null && password != null) {
              return 'SOCKS5 $username:$password@$host:$port';
            } else {
              return 'SOCKS5 $host:$port';
            }
          };

          // 如果有用户名和密码，设置SOCKS5代理认证
          if (username != null && password != null) {
            client.authenticate = (
              Uri url,
              String scheme,
              String? realm,
            ) async {
              // 设置认证凭据
              client.addCredentials(
                url,
                realm ?? '',
                HttpClientBasicCredentials(username, password),
              );
              return true;
            };
          }

          return client;
        },
      );

      debugPrint('已设置SOCKS5代理: $host:$port');
    } catch (e) {
      debugPrint('SOCKS5代理设置失败: $e');
      // 失败时重置代理类型
      _proxyType = ProxyType.none;
      throw ExternalRequestException('SOCKS5代理设置失败: $e', 500);
    }
  }

  /// 清除代理设置
  void clearProxy() {
    _proxyType = ProxyType.none;
    _proxyHost = '';
    _proxyPort = 0;
    _proxyUsername = null;
    _proxyPassword = null;

    // 重置Dio的HTTP客户端适配器
    _dio.httpClientAdapter = IOHttpClientAdapter();

    debugPrint('已清除代理设置');
  }

  /// 获取当前代理信息
  Map<String, dynamic> getProxyInfo() {
    return {
      'type': _proxyType.toString(),
      'host': _proxyHost,
      'port': _proxyPort,
      'hasCredentials': _proxyUsername != null && _proxyPassword != null,
    };
  }

  /// 构建请求头
  Map<String, dynamic> _buildHeaders({Map<String, dynamic>? customHeaders}) {
    // 基础头部
    final headers = <String, dynamic>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'MofangQuant/1.0.0', // 添加User-Agent
    };

    // 添加自定义头部
    if (customHeaders != null) {
      headers.addAll(customHeaders);
    }

    return headers;
  }

  /// 处理响应
  ExternalResponse<T> _handleResponse<T>(
    Response response,
    T Function(dynamic)? dataConverter,
  ) {
    try {
      final data = response.data;

      // 检查响应状态码
      if (response.statusCode != null &&
          response.statusCode! >= 200 &&
          response.statusCode! < 300) {
        // 成功响应
        return ExternalResponse<T>(
          success: true,
          message: '请求成功',
          data:
              dataConverter != null && data != null
                  ? dataConverter(data)
                  : data as T?,
          statusCode: response.statusCode ?? 200,
          headers: response.headers.map,
        );
      } else {
        // 错误响应
        String errorMessage = '请求失败';
        if (data is Map<String, dynamic> && data.containsKey('message')) {
          errorMessage = data['message'];
        }
        return ExternalResponse<T>(
          success: false,
          message: errorMessage,
          statusCode: response.statusCode ?? 500,
          headers: response.headers.map,
        );
      }
    } catch (e) {
      debugPrint('响应处理错误: $e');
      return ExternalResponse<T>(
        success: false,
        message: '响应解析错误: ${e.toString()}',
        statusCode: response.statusCode ?? 500,
      );
    }
  }

  /// GET请求
  Future<ExternalResponse<T>> get<T>(
    String url, {
    Map<String, dynamic>? queryParams,
    Map<String, dynamic>? headers,
    T Function(dynamic)? dataConverter,
    int? timeout,
  }) async {
    try {
      // 构建请求选项
      final options = Options(
        headers: _buildHeaders(customHeaders: headers),
        sendTimeout: timeout != null ? Duration(seconds: timeout) : null,
        receiveTimeout: timeout != null ? Duration(seconds: timeout) : null,
      );

      // 发送请求
      final response = await _dio.get(
        url,
        queryParameters: queryParams,
        options: options,
      );

      // 处理响应
      return _handleResponse<T>(response, dataConverter);
    } on DioException catch (e) {
      debugPrint('Dio错误: ${e.message}, URL: $url');
      return ExternalResponse<T>(
        success: false,
        message: e.message ?? '请求失败',
        statusCode: e.response?.statusCode ?? 500,
        headers: e.response?.headers.map,
      );
    } catch (e) {
      debugPrint('请求错误: $e, URL: $url');
      return ExternalResponse<T>(
        success: false,
        message: '请求错误: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  /// POST请求
  Future<ExternalResponse<T>> post<T>(
    String url, {
    dynamic body,
    Map<String, dynamic>? headers,
    String? contentType,
    T Function(dynamic)? dataConverter,
    int? timeout,
  }) async {
    try {
      // 处理内容类型
      Map<String, dynamic> finalHeaders = headers ?? {};
      if (contentType != null) {
        finalHeaders['Content-Type'] = contentType;
      }

      // 构建请求选项
      final options = Options(
        headers: _buildHeaders(customHeaders: finalHeaders),
        sendTimeout: timeout != null ? Duration(seconds: timeout) : null,
        receiveTimeout: timeout != null ? Duration(seconds: timeout) : null,
      );

      // 发送请求
      final response = await _dio.post(url, data: body, options: options);

      // 处理响应
      return _handleResponse<T>(response, dataConverter);
    } on DioException catch (e) {
      debugPrint('Dio错误: ${e.message}, URL: $url');
      return ExternalResponse<T>(
        success: false,
        message: e.message ?? '请求失败',
        statusCode: e.response?.statusCode ?? 500,
        headers: e.response?.headers.map,
      );
    } catch (e) {
      debugPrint('请求错误: $e, URL: $url');
      return ExternalResponse<T>(
        success: false,
        message: '请求错误: ${e.toString()}',
        statusCode: 500,
      );
    }
  }
}
