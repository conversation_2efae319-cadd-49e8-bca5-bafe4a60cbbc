import 'dart:io';
import 'package:dio/dio.dart';
import 'package:dio/io.dart'; // For IOHttpClientAdapter on non-web platforms
import 'package:flutter/foundation.dart'; // For kIsWeb
// import 'package:dio/browser.dart';

class RequestClient {
  final Dio _dio;
  String? _proxyAddress; // e.g., 'localhost:8888'

  RequestClient() : _dio = Dio() {
    // 可以在这里设置一些dio的全局配置，例如超时时间
    _dio.options.connectTimeout = Duration(seconds: 15);
    _dio.options.receiveTimeout = Duration(seconds: 15);
  }

  /// 设置HTTP代理
  /// proxyAddress 格式应为 'host:port'，例如 '127.0.0.1:8888'
  /// 如果传入 null，则清除代理设置
  void setProxy(String? proxyAddress) {
    _proxyAddress = proxyAddress;
    if (kIsWeb) {
      if (_proxyAddress != null && _proxyAddress!.isNotEmpty) {
        debugPrint(
          'Warning: HTTP Proxy setting is not directly supported for web platform using dio. Relying on browser/system settings.',
        );
      }
      // 对于Web，dio默认使用浏览器的XMLHttpRequest或Fetch API。
      // 对于Web，dio默认使用浏览器的XMLHttpRequest或Fetch API，它们会遵循浏览器的代理设置
      // 不需要特别的HttpClientAdapter配置
    } else {
      // 对于非Web平台 (如移动端、桌面端)
      _dio.httpClientAdapter = IOHttpClientAdapter(
        createHttpClient: () {
          final client = HttpClient();
          if (_proxyAddress != null && _proxyAddress!.isNotEmpty) {
            client.findProxy = (uri) {
              return 'PROXY $_proxyAddress'; // 直接指定代理
            };
            debugPrint('HTTP Proxy configured: $_proxyAddress');
          } else {
            client.findProxy =
                HttpClient.findProxyFromEnvironment; // 使用环境变量或不使用代理
            debugPrint('HTTP Proxy cleared or using environment settings.');
          }
          // 如果代理服务器证书不是由受信任的CA颁发的，可能需要这个 (请谨慎使用)
          // client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
          return client;
        },
      );
    }
  }

  /// GET 请求
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      // 处理Dio错误，可以根据e.type和e.response进行更细致的处理
      debugPrint('GET Request Error: $e');
      rethrow; // 将错误重新抛出，让调用者处理
    }
  }

  /// POST 请求
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      debugPrint('POST Request Error: $e');
      rethrow;
    }
  }

  // 你可以根据需要添加其他HTTP方法，如PUT, DELETE等
}

// 示例用法 (可以放在你的main.dart或其他地方测试)
/*
void main() async {
  final client = RequestClient();

  // 设置代理 (如果需要)
  // client.setProxy('127.0.0.1:8888'); 

  try {
    print('Testing GET request...');
    final response = await client.get('https://jsonplaceholder.typicode.com/todos/1');
    print('GET Response: ${response.data}');
  } catch (e) {
    print('GET Test Error: $e');
  }

  try {
    print('\nTesting POST request...');
    final postResponse = await client.post(
      'https://jsonplaceholder.typicode.com/posts',
      data: {'title': 'foo', 'body': 'bar', 'userId': 1},
    );
    print('POST Response Status: ${postResponse.statusCode}');
    print('POST Response Data: ${postResponse.data}');
  } catch (e) {
    print('POST Test Error: $e');
  }
  
  // 清除代理 (如果之前设置了)
  // client.setProxy(null);
}
*/
