import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:msgpack_dart/msgpack_dart.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';
import '../config/api_route.dart';
import '../../providers/auth_provider.dart';
import '../market/market_service.dart';
import '../clients/dio_request.dart';

/// WebSocket 服务类
///
/// 功能：
/// - 管理 WebSocket 连接
/// - 处理消息发送和接收
/// - 自动重连机制
/// - 心跳机制
/// - 订阅管理
/// - 应用生命周期管理
class WebSocketService with WidgetsBindingObserver {
  static final WebSocketService _instance = WebSocketService._internal();
  static WebSocketService get instance => _instance;
  WebSocketService._internal() {
    WidgetsBinding.instance.addObserver(this);
  }

  // WebSocket 连接
  WebSocketChannel? _channel;

  // 连接状态
  bool _isConnected = false;
  bool _isConnecting = false;
  bool _isAppInBackground = false;

  // 重连相关
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const Duration _reconnectDelay = Duration(seconds: 3);

  // 心跳相关
  Timer? _heartbeatTimer;
  int _heartbeatInterval = 30;

  // 订阅管理
  final Map<String, StreamController<Map<String, dynamic>>> _subscriptions = {};

  // 连接状态流控制器
  final StreamController<bool> _connectionController = StreamController<bool>.broadcast();

  // Ticker数据处理相关
  Timer? _tickerUpdateTimer;
  final Map<int, Map<int, Map<String, dynamic>>> _latestTickerData = {}; // market_type -> currency_id -> ticker数据
  static const Duration _tickerUpdateInterval = Duration(seconds: 10);

  /// 获取连接状态流
  Stream<bool> get connectionStream => _connectionController.stream;

  /// 当前连接状态
  bool get isConnected => _isConnected;

  /// 连接到 WebSocket 服务器
  Future<void> connect() async {
    if (_isConnected || _isConnecting) {
      debugPrint('WebSocket 已连接或正在连接中');
      return;
    }

    _isConnecting = true;

    try {
      debugPrint('正在连接 WebSocket: ${ApiRoute.wsUrl}');

      // 创建 WebSocket 连接
      _channel = IOWebSocketChannel.connect(Uri.parse(ApiRoute.wsUrl), protocols: ['websocket']);

      // 监听连接状态
      await _channel!.ready;

      _isConnected = true;
      _isConnecting = false;
      _reconnectAttempts = 0;

      debugPrint('WebSocket 连接成功');
      _connectionController.add(true);

      // 开始监听消息
      _listenToMessages();
    } catch (e) {
      _isConnecting = false;
      _isConnected = false;
      debugPrint('WebSocket 连接失败: $e');
      _connectionController.add(false);

      // 尝试重连
      _scheduleReconnect();
    }
  }

  /// 监听 WebSocket 消息
  void _listenToMessages() {
    _channel?.stream.listen(
      (data) {
        try {
          Map<String, dynamic>? message = _parseWebSocketData(data);

          if (message != null) {
            // 处理连接确认消息
            if (message['type'] == 'connected') {
              _handleConnectedMessage(message);
            }

            // 处理ticker数据更新
            if (message['type'] == 'ticker') {
              _handleTickerMessage(message);
            }

            // 只有在应用前台时才分发消息
            if (!_isAppInBackground) {
              _distributeMessage(message);
            }
          }
        } catch (e) {
          debugPrint('处理 WebSocket 消息失败: $e');
        }
      },
      onError: (error) {
        debugPrint('WebSocket 错误: $error');
        _handleDisconnection();
      },
      onDone: () {
        debugPrint('WebSocket 连接关闭');
        _handleDisconnection();
      },
    );
  }

  /// 解析WebSocket数据
  Map<String, dynamic>? _parseWebSocketData(dynamic data) {
    try {
      // 处理字符串数据
      if (data is String) {
        return json.decode(data);
      }

      // 处理二进制数据
      if (data is List<int> || data is Uint8List) {
        Uint8List bytes = data is Uint8List ? data : Uint8List.fromList(data);

        // 尝试多种解码方式

        // 1. 尝试MessagePack解码
        try {
          // MessagePack解码
          final decoded = deserialize(bytes);

          // 递归转换所有Map和List为正确的类型
          final result = _convertToStringKeyMap(decoded);

          if (result is Map<String, dynamic>) {
            return result;
          } else {
            return null;
          }
        } catch (e) {
          debugPrint('MessagePack解码失败: $e');
        }

        // 2. 如果MessagePack解码失败，返回null

        return null;
      }

      // 其他类型数据
      return null;
    } catch (e) {
      debugPrint('解析 WebSocket 消息失败: $e');
      return null;
    }
  }

  /// 递归转换MessagePack解码结果为正确的类型
  dynamic _convertToStringKeyMap(dynamic value) {
    if (value is Map) {
      // 转换Map<dynamic, dynamic>为Map<String, dynamic>
      final result = <String, dynamic>{};
      value.forEach((key, val) {
        final stringKey = key.toString();
        result[stringKey] = _convertToStringKeyMap(val);
      });
      return result;
    } else if (value is List) {
      // 递归转换List中的元素
      return value.map((item) => _convertToStringKeyMap(item)).toList();
    } else {
      // 基本类型直接返回
      return value;
    }
  }

  /// 处理连接确认消息
  void _handleConnectedMessage(Map<String, dynamic> message) {
    _heartbeatInterval = message['heartbeat_interval'] ?? 30;
    debugPrint('收到连接确认，心跳间隔: $_heartbeatInterval 秒');

    // 立即发送一次心跳
    _sendHeartbeat();

    // 启动心跳定时器
    _startHeartbeat();
  }

  /// 启动心跳
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(Duration(seconds: _heartbeatInterval), (timer) => _sendHeartbeat());
  }

  /// 发送心跳
  void _sendHeartbeat() {
    if (_isConnected) {
      sendMessage({'type': 'ping'});
    }
  }

  /// 分发消息到订阅者
  void _distributeMessage(Map<String, dynamic> message) {
    // 根据消息类型分发到对应的订阅者
    final messageType = message['type'] as String?;
    if (messageType != null && _subscriptions.containsKey(messageType)) {
      _subscriptions[messageType]?.add(message);
    }

    // 分发到通用订阅者
    if (_subscriptions.containsKey('*')) {
      _subscriptions['*']?.add(message);
    }
  }

  /// 订阅消息
  Stream<Map<String, dynamic>> subscribe(String messageType, Map<String, dynamic> subscribeMessage) {
    // 发送订阅消息到WebSocket
    sendMessage(subscribeMessage);

    // 创建或获取对应的流控制器
    if (!_subscriptions.containsKey(messageType)) {
      _subscriptions[messageType] = StreamController<Map<String, dynamic>>.broadcast();
    }
    return _subscriptions[messageType]!.stream;
  }

  /// 取消订阅
  void unsubscribe(String messageType) {
    if (_subscriptions.containsKey(messageType)) {
      _subscriptions[messageType]?.close();
      _subscriptions.remove(messageType);
    }
  }

  /// 处理连接断开
  void _handleDisconnection() {
    _isConnected = false;
    _connectionController.add(false);
    _heartbeatTimer?.cancel();

    // 如果不是主动断开，则尝试重连
    if (_channel != null && !_isAppInBackground) {
      _scheduleReconnect();
    }
  }

  /// 安排重连
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      debugPrint('达到最大重连次数，停止重连');
      return;
    }

    _reconnectAttempts++;
    debugPrint('安排第 $_reconnectAttempts 次重连，${_reconnectDelay.inSeconds} 秒后执行');

    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(_reconnectDelay, () {
      connect();
    });
  }

  /// 发送消息
  void sendMessage(Map<String, dynamic> message) {
    if (!_isConnected || _channel == null) {
      debugPrint('WebSocket 未连接，无法发送消息');
      return;
    }

    try {
      final jsonMessage = json.encode(message);
      _channel!.sink.add(jsonMessage);
      debugPrint('发送 WebSocket 消息: $jsonMessage');
    } catch (e) {
      debugPrint('发送 WebSocket 消息失败: $e');
    }
  }

  /// 认证方法
  void authenticate(AuthProvider authProvider) {
    if (!_isConnected) {
      debugPrint('WebSocket 未连接，无法发送认证');
      return;
    }

    final authMessage = {'action': 'auth', 'type': 'login', 'token': authProvider.accessToken ?? '', 'X-Device-Info': DioRequest.deviceInfo};

    sendMessage(authMessage);
    debugPrint('发送认证消息');
  }

  /// 应用生命周期变化
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        _isAppInBackground = true;
        debugPrint('应用切换到后台，暂停消息分发');
        break;
      case AppLifecycleState.resumed:
        _isAppInBackground = false;
        debugPrint('应用切换到前台，恢复消息分发');
        // 如果连接断开，尝试重连
        if (!_isConnected && !_isConnecting) {
          connect();
        }
        break;
      case AppLifecycleState.hidden:
        break;
    }
  }

  /// 断开连接
  void disconnect() {
    debugPrint('主动断开 WebSocket 连接');

    _reconnectTimer?.cancel();
    _reconnectTimer = null;
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;

    _stopTickerUpdateTimer();

    _channel?.sink.close();
    _channel = null;

    _isConnected = false;
    _isConnecting = false;
    _reconnectAttempts = 0;

    _connectionController.add(false);
  }

  /// 处理ticker消息
  void _handleTickerMessage(Map<String, dynamic> message) {
    try {
      final marketType = message['market_type'] as int?;
      final data = message['data'] as List<dynamic>?;

      if (marketType == null || data == null) {
        return;
      }

      // 确保market_type的Map存在
      if (!_latestTickerData.containsKey(marketType)) {
        _latestTickerData[marketType] = {};
      }

      // 只保留每个currency_id的最新ticker数据
      for (final tickerData in data) {
        if (tickerData is Map) {
          final currencyId = tickerData['currency_id'] as int?;
          if (currencyId != null) {
            // 转换为Map<String, dynamic>类型
            _latestTickerData[marketType]![currencyId] = Map<String, dynamic>.from(tickerData);
          }
        }
      }

      // 启动定时器（如果还没有启动）
      _startTickerUpdateTimer();
    } catch (e) {
      debugPrint('❌ 处理ticker消息失败: $e');
    }
  }

  /// 启动ticker更新定时器
  void _startTickerUpdateTimer() {
    if (_tickerUpdateTimer != null && _tickerUpdateTimer!.isActive) {
      return; // 定时器已经在运行
    }

    _tickerUpdateTimer = Timer.periodic(_tickerUpdateInterval, (timer) {
      _processPendingTickerUpdates();
    });
  }

  /// 处理待更新的ticker数据
  void _processPendingTickerUpdates() {
    if (_latestTickerData.isEmpty) {
      return;
    }

    try {
      // 异步处理ticker更新，避免阻塞主线程
      Future.microtask(() {
        final updatesCopy = Map<int, Map<int, Map<String, dynamic>>>.from(_latestTickerData);
        _latestTickerData.clear();

        for (final entry in updatesCopy.entries) {
          final marketType = entry.key;
          final currencyTickerMap = entry.value;

          if (currencyTickerMap.isNotEmpty) {
            // 将Map转换为List进行批量更新
            final tickerList = currencyTickerMap.values.toList();
            MarketService.updateTickerDataBatch(tickerList, marketType);
          }
        }
      });
    } catch (e) {
      debugPrint('❌ 处理待更新ticker数据失败: $e');
    }
  }

  /// 停止ticker更新定时器
  void _stopTickerUpdateTimer() {
    _tickerUpdateTimer?.cancel();
    _tickerUpdateTimer = null;
    _latestTickerData.clear();
  }

  /// 销毁服务
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _stopTickerUpdateTimer();
    disconnect();

    // 关闭所有订阅
    for (final subscription in _subscriptions.values) {
      subscription.close();
    }
    _subscriptions.clear();

    _connectionController.close();
  }
}
