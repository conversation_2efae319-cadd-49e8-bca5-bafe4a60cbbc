import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 首次启动检测服务
/// 
/// 用于检测应用是否为第一次启动，管理首次启动状态
class FirstLaunchService {
  static const String _firstLaunchKey = 'is_first_launch';
  static const String _appVersionKey = 'app_version';
  
  static FirstLaunchService? _instance;
  static FirstLaunchService get instance => _instance ??= FirstLaunchService._();
  
  FirstLaunchService._();
  
  bool _isFirstLaunch = true;
  bool _isInitialized = false;
  
  /// 是否为第一次启动
  bool get isFirstLaunch => _isFirstLaunch;
  
  /// 是否已初始化
  bool get isInitialized => _isInitialized;
  
  /// 初始化首次启动检测
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('⚠️ FirstLaunchService 已经初始化，跳过重复初始化');
      return;
    }
    
    try {
      debugPrint('🚀 初始化首次启动检测服务...');
      
      final prefs = await SharedPreferences.getInstance();
      
      // 检查是否存在首次启动标记
      final hasLaunchedBefore = prefs.getBool(_firstLaunchKey) ?? false;
      _isFirstLaunch = !hasLaunchedBefore;
      
      // 检查应用版本是否更新
      await _checkVersionUpdate(prefs);
      
      _isInitialized = true;
      
      debugPrint('✅ 首次启动检测服务初始化完成');
      debugPrint('   - 是否为首次启动: $_isFirstLaunch');
      
    } catch (e) {
      debugPrint('❌ 首次启动检测服务初始化失败: $e');
      // 发生错误时，默认为非首次启动
      _isFirstLaunch = false;
      _isInitialized = true;
    }
  }
  
  /// 标记应用已启动过
  Future<void> markAsLaunched() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_firstLaunchKey, true);
      
      // 保存当前应用版本
      await _saveCurrentVersion(prefs);
      
      _isFirstLaunch = false;
      
      debugPrint('✅ 已标记应用为非首次启动');
      
    } catch (e) {
      debugPrint('❌ 标记应用启动状态失败: $e');
    }
  }
  
  /// 重置首次启动状态（用于测试）
  Future<void> resetFirstLaunchStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_firstLaunchKey);
      await prefs.remove(_appVersionKey);
      
      _isFirstLaunch = true;
      
      debugPrint('🔄 已重置首次启动状态');
      
    } catch (e) {
      debugPrint('❌ 重置首次启动状态失败: $e');
    }
  }
  
  /// 检查应用版本更新
  Future<void> _checkVersionUpdate(SharedPreferences prefs) async {
    try {
      const currentVersion = '1.0.0'; // 从配置或包信息获取
      final savedVersion = prefs.getString(_appVersionKey);
      
      if (savedVersion == null) {
        // 第一次安装
        debugPrint('📱 检测到应用首次安装');
      } else if (savedVersion != currentVersion) {
        // 版本更新
        debugPrint('📱 检测到应用版本更新: $savedVersion -> $currentVersion');
        // 可以在这里处理版本更新逻辑
      } else {
        // 相同版本
        debugPrint('📱 应用版本未变化: $currentVersion');
      }
      
    } catch (e) {
      debugPrint('❌ 检查版本更新失败: $e');
    }
  }
  
  /// 保存当前应用版本
  Future<void> _saveCurrentVersion(SharedPreferences prefs) async {
    try {
      const currentVersion = '1.0.0'; // 从配置或包信息获取
      await prefs.setString(_appVersionKey, currentVersion);
    } catch (e) {
      debugPrint('❌ 保存应用版本失败: $e');
    }
  }
  
  /// 获取调试信息
  Map<String, dynamic> getDebugInfo() {
    return {
      'isFirstLaunch': _isFirstLaunch,
      'isInitialized': _isInitialized,
    };
  }
  
  /// 打印调试信息
  void printDebugInfo() {
    if (kDebugMode) {
      debugPrint('=== FirstLaunchService 调试信息 ===');
      debugPrint('是否为首次启动: $_isFirstLaunch');
      debugPrint('是否已初始化: $_isInitialized');
      debugPrint('================================');
    }
  }
}
