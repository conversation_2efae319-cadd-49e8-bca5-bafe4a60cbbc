// 服务管理器
import 'package:flutter/foundation.dart';
import 'base_service.dart';

/// 服务管理器
/// 
/// 负责管理所有服务的生命周期，提供统一的服务注册、初始化、获取和释放功能
class ServiceManager {
  static final ServiceManager _instance = ServiceManager._internal();
  static ServiceManager get instance => _instance;
  ServiceManager._internal();

  final Map<Type, BaseService> _services = {};
  final List<BaseService> _initializationOrder = [];
  bool _isInitialized = false;
  bool _isInitializing = false;

  /// 是否已初始化所有服务
  bool get isInitialized => _isInitialized;
  
  /// 是否正在初始化
  bool get isInitializing => _isInitializing;

  /// 注册服务
  /// 
  /// [service] 要注册的服务实例
  /// [priority] 初始化优先级，数字越小优先级越高
  void registerService<T extends BaseService>(
    T service, {
    int priority = 100,
  }) {
    final type = T;
    
    if (_services.containsKey(type)) {
      debugPrint('⚠️ 服务 ${service.serviceName} 已经注册，将被替换');
    }
    
    _services[type] = service;
    
    // 按优先级插入到初始化顺序列表中
    _insertByPriority(service, priority);
    
    debugPrint('📝 服务 ${service.serviceName} 注册成功 (优先级: $priority)');
  }

  /// 获取服务实例
  /// 
  /// 返回指定类型的服务实例，如果服务未注册则返回null
  T? getService<T extends BaseService>() {
    return _services[T] as T?;
  }

  /// 获取服务实例（必须存在）
  /// 
  /// 返回指定类型的服务实例，如果服务未注册则抛出异常
  T getRequiredService<T extends BaseService>() {
    final service = getService<T>();
    if (service == null) {
      throw StateError('服务 $T 未注册');
    }
    return service;
  }

  /// 检查服务是否已注册
  bool hasService<T extends BaseService>() {
    return _services.containsKey(T);
  }

  /// 初始化所有服务
  /// 
  /// 按照注册时指定的优先级顺序初始化所有服务
  Future<void> initializeAll() async {
    if (_isInitialized) {
      debugPrint('⚠️ 服务管理器已经初始化，跳过重复初始化');
      return;
    }

    if (_isInitializing) {
      debugPrint('⚠️ 服务管理器正在初始化中，请等待');
      return;
    }

    _isInitializing = true;
    
    try {
      debugPrint('🚀 开始初始化所有服务...');
      debugPrint('📋 初始化顺序: ${_initializationOrder.map((s) => s.serviceName).join(' -> ')}');

      for (final service in _initializationOrder) {
        try {
          await service.initialize();
        } catch (e) {
          debugPrint('❌ 服务 ${service.serviceName} 初始化失败，继续初始化其他服务');
          // 可以选择是否继续初始化其他服务
          // rethrow; // 如果需要停止整个初始化过程，取消注释这行
        }
      }

      _isInitialized = true;
      debugPrint('✅ 所有服务初始化完成');
      _printServiceStatus();
      
    } catch (e) {
      debugPrint('❌ 服务初始化过程中发生错误: $e');
      rethrow;
    } finally {
      _isInitializing = false;
    }
  }

  /// 释放所有服务
  /// 
  /// 按照初始化顺序的逆序释放所有服务
  Future<void> disposeAll() async {
    if (!_isInitialized) {
      debugPrint('⚠️ 服务管理器未初始化，无需释放');
      return;
    }

    debugPrint('🔄 开始释放所有服务...');

    // 逆序释放服务
    for (final service in _initializationOrder.reversed) {
      try {
        await service.dispose();
      } catch (e) {
        debugPrint('❌ 服务 ${service.serviceName} 释放失败: $e');
        // 继续释放其他服务
      }
    }

    _isInitialized = false;
    debugPrint('✅ 所有服务释放完成');
  }

  /// 重置所有服务
  /// 
  /// 将所有服务状态重置为未初始化
  void resetAll() {
    debugPrint('🔄 重置所有服务状态...');
    
    for (final service in _services.values) {
      service.reset();
    }
    
    _isInitialized = false;
    _isInitializing = false;
    
    debugPrint('✅ 所有服务状态已重置');
  }

  /// 获取所有服务状态
  Map<String, String> getAllServiceStatus() {
    return _services.values.fold<Map<String, String>>(
      {},
      (map, service) => map..[service.serviceName] = service.getStatusDescription(),
    );
  }

  /// 打印服务状态
  void _printServiceStatus() {
    debugPrint('📊 服务状态统计:');
    final statusMap = getAllServiceStatus();
    statusMap.forEach((name, status) {
      debugPrint('   - $name: $status');
    });
  }

  /// 按优先级插入服务到初始化顺序列表
  void _insertByPriority(BaseService service, int priority) {
    // 移除已存在的同类型服务
    _initializationOrder.removeWhere((s) => s.runtimeType == service.runtimeType);
    
    // 找到合适的插入位置（按优先级排序，数字越小优先级越高）
    int insertIndex = 0;
    for (int i = 0; i < _initializationOrder.length; i++) {
      // 这里简化处理，假设所有服务的默认优先级都是100
      // 实际项目中可能需要更复杂的优先级管理
      if (priority < 100) {
        insertIndex = i;
        break;
      }
      insertIndex = i + 1;
    }
    
    _initializationOrder.insert(insertIndex, service);
  }

  /// 获取服务数量
  int get serviceCount => _services.length;

  /// 获取已初始化的服务数量
  int get initializedServiceCount {
    return _services.values.where((service) => service.isInitialized).length;
  }

  /// 获取有错误的服务数量
  int get errorServiceCount {
    return _services.values.where((service) => service.hasError).length;
  }
}
