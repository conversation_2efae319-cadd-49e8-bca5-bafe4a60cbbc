// 服务基类
import 'package:flutter/foundation.dart';

/// 服务状态枚举
enum ServiceStatus {
  uninitialized,  // 未初始化
  initializing,   // 初始化中
  initialized,    // 已初始化
  error,          // 错误状态
  disposed,       // 已释放
}

/// 服务基类
/// 
/// 所有服务都应该继承此基类，提供统一的生命周期管理
abstract class BaseService {
  ServiceStatus _status = ServiceStatus.uninitialized;
  String? _errorMessage;
  
  /// 服务名称
  String get serviceName;
  
  /// 服务状态
  ServiceStatus get status => _status;
  
  /// 是否已初始化
  bool get isInitialized => _status == ServiceStatus.initialized;
  
  /// 是否正在初始化
  bool get isInitializing => _status == ServiceStatus.initializing;
  
  /// 是否有错误
  bool get hasError => _status == ServiceStatus.error;
  
  /// 错误信息
  String? get errorMessage => _errorMessage;
  
  /// 初始化服务
  /// 
  /// 子类需要重写此方法实现具体的初始化逻辑
  Future<void> initialize() async {
    if (_status == ServiceStatus.initialized) {
      debugPrint('⚠️ $serviceName 已经初始化，跳过重复初始化');
      return;
    }
    
    if (_status == ServiceStatus.initializing) {
      debugPrint('⚠️ $serviceName 正在初始化中，请等待');
      return;
    }
    
    try {
      _status = ServiceStatus.initializing;
      _errorMessage = null;
      
      debugPrint('🚀 开始初始化 $serviceName...');
      await onInitialize();
      
      _status = ServiceStatus.initialized;
      debugPrint('✅ $serviceName 初始化成功');
      
    } catch (e, stackTrace) {
      _status = ServiceStatus.error;
      _errorMessage = e.toString();
      
      debugPrint('❌ $serviceName 初始化失败: $e');
      if (kDebugMode) {
        debugPrint('Stack trace: $stackTrace');
      }
      
      // 可以选择重新抛出异常或者静默处理
      rethrow;
    }
  }
  
  /// 释放服务资源
  /// 
  /// 子类需要重写此方法实现具体的资源释放逻辑
  Future<void> dispose() async {
    if (_status == ServiceStatus.disposed) {
      debugPrint('⚠️ $serviceName 已经释放，跳过重复释放');
      return;
    }
    
    try {
      debugPrint('🔄 开始释放 $serviceName...');
      await onDispose();
      
      _status = ServiceStatus.disposed;
      _errorMessage = null;
      
      debugPrint('✅ $serviceName 释放成功');
      
    } catch (e, stackTrace) {
      debugPrint('❌ $serviceName 释放失败: $e');
      if (kDebugMode) {
        debugPrint('Stack trace: $stackTrace');
      }
    }
  }
  
  /// 重置服务状态
  /// 
  /// 将服务状态重置为未初始化，可以重新初始化
  void reset() {
    _status = ServiceStatus.uninitialized;
    _errorMessage = null;
    debugPrint('🔄 $serviceName 状态已重置');
  }
  
  /// 子类实现的初始化逻辑
  /// 
  /// 在此方法中实现具体的初始化代码
  @protected
  Future<void> onInitialize();
  
  /// 子类实现的释放逻辑
  /// 
  /// 在此方法中实现具体的资源释放代码
  @protected
  Future<void> onDispose();
  
  /// 获取服务状态描述
  String getStatusDescription() {
    switch (_status) {
      case ServiceStatus.uninitialized:
        return '未初始化';
      case ServiceStatus.initializing:
        return '初始化中';
      case ServiceStatus.initialized:
        return '已初始化';
      case ServiceStatus.error:
        return '错误: $_errorMessage';
      case ServiceStatus.disposed:
        return '已释放';
    }
  }
  
  @override
  String toString() {
    return '$serviceName(${getStatusDescription()})';
  }
}
