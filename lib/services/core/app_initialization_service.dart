import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qubic_exchange/core/index.dart';
import '../../utils/common/app_info_util.dart';

// ========================================
// 🚀 应用初始化服务
// ========================================

/// 应用初始化服务
/// 负责应用启动时的各种初始化工作
class AppInitializationService {
  static final AppInitializationService _instance =
      AppInitializationService._internal();
  static AppInitializationService get instance => _instance;
  AppInitializationService._internal();

  // ========== 私有属性 ==========
  bool _isInitialized = false;
  AppConfigModel? _appConfig;

  // ========== 公共属性 ==========
  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 获取应用配置
  AppConfigModel? get appConfig => _appConfig;

  // ========== 初始化方法 ==========
  /// 初始化应用
  /// 按顺序执行各种初始化任务
  Future<void> initializeApp() async {
    if (_isInitialized) {
      debugPrint('⚠️ 应用已经初始化，跳过重复初始化');
      return;
    }

    try {
      debugPrint('🚀 开始应用初始化...');

      // 1. 确保Flutter绑定初始化
      await _ensureFlutterBinding();

      // 2. 设置系统UI样式
      await _setupSystemUI();

      // 3. 加载应用配置
      await _loadAppConfig();

      // 4. 初始化主题系统
      await _initializeTheme();

      // 5. 初始化其他服务
      await _initializeOtherServices();

      _isInitialized = true;
      debugPrint('✅ 应用初始化完成');
    } catch (e) {
      debugPrint('❌ 应用初始化失败: $e');
      rethrow;
    }
  }

  // ========== 私有初始化方法 ==========
  /// 确保Flutter绑定初始化
  Future<void> _ensureFlutterBinding() async {
    debugPrint('🔧 确保Flutter绑定初始化...');
    WidgetsFlutterBinding.ensureInitialized();

    // 设置应用启动时间
    AppInfoUtil.setAppStartTime();
  }

  /// 设置系统UI样式
  Future<void> _setupSystemUI() async {
    debugPrint('🎨 设置系统UI样式...');

    // 设置状态栏样式
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    // 设置支持的屏幕方向
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  /// 加载应用配置
  Future<void> _loadAppConfig() async {
    debugPrint('📋 加载应用配置...');

    try {
      _appConfig = await ConfigService.instance.initializeConfig();
      debugPrint('✅ 应用配置加载成功');

      // 打印配置信息（调试模式）
      if (kDebugMode) {
        final config = ConfigService.instance.currentConfig;
        debugPrint('📋 当前配置: ${config.themeConfig.templateName}');
        debugPrint('📋 API地址: ${config.apiBaseUrl}');
        debugPrint('📋 调试模式: ${config.debugMode}');
      }
    } catch (e) {
      debugPrint('❌ 应用配置加载失败: $e');
      // 使用默认配置
      _appConfig = AppConfigModel.defaultConfig();
      debugPrint('⚠️ 使用默认应用配置');
    }
  }

  /// 初始化主题系统
  Future<void> _initializeTheme() async {
    debugPrint('🎨 初始化主题系统...');

    if (_appConfig == null) {
      debugPrint('❌ 应用配置未加载，无法初始化主题');
      return;
    }

    try {
      final themeConfig = _appConfig!.themeConfig;

      debugPrint('✅ 主题系统初始化完成');
      debugPrint('   - 主题模板: ${themeConfig.templateName}');
      debugPrint('   - 深色模式: ${themeConfig.defaultDarkMode}');
      debugPrint('   - 注意: 主题切换功能已移除，使用固定主题');
    } catch (e) {
      debugPrint('❌ 主题系统初始化失败: $e');
      debugPrint('⚠️ 使用默认主题配置');
    }
  }

  /// 初始化其他服务
  Future<void> _initializeOtherServices() async {
    debugPrint('🔧 初始化其他服务...');

    // 预留其他服务初始化位置
    // 如：数据库、网络服务、推送服务等

    debugPrint('✅ 其他服务初始化完成');
  }

  // ========== 公共方法 ==========
  /// 重新初始化主题
  /// 当配置更新时调用
  Future<void> reinitializeTheme() async {
    debugPrint('🔄 重新初始化主题...');

    try {
      // 重新加载配置
      _appConfig = await ConfigService.instance.refreshConfig();

      // 重新初始化主题
      await _initializeTheme();

      debugPrint('✅ 主题重新初始化完成');
    } catch (e) {
      debugPrint('❌ 主题重新初始化失败: $e');
    }
  }

  /// 重置应用状态
  /// 用于重新初始化
  void reset() {
    debugPrint('🔄 重置应用初始化状态');
    _isInitialized = false;
    _appConfig = null;
  }

  // ========== 便捷访问方法 ==========
  /// 获取主题配置
  ThemeConfig? get themeConfig => _appConfig?.themeConfig;

  /// 是否允许主题切换
  bool get allowThemeSwitch => themeConfig?.allowThemeSwitch ?? true;

  /// 是否允许深色模式切换
  bool get allowDarkModeSwitch => themeConfig?.allowDarkModeSwitch ?? true;

  /// 是否为调试模式
  bool get isDebugMode => _appConfig?.debugMode ?? kDebugMode;

  // ========== 调试方法 ==========
  /// 打印初始化状态信息
  void printInitializationInfo() {
    if (kDebugMode) {
      debugPrint('=== 应用初始化状态 ===');
      debugPrint('已初始化: $_isInitialized');
      debugPrint('配置已加载: ${_appConfig != null}');
      if (_appConfig != null) {
        debugPrint('主题模板: ${_appConfig!.themeConfig.templateName}');
        debugPrint('API地址: ${_appConfig!.apiBaseUrl}');
      }
      debugPrint('==================');
    }
  }
}
