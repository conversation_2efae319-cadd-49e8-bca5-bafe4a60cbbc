import 'dart:async';
import 'package:flutter/foundation.dart';
import '../clients/dio_request.dart';
import '../config/api_route.dart';
import '../core/base_service.dart';
import '../storage/storage_service.dart';
import 'models/currency_model.dart';
import 'models/market_type_model.dart';
import 'models/category_model.dart';
import 'models/main_tab_model.dart';

/// 市场行情服务
///
/// 负责管理市场行情数据，包括：
/// - 币种数据的获取和缓存
/// - 增量数据更新
/// - 币种栏目数据
/// - 交易市场类型数据
class MarketService extends BaseService {
  static final MarketService _instance = MarketService._internal();
  factory MarketService() => _instance;
  MarketService._internal();

  // 单例访问器
  static MarketService get instance => _instance;

  // 存储键名
  static const String _currencyDataKey = 'market_currency_data';
  static const String _currencyCateKey = 'market_currency_cate';
  static const String _marketTypeKey = 'market_type';
  static const String _mainTabKey = 'market_main_tab';
  static const String _lastUpdateTimeKey = 'market_last_update_time';

  // 数据存储
  List<Map<String, dynamic>> _currencyData = [];
  List<Map<String, dynamic>> _currencyCateData = [];
  List<Map<String, dynamic>> _marketTypeData = [];
  List<Map<String, dynamic>> _mainTabData = [];
  DateTime? _lastUpdateTime;

  // 新增：模型化数据存储
  List<CurrencyModel> _currencyModels = [];
  List<CategoryModel> _categoryModels = [];
  List<MarketTypeModel> _marketTypeModels = [];
  List<MainTabModel> _mainTabModels = [];

  // 定时更新
  Timer? _updateTimer;
  static const Duration _updateInterval = Duration(minutes: 5);

  // 缓存写入定时器
  Timer? _cacheWriteTimer;
  static const Duration _cacheWriteInterval = Duration(seconds: 60); // 5分钟更新一次

  // BaseService 抽象方法实现
  @override
  String get serviceName => 'MarketService';

  @override
  Future<void> onInitialize() async {
    try {
      // 加载本地缓存数据
      await _loadCachedData();

      // 检查是否需要获取币种数据
      if (_currencyData.isEmpty) {
        debugPrint('📊 本地无币种缓存，开始获取币种数据...');
        final success = await fetchCurrencyData();
        if (!success) {
          debugPrint('⚠️ 币种数据获取失败，将在后台重试');
        }
      } else {
        debugPrint('📊 已加载本地币种缓存，共 ${_currencyData.length} 个币种');

        // 验证缓存数据的完整性
        if (!_validateCacheData()) {
          debugPrint('⚠️ 缓存数据验证失败，重新获取');
          await _handleCorruptedCache('Cache validation failed');
          await fetchCurrencyData();
        }
      }

      // 启动定时增量更新
      _startPeriodicUpdate();

      // 启动缓存写入定时器
      _startCacheWriteTimer();
    } catch (e) {
      debugPrint('❌ MarketService 初始化过程中发生错误: $e');
      // 即使初始化失败，也要启动定时器，以便后续重试
      _startPeriodicUpdate();
      _startCacheWriteTimer();
    }
  }

  @override
  Future<void> onDispose() async {
    // 停止定时器
    _updateTimer?.cancel();
    _updateTimer = null;

    // 停止缓存写入定时器
    _cacheWriteTimer?.cancel();
    _cacheWriteTimer = null;

    _currencyData.clear();
    _currencyCateData.clear();
    _marketTypeData.clear();
    _lastUpdateTime = null;
  }

  // 数据访问器（保留用于ticker更新功能）
  @Deprecated('使用 currencyModels 替代')
  List<Map<String, dynamic>> get currencyData => List.unmodifiable(_currencyData);
  @Deprecated('使用 categoryModels 替代')
  List<Map<String, dynamic>> get currencyCateData => List.unmodifiable(_currencyCateData);
  @Deprecated('使用 marketTypeModels 替代')
  List<Map<String, dynamic>> get marketTypeData => List.unmodifiable(_marketTypeData);
  DateTime? get lastUpdateTime => _lastUpdateTime;

  // 新增：模型数据访问器
  List<CurrencyModel> get currencyModels => List.unmodifiable(_currencyModels);
  List<CategoryModel> get categoryModels => List.unmodifiable(_categoryModels);
  List<MarketTypeModel> get marketTypeModels => List.unmodifiable(_marketTypeModels);
  List<MainTabModel> get mainTabModels => List.unmodifiable(_mainTabModels);

  // 保持原有的initialize方法以兼容现有代码
  @override
  Future<void> initialize() async {
    await super.initialize();
  }

  // 保持原有的dispose方法以兼容现有代码
  @override
  Future<void> dispose() async {
    await super.dispose();
  }

  /// 启动定时增量更新
  void _startPeriodicUpdate() {
    // 如果已有定时器在运行，先停止
    _updateTimer?.cancel();
    _updateTimer = Timer.periodic(_updateInterval, (timer) async {
      try {
        // 只有当币种数据存在时才执行增量更新
        if (_currencyData.isNotEmpty) {
          await updateCurrencyData();
        }
      } catch (e) {
        debugPrint('❌ 定时增量更新异常: $e');
      }
    });
  }

  /// 停止定时更新
  void stopPeriodicUpdate() {
    _updateTimer?.cancel();
    _updateTimer = null;
    debugPrint('⏹️ 定时增量更新已停止');
  }

  /// 加载本地缓存数据
  Future<void> _loadCachedData() async {
    try {
      final storage = StorageService.instance;

      // 加载币种数据
      final currencyList = storage.getList(_currencyDataKey);
      if (currencyList != null) {
        _currencyData = _convertToMapList(currencyList, '币种');
      }

      // 加载币种栏目数据
      final cateList = storage.getList(_currencyCateKey);
      if (cateList != null) {
        _currencyCateData = _convertToMapList(cateList, '币种栏目');
      }

      // 加载交易市场类型数据
      final marketList = storage.getList(_marketTypeKey);
      if (marketList != null) {
        _marketTypeData = _convertToMapList(marketList, '交易市场');
      }

      // 加载主标签数据
      final mainTabList = storage.getList(_mainTabKey);
      if (mainTabList != null) {
        _mainTabData = _convertToMapList(mainTabList, '主标签');
      }

      // 加载最后更新时间
      final updateTimeStr = storage.getString(_lastUpdateTimeKey);
      if (updateTimeStr != null) {
        _lastUpdateTime = DateTime.tryParse(updateTimeStr);
      }

      // 新增：从缓存数据转换为模型数据
      if (_currencyData.isNotEmpty) {
        _currencyModels = _convertToCurrencyModels(_currencyData.cast<dynamic>());
      }
      if (_currencyCateData.isNotEmpty) {
        // 将List<Map<String, dynamic>>转换为List<dynamic>格式供转换方法使用
        final cateDataList = _currencyCateData.cast<dynamic>();
        _categoryModels = _convertToCategoryModels(cateDataList);
      }
      if (_marketTypeData.isNotEmpty) {
        // 将List<Map>转换为Map<String, dynamic>格式供转换方法使用
        final marketTypeMap = <String, dynamic>{};
        for (final item in _marketTypeData) {
          final id = item['id']?.toString() ?? '';
          final name = item['name']?.toString() ?? '';
          if (id.isNotEmpty && name.isNotEmpty) {
            marketTypeMap[id] = name;
          }
        }
        _marketTypeModels = _convertToMarketTypeModels(marketTypeMap);
      }
      if (_mainTabData.isNotEmpty) {
        final mainTabDataList = _mainTabData.cast<dynamic>();
        _mainTabModels = _convertToMainTabModels(mainTabDataList);
      }
    } catch (e) {
      debugPrint('⚠️ 加载本地缓存失败: $e');
      // 缓存数据有问题时清除并重新获取
      await _handleCorruptedCache(e);
    }
  }

  /// 验证缓存数据的完整性
  bool _validateCacheData() {
    try {
      // 检查币种数据的基本结构
      if (_currencyData.isNotEmpty) {
        final sample = _currencyData.first;

        // 检查必要字段
        final requiredFields = ['id', 'symbol', 'market_type'];
        for (final field in requiredFields) {
          if (!sample.containsKey(field)) {
            debugPrint('⚠️ 币种数据缺少必要字段: $field');
            return false;
          }
        }
      }

      // 检查市场类型数据的基本结构
      if (_marketTypeData.isNotEmpty) {
        final sample = _marketTypeData.first;

        // 检查必要字段
        if (!sample.containsKey('id') || !sample.containsKey('name')) {
          debugPrint('⚠️ 市场类型数据缺少必要字段');
          return false;
        }
      }

      return true;
    } catch (e) {
      debugPrint('⚠️ 缓存数据验证异常: $e');
      return false;
    }
  }

  /// 安全转换数据为Map&lt;String, dynamic&gt;列表
  List<Map<String, dynamic>> _convertToMapList(List<dynamic> data, String dataType) {
    try {
      return data.map((item) {
        if (item is Map<String, dynamic>) {
          return item;
        } else if (item is Map) {
          // 处理 Map<dynamic, dynamic> 的情况
          return Map<String, dynamic>.from(item);
        } else {
          debugPrint('⚠️ $dataType 数据格式异常: ${item.runtimeType}');
          throw FormatException('Invalid data format for $dataType');
        }
      }).toList();
    } catch (e) {
      debugPrint('❌ $dataType 数据转换失败: $e');
      throw FormatException('Failed to convert $dataType data: $e');
    }
  }

  /// 处理损坏的缓存数据
  Future<void> _handleCorruptedCache(dynamic error) async {
    try {
      debugPrint('🔧 检测到缓存数据损坏，开始清理...');

      // 清除损坏的缓存
      await clearCache();

      // 重置内存数据
      _currencyData.clear();
      _currencyCateData.clear();
      _marketTypeData.clear();
      _lastUpdateTime = null;

      // 新增：清除模型数据
      _currencyModels.clear();
      _categoryModels.clear();
      _marketTypeModels.clear();

      debugPrint('🗑️ 损坏缓存已清理，将重新获取数据');

      // 标记需要重新获取数据
      // 这里不直接调用获取方法，而是让初始化流程自然处理
    } catch (e) {
      debugPrint('❌ 清理损坏缓存失败: $e');
    }
  }

  /// 保存数据到本地缓存
  Future<void> _saveCachedData() async {
    try {
      final storage = StorageService.instance;

      // 保存币种数据
      await storage.setList(_currencyDataKey, _currencyData);

      // 保存币种栏目数据
      await storage.setList(_currencyCateKey, _currencyCateData);

      // 保存交易市场类型数据
      await storage.setList(_marketTypeKey, _marketTypeData);

      // 保存主标签数据
      await storage.setList(_mainTabKey, _mainTabData);

      // 保存最后更新时间
      if (_lastUpdateTime != null) {
        await storage.setString(_lastUpdateTimeKey, _lastUpdateTime!.toIso8601String());
      }
    } catch (e) {
      debugPrint('❌ 保存本地缓存失败: $e');
    }
  }

  /// 获取币种数据
  Future<bool> fetchCurrencyData() async {
    try {
      debugPrint('🔄 开始获取币种数据...');

      final response = await DioRequest.instance.get(ApiRoute.marketCurrency);

      if (response.success && response.data != null) {
        final List<dynamic> dataList = response.data as List<dynamic>;
        _currencyData = _convertToMapList(dataList, '币种');
        _lastUpdateTime = DateTime.now();

        // 新增：转换为模型数据
        _currencyModels = _convertToCurrencyModels(dataList);

        // 保存到本地缓存
        await _saveCachedData();

        debugPrint('✅ 币种数据获取成功，共 ${_currencyData.length} 个币种，模型数据 ${_currencyModels.length} 个');
        return true;
      } else {
        debugPrint('❌ 币种数据获取失败: ${response.message}');
        return false;
      }
    } catch (e) {
      debugPrint('💥 获取币种数据异常: $e');
      // 如果是数据格式问题，清除缓存
      if (e.toString().contains('type') && e.toString().contains('subtype')) {
        await _handleCorruptedCache(e);
      }
      return false;
    }
  }

  /// 增量更新币种数据
  Future<bool> updateCurrencyData() async {
    try {
      debugPrint('🔄 开始增量更新币种数据...');

      final response = await DioRequest.instance.get(ApiRoute.marketUpdateCurrency);

      if (response.success && response.data != null) {
        final List<dynamic> updateList = response.data as List<dynamic>;
        final List<Map<String, dynamic>> updates = _convertToMapList(updateList, '增量更新');

        // 处理增量更新
        for (final update in updates) {
          final String? symbol = update['symbol'];
          if (symbol != null) {
            // 查找现有数据中的对应项
            final existingIndex = _currencyData.indexWhere((item) => item['symbol'] == symbol);

            if (existingIndex != -1) {
              // 更新现有数据
              _currencyData[existingIndex] = {..._currencyData[existingIndex], ...update};
            } else {
              // 添加新数据
              _currencyData.add(update);
            }
          }
        }

        _lastUpdateTime = DateTime.now();

        // 新增：重新转换为模型数据（增量更新后需要重新转换）
        _currencyModels = _convertToCurrencyModels(_currencyData.cast<dynamic>());

        // 保存到本地缓存
        await _saveCachedData();
        return true;
      } else {
        debugPrint('❌ 币种数据增量更新失败: ${response.message}');
        return false;
      }
    } catch (e) {
      debugPrint('💥 增量更新币种数据异常: $e');
      // 如果是数据格式问题，清除缓存并重新获取完整数据
      if (e.toString().contains('type') && e.toString().contains('subtype')) {
        await _handleCorruptedCache(e);
        return await fetchCurrencyData(); // 重新获取完整数据
      }
      return false;
    }
  }

  /// 获取币种栏目数据
  Future<bool> fetchCurrencyCateData() async {
    try {
      debugPrint('🔄 开始获取币种栏目数据...');

      final response = await DioRequest.instance.get(ApiRoute.marketCurrencyCate);

      if (response.success && response.data != null) {
        final List<dynamic> dataList = response.data as List<dynamic>;
        _currencyCateData = _convertToMapList(dataList, '币种栏目');

        // 新增：转换为模型数据
        _categoryModels = _convertToCategoryModels(dataList);

        // 保存到本地缓存
        await _saveCachedData();

        debugPrint('✅ 币种栏目数据获取成功，共 ${_currencyCateData.length} 个栏目，模型数据 ${_categoryModels.length} 个');
        return true;
      } else {
        debugPrint('❌ 币种栏目数据获取失败: ${response.message}');
        return false;
      }
    } catch (e) {
      debugPrint('💥 获取币种栏目数据异常: $e');
      // 如果是数据格式问题，清除缓存
      if (e.toString().contains('type') && e.toString().contains('subtype')) {
        await _handleCorruptedCache(e);
      }
      return false;
    }
  }

  /// 获取交易市场类型数据
  Future<bool> fetchMarketTypeData() async {
    try {
      final response = await DioRequest.instance.get(ApiRoute.marketType);

      if (response.success && response.data != null) {
        final Map<String, dynamic> dataMap = response.data as Map<String, dynamic>;

        _marketTypeData =
            dataMap.entries.map((entry) {
              return {'id': entry.key, 'name': entry.value.toString()};
            }).toList();

        _marketTypeModels = _convertToMarketTypeModels(dataMap);

        await _saveCachedData();

        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  /// 获取主标签数据
  Future<bool> fetchMainTabData() async {
    try {
      final response = await DioRequest.instance.get(ApiRoute.homeMainTab);

      if (response.success && response.data != null) {
        final List<dynamic> dataList = response.data as List<dynamic>;
        _mainTabData = _convertToMapList(dataList, '主标签');

        _mainTabModels = _convertToMainTabModels(dataList);

        await _saveCachedData();

        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  /// 根据交易对符号查找币种数据
  CurrencyModel? findCurrencyBySymbol(String symbol) {
    try {
      return _currencyModels.firstWhere((item) => item.symbol == symbol);
    } catch (e) {
      return null;
    }
  }

  /// 根据栏目ID获取币种列表
  List<CurrencyModel> getCurrenciesByCateId(int cateId) {
    return _currencyModels.where((item) => item.cateIds.contains(cateId)).toList();
  }

  /// 清除所有缓存数据
  Future<void> clearCache() async {
    try {
      final storage = StorageService.instance;

      // 清除存储中的数据
      await storage.remove(_currencyDataKey);
      await storage.remove(_currencyCateKey);
      await storage.remove(_marketTypeKey);
      await storage.remove(_lastUpdateTimeKey);

      // 清除内存中的数据
      _currencyData.clear();
      _currencyCateData.clear();
      _marketTypeData.clear();
      _lastUpdateTime = null;

      // 新增：清除模型数据
      _currencyModels.clear();
      _categoryModels.clear();
      _marketTypeModels.clear();

      debugPrint('🗑️ 市场数据缓存已清除');
    } catch (e) {
      debugPrint('❌ 清除缓存失败: $e');
    }
  }

  /// 手动刷新所有数据
  ///
  /// 获取最新的币种数据、栏目数据和市场类型数据
  Future<bool> refreshAllData() async {
    try {
      debugPrint('🔄 开始刷新所有市场数据...');

      // 并行获取所有数据
      final results = await Future.wait([fetchCurrencyData(), fetchCurrencyCateData(), fetchMarketTypeData(), fetchMainTabData()]);

      final allSuccess = results.every((result) => result);

      if (allSuccess) {
        debugPrint('✅ 所有市场数据刷新成功');
        debugPrint('📊 数据统计: 币种=${_currencyData.length}, 栏目=${_currencyCateData.length}, 市场=${_marketTypeData.length}');
      } else {
        debugPrint('⚠️ 部分市场数据刷新失败');
      }

      return allSuccess;
    } catch (e) {
      debugPrint('❌ 刷新所有市场数据异常: $e');
      return false;
    }
  }

  /// 检查是否需要更新数据
  ///
  /// 根据最后更新时间判断是否需要更新
  bool shouldUpdate({Duration threshold = const Duration(hours: 1)}) {
    if (_lastUpdateTime == null) return true;

    final now = DateTime.now();
    final timeSinceUpdate = now.difference(_lastUpdateTime!);

    return timeSinceUpdate > threshold;
  }

  /// 获取数据统计信息
  Map<String, dynamic> getDataStats() {
    return {
      'currency_count': _currencyData.length,
      'category_count': _currencyCateData.length,
      'market_count': _marketTypeData.length,
      'currency_models_count': _currencyModels.length,
      'category_models_count': _categoryModels.length,
      'market_type_models_count': _marketTypeModels.length,
      'last_update': _lastUpdateTime?.toIso8601String(),
      'update_timer_active': _updateTimer?.isActive ?? false,
    };
  }

  // 新增：模型转换方法

  /// 转换币种数据为模型列表
  List<CurrencyModel> _convertToCurrencyModels(List<dynamic> dataList) {
    final List<CurrencyModel> models = [];

    for (final item in dataList) {
      try {
        if (item is Map<String, dynamic>) {
          final model = CurrencyModel.fromJson(item);
          models.add(model);
        }
      } catch (e) {
        debugPrint('⚠️ 转换币种模型失败: $e');
      }
    }

    return models;
  }

  /// 转换栏目数据为模型列表
  List<CategoryModel> _convertToCategoryModels(List<dynamic> dataList) {
    final List<CategoryModel> models = [];
    for (final item in dataList) {
      try {
        if (item is Map<String, dynamic>) {
          final model = CategoryModel.fromJson(item);
          models.add(model);
        }
      } catch (e) {
        debugPrint('⚠️ 转换栏目模型失败: $e');
      }
    }
    return models;
  }

  /// 转换市场类型数据为模型列表
  List<MarketTypeModel> _convertToMarketTypeModels(Map<String, dynamic> dataMap) {
    return MarketTypeUtils.fromApiResponse(dataMap);
  }

  /// 转换主标签数据为模型列表
  List<MainTabModel> _convertToMainTabModels(List<dynamic> dataList) {
    final List<MainTabModel> models = [];
    for (final item in dataList) {
      try {
        if (item is Map<String, dynamic>) {
          final model = MainTabModel.fromJson(item);
          models.add(model);
        }
      } catch (e) {
        // 转换失败时跳过该项
      }
    }
    return models;
  }

  /// 更新币种模型中的ticker数据
  static void _updateCurrencyModelTicker(int currencyId, String marketTypeKey, Map<String, dynamic> tickerData) {
    try {
      // 查找对应的币种模型
      final currencyModelIndex = _instance._currencyModels.indexWhere((model) => model.id == currencyId);

      if (currencyModelIndex == -1) {
        return;
      }

      final currencyModel = _instance._currencyModels[currencyModelIndex];

      // 创建新的TickerModel
      final newTickerModel = TickerModel(
        currencyId: currencyId,
        marketType: int.tryParse(marketTypeKey) ?? 1,
        priceChange: (tickerData['price_change'] as num?)?.toDouble() ?? 0.0,
        priceChangeP: (tickerData['price_changeP'] as num?)?.toDouble() ?? 0.0,
        preClosePrice: (tickerData['pre_close_price'] as num?)?.toDouble() ?? 0.0,
        lastPrice: (tickerData['last_price'] as num?)?.toDouble() ?? 0.0,
        lastQty: (tickerData['last_qty'] as num?)?.toDouble() ?? 0.0,
        openPrice: (tickerData['open_price'] as num?)?.toDouble() ?? 0.0,
        highPrice: (tickerData['high_price'] as num?)?.toDouble() ?? 0.0,
        lowPrice: (tickerData['low_price'] as num?)?.toDouble() ?? 0.0,
        volume: (tickerData['volume'] as num?)?.toDouble() ?? 0.0,
        source: 'websocket',
      );

      // 更新币种模型的tickers
      final updatedTickers = Map<String, TickerModel>.from(currencyModel.tickers);
      updatedTickers[marketTypeKey] = newTickerModel;

      // 创建新的币种模型
      final updatedCurrencyModel = CurrencyModel(
        id: currencyModel.id,
        symbol: currencyModel.symbol,
        baseAsset: currencyModel.baseAsset,
        quoteAsset: currencyModel.quoteAsset,
        baseAssetsPrecision: currencyModel.baseAssetsPrecision,
        sPricePrecision: currencyModel.sPricePrecision,
        sQuantityPrecision: currencyModel.sQuantityPrecision,
        mPricePrecision: currencyModel.mPricePrecision,
        mQuantityPrecision: currencyModel.mQuantityPrecision,
        isSpotTrade: currencyModel.isSpotTrade,
        isMarginTrade: currencyModel.isMarginTrade,
        marketType: currencyModel.marketType,
        tradingStart: currencyModel.tradingStart,
        tradingEnd: currencyModel.tradingEnd,
        tradingTimezone: currencyModel.tradingTimezone,
        status: currencyModel.status,
        logo: currencyModel.logo,
        cateIds: currencyModel.cateIds,
        tickers: updatedTickers,
        quoteAssetsId: currencyModel.quoteAssetsId
      );

      // 替换模型列表中的数据
      _instance._currencyModels[currencyModelIndex] = updatedCurrencyModel;
    } catch (e) {
      debugPrint('❌ _updateCurrencyModelTicker失败: $e');
    }
  }

  /// 从模型数据中获取指定币种的ticker数据
  ///
  /// [currencyId] 币种ID
  /// [marketType] 市场类型，可选
  /// 返回TickerModel，如果指定了marketType则返回对应市场的数据
  static TickerModel? getTickerModelData(int currencyId, {int? marketType}) {
    try {
      final currencyModel = _instance._currencyModels.firstWhere(
        (model) => model.id == currencyId,
        orElse: () => throw StateError('Currency not found'),
      );

      if (marketType != null) {
        // 返回指定市场类型的ticker数据
        return currencyModel.tickers[marketType.toString()];
      } else {
        // 返回第一个可用的ticker数据（通常是现货市场）
        if (currencyModel.tickers.isNotEmpty) {
          return currencyModel.tickers.values.first;
        }
        return null;
      }
    } catch (e) {
      debugPrint('❌ getTickerModelData失败: $e');
      return null;
    }
  }

  /// 从模型数据中获取币种信息
  ///
  /// [currencyId] 币种ID
  /// 返回CurrencyModel
  static CurrencyModel? getCurrencyModel(int currencyId) {
    try {
      return _instance._currencyModels.firstWhere((model) => model.id == currencyId, orElse: () => throw StateError('Currency not found'));
    } catch (e) {
      debugPrint('❌ getCurrencyModel失败: $e');
      return null;
    }
  }

  /// 静态更新ticker数据
  ///
  /// [tickerData] ticker数据，包含currency_id、market_type等字段
  /// [marketType] 市场类型
  static void updateTickerData(Map<String, dynamic> tickerData, int marketType) {
    try {
      final currencyId = tickerData['currency_id'] as int?;
      if (currencyId == null) {
        debugPrint('⚠️ updateTickerData: currency_id为空');
        return;
      }

      // 查找对应的币种数据
      final currencyIndex = _instance._currencyData.indexWhere((currency) => currency['id'] == currencyId);

      if (currencyIndex == -1) {
        debugPrint('⚠️ updateTickerData: 未找到currency_id=$currencyId的币种');
        return;
      }

      final currency = _instance._currencyData[currencyIndex];

      // 确保tickers字段存在
      if (currency['tickers'] == null) {
        currency['tickers'] = <String, dynamic>{};
      }

      final tickers = currency['tickers'] as Map;
      final marketTypeKey = marketType.toString();

      // 更新对应market_type的ticker数据
      tickers[marketTypeKey] = {
        'currency_id': currencyId,
        'market_type': marketType,
        'price_change': tickerData['price_change'] ?? 0.0,
        'price_changeP': tickerData['price_changeP'] ?? 0.0,
        'pre_close_price': tickerData['pre_close_price'] ?? 0.0,
        'last_price': tickerData['last_price'] ?? 0.0,
        'last_qty': tickerData['last_qty'] ?? 0.0,
        'open_price': tickerData['open_price'] ?? 0.0,
        'high_price': tickerData['high_price'] ?? 0.0,
        'low_price': tickerData['low_price'] ?? 0.0,
        'volume': tickerData['volume'] ?? 0.0,
        'timestamp': tickerData['timestamp'] ?? DateTime.now().millisecondsSinceEpoch,
        'source': 'websocket', // 标记数据来源
      };

      // 新增：同时更新模型数据中的ticker
      _updateCurrencyModelTicker(currencyId, marketTypeKey, tickerData);
    } catch (e) {
      debugPrint('❌ updateTickerData失败: $e');
    }
  }

  /// 批量更新ticker数据
  ///
  /// [tickerList] ticker数据列表
  /// [marketType] 市场类型
  static void updateTickerDataBatch(List<Map<String, dynamic>> tickerList, int marketType) {
    try {
      for (final tickerData in tickerList) {
        updateTickerData(tickerData, marketType);
      }
    } catch (e) {
      debugPrint('❌ 批量更新ticker数据失败: $e');
    }
  }

  /// 获取指定币种的ticker数据
  ///
  /// [currencyId] 币种ID
  /// [marketType] 市场类型，可选
  /// 返回ticker数据，如果指定了marketType则返回对应市场的数据，否则返回所有市场的数据
  static Map<String, dynamic>? getTickerData(int currencyId, {int? marketType}) {
    try {
      final currency = _instance._currencyData.firstWhere((currency) => currency['id'] == currencyId, orElse: () => <String, dynamic>{});

      if (currency.isEmpty) {
        return null;
      }

      final tickers = currency['tickers'] as Map?;
      if (tickers == null) {
        return null;
      }
      if (marketType != null) {
        // 返回指定市场类型的ticker数据
        final tickerData = tickers[marketType.toString()];
        if (tickerData is Map) {
          final result = Map<String, dynamic>.from(tickerData);
          result['symbol'] = currency['base_asset'];
          return result;
        } else {
          return null;
        }
      } else {
        // 返回所有市场的ticker数据
        return Map<String, dynamic>.from(tickers);
      }
    } catch (e) {
      debugPrint('❌ getTickerData失败: $e');
      return null;
    }
  }

  /// 启动缓存写入定时器
  void _startCacheWriteTimer() {
    _cacheWriteTimer?.cancel();
    _cacheWriteTimer = Timer.periodic(_cacheWriteInterval, (timer) {
      _writeCurrencyDataToCache();
    });
    debugPrint('🕒 缓存写入定时器已启动，每${_cacheWriteInterval.inSeconds}秒写入一次');
  }

  /// 将currencyData写入本地缓存
  Future<void> _writeCurrencyDataToCache() async {
    try {
      if (_currencyData.isEmpty) {
        return;
      }

      final storage = StorageService.instance;

      // 使用现有的key和方法保存币种数据
      await storage.setList(_currencyDataKey, _currencyData);

      // 更新缓存时间戳
      await storage.setInt(_lastUpdateTimeKey, DateTime.now().millisecondsSinceEpoch);

      debugPrint('💾 已将${_currencyData.length}个币种数据写入缓存');
    } catch (e) {
      debugPrint('❌ 写入币种数据缓存失败: $e');
    }
  }

  /// 手动触发缓存写入
  Future<void> forceCacheWrite() async {
    await _writeCurrencyDataToCache();
  }
}
