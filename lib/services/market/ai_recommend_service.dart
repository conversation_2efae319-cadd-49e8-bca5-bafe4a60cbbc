import 'package:flutter/foundation.dart';
import '../clients/dio_request.dart';
import '../config/api_route.dart';
import '../market/index.dart';
import '../common/common_service.dart';
import '../../l10n/managers/language_manager.dart';
import '../../pages/market/tabs/opportunities/models/ai_pick_data.dart';

class AiRecommendService {
  static final AiRecommendService _instance = AiRecommendService._internal();
  static AiRecommendService get instance => _instance;
  AiRecommendService._internal();

  /// 获取AI推荐数据
  Future<List<AiPickData>> fetchAiRecommendData({int limit = 10}) async {
    try {
      debugPrint('🤖 开始获取AI推荐数据...');

      final response = await DioRequest.instance.get(ApiRoute.aiRecommend, queryParams: {'limit': limit});

      if (response.success && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        final List<dynamic> dataList = responseData['data'] ?? [];

        debugPrint('✅ AI推荐数据获取成功，共 ${dataList.length} 条');

        final List<AiPickData> aiPickList = [];

        for (final item in dataList) {
          try {
            final aiPickData = await _convertToAiPickData(item);
            if (aiPickData != null) {
              aiPickList.add(aiPickData);
            }
          } catch (e) {
            debugPrint('⚠️ 转换AI推荐数据失败: $e');
          }
        }

        return aiPickList;
      } else {
        debugPrint('❌ AI推荐数据获取失败: ${response.message}');
        return [];
      }
    } catch (e) {
      debugPrint('💥 获取AI推荐数据异常: $e');
      return [];
    }
  }

  /// 将API数据转换为AiPickData
  Future<AiPickData?> _convertToAiPickData(Map<String, dynamic> item) async {
    try {
      final int currencyId = item['currency_id'] ?? 0;
      final Map<String, dynamic> metrics = item['metrics'] ?? {};
      final int up = item['up'] ?? 0;
      final int down = item['down'] ?? 0;

      // 从MarketService获取币种信息
      CurrencyModel? currency;
      try {
        currency = MarketService.instance.currencyModels.firstWhere((c) => c.id == currencyId);
      } catch (e) {
        debugPrint('⚠️ 未找到币种ID: $currencyId');
        return null;
      }

      final String baseAsset = currency.baseAsset;
      final String logoUrl = currency.logo ?? '';

      // 获取ticker数据
      double currentPrice = 0.0;
      double changePercentage = 0.0;

      // 从模型数据中获取现货市场的ticker数据
      final tickerData = currency.tickers['1']; // 现货市场数据
      if (tickerData != null) {
        currentPrice = tickerData.lastPrice;
        changePercentage = tickerData.priceChangeP;
      }

      // 计算看多看空比例
      final total = up + down;
      double bullishRatio = 0.0;
      double bearishRatio = 0.0;

      if (total > 0) {
        bullishRatio = (up / total) * 100;
        bearishRatio = (down / total) * 100;
      }

      // 获取情绪摘要
      final sentimentSummary = _getSentimentSummary(metrics);

      // 获取情绪评分标签作为tag
      final tag = _getSentimentScoreLabel(metrics);

      return AiPickData(
        symbol: baseAsset,
        tag: tag,
        changePercentage: changePercentage,
        currentPrice: currentPrice,
        bullishRatio: bullishRatio,
        bearishRatio: bearishRatio,
        logoUrl: logoUrl,
        sentimentSummary: sentimentSummary,
      );
    } catch (e) {
      debugPrint('💥 转换AI推荐数据异常: $e');
      return null;
    }
  }

  /// 获取情绪摘要
  String _getSentimentSummary(Map<String, dynamic> metrics) {
    final sentimentSummary = metrics['sentiment_summary'];
    if (sentimentSummary != null && sentimentSummary['value'] != null) {
      final List<dynamic> valueList = sentimentSummary['value'];

      // 获取当前语言
      String targetLang = _getCurrentLanguageCode();

      return CommonService.getLocalizedText(valueList, targetLang: targetLang, fallbackLang: 'en');
    }
    return '';
  }

  /// 获取情绪评分标签
  String _getSentimentScoreLabel(Map<String, dynamic> metrics) {
    final sentimentScore = metrics['sentiment_score'];
    if (sentimentScore != null && sentimentScore['valueLabel'] != null) {
      final List<dynamic> valueLabelList = sentimentScore['valueLabel'];

      // 获取当前语言
      String targetLang = _getCurrentLanguageCode();

      return CommonService.getLocalizedText(valueLabelList, targetLang: targetLang, fallbackLang: 'en');
    }
    return 'AI推荐';
  }

  /// 获取当前语言代码
  String _getCurrentLanguageCode() {
    final currentLocale = LanguageManager().currentLocale;

    if (currentLocale.languageCode == 'en') {
      return 'en';
    } else if (currentLocale.countryCode == 'TW') {
      return 'zh_TW';
    } else {
      return 'zh_CN';
    }
  }
}
