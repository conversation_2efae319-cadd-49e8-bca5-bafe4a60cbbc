/*
  市场类型数据模型
*/

class MarketTypeModel {
  final int id;
  final String name;

  const MarketTypeModel({
    required this.id,
    required this.name,
  });

  factory MarketTypeModel.fromMapEntry(MapEntry<String, dynamic> entry) {
    return MarketTypeModel(
      id: int.tryParse(entry.key) ?? 0,
      name: entry.value?.toString() ?? '',
    );
  }

  factory MarketTypeModel.fromJson(Map<String, dynamic> json) {
    return MarketTypeModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }

  @override
  String toString() {
    return 'MarketTypeModel(id: $id, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MarketTypeModel && other.id == id && other.name == name;
  }

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
}

/// 市场类型工具类
class MarketTypeUtils {
  /// 从API返回的Map数据转换为MarketTypeModel列表
  static List<MarketTypeModel> fromApiResponse(Map<String, dynamic> apiData) {
    final List<MarketTypeModel> marketTypes = [];
    
    apiData.forEach((key, value) {
      final marketType = MarketTypeModel.fromMapEntry(MapEntry(key, value));
      marketTypes.add(marketType);
    });
    
    // 按ID排序
    marketTypes.sort((a, b) => a.id.compareTo(b.id));
    
    return marketTypes;
  }

  /// 根据ID查找市场类型名称
  static String getMarketTypeName(List<MarketTypeModel> marketTypes, int id) {
    try {
      final marketType = marketTypes.firstWhere((type) => type.id == id);
      return marketType.name;
    } catch (e) {
      return '';
    }
  }

  /// 根据名称查找市场类型ID
  static int getMarketTypeId(List<MarketTypeModel> marketTypes, String name) {
    try {
      final marketType = marketTypes.firstWhere((type) => type.name == name);
      return marketType.id;
    } catch (e) {
      return 0;
    }
  }

  /// 检查市场类型是否存在
  static bool hasMarketType(List<MarketTypeModel> marketTypes, int id) {
    return marketTypes.any((type) => type.id == id);
  }
}
