/*
  栏目数据模型
*/

class CategoryModel {
  final int id;
  final List<LocalizedText> cateName;
  final List<LocalizedText> cateDesc;
  final int sort;
  final int status;
  final String createdAt;
  final String updatedAt;

  const CategoryModel({
    required this.id,
    required this.cateName,
    required this.cateDesc,
    required this.sort,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    // 解析多语言名称
    final List<LocalizedText> nameList = [];
    final cateNameData = json['cate_name'];
    if (cateNameData != null && cateNameData is List) {
      for (final item in cateNameData) {
        if (item is Map) {
          // 安全转换为Map<String, dynamic>
          final itemMap = Map<String, dynamic>.from(item);
          nameList.add(LocalizedText.fromJson(itemMap));
        }
      }
    }

    // 解析多语言描述
    final List<LocalizedText> descList = [];
    final cateDescData = json['cate_desc'];
    if (cateDescData != null && cateDescData is List) {
      for (final item in cateDescData) {
        if (item is Map) {
          // 安全转换为Map<String, dynamic>
          final itemMap = Map<String, dynamic>.from(item);
          descList.add(LocalizedText.fromJson(itemMap));
        }
      }
    }

    return CategoryModel(
      id: json['id'] ?? 0,
      cateName: nameList,
      cateDesc: descList,
      sort: json['sort'] ?? 0,
      status: json['status'] ?? 1,
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'cate_name': cateName.map((e) => e.toJson()).toList(),
      'cate_desc': cateDesc.map((e) => e.toJson()).toList(),
      'sort': sort,
      'status': status,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  /// 获取指定语言的栏目名称
  String getName({String targetLang = 'zh_cn', String fallbackLang = 'en'}) {
    return _getLocalizedText(cateName, targetLang: targetLang, fallbackLang: fallbackLang);
  }

  /// 获取指定语言的栏目描述
  String getDescription({String targetLang = 'zh_cn', String fallbackLang = 'en'}) {
    return _getLocalizedText(cateDesc, targetLang: targetLang, fallbackLang: fallbackLang);
  }

  /// 获取本地化文本的通用方法
  String _getLocalizedText(List<LocalizedText> textList, {String targetLang = 'zh_cn', String fallbackLang = 'en'}) {
    if (textList.isEmpty) return '';

    // 尝试精确匹配目标语言
    for (final text in textList) {
      if (text.lang == targetLang && text.text.isNotEmpty) {
        return text.text;
      }
    }

    // 尝试模糊匹配目标语言
    final targetLangPrefix = targetLang.split('_').first;
    for (final text in textList) {
      if (text.lang.startsWith(targetLangPrefix) && text.text.isNotEmpty) {
        return text.text;
      }
    }

    // 尝试精确匹配备用语言
    for (final text in textList) {
      if (text.lang == fallbackLang && text.text.isNotEmpty) {
        return text.text;
      }
    }

    // 尝试模糊匹配备用语言
    final fallbackLangPrefix = fallbackLang.split('_').first;
    for (final text in textList) {
      if (text.lang.startsWith(fallbackLangPrefix) && text.text.isNotEmpty) {
        return text.text;
      }
    }

    // 返回第一个有效的文本
    for (final text in textList) {
      if (text.text.isNotEmpty) {
        return text.text;
      }
    }

    return '';
  }

  @override
  String toString() {
    return 'CategoryModel(id: $id, name: ${getName()}, sort: $sort, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class LocalizedText {
  final String lang;
  final String text;

  const LocalizedText({required this.lang, required this.text});

  factory LocalizedText.fromJson(Map<String, dynamic> json) {
    return LocalizedText(lang: json['lang'] ?? '', text: json['text'] ?? '');
  }

  Map<String, dynamic> toJson() {
    return {'lang': lang, 'text': text};
  }

  @override
  String toString() {
    return 'LocalizedText(lang: $lang, text: $text)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocalizedText && other.lang == lang && other.text == text;
  }

  @override
  int get hashCode => lang.hashCode ^ text.hashCode;
}

/// 栏目工具类
class CategoryUtils {
  /// 根据当前语言环境获取栏目名称
  static String getCategoryName(CategoryModel category, String currentLanguage) {
    String targetLang = 'zh_cn';

    if (currentLanguage == 'en') {
      targetLang = 'en';
    } else if (currentLanguage.contains('TW')) {
      targetLang = 'zh_tw';
    }

    return category.getName(targetLang: targetLang, fallbackLang: 'en');
  }

  /// 根据ID查找栏目
  static CategoryModel? findCategoryById(List<CategoryModel> categories, int id) {
    try {
      return categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 获取有效的栏目列表（状态为1）
  static List<CategoryModel> getActiveCategories(List<CategoryModel> categories) {
    return categories.where((category) => category.status == 1).toList();
  }

  /// 按排序字段排序栏目
  static List<CategoryModel> sortCategories(List<CategoryModel> categories) {
    final sortedList = List<CategoryModel>.from(categories);
    sortedList.sort((a, b) => a.sort.compareTo(b.sort));
    return sortedList;
  }
}
