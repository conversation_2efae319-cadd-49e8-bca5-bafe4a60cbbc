/*
  主标签数据模型
*/

class MainTabModel {
  final List<LocalizedText> title;
  final List<SubTabModel> sub;

  const MainTabModel({
    required this.title,
    required this.sub,
  });

  factory MainTabModel.fromJson(Map<String, dynamic> json) {
    final List<LocalizedText> titleList = [];
    final titleData = json['title'];
    if (titleData != null && titleData is List) {
      for (final item in titleData) {
        if (item is Map) {
          final itemMap = Map<String, dynamic>.from(item);
          titleList.add(LocalizedText.fromJson(itemMap));
        }
      }
    }

    final List<SubTabModel> subList = [];
    final subData = json['sub'];
    if (subData != null) {
      if (subData is List) {
        for (final item in subData) {
          if (item is Map) {
            final itemMap = Map<String, dynamic>.from(item);
            subList.add(SubTabModel.fromJson(itemMap));
          }
        }
      } else if (subData is Map) {
        final itemMap = Map<String, dynamic>.from(subData);
        subList.add(SubTabModel.fromJson(itemMap));
      }
    }

    return MainTabModel(
      title: titleList,
      sub: subList,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title.map((e) => e.toJson()).toList(),
      'sub': sub.map((e) => e.toJson()).toList(),
    };
  }

  String getTitle({String targetLang = 'zh_cn', String fallbackLang = 'en'}) {
    return _getLocalizedText(title, targetLang: targetLang, fallbackLang: fallbackLang);
  }

  String _getLocalizedText(List<LocalizedText> textList, {String targetLang = 'zh_cn', String fallbackLang = 'en'}) {
    if (textList.isEmpty) return '';

    LocalizedText? targetText = textList.cast<LocalizedText?>().firstWhere(
      (text) => text?.lang.toLowerCase() == targetLang.toLowerCase(),
      orElse: () => null,
    );

    if (targetText != null && targetText.text.isNotEmpty) {
      return targetText.text;
    }

    LocalizedText? fallbackText = textList.cast<LocalizedText?>().firstWhere(
      (text) => text?.lang.toLowerCase() == fallbackLang.toLowerCase(),
      orElse: () => null,
    );

    if (fallbackText != null && fallbackText.text.isNotEmpty) {
      return fallbackText.text;
    }

    return textList.first.text;
  }
}

class SubTabModel {
  final List<LocalizedText> title;
  final String source;
  final int sort;
  final String sortField;
  final int marketType;
  final String field;
  final int limit;

  const SubTabModel({
    required this.title,
    required this.source,
    required this.sort,
    required this.sortField,
    required this.marketType,
    required this.field,
    required this.limit,
  });

  factory SubTabModel.fromJson(Map<String, dynamic> json) {
    final List<LocalizedText> titleList = [];
    final titleData = json['title'];
    if (titleData != null && titleData is List) {
      for (final item in titleData) {
        if (item is Map) {
          final itemMap = Map<String, dynamic>.from(item);
          titleList.add(LocalizedText.fromJson(itemMap));
        }
      }
    }

    return SubTabModel(
      title: titleList,
      source: json['source']?.toString() ?? '',
      sort: json['sort'] ?? 0,
      sortField: json['sort_filed']?.toString() ?? '',
      marketType: json['market_type'] ?? 0,
      field: json['field']?.toString() ?? '',
      limit: json['limit'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title.map((e) => e.toJson()).toList(),
      'source': source,
      'sort': sort,
      'sort_filed': sortField,
      'market_type': marketType,
      'field': field,
      'limit': limit,
    };
  }

  String getTitle({String targetLang = 'zh_cn', String fallbackLang = 'en'}) {
    return _getLocalizedText(title, targetLang: targetLang, fallbackLang: fallbackLang);
  }

  String _getLocalizedText(List<LocalizedText> textList, {String targetLang = 'zh_cn', String fallbackLang = 'en'}) {
    if (textList.isEmpty) return '';

    LocalizedText? targetText = textList.cast<LocalizedText?>().firstWhere(
      (text) => text?.lang.toLowerCase() == targetLang.toLowerCase(),
      orElse: () => null,
    );

    if (targetText != null && targetText.text.isNotEmpty) {
      return targetText.text;
    }

    LocalizedText? fallbackText = textList.cast<LocalizedText?>().firstWhere(
      (text) => text?.lang.toLowerCase() == fallbackLang.toLowerCase(),
      orElse: () => null,
    );

    if (fallbackText != null && fallbackText.text.isNotEmpty) {
      return fallbackText.text;
    }

    return textList.first.text;
  }
}

class LocalizedText {
  final String lang;
  final String text;

  const LocalizedText({
    required this.lang,
    required this.text,
  });

  factory LocalizedText.fromJson(Map<String, dynamic> json) {
    return LocalizedText(
      lang: json['lang']?.toString() ?? '',
      text: json['text']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'lang': lang,
      'text': text,
    };
  }
}
