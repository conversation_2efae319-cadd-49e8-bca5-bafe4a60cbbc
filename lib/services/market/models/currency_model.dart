/*
  币种数据模型
*/

import 'package:flutter/foundation.dart';

class CurrencyModel {
  final int id;
  final String symbol;
  final String baseAsset;
  final String quoteAsset;
  final int quoteAssetsId;
  final int baseAssetsPrecision;
  final int sPricePrecision;
  final int sQuantityPrecision;
  final int mPricePrecision;
  final int mQuantityPrecision;
  final int isSpotTrade;
  final int isMarginTrade;
  final int marketType;
  final String tradingStart;
  final String tradingEnd;
  final String tradingTimezone;
  final int status;
  final String? logo;
  final List<int> cateIds;
  final Map<String, TickerModel> tickers;

  const CurrencyModel({
    required this.id,
    required this.symbol,
    required this.baseAsset,
    required this.quoteAsset,
    required this.baseAssetsPrecision,
    required this.sPricePrecision,
    required this.sQuantityPrecision,
    required this.mPricePrecision,
    required this.mQuantityPrecision,
    required this.isSpotTrade,
    required this.isMarginTrade,
    required this.marketType,
    required this.tradingStart,
    required this.tradingEnd,
    required this.tradingTimezone,
    required this.status,
    this.logo,
    required this.cateIds,
    required this.tickers,
    required this.quoteAssetsId
  });

  factory CurrencyModel.fromJson(Map<String, dynamic> json) {
    // 解析tickers数据
    final Map<String, TickerModel> tickersMap = {};
    final tickersData = json['tickers'];

    if (tickersData != null && tickersData is Map) {
      tickersData.forEach((key, value) {
        if (value is Map<String, dynamic>) {
          try {
            final tickerModel = TickerModel.fromJson(value);
            tickersMap[key] = tickerModel;
          } catch (e) {
            debugPrint('❌ 解析ticker失败: $e');
          }
        } else if (value is Map) {
          // 处理 Map<dynamic, dynamic> 的情况
          try {
            final convertedValue = Map<String, dynamic>.from(value);
            final tickerModel = TickerModel.fromJson(convertedValue);
            tickersMap[key] = tickerModel;
          } catch (e) {
            debugPrint('❌ 解析ticker失败: $e');
          }
        }
      });
    }

    // 解析cateIds
    final List<int> cateIdsList = [];
    final cateIdsData = json['cateIds'];
    if (cateIdsData != null && cateIdsData is List) {
      for (final item in cateIdsData) {
        if (item is int) {
          cateIdsList.add(item);
        }
      }
    }

    return CurrencyModel(
      id: json['id'] ?? 0,
      symbol: json['symbol'] ?? '',
      baseAsset: json['base_asset'] ?? '',
      quoteAsset: json['quote_asset'] ?? '',
      baseAssetsPrecision: json['base_assets_precision'] ?? 8,
      sPricePrecision: json['s_price_precision'] ?? 8,
      sQuantityPrecision: json['s_quantity_precision'] ?? 8,
      mPricePrecision: json['m_price_precision'] ?? 2,
      mQuantityPrecision: json['m_quantity_precision'] ?? 3,
      isSpotTrade: json['is_spotTrade'] ?? 0,
      isMarginTrade: json['is_marginTrade'] ?? 0,
      marketType: json['market_type'] ?? 1,
      tradingStart: json['trading_start'] ?? '0',
      tradingEnd: json['trading_end'] ?? '0',
      tradingTimezone: json['trading_timezone'] ?? 'UTC+8',
      status: json['status'] ?? 1,
      logo: json['logo'],
      cateIds: cateIdsList,
      tickers: tickersMap,
      quoteAssetsId:json['quote_assets_id']
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'symbol': symbol,
      'base_asset': baseAsset,
      'quote_asset': quoteAsset,
      'quote_assets_id' : quoteAssetsId,
      'base_assets_precision': baseAssetsPrecision,
      's_price_precision': sPricePrecision,
      's_quantity_precision': sQuantityPrecision,
      'm_price_precision': mPricePrecision,
      'm_quantity_precision': mQuantityPrecision,
      'is_spotTrade': isSpotTrade,
      'is_marginTrade': isMarginTrade,
      'market_type': marketType,
      'trading_start': tradingStart,
      'trading_end': tradingEnd,
      'trading_timezone': tradingTimezone,
      'status': status,
      'logo': logo,
      'cateIds': cateIds,
      'tickers': tickers.map((key, value) => MapEntry(key, value.toJson())),
    };
  }

  @override
  String toString() {
    return 'CurrencyModel(id: $id, symbol: $symbol, baseAsset: $baseAsset, quoteAsset: $quoteAsset)';
  }
}

class TickerModel {
  final int currencyId;
  final int marketType;
  final double priceChange;
  final double priceChangeP;
  final double preClosePrice;
  final double lastPrice;
  final double lastQty;
  final double openPrice;
  final double highPrice;
  final double lowPrice;
  final double volume;
  final String source;

  const TickerModel({
    required this.currencyId,
    required this.marketType,
    required this.priceChange,
    required this.priceChangeP,
    required this.preClosePrice,
    required this.lastPrice,
    required this.lastQty,
    required this.openPrice,
    required this.highPrice,
    required this.lowPrice,
    required this.volume,
    required this.source,
  });

  factory TickerModel.fromJson(Map<String, dynamic> json) {
    return TickerModel(
      currencyId: json['currency_id'] ?? 0,
      marketType: json['market_type'] ?? 1,
      priceChange: (json['price_change'] as num?)?.toDouble() ?? 0.0,
      priceChangeP: (json['price_changeP'] as num?)?.toDouble() ?? 0.0,
      preClosePrice: (json['pre_close_price'] as num?)?.toDouble() ?? 0.0,
      lastPrice: (json['last_price'] as num?)?.toDouble() ?? 0.0,
      lastQty: (json['last_qty'] as num?)?.toDouble() ?? 0.0,
      openPrice: (json['open_price'] as num?)?.toDouble() ?? 0.0,
      highPrice: (json['high_price'] as num?)?.toDouble() ?? 0.0,
      lowPrice: (json['low_price'] as num?)?.toDouble() ?? 0.0,
      volume: (json['volume'] as num?)?.toDouble() ?? 0.0,
      source: json['source'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currency_id': currencyId,
      'market_type': marketType,
      'price_change': priceChange,
      'price_changeP': priceChangeP,
      'pre_close_price': preClosePrice,
      'last_price': lastPrice,
      'last_qty': lastQty,
      'open_price': openPrice,
      'high_price': highPrice,
      'low_price': lowPrice,
      'volume': volume,
      'source': source,
    };
  }

  @override
  String toString() {
    return 'TickerModel(currencyId: $currencyId, marketType: $marketType, lastPrice: $lastPrice, priceChangeP: $priceChangeP)';
  }
}
