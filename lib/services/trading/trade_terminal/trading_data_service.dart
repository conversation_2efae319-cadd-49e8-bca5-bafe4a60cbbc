import '../../clients/external_request.dart';

/// 合约持仓量数据模型
class OpenInterestData {
  final String symbol;
  final double sumOpenInterest;
  final double sumOpenInterestValue;
  final int timestamp;
  final DateTime dateTime;

  OpenInterestData({
    required this.symbol,
    required this.sumOpenInterest,
    required this.sumOpenInterestValue,
    required this.timestamp,
    required this.dateTime,
  });

  factory OpenInterestData.fromJson(Map<String, dynamic> json) {
    final timestamp = int.parse(json['timestamp'].toString());
    return OpenInterestData(
      symbol: json['symbol'].toString(),
      sumOpenInterest: double.parse(json['sumOpenInterest'].toString()),
      sumOpenInterestValue: double.parse(
        json['sumOpenInterestValue'].toString(),
      ),
      timestamp: timestamp,
      dateTime: DateTime.fromMillisecondsSinceEpoch(timestamp),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'sumOpenInterest': sumOpenInterest.toString(),
      'sumOpenInterestValue': sumOpenInterestValue.toString(),
      'timestamp': timestamp.toString(),
    };
  }
}

/// 多空人数比值数据模型
class LongShortRatioData {
  final String symbol;
  final double longShortRatio;
  final double longAccount;
  final double shortAccount;
  final int timestamp;
  final DateTime dateTime;

  LongShortRatioData({
    required this.symbol,
    required this.longShortRatio,
    required this.longAccount,
    required this.shortAccount,
    required this.timestamp,
    required this.dateTime,
  });

  factory LongShortRatioData.fromBinanceJson(Map<String, dynamic> json) {
    final timestamp = int.parse(json['timestamp'].toString());
    return LongShortRatioData(
      symbol: json['symbol'].toString(),
      longShortRatio: double.parse(json['longShortRatio'].toString()),
      longAccount: double.parse(json['longAccount'].toString()),
      shortAccount: double.parse(json['shortAccount'].toString()),
      timestamp: timestamp,
      dateTime: DateTime.fromMillisecondsSinceEpoch(timestamp),
    );
  }

  factory LongShortRatioData.fromOkxData(String symbol, List<dynamic> item) {
    final timestamp = int.parse(item[0].toString());
    final ratio = double.parse(item[1].toString());

    // 通过OKX的比例计算多空账户百分比
    // ratio = longAccount / shortAccount
    // longAccount + shortAccount = 1
    // 解方程得到：longAccount = ratio / (ratio + 1), shortAccount = 1 / (ratio + 1)
    final longAccount = ratio / (ratio + 1);
    final shortAccount = 1 / (ratio + 1);

    return LongShortRatioData(
      symbol: symbol,
      longShortRatio: ratio,
      longAccount: longAccount,
      shortAccount: shortAccount,
      timestamp: timestamp,
      dateTime: DateTime.fromMillisecondsSinceEpoch(timestamp),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'longShortRatio': longShortRatio.toString(),
      'longAccount': longAccount.toString(),
      'shortAccount': shortAccount.toString(),
      'timestamp': timestamp.toString(),
    };
  }
}

/// 大账户多空比数据模型
class TopAccountRatioData {
  final String symbol;
  final double longShortRatio;
  final double longAccount;
  final double shortAccount;
  final int timestamp;
  final DateTime dateTime;

  TopAccountRatioData({
    required this.symbol,
    required this.longShortRatio,
    required this.longAccount,
    required this.shortAccount,
    required this.timestamp,
    required this.dateTime,
  });

  factory TopAccountRatioData.fromBinanceJson(Map<String, dynamic> json) {
    final timestamp = int.parse(json['timestamp'].toString());
    return TopAccountRatioData(
      symbol: json['symbol'].toString(),
      longShortRatio: double.parse(json['longShortRatio'].toString()),
      longAccount: double.parse(json['longAccount'].toString()),
      shortAccount: double.parse(json['shortAccount'].toString()),
      timestamp: timestamp,
      dateTime: DateTime.fromMillisecondsSinceEpoch(timestamp),
    );
  }

  factory TopAccountRatioData.fromOkxData(String symbol, List<dynamic> item) {
    final timestamp = int.parse(item[0].toString());
    final ratio = double.parse(item[1].toString());

    // 通过OKX的比例计算多空账户百分比
    // ratio = longAccount / shortAccount
    // longAccount + shortAccount = 1
    // 解方程得到：longAccount = ratio / (ratio + 1), shortAccount = 1 / (ratio + 1)
    final longAccount = ratio / (ratio + 1);
    final shortAccount = 1 / (ratio + 1);

    return TopAccountRatioData(
      symbol: symbol,
      longShortRatio: ratio,
      longAccount: longAccount,
      shortAccount: shortAccount,
      timestamp: timestamp,
      dateTime: DateTime.fromMillisecondsSinceEpoch(timestamp),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'longShortRatio': longShortRatio.toString(),
      'longAccount': longAccount.toString(),
      'shortAccount': shortAccount.toString(),
      'timestamp': timestamp.toString(),
    };
  }
}

/// 大账户持仓量多空比数据模型
class TopPositionRatioData {
  final String symbol;
  final double longShortRatio;
  final double longAccount;
  final double shortAccount;
  final int timestamp;
  final DateTime dateTime;

  TopPositionRatioData({
    required this.symbol,
    required this.longShortRatio,
    required this.longAccount,
    required this.shortAccount,
    required this.timestamp,
    required this.dateTime,
  });

  factory TopPositionRatioData.fromBinanceJson(Map<String, dynamic> json) {
    final timestamp = int.parse(json['timestamp'].toString());
    return TopPositionRatioData(
      symbol: json['symbol'].toString(),
      longShortRatio: double.parse(json['longShortRatio'].toString()),
      longAccount: double.parse(json['longAccount'].toString()),
      shortAccount: double.parse(json['shortAccount'].toString()),
      timestamp: timestamp,
      dateTime: DateTime.fromMillisecondsSinceEpoch(timestamp),
    );
  }

  factory TopPositionRatioData.fromOkxData(String symbol, List<dynamic> item) {
    final timestamp = int.parse(item[0].toString());
    final ratio = double.parse(item[1].toString());

    // 通过OKX的比例计算多空持仓量百分比
    // ratio = longAccount / shortAccount
    // longAccount + shortAccount = 1
    // 解方程得到：longAccount = ratio / (ratio + 1), shortAccount = 1 / (ratio + 1)
    final longAccount = ratio / (ratio + 1);
    final shortAccount = 1 / (ratio + 1);

    return TopPositionRatioData(
      symbol: symbol,
      longShortRatio: ratio,
      longAccount: longAccount,
      shortAccount: shortAccount,
      timestamp: timestamp,
      dateTime: DateTime.fromMillisecondsSinceEpoch(timestamp),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'longShortRatio': longShortRatio.toString(),
      'longAccount': longAccount.toString(),
      'shortAccount': shortAccount.toString(),
      'timestamp': timestamp.toString(),
    };
  }
}

/// 时间周期枚举
enum TimePeriod {
  fiveMinutes('5m', '5分钟'),
  fifteenMinutes('15m', '15分钟'),
  thirtyMinutes('30m', '30分钟'),
  oneHour('1h', '1小时'),
  twoHours('2h', '2小时'),
  fourHours('4h', '4小时'),
  sixHours('6h', '6小时'),
  twelveHours('12h', '12小时'),
  oneDay('1d', '1天');

  const TimePeriod(this.value, this.label);
  final String value;
  final String label;

  /// 获取OKX API对应的时间周期值
  String get okxValue {
    switch (this) {
      case TimePeriod.fiveMinutes:
        return '5m';
      case TimePeriod.fifteenMinutes:
        return '15m';
      case TimePeriod.thirtyMinutes:
        return '30m';
      case TimePeriod.oneHour:
        return '1H';
      case TimePeriod.twoHours:
        return '2H';
      case TimePeriod.fourHours:
        return '4H';
      case TimePeriod.sixHours:
        return '6H';
      case TimePeriod.twelveHours:
        return '12H';
      case TimePeriod.oneDay:
        return '1D';
    }
  }

  static TimePeriod fromValue(String value) {
    return TimePeriod.values.firstWhere(
      (period) => period.value == value,
      orElse: () => TimePeriod.fiveMinutes,
    );
  }
}

/// 交易数据处理服务
class TradingDataService {
  // 单例模式
  static final TradingDataService _instance = TradingDataService._internal();
  factory TradingDataService() => _instance;
  TradingDataService._internal();

  // 币安API基础URL
  static const String _binanceBaseUrl = 'http://*************:8080';

  // OKX API基础URL
  static const String _okxBaseUrl = 'https://www.okx.com';

  // 外部请求客户端
  final ExternalRequest _externalRequest = ExternalRequest.instance;

  /// 初始化服务（设置代理等）
  void initialize() {
    // 设置HTTP代理（如果需要）
    _externalRequest.setHttpProxy(host: '127.0.0.1', port: 1087);
  }

  /// 获取币安U本位合约持仓量历史数据
  /// [symbol] 交易对符号，如 "BTCUSDT"
  /// [period] 时间周期，默认为5分钟
  /// [limit] 数据条数，默认100条
  Future<List<OpenInterestData>> getBinanceOpenInterestHistory({
    required String symbol,
    TimePeriod period = TimePeriod.fiveMinutes,
    int limit = 100,
  }) async {
    try {
      // 格式化交易对符号（移除斜杠并转大写）
      final formattedSymbol = symbol.replaceAll('/', '').toUpperCase();

      // 构建API URL
      const String endpoint = '/futures/data/openInterestHist';
      final String url = '$_binanceBaseUrl$endpoint';

      // 构建查询参数
      final Map<String, dynamic> queryParams = {
        'symbol': formattedSymbol,
        'period': period.value,
        'limit': limit.toString(),
      };

      // 发送请求
      final response = await _externalRequest.get<List<dynamic>>(
        url,
        queryParams: queryParams,
        dataConverter: (data) => data as List<dynamic>,
      );

      if (response.success && response.data != null) {
        // 解析数据
        final List<OpenInterestData> openInterestList =
            response.data!
                .map(
                  (item) =>
                      OpenInterestData.fromJson(item as Map<String, dynamic>),
                )
                .toList();

        return openInterestList;
      } else {
        throw Exception('获取数据失败: ${response.message}');
      }
    } catch (e) {
      // 返回空数据
      return [];
    }
  }

  /// 获取OKX合约持仓量历史数据
  /// [symbol] 交易对符号，如 "BTC/USDT"
  /// [period] 时间周期，默认为5分钟
  /// [limit] 数据条数，默认100条
  Future<List<OpenInterestData>> getOkxOpenInterestHistory({
    required String symbol,
    TimePeriod period = TimePeriod.fiveMinutes,
    int limit = 100,
  }) async {
    try {
      // 转换币种格式 (BTC/USDT -> BTC-USDT-SWAP)
      final okxSymbol = '${symbol.replaceAll('/', '-').toUpperCase()}-SWAP';

      // 构建API URL
      const String endpoint =
          '/api/v5/rubik/stat/contracts/open-interest-history';
      final String url = '$_okxBaseUrl$endpoint';

      // 构建查询参数
      final Map<String, dynamic> queryParams = {
        'instId': okxSymbol,
        'period': period.okxValue,
        'limit': limit.toString(),
      };

      // 发送请求
      final response = await _externalRequest.get<Map<String, dynamic>>(
        url,
        queryParams: queryParams,
        dataConverter: (data) => data as Map<String, dynamic>,
      );

      if (response.success && response.data != null) {
        final responseData = response.data!;

        if (responseData['code'] == '0' && responseData['data'] is List) {
          // 解析OKX数据格式
          final List<OpenInterestData> openInterestList =
              (responseData['data'] as List).map((item) {
                final itemList = item as List;
                final timestamp = int.parse(itemList[0].toString());
                return OpenInterestData(
                  symbol: symbol,
                  sumOpenInterest: double.parse(
                    itemList[2].toString(),
                  ), // 第3个元素：持仓总量
                  sumOpenInterestValue: double.parse(
                    itemList[3].toString(),
                  ), // 第4个元素：持仓总价值
                  timestamp: timestamp,
                  dateTime: DateTime.fromMillisecondsSinceEpoch(timestamp),
                );
              }).toList();

          return openInterestList;
        } else {
          throw Exception('OKX API返回错误: ${responseData['msg']}');
        }
      } else {
        throw Exception('获取OKX数据失败: ${response.message}');
      }
    } catch (e) {
      // 返回空数据
      return [];
    }
  }

  /// 获取聚合的合约持仓量历史数据（币安 + OKX）
  /// [symbol] 交易对符号，如 "BTC/USDT"
  /// [period] 时间周期，默认为5分钟
  /// [limit] 数据条数，默认100条
  Future<List<OpenInterestData>> getAggregatedOpenInterestHistory({
    required String symbol,
    TimePeriod period = TimePeriod.fiveMinutes,
    int limit = 100,
  }) async {
    try {
      // 并行获取币安和OKX数据
      final results = await Future.wait([
        getBinanceOpenInterestHistory(
          symbol: symbol,
          period: period,
          limit: limit,
        ),
        getOkxOpenInterestHistory(symbol: symbol, period: period, limit: limit),
      ]);

      final binanceData = results[0];
      final okxData = results[1];

      // 创建时间戳到数据的映射
      final Map<int, OpenInterestData> aggregatedMap = {};

      // 添加币安数据
      for (final item in binanceData) {
        aggregatedMap[item.timestamp] = item;
      }

      // 聚合OKX数据
      for (final item in okxData) {
        if (aggregatedMap.containsKey(item.timestamp)) {
          // 如果时间戳已存在，聚合数据
          final existing = aggregatedMap[item.timestamp]!;
          aggregatedMap[item.timestamp] = OpenInterestData(
            symbol: symbol,
            sumOpenInterest: existing.sumOpenInterest + item.sumOpenInterest,
            sumOpenInterestValue:
                existing.sumOpenInterestValue + item.sumOpenInterestValue,
            timestamp: item.timestamp,
            dateTime: DateTime.fromMillisecondsSinceEpoch(item.timestamp),
          );
        } else {
          // 如果时间戳不存在，直接添加
          aggregatedMap[item.timestamp] = item;
        }
      }

      // 按时间戳排序并返回
      final sortedData =
          aggregatedMap.values.toList()
            ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

      final result = sortedData.take(limit).toList();

      return result;
    } catch (e) {
      return [];
    }
  }

  /// 获取币安多空人数比值历史数据
  /// [symbol] 交易对符号，如 "BTC/USDT"
  /// [period] 时间周期，默认为5分钟
  /// [limit] 数据条数，默认100条
  Future<List<LongShortRatioData>> getBinanceLongShortRatioHistory({
    required String symbol,
    TimePeriod period = TimePeriod.fiveMinutes,
    int limit = 100,
  }) async {
    try {
      // 格式化交易对符号（移除斜杠并转大写）
      final formattedSymbol = symbol.replaceAll('/', '').toUpperCase();

      // 构建API URL
      const String endpoint = '/futures/data/globalLongShortAccountRatio';
      final String url = '$_binanceBaseUrl$endpoint';

      // 构建查询参数
      final Map<String, dynamic> queryParams = {
        'symbol': formattedSymbol,
        'period': period.value,
        'limit': limit.toString(),
      };

      // 发送请求
      final response = await _externalRequest.get<List<dynamic>>(
        url,
        queryParams: queryParams,
        dataConverter: (data) => data as List<dynamic>,
      );

      if (response.success && response.data != null) {
        // 解析数据
        final List<LongShortRatioData> ratioList =
            response.data!
                .map(
                  (item) => LongShortRatioData.fromBinanceJson(
                    item as Map<String, dynamic>,
                  ),
                )
                .toList();

        return ratioList;
      } else {
        throw Exception('获取数据失败: ${response.message}');
      }
    } catch (e) {
      // 返回空数据
      return [];
    }
  }

  /// 获取OKX多空人数比值历史数据
  /// [symbol] 交易对符号，如 "BTC/USDT"
  /// [period] 时间周期，默认为5分钟
  /// [limit] 数据条数，默认100条
  Future<List<LongShortRatioData>> getOkxLongShortRatioHistory({
    required String symbol,
    TimePeriod period = TimePeriod.fiveMinutes,
    int limit = 100,
  }) async {
    try {
      // 转换币种格式 (BTC/USDT -> BTC-USDT-SWAP)
      final okxSymbol = '${symbol.replaceAll('/', '-').toUpperCase()}-SWAP';

      // 构建API URL
      const String endpoint =
          '/api/v5/rubik/stat/contracts/long-short-account-ratio-contract';
      final String url = '$_okxBaseUrl$endpoint';

      // 构建查询参数
      final Map<String, dynamic> queryParams = {
        'instId': okxSymbol,
        'period': period.okxValue,
        'limit': limit.toString(),
      };

      // 发送请求
      final response = await _externalRequest.get<Map<String, dynamic>>(
        url,
        queryParams: queryParams,
        dataConverter: (data) => data as Map<String, dynamic>,
      );

      if (response.success && response.data != null) {
        final responseData = response.data!;

        if (responseData['code'] == '0' && responseData['data'] is List) {
          // 解析OKX数据格式
          final List<LongShortRatioData> ratioList =
              (responseData['data'] as List)
                  .map(
                    (item) =>
                        LongShortRatioData.fromOkxData(symbol, item as List),
                  )
                  .toList();

          return ratioList;
        } else {
          throw Exception('OKX API返回错误: ${responseData['msg']}');
        }
      } else {
        throw Exception('获取OKX数据失败: ${response.message}');
      }
    } catch (e) {
      // 返回空数据
      return [];
    }
  }

  /// 获取聚合的多空人数比值历史数据（币安 + OKX）
  /// [symbol] 交易对符号，如 "BTC/USDT"
  /// [period] 时间周期，默认为5分钟
  /// [limit] 数据条数，默认100条
  Future<List<LongShortRatioData>> getAggregatedLongShortRatioHistory({
    required String symbol,
    TimePeriod period = TimePeriod.fiveMinutes,
    int limit = 100,
  }) async {
    try {
      // 并行获取币安和OKX数据
      final results = await Future.wait([
        getBinanceLongShortRatioHistory(
          symbol: symbol,
          period: period,
          limit: limit,
        ),
        getOkxLongShortRatioHistory(
          symbol: symbol,
          period: period,
          limit: limit,
        ),
      ]);

      final binanceData = results[0];
      final okxData = results[1];

      // 创建时间戳到数据的映射
      final Map<int, LongShortRatioData> aggregatedMap = {};

      // 添加币安数据
      for (final item in binanceData) {
        aggregatedMap[item.timestamp] = item;
      }

      // 聚合OKX数据（取平均值）
      for (final item in okxData) {
        if (aggregatedMap.containsKey(item.timestamp)) {
          // 如果时间戳已存在，计算平均值
          final existing = aggregatedMap[item.timestamp]!;
          final avgLongAccount = (existing.longAccount + item.longAccount) / 2;
          final avgShortAccount =
              (existing.shortAccount + item.shortAccount) / 2;
          final avgRatio = avgLongAccount / avgShortAccount;

          aggregatedMap[item.timestamp] = LongShortRatioData(
            symbol: symbol,
            longShortRatio: avgRatio,
            longAccount: avgLongAccount,
            shortAccount: avgShortAccount,
            timestamp: item.timestamp,
            dateTime: DateTime.fromMillisecondsSinceEpoch(item.timestamp),
          );
        } else {
          // 如果时间戳不存在，直接添加
          aggregatedMap[item.timestamp] = item;
        }
      }

      // 按时间戳排序并返回
      final sortedData =
          aggregatedMap.values.toList()
            ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

      final result = sortedData.take(limit).toList();

      return result;
    } catch (e) {
      return [];
    }
  }

  /// 获取币安大账户多空比历史数据
  /// [symbol] 交易对符号，如 "BTC/USDT"
  /// [period] 时间周期，默认为5分钟
  /// [limit] 数据条数，默认100条
  Future<List<TopAccountRatioData>> getBinanceTopAccountRatioHistory({
    required String symbol,
    TimePeriod period = TimePeriod.fiveMinutes,
    int limit = 100,
  }) async {
    try {
      // 格式化交易对符号（移除斜杠并转大写）
      final formattedSymbol = symbol.replaceAll('/', '').toUpperCase();

      // 构建API URL
      const String endpoint = '/futures/data/topLongShortAccountRatio';
      final String url = '$_binanceBaseUrl$endpoint';

      // 构建查询参数
      final Map<String, dynamic> queryParams = {
        'symbol': formattedSymbol,
        'period': period.value,
        'limit': limit.toString(),
      };

      // 发送请求
      final response = await _externalRequest.get<List<dynamic>>(
        url,
        queryParams: queryParams,
        dataConverter: (data) => data as List<dynamic>,
      );

      if (response.success && response.data != null) {
        // 解析数据
        final List<TopAccountRatioData> ratioList =
            response.data!
                .map(
                  (item) => TopAccountRatioData.fromBinanceJson(
                    item as Map<String, dynamic>,
                  ),
                )
                .toList();

        return ratioList;
      } else {
        throw Exception('获取数据失败: ${response.message}');
      }
    } catch (e) {
      // 返回空数据
      return [];
    }
  }

  /// 获取OKX大账户多空比历史数据
  /// [symbol] 交易对符号，如 "BTC/USDT"
  /// [period] 时间周期，默认为5分钟
  /// [limit] 数据条数，默认100条
  Future<List<TopAccountRatioData>> getOkxTopAccountRatioHistory({
    required String symbol,
    TimePeriod period = TimePeriod.fiveMinutes,
    int limit = 100,
  }) async {
    try {
      // 转换币种格式 (BTC/USDT -> BTC-USDT-SWAP)
      final okxSymbol = '${symbol.replaceAll('/', '-').toUpperCase()}-SWAP';

      // 构建API URL
      const String endpoint =
          '/api/v5/rubik/stat/contracts/long-short-account-ratio-contract-top-trader';
      final String url = '$_okxBaseUrl$endpoint';

      // 构建查询参数
      final Map<String, dynamic> queryParams = {
        'instId': okxSymbol,
        'period': period.okxValue,
        'limit': limit.toString(),
      };

      // 发送请求
      final response = await _externalRequest.get<Map<String, dynamic>>(
        url,
        queryParams: queryParams,
        dataConverter: (data) => data as Map<String, dynamic>,
      );

      if (response.success && response.data != null) {
        final responseData = response.data!;

        if (responseData['code'] == '0' && responseData['data'] is List) {
          // 解析OKX数据格式
          final List<TopAccountRatioData> ratioList =
              (responseData['data'] as List)
                  .map(
                    (item) =>
                        TopAccountRatioData.fromOkxData(symbol, item as List),
                  )
                  .toList();

          return ratioList;
        } else {
          throw Exception('OKX API返回错误: ${responseData['msg']}');
        }
      } else {
        throw Exception('获取OKX数据失败: ${response.message}');
      }
    } catch (e) {
      // 返回空数据
      return [];
    }
  }

  /// 获取聚合的大账户多空比历史数据（币安 + OKX）
  /// [symbol] 交易对符号，如 "BTC/USDT"
  /// [period] 时间周期，默认为5分钟
  /// [limit] 数据条数，默认100条
  Future<List<TopAccountRatioData>> getAggregatedTopAccountRatioHistory({
    required String symbol,
    TimePeriod period = TimePeriod.fiveMinutes,
    int limit = 100,
  }) async {
    try {
      // 并行获取币安和OKX数据
      final results = await Future.wait([
        getBinanceTopAccountRatioHistory(
          symbol: symbol,
          period: period,
          limit: limit,
        ),
        getOkxTopAccountRatioHistory(
          symbol: symbol,
          period: period,
          limit: limit,
        ),
      ]);

      final binanceData = results[0];
      final okxData = results[1];

      // 创建时间戳到数据的映射
      final Map<int, TopAccountRatioData> aggregatedMap = {};

      // 添加币安数据
      for (final item in binanceData) {
        aggregatedMap[item.timestamp] = item;
      }

      // 聚合OKX数据（取平均值）
      for (final item in okxData) {
        if (aggregatedMap.containsKey(item.timestamp)) {
          // 如果时间戳已存在，计算平均值
          final existing = aggregatedMap[item.timestamp]!;
          final avgLongAccount = (existing.longAccount + item.longAccount) / 2;
          final avgShortAccount =
              (existing.shortAccount + item.shortAccount) / 2;
          final avgRatio = avgLongAccount / avgShortAccount;

          aggregatedMap[item.timestamp] = TopAccountRatioData(
            symbol: symbol,
            longShortRatio: avgRatio,
            longAccount: avgLongAccount,
            shortAccount: avgShortAccount,
            timestamp: item.timestamp,
            dateTime: DateTime.fromMillisecondsSinceEpoch(item.timestamp),
          );
        } else {
          // 如果时间戳不存在，直接添加
          aggregatedMap[item.timestamp] = item;
        }
      }

      // 按时间戳排序并返回
      final sortedData =
          aggregatedMap.values.toList()
            ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

      final result = sortedData.take(limit).toList();

      return result;
    } catch (e) {
      return [];
    }
  }

  /// 获取币安大账户持仓量多空比历史数据
  /// [symbol] 交易对符号，如 "BTC/USDT"
  /// [period] 时间周期，默认为5分钟
  /// [limit] 数据条数，默认100条
  Future<List<TopPositionRatioData>> getBinanceTopPositionRatioHistory({
    required String symbol,
    TimePeriod period = TimePeriod.fiveMinutes,
    int limit = 100,
  }) async {
    try {
      // 格式化交易对符号（移除斜杠并转大写）
      final formattedSymbol = symbol.replaceAll('/', '').toUpperCase();

      // 构建API URL
      const String endpoint = '/futures/data/topLongShortPositionRatio';
      final String url = '$_binanceBaseUrl$endpoint';

      // 构建查询参数
      final Map<String, dynamic> queryParams = {
        'symbol': formattedSymbol,
        'period': period.value,
        'limit': limit.toString(),
      };

      // 发送请求
      final response = await _externalRequest.get<List<dynamic>>(
        url,
        queryParams: queryParams,
        dataConverter: (data) => data as List<dynamic>,
      );

      if (response.success && response.data != null) {
        // 解析数据
        final List<TopPositionRatioData> ratioList =
            response.data!
                .map(
                  (item) => TopPositionRatioData.fromBinanceJson(
                    item as Map<String, dynamic>,
                  ),
                )
                .toList();

        return ratioList;
      } else {
        throw Exception('获取数据失败: ${response.message}');
      }
    } catch (e) {
      // 返回空数据
      return [];
    }
  }

  /// 获取OKX大账户持仓量多空比历史数据
  /// [symbol] 交易对符号，如 "BTC/USDT"
  /// [period] 时间周期，默认为5分钟
  /// [limit] 数据条数，默认100条
  Future<List<TopPositionRatioData>> getOkxTopPositionRatioHistory({
    required String symbol,
    TimePeriod period = TimePeriod.fiveMinutes,
    int limit = 100,
  }) async {
    try {
      // 转换币种格式 (BTC/USDT -> BTC-USDT-SWAP)
      final okxSymbol = '${symbol.replaceAll('/', '-').toUpperCase()}-SWAP';

      // 构建API URL
      const String endpoint =
          '/api/v5/rubik/stat/contracts/long-short-position-ratio-contract-top-trader';
      final String url = '$_okxBaseUrl$endpoint';

      // 构建查询参数
      final Map<String, dynamic> queryParams = {
        'instId': okxSymbol,
        'period': period.okxValue,
        'limit': limit.toString(),
      };

      // 发送请求
      final response = await _externalRequest.get<Map<String, dynamic>>(
        url,
        queryParams: queryParams,
        dataConverter: (data) => data as Map<String, dynamic>,
      );

      if (response.success && response.data != null) {
        final responseData = response.data!;

        if (responseData['code'] == '0' && responseData['data'] is List) {
          // 解析OKX数据格式
          final List<TopPositionRatioData> ratioList =
              (responseData['data'] as List)
                  .map(
                    (item) =>
                        TopPositionRatioData.fromOkxData(symbol, item as List),
                  )
                  .toList();

          return ratioList;
        } else {
          throw Exception('OKX API返回错误: ${responseData['msg']}');
        }
      } else {
        throw Exception('获取OKX数据失败: ${response.message}');
      }
    } catch (e) {
      // 返回空数据
      return [];
    }
  }

  /// 获取聚合的大账户持仓量多空比历史数据（币安 + OKX）
  /// [symbol] 交易对符号，如 "BTC/USDT"
  /// [period] 时间周期，默认为5分钟
  /// [limit] 数据条数，默认100条
  Future<List<TopPositionRatioData>> getAggregatedTopPositionRatioHistory({
    required String symbol,
    TimePeriod period = TimePeriod.fiveMinutes,
    int limit = 100,
  }) async {
    try {
      // 并行获取币安和OKX数据
      final results = await Future.wait([
        getBinanceTopPositionRatioHistory(
          symbol: symbol,
          period: period,
          limit: limit,
        ),
        getOkxTopPositionRatioHistory(
          symbol: symbol,
          period: period,
          limit: limit,
        ),
      ]);

      final binanceData = results[0];
      final okxData = results[1];

      // 创建时间戳到数据的映射
      final Map<int, TopPositionRatioData> aggregatedMap = {};

      // 添加币安数据
      for (final item in binanceData) {
        aggregatedMap[item.timestamp] = item;
      }

      // 聚合OKX数据（取平均值）
      for (final item in okxData) {
        if (aggregatedMap.containsKey(item.timestamp)) {
          // 如果时间戳已存在，计算平均值
          final existing = aggregatedMap[item.timestamp]!;
          final avgLongAccount = (existing.longAccount + item.longAccount) / 2;
          final avgShortAccount =
              (existing.shortAccount + item.shortAccount) / 2;
          final avgRatio = avgLongAccount / avgShortAccount;

          aggregatedMap[item.timestamp] = TopPositionRatioData(
            symbol: symbol,
            longShortRatio: avgRatio,
            longAccount: avgLongAccount,
            shortAccount: avgShortAccount,
            timestamp: item.timestamp,
            dateTime: DateTime.fromMillisecondsSinceEpoch(item.timestamp),
          );
        } else {
          // 如果时间戳不存在，直接添加
          aggregatedMap[item.timestamp] = item;
        }
      }

      // 按时间戳排序并返回
      final sortedData =
          aggregatedMap.values.toList()
            ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

      final result = sortedData.take(limit).toList();

      return result;
    } catch (e) {
      return [];
    }
  }

  /// 获取所有可用的时间周期
  List<TimePeriod> getAvailableTimePeriods() {
    return TimePeriod.values;
  }

  /// 格式化交易对符号为币安格式
  String formatSymbolForBinance(String symbol) {
    return symbol.replaceAll('/', '').toUpperCase();
  }

  /// 清理资源
  void dispose() {
    _externalRequest.clearProxy();
  }
}
