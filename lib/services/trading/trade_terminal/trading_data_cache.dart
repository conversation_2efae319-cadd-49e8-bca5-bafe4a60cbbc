import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'trading_data_service.dart';

/// 缓存数据模型
class CachedData<T> {
  final List<T> data;
  final int lastUpdateTimestamp;
  final TimePeriod period;
  final String symbol;

  CachedData({
    required this.data,
    required this.lastUpdateTimestamp,
    required this.period,
    required this.symbol,
  });

  /// 判断是否需要更新数据
  bool needsUpdate() {
    return !TimeWindowCalculator.isInCurrentWindow(lastUpdateTimestamp, period);
  }

  /// 转换为JSON
  Map<String, dynamic> toJson(List<Map<String, dynamic>> Function(List<T>) dataToJson) {
    return {
      'data': dataToJson(data),
      'lastUpdateTimestamp': lastUpdateTimestamp,
      'period': period.value,
      'symbol': symbol,
    };
  }

  /// 从JSON创建
  static CachedData<T> fromJson<T>(
    Map<String, dynamic> json,
    List<T> Function(List<dynamic>) dataFromJson,
  ) {
    return CachedData<T>(
      data: dataFromJson(json['data'] as List<dynamic>),
      lastUpdateTimestamp: json['lastUpdateTimestamp'] as int,
      period: TimePeriod.fromValue(json['period'] as String),
      symbol: json['symbol'] as String,
    );
  }
}

/// 时间窗口计算工具
class TimeWindowCalculator {
  /// 根据时间周期计算当前时间窗口的开始时间
  static int getCurrentWindowStart(TimePeriod period) {
    final now = DateTime.now();
    DateTime windowStart;

    switch (period) {
      case TimePeriod.fiveMinutes:
        final minutes = (now.minute ~/ 5) * 5;
        windowStart = DateTime(now.year, now.month, now.day, now.hour, minutes);
        break;
      case TimePeriod.fifteenMinutes:
        final minutes = (now.minute ~/ 15) * 15;
        windowStart = DateTime(now.year, now.month, now.day, now.hour, minutes);
        break;
      case TimePeriod.thirtyMinutes:
        final minutes = (now.minute ~/ 30) * 30;
        windowStart = DateTime(now.year, now.month, now.day, now.hour, minutes);
        break;
      case TimePeriod.oneHour:
        windowStart = DateTime(now.year, now.month, now.day, now.hour);
        break;
      case TimePeriod.twoHours:
        final hours = (now.hour ~/ 2) * 2;
        windowStart = DateTime(now.year, now.month, now.day, hours);
        break;
      case TimePeriod.fourHours:
        final hours = (now.hour ~/ 4) * 4;
        windowStart = DateTime(now.year, now.month, now.day, hours);
        break;
      case TimePeriod.sixHours:
        final hours = (now.hour ~/ 6) * 6;
        windowStart = DateTime(now.year, now.month, now.day, hours);
        break;
      case TimePeriod.twelveHours:
        final hours = (now.hour ~/ 12) * 12;
        windowStart = DateTime(now.year, now.month, now.day, hours);
        break;
      case TimePeriod.oneDay:
        windowStart = DateTime(now.year, now.month, now.day);
        break;
    }

    return windowStart.millisecondsSinceEpoch;
  }

  /// 判断给定时间戳是否在当前时间窗口内
  static bool isInCurrentWindow(int timestamp, TimePeriod period) {
    final currentWindowStart = getCurrentWindowStart(period);
    final nextWindowStart = getNextWindowStart(period, currentWindowStart);
    
    return timestamp >= currentWindowStart && timestamp < nextWindowStart;
  }

  /// 获取下一个时间窗口的开始时间
  static int getNextWindowStart(TimePeriod period, int currentWindowStart) {
    final currentWindow = DateTime.fromMillisecondsSinceEpoch(currentWindowStart);
    DateTime nextWindow;

    switch (period) {
      case TimePeriod.fiveMinutes:
        nextWindow = currentWindow.add(const Duration(minutes: 5));
        break;
      case TimePeriod.fifteenMinutes:
        nextWindow = currentWindow.add(const Duration(minutes: 15));
        break;
      case TimePeriod.thirtyMinutes:
        nextWindow = currentWindow.add(const Duration(minutes: 30));
        break;
      case TimePeriod.oneHour:
        nextWindow = currentWindow.add(const Duration(hours: 1));
        break;
      case TimePeriod.twoHours:
        nextWindow = currentWindow.add(const Duration(hours: 2));
        break;
      case TimePeriod.fourHours:
        nextWindow = currentWindow.add(const Duration(hours: 4));
        break;
      case TimePeriod.sixHours:
        nextWindow = currentWindow.add(const Duration(hours: 6));
        break;
      case TimePeriod.twelveHours:
        nextWindow = currentWindow.add(const Duration(hours: 12));
        break;
      case TimePeriod.oneDay:
        nextWindow = currentWindow.add(const Duration(days: 1));
        break;
    }

    return nextWindow.millisecondsSinceEpoch;
  }
}

/// 缓存数据类型枚举
enum CacheDataType {
  openInterest('open_interest'),
  longShortRatio('long_short_ratio'),
  topAccountRatio('top_account_ratio'),
  topPositionRatio('top_position_ratio');

  const CacheDataType(this.value);
  final String value;
}

/// 交易数据缓存管理器
class TradingDataCache {
  static final TradingDataCache _instance = TradingDataCache._internal();
  factory TradingDataCache() => _instance;
  TradingDataCache._internal();

  SharedPreferences? _prefs;

  /// 初始化缓存
  Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// 生成缓存键
  String _generateCacheKey(String symbol, TimePeriod period, CacheDataType dataType) {
    return 'trading_data_${dataType.value}_${symbol}_${period.value}';
  }

  /// 保存合约持仓量数据到缓存
  Future<void> saveOpenInterestData(
    String symbol,
    TimePeriod period,
    List<OpenInterestData> data,
  ) async {
    await initialize();
    final cacheKey = _generateCacheKey(symbol, period, CacheDataType.openInterest);
    final cachedData = CachedData<OpenInterestData>(
      data: data,
      lastUpdateTimestamp: DateTime.now().millisecondsSinceEpoch,
      period: period,
      symbol: symbol,
    );
    
    final jsonString = jsonEncode(cachedData.toJson((data) => 
      data.map((item) => item.toJson()).toList()));
    await _prefs!.setString(cacheKey, jsonString);
  }

  /// 从缓存获取合约持仓量数据
  Future<CachedData<OpenInterestData>?> getOpenInterestData(
    String symbol,
    TimePeriod period,
  ) async {
    await initialize();
    final cacheKey = _generateCacheKey(symbol, period, CacheDataType.openInterest);
    final jsonString = _prefs!.getString(cacheKey);
    
    if (jsonString == null) return null;
    
    try {
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return CachedData.fromJson<OpenInterestData>(
        json,
        (data) => data.map((item) => OpenInterestData.fromJson(item as Map<String, dynamic>)).toList(),
      );
    } catch (e) {
      return null;
    }
  }

  /// 保存多空人数比值数据到缓存
  Future<void> saveLongShortRatioData(
    String symbol,
    TimePeriod period,
    List<LongShortRatioData> data,
  ) async {
    await initialize();
    final cacheKey = _generateCacheKey(symbol, period, CacheDataType.longShortRatio);
    final cachedData = CachedData<LongShortRatioData>(
      data: data,
      lastUpdateTimestamp: DateTime.now().millisecondsSinceEpoch,
      period: period,
      symbol: symbol,
    );
    
    final jsonString = jsonEncode(cachedData.toJson((data) => 
      data.map((item) => item.toJson()).toList()));
    await _prefs!.setString(cacheKey, jsonString);
  }

  /// 从缓存获取多空人数比值数据
  Future<CachedData<LongShortRatioData>?> getLongShortRatioData(
    String symbol,
    TimePeriod period,
  ) async {
    await initialize();
    final cacheKey = _generateCacheKey(symbol, period, CacheDataType.longShortRatio);
    final jsonString = _prefs!.getString(cacheKey);
    
    if (jsonString == null) return null;
    
    try {
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return CachedData.fromJson<LongShortRatioData>(
        json,
        (data) => data.map((item) => LongShortRatioData.fromBinanceJson(item as Map<String, dynamic>)).toList(),
      );
    } catch (e) {
      return null;
    }
  }

  /// 保存大账户多空比数据到缓存
  Future<void> saveTopAccountRatioData(
    String symbol,
    TimePeriod period,
    List<TopAccountRatioData> data,
  ) async {
    await initialize();
    final cacheKey = _generateCacheKey(symbol, period, CacheDataType.topAccountRatio);
    final cachedData = CachedData<TopAccountRatioData>(
      data: data,
      lastUpdateTimestamp: DateTime.now().millisecondsSinceEpoch,
      period: period,
      symbol: symbol,
    );
    
    final jsonString = jsonEncode(cachedData.toJson((data) => 
      data.map((item) => item.toJson()).toList()));
    await _prefs!.setString(cacheKey, jsonString);
  }

  /// 从缓存获取大账户多空比数据
  Future<CachedData<TopAccountRatioData>?> getTopAccountRatioData(
    String symbol,
    TimePeriod period,
  ) async {
    await initialize();
    final cacheKey = _generateCacheKey(symbol, period, CacheDataType.topAccountRatio);
    final jsonString = _prefs!.getString(cacheKey);
    
    if (jsonString == null) return null;
    
    try {
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return CachedData.fromJson<TopAccountRatioData>(
        json,
        (data) => data.map((item) => TopAccountRatioData.fromBinanceJson(item as Map<String, dynamic>)).toList(),
      );
    } catch (e) {
      return null;
    }
  }

  /// 保存大账户持仓量多空比数据到缓存
  Future<void> saveTopPositionRatioData(
    String symbol,
    TimePeriod period,
    List<TopPositionRatioData> data,
  ) async {
    await initialize();
    final cacheKey = _generateCacheKey(symbol, period, CacheDataType.topPositionRatio);
    final cachedData = CachedData<TopPositionRatioData>(
      data: data,
      lastUpdateTimestamp: DateTime.now().millisecondsSinceEpoch,
      period: period,
      symbol: symbol,
    );
    
    final jsonString = jsonEncode(cachedData.toJson((data) => 
      data.map((item) => item.toJson()).toList()));
    await _prefs!.setString(cacheKey, jsonString);
  }

  /// 从缓存获取大账户持仓量多空比数据
  Future<CachedData<TopPositionRatioData>?> getTopPositionRatioData(
    String symbol,
    TimePeriod period,
  ) async {
    await initialize();
    final cacheKey = _generateCacheKey(symbol, period, CacheDataType.topPositionRatio);
    final jsonString = _prefs!.getString(cacheKey);
    
    if (jsonString == null) return null;
    
    try {
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return CachedData.fromJson<TopPositionRatioData>(
        json,
        (data) => data.map((item) => TopPositionRatioData.fromBinanceJson(item as Map<String, dynamic>)).toList(),
      );
    } catch (e) {
      return null;
    }
  }

  /// 清除指定类型的缓存
  Future<void> clearCache(String symbol, TimePeriod period, CacheDataType dataType) async {
    await initialize();
    final cacheKey = _generateCacheKey(symbol, period, dataType);
    await _prefs!.remove(cacheKey);
  }

  /// 清除所有缓存
  Future<void> clearAllCache() async {
    await initialize();
    final keys = _prefs!.getKeys().where((key) => key.startsWith('trading_data_'));
    for (final key in keys) {
      await _prefs!.remove(key);
    }
  }

  /// 获取缓存大小信息
  Future<Map<String, int>> getCacheInfo() async {
    await initialize();
    final keys = _prefs!.getKeys().where((key) => key.startsWith('trading_data_'));
    final info = <String, int>{};
    
    for (final key in keys) {
      final value = _prefs!.getString(key);
      if (value != null) {
        info[key] = value.length;
      }
    }
    
    return info;
  }
} 