import '../../models/user/index.dart';

/// 用户服务接口
/// 
/// 定义用户相关的业务逻辑接口
abstract class UserService {
  /// 获取用户信息
  Future<UserInfo?> getUserInfo(String userId);
  
  /// 更新用户信息
  Future<bool> updateUserInfo(UserInfo userInfo);
  
  /// 获取用户配置
  Future<UserProfile?> getUserProfile(String userId);
  
  /// 更新用户配置
  Future<bool> updateUserProfile(UserProfile profile);
  
  /// 获取用户安全设置
  Future<UserSecurity?> getUserSecurity(String userId);
  
  /// 更新用户安全设置
  Future<bool> updateUserSecurity(UserSecurity security);
  
  /// 上传用户头像
  Future<String?> uploadAvatar(String userId, String imagePath);
  
  /// 更新用户昵称
  Future<bool> updateNickname(String userId, String nickname);
  
  /// 更新用户邮箱
  Future<bool> updateEmail(String userId, String email, String verificationCode);
  
  /// 更新用户手机号
  Future<bool> updatePhone(String userId, String phone, String countryCode, String verificationCode);
  
  /// 删除用户账户
  Future<bool> deleteAccount(String userId, String password, String verificationCode);
  
  /// 获取用户活动日志
  Future<List<Map<String, dynamic>>> getUserActivityLog(String userId, {int page = 1, int limit = 20});
}
