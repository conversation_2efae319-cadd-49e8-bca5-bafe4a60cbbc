import 'dart:math';
import 'package:flutter/foundation.dart';
import '../../models/user/index.dart';
import 'user_service.dart';

/// 模拟用户服务实现
///
/// 用于开发和测试阶段的模拟用户功能
class MockUserService implements UserService {
  static final MockUserService _instance = MockUserService._internal();
  factory MockUserService() => _instance;
  MockUserService._internal();

  // 模拟用户数据存储
  final Map<String, UserInfo> _userInfoCache = {};
  final Map<String, UserProfile> _userProfileCache = {};
  final Map<String, UserSecurity> _userSecurityCache = {};

  @override
  Future<UserInfo?> getUserInfo(String userId) async {
    debugPrint('👤 MockUserService: 获取用户信息 - userId: $userId');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));

    // 如果缓存中有数据，直接返回
    if (_userInfoCache.containsKey(userId)) {
      return _userInfoCache[userId];
    }

    // 生成模拟用户信息
    final mockUserInfo = _generateMockUserInfo(userId);
    _userInfoCache[userId] = mockUserInfo;

    debugPrint('✅ 用户信息获取成功: ${mockUserInfo.nickname}');
    return mockUserInfo;
  }

  @override
  Future<bool> updateUserInfo(UserInfo userInfo) async {
    debugPrint('👤 MockUserService: 更新用户信息 - userId: ${userInfo.id}');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 800));

    if (userInfo.id != null) {
      _userInfoCache[userInfo.id!.toString()] = userInfo;
      debugPrint('✅ 用户信息更新成功');
      return true;
    }

    debugPrint('❌ 用户信息更新失败：用户ID为空');
    return false;
  }

  @override
  Future<UserProfile?> getUserProfile(String userId) async {
    debugPrint('⚙️ MockUserService: 获取用户配置 - userId: $userId');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 400));

    // 如果缓存中有数据，直接返回
    if (_userProfileCache.containsKey(userId)) {
      return _userProfileCache[userId];
    }

    // 生成模拟用户配置
    final mockProfile = _generateMockUserProfile(userId);
    _userProfileCache[userId] = mockProfile;

    debugPrint('✅ 用户配置获取成功');
    return mockProfile;
  }

  @override
  Future<bool> updateUserProfile(UserProfile profile) async {
    debugPrint('⚙️ MockUserService: 更新用户配置 - userId: ${profile.userId}');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 600));

    if (profile.userId != null) {
      _userProfileCache[profile.userId!] = profile;
      debugPrint('✅ 用户配置更新成功');
      return true;
    }

    debugPrint('❌ 用户配置更新失败：用户ID为空');
    return false;
  }

  @override
  Future<UserSecurity?> getUserSecurity(String userId) async {
    debugPrint('🔒 MockUserService: 获取用户安全设置 - userId: $userId');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 400));

    // 如果缓存中有数据，直接返回
    if (_userSecurityCache.containsKey(userId)) {
      return _userSecurityCache[userId];
    }

    // 生成模拟安全设置
    final mockSecurity = _generateMockUserSecurity(userId);
    _userSecurityCache[userId] = mockSecurity;

    debugPrint('✅ 用户安全设置获取成功 - 安全等级: ${mockSecurity.securityLevel}');
    return mockSecurity;
  }

  @override
  Future<bool> updateUserSecurity(UserSecurity security) async {
    debugPrint('🔒 MockUserService: 更新用户安全设置 - userId: ${security.userId}');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 700));

    if (security.userId != null) {
      _userSecurityCache[security.userId!] = security;
      debugPrint('✅ 用户安全设置更新成功');
      return true;
    }

    debugPrint('❌ 用户安全设置更新失败：用户ID为空');
    return false;
  }

  @override
  Future<String?> uploadAvatar(String userId, String imagePath) async {
    debugPrint('📷 MockUserService: 上传头像 - userId: $userId, path: $imagePath');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 1500));

    // 生成模拟头像URL
    final avatarUrl =
        'https://mock-cdn.example.com/avatars/${userId}_${DateTime.now().millisecondsSinceEpoch}.jpg';

    // 更新用户信息中的头像
    if (_userInfoCache.containsKey(userId)) {
      final userInfo = _userInfoCache[userId]!;
      _userInfoCache[userId] = UserInfo(
        id: userInfo.id,
        account: userInfo.account,
        username: userInfo.username,
        nickname: userInfo.nickname,
        email: userInfo.email,
        avatar: avatarUrl,
        inviteCode: userInfo.inviteCode,
        parentId: userInfo.parentId,
        status: userInfo.status,
        isVip: userInfo.isVip,
      );
    }

    debugPrint('✅ 头像上传成功: $avatarUrl');
    return avatarUrl;
  }

  @override
  Future<bool> updateNickname(String userId, String nickname) async {
    debugPrint(
      '✏️ MockUserService: 更新昵称 - userId: $userId, nickname: $nickname',
    );

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 600));

    // 检查昵称是否已被使用（模拟）
    if (nickname.toLowerCase() == 'admin' ||
        nickname.toLowerCase() == 'system') {
      debugPrint('❌ 昵称更新失败：昵称已被使用');
      return false;
    }

    // 更新用户信息
    if (_userInfoCache.containsKey(userId)) {
      final userInfo = _userInfoCache[userId]!;
      _userInfoCache[userId] = UserInfo(
        id: userInfo.id,
        account: userInfo.account,
        username: userInfo.username,
        email: userInfo.email,
        avatar: userInfo.avatar,
        nickname: nickname,
        inviteCode: userInfo.inviteCode,
        parentId: userInfo.parentId,
        status: userInfo.status,
        isVip: userInfo.isVip,
      );
    }

    debugPrint('✅ 昵称更新成功');
    return true;
  }

  @override
  Future<bool> updateEmail(
    String userId,
    String email,
    String verificationCode,
  ) async {
    debugPrint('📧 MockUserService: 更新邮箱 - userId: $userId, email: $email');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 800));

    // 验证验证码（模拟）
    if (verificationCode != '123456') {
      debugPrint('❌ 邮箱更新失败：验证码错误');
      return false;
    }

    // 更新用户信息
    if (_userInfoCache.containsKey(userId)) {
      final userInfo = _userInfoCache[userId]!;
      _userInfoCache[userId] = UserInfo(
        id: userInfo.id,
        account: userInfo.account,
        username: userInfo.username,
        nickname: userInfo.nickname,
        email: email,
        avatar: userInfo.avatar,
        inviteCode: userInfo.inviteCode,
        parentId: userInfo.parentId,
        status: userInfo.status,
        isVip: userInfo.isVip,
      );
    }

    debugPrint('✅ 邮箱更新成功');
    return true;
  }

  @override
  Future<bool> updatePhone(
    String userId,
    String phone,
    String countryCode,
    String verificationCode,
  ) async {
    debugPrint(
      '📱 MockUserService: 更新手机号 - userId: $userId, phone: $countryCode$phone',
    );

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 800));

    // 验证验证码（模拟）
    if (verificationCode != '123456') {
      debugPrint('❌ 手机号更新失败：验证码错误');
      return false;
    }

    debugPrint('✅ 手机号更新成功');
    return true;
  }

  @override
  Future<bool> deleteAccount(
    String userId,
    String password,
    String verificationCode,
  ) async {
    debugPrint('🗑️ MockUserService: 删除账户 - userId: $userId');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 1000));

    // 验证密码和验证码（模拟）
    if (password != 'password123' || verificationCode != '123456') {
      debugPrint('❌ 账户删除失败：密码或验证码错误');
      return false;
    }

    // 清除缓存数据
    _userInfoCache.remove(userId);
    _userProfileCache.remove(userId);
    _userSecurityCache.remove(userId);

    debugPrint('✅ 账户删除成功');
    return true;
  }

  @override
  Future<List<Map<String, dynamic>>> getUserActivityLog(
    String userId, {
    int page = 1,
    int limit = 20,
  }) async {
    debugPrint('📋 MockUserService: 获取用户活动日志 - userId: $userId, page: $page');

    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 600));

    // 生成模拟活动日志
    final logs = <Map<String, dynamic>>[];
    final random = Random();

    for (int i = 0; i < limit; i++) {
      final timestamp = DateTime.now().subtract(Duration(hours: i * 2));
      logs.add({
        'id': 'log_${timestamp.millisecondsSinceEpoch}',
        'action': _getRandomAction(random),
        'description': _getRandomDescription(random),
        'ip': _getRandomIp(random),
        'device': _getRandomDevice(random),
        'timestamp': timestamp.toIso8601String(),
        'status': random.nextBool() ? 'success' : 'failed',
      });
    }

    debugPrint('✅ 用户活动日志获取成功 - 共${logs.length}条记录');
    return logs;
  }

  // 生成模拟用户信息
  UserInfo _generateMockUserInfo(String userId) {
    final random = Random();
    final nicknames = ['交易达人', '币圈新手', '投资专家', '数字货币爱好者', '区块链探索者'];

    return UserInfo(
      id: int.tryParse(userId) ?? random.nextInt(999999),
      account: 'user_$userId',
      username: 'user_$userId',
      nickname: nicknames[random.nextInt(nicknames.length)],
      email: 'user$<EMAIL>',
      avatar:
          'https://mock-cdn.example.com/avatars/default_${random.nextInt(10)}.jpg',
      inviteCode: 'INV${random.nextInt(999999).toString().padLeft(6, '0')}',
      parentId: random.nextBool() ? random.nextInt(1000) : null,
      status: 1, // 1表示活跃状态
      isVip: random.nextBool(),
    );
  }

  // 生成模拟用户配置
  UserProfile _generateMockUserProfile(String userId) {
    final random = Random();
    final languages = ['zh', 'en', 'zh_TW'];
    final currencies = ['USDT', 'USD', 'CNY'];
    final themes = ['light', 'dark', 'auto'];

    return UserProfile(
      userId: userId,
      language: languages[random.nextInt(languages.length)],
      currency: currencies[random.nextInt(currencies.length)],
      timezone: 'Asia/Shanghai',
      notificationsEnabled: random.nextBool(),
      emailNotifications: random.nextBool(),
      smsNotifications: random.nextBool(),
      pushNotifications: random.nextBool(),
      twoFactorEnabled: random.nextBool(),
      theme: themes[random.nextInt(themes.length)],
      tradingPreferences: {
        'defaultOrderType': 'limit',
        'confirmBeforeOrder': true,
        'showAdvancedFeatures': random.nextBool(),
      },
    );
  }

  // 生成模拟用户安全设置
  UserSecurity _generateMockUserSecurity(String userId) {
    final random = Random();

    return UserSecurity(
      userId: userId,
      emailVerified: random.nextBool(),
      phoneVerified: random.nextBool(),
      identityVerified: random.nextBool(),
      twoFactorEnabled: random.nextBool(),
      loginPasswordSet: true,
      tradingPasswordSet: random.nextBool(),
      lastLoginTime: DateTime.now().subtract(
        Duration(hours: random.nextInt(24)),
      ),
      lastLoginIp: _getRandomIp(random),
      lastLoginDevice: _getRandomDevice(random),
      trustedDevices: ['device_1', 'device_2'],
      securitySettings: {
        'loginNotifications': true,
        'withdrawalNotifications': true,
        'suspiciousActivityAlerts': true,
      },
    );
  }

  // 获取随机IP地址
  String _getRandomIp(Random random) {
    return '${random.nextInt(256)}.${random.nextInt(256)}.${random.nextInt(256)}.${random.nextInt(256)}';
  }

  // 获取随机设备信息
  String _getRandomDevice(Random random) {
    final devices = [
      'iPhone 15 Pro',
      'Samsung Galaxy S24',
      'iPad Pro',
      'MacBook Pro',
      'Windows PC',
    ];
    return devices[random.nextInt(devices.length)];
  }

  // 获取随机操作
  String _getRandomAction(Random random) {
    final actions = [
      'login',
      'logout',
      'trade',
      'deposit',
      'withdraw',
      'profile_update',
    ];
    return actions[random.nextInt(actions.length)];
  }

  // 获取随机描述
  String _getRandomDescription(Random random) {
    final descriptions = ['用户登录', '用户登出', '执行交易', '充值操作', '提现操作', '更新资料'];
    return descriptions[random.nextInt(descriptions.length)];
  }

  /// 清除所有缓存（用于测试）
  void clearCache() {
    _userInfoCache.clear();
    _userProfileCache.clear();
    _userSecurityCache.clear();
  }
}
