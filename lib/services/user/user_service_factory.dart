import 'user_service.dart';
import 'mock_user_service.dart';

/// 用户服务工厂
/// 
/// 根据环境配置返回相应的用户服务实现
class UserServiceFactory {
  static UserService _instance = MockUserService();
  
  /// 获取用户服务实例
  static UserService get instance => _instance;
  
  /// 设置用户服务实现（用于测试或切换环境）
  static void setInstance(UserService service) {
    _instance = service;
  }
  
  /// 使用模拟服务（开发环境）
  static void useMockService() {
    _instance = MockUserService();
  }
  
  /// 使用真实服务（生产环境）
  // static void useRealService() {
  //   _instance = RealUserService();
  // }
}
