import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';

/// 统一存储服务
/// 
/// 使用Hive数据库替代SharedPreferences，提供更好的性能
/// 支持多种数据类型的存储和读取
class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  // 单例访问器
  static StorageService get instance => _instance;

  // 不同类型的数据盒子
  Box<String>? _stringBox;
  Box<int>? _intBox;
  Box<double>? _doubleBox;
  Box<bool>? _boolBox;
  Box<List>? _listBox;
  Box<Map>? _mapBox;

  bool _isInitialized = false;

  /// 初始化存储服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('📦 StorageService 开始初始化...');

      // 初始化Hive（如果还没有初始化）
      if (!Hive.isAdapterRegistered(0)) {
        await Hive.initFlutter();
      }

      // 打开不同类型的数据盒子
      _stringBox = await Hive.openBox<String>('storage_strings');
      _intBox = await Hive.openBox<int>('storage_ints');
      _doubleBox = await Hive.openBox<double>('storage_doubles');
      _boolBox = await Hive.openBox<bool>('storage_bools');
      _listBox = await Hive.openBox<List>('storage_lists');
      _mapBox = await Hive.openBox<Map>('storage_maps');

      _isInitialized = true;
      debugPrint('✅ StorageService 初始化完成');
    } catch (e) {
      debugPrint('❌ StorageService 初始化失败: $e');
      rethrow;
    }
  }

  /// 销毁存储服务
  Future<void> dispose() async {
    debugPrint('🗑️ StorageService 正在销毁...');
    
    await _stringBox?.close();
    await _intBox?.close();
    await _doubleBox?.close();
    await _boolBox?.close();
    await _listBox?.close();
    await _mapBox?.close();
    
    _isInitialized = false;
  }

  // ========== 字符串存储 ==========
  
  /// 存储字符串
  Future<void> setString(String key, String value) async {
    await _stringBox?.put(key, value);
  }

  /// 获取字符串
  String? getString(String key) {
    return _stringBox?.get(key);
  }

  // ========== 整数存储 ==========
  
  /// 存储整数
  Future<void> setInt(String key, int value) async {
    await _intBox?.put(key, value);
  }

  /// 获取整数
  int? getInt(String key) {
    return _intBox?.get(key);
  }

  // ========== 浮点数存储 ==========
  
  /// 存储浮点数
  Future<void> setDouble(String key, double value) async {
    await _doubleBox?.put(key, value);
  }

  /// 获取浮点数
  double? getDouble(String key) {
    return _doubleBox?.get(key);
  }

  // ========== 布尔值存储 ==========
  
  /// 存储布尔值
  Future<void> setBool(String key, bool value) async {
    await _boolBox?.put(key, value);
  }

  /// 获取布尔值
  bool? getBool(String key) {
    return _boolBox?.get(key);
  }

  // ========== 列表存储 ==========
  
  /// 存储列表
  Future<void> setList(String key, List value) async {
    await _listBox?.put(key, value);
  }

  /// 获取列表
  List? getList(String key) {
    return _listBox?.get(key);
  }

  // ========== Map存储 ==========
  
  /// 存储Map
  Future<void> setMap(String key, Map value) async {
    await _mapBox?.put(key, value);
  }

  /// 获取Map
  Map? getMap(String key) {
    return _mapBox?.get(key);
  }

  // ========== 通用方法 ==========
  
  /// 删除指定键的数据
  Future<void> remove(String key) async {
    await _stringBox?.delete(key);
    await _intBox?.delete(key);
    await _doubleBox?.delete(key);
    await _boolBox?.delete(key);
    await _listBox?.delete(key);
    await _mapBox?.delete(key);
  }

  /// 检查键是否存在
  bool containsKey(String key) {
    return _stringBox?.containsKey(key) == true ||
           _intBox?.containsKey(key) == true ||
           _doubleBox?.containsKey(key) == true ||
           _boolBox?.containsKey(key) == true ||
           _listBox?.containsKey(key) == true ||
           _mapBox?.containsKey(key) == true;
  }

  /// 获取所有字符串键
  Iterable<String> getStringKeys() {
    return _stringBox?.keys.cast<String>() ?? [];
  }

  /// 清除所有数据
  Future<void> clear() async {
    await _stringBox?.clear();
    await _intBox?.clear();
    await _doubleBox?.clear();
    await _boolBox?.clear();
    await _listBox?.clear();
    await _mapBox?.clear();
    debugPrint('🗑️ 所有存储数据已清除');
  }

  /// 获取存储统计信息
  Map<String, int> getStorageStats() {
    return {
      'strings': _stringBox?.length ?? 0,
      'ints': _intBox?.length ?? 0,
      'doubles': _doubleBox?.length ?? 0,
      'bools': _boolBox?.length ?? 0,
      'lists': _listBox?.length ?? 0,
      'maps': _mapBox?.length ?? 0,
    };
  }

  // ========== SharedPreferences兼容方法 ==========
  
  /// 兼容SharedPreferences的setString方法
  Future<bool> setStringCompat(String key, String value) async {
    try {
      await setString(key, value);
      return true;
    } catch (e) {
      debugPrint('❌ setString失败: $e');
      return false;
    }
  }

  /// 兼容SharedPreferences的setInt方法
  Future<bool> setIntCompat(String key, int value) async {
    try {
      await setInt(key, value);
      return true;
    } catch (e) {
      debugPrint('❌ setInt失败: $e');
      return false;
    }
  }

  /// 兼容SharedPreferences的setBool方法
  Future<bool> setBoolCompat(String key, bool value) async {
    try {
      await setBool(key, value);
      return true;
    } catch (e) {
      debugPrint('❌ setBool失败: $e');
      return false;
    }
  }

  /// 兼容SharedPreferences的setDouble方法
  Future<bool> setDoubleCompat(String key, double value) async {
    try {
      await setDouble(key, value);
      return true;
    } catch (e) {
      debugPrint('❌ setDouble失败: $e');
      return false;
    }
  }
}
