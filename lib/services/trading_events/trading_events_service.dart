/*
  交易事件服务
*/

import 'package:flutter/foundation.dart';
import '../clients/dio_request.dart';
import '../config/api_route.dart';
import '../../pages/market/tabs/opportunities/models/trading_event_model.dart';

class TradingEventsService {
  static final TradingEventsService _instance = TradingEventsService._internal();
  factory TradingEventsService() => _instance;
  TradingEventsService._internal();

  static TradingEventsService get instance => _instance;

  /// 获取指定日期的交易事件数据
  Future<TradingEventResponse?> getEventData({String? currentDate}) async {
    try {
      // 如果没有提供日期，使用当前日期
      final date = currentDate ?? _getCurrentDate();

      debugPrint('🗓️ 获取交易事件数据，日期: $date');

      final response = await DioRequest.instance.get(ApiRoute.eventData, queryParams: {'current_date': date});

      if (response.data != null) {
        final eventResponse = TradingEventResponse.fromJson(response.data);
        debugPrint('✅ 交易事件数据获取成功，事件数量: ${eventResponse.count}');
        return eventResponse;
      } else {
        debugPrint('⚠️ 交易事件数据为空');
        return null;
      }
    } catch (e) {
      debugPrint('❌ 获取交易事件数据失败: $e');
      return null;
    }
  }

  /// 获取当前日期字符串（Y-m-d格式）
  String _getCurrentDate() {
    final now = DateTime.now();
    return '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
  }

  /// 获取指定日期字符串（Y-m-d格式）
  String getDateString(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
