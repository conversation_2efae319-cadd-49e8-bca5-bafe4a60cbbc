import 'package:flutter/foundation.dart';
import '../clients/dio_request.dart';
import '../storage/storage_service.dart';

class CommonService {
  static final CommonService _instance = CommonService._internal();
  static CommonService get instance => _instance;
  CommonService._internal();

  /// 获取IP信息
  Future<Map<String, dynamic>?> getIpInfo() async {
    try {
      final response = await DioRequest.instance.get('https://ipinfo.io/json');
      if (response.success && response.data != null) {
        return Map<String, dynamic>.from(response.data);
      }
      return null;
    } catch (e) {
      debugPrint('获取IP信息失败: $e');
      return null;
    }
  }

  /// 获取多语言文本
  ///
  /// [langList] 多语言数据列表，格式：[{lang: 'zh_cn', text: '文本'}, ...]
  /// [targetLang] 目标语言，默认为 'zh_cn'
  /// [fallbackLang] 备用语言，默认为 'en'
  static String getLocalizedText(List<dynamic>? langList, {String targetLang = 'zh_cn', String fallbackLang = 'en'}) {
    if (langList == null || langList.isEmpty) {
      return '';
    }

    // 尝试精确匹配目标语言
    for (final item in langList) {
      if (item is Map) {
        final lang = item['lang']?.toString() ?? '';
        final text = item['text']?.toString() ?? '';
        if (lang == targetLang && text.isNotEmpty) {
          return text;
        }
      }
    }

    // 尝试模糊匹配目标语言（如 zh_cn 匹配 zh）
    final targetLangPrefix = targetLang.split('_').first;
    for (final item in langList) {
      if (item is Map) {
        final lang = item['lang']?.toString() ?? '';
        final text = item['text']?.toString() ?? '';
        if (lang.startsWith(targetLangPrefix) && text.isNotEmpty) {
          return text;
        }
      }
    }

    // 尝试备用语言
    for (final item in langList) {
      if (item is Map) {
        final lang = item['lang']?.toString() ?? '';
        final text = item['text']?.toString() ?? '';
        if (lang == fallbackLang && text.isNotEmpty) {
          return text;
        }
      }
    }

    // 尝试模糊匹配备用语言
    final fallbackLangPrefix = fallbackLang.split('_').first;
    for (final item in langList) {
      if (item is Map) {
        final lang = item['lang']?.toString() ?? '';
        final text = item['text']?.toString() ?? '';
        if (lang.startsWith(fallbackLangPrefix) && text.isNotEmpty) {
          return text;
        }
      }
    }

    // 如果都没有匹配，返回第一个有效的文本
    for (final item in langList) {
      if (item is Map) {
        final text = item['text']?.toString() ?? '';
        if (text.isNotEmpty) {
          return text;
        }
      }
    }

    return '';
  }

  /// 格式化时间为相对时间（多久前）
  ///
  /// [timeStr] 时间字符串，支持ISO格式或其他可解析格式
  /// 返回格式：刚刚、X分钟前、X小时前、X天前、具体日期
  static String formatTimeAgo(String timeStr) {
    if (timeStr.isEmpty) return '';

    try {
      final dateTime = DateTime.parse(timeStr);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inSeconds < 60) {
        return '刚刚';
      } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes}分钟前';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}小时前';
      } else if (difference.inDays < 30) {
        return '${difference.inDays}天前';
      } else {
        // 超过30天显示具体日期
        return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
      }
    } catch (e) {
      debugPrint('时间格式化失败: $e, 原始时间: $timeStr');
      return timeStr;
    }
  }

  // ========== 自选币种管理方法 ==========

  /// 获取自选币种缓存键
  static String _getFavoritesKey(int marketType) {
    return 'favorites_market_$marketType';
  }

  /// 获取自选币种列表
  ///
  /// [marketType] 市场类型 (1=现货, 5=合约等)
  /// 返回币种ID列表
  static List<int> getFavoriteCoins(int marketType) {
    try {
      final storage = StorageService.instance;
      final cacheKey = _getFavoritesKey(marketType);
      final favoriteData = storage.getList(cacheKey);

      if (favoriteData == null || favoriteData.isEmpty) {
        return [];
      }

      final List<int> favoriteIds = [];
      for (final item in favoriteData) {
        if (item is int) {
          favoriteIds.add(item);
        } else if (item is String) {
          final id = int.tryParse(item);
          if (id != null) {
            favoriteIds.add(id);
          }
        }
      }

      return favoriteIds;
    } catch (e) {
      debugPrint('获取自选币种失败: $e');
      return [];
    }
  }

  /// 添加自选币种
  ///
  /// [currencyId] 币种ID
  /// [marketType] 市场类型
  /// 返回是否添加成功
  static Future<bool> addFavoriteCoin(int currencyId, int marketType) async {
    try {
      final storage = StorageService.instance;
      final cacheKey = _getFavoritesKey(marketType);

      final currentFavorites = getFavoriteCoins(marketType);

      // 检查是否已存在
      if (currentFavorites.contains(currencyId)) {
        debugPrint('币种 $currencyId 已在自选列表中');
        return true;
      }

      // 添加到列表
      currentFavorites.add(currencyId);

      // 保存到缓存
      await storage.setList(cacheKey, currentFavorites);

      debugPrint('成功添加币种 $currencyId 到自选列表 (市场类型: $marketType)');
      return true;
    } catch (e) {
      debugPrint('添加自选币种失败: $e');
      return false;
    }
  }

  /// 删除自选币种
  ///
  /// [currencyId] 币种ID
  /// [marketType] 市场类型
  /// 返回是否删除成功
  static Future<bool> removeFavoriteCoin(int currencyId, int marketType) async {
    try {
      final storage = StorageService.instance;
      final cacheKey = _getFavoritesKey(marketType);

      final currentFavorites = getFavoriteCoins(marketType);

      // 检查是否存在
      if (!currentFavorites.contains(currencyId)) {
        debugPrint('币种 $currencyId 不在自选列表中');
        return true;
      }

      // 从列表中移除
      currentFavorites.remove(currencyId);

      // 保存到缓存
      await storage.setList(cacheKey, currentFavorites);

      debugPrint('成功从自选列表移除币种 $currencyId (市场类型: $marketType)');
      return true;
    } catch (e) {
      debugPrint('删除自选币种失败: $e');
      return false;
    }
  }

  /// 更新自选币种状态（切换收藏状态）
  ///
  /// [currencyId] 币种ID
  /// [marketType] 市场类型
  /// [isFavorite] 是否收藏
  /// 返回是否更新成功
  static Future<bool> updateFavoriteCoin(int currencyId, int marketType, bool isFavorite) async {
    if (isFavorite) {
      return await addFavoriteCoin(currencyId, marketType);
    } else {
      return await removeFavoriteCoin(currencyId, marketType);
    }
  }

  /// 检查币种是否在自选列表中
  ///
  /// [currencyId] 币种ID
  /// [marketType] 市场类型
  /// 返回是否为自选币种
  static bool isFavoriteCoin(int currencyId, int marketType) {
    final favorites = getFavoriteCoins(marketType);
    return favorites.contains(currencyId);
  }

  /// 获取自选币种数量
  ///
  /// [marketType] 市场类型
  /// 返回自选币种数量
  static int getFavoriteCoinsCount(int marketType) {
    final favorites = getFavoriteCoins(marketType);
    return favorites.length;
  }

  /// 清空自选币种列表
  ///
  /// [marketType] 市场类型
  /// 返回是否清空成功
  static Future<bool> clearFavoriteCoins(int marketType) async {
    try {
      final storage = StorageService.instance;
      final cacheKey = _getFavoritesKey(marketType);

      await storage.setList(cacheKey, []);

      debugPrint('成功清空自选币种列表 (市场类型: $marketType)');
      return true;
    } catch (e) {
      debugPrint('清空自选币种列表失败: $e');
      return false;
    }
  }

  /// 批量添加自选币种
  ///
  /// [currencyIds] 币种ID列表
  /// [marketType] 市场类型
  /// 返回是否添加成功
  static Future<bool> addFavoriteCoinsBatch(List<int> currencyIds, int marketType) async {
    try {
      final storage = StorageService.instance;
      final cacheKey = _getFavoritesKey(marketType);

      final currentFavorites = getFavoriteCoins(marketType);

      // 添加新的币种ID（去重）
      for (final currencyId in currencyIds) {
        if (!currentFavorites.contains(currencyId)) {
          currentFavorites.add(currencyId);
        }
      }

      // 保存到缓存
      await storage.setList(cacheKey, currentFavorites);

      debugPrint('成功批量添加 ${currencyIds.length} 个币种到自选列表 (市场类型: $marketType)');
      return true;
    } catch (e) {
      debugPrint('批量添加自选币种失败: $e');
      return false;
    }
  }

  /// 批量删除自选币种
  ///
  /// [currencyIds] 币种ID列表
  /// [marketType] 市场类型
  /// 返回是否删除成功
  static Future<bool> removeFavoriteCoinsBatch(List<int> currencyIds, int marketType) async {
    try {
      final storage = StorageService.instance;
      final cacheKey = _getFavoritesKey(marketType);

      final currentFavorites = getFavoriteCoins(marketType);

      // 从列表中移除指定的币种ID
      for (final currencyId in currencyIds) {
        currentFavorites.remove(currencyId);
      }

      // 保存到缓存
      await storage.setList(cacheKey, currentFavorites);

      debugPrint('成功批量删除 ${currencyIds.length} 个币种从自选列表 (市场类型: $marketType)');
      return true;
    } catch (e) {
      debugPrint('批量删除自选币种失败: $e');
      return false;
    }
  }
}
