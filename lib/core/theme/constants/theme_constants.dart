// 主题相关常量统一定义
import 'package:flutter/material.dart';

/// 主题模式枚举
enum AppThemeMode { light, dark, system }

/// 模板名称枚举 - 统一的模板标识
enum TemplateType {
  base('base', '默认主题'),
  okx('okx', 'OKX主题'),
  bitget('bitget', 'Bitget主题');

  const TemplateType(this.id, this.displayName);

  /// 模板ID
  final String id;

  /// 显示名称
  final String displayName;

  /// 从ID获取模板类型
  static TemplateType fromId(String id) {
    for (final template in TemplateType.values) {
      if (template.id == id) {
        return template;
      }
    }
    return TemplateType.base; // 默认返回base模板
  }

  /// 获取所有可用模板ID
  static List<String> get allIds =>
      TemplateType.values.map((e) => e.id).toList();

  /// 获取所有模板类型
  static List<TemplateType> get all => TemplateType.values;
}

/// 模板名称常量 - 保持向后兼容
class TemplateNames {
  static const String base = 'base';
  static const String okx = 'okx';
  static const String bitget = 'bitget';

  /// 获取所有可用模板
  static List<String> get all => TemplateType.allIds;

  /// 获取模板显示名称
  static String getDisplayName(String templateName) {
    return TemplateType.fromId(templateName).displayName;
  }
}

/// 主题模式工具
class ThemeModeUtils {
  /// 获取主题模式显示名称
  static String getDisplayName(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.light:
        return '浅色模式';
      case AppThemeMode.dark:
        return '深色模式';
      case AppThemeMode.system:
        return '跟随系统';
    }
  }

  /// 从索引获取主题模式
  static AppThemeMode fromIndex(int index) {
    if (index >= 0 && index < AppThemeMode.values.length) {
      return AppThemeMode.values[index];
    }
    return AppThemeMode.system;
  }

  /// 获取Flutter的ThemeMode
  static ThemeMode toFlutterThemeMode(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }
}

/// 主题存储键名
class ThemeStorageKeys {
  static const String themeMode = 'app_theme_mode';
  static const String templateName = 'app_template_name';
}
