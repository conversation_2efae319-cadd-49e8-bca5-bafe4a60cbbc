// Flutter ThemeData 生成器
import 'package:flutter/material.dart';
import 'extensions/template_theme_extension.dart';
import 'constants/theme_constants.dart';
import '../templates/template_config.dart';
import '../templates/template_registry.dart' hide TemplateNames;

/// Flutter ThemeData 生成器 - 使用当前主题颜色生成ThemeData，当主题切换时，MaterialApp会自动重建并应用新的ThemeData
class ThemeDataGenerator {
  static final ThemeDataGenerator _instance = ThemeDataGenerator._internal();
  static ThemeDataGenerator get instance => _instance;
  ThemeDataGenerator._internal();

  /// 根据模板名称和深浅色模式获取颜色配置
  TemplateColors _getColorsForTemplate(String templateName, bool isDark) {
    final template = TemplateRegistry.instance.getTemplateOrDefault(
      templateName,
    );
    return template.getColors(isDark);
  }

  /// 生成浅色主题 - 使用当前主题颜色
  ThemeData generateLightTheme([String? templateName]) {
    final currentTemplate = templateName ?? TemplateNames.base;

    // 根据模板获取对应的颜色配置
    final colors = _getColorsForTemplate(currentTemplate, false);

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,

      // 设置全局字体为 Switzer
      fontFamily: 'Switzer',

      // 模板扩展 - 将模板信息存储在主题中
      extensions: [TemplateThemeExtension(templateName: currentTemplate)],

      // 基础颜色方案 - 使用模板特定的颜色
      colorScheme: ColorScheme.light(
        primary: colors.primary,
        surface: colors.surface,
        onPrimary: colors.buttonText,
        onSurface: colors.textPrimary,
      ),

      // 应用栏主题
      appBarTheme: AppBarTheme(
        elevation: 0,
        centerTitle: true,
        backgroundColor: colors.surface,
        foregroundColor: colors.textPrimary,
        iconTheme: IconThemeData(color: colors.textPrimary),
      ),

      // 卡片主题
      cardTheme: CardThemeData(
        elevation: 2,
        color: colors.surface,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),

      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colors.surface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colors.primary),
        ),
      ),

      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor: colors.buttonPrimary,
          foregroundColor: colors.buttonText,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),

      // 文本主题
      textTheme: TextTheme(
        bodyLarge: TextStyle(color: colors.textPrimary),
        bodyMedium: TextStyle(color: colors.textPrimary),
        bodySmall: TextStyle(color: colors.textTertiary),
        titleLarge: TextStyle(color: colors.textPrimary),
        titleMedium: TextStyle(color: colors.textPrimary),
        titleSmall: TextStyle(color: colors.textPrimary),
      ),

      // Scaffold背景色
      scaffoldBackgroundColor: colors.background,
    );
  }

  /// 生成深色主题 - 使用当前主题颜色
  ThemeData generateDarkTheme([String? templateName]) {
    final currentTemplate = templateName ?? TemplateNames.base;

    // 根据模板获取对应的颜色配置
    final colors = _getColorsForTemplate(currentTemplate, true);

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,

      // 设置全局字体为 Switzer
      fontFamily: 'Switzer',

      // 模板扩展 - 将模板信息存储在主题中
      extensions: [TemplateThemeExtension(templateName: currentTemplate)],

      // 基础颜色方案 - 使用模板特定的颜色
      colorScheme: ColorScheme.dark(
        primary: colors.primary,
        surface: colors.surface,
        onPrimary: colors.buttonText,
        onSurface: colors.textPrimary,
      ),

      // 应用栏主题
      appBarTheme: AppBarTheme(
        elevation: 0,
        centerTitle: true,
        backgroundColor: colors.surface,
        foregroundColor: colors.textPrimary,
        iconTheme: IconThemeData(color: colors.textPrimary),
      ),

      // 卡片主题
      cardTheme: CardThemeData(
        elevation: 2,
        color: colors.surface,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),

      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colors.surface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colors.primary),
        ),
      ),

      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor: colors.buttonPrimary,
          foregroundColor: colors.buttonText,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),

      // 文本主题
      textTheme: TextTheme(
        bodyLarge: TextStyle(color: colors.textPrimary),
        bodyMedium: TextStyle(color: colors.textPrimary),
        bodySmall: TextStyle(color: colors.textTertiary),
        titleLarge: TextStyle(color: colors.textPrimary),
        titleMedium: TextStyle(color: colors.textPrimary),
        titleSmall: TextStyle(color: colors.textPrimary),
      ),

      // Scaffold背景色
      scaffoldBackgroundColor: colors.background,
    );
  }

  /// 根据当前主题设置生成对应的 ThemeData
  ThemeData generateCurrentTheme(Brightness brightness) {
    switch (brightness) {
      case Brightness.light:
        return generateLightTheme();
      case Brightness.dark:
        return generateDarkTheme();
    }
  }
}
