import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'constants/theme_constants.dart';
import '../templates/template_theme_provider.dart';

/// 简化的主题状态管理器
/// 使用 ChangeNotifier 实现响应式主题切换
class ThemeProvider with ChangeNotifier {
  static final ThemeProvider _instance = ThemeProvider._internal();
  static ThemeProvider get instance => _instance;
  ThemeProvider._internal();

  // 私有属性
  AppThemeMode _themeMode = AppThemeMode.system;
  String _templateName = TemplateNames.base;
  bool _isInitialized = false;

  // 存储键名
  static const String _themeModeKey = 'app_theme_mode';
  static const String _templateNameKey = 'app_template_name';

  // 公共属性
  /// 获取当前主题模式
  AppThemeMode get themeMode => _themeMode;

  /// 获取当前模板名称
  String get templateName => _templateName;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 是否为深色模式
  bool get isDark {
    switch (_themeMode) {
      case AppThemeMode.light:
        return false;
      case AppThemeMode.dark:
        return true;
      case AppThemeMode.system:
        return WidgetsBinding.instance.platformDispatcher.platformBrightness ==
            Brightness.dark;
    }
  }

  /// 获取Flutter的ThemeMode
  ThemeMode get flutterThemeMode {
    return ThemeModeUtils.toFlutterThemeMode(_themeMode);
  }

  /// 初始化主题管理器
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🎨 初始化主题管理器...');

      // 首先初始化模板注册表
      TemplateThemeProvider.instance;

      // 从本地存储加载设置
      final prefs = await SharedPreferences.getInstance();

      // 加载主题模式
      final savedThemeModeIndex = prefs.getInt(_themeModeKey);
      if (savedThemeModeIndex != null) {
        _themeMode = ThemeModeUtils.fromIndex(savedThemeModeIndex);
      }

      // 加载模板名称
      final savedTemplateName = prefs.getString(_templateNameKey);
      if (savedTemplateName != null && savedTemplateName.isNotEmpty) {
        _templateName = savedTemplateName;
      }

      _isInitialized = true;
      notifyListeners();

      debugPrint('✅ 主题管理器初始化完成: 模式: $_themeMode, 模板: $_templateName');
    } catch (e) {
      debugPrint('❌ 主题管理器初始化失败: $e');
      // 使用默认设置
      _themeMode = AppThemeMode.system;
      _templateName = TemplateNames.base;
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// 设置主题模式
  Future<void> setThemeMode(AppThemeMode mode) async {
    if (_themeMode == mode) return;

    _themeMode = mode;
    notifyListeners();

    // 保存到本地存储
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeModeKey, mode.index);
      debugPrint('✅ 主题模式已保存: $mode');
    } catch (e) {
      debugPrint('❌ 主题模式保存失败: $e');
    }
  }

  /// 设置模板名称
  Future<void> setTemplate(String templateName) async {
    if (_templateName == templateName) return;

    _templateName = templateName;
    notifyListeners();

    // 保存到本地存储
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_templateNameKey, templateName);
      debugPrint('✅ 主题模板已保存: $templateName');
    } catch (e) {
      debugPrint('❌ 主题模板保存失败: $e');
    }
  }

  /// 设置浅色模式
  Future<void> setLightMode() async {
    await setThemeMode(AppThemeMode.light);
  }

  /// 设置深色模式
  Future<void> setDarkMode() async {
    await setThemeMode(AppThemeMode.dark);
  }

  /// 设置系统模式
  Future<void> setSystemMode() async {
    await setThemeMode(AppThemeMode.system);
  }

  /// 切换主题模式
  Future<void> toggleThemeMode() async {
    switch (_themeMode) {
      case AppThemeMode.light:
        await setDarkMode();
        break;
      case AppThemeMode.dark:
        await setSystemMode();
        break;
      case AppThemeMode.system:
        await setLightMode();
        break;
    }
  }

  /// 打印主题信息（用于调试）
  void printThemeInfo() {
    debugPrint('=== 主题信息 ===');
    debugPrint('主题模式: $_themeMode');
    debugPrint('模板名称: $_templateName');
    debugPrint('是否深色: $isDark');
    debugPrint('是否已初始化: $_isInitialized');
    debugPrint('===============');
  }
}
