// 主题工具类 - 简化版本
import 'package:flutter/material.dart';
import 'constants/theme_constants.dart';

/// 主题工具类 - 简化版本
class ThemeUtils {
  /// 检查是否为深色模式
  static bool isDarkMode(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark;
  }

  /// 根据主题获取对应的颜色
  static Color getColor(
    BuildContext context, {
    required Color light,
    required Color dark,
  }) {
    return isDarkMode(context) ? dark : light;
  }

  /// 获取主题模式显示名称
  static String getThemeModeDisplayName(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.light:
        return '浅色模式';
      case AppThemeMode.dark:
        return '深色模式';
      case AppThemeMode.system:
        return '跟随系统';
    }
  }

  /// 获取模板显示名称
  static String getTemplateDisplayName(String templateName) {
    switch (templateName) {
      case 'base':
        return '默认主题';
      case 'okx':
        return 'OKX主题';
      default:
        return templateName;
    }
  }

  /// 获取可用的主题模式列表
  static List<AppThemeMode> getAvailableThemeModes() {
    return AppThemeMode.values;
  }

  /// 获取可用的模板列表
  static List<String> getAvailableTemplates() {
    return TemplateNames.all;
  }
}
