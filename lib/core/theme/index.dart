// 主题系统统一导出

// === 主题常量 ===
// 主题相关常量（模式、模板名称等）
export 'constants/theme_constants.dart';

// === 核心主题管理 ===
// 主题状态管理器（主要的主题切换功能）
export 'theme_provider.dart';

// Flutter ThemeData 生成器
export 'theme_data_generator.dart';

// === 主题访问接口 ===
// 注意：旧的颜色和样式系统已被新的模板系统替代
// 请使用新的模板系统：
// - context.templateColors 替代 context.colors 或 theme.xxx(context)
// - context.templateStyle 替代 themeStyles.xxx(context)
// - TemplateManager.instance 进行模板切换

// === 主题工具 ===
// 主题工具类（便捷的主题切换方法和组件）
export 'theme_utils.dart';

// 模板主题扩展
export 'extensions/template_theme_extension.dart';

// === 新增：主题模板系统 ===
// 主题模板接口
// export 'interfaces/theme_template_interface.dart'; // 已移除复杂的接口

// 主题模板注册系统
// export 'registry/theme_template_registry.dart'; // 已移除复杂的注册系统

// 主题缓存管理
// export 'cache/theme_cache_manager.dart'; // 已移除复杂的缓存管理

// === 模板系统 ===
// 注意：模板系统已迁移到新的统一架构
// 请使用 core/templates/index.dart 导入新的模板系统
