// ========================================
// 🎨 模板主题扩展 - 将模板信息整合到Flutter主题系统中
// ========================================

import 'package:flutter/material.dart';
import '../constants/theme_constants.dart';
import '../../templates/template_manager.dart';

/// 模板主题扩展 - 用于在ThemeData中存储模板信息
class TemplateThemeExtension extends ThemeExtension<TemplateThemeExtension> {
  final String templateName;

  const TemplateThemeExtension({required this.templateName});

  @override
  ThemeExtension<TemplateThemeExtension> copyWith({String? templateName}) {
    return TemplateThemeExtension(
      templateName: templateName ?? this.templateName,
    );
  }

  @override
  ThemeExtension<TemplateThemeExtension> lerp(
    ThemeExtension<TemplateThemeExtension>? other,
    double t,
  ) {
    if (other is! TemplateThemeExtension) {
      return this;
    }
    // 模板切换不需要插值动画，直接返回目标模板
    return t < 0.5 ? this : other;
  }

  /// 从BuildContext获取当前模板名称
  static String of(BuildContext context) {
    final extension = Theme.of(context).extension<TemplateThemeExtension>();
    return extension?.templateName ?? TemplateNames.base;
  }

  /// 从BuildContext获取当前模板类型
  static TemplateType templateTypeOf(BuildContext context) {
    final templateName = of(context);
    return TemplateType.fromId(templateName);
  }

  /// 创建默认扩展
  static TemplateThemeExtension get defaultExtension {
    return TemplateThemeExtension(
      templateName: TemplateManager.instance.currentTemplateName,
    );
  }

  /// 创建指定模板的扩展
  static TemplateThemeExtension forTemplate(TemplateType template) {
    return TemplateThemeExtension(templateName: template.id);
  }

  /// 创建指定模板名称的扩展
  static TemplateThemeExtension forTemplateName(String templateName) {
    return TemplateThemeExtension(templateName: templateName);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TemplateThemeExtension &&
        other.templateName == templateName;
  }

  @override
  int get hashCode => templateName.hashCode;

  @override
  String toString() => 'TemplateThemeExtension(templateName: $templateName)';
}
