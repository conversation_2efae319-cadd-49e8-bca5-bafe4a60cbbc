// ========================================
// 🎨 OKX模板实现
// ========================================

import 'package:flutter/material.dart';
import '../template_config.dart';
import '../styles/kline/kline_styles.dart';

/// OKX模板配置实现
class OkxTemplate extends TemplateConfig {
  @override
  String get name => 'okx';

  @override
  String get displayName => 'OKX主题';

  @override
  TemplateColors get lightColors => const TemplateColors(
    // 品牌颜色 - OKX蓝色系
    primary: Color(0xFF1890FF),
    secondary: Color(0xFF40A9FF),

    // 背景颜色
    background: Color(0xFFF5F5F5),
    surface: Color(0xFFFAFAFA),
    cardBackground: Color(0xFFFFFFFF),
    popupBackground: Color(0xFFFFFFFF),
    tabbarBackground: Color(0xFFFFFFFF),
    tabbarActive: Color(0xFF1890FF),
    dragIndicator: Color(0xFF4A4A55),

    // 文本颜色
    textPrimary: Color(0xFF1A1A1A),
    textSecondary: Color(0xFF666666),
    textTertiary: Color(0xFF999999),

    // 按钮颜色
    buttonPrimary: Color(0xFF1890FF),
    buttonSecondary: Color(0xFFF0F0F0),
    buttonText: Color(0xFFFFFFFF),
    buttonTextSecondary: Color(0xFF333333),

    // 输入框颜色
    inputBackground: Color(0xFFF5F5F5),
    inputBorder: Color(0xFFE5E5E5),
    inputFocusedBorder: Color(0xFF1890FF),

    // 交易颜色 - OKX特色
    tradeBuy: Color(0xFF52C41A),
    tradeSell: Color(0xFFFF4D4F),

    // 状态颜色
    success: Color(0xFF52C41A),
    warning: Color(0xFFFAAD14),
    error: Color(0xFFFF4D4F),
    info: Color(0xFF1890FF),
    white: Color(0xFFFFFFFF),

    // 分割线和边框
    divider: Color(0xFFE5E5E5),
    border: Color(0xFFD9D9D9),

    // 骨架屏颜色
    skeletonBase: Color(0xFFE0E0E0),
    skeletonHighlight: Color(0xFFF5F5F5),
  );

  @override
  TemplateColors get darkColors => const TemplateColors(
    // 品牌颜色
    primary: Color(0xFF1890FF),
    secondary: Color(0xFF40A9FF),

    // 背景颜色
    background: Color(0xFF141414),
    surface: Color(0xFF1F1F1F),
    cardBackground: Color(0xFF1F1F1F),
    popupBackground: Color(0xFF1F1F1F),
    tabbarBackground: Color(0xFF1F1F1F),
    tabbarActive: Color(0xFF1890FF),
    dragIndicator: Color(0xFF4A4A55),

    // 文本颜色
    textPrimary: Color(0xFFFFFFFF),
    textSecondary: Color(0xFFCCCCCC),
    textTertiary: Color(0xFF999999),
    white: Color(0xFFFFFFFF),

    // 按钮颜色
    buttonPrimary: Color(0xFF1890FF),
    buttonSecondary: Color(0xFF404040),
    buttonText: Color(0xFFFFFFFF),
    buttonTextSecondary: Color(0xFFCCCCCC),

    // 输入框颜色
    inputBackground: Color(0xFF333333),
    inputBorder: Color(0xFF404040),
    inputFocusedBorder: Color(0xFF1890FF),

    // 交易颜色
    tradeBuy: Color(0xFF52C41A),
    tradeSell: Color(0xFFFF4D4F),

    // 状态颜色
    success: Color(0xFF52C41A),
    warning: Color(0xFFFAAD14),
    error: Color(0xFFFF4D4F),
    info: Color(0xFF1890FF),

    // 分割线和边框
    divider: Color(0xFF404040),
    border: Color(0xFF333333),

    // 骨架屏颜色
    skeletonBase: Color(0xFF333333),
    skeletonHighlight: Color(0xFF404040),
  );

  @override
  TemplateStyles get styles => const TemplateStyles(
    // 圆角配置 - OKX使用更小的圆角
    borderRadiusSmall: 2.0,
    borderRadiusMedium: 6.0,
    borderRadiusLarge: 12.0,

    // 间距配置
    spacingSmall: 8.0,
    spacingMedium: 16.0,
    spacingLarge: 24.0,

    // 阴影配置 - OKX扁平化设计，较低阴影
    elevationLow: 0.0,
    elevationMedium: 2.0,
    elevationHigh: 4.0,

    // 边框配置 - OKX使用更粗的边框
    borderWidthThin: 1.0,
    borderWidthMedium: 1.5,
    borderWidthThick: 2.0,

    // 按钮高度
    buttonHeightMedium: 40.0,
  );

  @override
  TemplateFonts get fonts => const TemplateFonts(
    primaryFont: 'Switzer',
    secondaryFont: 'IBMPlexSans',
    numberFont: 'IBMPlexSans',
  );

  @override
  TemplateAssets get assets => const TemplateAssets(
    iconPath: 'assets/icons/',
    imagePath: 'assets/images/okx/',
    lottiePath: 'assets/lottie/',
  );

  @override
  TemplateKlineStyles get lightKlineStyles => const TemplateKlineStyles(
    // 蜡烛图样式 - OKX特色
    bullCandleColor: Color(0xFF52C41A),
    bearCandleColor: Color(0xFFFF4D4F),
    candleBorderColor: Color(0xFFE5E5E5),
    candleMaxWidth: 50.0,
    candleMinWidth: 1.0,
    candleLineWidth: 1.0,
    candleSpacingRatio: 0.1,

    // 图表背景样式
    chartBackgroundColor: Color(0xFFF5F5F5),
    chartSurfaceColor: Color(0xFFFAFAFA),
    chartBorderColor: Color(0xFFE5E5E5),

    // 网格线样式
    gridLineColor: Color(0xFFE8E8E8),
    gridLineWidth: 0.5,
    showGridLines: true,
    gridLineDashed: false,

    // 十字线样式
    crossLineColor: Color(0xFF666666),
    crossLineWidth: 1.0,
    crossLineDashed: true,

    // 价格线样式
    latestPriceLineColor: Color(0xFF1890FF),
    markLineColor: Color(0xFF1A1A1A),
    averagePriceLineColor: Color(0xFFFF9F43),
    priceLineWidth: 1.0,

    // 文本样式
    primaryTextColor: Color(0xFF1A1A1A),
    secondaryTextColor: Color(0xFF666666),
    priceTextColor: Color(0xFF1A1A1A),
    textFontSize: 10.0,
    priceTextFontSize: 12.0,
    textFontWeight: FontWeight.w400,

    // 工具提示样式
    tooltipBackgroundColor: Color(0xFFFFFFFF),
    tooltipTextColor: Color(0xFF1A1A1A),
    tooltipBorderColor: Color(0xFFE5E5E5),
    tooltipBorderRadius: 4.0,

    // 成交量样式
    volumeBullColor: Color(0x9952C41A),
    volumeBearColor: Color(0x99FF4D4F),
    volumeOpacity: 0.6,

    // 指标样式
    maIndicatorColors: [
      Color(0xFFFFD700), // MA5
      Color(0xFF9932CC), // MA10
      Color(0xFF00CED1), // MA20
      Color(0xFFFF69B4), // MA30
      Color(0xFF32CD32), // MA60
    ],
    macdLineColor: Color(0xFFFFD700),
    macdSignalColor: Color(0xFF9932CC),
    macdHistogramColor: Color(0xFF666666),
    rsiLineColor: Color(0xFF00CED1),
    indicatorLineWidth: 1.0,

    // 缩放和手势样式
    magnifierBackgroundColor: Color(0xFFFFFFFF),
    magnifierBorderColor: Color(0xFFE5E5E5),
    magnifierBorderRadius: 4.0,

    // 加载样式
    loadingIndicatorColor: Color(0xFF1890FF),
    loadingBackgroundColor: Color(0xFFF5F5F5),

    // 拖拽和交互样式
    dragBackgroundColor: Color(0xFFF0F0F0),
    themeColor: Color(0xFF1890FF),
    drawColor: Color(0xFF666666),
    ticksTextColor: Color(0xB3666666),
    latestPriceTextBackgroundColor: Color(0xFFFFFFFF),
    lastPriceTextBackgroundColor: Color(0xFFFFFFFF),
    countDownTextBackgroundColor: Color(0xFFFFFFFF),

    // 成交量轨迹样式
    buyTrackColor: Color(0xB352C41A),
    sellTrackColor: Color(0xB3FF4D4F),
    priceLevelColor: Color(0x80666666),
  );

  @override
  TemplateKlineStyles get darkKlineStyles => const TemplateKlineStyles(
    // 蜡烛图样式 - OKX特色
    bullCandleColor: Color(0xFF52C41A),
    bearCandleColor: Color(0xFFFF4D4F),
    candleBorderColor: Color(0xFF333333),
    candleMaxWidth: 50.0,
    candleMinWidth: 1.0,
    candleLineWidth: 1.0,
    candleSpacingRatio: 0.1,

    // 图表背景样式
    chartBackgroundColor: Color(0xFF1A1A1A),
    chartSurfaceColor: Color(0xFF2D2D2D),
    chartBorderColor: Color(0xFF333333),

    // 网格线样式
    gridLineColor: Color(0xFF404040),
    gridLineWidth: 0.5,
    showGridLines: true,
    gridLineDashed: false,

    // 十字线样式
    crossLineColor: Color(0xFFCCCCCC),
    crossLineWidth: 1.0,
    crossLineDashed: true,

    // 价格线样式
    latestPriceLineColor: Color(0xFF1890FF),
    markLineColor: Color(0xFFFFFFFF),
    averagePriceLineColor: Color(0xFFFF9F43),
    priceLineWidth: 1.0,

    // 文本样式
    primaryTextColor: Color(0xFFFFFFFF),
    secondaryTextColor: Color(0xFFCCCCCC),
    priceTextColor: Color(0xFFFFFFFF),
    textFontSize: 10.0,
    priceTextFontSize: 12.0,
    textFontWeight: FontWeight.w400,

    // 工具提示样式
    tooltipBackgroundColor: Color(0xFF2D2D2D),
    tooltipTextColor: Color(0xFFFFFFFF),
    tooltipBorderColor: Color(0xFF333333),
    tooltipBorderRadius: 4.0,

    // 成交量样式
    volumeBullColor: Color(0x9952C41A),
    volumeBearColor: Color(0x99FF4D4F),
    volumeOpacity: 0.6,

    // 指标样式
    maIndicatorColors: [
      Color(0xFFFFD700), // MA5
      Color(0xFF9932CC), // MA10
      Color(0xFF00CED1), // MA20
      Color(0xFFFF69B4), // MA30
      Color(0xFF32CD32), // MA60
    ],
    macdLineColor: Color(0xFFFFD700),
    macdSignalColor: Color(0xFF9932CC),
    macdHistogramColor: Color(0xFFCCCCCC),
    rsiLineColor: Color(0xFF00CED1),
    indicatorLineWidth: 1.0,

    // 缩放和手势样式
    magnifierBackgroundColor: Color(0xFF2D2D2D),
    magnifierBorderColor: Color(0xFF333333),
    magnifierBorderRadius: 4.0,

    // 加载样式
    loadingIndicatorColor: Color(0xFF1890FF),
    loadingBackgroundColor: Color(0xFF1A1A1A),

    // 拖拽和交互样式
    dragBackgroundColor: Color(0xFF2D2D2D),
    themeColor: Color(0xFF1890FF),
    drawColor: Color(0xFFCCCCCC),
    ticksTextColor: Color(0xB3CCCCCC),
    latestPriceTextBackgroundColor: Color(0xFF2D2D2D),
    lastPriceTextBackgroundColor: Color(0xFF2D2D2D),
    countDownTextBackgroundColor: Color(0xFF2D2D2D),

    // 成交量轨迹样式
    buyTrackColor: Color(0xB352C41A),
    sellTrackColor: Color(0xB3FF4D4F),
    priceLevelColor: Color(0x80CCCCCC),
  );
}
