// ========================================
// 🎨 Bitget模板实现
// ========================================

import 'package:flutter/material.dart';
import '../template_config.dart';
import '../styles/kline/kline_styles.dart';

/// Bitget模板配置实现
class BitgetTemplate extends TemplateConfig {
  @override
  String get name => 'bitget';

  @override
  String get displayName => 'Bitget主题';

  @override
  TemplateColors get lightColors => const TemplateColors(
    // 品牌颜色 - Bitget黄色系
    primary: Color(0xFFF7931A),
    secondary: Color(0xFFFFB84D),

    // 背景颜色
    background: Color(0xFFFFFFFF),
    surface: Color(0xFFF8F9FA),
    cardBackground: Color(0xFFFFFFFF),
    popupBackground: Color(0xFFFFFFFF),
    tabbarBackground: Color(0xFFFFFFFF),
    tabbarActive: Color(0xFFF7931A),
    dragIndicator: Color(0xFF4A4A55),

    // 文本颜色
    textPrimary: Color(0xFF1A1A1A),
    textSecondary: Color(0xFF666666),
    textTertiary: Color(0xFF999999),

    // 按钮颜色
    buttonPrimary: Color(0xFFF7931A),
    buttonSecondary: Color(0xFFF0F0F0),
    buttonText: Color(0xFFFFFFFF),
    buttonTextSecondary: Color(0xFF333333),

    // 输入框颜色
    inputBackground: Color(0xFFF5F5F5),
    inputBorder: Color(0xFFE5E5E5),
    inputFocusedBorder: Color(0xFFF7931A),

    // 交易颜色 - Bitget特色
    tradeBuy: Color(0xFF00D4AA),
    tradeSell: Color(0xFFFF5B5B),

    // 状态颜色
    success: Color(0xFF00D4AA),
    warning: Color(0xFFF7931A),
    error: Color(0xFFFF5B5B),
    info: Color(0xFF4A90E2),
    white: Color(0xFFFFFFFF),

    // 分割线和边框
    divider: Color(0xFFE5E5E5),
    border: Color(0xFFE0E0E0),

    // 骨架屏颜色
    skeletonBase: Color(0xFFE0E0E0),
    skeletonHighlight: Color(0xFFF5F5F5),
  );

  @override
  TemplateColors get darkColors => const TemplateColors(
    // 品牌颜色
    primary: Color(0xFFF7931A),
    secondary: Color(0xFFFFB84D),

    // 背景颜色
    background: Color(0xFF1A1A1A),
    surface: Color(0xFF2D2D2D),
    cardBackground: Color(0xFF2D2D2D),
    popupBackground: Color(0xFF2D2D2D),
    tabbarBackground: Color(0xFF2D2D2D),
    tabbarActive: Color(0xFFF7931A),
    dragIndicator: Color(0xFF4A4A55),

    // 文本颜色
    textPrimary: Color(0xFFFFFFFF),
    textSecondary: Color(0xFFCCCCCC),
    textTertiary: Color(0xFF999999),

    // 按钮颜色
    buttonPrimary: Color(0xFFF7931A),
    buttonSecondary: Color(0xFF404040),
    buttonText: Color(0xFFFFFFFF),
    buttonTextSecondary: Color(0xFFCCCCCC),

    // 输入框颜色
    inputBackground: Color(0xFF333333),
    inputBorder: Color(0xFF404040),
    inputFocusedBorder: Color(0xFFF7931A),

    // 交易颜色
    tradeBuy: Color(0xFF00D4AA),
    tradeSell: Color(0xFFFF5B5B),

    // 状态颜色
    success: Color(0xFF00D4AA),
    warning: Color(0xFFF7931A),
    error: Color(0xFFFF5B5B),
    info: Color(0xFF4A90E2),
    white: Color(0xFFFFFFFF),

    // 分割线和边框
    divider: Color(0xFF404040),
    border: Color(0xFF333333),

    // 骨架屏颜色
    skeletonBase: Color(0xFF333333),
    skeletonHighlight: Color(0xFF404040),
  );

  @override
  TemplateStyles get styles => const TemplateStyles(
    // 圆角配置 - Bitget使用中等圆角
    borderRadiusSmall: 6.0,
    borderRadiusMedium: 12.0,
    borderRadiusLarge: 20.0,

    // 间距配置
    spacingSmall: 8.0,
    spacingMedium: 16.0,
    spacingLarge: 24.0,

    // 阴影配置
    elevationLow: 2.0,
    elevationMedium: 4.0,
    elevationHigh: 8.0,

    // 边框配置
    borderWidthThin: 1.0,
    borderWidthMedium: 2.0,
    borderWidthThick: 3.0,

    // 按钮高度
    buttonHeightMedium: 48.0,
  );

  @override
  TemplateFonts get fonts => const TemplateFonts(
    primaryFont: 'Switzer',
    secondaryFont: 'IBMPlexSans',
    numberFont: 'IBMPlexSans',
  );

  @override
  TemplateAssets get assets => const TemplateAssets(
    iconPath: 'assets/icons/',
    imagePath: 'assets/images/bitget/',
    lottiePath: 'assets/lottie/',
  );

  @override
  TemplateKlineStyles get lightKlineStyles => const TemplateKlineStyles(
    // 蜡烛图样式 - Bitget特色
    bullCandleColor: Color(0xFF00D4AA),
    bearCandleColor: Color(0xFFFF5B5B),
    candleBorderColor: Color(0xFFE5E5E5),
    candleMaxWidth: 50.0,
    candleMinWidth: 1.0,
    candleLineWidth: 1.0,
    candleSpacingRatio: 0.1,

    // 图表背景样式
    chartBackgroundColor: Color(0xFFFFFFFF),
    chartSurfaceColor: Color(0xFFF8F9FA),
    chartBorderColor: Color(0xFFE5E5E5),

    // 网格线样式
    gridLineColor: Color(0xFFE8E8E8),
    gridLineWidth: 0.5,
    showGridLines: true,
    gridLineDashed: false,

    // 十字线样式
    crossLineColor: Color(0xFF666666),
    crossLineWidth: 1.0,
    crossLineDashed: true,

    // 价格线样式
    latestPriceLineColor: Color(0xFFF7931A),
    markLineColor: Color(0xFF1A1A1A),
    averagePriceLineColor: Color(0xFFFF9F43),
    priceLineWidth: 1.0,

    // 文本样式
    primaryTextColor: Color(0xFF1A1A1A),
    secondaryTextColor: Color(0xFF666666),
    priceTextColor: Color(0xFF1A1A1A),
    textFontSize: 10.0,
    priceTextFontSize: 12.0,
    textFontWeight: FontWeight.w400,

    // 工具提示样式
    tooltipBackgroundColor: Color(0xFFFFFFFF),
    tooltipTextColor: Color(0xFF1A1A1A),
    tooltipBorderColor: Color(0xFFE5E5E5),
    tooltipBorderRadius: 4.0,

    // 成交量样式
    volumeBullColor: Color(0x9900D4AA),
    volumeBearColor: Color(0x99FF5B5B),
    volumeOpacity: 0.6,

    // 指标样式
    maIndicatorColors: [
      Color(0xFFFFD700), // MA5
      Color(0xFF9932CC), // MA10
      Color(0xFF00CED1), // MA20
      Color(0xFFFF69B4), // MA30
      Color(0xFF32CD32), // MA60
    ],
    macdLineColor: Color(0xFFFFD700),
    macdSignalColor: Color(0xFF9932CC),
    macdHistogramColor: Color(0xFF666666),
    rsiLineColor: Color(0xFF00CED1),
    indicatorLineWidth: 1.0,

    // 缩放和手势样式
    magnifierBackgroundColor: Color(0xFFFFFFFF),
    magnifierBorderColor: Color(0xFFE5E5E5),
    magnifierBorderRadius: 4.0,

    // 加载样式
    loadingIndicatorColor: Color(0xFFF7931A),
    loadingBackgroundColor: Color(0xFFFFFFFF),

    // 拖拽和交互样式
    dragBackgroundColor: Color(0xFFF8F9FA),
    themeColor: Color(0xFFF7931A),
    drawColor: Color(0xFF666666),
    ticksTextColor: Color(0xB3666666),
    latestPriceTextBackgroundColor: Color(0xFFFFFFFF),
    lastPriceTextBackgroundColor: Color(0xFFFFFFFF),
    countDownTextBackgroundColor: Color(0xFFFFFFFF),

    // 成交量轨迹样式
    buyTrackColor: Color(0xB300D4AA),
    sellTrackColor: Color(0xB3FF5B5B),
    priceLevelColor: Color(0x80666666),
  );

  @override
  TemplateKlineStyles get darkKlineStyles => const TemplateKlineStyles(
    // 蜡烛图样式 - Bitget特色
    bullCandleColor: Color(0xFF00D4AA),
    bearCandleColor: Color(0xFFFF5B5B),
    candleBorderColor: Color(0xFF333333),
    candleMaxWidth: 50.0,
    candleMinWidth: 1.0,
    candleLineWidth: 1.0,
    candleSpacingRatio: 0.1,

    // 图表背景样式
    chartBackgroundColor: Color(0xFF1A1A1A),
    chartSurfaceColor: Color(0xFF2D2D2D),
    chartBorderColor: Color(0xFF333333),

    // 网格线样式
    gridLineColor: Color(0xFF404040),
    gridLineWidth: 0.5,
    showGridLines: true,
    gridLineDashed: false,

    // 十字线样式
    crossLineColor: Color(0xFFCCCCCC),
    crossLineWidth: 1.0,
    crossLineDashed: true,

    // 价格线样式
    latestPriceLineColor: Color(0xFFF7931A),
    markLineColor: Color(0xFFFFFFFF),
    averagePriceLineColor: Color(0xFFFF9F43),
    priceLineWidth: 1.0,

    // 文本样式
    primaryTextColor: Color(0xFFFFFFFF),
    secondaryTextColor: Color(0xFFCCCCCC),
    priceTextColor: Color(0xFFFFFFFF),
    textFontSize: 10.0,
    priceTextFontSize: 12.0,
    textFontWeight: FontWeight.w400,

    // 工具提示样式
    tooltipBackgroundColor: Color(0xFF2D2D2D),
    tooltipTextColor: Color(0xFFFFFFFF),
    tooltipBorderColor: Color(0xFF333333),
    tooltipBorderRadius: 4.0,

    // 成交量样式
    volumeBullColor: Color(0x9900D4AA),
    volumeBearColor: Color(0x99FF5B5B),
    volumeOpacity: 0.6,

    // 指标样式
    maIndicatorColors: [
      Color(0xFFFFD700), // MA5
      Color(0xFF9932CC), // MA10
      Color(0xFF00CED1), // MA20
      Color(0xFFFF69B4), // MA30
      Color(0xFF32CD32), // MA60
    ],
    macdLineColor: Color(0xFFFFD700),
    macdSignalColor: Color(0xFF9932CC),
    macdHistogramColor: Color(0xFFCCCCCC),
    rsiLineColor: Color(0xFF00CED1),
    indicatorLineWidth: 1.0,

    // 缩放和手势样式
    magnifierBackgroundColor: Color(0xFF2D2D2D),
    magnifierBorderColor: Color(0xFF333333),
    magnifierBorderRadius: 4.0,

    // 加载样式
    loadingIndicatorColor: Color(0xFFF7931A),
    loadingBackgroundColor: Color(0xFF1A1A1A),

    // 拖拽和交互样式
    dragBackgroundColor: Color(0xFF2D2D2D),
    themeColor: Color(0xFFF7931A),
    drawColor: Color(0xFFCCCCCC),
    ticksTextColor: Color(0xB3CCCCCC),
    latestPriceTextBackgroundColor: Color(0xFF2D2D2D),
    lastPriceTextBackgroundColor: Color(0xFF2D2D2D),
    countDownTextBackgroundColor: Color(0xFF2D2D2D),

    // 成交量轨迹样式
    buyTrackColor: Color(0xB300D4AA),
    sellTrackColor: Color(0xB3FF5B5B),
    priceLevelColor: Color(0x80CCCCCC),
  );
}
