// ========================================
// 📈 K线样式提供者
// ========================================

import 'package:flutter/material.dart';
import '../../template_config.dart';
import '../../template_theme_provider.dart';
import '../../../config/static/ui_constants.dart';
import 'kline_styles.dart';

/// K线样式提供者
/// 提供统一的K线图表样式访问接口
class TemplateKlineStyleProvider {
  final BuildContext context;

  TemplateKlineStyleProvider(this.context);

  /// 获取当前模板的颜色和样式配置
  TemplateColors get _colors => context.templateColors;
  TemplateStyles get _styles => context.templateStyles;

  // ========== 蜡烛图样式 ==========

  /// 上涨蜡烛颜色
  Color get bullCandleColor => _colors.tradeBuy;

  /// 下跌蜡烛颜色
  Color get bearCandleColor => _colors.tradeSell;

  /// 蜡烛边框颜色
  Color get candleBorderColor => _colors.border;

  /// 蜡烛最大宽度
  double get candleMaxWidth => 50.0;

  /// 蜡烛最小宽度
  double get candleMinWidth => 1.0;

  /// 蜡烛线宽
  double get candleLineWidth => 1.0;

  /// 蜡烛间距比例
  double get candleSpacingRatio => 0.1;

  // ========== 图表背景样式 ==========

  /// 图表背景颜色
  Color get chartBackgroundColor => _colors.background;

  /// 图表表面颜色
  Color get chartSurfaceColor => _colors.surface;

  /// 图表边框颜色
  Color get chartBorderColor => _colors.border;

  // ========== 网格线样式 ==========

  /// 网格线颜色
  Color get gridLineColor => _colors.divider;

  /// 网格线宽度
  double get gridLineWidth => 0.5;

  /// 是否显示网格线
  bool get showGridLines => true;

  /// 网格线样式（实线/虚线）
  bool get gridLineDashed => false;

  // ========== 十字线样式 ==========

  /// 十字线颜色
  Color get crossLineColor => _colors.textSecondary;

  /// 十字线宽度
  double get crossLineWidth => 1.0;

  /// 十字线样式（实线/虚线）
  bool get crossLineDashed => true;

  // ========== 价格线样式 ==========

  /// 最新价格线颜色
  Color get latestPriceLineColor => _colors.primary;

  /// 标记线颜色
  Color get markLineColor => _colors.textPrimary;

  /// 平均价格线颜色
  Color get averagePriceLineColor => const Color(0xFFFF9F43);

  /// 价格线宽度
  double get priceLineWidth => 1.0;

  // ========== 文本样式 ==========

  /// 主要文本颜色
  Color get primaryTextColor => _colors.textPrimary;

  /// 次要文本颜色
  Color get secondaryTextColor => _colors.textSecondary;

  /// 价格文本颜色
  Color get priceTextColor => _colors.textPrimary;

  /// 文本字体大小
  double get textFontSize => UiConstants.fontSize10;

  /// 价格文本字体大小
  double get priceTextFontSize => UiConstants.fontSize12;

  /// 文本字体粗细
  FontWeight get textFontWeight => UiConstants.fontWeightRegular;

  // ========== 工具提示样式 ==========

  /// 工具提示背景颜色
  Color get tooltipBackgroundColor => _colors.popupBackground;

  /// 工具提示文本颜色
  Color get tooltipTextColor => _colors.textPrimary;

  /// 工具提示边框颜色
  Color get tooltipBorderColor => _colors.border;

  /// 工具提示圆角半径
  double get tooltipBorderRadius => _styles.borderRadiusSmall;

  // ========== 成交量样式 ==========

  /// 成交量上涨颜色
  Color get volumeBullColor => _colors.tradeBuy.withValues(alpha: 0.6);

  /// 成交量下跌颜色
  Color get volumeBearColor => _colors.tradeSell.withValues(alpha: 0.6);

  /// 成交量透明度
  double get volumeOpacity => 0.6;

  // ========== 指标样式 ==========

  /// MA指标颜色列表
  List<Color> get maIndicatorColors => [
    const Color(0xFFFFD700), // 金色 MA5
    const Color(0xFF9932CC), // 紫色 MA10
    const Color(0xFF00CED1), // 青色 MA20
    const Color(0xFFFF69B4), // 粉色 MA30
    const Color(0xFF32CD32), // 绿色 MA60
  ];

  /// MACD指标颜色
  Color get macdLineColor => const Color(0xFFFFD700);
  Color get macdSignalColor => const Color(0xFF9932CC);
  Color get macdHistogramColor => _colors.textSecondary;

  /// RSI指标颜色
  Color get rsiLineColor => const Color(0xFF00CED1);

  /// 指标线宽度
  double get indicatorLineWidth => 1.0;

  // ========== 缩放和手势样式 ==========

  /// 放大镜背景颜色
  Color get magnifierBackgroundColor => _colors.popupBackground;

  /// 放大镜边框颜色
  Color get magnifierBorderColor => _colors.border;

  /// 放大镜圆角半径
  double get magnifierBorderRadius => _styles.borderRadiusSmall;

  // ========== 加载样式 ==========

  /// 加载指示器颜色
  Color get loadingIndicatorColor => _colors.primary;

  /// 加载背景颜色
  Color get loadingBackgroundColor => _colors.background;

  // ========== 拖拽和交互样式 ==========

  /// 拖拽区域背景颜色
  Color get dragBackgroundColor => _colors.surface;

  /// 主题色
  Color get themeColor => _colors.primary;

  /// 绘制工具颜色
  Color get drawColor => _colors.textSecondary;

  /// 刻度文本颜色
  Color get ticksTextColor => _colors.textSecondary.withValues(alpha: 0.7);

  /// 最新价格文本背景色
  Color get latestPriceTextBackgroundColor => _colors.popupBackground;

  /// 最后价格文本背景色
  Color get lastPriceTextBackgroundColor => _colors.popupBackground;

  /// 倒计时文本背景色
  Color get countDownTextBackgroundColor => _colors.popupBackground;

  // ========== 成交量轨迹样式 ==========

  /// 买入轨迹颜色
  Color get buyTrackColor => _colors.tradeBuy.withValues(alpha: 0.7);

  /// 卖出轨迹颜色
  Color get sellTrackColor => _colors.tradeSell.withValues(alpha: 0.7);

  /// 价格级别颜色
  Color get priceLevelColor => _colors.textSecondary.withValues(alpha: 0.5);

  // ========== 便捷方法 ==========

  /// 获取完整的K线样式配置
  TemplateKlineStyles get klineStyles => TemplateKlineStyles(
    // 蜡烛图样式
    bullCandleColor: bullCandleColor,
    bearCandleColor: bearCandleColor,
    candleBorderColor: candleBorderColor,
    candleMaxWidth: candleMaxWidth,
    candleMinWidth: candleMinWidth,
    candleLineWidth: candleLineWidth,
    candleSpacingRatio: candleSpacingRatio,

    // 图表背景样式
    chartBackgroundColor: chartBackgroundColor,
    chartSurfaceColor: chartSurfaceColor,
    chartBorderColor: chartBorderColor,

    // 网格线样式
    gridLineColor: gridLineColor,
    gridLineWidth: gridLineWidth,
    showGridLines: showGridLines,
    gridLineDashed: gridLineDashed,

    // 十字线样式
    crossLineColor: crossLineColor,
    crossLineWidth: crossLineWidth,
    crossLineDashed: crossLineDashed,

    // 价格线样式
    latestPriceLineColor: latestPriceLineColor,
    markLineColor: markLineColor,
    averagePriceLineColor: averagePriceLineColor,
    priceLineWidth: priceLineWidth,

    // 文本样式
    primaryTextColor: primaryTextColor,
    secondaryTextColor: secondaryTextColor,
    priceTextColor: priceTextColor,
    textFontSize: textFontSize,
    priceTextFontSize: priceTextFontSize,
    textFontWeight: textFontWeight,

    // 工具提示样式
    tooltipBackgroundColor: tooltipBackgroundColor,
    tooltipTextColor: tooltipTextColor,
    tooltipBorderColor: tooltipBorderColor,
    tooltipBorderRadius: tooltipBorderRadius,

    // 成交量样式
    volumeBullColor: volumeBullColor,
    volumeBearColor: volumeBearColor,
    volumeOpacity: volumeOpacity,

    // 指标样式
    maIndicatorColors: maIndicatorColors,
    macdLineColor: macdLineColor,
    macdSignalColor: macdSignalColor,
    macdHistogramColor: macdHistogramColor,
    rsiLineColor: rsiLineColor,
    indicatorLineWidth: indicatorLineWidth,

    // 缩放和手势样式
    magnifierBackgroundColor: magnifierBackgroundColor,
    magnifierBorderColor: magnifierBorderColor,
    magnifierBorderRadius: magnifierBorderRadius,

    // 加载样式
    loadingIndicatorColor: loadingIndicatorColor,
    loadingBackgroundColor: loadingBackgroundColor,

    // 拖拽和交互样式
    dragBackgroundColor: dragBackgroundColor,
    themeColor: themeColor,
    drawColor: drawColor,
    ticksTextColor: ticksTextColor,
    latestPriceTextBackgroundColor: latestPriceTextBackgroundColor,
    lastPriceTextBackgroundColor: lastPriceTextBackgroundColor,
    countDownTextBackgroundColor: countDownTextBackgroundColor,

    // 成交量轨迹样式
    buyTrackColor: buyTrackColor,
    sellTrackColor: sellTrackColor,
    priceLevelColor: priceLevelColor,
  );

  /// 自定义K线样式
  TemplateKlineStyles customKlineStyles({
    Color? bullCandleColor,
    Color? bearCandleColor,
    Color? chartBackgroundColor,
    bool? showGridLines,
    List<Color>? maIndicatorColors,
  }) {
    return klineStyles.copyWith(
      bullCandleColor: bullCandleColor,
      bearCandleColor: bearCandleColor,
      chartBackgroundColor: chartBackgroundColor,
      showGridLines: showGridLines,
      maIndicatorColors: maIndicatorColors,
    );
  }
}
