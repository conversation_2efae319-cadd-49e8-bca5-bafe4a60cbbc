// ========================================
// 📈 K线样式配置
// ========================================

import 'package:flutter/material.dart';

/// K线样式配置类
/// 定义K线图表的所有样式配置
class TemplateKlineStyles {
  // ========== 蜡烛图样式 ==========
  /// 上涨蜡烛颜色
  final Color bullCandleColor;

  /// 下跌蜡烛颜色
  final Color bearCandleColor;

  /// 蜡烛边框颜色
  final Color candleBorderColor;

  /// 蜡烛最大宽度
  final double candleMaxWidth;

  /// 蜡烛最小宽度
  final double candleMinWidth;

  /// 蜡烛线宽
  final double candleLineWidth;

  /// 蜡烛间距比例
  final double candleSpacingRatio;

  // ========== 图表背景样式 ==========
  /// 图表背景颜色
  final Color chartBackgroundColor;

  /// 图表表面颜色
  final Color chartSurfaceColor;

  /// 图表边框颜色
  final Color chartBorderColor;

  // ========== 网格线样式 ==========
  /// 网格线颜色
  final Color gridLineColor;

  /// 网格线宽度
  final double gridLineWidth;

  /// 是否显示网格线
  final bool showGridLines;

  /// 网格线样式（实线/虚线）
  final bool gridLineDashed;

  // ========== 十字线样式 ==========
  /// 十字线颜色
  final Color crossLineColor;

  /// 十字线宽度
  final double crossLineWidth;

  /// 十字线样式（实线/虚线）
  final bool crossLineDashed;

  // ========== 价格线样式 ==========
  /// 最新价格线颜色
  final Color latestPriceLineColor;

  /// 标记线颜色
  final Color markLineColor;

  /// 平均价格线颜色
  final Color averagePriceLineColor;

  /// 价格线宽度
  final double priceLineWidth;

  // ========== 文本样式 ==========
  /// 主要文本颜色
  final Color primaryTextColor;

  /// 次要文本颜色
  final Color secondaryTextColor;

  /// 价格文本颜色
  final Color priceTextColor;

  /// 文本字体大小
  final double textFontSize;

  /// 价格文本字体大小
  final double priceTextFontSize;

  /// 文本字体粗细
  final FontWeight textFontWeight;

  // ========== 工具提示样式 ==========
  /// 工具提示背景颜色
  final Color tooltipBackgroundColor;

  /// 工具提示文本颜色
  final Color tooltipTextColor;

  /// 工具提示边框颜色
  final Color tooltipBorderColor;

  /// 工具提示圆角半径
  final double tooltipBorderRadius;

  // ========== 成交量样式 ==========
  /// 成交量上涨颜色
  final Color volumeBullColor;

  /// 成交量下跌颜色
  final Color volumeBearColor;

  /// 成交量透明度
  final double volumeOpacity;

  // ========== 指标样式 ==========
  /// MA指标颜色列表
  final List<Color> maIndicatorColors;

  /// MACD指标颜色
  final Color macdLineColor;
  final Color macdSignalColor;
  final Color macdHistogramColor;

  /// RSI指标颜色
  final Color rsiLineColor;

  /// 指标线宽度
  final double indicatorLineWidth;

  // ========== 缩放和手势样式 ==========
  /// 放大镜背景颜色
  final Color magnifierBackgroundColor;

  /// 放大镜边框颜色
  final Color magnifierBorderColor;

  /// 放大镜圆角半径
  final double magnifierBorderRadius;

  // ========== 加载样式 ==========
  /// 加载指示器颜色
  final Color loadingIndicatorColor;

  /// 加载背景颜色
  final Color loadingBackgroundColor;

  // ========== 拖拽和交互样式 ==========
  /// 拖拽区域背景颜色
  final Color dragBackgroundColor;

  /// 主题色
  final Color themeColor;

  /// 绘制工具颜色
  final Color drawColor;

  /// 刻度文本颜色
  final Color ticksTextColor;

  /// 最新价格文本背景色
  final Color latestPriceTextBackgroundColor;

  /// 最后价格文本背景色
  final Color lastPriceTextBackgroundColor;

  /// 倒计时文本背景色
  final Color countDownTextBackgroundColor;

  // ========== 成交量轨迹样式 ==========
  /// 买入轨迹颜色
  final Color buyTrackColor;

  /// 卖出轨迹颜色
  final Color sellTrackColor;

  /// 价格级别颜色
  final Color priceLevelColor;

  const TemplateKlineStyles({
    // 蜡烛图样式
    required this.bullCandleColor,
    required this.bearCandleColor,
    required this.candleBorderColor,
    required this.candleMaxWidth,
    required this.candleMinWidth,
    required this.candleLineWidth,
    required this.candleSpacingRatio,

    // 图表背景样式
    required this.chartBackgroundColor,
    required this.chartSurfaceColor,
    required this.chartBorderColor,

    // 网格线样式
    required this.gridLineColor,
    required this.gridLineWidth,
    required this.showGridLines,
    required this.gridLineDashed,

    // 十字线样式
    required this.crossLineColor,
    required this.crossLineWidth,
    required this.crossLineDashed,

    // 价格线样式
    required this.latestPriceLineColor,
    required this.markLineColor,
    required this.averagePriceLineColor,
    required this.priceLineWidth,

    // 文本样式
    required this.primaryTextColor,
    required this.secondaryTextColor,
    required this.priceTextColor,
    required this.textFontSize,
    required this.priceTextFontSize,
    required this.textFontWeight,

    // 工具提示样式
    required this.tooltipBackgroundColor,
    required this.tooltipTextColor,
    required this.tooltipBorderColor,
    required this.tooltipBorderRadius,

    // 成交量样式
    required this.volumeBullColor,
    required this.volumeBearColor,
    required this.volumeOpacity,

    // 指标样式
    required this.maIndicatorColors,
    required this.macdLineColor,
    required this.macdSignalColor,
    required this.macdHistogramColor,
    required this.rsiLineColor,
    required this.indicatorLineWidth,

    // 缩放和手势样式
    required this.magnifierBackgroundColor,
    required this.magnifierBorderColor,
    required this.magnifierBorderRadius,

    // 加载样式
    required this.loadingIndicatorColor,
    required this.loadingBackgroundColor,

    // 拖拽和交互样式
    required this.dragBackgroundColor,
    required this.themeColor,
    required this.drawColor,
    required this.ticksTextColor,
    required this.latestPriceTextBackgroundColor,
    required this.lastPriceTextBackgroundColor,
    required this.countDownTextBackgroundColor,

    // 成交量轨迹样式
    required this.buyTrackColor,
    required this.sellTrackColor,
    required this.priceLevelColor,
  });

  /// 创建副本并修改部分属性
  TemplateKlineStyles copyWith({
    Color? bullCandleColor,
    Color? bearCandleColor,
    Color? candleBorderColor,
    double? candleMaxWidth,
    double? candleMinWidth,
    double? candleLineWidth,
    double? candleSpacingRatio,
    Color? chartBackgroundColor,
    Color? chartSurfaceColor,
    Color? chartBorderColor,
    Color? gridLineColor,
    double? gridLineWidth,
    bool? showGridLines,
    bool? gridLineDashed,
    Color? crossLineColor,
    double? crossLineWidth,
    bool? crossLineDashed,
    Color? latestPriceLineColor,
    Color? markLineColor,
    Color? averagePriceLineColor,
    double? priceLineWidth,
    Color? primaryTextColor,
    Color? secondaryTextColor,
    Color? priceTextColor,
    double? textFontSize,
    double? priceTextFontSize,
    FontWeight? textFontWeight,
    Color? tooltipBackgroundColor,
    Color? tooltipTextColor,
    Color? tooltipBorderColor,
    double? tooltipBorderRadius,
    Color? volumeBullColor,
    Color? volumeBearColor,
    double? volumeOpacity,
    List<Color>? maIndicatorColors,
    Color? macdLineColor,
    Color? macdSignalColor,
    Color? macdHistogramColor,
    Color? rsiLineColor,
    double? indicatorLineWidth,
    Color? magnifierBackgroundColor,
    Color? magnifierBorderColor,
    double? magnifierBorderRadius,
    Color? loadingIndicatorColor,
    Color? loadingBackgroundColor,
    Color? dragBackgroundColor,
    Color? themeColor,
    Color? drawColor,
    Color? ticksTextColor,
    Color? latestPriceTextBackgroundColor,
    Color? lastPriceTextBackgroundColor,
    Color? countDownTextBackgroundColor,
    Color? buyTrackColor,
    Color? sellTrackColor,
    Color? priceLevelColor,
  }) {
    return TemplateKlineStyles(
      bullCandleColor: bullCandleColor ?? this.bullCandleColor,
      bearCandleColor: bearCandleColor ?? this.bearCandleColor,
      candleBorderColor: candleBorderColor ?? this.candleBorderColor,
      candleMaxWidth: candleMaxWidth ?? this.candleMaxWidth,
      candleMinWidth: candleMinWidth ?? this.candleMinWidth,
      candleLineWidth: candleLineWidth ?? this.candleLineWidth,
      candleSpacingRatio: candleSpacingRatio ?? this.candleSpacingRatio,
      chartBackgroundColor: chartBackgroundColor ?? this.chartBackgroundColor,
      chartSurfaceColor: chartSurfaceColor ?? this.chartSurfaceColor,
      chartBorderColor: chartBorderColor ?? this.chartBorderColor,
      gridLineColor: gridLineColor ?? this.gridLineColor,
      gridLineWidth: gridLineWidth ?? this.gridLineWidth,
      showGridLines: showGridLines ?? this.showGridLines,
      gridLineDashed: gridLineDashed ?? this.gridLineDashed,
      crossLineColor: crossLineColor ?? this.crossLineColor,
      crossLineWidth: crossLineWidth ?? this.crossLineWidth,
      crossLineDashed: crossLineDashed ?? this.crossLineDashed,
      latestPriceLineColor: latestPriceLineColor ?? this.latestPriceLineColor,
      markLineColor: markLineColor ?? this.markLineColor,
      averagePriceLineColor:
          averagePriceLineColor ?? this.averagePriceLineColor,
      priceLineWidth: priceLineWidth ?? this.priceLineWidth,
      primaryTextColor: primaryTextColor ?? this.primaryTextColor,
      secondaryTextColor: secondaryTextColor ?? this.secondaryTextColor,
      priceTextColor: priceTextColor ?? this.priceTextColor,
      textFontSize: textFontSize ?? this.textFontSize,
      priceTextFontSize: priceTextFontSize ?? this.priceTextFontSize,
      textFontWeight: textFontWeight ?? this.textFontWeight,
      tooltipBackgroundColor:
          tooltipBackgroundColor ?? this.tooltipBackgroundColor,
      tooltipTextColor: tooltipTextColor ?? this.tooltipTextColor,
      tooltipBorderColor: tooltipBorderColor ?? this.tooltipBorderColor,
      tooltipBorderRadius: tooltipBorderRadius ?? this.tooltipBorderRadius,
      volumeBullColor: volumeBullColor ?? this.volumeBullColor,
      volumeBearColor: volumeBearColor ?? this.volumeBearColor,
      volumeOpacity: volumeOpacity ?? this.volumeOpacity,
      maIndicatorColors: maIndicatorColors ?? this.maIndicatorColors,
      macdLineColor: macdLineColor ?? this.macdLineColor,
      macdSignalColor: macdSignalColor ?? this.macdSignalColor,
      macdHistogramColor: macdHistogramColor ?? this.macdHistogramColor,
      rsiLineColor: rsiLineColor ?? this.rsiLineColor,
      indicatorLineWidth: indicatorLineWidth ?? this.indicatorLineWidth,
      magnifierBackgroundColor:
          magnifierBackgroundColor ?? this.magnifierBackgroundColor,
      magnifierBorderColor: magnifierBorderColor ?? this.magnifierBorderColor,
      magnifierBorderRadius:
          magnifierBorderRadius ?? this.magnifierBorderRadius,
      loadingIndicatorColor:
          loadingIndicatorColor ?? this.loadingIndicatorColor,
      loadingBackgroundColor:
          loadingBackgroundColor ?? this.loadingBackgroundColor,
      dragBackgroundColor: dragBackgroundColor ?? this.dragBackgroundColor,
      themeColor: themeColor ?? this.themeColor,
      drawColor: drawColor ?? this.drawColor,
      ticksTextColor: ticksTextColor ?? this.ticksTextColor,
      latestPriceTextBackgroundColor:
          latestPriceTextBackgroundColor ?? this.latestPriceTextBackgroundColor,
      lastPriceTextBackgroundColor:
          lastPriceTextBackgroundColor ?? this.lastPriceTextBackgroundColor,
      countDownTextBackgroundColor:
          countDownTextBackgroundColor ?? this.countDownTextBackgroundColor,
      buyTrackColor: buyTrackColor ?? this.buyTrackColor,
      sellTrackColor: sellTrackColor ?? this.sellTrackColor,
      priceLevelColor: priceLevelColor ?? this.priceLevelColor,
    );
  }
}
