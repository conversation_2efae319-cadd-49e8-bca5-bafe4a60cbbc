// ========================================
// 🔘 按钮样式提供者
// ========================================

import 'package:flutter/material.dart';
import '../../template_config.dart';
import '../../template_theme_provider.dart';
import '../text/text_styles.dart';

/// 按钮样式提供者
class TemplateButtonStyleProvider {
  final BuildContext context;

  TemplateButtonStyleProvider(this.context);

  /// 获取当前模板的颜色和样式配置
  TemplateColors get _colors => context.templateColors;
  TemplateStyles get _styles => context.templateStyles;
  TemplateTextStyleProvider get _textStyles =>
      TemplateTextStyleProvider(context);

  // ========== 基础按钮样式 ==========

  /// 主要按钮样式
  ButtonStyle get primary => ElevatedButton.styleFrom(
    backgroundColor: _colors.buttonPrimary,
    foregroundColor: _colors.buttonText,
    textStyle: _textStyles.buttonText,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    ),
    elevation: _styles.elevationLow,
    shadowColor: Colors.transparent,
    padding: EdgeInsets.symmetric(
      horizontal: _styles.spacingMedium,
      vertical: _styles.spacingSmall,
    ),
    minimumSize: Size(0, _styles.buttonHeightMedium),
  );

  /// 次要按钮样式
  ButtonStyle get secondary => ElevatedButton.styleFrom(
    backgroundColor: _colors.buttonSecondary,
    foregroundColor: _colors.textPrimary,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    ),
    elevation: _styles.elevationLow,
    shadowColor: Colors.transparent,
    padding: EdgeInsets.symmetric(
      horizontal: _styles.spacingMedium,
      vertical: _styles.spacingSmall,
    ),
    minimumSize: Size(0, _styles.buttonHeightMedium),
  );

  /// 边框按钮样式
  ButtonStyle get outlined => OutlinedButton.styleFrom(
    foregroundColor: _colors.buttonTextSecondary,
    side: BorderSide(color: _colors.border, width: _styles.borderWidthThin),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    ),
    elevation: _styles.elevationLow,
    shadowColor: Colors.transparent,
    padding: EdgeInsets.symmetric(
      horizontal: _styles.spacingMedium,
      vertical: _styles.spacingSmall,
    ),
    minimumSize: Size(0, _styles.buttonHeightMedium),
  );

  /// 文本按钮样式
  ButtonStyle get text => TextButton.styleFrom(
    foregroundColor: _colors.primary,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    ),
    elevation: _styles.elevationLow,
    shadowColor: Colors.transparent,
    overlayColor: Colors.transparent,
    padding: EdgeInsets.symmetric(
      horizontal: _styles.spacingMedium,
      vertical: _styles.spacingSmall,
    ),
    minimumSize: Size(0, _styles.buttonHeightMedium),
  );

  // ========== 交易按钮样式 ==========

  /// 买入按钮样式
  ButtonStyle get buy => ElevatedButton.styleFrom(
    backgroundColor: _colors.tradeBuy,
    foregroundColor: _colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    ),
    elevation: _styles.elevationLow,
    padding: EdgeInsets.symmetric(
      horizontal: _styles.spacingMedium,
      vertical: _styles.spacingSmall,
    ),
    minimumSize: Size(0, _styles.buttonHeightMedium),
  );

  /// 卖出按钮样式
  ButtonStyle get sell => ElevatedButton.styleFrom(
    backgroundColor: _colors.tradeSell,
    foregroundColor: _colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    ),
    elevation: _styles.elevationLow,
    padding: EdgeInsets.symmetric(
      horizontal: _styles.spacingMedium,
      vertical: _styles.spacingSmall,
    ),
    minimumSize: Size(0, _styles.buttonHeightMedium),
  );

  /// 买入边框按钮样式
  ButtonStyle get buyOutlined => OutlinedButton.styleFrom(
    foregroundColor: _colors.tradeBuy,
    side: BorderSide(color: _colors.tradeBuy, width: _styles.borderWidthThin),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    ),
    padding: EdgeInsets.symmetric(
      horizontal: _styles.spacingMedium,
      vertical: _styles.spacingSmall,
    ),
    minimumSize: Size(0, _styles.buttonHeightMedium),
  );

  /// 卖出边框按钮样式
  ButtonStyle get sellOutlined => OutlinedButton.styleFrom(
    foregroundColor: _colors.tradeSell,
    side: BorderSide(color: _colors.tradeSell, width: _styles.borderWidthThin),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    ),
    padding: EdgeInsets.symmetric(
      horizontal: _styles.spacingMedium,
      vertical: _styles.spacingSmall,
    ),
    minimumSize: Size(0, _styles.buttonHeightMedium),
  );

  // ========== 状态按钮样式 ==========

  /// 成功按钮样式
  ButtonStyle get success => ElevatedButton.styleFrom(
    backgroundColor: _colors.success,
    foregroundColor: _colors.buttonText,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    ),
    elevation: _styles.elevationLow,
    padding: EdgeInsets.symmetric(
      horizontal: _styles.spacingMedium,
      vertical: _styles.spacingSmall,
    ),
    minimumSize: Size(0, _styles.buttonHeightMedium),
  );

  /// 警告按钮样式
  ButtonStyle get warning => ElevatedButton.styleFrom(
    backgroundColor: _colors.warning,
    foregroundColor: _colors.buttonText,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    ),
    elevation: _styles.elevationLow,
    padding: EdgeInsets.symmetric(
      horizontal: _styles.spacingMedium,
      vertical: _styles.spacingSmall,
    ),
    minimumSize: Size(0, _styles.buttonHeightMedium),
  );

  /// 错误按钮样式
  ButtonStyle get error => ElevatedButton.styleFrom(
    backgroundColor: _colors.error,
    foregroundColor: _colors.buttonText,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    ),
    elevation: _styles.elevationLow,
    padding: EdgeInsets.symmetric(
      horizontal: _styles.spacingMedium,
      vertical: _styles.spacingSmall,
    ),
    minimumSize: Size(0, _styles.buttonHeightMedium),
  );

  // ========== 尺寸变体 ==========

  /// 小尺寸按钮样式
  ButtonStyle get smallPrimary => primary.copyWith(
    padding: WidgetStateProperty.all(
      EdgeInsets.symmetric(
        horizontal: _styles.spacingSmall,
        vertical: _styles.spacingSmall / 2,
      ),
    ),
    minimumSize: WidgetStateProperty.all(const Size(0, 32)),
  );

  /// 大尺寸按钮样式
  ButtonStyle get largePrimary => primary.copyWith(
    padding: WidgetStateProperty.all(
      EdgeInsets.symmetric(
        horizontal: _styles.spacingLarge,
        vertical: _styles.spacingMedium,
      ),
    ),
    minimumSize: WidgetStateProperty.all(const Size(0, 56)),
  );

  // ========== 特殊按钮样式 ==========

  /// 圆形按钮样式
  ButtonStyle get circular => ElevatedButton.styleFrom(
    backgroundColor: _colors.buttonPrimary,
    foregroundColor: _colors.buttonText,
    shape: const CircleBorder(),
    elevation: _styles.elevationLow,
    padding: EdgeInsets.all(_styles.spacingSmall),
  );

  /// 图标按钮样式
  ButtonStyle get icon => IconButton.styleFrom(
    foregroundColor: _colors.primary,
    backgroundColor: Colors.transparent,
    padding: EdgeInsets.all(_styles.spacingSmall),
  );

  /// 浮动操作按钮样式（用于ElevatedButton）
  ButtonStyle get fab => ElevatedButton.styleFrom(
    backgroundColor: _colors.primary,
    foregroundColor: _colors.buttonText,
    elevation: _styles.elevationMedium,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusLarge),
    ),
    padding: EdgeInsets.all(_styles.spacingMedium),
    minimumSize: const Size(56, 56), // FAB标准尺寸
  );

  /// 获取FloatingActionButton的主题数据
  FloatingActionButtonThemeData get fabTheme => FloatingActionButtonThemeData(
    backgroundColor: _colors.primary,
    foregroundColor: _colors.buttonText,
    elevation: _styles.elevationMedium,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusLarge),
    ),
  );

  // ========== 工具方法 ==========

  /// 自定义按钮样式
  ButtonStyle customButton({
    Color? backgroundColor,
    Color? foregroundColor,
    double? borderRadius,
    double? elevation,
    EdgeInsetsGeometry? padding,
    Color? borderColor,
    double? borderWidth,
  }) {
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor ?? _colors.buttonPrimary,
      foregroundColor: foregroundColor ?? _colors.buttonText,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          borderRadius ?? _styles.borderRadiusMedium,
        ),
        side:
            borderColor != null
                ? BorderSide(
                  color: borderColor,
                  width: borderWidth ?? _styles.borderWidthThin,
                )
                : BorderSide.none,
      ),
      elevation: elevation ?? _styles.elevationLow,
      padding:
          padding ??
          EdgeInsets.symmetric(
            horizontal: _styles.spacingMedium,
            vertical: _styles.spacingSmall,
          ),
    );
  }

  /// 自定义边框按钮样式
  ButtonStyle customOutlinedButton({
    Color? foregroundColor,
    Color? borderColor,
    double? borderWidth,
    double? borderRadius,
    EdgeInsetsGeometry? padding,
  }) {
    return OutlinedButton.styleFrom(
      foregroundColor: foregroundColor ?? _colors.primary,
      side: BorderSide(
        color: borderColor ?? _colors.primary,
        width: borderWidth ?? _styles.borderWidthThin,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          borderRadius ?? _styles.borderRadiusMedium,
        ),
      ),
      padding:
          padding ??
          EdgeInsets.symmetric(
            horizontal: _styles.spacingMedium,
            vertical: _styles.spacingSmall,
          ),
    );
  }
}
