// ========================================
// 📦 容器样式提供者
// ========================================

import 'package:flutter/material.dart';
import '../../template_config.dart';
import '../../template_theme_provider.dart';

/// 容器样式提供者
class TemplateContainerStyleProvider {
  final BuildContext context;

  TemplateContainerStyleProvider(this.context);

  /// 获取当前模板的颜色和样式配置
  TemplateColors get _colors => context.templateColors;
  TemplateStyles get _styles => context.templateStyles;

  // ========== 卡片样式 ==========

  /// 主要卡片样式
  BoxDecoration get primaryCard => BoxDecoration(
    color: _colors.cardBackground,
    borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    boxShadow: [
      BoxShadow(
        color: _colors.divider.withValues(alpha: 0.1),
        blurRadius: _styles.elevationLow,
        offset: const Offset(0, 1),
      ),
    ],
  );

  /// 次要卡片样式
  BoxDecoration get secondaryCard => BoxDecoration(
    color: _colors.surface,
    borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    border: Border.all(color: _colors.border, width: _styles.borderWidthThin),
  );

  /// 浮动卡片样式
  BoxDecoration get elevatedCard => BoxDecoration(
    color: _colors.cardBackground,
    borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    boxShadow: [
      BoxShadow(
        color: _colors.divider.withValues(alpha: 0.15),
        blurRadius: _styles.elevationMedium,
        offset: const Offset(0, 2),
      ),
    ],
  );

  /// 高阴影卡片样式
  BoxDecoration get highElevatedCard => BoxDecoration(
    color: _colors.cardBackground,
    borderRadius: BorderRadius.circular(_styles.borderRadiusLarge),
    boxShadow: [
      BoxShadow(
        color: _colors.divider.withValues(alpha: 0.2),
        blurRadius: _styles.elevationHigh,
        offset: const Offset(0, 4),
      ),
    ],
  );

  // ========== 标签样式 ==========

  /// 主要标签样式
  BoxDecoration get primaryTag => BoxDecoration(
    color: _colors.primary.withValues(alpha: 0.1),
    borderRadius: BorderRadius.circular(_styles.borderRadiusSmall),
  );

  /// 次要标签样式
  BoxDecoration get secondaryTag => BoxDecoration(
    color: _colors.surface,
    borderRadius: BorderRadius.circular(_styles.borderRadiusSmall),
  );

  /// 看涨标签样式
  BoxDecoration get bullishTag => BoxDecoration(
    color: _colors.tradeBuy.withValues(alpha: 0.1),
    borderRadius: BorderRadius.circular(_styles.borderRadiusSmall),
  );

  /// 看跌标签样式
  BoxDecoration get bearishTag => BoxDecoration(
    color: _colors.tradeSell.withValues(alpha: 0.1),
    borderRadius: BorderRadius.circular(_styles.borderRadiusSmall),
  );

  /// 边框标签样式
  BoxDecoration get outlinedTag => BoxDecoration(
    borderRadius: BorderRadius.circular(_styles.borderRadiusSmall),
    border: Border.all(color: _colors.primary, width: _styles.borderWidthThin),
  );

  /// 成功标签样式
  BoxDecoration get successTag => BoxDecoration(
    color: _colors.success.withValues(alpha: 0.1),
    borderRadius: BorderRadius.circular(_styles.borderRadiusSmall),
  );

  /// 警告标签样式
  BoxDecoration get warningTag => BoxDecoration(
    color: _colors.warning.withValues(alpha: 0.1),
    borderRadius: BorderRadius.circular(_styles.borderRadiusSmall),
  );

  /// 错误标签样式
  BoxDecoration get errorTag => BoxDecoration(
    color: _colors.error.withValues(alpha: 0.1),
    borderRadius: BorderRadius.circular(_styles.borderRadiusSmall),
  );

  // ========== 面板样式 ==========

  /// 基础面板样式
  BoxDecoration get panel => BoxDecoration(
    color: _colors.surface,
    borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    border: Border.all(color: _colors.border, width: _styles.borderWidthThin),
  );

  /// 突出面板样式
  BoxDecoration get highlightPanel => BoxDecoration(
    color: _colors.primary.withValues(alpha: 0.05),
    borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    border: Border.all(color: _colors.primary.withValues(alpha: 0.2), width: _styles.borderWidthThin),
  );

  // ========== 分割线样式 ==========

  /// 水平分割线
  BoxDecoration get horizontalDivider => BoxDecoration(
    color: _colors.divider,
  );

  /// 垂直分割线
  BoxDecoration get verticalDivider => BoxDecoration(
    color: _colors.divider,
  );

  // ========== 背景样式 ==========

  /// 页面背景
  BoxDecoration get pageBackground => BoxDecoration(
    color: _colors.background,
  );

  /// 弹窗背景
  BoxDecoration get popupBackground => BoxDecoration(
    color: _colors.popupBackground,
    borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
  );

  /// 底部弹窗背景
  BoxDecoration get bottomSheetBackground => BoxDecoration(
    color: _colors.popupBackground,
    borderRadius: BorderRadius.only(
      topLeft: Radius.circular(_styles.borderRadiusLarge),
      topRight: Radius.circular(_styles.borderRadiusLarge),
    ),
  );

  // ========== 交易相关容器 ==========

  /// 买入容器样式
  BoxDecoration get buyContainer => BoxDecoration(
    color: _colors.tradeBuy.withValues(alpha: 0.1),
    borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    border: Border.all(color: _colors.tradeBuy.withValues(alpha: 0.3), width: _styles.borderWidthThin),
  );

  /// 卖出容器样式
  BoxDecoration get sellContainer => BoxDecoration(
    color: _colors.tradeSell.withValues(alpha: 0.1),
    borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
    border: Border.all(color: _colors.tradeSell.withValues(alpha: 0.3), width: _styles.borderWidthThin),
  );

  // ========== 工具方法 ==========

  /// 自定义圆角容器
  BoxDecoration customRoundedContainer({
    Color? color,
    double? borderRadius,
    Color? borderColor,
    double? borderWidth,
    List<BoxShadow>? boxShadow,
  }) {
    return BoxDecoration(
      color: color ?? _colors.cardBackground,
      borderRadius: BorderRadius.circular(borderRadius ?? _styles.borderRadiusMedium),
      border: borderColor != null 
          ? Border.all(color: borderColor, width: borderWidth ?? _styles.borderWidthThin)
          : null,
      boxShadow: boxShadow,
    );
  }

  /// 自定义标签容器
  BoxDecoration customTag({
    required Color color,
    double? borderRadius,
    bool outlined = false,
  }) {
    return BoxDecoration(
      color: outlined ? Colors.transparent : color.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(borderRadius ?? _styles.borderRadiusSmall),
      border: outlined 
          ? Border.all(color: color, width: _styles.borderWidthThin)
          : null,
    );
  }
}
