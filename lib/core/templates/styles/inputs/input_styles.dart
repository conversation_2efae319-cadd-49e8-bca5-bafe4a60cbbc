// ========================================
// 📝 输入框样式提供者
// ========================================

import 'package:flutter/material.dart';
import '../../template_config.dart';
import '../../template_theme_provider.dart';

/// 输入框样式提供者
class TemplateInputStyleProvider {
  final BuildContext context;

  TemplateInputStyleProvider(this.context);

  /// 获取当前模板的颜色和样式配置
  TemplateColors get _colors => context.templateColors;
  TemplateStyles get _styles => context.templateStyles;

  // ========== 基础输入框样式 ==========

  /// 标准输入框装饰
  InputDecoration get standard => InputDecoration(
    filled: true,
    fillColor: _colors.inputBackground,
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
      borderSide: BorderSide(
        color: _colors.inputBorder,
        width: _styles.borderWidthThin,
      ),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
      borderSide: BorderSide(
        color: _colors.inputBorder,
        width: _styles.borderWidthThin,
      ),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
      borderSide: BorderSide(
        color: _colors.inputFocusedBorder,
        width: _styles.borderWidthMedium,
      ),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
      borderSide: BorderSide(
        color: _colors.error,
        width: _styles.borderWidthThin,
      ),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
      borderSide: BorderSide(
        color: _colors.error,
        width: _styles.borderWidthMedium,
      ),
    ),
    contentPadding: EdgeInsets.symmetric(
      horizontal: _styles.spacingMedium,
      vertical: _styles.spacingSmall,
    ),
  );

  /// 无边框输入框装饰
  InputDecoration get borderless => InputDecoration(
    filled: true,
    fillColor: _colors.inputBackground,
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
      borderSide: BorderSide.none,
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
      borderSide: BorderSide.none,
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
      borderSide: BorderSide(
        color: _colors.inputFocusedBorder,
        width: _styles.borderWidthMedium,
      ),
    ),
    contentPadding: EdgeInsets.symmetric(
      horizontal: _styles.spacingMedium,
      vertical: _styles.spacingSmall,
    ),
  );

  /// 下划线输入框装饰
  InputDecoration get underline => InputDecoration(
    filled: false,
    border: UnderlineInputBorder(
      borderSide: BorderSide(
        color: _colors.inputBorder,
        width: _styles.borderWidthThin,
      ),
    ),
    enabledBorder: UnderlineInputBorder(
      borderSide: BorderSide(
        color: _colors.inputBorder,
        width: _styles.borderWidthThin,
      ),
    ),
    focusedBorder: UnderlineInputBorder(
      borderSide: BorderSide(
        color: _colors.inputFocusedBorder,
        width: _styles.borderWidthMedium,
      ),
    ),
    errorBorder: UnderlineInputBorder(
      borderSide: BorderSide(
        color: _colors.error,
        width: _styles.borderWidthThin,
      ),
    ),
    focusedErrorBorder: UnderlineInputBorder(
      borderSide: BorderSide(
        color: _colors.error,
        width: _styles.borderWidthMedium,
      ),
    ),
    contentPadding: EdgeInsets.symmetric(
      horizontal: 0,
      vertical: _styles.spacingSmall,
    ),
  );

  // ========== 特殊用途输入框样式 ==========

  /// 搜索框装饰
  InputDecoration get search => standard.copyWith(
    prefixIcon: Icon(
      Icons.search,
      color: _colors.textTertiary,
    ),
    hintText: '搜索...',
    contentPadding: EdgeInsets.symmetric(
      horizontal: _styles.spacingSmall,
      vertical: _styles.spacingSmall,
    ),
  );

  /// 密码框装饰
  InputDecoration passwordDecoration({bool obscureText = true}) => standard.copyWith(
    suffixIcon: Icon(
      obscureText ? Icons.visibility_off : Icons.visibility,
      color: _colors.textTertiary,
    ),
  );

  /// 数字输入框装饰
  InputDecoration get number => standard.copyWith(
    suffixIcon: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          onTap: () {}, // 增加按钮
          child: Icon(
            Icons.keyboard_arrow_up,
            color: _colors.textTertiary,
            size: 16,
          ),
        ),
        InkWell(
          onTap: () {}, // 减少按钮
          child: Icon(
            Icons.keyboard_arrow_down,
            color: _colors.textTertiary,
            size: 16,
          ),
        ),
      ],
    ),
  );

  /// 价格输入框装饰
  InputDecoration get price => standard.copyWith(
    prefixText: '¥ ',
    prefixStyle: TextStyle(
      color: _colors.textSecondary,
      fontSize: 14,
    ),
  );

  /// 百分比输入框装饰
  InputDecoration get percentage => standard.copyWith(
    suffixText: '%',
    suffixStyle: TextStyle(
      color: _colors.textSecondary,
      fontSize: 14,
    ),
  );

  // ========== 尺寸变体 ==========

  /// 小尺寸输入框装饰
  InputDecoration get small => standard.copyWith(
    contentPadding: EdgeInsets.symmetric(
      horizontal: _styles.spacingSmall,
      vertical: _styles.spacingSmall / 2,
    ),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusSmall),
      borderSide: BorderSide(
        color: _colors.inputBorder,
        width: _styles.borderWidthThin,
      ),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusSmall),
      borderSide: BorderSide(
        color: _colors.inputBorder,
        width: _styles.borderWidthThin,
      ),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusSmall),
      borderSide: BorderSide(
        color: _colors.inputFocusedBorder,
        width: _styles.borderWidthMedium,
      ),
    ),
  );

  /// 大尺寸输入框装饰
  InputDecoration get large => standard.copyWith(
    contentPadding: EdgeInsets.symmetric(
      horizontal: _styles.spacingLarge,
      vertical: _styles.spacingMedium,
    ),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusLarge),
      borderSide: BorderSide(
        color: _colors.inputBorder,
        width: _styles.borderWidthThin,
      ),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusLarge),
      borderSide: BorderSide(
        color: _colors.inputBorder,
        width: _styles.borderWidthThin,
      ),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusLarge),
      borderSide: BorderSide(
        color: _colors.inputFocusedBorder,
        width: _styles.borderWidthMedium,
      ),
    ),
  );

  // ========== 状态变体 ==========

  /// 成功状态输入框装饰
  InputDecoration get success => standard.copyWith(
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
      borderSide: BorderSide(
        color: _colors.success,
        width: _styles.borderWidthThin,
      ),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
      borderSide: BorderSide(
        color: _colors.success,
        width: _styles.borderWidthMedium,
      ),
    ),
    suffixIcon: Icon(
      Icons.check_circle,
      color: _colors.success,
    ),
  );

  /// 警告状态输入框装饰
  InputDecoration get warning => standard.copyWith(
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
      borderSide: BorderSide(
        color: _colors.warning,
        width: _styles.borderWidthThin,
      ),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
      borderSide: BorderSide(
        color: _colors.warning,
        width: _styles.borderWidthMedium,
      ),
    ),
    suffixIcon: Icon(
      Icons.warning,
      color: _colors.warning,
    ),
  );

  // ========== 交易相关输入框样式 ==========

  /// 买入输入框装饰
  InputDecoration get buy => standard.copyWith(
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
      borderSide: BorderSide(
        color: _colors.tradeBuy,
        width: _styles.borderWidthMedium,
      ),
    ),
  );

  /// 卖出输入框装饰
  InputDecoration get sell => standard.copyWith(
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_styles.borderRadiusMedium),
      borderSide: BorderSide(
        color: _colors.tradeSell,
        width: _styles.borderWidthMedium,
      ),
    ),
  );

  // ========== 工具方法 ==========

  /// 自定义输入框装饰
  InputDecoration customDecoration({
    Color? fillColor,
    Color? borderColor,
    Color? focusedBorderColor,
    double? borderRadius,
    double? borderWidth,
    EdgeInsetsGeometry? contentPadding,
    Widget? prefixIcon,
    Widget? suffixIcon,
    String? hintText,
    String? labelText,
    bool filled = true,
  }) {
    return InputDecoration(
      filled: filled,
      fillColor: fillColor ?? _colors.inputBackground,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius ?? _styles.borderRadiusMedium),
        borderSide: BorderSide(
          color: borderColor ?? _colors.inputBorder,
          width: borderWidth ?? _styles.borderWidthThin,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius ?? _styles.borderRadiusMedium),
        borderSide: BorderSide(
          color: borderColor ?? _colors.inputBorder,
          width: borderWidth ?? _styles.borderWidthThin,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius ?? _styles.borderRadiusMedium),
        borderSide: BorderSide(
          color: focusedBorderColor ?? _colors.inputFocusedBorder,
          width: (borderWidth ?? _styles.borderWidthThin) * 1.5,
        ),
      ),
      contentPadding: contentPadding ?? EdgeInsets.symmetric(
        horizontal: _styles.spacingMedium,
        vertical: _styles.spacingSmall,
      ),
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      hintText: hintText,
      labelText: labelText,
    );
  }
}
