// ========================================
// 📝 文本样式提供者
// ========================================

import 'package:flutter/material.dart';
import '../../template_config.dart';
import '../../template_theme_provider.dart';
import '../../../config/static/ui_constants.dart';

/// 文本样式提供者
class TemplateTextStyleProvider {
  final BuildContext context;

  TemplateTextStyleProvider(this.context);

  /// 获取当前模板的颜色和字体配置
  TemplateColors get _colors => context.templateColors;
  TemplateFonts get _fonts => context.templateFonts;

  // ========== 标题样式 ==========

  /// 超大标题样式
  TextStyle get h1 => TextStyle(
    fontSize: UiConstants.fontSize32,
    letterSpacing: -0.3,
    height: 1.3,
    fontFamily: _fonts.primaryFont,
    fontWeight: UiConstants.fontWeightSemiBold,
    color: _colors.textPrimary,
  );

  /// 大标题样式
  TextStyle get h2 => h1.copyWith(fontSize: UiConstants.fontSize28);

  /// 中标题样式
  TextStyle get h3 => h1.copyWith(fontSize: UiConstants.fontSize20);

  /// 小标题样式
  TextStyle get h4 => h1.copyWith(fontSize: UiConstants.fontSize18);

  /// 最小标题样式
  TextStyle get h5 => h1.copyWith(fontSize: UiConstants.fontSize16);

  /// 副标题样式
  TextStyle get h6 => h1.copyWith(
    fontSize: UiConstants.fontSize14,
    fontWeight: UiConstants.fontWeightMedium,
  );

  // ========== 正文样式 ==========

  /// 基础正文样式
  TextStyle get bodyText => TextStyle(
    fontSize: UiConstants.fontSize14,
    height: 1.4,
    letterSpacing: -0.2,
    fontFamily: _fonts.primaryFont,
    fontWeight: UiConstants.fontWeightRegular,
    color: _colors.textPrimary,
  );

  /// 加粗正文样式
  TextStyle get bodyTextMedium =>
      bodyText.copyWith(fontWeight: UiConstants.fontWeightMedium);

  /// 粗体正文样式
  TextStyle get bodyTextBold =>
      bodyText.copyWith(fontWeight: UiConstants.fontWeightSemiBold);

  /// 大号正文样式
  TextStyle get bodyLarge =>
      bodyText.copyWith(fontSize: UiConstants.fontSize16);

  /// 大号加粗正文样式
  TextStyle get bodyLargeMedium =>
      bodyLarge.copyWith(fontWeight: UiConstants.fontWeightMedium);

  /// 大号粗体正文样式
  TextStyle get bodyLargeBold =>
      bodyLarge.copyWith(fontWeight: UiConstants.fontWeightSemiBold);

  /// 小号正文样式
  TextStyle get bodySmall =>
      bodyText.copyWith(fontSize: UiConstants.fontSize12);

  /// 小号加粗正文样式
  TextStyle get bodySmallMedium =>
      bodySmall.copyWith(fontWeight: UiConstants.fontWeightMedium);

  // ========== 描述文本样式 ==========

  /// 基础描述文本样式
  TextStyle get descriptionText => TextStyle(
    fontSize: UiConstants.fontSize14,
    letterSpacing: -0.2,
    fontFamily: _fonts.primaryFont,
    fontWeight: UiConstants.fontWeightRegular,
    color: _colors.textSecondary,
  );

  /// 加粗描述文本样式
  TextStyle get descriptionTextMedium =>
      descriptionText.copyWith(fontWeight: UiConstants.fontWeightMedium);

  /// 大号描述文本样式
  TextStyle get descriptionLarge =>
      descriptionText.copyWith(fontSize: UiConstants.fontSize16);

  /// 小号描述文本样式
  TextStyle get descriptionSmall =>
      descriptionText.copyWith(fontSize: UiConstants.fontSize12);

  // ========== 标签文本样式 ==========

  /// 基础标签文本样式
  TextStyle get tagText => bodyText.copyWith(fontSize: UiConstants.fontSize12);

  /// 加粗标签文本样式
  TextStyle get tagTextMedium =>
      bodyTextMedium.copyWith(fontSize: UiConstants.fontSize12);

  /// 次要标签文本样式
  TextStyle get tagSecondaryText =>
      descriptionText.copyWith(fontSize: UiConstants.fontSize12);

  /// 次要加粗标签文本样式
  TextStyle get tagSecondaryTextMedium =>
      descriptionTextMedium.copyWith(fontSize: UiConstants.fontSize12);

  /// 小号标签文本样式
  TextStyle get tagSmall => tagText.copyWith(fontSize: UiConstants.fontSize10);

  // ========== 提示文本样式 ==========

  /// 基础提示文本样式
  TextStyle get hintText => TextStyle(
    fontSize: UiConstants.fontSize12, // 使用10px作为提示文本的标准大小
    letterSpacing: -0.2,
    fontFamily: _fonts.primaryFont,
    fontWeight: UiConstants.fontWeightRegular,
    color: _colors.textTertiary,
  );

  /// 加粗提示文本样式
  TextStyle get hintTextMedium =>
      hintText.copyWith(fontWeight: UiConstants.fontWeightMedium);

  /// 大号提示文本样式
  TextStyle get hintLarge =>
      hintText.copyWith(fontSize: UiConstants.fontSize12);

  /// 小号提示文本样式
  TextStyle get hintSmall =>
      hintText.copyWith(fontSize: UiConstants.fontSize10);

  // ========== 选项卡文本样式 ==========

  /// 选项卡文本样式
  TextStyle get tabText =>
      bodyTextMedium.copyWith(fontSize: UiConstants.fontSize16);

  /// 小号选项卡文本样式
  TextStyle get tabTextSmall =>
      bodyTextMedium.copyWith(fontSize: UiConstants.fontSize14);

  /// 大号选项卡文本样式
  TextStyle get tabTextLarge =>
      bodyTextMedium.copyWith(fontSize: UiConstants.fontSize18);

  // ========== 按钮文本样式 ==========

  /// 按钮文本样式
  TextStyle get buttonText =>
      bodyTextMedium.copyWith(color: _colors.buttonText);

  /// 大号按钮文本样式
  TextStyle get buttonTextLarge => bodyTextMedium.copyWith(
    fontSize: UiConstants.fontSize14,
    color: _colors.buttonText,
  );

  /// 小号按钮文本样式
  TextStyle get buttonTextSmall => bodyTextMedium.copyWith(
    fontSize: UiConstants.fontSize12,
    color: _colors.buttonText,
  );

  // ========== 链接文本样式 ==========

  /// 链接文本样式
  TextStyle get linkText => bodyText.copyWith(color: _colors.primary);

  /// 加粗链接文本样式
  TextStyle get linkTextMedium =>
      bodyTextMedium.copyWith(color: _colors.primary);

  // ========== 状态文本样式 ==========

  // 协议文本样式
  TextStyle get agreementText => bodyText.copyWith(color: _colors.primary);

  /// 成功文本样式
  TextStyle get successText => bodyText.copyWith(color: _colors.success);

  /// 成功加粗文本样式
  TextStyle get successTextMedium =>
      bodyTextMedium.copyWith(color: _colors.success);

  /// 警告文本样式
  TextStyle get warningText => bodyText.copyWith(color: _colors.warning);

  /// 警告加粗文本样式
  TextStyle get warningTextMedium =>
      bodyTextMedium.copyWith(color: _colors.warning);

  /// 错误文本样式
  TextStyle get errorText =>
      bodyText.copyWith(color: _colors.error, fontSize: UiConstants.fontSize12);

  /// 错误加粗文本样式
  TextStyle get errorTextMedium =>
      bodyTextMedium.copyWith(color: _colors.error);

  /// 信息文本样式
  TextStyle get infoText => bodyText.copyWith(color: _colors.info);

  /// 信息加粗文本样式
  TextStyle get infoTextMedium => bodyTextMedium.copyWith(color: _colors.info);

  // ========== 交易文本样式 ==========

  /// 买入文本样式
  TextStyle get buyText => bodyText.copyWith(color: _colors.tradeBuy);

  /// 买入加粗文本样式
  TextStyle get buyTextMedium =>
      bodyTextMedium.copyWith(color: _colors.tradeBuy);

  /// 买入粗体文本样式
  TextStyle get buyTextBold => bodyTextBold.copyWith(color: _colors.tradeBuy);

  /// 卖出文本样式
  TextStyle get sellText => bodyText.copyWith(color: _colors.tradeSell);

  /// 卖出加粗文本样式
  TextStyle get sellTextMedium =>
      bodyTextMedium.copyWith(color: _colors.tradeSell);

  /// 卖出粗体文本样式
  TextStyle get sellTextBold => bodyTextBold.copyWith(color: _colors.tradeSell);

  // ========== 输入框文本样式 ==========

  /// 输入框文本样式
  TextStyle get inputText => TextStyle(
    fontSize: UiConstants.fontSize16,
    height: 1.4,
    letterSpacing: -0.2,
    fontFamily: _fonts.primaryFont,
    fontWeight: UiConstants.fontWeightMedium,
    color: _colors.textPrimary,
  );

  /// 输入框占位符文本样式
  TextStyle get inputPlaceholder => TextStyle(
    fontSize: UiConstants.fontSize16,
    height: 1.4,
    letterSpacing: -0.2,
    fontFamily: _fonts.primaryFont,
    fontWeight: UiConstants.fontWeightRegular,
    color: _colors.textTertiary,
  );

  /// 输入框标签文本样式
  TextStyle get inputLabel => TextStyle(
    fontSize: UiConstants.fontSize14,
    height: 1.4,
    letterSpacing: -0.2,
    fontFamily: _fonts.primaryFont,
    fontWeight: UiConstants.fontWeightMedium,
    color: _colors.textSecondary,
  );

  /// 输入框错误文本样式
  TextStyle get inputError => TextStyle(
    fontSize: UiConstants.fontSize12,
    height: 1.4,
    letterSpacing: -0.2,
    fontFamily: _fonts.primaryFont,
    fontWeight: UiConstants.fontWeightRegular,
    color: _colors.error,
  );

  /// 输入框帮助文本样式
  TextStyle get inputHelper => TextStyle(
    fontSize: UiConstants.fontSize12,
    height: 1.4,
    letterSpacing: -0.2,
    fontFamily: _fonts.primaryFont,
    fontWeight: UiConstants.fontWeightRegular,
    color: _colors.textTertiary,
  );

  /// 大号输入框文本样式
  TextStyle get inputTextLarge =>
      inputText.copyWith(fontSize: UiConstants.fontSize18);

  /// 小号输入框文本样式
  TextStyle get inputTextSmall =>
      inputText.copyWith(fontSize: UiConstants.fontSize14);

  /// 大号输入框占位符样式
  TextStyle get inputPlaceholderLarge =>
      inputPlaceholder.copyWith(fontSize: UiConstants.fontSize18);

  /// 小号输入框占位符样式
  TextStyle get inputPlaceholderSmall =>
      inputPlaceholder.copyWith(fontSize: UiConstants.fontSize14);

  // ========== 价格文本样式 ==========

  /// 价格文本样式
  TextStyle get priceText => bodyTextMedium.copyWith(
    fontFamily: _fonts.numberFont ?? _fonts.primaryFont,
  );

  /// 大号价格文本样式
  TextStyle get priceLarge => bodyLargeMedium.copyWith(
    fontFamily: _fonts.numberFont ?? _fonts.primaryFont,
  );

  /// 小号价格文本样式
  TextStyle get priceSmall => bodySmallMedium.copyWith(
    fontFamily: _fonts.numberFont ?? _fonts.primaryFont,
  );

  // ========== 工具方法 ==========

  /// 自定义文本样式
  TextStyle customText({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
    String? fontFamily,
    TextDecoration? decoration,
  }) {
    return TextStyle(
      fontSize: fontSize ?? UiConstants.fontSize14,
      fontWeight: fontWeight ?? UiConstants.fontWeightRegular,
      color: color ?? _colors.textPrimary,
      height: height ?? 1.4,
      letterSpacing: letterSpacing ?? -0.2,
      fontFamily: fontFamily ?? _fonts.primaryFont,
      decoration: decoration,
    );
  }

  /// 基于现有样式的颜色变体
  TextStyle withColor(TextStyle baseStyle, Color color) {
    return baseStyle.copyWith(color: color);
  }

  /// 基于现有样式的字重变体
  TextStyle withWeight(TextStyle baseStyle, FontWeight fontWeight) {
    return baseStyle.copyWith(fontWeight: fontWeight);
  }

  /// 基于现有样式的字号变体
  TextStyle withSize(TextStyle baseStyle, double fontSize) {
    return baseStyle.copyWith(fontSize: fontSize);
  }
}
