// ========================================
// 🎨 模板样式统一访问器
// ========================================

import 'package:flutter/material.dart';
import 'containers/container_styles.dart';
import 'buttons/button_styles.dart';
import 'text/text_styles.dart';
import 'inputs/input_styles.dart';
import 'kline/kline_style_provider.dart';

/// 统一样式访问器
///
/// 提供对所有模板样式的统一访问接口
class TemplateStyleAccessor {
  final BuildContext context;

  TemplateStyleAccessor(this.context);

  /// 容器样式
  TemplateContainerStyleProvider get container =>
      TemplateContainerStyleProvider(context);

  /// 按钮样式
  TemplateButtonStyleProvider get button =>
      TemplateButtonStyleProvider(context);

  /// 文本样式
  TemplateTextStyleProvider get text => TemplateTextStyleProvider(context);

  /// 输入框样式
  TemplateInputStyleProvider get input => TemplateInputStyleProvider(context);

  /// K线样式
  TemplateKlineStyleProvider get kline => TemplateKlineStyleProvider(context);
}

/// BuildContext扩展 - 便捷的样式访问
extension TemplateStyleExtension on BuildContext {
  /// 获取样式访问器
  TemplateStyleAccessor get templateStyle => TemplateStyleAccessor(this);
}
