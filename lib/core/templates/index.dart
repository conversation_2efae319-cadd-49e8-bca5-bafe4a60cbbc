// ========================================
// 🎨 模板系统统一导出
// ========================================

// === 核心配置接口 ===
export 'template_config.dart';

// === 模板注册管理 ===
export 'template_registry.dart' hide TemplateNames;

// === 资源管理 ===
export 'template_resource_manager.dart';

// === 主题提供者 ===
export 'template_theme_provider.dart';

// === 分类样式系统 ===
export 'styles/index.dart';

// === 模板管理器 ===
export 'template_manager.dart';

// === 具体模板实现 ===
export 'implementations/base_template.dart';
export 'implementations/okx_template.dart';
export 'implementations/bitget_template.dart';
