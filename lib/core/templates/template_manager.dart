// ========================================
// 🎨 模板管理器
// ========================================

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme/constants/theme_constants.dart';
import 'template_registry.dart';
import 'template_theme_provider.dart';

/// 模板管理器
/// 负责模板的切换、持久化存储和状态管理
class TemplateManager extends ChangeNotifier {
  static final TemplateManager _instance = TemplateManager._internal();
  static TemplateManager get instance => _instance;
  TemplateManager._internal();

  /// 当前选中的模板
  TemplateType _currentTemplate = TemplateType.base;
  
  /// 是否已初始化
  bool _isInitialized = false;

  /// SharedPreferences存储键
  static const String _templateKey = 'selected_template';

  /// 获取当前模板
  TemplateType get currentTemplate => _currentTemplate;

  /// 获取当前模板名称
  String get currentTemplateName => _currentTemplate.id;

  /// 获取当前模板显示名称
  String get currentTemplateDisplayName => _currentTemplate.displayName;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 初始化模板管理器
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 初始化模板提供者
      TemplateThemeProvider.instance;
      
      // 从本地存储加载模板设置
      await _loadTemplateFromStorage();
      
      _isInitialized = true;
      debugPrint('✅ 模板管理器初始化完成，当前模板: ${_currentTemplate.displayName}');
    } catch (e) {
      debugPrint('❌ 模板管理器初始化失败: $e');
      _currentTemplate = TemplateType.base;
      _isInitialized = true;
    }
  }

  /// 从本地存储加载模板设置
  Future<void> _loadTemplateFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final templateName = prefs.getString(_templateKey);
      
      if (templateName != null) {
        final templateType = TemplateType.fromId(templateName);
        _currentTemplate = templateType;
        debugPrint('📱 从本地存储加载模板: ${templateType.displayName}');
      } else {
        debugPrint('📱 未找到本地模板设置，使用默认模板');
      }
    } catch (e) {
      debugPrint('❌ 加载模板设置失败: $e');
    }
  }

  /// 切换模板
  Future<bool> switchTemplate(TemplateType template) async {
    if (_currentTemplate == template) {
      debugPrint('⚠️ 模板未改变: ${template.displayName}');
      return true;
    }

    try {
      // 检查模板是否存在
      if (!TemplateRegistry.instance.hasTemplate(template.id)) {
        debugPrint('❌ 模板不存在: ${template.displayName}');
        return false;
      }

      final oldTemplate = _currentTemplate;
      _currentTemplate = template;

      // 保存到本地存储
      await _saveTemplateToStorage();

      // 通知监听者
      notifyListeners();

      debugPrint('✅ 模板切换成功: ${oldTemplate.displayName} → ${template.displayName}');
      return true;
    } catch (e) {
      debugPrint('❌ 模板切换失败: $e');
      return false;
    }
  }

  /// 根据模板名称切换模板
  Future<bool> switchTemplateByName(String templateName) async {
    final templateType = TemplateType.fromId(templateName);
    return await switchTemplate(templateType);
  }

  /// 保存模板设置到本地存储
  Future<void> _saveTemplateToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_templateKey, _currentTemplate.id);
      debugPrint('💾 模板设置已保存: ${_currentTemplate.displayName}');
    } catch (e) {
      debugPrint('❌ 保存模板设置失败: $e');
    }
  }

  /// 获取所有可用模板
  List<TemplateType> get availableTemplates => TemplateType.all;

  /// 获取下一个模板（用于循环切换）
  TemplateType get nextTemplate {
    final currentIndex = TemplateType.all.indexOf(_currentTemplate);
    final nextIndex = (currentIndex + 1) % TemplateType.all.length;
    return TemplateType.all[nextIndex];
  }

  /// 获取上一个模板（用于循环切换）
  TemplateType get previousTemplate {
    final currentIndex = TemplateType.all.indexOf(_currentTemplate);
    final previousIndex = (currentIndex - 1 + TemplateType.all.length) % TemplateType.all.length;
    return TemplateType.all[previousIndex];
  }

  /// 循环切换到下一个模板
  Future<bool> switchToNextTemplate() async {
    return await switchTemplate(nextTemplate);
  }

  /// 循环切换到上一个模板
  Future<bool> switchToPreviousTemplate() async {
    return await switchTemplate(previousTemplate);
  }

  /// 重置为默认模板
  Future<bool> resetToDefault() async {
    return await switchTemplate(TemplateType.base);
  }

  /// 检查模板是否可用
  bool isTemplateAvailable(TemplateType template) {
    return TemplateRegistry.instance.hasTemplate(template.id);
  }

  /// 获取模板信息
  Map<String, dynamic> getTemplateInfo(TemplateType template) {
    final config = TemplateRegistry.instance.getTemplate(template.id);
    return {
      'id': template.id,
      'name': template.displayName,
      'available': config != null,
      'current': template == _currentTemplate,
    };
  }

  /// 获取所有模板信息
  List<Map<String, dynamic>> getAllTemplateInfo() {
    return TemplateType.all.map((template) => getTemplateInfo(template)).toList();
  }

  /// 清除本地存储的模板设置
  Future<void> clearStoredTemplate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_templateKey);
      debugPrint('🗑️ 已清除本地模板设置');
    } catch (e) {
      debugPrint('❌ 清除模板设置失败: $e');
    }
  }

  /// 打印当前状态信息
  void printStatus() {
    debugPrint('=== 模板管理器状态 ===');
    debugPrint('当前模板: ${_currentTemplate.displayName} (${_currentTemplate.id})');
    debugPrint('是否初始化: $_isInitialized');
    debugPrint('可用模板: ${TemplateType.all.map((e) => e.displayName).join(', ')}');
    debugPrint('==================');
  }

  @override
  void dispose() {
    super.dispose();
    debugPrint('🗑️ 模板管理器已销毁');
  }
}

/// 全局模板管理器实例
final templateManager = TemplateManager.instance;
