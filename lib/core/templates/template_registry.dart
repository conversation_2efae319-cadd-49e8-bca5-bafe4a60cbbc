// ========================================
// 🎨 模板注册管理器
// ========================================

import 'package:flutter/foundation.dart';
import 'template_config.dart';

/// 模板注册管理器
/// 负责管理所有可用的模板配置
class TemplateRegistry {
  static final TemplateRegistry _instance = TemplateRegistry._internal();
  static TemplateRegistry get instance => _instance;
  TemplateRegistry._internal();

  /// 已注册的模板配置
  final Map<String, TemplateConfig> _templates = {};

  /// 默认模板名称
  static const String defaultTemplateName = 'base';

  /// 注册模板
  void registerTemplate(TemplateConfig template) {
    _templates[template.name] = template;
    debugPrint('✅ 注册模板: ${template.name} (${template.displayName})');
  }

  /// 批量注册模板
  void registerTemplates(List<TemplateConfig> templates) {
    for (final template in templates) {
      registerTemplate(template);
    }
  }

  /// 获取模板配置
  TemplateConfig? getTemplate(String name) {
    return _templates[name];
  }

  /// 获取模板配置（带默认值）
  TemplateConfig getTemplateOrDefault(String name) {
    // 首先尝试获取指定的模板
    final template = _templates[name];
    if (template != null) return template;

    // 如果指定模板不存在，尝试获取默认模板
    final defaultTemplate = _templates[defaultTemplateName];
    if (defaultTemplate != null) return defaultTemplate;

    // 如果默认模板也不存在，返回第一个可用的模板
    if (_templates.isNotEmpty) {
      return _templates.values.first;
    }

    // 如果没有任何模板，抛出异常
    throw StateError(
      'No templates registered. Please register at least one template.',
    );
  }

  /// 获取所有可用模板名称
  List<String> get availableTemplates => _templates.keys.toList();

  /// 获取所有模板配置
  List<TemplateConfig> get allTemplates => _templates.values.toList();

  /// 检查模板是否存在
  bool hasTemplate(String name) {
    return _templates.containsKey(name);
  }

  /// 获取模板显示名称
  String getDisplayName(String name) {
    final template = _templates[name];
    return template?.displayName ?? '未知模板';
  }

  /// 清除所有模板
  void clear() {
    _templates.clear();
    debugPrint('🗑️ 清除所有模板注册');
  }

  /// 打印所有已注册的模板信息
  void printRegisteredTemplates() {
    debugPrint('=== 已注册的模板 ===');
    for (final template in _templates.values) {
      debugPrint('${template.name}: ${template.displayName}');
    }
    debugPrint('==================');
  }
}

/// 模板常量
class TemplateNames {
  static const String base = 'base';
  static const String okx = 'okx';
  static const String bitget = 'bitget';

  /// 获取所有可用模板名称
  static List<String> get all => [base, okx, bitget];
}
