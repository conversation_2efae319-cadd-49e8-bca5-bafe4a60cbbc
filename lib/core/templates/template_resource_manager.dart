// ========================================
// 🎨 模板资源管理器
// ========================================

import 'package:flutter/material.dart';
import '../theme/constants/theme_constants.dart';

/// 资源类型枚举
enum ResourceType { icon, image, lottie, crypto }

/// 模板资源管理器
/// 负责管理不同模板的资源文件路径
class TemplateResourceManager {
  static final TemplateResourceManager _instance =
      TemplateResourceManager._internal();
  static TemplateResourceManager get instance => _instance;
  TemplateResourceManager._internal();

  /// 获取资源路径
  String getResourcePath({
    required String resourceName,
    required ResourceType type,
    TemplateType? template,
    bool followTheme = false,
    String? fileExtension,
  }) {
    final templateType = template ?? TemplateType.base;
    final basePath = _getBasePath(type, templateType);
    final fileName = _buildFileName(
      resourceName,
      followTheme,
      fileExtension,
      type,
    );

    return '$basePath$fileName';
  }

  /// 获取基础路径
  String _getBasePath(ResourceType type, TemplateType template) {
    switch (type) {
      case ResourceType.icon:
        return 'assets/icons/';
      case ResourceType.image:
        return 'assets/images/${template.id}/';
      case ResourceType.lottie:
        return 'assets/lottie/main_tab/'; // Lottie 文件在 main_tab 子文件夹中
      case ResourceType.crypto:
        return 'assets/crypto/';
    }
  }

  /// 构建文件名
  String _buildFileName(
    String resourceName,
    bool followTheme,
    String? fileExtension,
    ResourceType type,
  ) {
    String fileName = resourceName;
    String extension = fileExtension ?? _getDefaultExtension(type);

    // 如果文件名已经包含扩展名，直接返回
    if (fileName.contains('.')) {
      return fileName;
    }

    // 处理主题相关的文件名
    if (followTheme &&
        (type == ResourceType.image || type == ResourceType.lottie)) {
      // 这里需要根据当前主题模式来决定文件名
      // 由于这是静态方法，我们暂时返回基础文件名
      // 实际的主题处理会在调用时进行
      fileName = '$fileName$extension';
    } else {
      fileName = '$fileName$extension';
    }

    return fileName;
  }

  /// 获取默认文件扩展名
  String _getDefaultExtension(ResourceType type) {
    switch (type) {
      case ResourceType.icon:
      case ResourceType.image:
      case ResourceType.crypto:
        return '.png';
      case ResourceType.lottie:
        return '.json';
    }
  }

  /// 获取主题相关的资源路径
  String getThemedResourcePath({
    required String resourceName,
    required ResourceType type,
    required bool isDark,
    TemplateType? template,
    String? fileExtension,
  }) {
    final templateType = template ?? TemplateType.base;
    final basePath = _getBasePath(type, templateType);
    final extension = fileExtension ?? _getDefaultExtension(type);

    String fileName;
    if (type == ResourceType.image && !resourceName.contains('.')) {
      // 图片资源根据主题模式选择文件
      if (isDark) {
        fileName = '$resourceName$extension';
      } else {
        fileName = '${resourceName}_light$extension';
      }
    } else if (type == ResourceType.lottie && !resourceName.contains('.')) {
      // Lottie动画根据主题模式选择文件
      if (isDark) {
        fileName = '$resourceName$extension';
      } else {
        fileName = '${resourceName}_light$extension';
      }
    } else {
      fileName =
          resourceName.contains('.') ? resourceName : '$resourceName$extension';
    }

    return '$basePath$fileName';
  }

  /// 检查资源是否存在（这里只是路径构建，实际存在性检查需要在使用时进行）
  bool isResourcePathValid(String path) {
    return path.isNotEmpty && path.startsWith('assets/');
  }

  /// 获取所有支持的图片格式
  List<String> get supportedImageFormats => [
    '.png',
    '.jpg',
    '.jpeg',
    '.webp',
    '.gif',
  ];

  /// 获取所有支持的动画格式
  List<String> get supportedAnimationFormats => ['.json'];

  /// 打印资源路径信息（调试用）
  void printResourceInfo(
    String resourceName,
    ResourceType type,
    TemplateType template,
  ) {
    final path = getResourcePath(
      resourceName: resourceName,
      type: type,
      template: template,
    );
    debugPrint('资源路径: $path');
  }
}

/// 资源路径构建器 - 便捷的静态方法
class ResourcePathBuilder {
  /// 构建图片路径
  static String image(
    String name, {
    TemplateType? template,
    bool followTheme = false,
  }) {
    return TemplateResourceManager.instance.getResourcePath(
      resourceName: name,
      type: ResourceType.image,
      template: template,
      followTheme: followTheme,
    );
  }

  /// 构建图标路径
  static String icon(String name) {
    return TemplateResourceManager.instance.getResourcePath(
      resourceName: name,
      type: ResourceType.icon,
    );
  }

  /// 构建Lottie动画路径
  static String lottie(String name, {bool followTheme = false}) {
    return TemplateResourceManager.instance.getResourcePath(
      resourceName: name,
      type: ResourceType.lottie,
      followTheme: followTheme,
    );
  }

  /// 构建加密货币图标路径
  static String crypto(String name) {
    return TemplateResourceManager.instance.getResourcePath(
      resourceName: name,
      type: ResourceType.crypto,
    );
  }

  /// 构建主题相关的图片路径
  static String themedImage(
    String name,
    bool isDark, {
    TemplateType? template,
  }) {
    return TemplateResourceManager.instance.getThemedResourcePath(
      resourceName: name,
      type: ResourceType.image,
      isDark: isDark,
      template: template,
    );
  }

  /// 构建主题相关的Lottie路径
  static String themedLottie(String name, bool isDark) {
    return TemplateResourceManager.instance.getThemedResourcePath(
      resourceName: name,
      type: ResourceType.lottie,
      isDark: isDark,
    );
  }
}
