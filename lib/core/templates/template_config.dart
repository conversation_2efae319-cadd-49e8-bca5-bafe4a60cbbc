// ========================================
// 🎨 统一模板配置接口
// ========================================

import 'package:flutter/material.dart';
import 'styles/kline/kline_styles.dart';

/// 模板资源配置
class TemplateAssets {
  /// 图标资源路径
  final String iconPath;

  /// 图片资源路径
  final String imagePath;

  /// Lottie动画资源路径
  final String lottiePath;

  const TemplateAssets({
    required this.iconPath,
    required this.imagePath,
    required this.lottiePath,
  });
}

/// 模板字体配置
class TemplateFonts {
  /// 主要字体
  final String primaryFont;

  /// 次要字体
  final String? secondaryFont;

  /// 数字字体
  final String? numberFont;

  const TemplateFonts({
    required this.primaryFont,
    this.secondaryFont,
    this.numberFont,
  });
}

/// 模板颜色配置
class TemplateColors {
  // ========== 品牌颜色 ==========
  final Color primary;
  final Color secondary;

  // ========== 背景颜色 ==========
  final Color background;
  final Color surface;
  final Color cardBackground;
  final Color popupBackground;
  final Color tabbarBackground;
  final Color tabbarActive;

  // ========== 文本颜色 ==========
  final Color textPrimary;
  final Color textSecondary;
  final Color textTertiary;

  // ========== 按钮颜色 ==========
  final Color buttonPrimary;
  final Color buttonSecondary;
  final Color buttonText;
  final Color buttonTextSecondary;

  // ========== 输入框颜色 ==========
  final Color inputBackground;
  final Color inputBorder;
  final Color inputFocusedBorder;

  // ========== 交易颜色 ==========
  final Color tradeBuy;
  final Color tradeSell;

  // ========== 状态颜色 ==========
  final Color success;
  final Color warning;
  final Color error;
  final Color info;

  // ========== 分割线和边框 ==========
  final Color divider;
  final Color border;

  // ========== 骨架屏颜色 ==========
  final Color skeletonBase;
  final Color skeletonHighlight;

  // ========== 其他颜色 ==========
  final Color white;
  final Color dragIndicator;

  const TemplateColors({
    required this.primary,
    required this.secondary,
    required this.background,
    required this.surface,
    required this.cardBackground,
    required this.popupBackground,
    required this.tabbarBackground,
    required this.tabbarActive,
    required this.textPrimary,
    required this.textSecondary,
    required this.textTertiary,
    required this.buttonPrimary,
    required this.buttonSecondary,
    required this.buttonText,
    required this.buttonTextSecondary,
    required this.inputBackground,
    required this.inputBorder,
    required this.inputFocusedBorder,
    required this.tradeBuy,
    required this.tradeSell,
    required this.success,
    required this.warning,
    required this.error,
    required this.white,
    required this.info,
    required this.divider,
    required this.border,
    required this.skeletonBase,
    required this.skeletonHighlight,
    required this.dragIndicator,
  });
}

/// 模板样式配置
class TemplateStyles {
  // ========== 圆角配置 ==========
  final double borderRadiusSmall;
  final double borderRadiusMedium;
  final double borderRadiusLarge;

  // ========== 间距配置 ==========
  final double spacingSmall;
  final double spacingMedium;
  final double spacingLarge;

  // ========== 阴影配置 ==========
  final double elevationLow;
  final double elevationMedium;
  final double elevationHigh;

  // ========== 边框配置 ==========
  final double borderWidthThin;
  final double borderWidthMedium;
  final double borderWidthThick;

  // ========== 按钮高度 ==========
  final double buttonHeightMedium;

  const TemplateStyles({
    required this.borderRadiusSmall,
    required this.borderRadiusMedium,
    required this.borderRadiusLarge,
    required this.spacingSmall,
    required this.spacingMedium,
    required this.spacingLarge,
    required this.elevationLow,
    required this.elevationMedium,
    required this.elevationHigh,
    required this.borderWidthThin,
    required this.borderWidthMedium,
    required this.borderWidthThick,
    required this.buttonHeightMedium,
  });
}

/// 统一模板配置接口
abstract class TemplateConfig {
  /// 模板名称
  String get name;

  /// 模板显示名称
  String get displayName;

  /// 浅色模式颜色配置
  TemplateColors get lightColors;

  /// 深色模式颜色配置
  TemplateColors get darkColors;

  /// 样式配置
  TemplateStyles get styles;

  /// 字体配置
  TemplateFonts get fonts;

  /// 资源配置
  TemplateAssets get assets;

  /// K线样式配置（浅色模式）
  TemplateKlineStyles get lightKlineStyles;

  /// K线样式配置（深色模式）
  TemplateKlineStyles get darkKlineStyles;

  /// 根据深浅色模式获取颜色配置
  TemplateColors getColors(bool isDark) {
    return isDark ? darkColors : lightColors;
  }

  /// 根据深浅色模式获取K线样式配置
  TemplateKlineStyles getKlineStyles(bool isDark) {
    return isDark ? darkKlineStyles : lightKlineStyles;
  }
}
