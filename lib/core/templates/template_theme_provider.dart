// ========================================
// 🎨 模板主题提供者
// ========================================

import 'package:flutter/material.dart';
import '../theme/extensions/template_theme_extension.dart';
import 'template_config.dart';
import 'template_registry.dart';
import 'implementations/base_template.dart';
import 'implementations/okx_template.dart';
import 'implementations/bitget_template.dart';
import 'styles/kline/kline_styles.dart';

/// 模板主题提供者
/// 提供统一的主题访问接口
class TemplateThemeProvider {
  static final TemplateThemeProvider _instance =
      TemplateThemeProvider._internal();
  static TemplateThemeProvider get instance => _instance;
  TemplateThemeProvider._internal() {
    _initializeTemplates();
  }

  /// 初始化所有模板
  void _initializeTemplates() {
    final registry = TemplateRegistry.instance;

    // 注册所有模板
    registry.registerTemplates([
      BaseTemplate(),
      OkxTemplate(),
      BitgetTemplate(),
    ]);

    debugPrint('✅ 模板主题提供者初始化完成');
  }

  /// 从BuildContext获取当前模板配置
  TemplateConfig getCurrentTemplate(BuildContext context) {
    final templateName = TemplateThemeExtension.of(context);
    return TemplateRegistry.instance.getTemplateOrDefault(templateName);
  }

  /// 从BuildContext获取当前颜色配置
  TemplateColors getCurrentColors(BuildContext context) {
    final template = getCurrentTemplate(context);
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return template.getColors(isDark);
  }

  /// 从 BuildContext 获取当前样式配置
  TemplateStyles getCurrentStyles(BuildContext context) {
    final template = getCurrentTemplate(context);
    return template.styles;
  }

  /// 从 BuildContext 获取当前字体配置
  TemplateFonts getCurrentFonts(BuildContext context) {
    final template = getCurrentTemplate(context);
    return template.fonts;
  }

  /// 从BuildContext获取当前资源配置
  TemplateAssets getCurrentAssets(BuildContext context) {
    final template = getCurrentTemplate(context);
    return template.assets;
  }

  /// 从BuildContext获取当前K线样式配置
  TemplateKlineStyles getCurrentKlineStyles(BuildContext context) {
    final template = getCurrentTemplate(context);
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return template.getKlineStyles(isDark);
  }

  /// 根据模板名称和主题模式获取颜色配置
  TemplateColors getColors(String templateName, bool isDark) {
    final template = TemplateRegistry.instance.getTemplateOrDefault(
      templateName,
    );
    return template.getColors(isDark);
  }

  /// 根据模板名称获取样式配置
  TemplateStyles getStyles(String templateName) {
    final template = TemplateRegistry.instance.getTemplateOrDefault(
      templateName,
    );
    return template.styles;
  }

  /// 获取所有可用模板
  List<TemplateConfig> get availableTemplates {
    return TemplateRegistry.instance.allTemplates;
  }

  /// 检查模板是否存在
  bool hasTemplate(String templateName) {
    return TemplateRegistry.instance.hasTemplate(templateName);
  }
}

/// BuildContext 扩展 - 便捷的主题访问
extension TemplateThemeContextExtension on BuildContext {
  /// 获取当前模板配置
  TemplateConfig get template =>
      TemplateThemeProvider.instance.getCurrentTemplate(this);

  /// 获取当前颜色配置
  TemplateColors get templateColors =>
      TemplateThemeProvider.instance.getCurrentColors(this);

  /// 获取当前样式配置
  TemplateStyles get templateStyles =>
      TemplateThemeProvider.instance.getCurrentStyles(this);

  /// 获取当前字体配置
  TemplateFonts get templateFonts =>
      TemplateThemeProvider.instance.getCurrentFonts(this);

  /// 获取当前资源配置
  TemplateAssets get templateAssets =>
      TemplateThemeProvider.instance.getCurrentAssets(this);

  /// 获取当前K线样式配置
  TemplateKlineStyles get templateKlineStyles =>
      TemplateThemeProvider.instance.getCurrentKlineStyles(this);
}

/// 全局模板主题访问器 - 向后兼容
class TemplateTheme {
  static final TemplateTheme _instance = TemplateTheme._internal();
  static TemplateTheme get instance => _instance;
  TemplateTheme._internal();

  /// 获取颜色配置
  TemplateColors colors(BuildContext context) {
    return TemplateThemeProvider.instance.getCurrentColors(context);
  }

  /// 获取样式配置
  TemplateStyles styles(BuildContext context) {
    return TemplateThemeProvider.instance.getCurrentStyles(context);
  }

  /// 获取字体配置
  TemplateFonts fonts(BuildContext context) {
    return TemplateThemeProvider.instance.getCurrentFonts(context);
  }

  /// 获取资源配置
  TemplateAssets assets(BuildContext context) {
    return TemplateThemeProvider.instance.getCurrentAssets(context);
  }

  // ========== 便捷的颜色访问方法 ==========

  Color primary(BuildContext context) => colors(context).primary;
  Color secondary(BuildContext context) => colors(context).secondary;
  Color background(BuildContext context) => colors(context).background;
  Color surface(BuildContext context) => colors(context).surface;
  Color textPrimary(BuildContext context) => colors(context).textPrimary;
  Color textSecondary(BuildContext context) => colors(context).textSecondary;
  Color textTertiary(BuildContext context) => colors(context).textTertiary;
  Color buttonPrimary(BuildContext context) => colors(context).buttonPrimary;
  Color buttonText(BuildContext context) => colors(context).buttonText;
  Color tradeBuy(BuildContext context) => colors(context).tradeBuy;
  Color tradeSell(BuildContext context) => colors(context).tradeSell;
  Color divider(BuildContext context) => colors(context).divider;
  Color inputBackground(BuildContext context) =>
      colors(context).inputBackground;
  Color skeletonBase(BuildContext context) => colors(context).skeletonBase;
  Color skeletonHighlight(BuildContext context) =>
      colors(context).skeletonHighlight;
}

/// 全局模板主题实例 - 向后兼容
final templateTheme = TemplateTheme.instance;
