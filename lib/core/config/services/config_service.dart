import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/config_models.dart';

/// 配置服务 - 简化版本
/// 负责从后端获取应用配置，包括主题模板配置
class ConfigService {
  static final ConfigService _instance = ConfigService._internal();
  static ConfigService get instance => _instance;
  ConfigService._internal();

  AppConfigModel? _currentConfig;
  bool _isInitialized = false;
  static const String _cacheKey = 'app_config_cache';
  static const String _baseUrl = 'https://api.example.com';

  /// 获取当前配置
  AppConfigModel get currentConfig =>
      _currentConfig ?? AppConfigModel.defaultConfig();

  /// 获取主题配置
  ThemeConfig get themeConfig => currentConfig.themeConfig;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 初始化配置
  Future<AppConfigModel> initializeConfig() async {
    if (_isInitialized) return currentConfig;

    try {
      debugPrint('🔧 初始化配置服务...');

      // 先尝试从缓存加载
      await _loadFromCache();

      // 然后从网络更新
      await _loadFromNetwork();

      _isInitialized = true;
      debugPrint('✅ 配置服务初始化完成');

      return currentConfig;
    } catch (e) {
      debugPrint('❌ 配置服务初始化失败: $e');

      // 如果网络失败，使用默认配置
      _currentConfig = AppConfigModel.defaultConfig();
      _isInitialized = true;

      return currentConfig;
    }
  }

  /// 强制刷新配置
  Future<AppConfigModel> refreshConfig() async {
    try {
      debugPrint('🔄 刷新配置...');
      await _loadFromNetwork();
      debugPrint('✅ 配置刷新完成');
      return currentConfig;
    } catch (e) {
      debugPrint('❌ 配置刷新失败: $e');
      return currentConfig;
    }
  }

  /// 从缓存加载配置
  Future<void> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_cacheKey);

      if (cachedData != null) {
        final configMap = json.decode(cachedData) as Map<String, dynamic>;
        _currentConfig = AppConfigModel.fromJson(configMap);
        debugPrint('✅ 从缓存加载配置成功');
      }
    } catch (e) {
      debugPrint('⚠️ 从缓存加载配置失败: $e');
    }
  }

  /// 从网络加载配置
  Future<void> _loadFromNetwork() async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/config'),
            headers: {'Content-Type': 'application/json'},
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final configMap = json.decode(response.body) as Map<String, dynamic>;
        _currentConfig = AppConfigModel.fromJson(configMap);

        // 保存到缓存
        await _saveToCache();

        debugPrint('✅ 从网络加载配置成功');
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      debugPrint('❌ 从网络加载配置失败: $e');

      // 如果没有缓存配置，使用默认配置
      _currentConfig ??= AppConfigModel.defaultConfig();

      rethrow;
    }
  }

  /// 保存配置到缓存
  Future<void> _saveToCache() async {
    try {
      if (_currentConfig != null) {
        final prefs = await SharedPreferences.getInstance();
        final configJson = json.encode(_currentConfig!.toJson());
        await prefs.setString(_cacheKey, configJson);
        debugPrint('✅ 配置已保存到缓存');
      }
    } catch (e) {
      debugPrint('⚠️ 保存配置到缓存失败: $e');
    }
  }

  /// 清除缓存
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      debugPrint('✅ 配置缓存已清除');
    } catch (e) {
      debugPrint('❌ 清除配置缓存失败: $e');
    }
  }

  /// 获取缓存信息
  Future<Map<String, dynamic>> getCacheInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_cacheKey);

      return {
        'hasCachedData': cachedData != null,
        'cacheSize': cachedData?.length ?? 0,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {'hasCachedData': false, 'cacheSize': 0, 'error': e.toString()};
    }
  }
}
