// ========================================
// 🔧 应用配置模型
// ========================================

/// 主题配置模型
class ThemeConfig {
  /// 主题模板名称
  final String templateName;

  /// 是否默认使用深色模式
  final bool defaultDarkMode;

  /// 是否允许用户切换主题
  final bool allowThemeSwitch;

  /// 是否允许用户切换深色模式
  final bool allowDarkModeSwitch;

  const ThemeConfig({
    required this.templateName,
    this.defaultDarkMode = false,
    this.allowThemeSwitch = true,
    this.allowDarkModeSwitch = true,
  });

  /// 从JSON创建ThemeConfig
  factory ThemeConfig.fromJson(Map<String, dynamic> json) {
    return ThemeConfig(
      templateName: json['templateName'] ?? 'base',
      defaultDarkMode: json['defaultDarkMode'] ?? false,
      allowThemeSwitch: json['allowThemeSwitch'] ?? true,
      allowDarkModeSwitch: json['allowDarkModeSwitch'] ?? true,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'templateName': templateName,
      'defaultDarkMode': defaultDarkMode,
      'allowThemeSwitch': allowThemeSwitch,
      'allowDarkModeSwitch': allowDarkModeSwitch,
    };
  }

  /// 创建默认配置
  factory ThemeConfig.defaultConfig() {
    return const ThemeConfig(
      templateName: 'base',
      defaultDarkMode: false,
      allowThemeSwitch: true,
      allowDarkModeSwitch: true,
    );
  }

  @override
  String toString() {
    return 'ThemeConfig(templateName: $templateName, defaultDarkMode: $defaultDarkMode, allowThemeSwitch: $allowThemeSwitch, allowDarkModeSwitch: $allowDarkModeSwitch)';
  }
}

/// 应用动态配置模型（从后端获取）
class AppConfigModel {
  /// 主题配置
  final ThemeConfig themeConfig;

  /// API基础URL
  final String apiBaseUrl;

  /// 应用版本
  final String appVersion;

  /// 是否启用调试模式
  final bool debugMode;

  const AppConfigModel({
    required this.themeConfig,
    required this.apiBaseUrl,
    required this.appVersion,
    this.debugMode = false,
  });

  /// 从JSON创建AppConfigModel
  factory AppConfigModel.fromJson(Map<String, dynamic> json) {
    return AppConfigModel(
      themeConfig: ThemeConfig.fromJson(json['themeConfig'] ?? {}),
      apiBaseUrl: json['apiBaseUrl'] ?? '',
      appVersion: json['appVersion'] ?? '1.0.0',
      debugMode: json['debugMode'] ?? false,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'themeConfig': themeConfig.toJson(),
      'apiBaseUrl': apiBaseUrl,
      'appVersion': appVersion,
      'debugMode': debugMode,
    };
  }

  /// 创建默认配置
  factory AppConfigModel.defaultConfig() {
    return AppConfigModel(
      themeConfig: ThemeConfig.defaultConfig(),
      apiBaseUrl: 'https://api.qubic-exchange.com',
      appVersion: '1.0.0',
      debugMode: false,
    );
  }

  @override
  String toString() {
    return 'AppConfigModel(themeConfig: $themeConfig, apiBaseUrl: $apiBaseUrl, appVersion: $appVersion, debugMode: $debugMode)';
  }
}
