/*
*  UI相关常量
*/

import 'package:flutter/material.dart';

class UiConstants {
  // 私有构造函数，禁止外部实例化
  UiConstants._();

  // === 字体尺寸 ===
  static double get fontSize10 => 10;
  static double get fontSize12 => 12;
  static double get fontSize13 => 13;
  static double get fontSize14 => 14;
  static double get fontSize16 => 16;
  static double get fontSize18 => 18;
  static double get fontSize20 => 20;
  static double get fontSize24 => 24;
  static double get fontSize28 => 28;
  static double get fontSize32 => 32;
  static double get fontSize36 => 36;
  static double get fontSize40 => 40;
  static double get fontSize48 => 48;
  static double get fontSize56 => 56;
  static double get fontSize64 => 64;

  // === 字体粗细 ===
  static FontWeight get fontWeightRegular => FontWeight.w400;
  static FontWeight get fontWeightMedium => FontWeight.w600;
  static FontWeight get fontWeightSemiBold => FontWeight.w800;
  static FontWeight get fontWeightBold => FontWeight.w900;

  // === 边距 ===
  static double get spacing2 => 2;
  static double get spacing4 => 4;
  static double get spacing6 => 6;
  static double get spacing8 => 8;
  static double get spacing10 => 10;
  static double get spacing12 => 12;
  static double get spacing14 => 14;
  static double get spacing16 => 16;
  static double get spacing18 => 18;
  static double get spacing20 => 20;
  static double get spacing22 => 22;
  static double get spacing24 => 24;
  static double get spacing28 => 28;
  static double get spacing32 => 32;
  static double get spacing36 => 36;
  static double get spacing40 => 40;
  static double get spacing48 => 48;
  static double get spacing56 => 56;
  static double get spacing64 => 64;

  // === 圆角 ===
  static double get borderRadius2 => 2;
  static double get borderRadius4 => 4;
  static double get borderRadius6 => 6;
  static double get borderRadius8 => 8;
  static double get borderRadius10 => 10;
  static double get borderRadius12 => 12;
  static double get borderRadius14 => 14;
  static double get borderRadius16 => 16;
  static double get borderRadius18 => 18;
  static double get borderRadius20 => 20;
  static double get borderRadiusCircle => 999;

  // === 边框 ===
  static double get borderWidth0_5 => 0.5;
  static double get borderWidth1 => 1;
  static double get borderWidth1_5 => 1.5;
  static double get borderWidth2 => 2;
  static double get borderWidth2_5 => 2.5;
  static double get borderWidth3 => 3;
  static double get borderWidth4 => 4;

  // === 阴影 ===
  static double get elevation0_5 => 0.5;
  static double get elevation1 => 1;
  static double get elevation2 => 2;
  static double get elevation4 => 4;
  static double get elevation8 => 8;
  static double get elevation16 => 16;

  // === 图标尺寸 ===
  static double get iconSize10 => 10;
  static double get iconSize12 => 12;
  static double get iconSize14 => 14;
  static double get iconSize16 => 16;
  static double get iconSize20 => 20;
  static double get iconSize24 => 24;
  static double get iconSize28 => 28;
  static double get iconSize32 => 32;
  static double get iconSize36 => 36;
  static double get iconSize40 => 40;
  static double get iconSize48 => 48;
  static double get iconSize56 => 56;
  static double get iconSize64 => 64;

  // === 图片尺寸 ===
  static double get imageSize48 => 48;
  static double get imageSize96 => 96;
  static double get imageSize192 => 192;

  // === 按钮高度 ===
  static double get buttonHeightSmall => 40;
  static double get buttonHeightMedium => 45;
  static double get buttonHeightLarge => 56;

  // === 字间距 ===
  static double get letterSpacing0_5 => 0.5;
  static double get letterSpacing1 => 1;
  static double get letterSpacing2 => 2;
  static double get letterSpacing4 => 4;

  // === 行高 ===
  static double get lineHeight1_5 => 1.5;
  static double get lineHeight2 => 2;
  static double get lineHeight3 => 3;

  // === 动画持续时间 ===
  static Duration get animationDurationShort =>
      const Duration(milliseconds: 200);
  static Duration get animationDurationMedium =>
      const Duration(milliseconds: 300);
  static Duration get animationDurationLong =>
      const Duration(milliseconds: 400);

  // === 动画模式 ===
  static Curve get animationCurveEaseInOut => Curves.easeInOut;
  static Curve get animationCurveEaseOut => Curves.easeOut;
  static Curve get animationCurveEaseIn => Curves.easeIn;
}
