/*
 * 货币配置常量
 * 
 * 功能：
 * - 定义支持的货币列表
 * - 包含货币代码、名称、符号的映射关系
 * - 提供货币查询和验证方法
 */

import 'package:qubic_exchange/core/models/currency_model.dart';

/// 货币配置类
class CurrencyConfig {
  CurrencyConfig._();

  /// 支持的货币列表
  static const List<CurrencyModel> supportedCurrencies = [
    CurrencyModel(
      code: 'CNY',
      name: '人民币',
      symbol: '¥',
      englishName: 'Chinese Yuan',
      isDefault: true,
    ),
    CurrencyModel(
      code: 'USD',
      name: '美元',
      symbol: '\$',
      englishName: 'US Dollar',
    ),
    CurrencyModel(
      code: 'HKD',
      name: '港币',
      symbol: 'HK\$',
      englishName: 'Hong Kong Dollar',
    ),
    CurrencyModel(code: 'EUR', name: '欧元', symbol: '€', englishName: 'Euro'),
    CurrencyModel(
      code: 'JPY',
      name: '日元',
      symbol: '¥',
      englishName: 'Japanese Yen',
    ),
    CurrencyModel(
      code: 'GBP',
      name: '英镑',
      symbol: '£',
      englishName: 'British Pound',
    ),
    CurrencyModel(
      code: 'AUD',
      name: '澳元',
      symbol: 'A\$',
      englishName: 'Australian Dollar',
    ),
    CurrencyModel(
      code: 'CAD',
      name: '加元',
      symbol: 'C\$',
      englishName: 'Canadian Dollar',
    ),
    CurrencyModel(
      code: 'CHF',
      name: '瑞士法郎',
      symbol: 'CHF',
      englishName: 'Swiss Franc',
    ),
    CurrencyModel(
      code: 'SGD',
      name: '新加坡元',
      symbol: 'S\$',
      englishName: 'Singapore Dollar',
    ),
    CurrencyModel(
      code: 'KRW',
      name: '韩元',
      symbol: '₩',
      englishName: 'South Korean Won',
    ),
    CurrencyModel(
      code: 'THB',
      name: '泰铢',
      symbol: '฿',
      englishName: 'Thai Baht',
    ),
    CurrencyModel(
      code: 'MYR',
      name: '马来西亚林吉特',
      symbol: 'RM',
      englishName: 'Malaysian Ringgit',
    ),
    CurrencyModel(
      code: 'INR',
      name: '印度卢比',
      symbol: '₹',
      englishName: 'Indian Rupee',
    ),
    CurrencyModel(
      code: 'RUB',
      name: '俄罗斯卢布',
      symbol: '₽',
      englishName: 'Russian Ruble',
    ),
  ];

  /// 货币代码映射表（用于快速查找）
  static final Map<String, CurrencyModel> _currencyMap = {
    for (var currency in supportedCurrencies) currency.code: currency,
  };

  /// 根据货币代码获取货币信息
  static CurrencyModel? getCurrencyByCode(String code) {
    return _currencyMap[code.toUpperCase()];
  }

  /// 获取货币符号
  static String getCurrencySymbol(String code) {
    final currency = getCurrencyByCode(code);
    return currency?.symbol ?? '\$'; // 默认使用美元符号
  }

  /// 获取货币名称
  static String getCurrencyName(String code) {
    final currency = getCurrencyByCode(code);
    return currency?.name ?? code;
  }

  /// 获取货币英文名称
  static String getCurrencyEnglishName(String code) {
    final currency = getCurrencyByCode(code);
    return currency?.englishName ?? code;
  }

  /// 验证货币代码是否支持
  static bool isSupportedCurrency(String code) {
    return _currencyMap.containsKey(code.toUpperCase());
  }

  /// 获取默认货币
  static CurrencyModel get defaultCurrency {
    return supportedCurrencies.firstWhere(
      (currency) => currency.isDefault,
      orElse: () => supportedCurrencies.first,
    );
  }

  /// 获取所有货币代码列表
  static List<String> get allCurrencyCodes {
    return supportedCurrencies.map((currency) => currency.code).toList();
  }

  /// 搜索货币（支持代码、名称、英文名称搜索）
  static List<CurrencyModel> searchCurrencies(String query) {
    if (query.isEmpty) return supportedCurrencies;

    final lowerQuery = query.toLowerCase();
    return supportedCurrencies.where((currency) {
      return currency.code.toLowerCase().contains(lowerQuery) ||
          currency.name.toLowerCase().contains(lowerQuery) ||
          currency.englishName.toLowerCase().contains(lowerQuery);
    }).toList();
  }
}
