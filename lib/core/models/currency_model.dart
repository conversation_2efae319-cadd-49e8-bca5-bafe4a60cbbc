/*
 * 货币数据模型
 * 
 * 功能：
 * - 定义货币的基本信息结构
 * - 包含货币代码、名称、符号等属性
 * - 支持序列化和反序列化
 */

/// 货币信息模型
class CurrencyModel {
  /// 货币代码（如：CNY、USD）
  final String code;
  
  /// 货币名称（如：人民币、美元）
  final String name;
  
  /// 货币符号（如：¥、$）
  final String symbol;
  
  /// 货币英文名称（如：Chinese Yuan、US Dollar）
  final String englishName;
  
  /// 是否为默认货币
  final bool isDefault;

  const CurrencyModel({
    required this.code,
    required this.name,
    required this.symbol,
    required this.englishName,
    this.isDefault = false,
  });

  /// 从 JSON 创建货币模型
  factory CurrencyModel.fromJson(Map<String, dynamic> json) {
    return CurrencyModel(
      code: json['code'] as String,
      name: json['name'] as String,
      symbol: json['symbol'] as String,
      englishName: json['englishName'] as String,
      isDefault: json['isDefault'] as bool? ?? false,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'symbol': symbol,
      'englishName': englishName,
      'isDefault': isDefault,
    };
  }

  /// 复制并修改属性
  CurrencyModel copyWith({
    String? code,
    String? name,
    String? symbol,
    String? englishName,
    bool? isDefault,
  }) {
    return CurrencyModel(
      code: code ?? this.code,
      name: name ?? this.name,
      symbol: symbol ?? this.symbol,
      englishName: englishName ?? this.englishName,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CurrencyModel && other.code == code;
  }

  @override
  int get hashCode => code.hashCode;

  @override
  String toString() {
    return 'CurrencyModel(code: $code, name: $name, symbol: $symbol)';
  }
}
