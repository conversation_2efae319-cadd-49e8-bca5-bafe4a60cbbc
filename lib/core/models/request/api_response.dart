///自定义api响应类
class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final int statusCode;

  ApiResponse({required this.success, required this.message, this.data, required this.statusCode});
}

/// 自定义异常类
class ApiException implements Exception {
  final String message;
  final int statusCode;

  ApiException(this.message, this.statusCode);

  @override
  String toString() => 'ApiException: $statusCode - $message';
}
