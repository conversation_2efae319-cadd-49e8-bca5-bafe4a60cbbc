/*
  全局焦点管理器
  
  功能：
  - 统一管理应用中所有输入框的焦点状态
  - 提供全局点击取消焦点的功能
  - 支持特定区域的焦点保护
  - 键盘显示/隐藏状态管理
*/

import 'package:flutter/material.dart';

/// 全局焦点管理器
class GlobalFocusManager extends ChangeNotifier {
  static final GlobalFocusManager _instance = GlobalFocusManager._internal();
  static GlobalFocusManager get instance => _instance;
  GlobalFocusManager._internal();

  // ========== 私有属性 ==========
  final List<FocusNode> _activeFocusNodes = [];
  final List<GlobalKey> _protectedAreas = [];
  bool _isKeyboardVisible = false;

  // ========== 公共属性 ==========
  /// 是否有输入框处于焦点状态
  bool get hasFocus => _activeFocusNodes.any((node) => node.hasFocus);

  /// 键盘是否可见
  bool get isKeyboardVisible => _isKeyboardVisible;

  /// 当前活跃的焦点节点数量
  int get activeFocusCount =>
      _activeFocusNodes.where((node) => node.hasFocus).length;

  // ========== 焦点管理方法 ==========
  /// 注册焦点节点
  void registerFocusNode(FocusNode focusNode) {
    if (!_activeFocusNodes.contains(focusNode)) {
      _activeFocusNodes.add(focusNode);
      debugPrint('注册焦点节点: ${focusNode.debugLabel ?? 'unnamed'}');
      // 添加焦点变化监听
      focusNode.addListener(_onFocusNodeChanged);
    }
  }

  /// 注销焦点节点
  void unregisterFocusNode(FocusNode focusNode) {
    if (_activeFocusNodes.remove(focusNode)) {
      // 移除焦点变化监听
      focusNode.removeListener(_onFocusNodeChanged);
      //debugPrint('注销焦点节点: ${focusNode.debugLabel ?? 'unnamed'}');
      // 通知状态变化
      notifyListeners();
    }
  }

  /// 焦点节点状态变化回调
  void _onFocusNodeChanged() {
    // 通知监听器焦点状态发生变化
    notifyListeners();
  }

  /// 取消所有焦点
  void unfocusAll(BuildContext context) {
    try {
      // 方法1: 使用FocusScope
      FocusScope.of(context).unfocus();

      // 方法2: 手动取消所有注册的焦点节点
      for (final node in _activeFocusNodes) {
        if (node.hasFocus) {
          node.unfocus();
        }
      }

      debugPrint('已取消所有输入框焦点');
    } catch (e) {
      debugPrint('取消焦点失败: $e');
    }
  }

  /// 添加受保护区域
  /// 在这些区域内点击不会取消焦点
  void addProtectedArea(GlobalKey key) {
    if (!_protectedAreas.contains(key)) {
      _protectedAreas.add(key);
      debugPrint('添加焦点保护区域');
    }
  }

  /// 移除受保护区域
  void removeProtectedArea(GlobalKey key) {
    _protectedAreas.remove(key);
    debugPrint('移除焦点保护区域');
  }

  /// 检查点击位置是否在受保护区域内
  bool _isInProtectedArea(Offset globalPosition) {
    for (final key in _protectedAreas) {
      final RenderBox? renderBox =
          key.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final Offset localPosition = renderBox.globalToLocal(globalPosition);
        final Size size = renderBox.size;

        if (localPosition.dx >= 0 &&
            localPosition.dx <= size.width &&
            localPosition.dy >= 0 &&
            localPosition.dy <= size.height) {
          return true;
        }
      }
    }
    return false;
  }

  // ========== 键盘状态管理 ==========
  /// 更新键盘可见状态
  void updateKeyboardVisibility(BuildContext context) {
    final bool keyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;
    if (_isKeyboardVisible != keyboardVisible) {
      _isKeyboardVisible = keyboardVisible;
      debugPrint('⌨键盘状态变化: ${keyboardVisible ? '显示' : '隐藏'}');
    }
  }

  // ========== 全局手势处理 ==========
  /// 处理全局点击事件
  /// 返回true表示事件已被处理，应该阻止其他操作
  bool handleGlobalTap(BuildContext context, Offset globalPosition) {
    // 检查是否在受保护区域内
    if (_isInProtectedArea(globalPosition)) {
      debugPrint('点击在保护区域内，不取消焦点');
      return false;
    }

    // 如果有焦点，则取消焦点并阻止其他操作
    if (hasFocus) {
      unfocusAll(context);
      debugPrint('已取消焦点，阻止其他操作执行');
      return true; // 返回true表示事件已被处理，阻止其他操作
    }

    return false; // 没有焦点时，允许其他操作继续执行
  }

  // ========== 调试方法 ==========
  /// 打印当前焦点状态
  void printFocusStatus() {
    debugPrint('=== 焦点状态 ===');
    debugPrint('活跃焦点节点数: ${_activeFocusNodes.length}');
    debugPrint('当前有焦点: $hasFocus');
    debugPrint('键盘可见: $_isKeyboardVisible');
    debugPrint('保护区域数: ${_protectedAreas.length}');

    for (int i = 0; i < _activeFocusNodes.length; i++) {
      final node = _activeFocusNodes[i];
      debugPrint(
        '节点$i: ${node.debugLabel ?? 'unnamed'} - 有焦点: ${node.hasFocus}',
      );
    }
  }

  // ========== 清理方法 ==========
  /// 清理所有资源
  @override
  void dispose() {
    _activeFocusNodes.clear();
    _protectedAreas.clear();
    _isKeyboardVisible = false;
    debugPrint('全局焦点管理器已清理');
    super.dispose();
  }
}

/// 全局焦点管理包装器组件
class GlobalFocusWrapper extends StatefulWidget {
  final Widget child;
  final bool enableGlobalUnfocus;

  const GlobalFocusWrapper({
    super.key,
    required this.child,
    this.enableGlobalUnfocus = true,
  });

  @override
  State<GlobalFocusWrapper> createState() => _GlobalFocusWrapperState();
}

class _GlobalFocusWrapperState extends State<GlobalFocusWrapper>
    with WidgetsBindingObserver {
  final GlobalFocusManager _focusManager = GlobalFocusManager.instance;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    // 监听键盘状态变化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _focusManager.updateKeyboardVisibility(context);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enableGlobalUnfocus) {
      return widget.child;
    }

    return _FocusInterceptor(focusManager: _focusManager, child: widget.child);
  }
}

/// 焦点拦截器组件
/// 使用AnimatedBuilder来监听焦点状态变化，动态显示拦截层
class _FocusInterceptor extends StatefulWidget {
  final GlobalFocusManager focusManager;
  final Widget child;

  const _FocusInterceptor({required this.focusManager, required this.child});

  @override
  State<_FocusInterceptor> createState() => _FocusInterceptorState();
}

class _FocusInterceptorState extends State<_FocusInterceptor> {
  @override
  void initState() {
    super.initState();
    // 监听焦点管理器的状态变化
    widget.focusManager.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    widget.focusManager.removeListener(_onFocusChanged);
    super.dispose();
  }

  void _onFocusChanged() {
    // 当焦点状态变化时，重新构建UI
    if (mounted) {
      // 使用 WidgetsBinding.instance.addPostFrameCallback 来延迟执行 setState
      // 避免在组件树锁定时调用 setState
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {});
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topLeft, // 使用非方向性的对齐方式
      children: [
        widget.child,
        // 当有焦点时，显示透明的拦截层
        if (widget.focusManager.hasFocus)
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                widget.focusManager.unfocusAll(context);
                debugPrint('拦截层激活：取消焦点，阻止其他操作');
              },
              behavior: HitTestBehavior.opaque,
              child: Container(color: Colors.transparent),
            ),
          ),
      ],
    );
  }
}
