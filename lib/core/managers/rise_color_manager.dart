/*
  涨跌颜色管理器
  管理涨跌颜色设置，支持"绿涨红跌"和"红涨绿跌"两种模式
*/

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 涨跌颜色模式枚举
enum RiseColorMode {
  greenRiseRedFall('绿涨红跌'), // 传统中国股市：绿色上涨，红色下跌
  redRiseGreenFall('红涨绿跌'); // 国际惯例：红色上涨，绿色下跌

  const RiseColorMode(this.displayName);
  final String displayName;

  /// 从显示名称获取枚举值
  static RiseColorMode fromDisplayName(String displayName) {
    switch (displayName) {
      case '绿涨红跌':
        return RiseColorMode.greenRiseRedFall;
      case '红涨绿跌':
        return RiseColorMode.redRiseGreenFall;
      default:
        return RiseColorMode.greenRiseRedFall; // 默认值
    }
  }
}

/// 涨跌颜色配置
class RiseColorConfig {
  final Color tradeBuyColor; // 买入颜色（对应上涨）
  final Color tradeSellColor; // 卖出颜色（对应下跌）

  const RiseColorConfig({
    required this.tradeBuyColor,
    required this.tradeSellColor,
  });

  /// 绿涨红跌配置（传统中国股市）
  static const RiseColorConfig greenRiseRedFall = RiseColorConfig(
    tradeBuyColor: Color(0xFF02B7D4), // 买入：青色（上涨）
    tradeSellColor: Color(0xFFFC495C), // 卖出：红色（下跌）
  );

  /// 红涨绿跌配置（国际惯例）
  static const RiseColorConfig redRiseGreenFall = RiseColorConfig(
    tradeBuyColor: Color(0xFFFC495C), // 买入：红色（上涨）
    tradeSellColor: Color(0xFF02B7D4), // 卖出：青色（下跌）
  );

  /// 根据模式获取配置
  static RiseColorConfig getConfig(RiseColorMode mode) {
    switch (mode) {
      case RiseColorMode.greenRiseRedFall:
        return greenRiseRedFall;
      case RiseColorMode.redRiseGreenFall:
        return redRiseGreenFall;
    }
  }
}

/// 涨跌颜色管理器
class RiseColorManager extends ChangeNotifier {
  static const String _storageKey = 'rise_color_setting';
  static final RiseColorManager _instance = RiseColorManager._internal();
  static RiseColorManager get instance => _instance;
  RiseColorManager._internal();

  RiseColorMode _currentMode = RiseColorMode.greenRiseRedFall;

  /// 获取当前涨跌颜色模式
  RiseColorMode get currentMode => _currentMode;

  /// 获取当前涨跌颜色配置
  RiseColorConfig get currentConfig => RiseColorConfig.getConfig(_currentMode);

  /// 获取买入颜色（上涨颜色）
  Color get buyColor => currentConfig.tradeBuyColor;

  /// 获取卖出颜色（下跌颜色）
  Color get sellColor => currentConfig.tradeSellColor;

  /// 初始化涨跌颜色设置
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedSetting = prefs.getString(_storageKey);

      if (savedSetting != null) {
        _currentMode = RiseColorMode.fromDisplayName(savedSetting);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('初始化涨跌颜色设置失败: $e');
    }
  }

  /// 设置涨跌颜色模式
  Future<void> setRiseColorMode(RiseColorMode mode) async {
    if (_currentMode == mode) return;

    _currentMode = mode;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_storageKey, mode.displayName);
      notifyListeners();
    } catch (e) {
      debugPrint('保存涨跌颜色设置失败: $e');
    }
  }

  /// 通过显示名称设置涨跌颜色模式
  Future<void> setRiseColorModeByDisplayName(String displayName) async {
    final mode = RiseColorMode.fromDisplayName(displayName);
    await setRiseColorMode(mode);
  }

  /// 切换涨跌颜色模式
  Future<void> toggleRiseColorMode() async {
    final newMode =
        _currentMode == RiseColorMode.greenRiseRedFall
            ? RiseColorMode.redRiseGreenFall
            : RiseColorMode.greenRiseRedFall;
    await setRiseColorMode(newMode);
  }

  /// 获取当前模式的显示名称
  String get currentDisplayName => _currentMode.displayName;

  /// 重置为默认设置
  Future<void> reset() async {
    await setRiseColorMode(RiseColorMode.greenRiseRedFall);
  }
}
