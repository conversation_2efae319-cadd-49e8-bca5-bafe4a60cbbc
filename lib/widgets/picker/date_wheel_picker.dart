/*
 * 日期滚轮选择器组件
 */

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';

/// 日期选择器类型
enum DatePickerType { year, month, day }

/// 日期滚轮选择器配置
class DateWheelPickerConfig {
  /// 选择器高度
  final double height;

  /// 选择器项目高度
  final double itemHeight;

  /// 是否显示边框
  final bool showBorder;

  /// 边框颜色
  final Color? borderColor;

  /// 背景颜色
  final Color? backgroundColor;

  /// 边框圆角
  final double borderRadius;

  /// 选中项文本大小
  final double selectedFontSize;

  /// 未选中项文本大小
  final double unselectedFontSize;

  /// 选中项字重
  final FontWeight selectedFontWeight;

  /// 未选中项字重
  final FontWeight unselectedFontWeight;

  /// 选中项文本颜色
  final Color? selectedTextColor;

  /// 未选中项文本颜色
  final Color? unselectedTextColor;

  /// 放大倍数
  final double magnification;

  /// 透视压缩比例
  final double squeeze;

  /// 滚轮直径比例（影响滚动流畅度）
  final double diameterRatio;

  /// 滚轮偏移比例
  final double offAxisFraction;

  const DateWheelPickerConfig({
    this.height = 100.0,
    this.itemHeight = 40.0,
    this.showBorder = true,
    this.borderColor,
    this.backgroundColor,
    this.borderRadius = 8.0,
    this.selectedFontSize = 16.0,
    this.unselectedFontSize = 14.0,
    this.selectedFontWeight = FontWeight.w500,
    this.unselectedFontWeight = FontWeight.w400,
    this.selectedTextColor,
    this.unselectedTextColor,
    this.magnification = 1.1,
    this.squeeze = 1.2,
    this.diameterRatio = 1.5,
    this.offAxisFraction = 0.0,
  });
}

/// 日期滚轮选择器
class DateWheelPicker extends StatefulWidget {
  /// 当前选中的日期
  final DateTime selectedDate;

  /// 日期变化回调
  final ValueChanged<DateTime> onDateChanged;

  /// 配置参数
  final DateWheelPickerConfig config;

  /// 是否只显示当前年份
  final bool currentYearOnly;

  /// 年份范围（当currentYearOnly为false时使用）
  final int startYear;
  final int endYear;

  const DateWheelPicker({
    super.key,
    required this.selectedDate,
    required this.onDateChanged,
    this.config = const DateWheelPickerConfig(),
    this.currentYearOnly = true,
    this.startYear = 2020,
    this.endYear = 2030,
  });

  @override
  State<DateWheelPicker> createState() => _DateWheelPickerState();
}

class _DateWheelPickerState extends State<DateWheelPicker> {
  late FixedExtentScrollController _yearController;
  late FixedExtentScrollController _monthController;
  late FixedExtentScrollController _dayController;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  @override
  void didUpdateWidget(DateWheelPicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当selectedDate改变时，更新滚动控制器
    if (oldWidget.selectedDate != widget.selectedDate) {
      // 使用 WidgetsBinding.instance.addPostFrameCallback 确保在下一帧更新
      // 这样可以避免在构建过程中调用动画
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _updateControllers();
        }
      });
    }
  }

  @override
  void dispose() {
    _yearController.dispose();
    _monthController.dispose();
    _dayController.dispose();
    super.dispose();
  }

  void _initializeControllers() {
    if (widget.currentYearOnly) {
      _yearController = FixedExtentScrollController(initialItem: 0);
    } else {
      _yearController = FixedExtentScrollController(
        initialItem: widget.selectedDate.year - widget.startYear,
      );
    }

    _monthController = FixedExtentScrollController(
      initialItem: widget.selectedDate.month - 1,
    );

    _dayController = FixedExtentScrollController(
      initialItem: widget.selectedDate.day - 1,
    );
  }

  void _updateControllers() {
    // 使用更流畅的动画参数
    const duration = Duration(milliseconds: 250); // 稍微快一点
    const curve = Curves.fastOutSlowIn; // 更自然的缓动曲线

    // 更新月份控制器
    if (_monthController.hasClients) {
      final monthIndex = widget.selectedDate.month - 1;
      if (monthIndex >= 0 && monthIndex < 12) {
        _monthController.animateToItem(
          monthIndex,
          duration: duration,
          curve: curve,
        );
      }
    }

    // 更新日期控制器
    if (_dayController.hasClients) {
      final year =
          widget.currentYearOnly
              ? DateTime.now().year
              : widget.selectedDate.year;
      final daysInMonth = DateTime(year, widget.selectedDate.month + 1, 0).day;
      final dayIndex = widget.selectedDate.day - 1;

      if (dayIndex >= 0 && dayIndex < daysInMonth) {
        _dayController.animateToItem(
          dayIndex,
          duration: duration,
          curve: curve,
        );
      }
    }

    // 如果不是只显示当前年份，也更新年份控制器
    if (!widget.currentYearOnly && _yearController.hasClients) {
      final yearIndex = widget.selectedDate.year - widget.startYear;
      final yearCount = widget.endYear - widget.startYear + 1;

      if (yearIndex >= 0 && yearIndex < yearCount) {
        _yearController.animateToItem(
          yearIndex,
          duration: duration,
          curve: curve,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: UiConstants.spacing16),
      height: widget.config.height,
      child: Row(
        children: [
          // 年份选择器
          Expanded(child: _buildYearPicker(context)),
          // 月份选择器
          Expanded(child: _buildMonthPicker(context)),
          // 日期选择器
          Expanded(child: _buildDayPicker(context)),
        ],
      ),
    );
  }

  /// 构建年份选择器
  Widget _buildYearPicker(BuildContext context) {
    if (widget.currentYearOnly) {
      // 只显示当前年份
      final currentYear = DateTime.now().year;
      return CupertinoPicker(
        backgroundColor: Colors.transparent,
        selectionOverlay: Container(), // 移除选中状态的背景覆盖层
        scrollController: _yearController,
        itemExtent: widget.config.itemHeight,
        squeeze: widget.config.squeeze,
        useMagnifier: false, // 禁用放大镜，使用自定义字体大小
        magnification: 1.0, // 不使用系统放大
        diameterRatio: widget.config.diameterRatio, // 使用配置的滚轮直径比例
        offAxisFraction: widget.config.offAxisFraction, // 使用配置的滚轮偏移
        onSelectedItemChanged: (index) {
          // 年份固定，不需要处理变化
          _updateDate(
            widget.selectedDate.year,
            widget.selectedDate.month,
            widget.selectedDate.day,
          );
        },
        children: [_buildPickerItem(context, '$currentYear', isSelected: true)],
      );
    } else {
      // 可选择年份范围
      final yearCount = widget.endYear - widget.startYear + 1;
      return CupertinoPicker(
        backgroundColor: Colors.transparent,
        selectionOverlay: Container(), // 移除选中状态的背景覆盖层
        scrollController: _yearController,
        itemExtent: widget.config.itemHeight,
        squeeze: widget.config.squeeze,
        useMagnifier: false, // 禁用放大镜，使用自定义字体大小
        magnification: 1.0, // 不使用系统放大
        diameterRatio: widget.config.diameterRatio, // 使用配置的滚轮直径比例
        offAxisFraction: widget.config.offAxisFraction, // 使用配置的滚轮偏移
        onSelectedItemChanged: (index) {
          final newYear = widget.startYear + index;
          _updateDate(
            newYear,
            widget.selectedDate.month,
            widget.selectedDate.day,
          );
        },
        children: List.generate(yearCount, (index) {
          final year = widget.startYear + index;
          final isSelected = year == widget.selectedDate.year;
          return _buildPickerItem(context, '$year', isSelected: isSelected);
        }),
      );
    }
  }

  /// 构建月份选择器
  Widget _buildMonthPicker(BuildContext context) {
    return CupertinoPicker(
      backgroundColor: Colors.transparent,
      selectionOverlay: Container(), // 移除选中状态的背景覆盖层
      scrollController: _monthController,
      itemExtent: widget.config.itemHeight,
      squeeze: widget.config.squeeze,
      useMagnifier: false, // 禁用放大镜，使用自定义字体大小
      magnification: 1.0, // 不使用系统放大
      diameterRatio: widget.config.diameterRatio, // 使用配置的滚轮直径比例
      offAxisFraction: widget.config.offAxisFraction, // 使用配置的滚轮偏移
      onSelectedItemChanged: (index) {
        final newMonth = index + 1;
        final year =
            widget.currentYearOnly
                ? DateTime.now().year
                : widget.selectedDate.year;
        final daysInMonth = DateTime(year, newMonth + 1, 0).day;
        final newDay =
            widget.selectedDate.day > daysInMonth
                ? daysInMonth
                : widget.selectedDate.day;
        _updateDate(year, newMonth, newDay);
      },
      children: List.generate(12, (index) {
        final month = index + 1;
        final isSelected = month == widget.selectedDate.month;
        return _buildPickerItem(
          context,
          month.toString().padLeft(2, '0'),
          isSelected: isSelected,
        );
      }),
    );
  }

  /// 构建日期选择器
  Widget _buildDayPicker(BuildContext context) {
    final year =
        widget.currentYearOnly ? DateTime.now().year : widget.selectedDate.year;
    final daysInMonth = DateTime(year, widget.selectedDate.month + 1, 0).day;

    return CupertinoPicker(
      backgroundColor: Colors.transparent,
      selectionOverlay: Container(), // 移除选中状态的背景覆盖层
      scrollController: _dayController,
      itemExtent: widget.config.itemHeight,
      squeeze: widget.config.squeeze,
      useMagnifier: false, // 禁用放大镜，使用自定义字体大小
      magnification: 1.0, // 不使用系统放大
      diameterRatio: widget.config.diameterRatio, // 使用配置的滚轮直径比例
      offAxisFraction: widget.config.offAxisFraction, // 使用配置的滚轮偏移
      onSelectedItemChanged: (index) {
        final newDay = index + 1;
        _updateDate(year, widget.selectedDate.month, newDay);
      },
      children: List.generate(daysInMonth, (index) {
        final day = index + 1;
        final isSelected = day == widget.selectedDate.day;
        return _buildPickerItem(
          context,
          day.toString().padLeft(2, '0'),
          isSelected: isSelected,
        );
      }),
    );
  }

  /// 更新日期
  void _updateDate(int year, int month, int day) {
    final newDate = DateTime(year, month, day);
    widget.onDateChanged(newDate);
  }

  /// 构建选择器项目，支持选中状态样式
  Widget _buildPickerItem(
    BuildContext context,
    String text, {
    bool isSelected = false,
  }) {
    return Container(
      height: widget.config.itemHeight,
      alignment: Alignment.center,
      decoration: null, // 确保没有背景装饰
      child: Text(
        text,
        style: context.templateStyle.text.bodyTextMedium.copyWith(
          color:
              isSelected
                  ? (widget.config.selectedTextColor ??
                      context.templateColors.textPrimary)
                  : (widget.config.unselectedTextColor ??
                      context.templateColors.textSecondary),
          fontSize:
              isSelected
                  ? widget.config.selectedFontSize
                  : widget.config.unselectedFontSize,
          fontWeight:
              isSelected
                  ? widget.config.selectedFontWeight
                  : widget.config.unselectedFontWeight,
        ),
      ),
    );
  }
}
