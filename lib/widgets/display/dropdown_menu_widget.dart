/*
*  下拉菜单组件
*  使用 super_tooltip 实现，支持自动遮挡层和边界检测
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:super_tooltip/super_tooltip.dart';
import 'package:qubic_exchange/widgets/index.dart';

class DropdownMenuWidget extends StatefulWidget {
  final Widget? child; // 下拉菜单内容
  final VoidCallback? onTap; // 点击回调
  final Widget? buttonWidget; // 按钮组件
  final TooltipDirection direction; // 弹出方向
  final Color? barrierColor; // 遮挡层颜色
  final bool closeOnBackgroundTap; // 点击背景关闭
  final double? arrowTipDistance; // 箭头距离
  final double? borderRadius; // 圆角半径
  final Color? borderColor; // 边框颜色
  final double? borderWidth; // 边框宽度
  final bool hasShadow; // 是否有阴影
  final bool enableAutoAdjust; // 是否启用自动边界调整
  final EdgeInsets? offset; // 偏移距离
  final Function(VoidCallback closeMenu)? childBuilder; // 子组件构建器，提供关闭菜单的回调

  const DropdownMenuWidget({
    super.key,
    this.child,
    this.onTap,
    this.buttonWidget,
    this.direction = TooltipDirection.up,
    this.barrierColor,
    this.closeOnBackgroundTap = true,
    this.arrowTipDistance = 15.0,
    this.borderRadius = 8.0,
    this.borderColor,
    this.borderWidth = 1.0,
    this.hasShadow = true,
    this.enableAutoAdjust = false,
    this.offset,
    this.childBuilder,
  });

  @override
  State<DropdownMenuWidget> createState() => _DropdownMenuWidgetState();
}

class _DropdownMenuWidgetState extends State<DropdownMenuWidget> {
  late final SuperTooltipController _controller;

  @override
  void initState() {
    super.initState();
    _controller = SuperTooltipController();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// 构建内容，提供关闭菜单的回调
  Widget _buildContent() {
    // 关闭菜单的方法
    void closeMenu() {
      if (_controller.isVisible) {
        _controller.hideTooltip();
      }
    }

    // 如果提供了 childBuilder，使用它
    if (widget.childBuilder != null) {
      return widget.childBuilder!(closeMenu);
    }

    // 否则使用默认的 child
    return widget.child ?? const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    return SuperTooltip(
      controller: _controller,

      // === 强制设置方向 ===
      popupDirection: widget.direction,
      popupDirectionBuilder: () {
        debugPrint('🎯 SuperTooltip方向设置为: ${widget.direction}');
        return widget.direction;
      },

      // === 禁用自动调整 ===
      snapsFarAwayVertically: false,
      snapsFarAwayHorizontally: false,

      // === 简化配置 ===
      showBarrier: true,
      barrierColor: Colors.black.withValues(alpha: 0.3),
      hideTooltipOnBarrierTap: true,
      backgroundColor: Colors.transparent,
      shadowColor: Colors.transparent,
      borderColor: Colors.transparent,
      minimumOutsideMargin: 0,

      // 三角配置
      arrowBaseWidth: 0.0,
      arrowLength: 0.0,

      // === 内容（应用偏移） ===
      content:
          widget.offset != null
              ? Container(
                margin: widget.offset,
                padding: EdgeInsets.all(UiConstants.spacing10),
                decoration: BoxDecoration(
                  color: context.templateColors.popupBackground,
                  borderRadius: BorderRadius.circular(
                    context.templateStyles.borderRadiusMedium,
                  ),
                ),
                child: _buildContent(),
              )
              : _buildContent(),

      // === 触发器 ===
      child: InkWellWidget(
        onTap: () {
          // 调用外部回调
          widget.onTap?.call();

          // 切换tooltip显示状态
          try {
            if (_controller.isVisible) {
              _controller.hideTooltip();
            } else {
              _controller.showTooltip();
            }
          } catch (e) {
            debugPrint('DropdownMenuWidget: 切换tooltip状态时出错: $e');
          }
        },
        child: widget.buttonWidget ?? const SizedBox.shrink(),
      ),
    );
  }
}
