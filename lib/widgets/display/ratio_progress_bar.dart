/*
  === 比例进度条组件 ===

  功能：
  - 支持看多/看空比例显示
  - 支持横向布局（文本在条形图两侧）和纵向布局（文本在条形图下方）
  - 支持自定义颜色、样式、间距等
  - 支持斜角分割效果

  使用示例：

  // 横向布局 - 文本在条形图两侧（适合紧凑布局）
  RatioProgressBar(
    bullishRatio: 65.5,
    bearishRatio: 34.5,
    layout: RatioBarLayout.horizontal,
    height: 6.0,
    horizontalSpacing: 12.0, // 条形图与文本的间距
    showPercentage: true,
  )

  // 纵向布局 - 文本在条形图下方（适合宽松布局）
  RatioProgressBar(
    bullishRatio: 65.5,
    bearishRatio: 34.5,
    layout: RatioBarLayout.vertical,
    labelAlignment: MainAxisAlignment.spaceBetween, // 标签对齐方式
    height: 4.0,
  )

  // 自定义文本标签
  RatioProgressBar(
    bullishRatio: 75.0,
    bearishRatio: 25.0,
    bullishText: '买入', // 自定义看多文本
    bearishText: '卖出', // 自定义看空文本
    layout: RatioBarLayout.horizontal,
  )

  // 控制小数点显示
  RatioProgressBar(
    bullishRatio: 65.5,
    bearishRatio: 34.5,
    showDecimal: false, // 不显示小数点，显示整数百分比
    showPercentage: true,
  )
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';

// 排列方式（横向 / 纵向）
enum RatioBarLayout {
  horizontal, // 文本在条形图两侧
  vertical, // 文本在条形图下方
}

class RatioProgressBar extends StatefulWidget {
  final double bullishRatio; // 看多比例（0-100）
  final double bearishRatio; // 看空比例（0-100）
  final double height; // 条形图高度
  final double borderRadius; // 圆角
  final bool showLabels; // 是否显示标签
  final bool showPercentage; // 是否显示百分比
  final Color? bullishColor; // 看多颜色
  final Color? bearishColor; // 看空颜色
  final TextStyle? labelStyle; // 标签样式
  final EdgeInsets? margin; // 外边距
  final RatioBarLayout layout; // 排列方式
  final double horizontalSpacing; // 横向布局时条形图与文本的间距
  final MainAxisAlignment labelAlignment; // 标签对齐方式（仅纵向布局有效）
  final String? bullishText; // 看多文本（可选，默认为"看多"）
  final String? bearishText; // 看空文本（可选，默认为"看空"）
  final bool showDecimal; // 是否显示小数点（默认显示2位小数）

  const RatioProgressBar({
    super.key,
    required this.bullishRatio,
    required this.bearishRatio,
    this.height = 3.0,
    this.borderRadius = 3.0,
    this.showLabels = true,
    this.showPercentage = true,
    this.bullishColor,
    this.bearishColor,
    this.labelStyle,
    this.margin,
    this.layout = RatioBarLayout.vertical,
    this.horizontalSpacing = 8.0,
    this.labelAlignment = MainAxisAlignment.spaceBetween,
    this.bullishText,
    this.bearishText,
    this.showDecimal = true,
  });

  @override
  State<RatioProgressBar> createState() => _RatioProgressBarState();
}

class _RatioProgressBarState extends State<RatioProgressBar> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin,
      child:
          widget.layout == RatioBarLayout.vertical
              ? _buildVerticalLayout()
              : _buildHorizontalLayout(),
    );
  }

  Widget _buildVerticalLayout() {
    return Column(
      children: [
        _buildProgressBar(),
        if (widget.showLabels) ...[
          SizedBox(height: UiConstants.spacing8),
          _buildLabels(),
        ],
      ],
    );
  }

  Widget _buildHorizontalLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 左侧看多标签
        if (widget.showLabels) _buildBullishLabel(),

        // 条形图区域
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: widget.horizontalSpacing),
            child: _buildProgressBar(),
          ),
        ),

        // 右侧看空标签
        if (widget.showLabels) _buildBearishLabel(),
      ],
    );
  }

  Widget _buildProgressBar() {
    final bullishColor = widget.bullishColor ?? context.templateColors.tradeBuy;
    final bearishColor =
        widget.bearishColor ?? context.templateColors.tradeSell;

    final totalRatio = widget.bullishRatio + widget.bearishRatio;
    final normalizedBullishRatio =
        totalRatio > 0 ? widget.bullishRatio / totalRatio : 0.5;
    final normalizedBearishRatio =
        totalRatio > 0 ? widget.bearishRatio / totalRatio : 0.5;

    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(widget.borderRadius),
        color: context.templateColors.surface,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(widget.borderRadius),
        child: CustomPaint(
          size: Size.infinite,
          painter: _SlantedRatioBarPainter(
            bullishColor: bullishColor,
            bearishColor: bearishColor,
            bullishRatio: normalizedBullishRatio,
            bearishRatio: normalizedBearishRatio,
            borderRadius: widget.borderRadius,
          ),
        ),
      ),
    );
  }

  Widget _buildLabels() {
    return Row(
      mainAxisAlignment: widget.labelAlignment,
      children: [_buildBullishLabel(), _buildBearishLabel()],
    );
  }

  Widget _buildBullishLabel() {
    final color = widget.bullishColor ?? context.templateColors.tradeBuy;
    final style =
        widget.labelStyle ?? context.templateStyle.text.hintTextMedium;
    final text = widget.bullishText ?? '看多'; // 使用传入的文本或默认文本

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(text, style: style.copyWith(color: color)),
        if (widget.showPercentage) ...[
          SizedBox(width: UiConstants.spacing4),
          Text(
            widget.showDecimal
                ? '${widget.bullishRatio.toStringAsFixed(2)}%'
                : '${widget.bullishRatio.round()}%',
            style: style.copyWith(color: color),
          ),
        ],
      ],
    );
  }

  Widget _buildBearishLabel() {
    final color = widget.bearishColor ?? context.templateColors.tradeSell;
    final style =
        widget.labelStyle ?? context.templateStyle.text.hintTextMedium;
    final text = widget.bearishText ?? '看空'; // 使用传入的文本或默认文本

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(text, style: style.copyWith(color: color)),
        if (widget.showPercentage) ...[
          SizedBox(width: UiConstants.spacing4),
          Text(
            widget.showDecimal
                ? '${widget.bearishRatio.toStringAsFixed(2)}%'
                : '${widget.bearishRatio.round()}%',
            style: style.copyWith(color: color),
          ),
        ],
      ],
    );
  }
}

class _SlantedRatioBarPainter extends CustomPainter {
  final Color bullishColor;
  final Color bearishColor;
  final double bullishRatio;
  final double bearishRatio;
  final double borderRadius;

  _SlantedRatioBarPainter({
    required this.bullishColor,
    required this.bearishColor,
    required this.bullishRatio,
    required this.bearishRatio,
    required this.borderRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (size.width <= 0 || size.height <= 0) return;

    final paint = Paint()..style = PaintingStyle.fill;
    final dividerX = size.width * bullishRatio;
    final slantWidth = size.height * 0.5;
    final gapWidth = 1.0;

    if (bullishRatio > 0) {
      paint.color = bullishColor;
      final path = Path();
      if (bearishRatio > 0) {
        path.moveTo(0, 0);
        path.lineTo(dividerX - slantWidth - gapWidth, 0);
        path.lineTo(dividerX + slantWidth - gapWidth, size.height);
        path.lineTo(0, size.height);
        path.close();
      } else {
        path.addRRect(
          RRect.fromRectAndRadius(
            Rect.fromLTWH(0, 0, size.width, size.height),
            Radius.circular(borderRadius),
          ),
        );
      }
      canvas.drawPath(path, paint);
    }

    if (bearishRatio > 0) {
      paint.color = bearishColor;
      final path = Path();
      if (bullishRatio > 0) {
        path.moveTo(dividerX - slantWidth + gapWidth, 0);
        path.lineTo(size.width, 0);
        path.lineTo(size.width, size.height);
        path.lineTo(dividerX + slantWidth + gapWidth, size.height);
        path.close();
      } else {
        path.addRRect(
          RRect.fromRectAndRadius(
            Rect.fromLTWH(0, 0, size.width, size.height),
            Radius.circular(borderRadius),
          ),
        );
      }
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is! _SlantedRatioBarPainter) return true;
    return oldDelegate.bullishColor != bullishColor ||
        oldDelegate.bearishColor != bearishColor ||
        oldDelegate.bullishRatio != bullishRatio ||
        oldDelegate.bearishRatio != bearishRatio ||
        oldDelegate.borderRadius != borderRadius;
  }
}
