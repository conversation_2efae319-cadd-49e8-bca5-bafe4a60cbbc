/*
*  公用滑块组件
*  基于 Syncfusion Flutter Sliders 封装的通用滑块组件
*  支持单值滑块、范围滑块和范围选择器
*/

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:intl/intl.dart';

/// 滑块类型枚举
enum SliderType {
  single, // 单值滑块
  range, // 范围滑块
  selector, // 范围选择器
}

/// 滑块方向枚举
enum SliderOrientation {
  horizontal, // 水平
  vertical, // 垂直
}

/// 标签位置枚举
enum SliderLabelPlacement {
  onTicks, // 在刻度上
  betweenTicks, // 在刻度之间
}

/// 公用滑块组件
class CommonSlider extends StatefulWidget {
  /// 滑块类型
  final SliderType type;

  /// 滑块方向
  final SliderOrientation orientation;

  /// 最小值
  final double min;

  /// 最大值
  final double max;

  /// 当前值（单值滑块）
  final double? value;

  /// 当前范围值（范围滑块）
  final SfRangeValues? values;

  /// 初始值（单值滑块）
  final double? initialValue;

  /// 初始范围值（范围滑块）
  final SfRangeValues? initialValues;

  /// 间隔
  final double? interval;

  /// 小刻度间隔数
  final int minorTicksPerInterval;

  /// 是否显示标签
  final bool showLabels;

  /// 是否显示刻度
  final bool showTicks;

  /// 是否显示分割点
  final bool showDividers;

  /// 是否启用工具提示
  final bool enableTooltip;

  /// 是否启用触觉反馈
  final bool enableHapticFeedback;

  /// 标签位置
  final SliderLabelPlacement labelPlacement;

  /// 数字格式化
  final String? numberFormat;

  /// 标签格式化回调
  final String Function(dynamic actualValue, String formattedText)?
  labelFormatterCallback;

  /// 工具提示格式化回调
  final String Function(dynamic actualValue, String formattedText)?
  tooltipFormatterCallback;

  /// 单值变化回调
  final ValueChanged<double>? onChanged;

  /// 单值变化结束回调
  final ValueChanged<double>? onChangeEnd;

  /// 范围值变化回调
  final ValueChanged<SfRangeValues>? onRangeChanged;

  /// 范围值变化结束回调
  final ValueChanged<SfRangeValues>? onRangeChangeEnd;

  /// 自定义子组件（范围选择器）
  final Widget? child;

  /// 滑块高度（垂直滑块的宽度）
  final double? sliderHeight;

  /// 容器内边距
  final EdgeInsets? padding;

  /// 容器外边距
  final EdgeInsets? margin;

  /// 自定义主题数据
  final SfSliderThemeData? themeData;

  const CommonSlider({
    super.key,
    this.type = SliderType.single,
    this.orientation = SliderOrientation.horizontal,
    this.min = 0.0,
    this.max = 100.0,
    this.value,
    this.values,
    this.initialValue,
    this.initialValues,
    this.interval,
    this.minorTicksPerInterval = 0,
    this.showLabels = false,
    this.showTicks = false,
    this.showDividers = false,
    this.enableTooltip = false,
    this.enableHapticFeedback = true,
    this.labelPlacement = SliderLabelPlacement.onTicks,
    this.numberFormat,
    this.labelFormatterCallback,
    this.tooltipFormatterCallback,
    this.onChanged,
    this.onChangeEnd,
    this.onRangeChanged,
    this.onRangeChangeEnd,
    this.child,
    this.sliderHeight,
    this.padding,
    this.margin,
    this.themeData,
  });

  /// 创建单值滑块
  factory CommonSlider.single({
    Key? key,
    SliderOrientation orientation = SliderOrientation.horizontal,
    required double min,
    required double max,
    double? value,
    double? initialValue,
    double? interval,
    int minorTicksPerInterval = 0,
    bool showLabels = false,
    bool showTicks = false,
    bool showDividers = false,
    bool enableTooltip = false,
    bool enableHapticFeedback = true,
    SliderLabelPlacement labelPlacement = SliderLabelPlacement.onTicks,
    String? numberFormat,
    String Function(dynamic, String)? labelFormatterCallback,
    String Function(dynamic, String)? tooltipFormatterCallback,
    ValueChanged<double>? onChanged,
    ValueChanged<double>? onChangeEnd,
    double? sliderHeight,
    EdgeInsets? padding,
    EdgeInsets? margin,
    SfSliderThemeData? themeData,
  }) {
    return CommonSlider(
      key: key,
      type: SliderType.single,
      orientation: orientation,
      min: min,
      max: max,
      value: value,
      initialValue: initialValue,
      interval: interval,
      minorTicksPerInterval: minorTicksPerInterval,
      showLabels: showLabels,
      showTicks: showTicks,
      showDividers: showDividers,
      enableTooltip: enableTooltip,
      enableHapticFeedback: enableHapticFeedback,
      labelPlacement: labelPlacement,
      numberFormat: numberFormat,
      labelFormatterCallback: labelFormatterCallback,
      tooltipFormatterCallback: tooltipFormatterCallback,
      onChanged: onChanged,
      onChangeEnd: onChangeEnd,
      sliderHeight: sliderHeight,
      padding: padding,
      margin: margin,
      themeData: themeData,
    );
  }

  /// 创建范围滑块
  factory CommonSlider.range({
    Key? key,
    SliderOrientation orientation = SliderOrientation.horizontal,
    required double min,
    required double max,
    SfRangeValues? values,
    SfRangeValues? initialValues,
    double? interval,
    int minorTicksPerInterval = 0,
    bool showLabels = false,
    bool showTicks = false,
    bool showDividers = false,
    bool enableTooltip = false,
    bool enableHapticFeedback = true,
    SliderLabelPlacement labelPlacement = SliderLabelPlacement.onTicks,
    String? numberFormat,
    String Function(dynamic, String)? labelFormatterCallback,
    String Function(dynamic, String)? tooltipFormatterCallback,
    ValueChanged<SfRangeValues>? onChanged,
    ValueChanged<SfRangeValues>? onChangeEnd,
    double? sliderHeight,
    EdgeInsets? padding,
    EdgeInsets? margin,
    SfSliderThemeData? themeData,
  }) {
    return CommonSlider(
      key: key,
      type: SliderType.range,
      orientation: orientation,
      min: min,
      max: max,
      values: values,
      initialValues: initialValues,
      interval: interval,
      minorTicksPerInterval: minorTicksPerInterval,
      showLabels: showLabels,
      showTicks: showTicks,
      showDividers: showDividers,
      enableTooltip: enableTooltip,
      enableHapticFeedback: enableHapticFeedback,
      labelPlacement: labelPlacement,
      numberFormat: numberFormat,
      labelFormatterCallback: labelFormatterCallback,
      tooltipFormatterCallback: tooltipFormatterCallback,
      onRangeChanged: onChanged,
      onRangeChangeEnd: onChangeEnd,
      sliderHeight: sliderHeight,
      padding: padding,
      margin: margin,
      themeData: themeData,
    );
  }

  /// 创建范围选择器
  factory CommonSlider.selector({
    Key? key,
    required double min,
    required double max,
    SfRangeValues? initialValues,
    double? interval,
    int minorTicksPerInterval = 0,
    bool showLabels = false,
    bool showTicks = false,
    bool showDividers = false,
    bool enableTooltip = false,
    bool enableHapticFeedback = true,
    SliderLabelPlacement labelPlacement = SliderLabelPlacement.onTicks,
    String? numberFormat,
    String Function(dynamic, String)? labelFormatterCallback,
    String Function(dynamic, String)? tooltipFormatterCallback,
    ValueChanged<SfRangeValues>? onChanged,
    ValueChanged<SfRangeValues>? onChangeEnd,
    required Widget child,
    double? sliderHeight,
    EdgeInsets? padding,
    EdgeInsets? margin,
    SfSliderThemeData? themeData,
  }) {
    return CommonSlider(
      key: key,
      type: SliderType.selector,
      orientation: SliderOrientation.horizontal, // 范围选择器只支持水平方向
      min: min,
      max: max,
      initialValues: initialValues,
      interval: interval,
      minorTicksPerInterval: minorTicksPerInterval,
      showLabels: showLabels,
      showTicks: showTicks,
      showDividers: showDividers,
      enableTooltip: enableTooltip,
      enableHapticFeedback: enableHapticFeedback,
      labelPlacement: labelPlacement,
      numberFormat: numberFormat,
      labelFormatterCallback: labelFormatterCallback,
      tooltipFormatterCallback: tooltipFormatterCallback,
      onRangeChanged: onChanged,
      onRangeChangeEnd: onChangeEnd,
      sliderHeight: sliderHeight,
      padding: padding,
      margin: margin,
      themeData: themeData,
      child: child,
    );
  }

  @override
  State<CommonSlider> createState() => _CommonSliderState();
}

class _CommonSliderState extends State<CommonSlider> {
  late double _currentValue;
  late SfRangeValues _currentValues;

  @override
  void initState() {
    super.initState();
    _initializeValues();
  }

  @override
  void didUpdateWidget(CommonSlider oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.value != oldWidget.value ||
        widget.values != oldWidget.values ||
        widget.initialValue != oldWidget.initialValue ||
        widget.initialValues != oldWidget.initialValues) {
      _initializeValues();
    }
  }

  /// 初始化滑块值
  void _initializeValues() {
    if (widget.type == SliderType.single) {
      _currentValue = widget.value ?? widget.initialValue ?? widget.min;
    } else {
      _currentValues =
          widget.values ??
          widget.initialValues ??
          SfRangeValues(widget.min, widget.max);
    }
  }

  /// 触发触觉反馈
  void _triggerHapticFeedback() {
    if (widget.enableHapticFeedback) {
      HapticFeedback.lightImpact();
    }
  }

  /// 获取默认主题数据
  SfSliderThemeData _getDefaultThemeData() {
    return SfSliderThemeData(
      // 轨道样式 - 白色轨道
      activeTrackHeight: 4.0,
      inactiveTrackHeight: 4.0,
      trackCornerRadius: 2.0,
      activeTrackColor: Colors.white,
      inactiveTrackColor: Colors.white.withValues(alpha: 0.6),

      // 节点样式 - 白色节点，无边框
      thumbRadius: 12.0,
      thumbColor: Colors.white,
      thumbStrokeWidth: 0.0,
      thumbStrokeColor: Colors.transparent,

      // 覆盖层 - 透明
      overlayRadius: 0.0,
      overlayColor: Colors.transparent,

      // 分割点样式 - 灰色圆点
      activeDividerColor: Colors.grey.shade600,
      inactiveDividerColor: Colors.grey.shade600,
      activeDividerRadius: 4.0,
      inactiveDividerRadius: 4.0,

      // 标签样式 - 白色文字
      activeLabelStyle: context.templateStyle.text.bodySmall.copyWith(
        color: Colors.white,
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
      inactiveLabelStyle: context.templateStyle.text.bodySmall.copyWith(
        color: Colors.white.withValues(alpha: 0.7),
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),

      // 工具提示样式
      tooltipBackgroundColor: Colors.black.withValues(alpha: 0.8),
      tooltipTextStyle: context.templateStyle.text.bodySmall.copyWith(
        color: Colors.white,
      ),

      // 刻度样式 - 隐藏刻度
      activeTickColor: Colors.transparent,
      inactiveTickColor: Colors.transparent,
      activeMinorTickColor: Colors.transparent,
      inactiveMinorTickColor: Colors.transparent,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding:
          widget.padding ??
          EdgeInsets.symmetric(
            vertical: UiConstants.spacing8,
            horizontal: UiConstants.spacing16,
          ),
      margin: widget.margin,
      child: _buildSlider(),
    );
  }

  /// 构建滑块组件
  Widget _buildSlider() {
    final themeData = widget.themeData ?? _getDefaultThemeData();

    return SfSliderTheme(data: themeData, child: _buildSliderByType());
  }

  /// 根据类型构建滑块
  Widget _buildSliderByType() {
    switch (widget.type) {
      case SliderType.single:
        return _buildSingleSlider();
      case SliderType.range:
        return _buildRangeSlider();
      case SliderType.selector:
        return _buildRangeSelector();
    }
  }

  /// 构建单值滑块
  Widget _buildSingleSlider() {
    if (widget.orientation == SliderOrientation.vertical) {
      return SizedBox(
        height: widget.sliderHeight ?? 200,
        child: SfSlider.vertical(
          min: widget.min,
          max: widget.max,
          value: _currentValue,
          interval: widget.interval,
          minorTicksPerInterval: widget.minorTicksPerInterval,
          showLabels: widget.showLabels,
          showTicks: widget.showTicks,
          showDividers: widget.showDividers,
          enableTooltip: widget.enableTooltip,
          labelPlacement:
              widget.labelPlacement == SliderLabelPlacement.onTicks
                  ? LabelPlacement.onTicks
                  : LabelPlacement.betweenTicks,
          numberFormat:
              widget.numberFormat != null
                  ? NumberFormat(widget.numberFormat!)
                  : null,
          labelFormatterCallback: widget.labelFormatterCallback,
          tooltipTextFormatterCallback: widget.tooltipFormatterCallback,
          onChanged: (dynamic value) {
            _triggerHapticFeedback();
            setState(() {
              _currentValue = value;
            });
            widget.onChanged?.call(value);
          },
          onChangeEnd: (dynamic value) {
            widget.onChangeEnd?.call(value);
          },
        ),
      );
    } else {
      return SfSlider(
        min: widget.min,
        max: widget.max,
        value: _currentValue,
        interval: widget.interval,
        minorTicksPerInterval: widget.minorTicksPerInterval,
        showLabels: widget.showLabels,
        showTicks: widget.showTicks,
        showDividers: widget.showDividers,
        enableTooltip: widget.enableTooltip,
        labelPlacement:
            widget.labelPlacement == SliderLabelPlacement.onTicks
                ? LabelPlacement.onTicks
                : LabelPlacement.betweenTicks,
        numberFormat:
            widget.numberFormat != null
                ? NumberFormat(widget.numberFormat!)
                : null,
        labelFormatterCallback: widget.labelFormatterCallback,
        tooltipTextFormatterCallback: widget.tooltipFormatterCallback,
        onChanged: (dynamic value) {
          _triggerHapticFeedback();
          setState(() {
            _currentValue = value;
          });
          widget.onChanged?.call(value);
        },
        onChangeEnd: (dynamic value) {
          widget.onChangeEnd?.call(value);
        },
      );
    }
  }

  /// 构建范围滑块
  Widget _buildRangeSlider() {
    if (widget.orientation == SliderOrientation.vertical) {
      return SizedBox(
        height: widget.sliderHeight ?? 200,
        child: SfRangeSlider.vertical(
          min: widget.min,
          max: widget.max,
          values: _currentValues,
          interval: widget.interval,
          minorTicksPerInterval: widget.minorTicksPerInterval,
          showLabels: widget.showLabels,
          showTicks: widget.showTicks,
          showDividers: widget.showDividers,
          enableTooltip: widget.enableTooltip,
          labelPlacement:
              widget.labelPlacement == SliderLabelPlacement.onTicks
                  ? LabelPlacement.onTicks
                  : LabelPlacement.betweenTicks,
          numberFormat:
              widget.numberFormat != null
                  ? NumberFormat(widget.numberFormat!)
                  : null,
          labelFormatterCallback: widget.labelFormatterCallback,
          tooltipTextFormatterCallback: widget.tooltipFormatterCallback,
          onChanged: (SfRangeValues values) {
            _triggerHapticFeedback();
            setState(() {
              _currentValues = values;
            });
            widget.onRangeChanged?.call(values);
          },
          onChangeEnd: (SfRangeValues values) {
            widget.onRangeChangeEnd?.call(values);
          },
        ),
      );
    } else {
      return SfRangeSlider(
        min: widget.min,
        max: widget.max,
        values: _currentValues,
        interval: widget.interval,
        minorTicksPerInterval: widget.minorTicksPerInterval,
        showLabels: widget.showLabels,
        showTicks: widget.showTicks,
        showDividers: widget.showDividers,
        enableTooltip: widget.enableTooltip,
        labelPlacement:
            widget.labelPlacement == SliderLabelPlacement.onTicks
                ? LabelPlacement.onTicks
                : LabelPlacement.betweenTicks,
        numberFormat:
            widget.numberFormat != null
                ? NumberFormat(widget.numberFormat!)
                : null,
        labelFormatterCallback: widget.labelFormatterCallback,
        tooltipTextFormatterCallback: widget.tooltipFormatterCallback,
        onChanged: (SfRangeValues values) {
          _triggerHapticFeedback();
          setState(() {
            _currentValues = values;
          });
          widget.onRangeChanged?.call(values);
        },
        onChangeEnd: (SfRangeValues values) {
          widget.onRangeChangeEnd?.call(values);
        },
      );
    }
  }

  /// 构建范围选择器
  Widget _buildRangeSelector() {
    return SizedBox(
      height: widget.sliderHeight ?? 200,
      child: SfRangeSelector(
        min: widget.min,
        max: widget.max,
        initialValues: _currentValues,
        interval: widget.interval,
        minorTicksPerInterval: widget.minorTicksPerInterval,
        showLabels: widget.showLabels,
        showTicks: widget.showTicks,
        showDividers: widget.showDividers,
        enableTooltip: widget.enableTooltip,
        labelPlacement:
            widget.labelPlacement == SliderLabelPlacement.onTicks
                ? LabelPlacement.onTicks
                : LabelPlacement.betweenTicks,
        numberFormat:
            widget.numberFormat != null
                ? NumberFormat(widget.numberFormat!)
                : null,
        labelFormatterCallback: widget.labelFormatterCallback,
        tooltipTextFormatterCallback: widget.tooltipFormatterCallback,
        onChanged: (SfRangeValues values) {
          _triggerHapticFeedback();
          setState(() {
            _currentValues = values;
          });
          widget.onRangeChanged?.call(values);
        },
        onChangeEnd: (SfRangeValues values) {
          widget.onRangeChangeEnd?.call(values);
        },
        child:
            widget.child ??
            Container(
              height: 100,
              decoration: BoxDecoration(
                color: context.templateColors.cardBackground,
                borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
              ),
              child: Center(
                child: Text(
                  '范围选择器内容区域',
                  style: context.templateStyle.text.bodyText.copyWith(
                    color: context.templateColors.textSecondary,
                  ),
                ),
              ),
            ),
      ),
    );
  }
}
