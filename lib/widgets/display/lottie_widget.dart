import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:shimmer/shimmer.dart';
import '../../core/index.dart';

// Lottie动画组件
class LottieWidget extends StatefulWidget {
  final String path;
  final AnimationController? controller;
  final double? width;
  final double? height;
  final bool repeat;
  final bool animate;
  final bool followTheme;
  final BoxFit fit;
  final Widget? fallback;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final bool showSkeleton;
  final TemplateType? template;

  const LottieWidget({
    super.key,
    required this.path,
    this.controller,
    this.width,
    this.height,
    this.repeat = false,
    this.animate = true,
    this.followTheme = true,
    this.fit = BoxFit.contain,
    this.fallback,
    this.margin,
    this.padding,
    this.showSkeleton = false,
    this.template,
  });

  @override
  State<LottieWidget> createState() => _LottieWidgetState();
}

class _LottieWidgetState extends State<LottieWidget> {
  // 使用新的主题系统

  @override
  Widget build(BuildContext context) {
    if (widget.showSkeleton) {
      return _buildSkeleton();
    }

    // 使用 TemplateResourceManager 生成完整的资源路径
    String assetPath = _generateAssetPath();

    Widget lottieWidget = Lottie.asset(
      assetPath,
      controller: widget.controller,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      repeat: widget.repeat,
      animate: widget.animate,
      errorBuilder: (context, error, stackTrace) {
        return widget.fallback ?? _buildSkeleton();
      },
    );

    if (widget.margin != null || widget.padding != null) {
      lottieWidget = Container(
        margin: widget.margin,
        padding: widget.padding,
        child: lottieWidget,
      );
    }

    return lottieWidget;
  }

  /// 生成资源路径（与 ImageWidget 和 ThemedImage 保持一致）
  String _generateAssetPath() {
    final templateType = widget.template ?? TemplateType.base;
    String resourceName = widget.path;

    // 如果启用主题跟随，根据当前主题选择文件
    if (widget.followTheme) {
      final isDark = Theme.of(context).brightness == Brightness.dark;
      if (isDark) {
        // 深色模式使用默认文件名
        resourceName = widget.path;
      } else {
        // 浅色模式使用 _light 后缀
        resourceName = '${widget.path}_light';
      }
    }

    final finalPath = TemplateResourceManager.instance.getResourcePath(
      resourceName: resourceName,
      type: ResourceType.lottie,
      template: templateType,
      followTheme: false, // 我们已经手动处理了主题切换
      fileExtension: '.json',
    );

    debugPrint('🎬 LottieWidget 路径生成: ${widget.path} -> $finalPath');
    return finalPath;
  }

  // 构建骨架屏效果
  Widget _buildSkeleton() {
    return Shimmer.fromColors(
      baseColor: context.templateColors.skeletonBase,
      highlightColor: context.templateColors.skeletonHighlight,
      child: Container(
        width: widget.width,
        height: widget.height,
        margin: widget.margin,
        padding: widget.padding,
        decoration: BoxDecoration(color: context.templateColors.skeletonBase),
      ),
    );
  }
}
