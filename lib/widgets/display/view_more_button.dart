/*

  查看更多按钮
*/

import 'package:flutter/material.dart';
import 'package:remixicon/remixicon.dart';
import 'package:qubic_exchange/core/index.dart';

class ViewMoreButton extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final bool showArrow;
  final Color? color;
  final Color? iconColor;
  final VoidCallback? onPressed;

  const ViewMoreButton({
    super.key,
    this.text = '',
    this.style,
    this.showArrow = true,
    this.onPressed,
    this.color,
    this.iconColor,
  });

  @override
  State<ViewMoreButton> createState() => _ViewMoreButtonState();
}

class _ViewMoreButtonState extends State<ViewMoreButton> {
  @override
  Widget build(BuildContext context) {
    final textStyle =
        widget.style ??
        context.templateStyle.text.bodyTextMedium.copyWith(color: widget.color);

    return TextButton(
      onPressed: widget.onPressed,
      style: TextButton.styleFrom(
        padding: EdgeInsets.zero,
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        splashFactory: NoSplash.splashFactory,
        overlayColor: Colors.transparent,
      ),
      child: Row(
        children: [
          if (widget.text.isNotEmpty) ...[
            Text(widget.text, style: textStyle),
            SizedBox(width: UiConstants.spacing4),
          ],
          if (widget.showArrow)
            Icon(
              RemixIcons.arrow_right_line,
              size: UiConstants.fontSize16,
              color: widget.iconColor ?? context.templateColors.textPrimary,
            ),
        ],
      ),
    );
  }
}
