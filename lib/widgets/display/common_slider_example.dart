/*
* CommonSlider 使用示例
* 展示如何使用 CommonSlider 实现各种滑块效果
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/display/common_slider.dart';

/// CommonSlider 使用示例页面
class CommonSliderExample extends StatefulWidget {
  const CommonSliderExample({super.key});

  @override
  State<CommonSliderExample> createState() => _CommonSliderExampleState();
}

class _CommonSliderExampleState extends State<CommonSliderExample> {
  double _singleValue = 25.0;
  double _percentageValue = 50.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('CommonSlider 示例'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: EdgeInsets.all(UiConstants.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('标准百分比滑块'),
            _buildPercentageSlider(),

            SizedBox(height: UiConstants.spacing32),

            _buildSectionTitle('基础单值滑块'),
            _buildBasicSlider(),

            SizedBox(height: UiConstants.spacing32),

            _buildSectionTitle('带工具提示的滑块'),
            _buildTooltipSlider(),
          ],
        ),
      ),
    );
  }

  /// 构建章节标题
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: UiConstants.spacing16),
      child: Text(
        title,
        style: context.templateStyle.text.h4.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// 构建百分比滑块（如截图所示的样式）
  Widget _buildPercentageSlider() {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing16),
      decoration: BoxDecoration(
        color: Colors.grey.shade900,
        borderRadius: BorderRadius.circular(UiConstants.borderRadius12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '当前值: ${_percentageValue.toInt()}%',
            style: context.templateStyle.text.bodyText.copyWith(
              color: Colors.white,
            ),
          ),
          SizedBox(height: UiConstants.spacing16),
          CommonSlider.single(
            min: 0.0,
            max: 100.0,
            value: _percentageValue,
            interval: 25.0,
            showLabels: true,
            showDividers: true,
            enableTooltip: false,
            labelFormatterCallback: (value, text) => '${value.toInt()}%',
            onChanged: (value) {
              setState(() {
                _percentageValue = value;
              });
            },
          ),
        ],
      ),
    );
  }

  /// 构建基础滑块
  Widget _buildBasicSlider() {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing16),
      decoration: BoxDecoration(
        color: Colors.grey.shade900,
        borderRadius: BorderRadius.circular(UiConstants.borderRadius12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '当前值: ${_singleValue.toStringAsFixed(1)}',
            style: context.templateStyle.text.bodyText.copyWith(
              color: Colors.white,
            ),
          ),
          SizedBox(height: UiConstants.spacing16),
          CommonSlider.single(
            min: 0.0,
            max: 100.0,
            value: _singleValue,
            interval: 20.0,
            showLabels: true,
            showDividers: true,
            enableTooltip: false,
            onChanged: (value) {
              setState(() {
                _singleValue = value;
              });
            },
          ),
        ],
      ),
    );
  }

  /// 构建带工具提示的滑块
  Widget _buildTooltipSlider() {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing16),
      decoration: BoxDecoration(
        color: Colors.grey.shade900,
        borderRadius: BorderRadius.circular(UiConstants.borderRadius12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '拖拽节点查看工具提示',
            style: context.templateStyle.text.bodyText.copyWith(
              color: Colors.white,
            ),
          ),
          SizedBox(height: UiConstants.spacing16),
          CommonSlider.single(
            min: 0.0,
            max: 1000.0,
            value: 500.0,
            interval: 200.0,
            showLabels: true,
            showDividers: true,
            enableTooltip: true,
            labelFormatterCallback: (value, text) => '${value.toInt()}',
            tooltipFormatterCallback: (value, text) => '${value.toInt()} USDT',
            onChanged: (value) {
              // 处理值变化
            },
          ),
        ],
      ),
    );
  }
}
