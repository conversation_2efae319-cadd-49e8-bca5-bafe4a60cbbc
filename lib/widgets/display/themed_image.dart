/*
*  主题感知图片组件
      - 支持根据模板和主题切换图片
      - 支持网络图片加载
      - 支持占位符和错误处理
      - 支持缓存和加载状态

使用示例：
ThemedImage.asset('icon_favorite', size: 24, followTheme: true)
ThemedImage.network('https://example.com/image.jpg', width: 100, height: 100)
*/

import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../../core/index.dart';

/// 图片类型枚举
enum ThemedImageType {
  asset, // 本地资源图片
  network, // 网络图片
}

/// 图片格式枚举（Flutter Image 组件官方支持的格式）
enum ImageFormat {
  png('.png'),
  jpg('.jpg'),
  jpeg('.jpeg'),
  gif('.gif'),
  webp('.webp');

  const ImageFormat(this.extension);
  final String extension;
}

/// 资源文件夹枚举
enum ThemedAssetFolder {
  crypto, // assets/crypto/
  icons, // assets/icons/
  images, // assets/images/
}

/// 主题化图片组件
class ThemedImage extends StatefulWidget {
  /// 图片名称（不包含路径和扩展名），可以为空
  final String name;

  /// 图片类型
  final ThemedImageType type;

  /// 图片格式
  final ImageFormat format;

  /// 尺寸
  final double? size;

  /// 宽度
  final double? width;

  /// 高度
  final double? height;

  /// 适应方式
  final BoxFit fit;

  /// 是否跟随主题（仅对本地图片有效）
  /// 如果为true，会根据当前主题模式自动选择对应的图片
  /// 浅色模式：name_light.format，深色模式：name.format
  final bool followTheme;

  /// 占位符组件
  final Widget? placeholder;

  /// 错误时显示的组件
  final Widget? errorWidget;

  /// 加载完成回调
  final VoidCallback? onLoaded;

  /// 加载失败回调
  final VoidCallback? onError;

  /// 边框圆角
  final BorderRadius? borderRadius;

  /// 颜色滤镜
  final ColorFilter? colorFilter;

  /// 透明度
  final double opacity;

  /// 外边距
  final EdgeInsets? margin;

  /// 模板类型
  final TemplateType? template;

  /// 是否显示骨架屏
  final bool showSkeleton;

  /// 自定义文件夹
  final ThemedAssetFolder? folder;

  const ThemedImage({
    super.key,
    this.name = '',
    this.type = ThemedImageType.asset,
    this.format = ImageFormat.png,
    this.size,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.followTheme = false,
    this.placeholder,
    this.errorWidget,
    this.onLoaded,
    this.onError,
    this.borderRadius,
    this.colorFilter,
    this.opacity = 1.0,
    this.margin,
    this.template,
    this.showSkeleton = true,
    this.folder,
  });

  /// 创建本地资源图片
  const ThemedImage.asset(
    String? name, {
    Key? key,
    ImageFormat format = ImageFormat.png,
    double? size,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    bool followTheme = false,
    Widget? placeholder,
    Widget? errorWidget,
    VoidCallback? onLoaded,
    VoidCallback? onError,
    BorderRadius? borderRadius,
    ColorFilter? colorFilter,
    double opacity = 1.0,
    EdgeInsets? margin,
    TemplateType? template,
    bool showSkeleton = true,
    ThemedAssetFolder? folder,
  }) : this(
         key: key,
         name: name ?? '',
         type: ThemedImageType.asset,
         format: format,
         size: size,
         width: width,
         height: height,
         fit: fit,
         followTheme: followTheme,
         placeholder: placeholder,
         errorWidget: errorWidget,
         onLoaded: onLoaded,
         onError: onError,
         borderRadius: borderRadius,
         colorFilter: colorFilter,
         opacity: opacity,
         margin: margin,
         template: template,
         showSkeleton: showSkeleton,
         folder: folder,
       );

  /// 创建网络图片
  const ThemedImage.network(
    String? imageUrl, {
    Key? key,
    double? size,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
    VoidCallback? onLoaded,
    VoidCallback? onError,
    BorderRadius? borderRadius,
    ColorFilter? colorFilter,
    double opacity = 1.0,
    EdgeInsets? margin,
    bool showSkeleton = true,
    ThemedAssetFolder? folder,
  }) : this(
         key: key,
         name: imageUrl ?? '',
         type: ThemedImageType.network,
         format: ImageFormat.png, // 网络图片格式不重要，因为是完整URL
         size: size,
         width: width,
         height: height,
         fit: fit,
         followTheme: false,
         placeholder: placeholder,
         errorWidget: errorWidget,
         onLoaded: onLoaded,
         onError: onError,
         borderRadius: borderRadius,
         colorFilter: colorFilter,
         opacity: opacity,
         margin: margin,
         template: null,
         showSkeleton: showSkeleton,
         folder: folder,
       );

  /// 创建加密货币图标
  const ThemedImage.crypto(
    String? name, {
    Key? key,
    ImageFormat format = ImageFormat.png,
    double? size,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    bool followTheme = false,
    Widget? placeholder,
    Widget? errorWidget,
    VoidCallback? onLoaded,
    VoidCallback? onError,
    BorderRadius? borderRadius,
    ColorFilter? colorFilter,
    double opacity = 1.0,
    EdgeInsets? margin,
    bool showSkeleton = true,
  }) : this(
         key: key,
         name: name ?? '',
         type: ThemedImageType.asset,
         format: format,
         size: size,
         width: width,
         height: height,
         fit: fit,
         followTheme: followTheme,
         placeholder: placeholder,
         errorWidget: errorWidget,
         onLoaded: onLoaded,
         onError: onError,
         borderRadius: borderRadius,
         colorFilter: colorFilter,
         opacity: opacity,
         margin: margin,
         template: null,
         showSkeleton: showSkeleton,
         folder: ThemedAssetFolder.crypto,
       );

  /// 创建图标资源
  const ThemedImage.icon(
    String? name, {
    Key? key,
    ImageFormat format = ImageFormat.png,
    double? size,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    bool followTheme = true,
    Widget? placeholder,
    Widget? errorWidget,
    VoidCallback? onLoaded,
    VoidCallback? onError,
    BorderRadius? borderRadius,
    ColorFilter? colorFilter,
    double opacity = 1.0,
    EdgeInsets? margin,
    bool showSkeleton = true,
  }) : this(
         key: key,
         name: name ?? '',
         type: ThemedImageType.asset,
         format: format,
         size: size,
         width: width,
         height: height,
         fit: fit,
         followTheme: followTheme,
         placeholder: placeholder,
         errorWidget: errorWidget,
         onLoaded: onLoaded,
         onError: onError,
         borderRadius: borderRadius,
         colorFilter: colorFilter,
         opacity: opacity,
         margin: margin,
         template: null,
         showSkeleton: showSkeleton,
         folder: ThemedAssetFolder.icons,
       );

  @override
  State<ThemedImage> createState() => _ThemedImageState();
}

class _ThemedImageState extends State<ThemedImage> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    // 检查 name 是否为空或无效
    if (widget.name.isEmpty) {
      return widget.showSkeleton ? _buildSkeleton() : const SizedBox.shrink();
    }

    // 检查是否有有效的图片源（与 ImageWidget 保持一致）
    bool hasValidSource = false;
    if (widget.type == ThemedImageType.network && widget.name.isNotEmpty) {
      hasValidSource = true;
    } else if (widget.type == ThemedImageType.asset && widget.name.isNotEmpty) {
      hasValidSource = true;
    }

    if (!hasValidSource) {
      return widget.showSkeleton ? _buildSkeleton() : const SizedBox.shrink();
    }

    Widget imageWidget;

    // 根据图片类型构建不同的图片组件
    switch (widget.type) {
      case ThemedImageType.asset:
        imageWidget = _buildAssetImage();
        break;
      case ThemedImageType.network:
        imageWidget = _buildNetworkImage();
        break;
    }

    // 应用容器装饰
    return _buildContainer(imageWidget);
  }

  /// 构建本地资源图片
  Widget _buildAssetImage() {
    // 这里不需要再次检查 name 是否为空，因为在 build 方法中已经检查过了

    // 根据主题生成图片路径
    String imagePath = _generateAssetPath();

    Widget imageWidget = Image.asset(
      imagePath,
      width: widget.size ?? widget.width,
      height: widget.size ?? widget.height,
      fit: widget.fit,
      opacity: AlwaysStoppedAnimation(widget.opacity),
      errorBuilder: (context, error, stackTrace) {
        _handleError();
        return widget.showSkeleton ? _buildSkeleton() : _buildErrorWidget();
      },
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) {
          _handleLoaded();
          return child;
        }

        if (frame == null) {
          return _buildPlaceholder();
        } else {
          _handleLoaded();
          return child;
        }
      },
    );

    // 应用颜色滤镜
    if (widget.colorFilter != null) {
      imageWidget = ColorFiltered(
        colorFilter: widget.colorFilter!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// 生成资源图片路径
  String _generateAssetPath() {
    // 如果指定了自定义文件夹，使用自定义路径生成逻辑
    if (widget.folder != null) {
      return _generateCustomFolderPath();
    }

    // 使用 TemplateResourceManager 生成路径，与 ImageWidget 保持一致
    final templateType = widget.template ?? TemplateType.base;
    String resourceName = widget.name;

    // 优化的主题切换：浅色模式使用 _light 后缀
    if (widget.followTheme) {
      final isDark = Theme.of(context).brightness == Brightness.dark;
      if (!isDark) {
        // 浅色模式使用 _light 后缀
        resourceName = '${widget.name}_light';
      }
    }

    return TemplateResourceManager.instance.getResourcePath(
      resourceName: resourceName,
      type: ResourceType.image,
      template: templateType,
      followTheme: false, // 我们已经手动处理了主题切换
      fileExtension: widget.format.extension,
    );
  }

  /// 生成自定义文件夹路径
  String _generateCustomFolderPath() {
    String basePath;

    // 根据文件夹枚举确定基础路径
    switch (widget.folder!) {
      case ThemedAssetFolder.crypto:
        basePath = 'assets/crypto/';
        break;
      case ThemedAssetFolder.icons:
        basePath = 'assets/icons/';
        break;
      case ThemedAssetFolder.images:
        basePath = 'assets/images/';
        break;
    }

    String fileName = widget.name;

    // 优化的主题切换：浅色模式使用 _light 后缀
    if (widget.followTheme) {
      final isDark = Theme.of(context).brightness == Brightness.dark;
      if (!isDark) {
        // 浅色模式使用 _light 后缀
        fileName = '${widget.name}_light';
      }
    }

    // 添加文件扩展名
    fileName = '$fileName${widget.format.extension}';

    final finalPath = '$basePath$fileName';
    return finalPath;
  }

  /// 构建网络图片
  Widget _buildNetworkImage() {
    // 这里不需要再次检查 name 是否为空，因为在 build 方法中已经检查过了

    Widget imageWidget = Image.network(
      widget.name, // 对于网络图片，name 就是完整的 URL
      width: widget.size ?? widget.width,
      height: widget.size ?? widget.height,
      fit: widget.fit,
      opacity: AlwaysStoppedAnimation(widget.opacity),
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) {
          _handleLoaded();
          return child;
        }

        // 使用 addPostFrameCallback 延迟 setState 调用，避免在 build 期间调用
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              _isLoading = true;
            });
          }
        });

        return _buildPlaceholder();
      },
      errorBuilder: (context, error, stackTrace) {
        _handleError();
        return widget.showSkeleton ? _buildSkeleton() : _buildErrorWidget();
      },
    );

    // 应用颜色滤镜
    if (widget.colorFilter != null) {
      imageWidget = ColorFiltered(
        colorFilter: widget.colorFilter!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// 构建容器装饰
  Widget _buildContainer(Widget child) {
    Widget result = child;

    // 应用边框圆角
    if (widget.borderRadius != null) {
      result = ClipRRect(borderRadius: widget.borderRadius!, child: result);
    }

    // 应用外边距
    if (widget.margin != null) {
      result = Container(margin: widget.margin, child: result);
    }

    return result;
  }

  /// 构建占位符组件
  Widget _buildPlaceholder() {
    if (widget.placeholder != null) {
      return widget.placeholder!;
    }

    return Container(
      width: widget.size ?? widget.width,
      height: widget.size ?? widget.height,
      color: context.templateColors.surface,
      child: Center(
        child:
            _isLoading
                ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      context.templateColors.primary,
                    ),
                  ),
                )
                : Icon(
                  Icons.image_outlined,
                  color: context.templateColors.textTertiary,
                  size: 24,
                ),
      ),
    );
  }

  /// 构建错误组件
  Widget _buildErrorWidget() {
    if (widget.errorWidget != null) {
      return widget.errorWidget!;
    }

    return Container(
      width: widget.size ?? widget.width,
      height: widget.size ?? widget.height,
      color: context.templateColors.surface,
      child: Center(
        child: Icon(
          Icons.broken_image_outlined,
          color: context.templateColors.textTertiary,
          size: 24,
        ),
      ),
    );
  }

  /// 构建骨架屏组件（与 ImageWidget 保持一致）
  Widget _buildSkeleton() {
    return Container(
      margin: widget.margin,
      child: ClipRRect(
        borderRadius: widget.borderRadius ?? BorderRadius.zero,
        child: Shimmer.fromColors(
          baseColor: context.templateColors.skeletonBase,
          highlightColor: context.templateColors.skeletonHighlight,
          child: Container(
            width: widget.size ?? widget.width,
            height: widget.size ?? widget.height,
            decoration: BoxDecoration(
              color: context.templateColors.skeletonBase,
              borderRadius: widget.borderRadius ?? BorderRadius.zero,
            ),
          ),
        ),
      ),
    );
  }

  /// 处理加载完成
  void _handleLoaded() {
    if (_isLoading) {
      // 使用 addPostFrameCallback 延迟 setState 调用，避免在 build 期间调用
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      });
    }
    widget.onLoaded?.call();
  }

  /// 处理加载错误
  void _handleError() {
    // 使用 addPostFrameCallback 延迟 setState 调用，避免在 build 期间调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
    widget.onError?.call();
  }
}
