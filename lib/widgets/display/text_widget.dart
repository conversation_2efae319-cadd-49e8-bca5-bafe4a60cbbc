import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:qubic_exchange/core/index.dart';

// 文本组件
class TextWidget extends StatefulWidget {
  final String? text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final bool? softWrap;
  final TextDirection? textDirection;
  final bool showSkeleton;

  const TextWidget({
    super.key,
    this.text,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.softWrap,
    this.textDirection,
    this.showSkeleton = false,
  });

  @override
  State<TextWidget> createState() => _TextWidgetState();
}

class _TextWidgetState extends State<TextWidget> {
  @override
  Widget build(BuildContext context) {
    Widget textWidget = Text(
      widget.text ?? '',
      style: widget.style,
      textAlign: widget.textAlign,
      maxLines: widget.maxLines,
      overflow: widget.overflow,
      softWrap: widget.softWrap,
      textDirection: widget.textDirection,
      // 确保文本不受系统文本缩放影响，但保持代码中设置的fontSize生效
      textScaler: TextScaler.noScaling,
    );

    if (widget.showSkeleton) {
      textWidget = Shimmer.fromColors(
        baseColor: context.templateColors.skeletonBase,
        highlightColor: context.templateColors.skeletonHighlight,
        child: Container(
          width: double.infinity,
          height: UiConstants.spacing32,
          color: context.templateColors.skeletonBase,
        ),
      );
    }

    return textWidget;
  }
}
