import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../../core/index.dart';

// 图片类型枚举
enum ImageType { asset, network }

// 文件夹类型枚举
enum AssetFolder {
  crypto, // assets/crypto/
  icons, // assets/icons/
  images, // assets/images/
}

// 图片模板类型枚举 - 已废弃，使用TemplateType替代
@Deprecated('使用 TemplateType 替代')
enum ImageTemplateType {
  bitget, // bitget模板
  okx, // okx模板
}

// 图片组件
class ImageWidget extends StatefulWidget {
  final double? width;
  final double? height;
  final EdgeInsets? margin;
  final double? radius;
  final String? name;
  final String? path;
  final ImageType type;
  final AssetFolder? assetFolder;
  final TemplateType? template;
  final String? fileExtension;
  final bool followTheme;
  final bool showSkeleton;

  const ImageWidget({
    super.key,
    this.width,
    this.height,
    this.margin,
    this.radius,
    this.name,
    this.path,
    this.type = ImageType.asset,
    this.assetFolder = AssetFolder.images,
    this.template = TemplateType.base,
    this.fileExtension,
    this.followTheme = false,
    this.showSkeleton = true,
  });

  @override
  State<ImageWidget> createState() => _ImageWidgetState();
}

class _ImageWidgetState extends State<ImageWidget> {
  @override
  Widget build(BuildContext context) {
    // 检查是否有有效的图片源
    bool hasValidSource = false;
    if (widget.type == ImageType.network &&
        widget.path != null &&
        widget.path!.isNotEmpty) {
      hasValidSource = true;
    } else if (widget.type == ImageType.asset) {
      // 对于本地图片，检查是否有完整路径或者有文件夹+名称
      hasValidSource =
          (widget.path != null && widget.path!.isNotEmpty) ||
          (widget.assetFolder != null &&
              widget.name != null &&
              widget.name!.isNotEmpty);
    }

    if (!hasValidSource) {
      return widget.showSkeleton ? _buildSkeleton() : const SizedBox.shrink();
    }

    switch (widget.type) {
      case ImageType.asset:
        return _buildAssetImage();
      case ImageType.network:
        return _buildNetworkImage();
    }
  }

  // 本地图片
  Widget _buildAssetImage() {
    String finalPath = _buildAssetPath(context);

    return ClipRRect(
      borderRadius: BorderRadius.circular(widget.radius ?? 0),
      child: Container(
        margin: widget.margin,
        child: Image.asset(
          finalPath,
          width: widget.width,
          height: widget.height,
          errorBuilder: (context, error, stackTrace) {
            return widget.showSkeleton
                ? _buildSkeleton()
                : const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  // 构建资源路径
  String _buildAssetPath(BuildContext context) {
    // 如果直接提供了完整路径，直接使用
    if (widget.path != null && widget.path!.startsWith('assets/')) {
      return widget.path!;
    }

    // 使用新的资源管理器构建路径
    if (widget.assetFolder == AssetFolder.images && widget.name != null) {
      return _buildResourcePath();
    }

    // 对于非图片资源，使用传统方式构建路径
    if (widget.assetFolder != null && widget.name != null) {
      String folderPath = _getFolderPath(widget.assetFolder!);
      String fileName = widget.name!;
      String extension =
          widget.fileExtension ?? _getDefaultExtension(widget.assetFolder!);

      // 处理文件名和扩展名
      if (!fileName.contains('.')) {
        fileName = '$fileName$extension';
      }

      String finalPath = '$folderPath$fileName';
      return finalPath;
    }

    // 兼容旧的使用方式
    return widget.path ?? '';
  }

  // 获取文件夹路径
  String _getFolderPath(AssetFolder folder) {
    switch (folder) {
      case AssetFolder.crypto:
        return 'assets/crypto/';
      case AssetFolder.icons:
        return 'assets/icons/';
      case AssetFolder.images:
        // images 文件夹需要包含模板子文件夹
        String templateName = _getTemplateName();
        return 'assets/images/$templateName/';
    }
  }

  // 获取模板名称
  String _getTemplateName() {
    if (widget.template != null) {
      return widget.template!.id;
    }

    // 如果没有指定模板，使用默认模板
    return TemplateNames.base;
  }

  // 使用新的资源管理器构建路径
  String _buildResourcePath() {
    final templateType = widget.template ?? TemplateType.base;
    final resourceName = widget.name ?? '';

    return TemplateResourceManager.instance.getResourcePath(
      resourceName: resourceName,
      type: ResourceType.image,
      template: templateType,
      followTheme: widget.followTheme,
      fileExtension: widget.fileExtension,
    );
  }

  // 获取默认文件扩展名，使用 png
  String _getDefaultExtension(AssetFolder folder) {
    return '.png';
  }

  // 网络图片
  Widget _buildNetworkImage() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(widget.radius ?? 0),
      child: Container(
        margin: widget.margin,
        child: Image.network(
          widget.path!,
          width: widget.width,
          height: widget.height,
          errorBuilder: (context, error, stackTrace) {
            return widget.showSkeleton
                ? _buildSkeleton()
                : const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  // 骨架屏效果
  Widget _buildSkeleton() {
    return Container(
      margin:
          (widget.name == null || widget.path == null || widget.path!.isEmpty)
              ? widget.margin
              : null,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(widget.radius ?? 0),
        child: Shimmer.fromColors(
          baseColor: context.templateColors.skeletonBase,
          highlightColor: context.templateColors.skeletonHighlight,
          child: Container(
            width: widget.width,
            height: widget.height,
            decoration: BoxDecoration(
              color: context.templateColors.skeletonBase,
              borderRadius: BorderRadius.circular(widget.radius ?? 0),
            ),
          ),
        ),
      ),
    );
  }
}
