import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

// 标签组件 - 改为 StatefulWidget 支持参数实时更新
class TagWidget extends StatefulWidget {
  final String? text;
  final VoidCallback? onTap;
  final Widget? icon;
  final bool closable;
  final VoidCallback? onClose;
  final EdgeInsets? margin;
  final EdgeInsets? padding;
  final double? width;
  final double? height;
  final double? radius;

  // 自定义样式参数
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? borderWidth;

  // 自定义内容参数 - 当传入时，内置内容不会显示
  final Widget? child;

  const TagWidget({
    super.key,
    this.text,
    this.onTap,
    this.icon,
    this.closable = false,
    this.onClose,
    this.margin,
    this.padding,
    this.width,
    this.height,
    this.radius,
    // 自定义样式参数
    this.textStyle,
    this.backgroundColor,
    this.borderColor,
    this.borderWidth,
    // 自定义内容参数
    this.child,
  }) : assert(
         child != null || text != null,
         'Either child or text must be provided',
       );

  @override
  State<TagWidget> createState() => _TagWidgetState();
}

class _TagWidgetState extends State<TagWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin,
      width: widget.width,
      height: widget.height,
      child: Material(
        color: Colors.transparent,
        child: InkWellWidget(
          onTap: widget.onTap,
          child: Container(
            padding:
                widget.padding ??
                EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
            decoration: _getDecoration(context),
            child:
                widget.child ??
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 图标
                    if (widget.icon != null) ...[
                      widget.icon!,
                      SizedBox(width: UiConstants.spacing8),
                    ],
                    // 文本
                    if (widget.text != null)
                      Text(widget.text!, style: _getTextStyle(context)),
                    // 关闭按钮
                    if (widget.closable) ...[
                      SizedBox(width: UiConstants.spacing4),
                      GestureDetector(
                        onTap: widget.onClose,
                        child: Icon(
                          Icons.close,
                          size: UiConstants.fontSize12,
                          color: _getIconColor(context),
                        ),
                      ),
                    ],
                  ],
                ),
          ),
        ),
      ),
    );
  }

  // 获取装饰样式
  BoxDecoration _getDecoration(BuildContext context) {
    return BoxDecoration(
      color: widget.backgroundColor ?? context.templateColors.surface,
      border:
          widget.borderColor != null
              ? Border.all(
                color: widget.borderColor ?? context.templateColors.border,
                width:
                    widget.borderWidth ??
                    context.templateStyles.borderWidthThin,
              )
              : null,
      borderRadius: BorderRadius.circular(
        widget.radius ?? UiConstants.borderRadius2,
      ),
    );
  }

  // 获取文本样式
  TextStyle _getTextStyle(BuildContext context) {
    // 如果有自定义文本样式，优先使用自定义样式
    if (widget.textStyle != null) {
      return widget.textStyle!;
    }

    // 使用默认样式
    return context.templateStyle.text.bodyTextMedium.copyWith(
      color: context.templateColors.textPrimary,
    );
  }

  // 获取图标颜色（关闭按钮等）
  Color _getIconColor(BuildContext context) {
    // 如果有自定义文本样式，使用其颜色
    if (widget.textStyle?.color != null) {
      return widget.textStyle!.color!;
    }

    // 否则使用默认文本颜色
    return context.templateColors.textPrimary;
  }
}
