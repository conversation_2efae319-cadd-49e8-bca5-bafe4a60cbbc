/*
*   标签栏组件 - 基于原生TabBar
*
*   ✅ 已适配新的模板主题系统：
*   - 使用 context.templateColors 替代 theme.xxx(context)
*   - 使用 context.templateStyle.text.xxx 替代 themeStyles.xxx(context)
*   - 支持多种主题模板（Base、OKX、Bitget）
*   - 自动适应深色/浅色模式
*
*   功能特性：
*   - 支持角标显示
*   - 支持下拉箭头
*   - 支持多种指示器样式（下划线、填充、无）
*   - 支持自定义颜色和样式
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

// 指示器样式枚举
enum TabBarIndicatorStyle {
  underline, // 下划线样式
  filled, // 填充样式
  none, // 无指示器
}

class TabItem {
  final String title;
  final String? badge; // 角标文本
  final int? badgeCount; // 角标数字
  final bool showBadge; // 是否显示角标
  final Color? badgeColor; // 角标颜色
  final bool showDropdownArrow; // 是否显示下拉箭头
  final VoidCallback? onDropdownTap; // 下拉箭头点击回调

  const TabItem({
    required this.title,
    this.badge,
    this.badgeCount,
    this.badgeColor,
    this.showDropdownArrow = false,
    this.onDropdownTap,
    this.showBadge = false,
  });
}

class TabbarWidget extends StatefulWidget {
  final List<TabItem> tabs;
  final TabController? controller;
  final ValueChanged<int>? onTap;
  final TabBarIndicatorSize? indicatorSize;
  final TabBarIndicatorStyle indicatorStyle;
  final bool showIndicator;

  // 样式配置
  final Color? selectedColor;
  final Color? unselectedColor;
  final Color? indicatorColor;
  final double indicatorRadius;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final TextStyle? labelStyle;
  final TextStyle? unselectedLabelStyle;
  final double? indicatorWeight;
  final EdgeInsetsGeometry? indicatorPadding;
  final EdgeInsetsGeometry? labelPadding; // 标签项的内边距
  final bool isScrollable;
  final TabAlignment tabAlignment;
  final Color? dividerColor;
  final Color? fillColor;
  final EdgeInsetsGeometry? tabPadding; // 标签项的外边距

  const TabbarWidget({
    super.key,
    required this.tabs,
    this.controller,
    this.onTap,
    this.indicatorSize,
    this.indicatorStyle = TabBarIndicatorStyle.underline,
    this.showIndicator = true,
    this.selectedColor,
    this.unselectedColor,
    this.indicatorColor,
    this.indicatorRadius = 8.0,
    this.height = 48.0,
    this.padding,
    this.margin,
    this.labelStyle,
    this.unselectedLabelStyle,
    this.indicatorWeight = 2.5,
    this.indicatorPadding,
    this.labelPadding,
    this.isScrollable = true,
    this.tabAlignment = TabAlignment.start,
    this.dividerColor,
    this.fillColor,
    this.tabPadding,
  });

  @override
  State<TabbarWidget> createState() => _TabbarWidgetState();
}

class _TabbarWidgetState extends State<TabbarWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int? _lastSelectedIndex;

  @override
  void initState() {
    super.initState();
    _tabController =
        widget.controller ??
        TabController(length: widget.tabs.length, vsync: this);
    _lastSelectedIndex = _tabController.index;

    // 监听tab切换，更新选中状态
    _tabController.addListener(_onTabChanged);
  }

  // Tab切换监听器
  void _onTabChanged() {
    if (!_tabController.indexIsChanging && mounted) {
      // 切换完成后更新状态，确保组件仍然mounted
      setState(() {
        _lastSelectedIndex = _tabController.index;
      });
    }
  }

  @override
  void dispose() {
    // 移除监听器，防止内存泄漏
    _tabController.removeListener(_onTabChanged);

    // 只有当controller为null时才dispose TabController
    if (widget.controller == null) {
      _tabController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 使用新的模板主题系统
    final colors = context.templateColors;

    final defaultSelectedColor = widget.selectedColor ?? colors.primary;
    final defaultUnselectedColor =
        widget.unselectedColor ?? colors.textTertiary;
    final defaultIndicatorColor = widget.indicatorColor ?? defaultSelectedColor;
    final defaultLabelStyle =
        widget.labelStyle ?? context.templateStyle.text.tabTextSmall;

    return Container(
      color: widget.fillColor,
      height: widget.height,
      margin: widget.margin,
      padding: widget.padding,
      child: TabBar(
        controller: _tabController,
        onTap: _handleTabTap,
        labelColor: defaultSelectedColor,
        unselectedLabelColor: defaultUnselectedColor,
        indicatorColor:
            widget.indicatorStyle == TabBarIndicatorStyle.underline
                ? (widget.indicatorColor ?? defaultIndicatorColor)
                : Colors.transparent,
        indicatorWeight: widget.indicatorWeight ?? 1.0,
        indicatorPadding: widget.indicatorPadding ?? EdgeInsets.zero,
        indicatorSize: widget.indicatorSize ?? TabBarIndicatorSize.tab,
        indicator: _buildIndicator(),
        isScrollable: widget.isScrollable,
        tabAlignment: widget.tabAlignment,
        labelStyle: widget.labelStyle ?? defaultLabelStyle,
        unselectedLabelStyle: widget.unselectedLabelStyle,
        labelPadding: widget.labelPadding ?? EdgeInsets.zero, // 添加标签项的内边距
        splashFactory: NoSplash.splashFactory,
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        dividerColor: widget.dividerColor ?? Colors.transparent,
        tabs: widget.tabs.map((tab) => _buildTab(tab)).toList(),
      ),
    );
  }

  // 处理标签点击
  void _handleTabTap(int index) {
    final tab = widget.tabs[index];
    final wasAlreadySelected = _lastSelectedIndex == index;

    if (wasAlreadySelected &&
        tab.showDropdownArrow &&
        tab.onDropdownTap != null) {
      // 如果点击的是已选中的tab且有下拉箭头，执行下拉回调
      tab.onDropdownTap!();
    } else {
      // 否则执行正常的tab切换
      widget.onTap?.call(index);
    }
  }

  // 构建指示器
  Decoration? _buildIndicator() {
    if (!widget.showIndicator ||
        widget.indicatorStyle == TabBarIndicatorStyle.none) {
      return const BoxDecoration();
    }

    // 获取当前模板颜色
    final colors = context.templateColors;
    final styles = context.templateStyles;

    switch (widget.indicatorStyle) {
      case TabBarIndicatorStyle.filled:
        // 使用 Flutter 官方的 BoxDecoration 填充指示器
        return BoxDecoration(
          color: widget.indicatorColor ?? colors.tabbarBackground,
          borderRadius: BorderRadius.circular(widget.indicatorRadius),
        );
      case TabBarIndicatorStyle.underline:
        // 使用 Flutter 官方下划线指示器
        return UnderlineTabIndicator(
          borderRadius: BorderRadius.circular(8.0),
          borderSide: BorderSide(
            color:
                widget.indicatorColor ?? widget.selectedColor ?? colors.primary,
            width: widget.indicatorWeight ?? 2.5,
          ),
        );
      case TabBarIndicatorStyle.none:
        return const BoxDecoration();
    }
  }

  Widget _buildTab(TabItem tab) {
    return Tab(
      child: Container(
        padding: widget.tabPadding ?? EdgeInsets.zero,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                Text(tab.title),
                // 角标
                if (tab.showBadge)
                  Positioned(top: -2, right: -2, child: _buildBadge(tab)),
              ],
            ),
            // 下拉箭头
            if (tab.showDropdownArrow) ...[
              const SizedBox(width: 4),
              ThemedImage.asset(
                'arrow_triangle_down',
                followTheme: true,
                size: UiConstants.iconSize10,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBadge(TabItem tab) {
    final colors = context.templateColors;
    final badgeColor = tab.badgeColor ?? colors.primary;

    return Container(
      decoration: BoxDecoration(
        color: badgeColor,
        borderRadius: BorderRadius.circular(10),
      ),
      constraints: const BoxConstraints(minWidth: 8, minHeight: 8),
    );
  }
}
