/*
 * 自定义输入框组件 - TextFieldWidget
 *
 * 🎯 核心功能：
 * - 统一的输入框样式和行为管理
 * - 自动控制器和焦点节点管理
 * - 动态标签样式（聚焦时缩小）
 * - 错误状态管理和动画
 * - 全局焦点管理集成
 *
 * 🎨 视觉特性：
 * - Container控制边框和背景，TextField使用透明背景
 * - 聚焦时边框自动高亮，支持自定义颜色
 * - 标签文字在聚焦/有内容时自动缩小
 * - 错误提示带滑入动画效果
 * - 支持前缀、后缀组件和图标
 *
 * 🔧 技术特性：
 * - 自动创建和管理默认控制器
 * - 文本渲染一致性优化（StrutStyle）
 * - 内存安全的生命周期管理
 * - 实时状态响应和样式更新
 *
 * 📱 使用场景：
 * - 表单输入、搜索框、登录注册
 * - 交易下单、设置页面等所有文本输入场景
 */

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/core/managers/focus_manager.dart';
import 'package:qubic_exchange/widgets/display/text_widget.dart';
import 'dart:ui' as ui;

/// 自定义输入框组件
///
/// 提供统一的输入框样式和行为，支持自动控制器管理、
/// 动态标签样式、错误状态管理等功能。
///
/// 示例用法：
/// ```dart
/// TextFieldWidget(
///   labelText: '用户名',
///   hintText: '请输入用户名',
///   onChanged: (value) => print(value),
/// )
/// ```
class TextFieldWidget extends StatefulWidget {
  // ========== 核心控制参数 ==========

  /// 输入框控制器
  ///
  /// 如果不提供，组件会自动创建一个默认控制器
  final TextEditingController? controller;

  /// 焦点节点
  ///
  /// 如果不提供，组件会自动创建一个默认焦点节点
  final FocusNode? focusNode;

  // ========== 文本内容参数 ==========

  /// 标签文本
  ///
  /// 显示在输入框上方，聚焦时会缩小并上移
  final String? labelText;

  /// 占位符文本
  ///
  /// 输入框为空时显示的提示文本
  final String? hintText;

  // ========== 样式参数 ==========

  /// 输入文本样式
  final TextStyle? textStyle;

  /// 占位符文本样式
  final TextStyle? hintStyle;

  /// 标签文本样式
  final TextStyle? labelStyle;

  /// 自定义输入框装饰
  ///
  /// 如果提供此参数，将覆盖默认的装饰设置
  final InputDecoration? decoration;

  // ========== 颜色和外观参数 ==========

  /// 输入框背景颜色
  final Color? fillColor;

  /// 是否填充背景
  final bool? filled;

  /// 边框颜色（未聚焦状态）
  final Color? borderColor;

  /// 聚焦时的边框颜色
  final Color? focusBorderColor;

  // ========== 布局和尺寸参数 ==========

  /// 内容边距
  final EdgeInsetsGeometry? contentPadding;

  /// 输入框高度
  final double? height;

  /// 输入框宽度
  final double? width;

  /// 最大行数
  final int? maxLines;

  /// 最小行数
  final int? minLines;

  // ========== 前缀组件参数 ==========

  /// 前缀组件
  final Widget? prefix;

  /// 前缀图标
  final Widget? prefixIcon;

  /// 前缀文本
  final String? prefixText;

  /// 前缀文本样式
  final TextStyle? prefixStyle;

  /// 前缀图标约束
  final BoxConstraints? prefixIconConstraints;

  // ========== 后缀组件参数 ==========

  /// 后缀组件
  final Widget? suffix;

  /// 后缀图标
  final Widget? suffixIcon;

  /// 后缀文本
  final String? suffixText;

  /// 后缀文本样式
  final TextStyle? suffixStyle;

  /// 后缀图标约束
  final BoxConstraints? suffixIconConstraints;

  /// 聚焦时是否隐藏后缀图标
  final bool hideSuffixOnFocus;

  // ========== 行为控制参数 ==========

  /// 是否启用输入
  final bool enabled;

  /// 是否只读
  final bool readOnly;

  /// 是否自动获取焦点
  final bool autofocus;

  /// 是否隐藏输入内容（密码框）
  final bool obscureText;

  /// 是否使用紧凑布局
  final bool isDense;

  // ========== 输入控制参数 ==========

  /// 键盘类型
  final TextInputType? keyboardType;

  /// 输入格式限制
  final List<TextInputFormatter>? inputFormatters;

  /// 文本对齐方式
  final TextAlign textAlign;

  /// 文本垂直对齐方式
  final TextAlignVertical? textAlignVertical;

  /// 文本输入动作
  final TextInputAction? textInputAction;

  /// 自动填充提示
  final Iterable<String>? autofillHints;

  /// 光标颜色
  final Color? cursorColor;

  /// 光标宽度
  final double? cursorWidth;

  /// 光标高度
  final double? cursorHeight;

  /// 光标圆角
  final Radius? cursorRadius;

  /// 选择颜色
  final Color? selectionColor;

  /// 输入完成回调
  final VoidCallback? onEditingComplete;

  /// 提交回调
  final ValueChanged<String>? onSubmitted;

  /// 内容变化回调
  final ValueChanged<String>? onChanged;

  /// 点击回调
  final VoidCallback? onTap;

  // ========== 容器装饰参数 ==========

  /// 容器装饰
  final BoxDecoration? containerDecoration;

  /// 容器边距
  final EdgeInsets? margin;

  /// 容器内边距
  final EdgeInsets? padding;

  /// 边框圆角
  final BorderRadius? radius;

  // ========== 错误状态参数 ==========

  /// 错误提示文本
  final String? errorText;

  /// 错误提示文本样式
  final TextStyle? errorStyle;

  /// 帮助文本
  final String? helperText;

  /// 帮助文本样式
  final TextStyle? helperStyle;

  const TextFieldWidget({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.textStyle,
    this.hintStyle,
    this.decoration,
    this.fillColor,
    this.filled,
    this.labelStyle,
    this.borderColor,
    this.focusBorderColor,
    this.contentPadding,
    this.prefix,
    this.prefixIcon,
    this.prefixText,
    this.prefixStyle,
    this.suffix,
    this.suffixIcon,
    this.suffixText,
    this.suffixStyle,
    this.height,
    this.width,
    this.maxLines = 1,
    this.minLines,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.keyboardType,
    this.inputFormatters,
    this.textAlign = TextAlign.start,
    this.textAlignVertical,
    this.cursorColor,
    this.cursorWidth,
    this.cursorHeight,
    this.cursorRadius,
    this.selectionColor,
    this.onEditingComplete,
    this.onSubmitted,
    this.onChanged,
    this.onTap,
    this.focusNode,
    this.containerDecoration,
    this.margin,
    this.padding,
    this.prefixIconConstraints,
    this.suffixIconConstraints,
    this.obscureText = false,
    this.autofillHints,
    this.textInputAction,
    this.errorText,
    this.errorStyle,
    this.isDense = true,
    this.radius,
    this.hideSuffixOnFocus = false,
    this.helperText,
    this.helperStyle,
  });

  @override
  State<TextFieldWidget> createState() => _TextFieldWidgetState();
}

/// TextFieldWidget 的状态管理类
///
/// 负责管理输入框的焦点、控制器、错误状态等
class _TextFieldWidgetState extends State<TextFieldWidget> {
  // ========== 核心组件 ==========

  /// 焦点节点 - 管理输入框的焦点状态
  late FocusNode _focusNode;

  /// 文本控制器 - 管理输入框的文本内容
  late TextEditingController _controller;

  /// 全局焦点管理器 - 统一管理应用内的焦点
  final GlobalFocusManager _globalFocusManager = GlobalFocusManager.instance;

  // ========== 状态变量 ==========

  /// 当前显示的错误文本
  ///
  /// 聚焦时会隐藏错误文本，失焦时恢复显示
  String? _displayErrorText;

  // ========== 生命周期方法 ==========

  @override
  void initState() {
    super.initState();

    // 初始化焦点节点（优先使用外部传入的，否则创建新的）
    _focusNode = widget.focusNode ?? FocusNode();

    // 初始化文本控制器（优先使用外部传入的，否则创建新的）
    _controller = widget.controller ?? TextEditingController();

    // 初始化错误文本显示状态
    _displayErrorText = widget.errorText;

    // 注册到全局焦点管理器，统一管理焦点
    _globalFocusManager.registerFocusNode(_focusNode);

    // 添加监听器
    _focusNode.addListener(_onFocusChanged); // 监听焦点变化
    _controller.addListener(_onTextChanged); // 监听文本变化
  }

  @override
  void didUpdateWidget(TextFieldWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 处理控制器变化
    if (oldWidget.controller != widget.controller) {
      // 移除旧控制器的监听器
      _controller.removeListener(_onTextChanged);

      // 如果旧控制器是内部创建的，需要释放它
      if (oldWidget.controller == null) {
        _controller.dispose();
      }

      // 更新为新的控制器
      _controller = widget.controller ?? TextEditingController();
      _controller.addListener(_onTextChanged);
    }

    // 处理错误文本变化
    if (oldWidget.errorText != widget.errorText) {
      // 聚焦状态下不显示新的错误文本，避免干扰用户输入
      if (_focusNode.hasFocus && widget.errorText != null) {
        _displayErrorText = null;
      } else {
        _displayErrorText = widget.errorText;
      }
    }
  }

  @override
  void dispose() {
    // 移除所有监听器，防止内存泄漏
    _focusNode.removeListener(_onFocusChanged);
    _controller.removeListener(_onTextChanged);

    // 延迟注销焦点节点，避免在组件树重建时触发状态更新
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _globalFocusManager.unregisterFocusNode(_focusNode);
    });

    // 只释放内部创建的资源，外部传入的资源由外部管理
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }

    if (widget.controller == null) {
      _controller.dispose();
    }

    super.dispose();
  }

  // ========== 事件处理方法 ==========

  /// 焦点变化处理
  ///
  /// 聚焦时隐藏错误提示，失焦时恢复错误提示
  /// 同时触发重建以更新标签样式和边框颜色
  void _onFocusChanged() {
    if (mounted) {
      setState(() {
        if (_focusNode.hasFocus) {
          // 聚焦时隐藏错误文本，避免干扰用户输入
          _displayErrorText = null;
        } else {
          // 失焦时恢复错误文本显示
          _displayErrorText = widget.errorText;
        }
      });
    }
  }

  /// 文本内容变化处理
  ///
  /// 当文本内容发生变化时，触发重建以更新标签样式
  /// （有内容时标签缩小，无内容时标签恢复正常大小）
  void _onTextChanged() {
    if (mounted) {
      setState(() {
        // 触发重建，更新标签样式
      });
    }
  }

  // ========== 构建方法 ==========

  @override
  Widget build(BuildContext context) {
    // 构建核心 TextField 组件
    final textField = TextField(
      // 核心控制
      controller: _controller,
      focusNode: _focusNode,

      // 样式设置
      style: _getTextStyle(context),
      decoration: _buildInputDecoration(context),

      // 文本渲染优化
      strutStyle: StrutStyle(
        fontSize: _getTextStyle(context).fontSize,
        height: _getTextStyle(context).height,
        forceStrutHeight: true, // 强制使用指定行高，确保一致性
      ),

      // 行为控制
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      autofocus: widget.autofocus,
      obscureText: widget.obscureText,

      // 输入限制
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      keyboardType: widget.keyboardType,
      inputFormatters: widget.inputFormatters,
      textInputAction: widget.textInputAction,
      autofillHints: widget.autofillHints,

      // 对齐方式
      textAlign: widget.textAlign,
      textAlignVertical: widget.textAlignVertical ?? TextAlignVertical.center,

      // 光标设置
      cursorColor: widget.cursorColor,
      cursorWidth: widget.cursorWidth ?? 2.0,
      cursorHeight: widget.cursorHeight ?? 14.0,
      cursorRadius: widget.cursorRadius,

      // 回调函数
      onEditingComplete: widget.onEditingComplete,
      onSubmitted: widget.onSubmitted,
      onChanged: widget.onChanged,
      onTap: widget.onTap,

      // 其他设置
      expands: false,
      scrollPadding: const EdgeInsets.all(20.0), // 滚动时的内边距
      selectionHeightStyle: ui.BoxHeightStyle.includeLineSpacingMiddle,
    );

    // 使用Container包装，提供统一的装饰和尺寸控制
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          alignment: Alignment.center,
          height: widget.height,
          width: widget.width,
          constraints:
              widget.height == null
                  ? BoxConstraints(minHeight: 40.0) // 确保最小高度
                  : null,
          decoration:
              widget.containerDecoration ??
              _getDefaultContainerDecoration(context),
          margin: widget.margin,
          padding: widget.padding,
          child: IntrinsicHeight(child: textField),
        ),
        // 错误提示文本 - 带动画效果
        _buildErrorText(context),
        // 帮助文本
        if (widget.helperText != null)
          Padding(
            padding: EdgeInsets.only(top: UiConstants.spacing4),
            child: TextWidget(
              text: widget.helperText!,
              style:
                  widget.helperStyle ?? context.templateStyle.text.inputHelper,
            ),
          ),
      ],
    );
  }

  /// 构建带动画的错误文本
  Widget _buildErrorText(BuildContext context) {
    // 使用AnimatedSwitcher来实现平滑的过渡效果
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      transitionBuilder: (Widget child, Animation<double> animation) {
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, -0.5),
            end: Offset.zero,
          ).animate(
            CurvedAnimation(parent: animation, curve: Curves.easeOutCubic),
          ),
          child: FadeTransition(opacity: animation, child: child),
        );
      },
      child:
          _displayErrorText != null
              ? Padding(
                key: ValueKey(_displayErrorText),
                padding: EdgeInsets.only(
                  top: UiConstants.spacing4,
                  left: UiConstants.spacing4,
                ),
                child: TextWidget(
                  text: _displayErrorText!,
                  style:
                      widget.errorStyle ?? context.templateStyle.text.errorText,
                ),
              )
              : const SizedBox.shrink(key: ValueKey('empty')),
    );
  }

  /// 获取默认容器装饰
  BoxDecoration _getDefaultContainerDecoration(BuildContext context) {
    return BoxDecoration(
      color: widget.fillColor ?? context.templateColors.inputBackground,
      borderRadius:
          widget.radius ?? BorderRadius.circular(UiConstants.borderRadius10),
      border: Border.all(
        color:
            _focusNode.hasFocus
                ? (widget.focusBorderColor ??
                    context.templateColors.inputFocusedBorder)
                : (widget.borderColor ?? context.templateColors.inputBorder),
        width: UiConstants.borderWidth1,
      ),
    );
  }

  /// 构建输入框装饰
  InputDecoration _buildInputDecoration(BuildContext context) {
    // 如果提供了自定义装饰，直接使用
    if (widget.decoration != null) {
      return widget.decoration!;
    }

    // 根据 hideSuffixOnFocus 参数决定是否显示后缀
    Widget? effectiveSuffixIcon = widget.suffixIcon;
    if (widget.hideSuffixOnFocus && _focusNode.hasFocus) {
      effectiveSuffixIcon = null;
    }

    return InputDecoration(
      labelText: widget.labelText,
      labelStyle: _getLabelStyle(context),
      hintText: widget.hintText,
      hintStyle: _getHintStyle(context),
      fillColor: Colors.transparent,
      filled: true,
      border: InputBorder.none,
      enabledBorder: InputBorder.none,
      focusedBorder: InputBorder.none,
      contentPadding: _getContentPadding(),
      prefix: widget.prefix,
      prefixIcon: widget.prefixIcon,
      prefixText: widget.prefixText,
      prefixStyle: widget.prefixStyle,
      prefixIconConstraints:
          widget.prefixIconConstraints ??
          BoxConstraints(minWidth: 0, minHeight: 0, maxWidth: 60),
      suffix: widget.suffix,
      suffixIcon: effectiveSuffixIcon,
      suffixText: widget.suffixText,
      suffixStyle: widget.suffixStyle,
      suffixIconConstraints:
          widget.suffixIconConstraints ??
          BoxConstraints(minWidth: 0, minHeight: 0),
      isDense: widget.isDense,
    );
  }

  // ========== 样式获取方法 ==========

  /// 获取标签样式
  ///
  /// 根据焦点状态和文本内容动态调整标签大小
  TextStyle _getLabelStyle(BuildContext context) {
    // 检查是否有焦点或者有输入内容
    final hasFocus = _focusNode.hasFocus;
    final hasText = _controller.text.isNotEmpty;

    return widget.labelStyle ??
        (hasFocus || hasText
            ? context.templateStyle.text.inputLabel.copyWith(
              fontSize: UiConstants.fontSize12,
              textBaseline: TextBaseline.alphabetic,
            )
            : context.templateStyle.text.inputLabel.copyWith(
              textBaseline: TextBaseline.alphabetic,
            ));
  }

  /// 获取提示文本样式
  TextStyle _getHintStyle(BuildContext context) {
    return widget.hintStyle ?? context.templateStyle.text.inputPlaceholder;
  }

  /// 获取内容边距
  EdgeInsetsGeometry _getContentPadding() {
    // 确保底部有足够的空间，防止文本被截断
    return widget.contentPadding ??
        EdgeInsets.symmetric(
          horizontal: UiConstants.spacing12,
          vertical: UiConstants.spacing8,
        );
  }

  /// 获取文本样式
  TextStyle _getTextStyle(BuildContext context) {
    return widget.textStyle ?? context.templateStyle.text.inputText;
  }
}
