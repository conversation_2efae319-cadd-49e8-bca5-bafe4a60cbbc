/*
 * 密码输入框组件
 *
 * 功能特性：
 * - 密码显示/隐藏切换
 * - 密码强度验证
 * - 支持自动填充
 * - 错误状态显示
 * - 适配模板主题系统
 */

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:remixicon/remixicon.dart';
import '../../core/index.dart';
import 'text_field_widget.dart';

/// 密码强度枚举
enum PasswordStrength {
  weak('弱'),
  medium('中'),
  strong('强');

  const PasswordStrength(this.label);

  final String label;

  /// 根据上下文获取对应的颜色
  Color getColor(BuildContext context) {
    switch (this) {
      case PasswordStrength.weak:
        return context.templateColors.error;
      case PasswordStrength.medium:
        return context.templateColors.warning;
      case PasswordStrength.strong:
        return context.templateColors.success;
    }
  }
}

class PasswordInputField extends StatefulWidget {
  /// 输入框控制器
  final TextEditingController? controller;

  /// 占位符文本
  final String? hintText;

  /// 标签文本
  final String? labelText;

  /// 是否启用
  final bool enabled;

  /// 是否只读
  final bool readOnly;

  /// 是否自动获取焦点
  final bool autofocus;

  /// 输入框高度
  final double? height;

  /// 输入框宽度
  final double? width;

  /// 文本样式
  final TextStyle? textStyle;

  /// 提示文本样式
  final TextStyle? hintStyle;

  /// 标签样式
  final TextStyle? labelStyle;

  /// 背景颜色
  final Color? fillColor;

  /// 边框颜色
  final Color? borderColor;

  /// 聚焦时边框颜色
  final Color? focusBorderColor;

  /// 圆角半径
  final BorderRadius? borderRadius;

  /// 内容边距
  final EdgeInsets? contentPadding;

  /// 外边距
  final EdgeInsets? margin;

  /// 内容变化回调
  final ValueChanged<String>? onChanged;

  /// 提交回调
  final ValueChanged<String>? onSubmitted;

  /// 编辑完成回调
  final VoidCallback? onEditingComplete;

  /// 点击回调
  final VoidCallback? onTap;

  /// 焦点节点
  final FocusNode? focusNode;

  /// 文本输入动作
  final TextInputAction? textInputAction;

  /// 是否显示密码可见性切换按钮
  final bool showVisibilityToggle;

  /// 是否显示清除按钮
  final bool showClearButton;

  /// 是否启用密码强度验证
  final bool enableStrengthValidation;

  /// 是否显示密码强度指示器
  final bool showStrengthIndicator;

  /// 最小密码长度
  final int minLength;

  /// 最大密码长度
  final int maxLength;

  /// 是否要求包含数字
  final bool requireNumbers;

  /// 是否要求包含字母
  final bool requireLetters;

  /// 是否要求包含特殊字符
  final bool requireSpecialChars;

  /// 自定义验证函数
  final String? Function(String?)? validator;

  /// 错误文本
  final String? errorText;

  /// 是否显示错误状态的边框
  final bool showErrorBorder;

  /// 密码强度变化回调
  final ValueChanged<PasswordStrength>? onStrengthChanged;

  const PasswordInputField({
    super.key,
    this.controller,
    this.hintText,
    this.labelText,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.height,
    this.width,
    this.textStyle,
    this.hintStyle,
    this.labelStyle,
    this.fillColor,
    this.borderColor,
    this.focusBorderColor,
    this.borderRadius,
    this.contentPadding,
    this.margin,
    this.onChanged,
    this.onSubmitted,
    this.onEditingComplete,
    this.onTap,
    this.focusNode,
    this.textInputAction,
    this.showVisibilityToggle = true,
    this.showClearButton = true,
    this.enableStrengthValidation = false,
    this.showStrengthIndicator = false,
    this.minLength = 6,
    this.maxLength = 20,
    this.requireNumbers = false,
    this.requireLetters = false,
    this.requireSpecialChars = false,
    this.validator,
    this.errorText,
    this.showErrorBorder = true,
    this.onStrengthChanged,
  });

  @override
  State<PasswordInputField> createState() => _PasswordInputFieldState();
}

class _PasswordInputFieldState extends State<PasswordInputField> {
  late TextEditingController _controller;
  bool _isPasswordVisible = false;
  String? _validationError;
  PasswordStrength? _currentStrength;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_onTextChanged);
    }
    super.dispose();
  }

  void _onTextChanged() {
    if (widget.enableStrengthValidation) {
      _validatePassword();
    }
    widget.onChanged?.call(_controller.text);
    setState(() {});
  }

  void _validatePassword() {
    final password = _controller.text;

    if (password.isEmpty) {
      _validationError = null;
      _currentStrength = null;
      widget.onStrengthChanged?.call(PasswordStrength.weak);
      return;
    }

    // 使用自定义验证器
    if (widget.validator != null) {
      _validationError = widget.validator!(password);
    } else {
      _validationError = _getDefaultValidationError(password);
    }

    // 计算密码强度
    _currentStrength = _calculatePasswordStrength(password);
    widget.onStrengthChanged?.call(_currentStrength!);
  }

  String? _getDefaultValidationError(String password) {
    if (password.length < widget.minLength) {
      return '密码长度至少${widget.minLength}位';
    }

    if (password.length > widget.maxLength) {
      return '密码长度不能超过${widget.maxLength}位';
    }

    if (widget.requireNumbers && !RegExp(r'\d').hasMatch(password)) {
      return '密码必须包含数字';
    }

    if (widget.requireLetters && !RegExp(r'[a-zA-Z]').hasMatch(password)) {
      return '密码必须包含字母';
    }

    if (widget.requireSpecialChars &&
        !RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) {
      return '密码必须包含特殊字符';
    }

    return null;
  }

  PasswordStrength _calculatePasswordStrength(String password) {
    int score = 0;

    // 长度评分
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;

    // 字符类型评分
    if (RegExp(r'[a-z]').hasMatch(password)) score += 1;
    if (RegExp(r'[A-Z]').hasMatch(password)) score += 1;
    if (RegExp(r'\d').hasMatch(password)) score += 1;
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) score += 1;

    // 根据评分返回强度
    if (score <= 2) return PasswordStrength.weak;
    if (score <= 4) return PasswordStrength.medium;
    return PasswordStrength.strong;
  }

  void _togglePasswordVisibility() {
    setState(() {
      _isPasswordVisible = !_isPasswordVisible;
    });
  }

  Widget? _buildSuffixIcon() {
    final List<Widget> icons = [];

    // 清除按钮
    if (widget.showClearButton && _controller.text.isNotEmpty) {
      icons.add(
        GestureDetector(
          onTap: () {
            _controller.clear();
            widget.onChanged?.call('');
          },
          child: Icon(
            RemixIcons.close_circle_fill,
            size: UiConstants.iconSize20,
            color: context.templateColors.textTertiary,
          ),
        ),
      );
    }

    // 密码可见性切换按钮
    if (widget.showVisibilityToggle) {
      if (icons.isNotEmpty) {
        icons.add(SizedBox(width: UiConstants.spacing8));
      }
      icons.add(
        GestureDetector(
          onTap: _togglePasswordVisibility,
          child: Icon(
            _isPasswordVisible
                ? RemixIcons.eye_close_fill
                : RemixIcons.eye_fill,
            size: UiConstants.iconSize20,
            color: context.templateColors.textTertiary,
          ),
        ),
      );
    }

    if (icons.isEmpty) return null;

    return Padding(
      padding: EdgeInsets.only(right: UiConstants.spacing12),
      child: Row(mainAxisSize: MainAxisSize.min, children: icons),
    );
  }

  String? _getEffectiveErrorText() {
    return widget.errorText ?? _validationError;
  }

  Color? _getEffectiveBorderColor() {
    if (!widget.showErrorBorder) return widget.borderColor;

    final errorText = _getEffectiveErrorText();
    if (errorText != null) {
      return context.templateColors.error;
    }
    return widget.borderColor;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFieldWidget(
          controller: _controller,
          hintText: widget.hintText ?? '密码',
          labelText: widget.labelText,
          enabled: widget.enabled,
          readOnly: widget.readOnly,
          autofocus: widget.autofocus,
          height: widget.height ?? UiConstants.spacing48 + UiConstants.spacing2,
          width: widget.width,
          textStyle: widget.textStyle ?? context.templateStyle.text.inputText,
          hintStyle:
              widget.hintStyle ?? context.templateStyle.text.inputPlaceholder,
          labelStyle:
              widget.labelStyle ?? context.templateStyle.text.inputLabel,
          fillColor: widget.fillColor ?? context.templateColors.inputBackground,
          borderColor: _getEffectiveBorderColor(),
          focusBorderColor:
              widget.focusBorderColor ??
              context.templateColors.inputFocusedBorder,
          radius: widget.borderRadius,
          contentPadding: widget.contentPadding,
          margin: widget.margin,
          onSubmitted: widget.onSubmitted,
          onEditingComplete: widget.onEditingComplete,
          onTap: widget.onTap,
          focusNode: widget.focusNode,
          keyboardType: TextInputType.visiblePassword,
          textInputAction: widget.textInputAction ?? TextInputAction.done,
          autofillHints: const [AutofillHints.password],
          obscureText: !_isPasswordVisible,
          suffixIcon: _buildSuffixIcon(),
          suffixIconConstraints: BoxConstraints(maxWidth: 120),
          errorText: _getEffectiveErrorText(),
          inputFormatters: [LengthLimitingTextInputFormatter(widget.maxLength)],
        ),
      ],
    );
  }
}
