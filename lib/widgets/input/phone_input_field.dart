/*
 * 手机号输入框组件
 *
 * 功能特性：
 * - 自动手机号格式验证
 * - 支持国际区号选择
 * - 支持自动填充
 * - 内置清除按钮
 * - 错误状态显示
 * - 适配模板主题系统
 */

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qubic_exchange/widgets/common/ink_well_widget.dart';
import 'package:qubic_exchange/widgets/display/themed_image.dart';
import 'package:qubic_exchange/routes/index.dart';
import 'package:qubic_exchange/data/country_code_data.dart' as country_data;
import 'package:remixicon/remixicon.dart';
import '../../core/index.dart';
import 'text_field_widget.dart';

/// 国家代码数据模型
class CountryCode {
  final String code;
  final String name;
  final String flag;

  const CountryCode({
    required this.code,
    required this.name,
    required this.flag,
  });

  static const List<CountryCode> defaultCountries = [
    CountryCode(code: '+86', name: '中国', flag: '🇨🇳'),
    CountryCode(code: '+1', name: '美国', flag: '🇺🇸'),
    CountryCode(code: '+44', name: '英国', flag: '🇬🇧'),
    CountryCode(code: '+81', name: '日本', flag: '🇯🇵'),
    CountryCode(code: '+82', name: '韩国', flag: '🇰🇷'),
    CountryCode(code: '+65', name: '新加坡', flag: '🇸🇬'),
    CountryCode(code: '+852', name: '香港', flag: '🇭🇰'),
    CountryCode(code: '+886', name: '台湾', flag: '🇹🇼'),
  ];
}

class PhoneInputField extends StatefulWidget {
  /// 输入框控制器
  final TextEditingController? controller;

  /// 占位符文本
  final String? hintText;

  /// 标签文本
  final String? labelText;

  /// 是否启用
  final bool enabled;

  /// 是否只读
  final bool readOnly;

  /// 是否自动获取焦点
  final bool autofocus;

  /// 输入框高度
  final double? height;

  /// 输入框宽度
  final double? width;

  /// 文本样式
  final TextStyle? textStyle;

  /// 提示文本样式
  final TextStyle? hintStyle;

  /// 标签样式
  final TextStyle? labelStyle;

  /// 背景颜色
  final Color? fillColor;

  /// 边框颜色
  final Color? borderColor;

  /// 聚焦时边框颜色
  final Color? focusBorderColor;

  /// 圆角半径
  final BorderRadius? borderRadius;

  /// 内容边距
  final EdgeInsets? contentPadding;

  /// 外边距
  final EdgeInsets? margin;

  /// 内容变化回调
  final ValueChanged<String>? onChanged;

  /// 提交回调
  final ValueChanged<String>? onSubmitted;

  /// 编辑完成回调
  final VoidCallback? onEditingComplete;

  /// 点击回调
  final VoidCallback? onTap;

  /// 焦点节点
  final FocusNode? focusNode;

  /// 文本输入动作
  final TextInputAction? textInputAction;

  /// 是否显示清除按钮
  final bool showClearButton;

  /// 是否启用自动验证
  final bool enableValidation;

  /// 自定义验证函数
  final String? Function(String?)? validator;

  /// 错误文本
  final String? errorText;

  /// 是否显示错误状态的边框
  final bool showErrorBorder;

  /// 是否显示国家代码选择器
  final bool showCountryCode;

  /// 默认国家代码
  final String defaultCountryCode;

  /// 国家代码变化回调
  final ValueChanged<String>? onCountryCodeChanged;

  /// 支持的国家代码列表
  final List<CountryCode>? supportedCountries;

  const PhoneInputField({
    super.key,
    this.controller,
    this.hintText,
    this.labelText,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.height,
    this.width,
    this.textStyle,
    this.hintStyle,
    this.labelStyle,
    this.fillColor,
    this.borderColor,
    this.focusBorderColor,
    this.borderRadius,
    this.contentPadding,
    this.margin,
    this.onChanged,
    this.onSubmitted,
    this.onEditingComplete,
    this.onTap,
    this.focusNode,
    this.textInputAction,
    this.showClearButton = true,
    this.enableValidation = true,
    this.validator,
    this.errorText,
    this.showErrorBorder = true,
    this.showCountryCode = true,
    this.defaultCountryCode = '+86',
    this.onCountryCodeChanged,
    this.supportedCountries,
  });

  @override
  State<PhoneInputField> createState() => _PhoneInputFieldState();
}

class _PhoneInputFieldState extends State<PhoneInputField> {
  late TextEditingController _controller;
  late String _selectedCountryCode;
  String? _validationError;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _selectedCountryCode = widget.defaultCountryCode;
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_onTextChanged);
    }
    super.dispose();
  }

  void _onTextChanged() {
    if (widget.enableValidation) {
      _validatePhone();
    }
    widget.onChanged?.call(_controller.text);
    setState(() {});
  }

  void _validatePhone() {
    final text = _controller.text.trim();
    if (text.isEmpty) {
      _validationError = null;
      return;
    }

    // 使用自定义验证器
    if (widget.validator != null) {
      _validationError = widget.validator!(text);
      return;
    }

    // 默认手机号格式验证
    if (!_isValidPhone(text)) {
      _validationError = '请输入有效的手机号码';
    } else {
      _validationError = null;
    }
  }

  bool _isValidPhone(String phone) {
    // 移除所有非数字字符
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');

    // 根据国家代码验证
    switch (_selectedCountryCode) {
      case '+86':
        // 中国手机号：11位，以1开头
        return RegExp(r'^1[3-9]\d{9}$').hasMatch(cleanPhone);
      case '+1':
        // 美国手机号：10位
        return cleanPhone.length == 10;
      case '+44':
        // 英国手机号：10-11位
        return cleanPhone.length >= 10 && cleanPhone.length <= 11;
      default:
        // 其他国家：6-15位数字
        return cleanPhone.length >= 6 && cleanPhone.length <= 15;
    }
  }

  void _clearText() {
    _controller.clear();
    _validationError = null;
    widget.onChanged?.call('');
    setState(() {});
  }

  void _showCountryCodePicker() async {
    // 跳转到国家代码选择页面
    final result = await NavigationService().navigateTo(AppRoutes.countryCode);

    // 如果用户选择了国家代码，更新当前选择
    if (result != null && result is country_data.CountryCode) {
      setState(() {
        _selectedCountryCode = result.dialCode;
      });
      widget.onCountryCodeChanged?.call(result.dialCode);
    }
  }

  Widget? _buildPrefixIcon() {
    if (!widget.showCountryCode) return null;

    final selectedCountry =
        (widget.supportedCountries ?? CountryCode.defaultCountries).firstWhere(
          (country) => country.code == _selectedCountryCode,
          orElse: () => CountryCode.defaultCountries.first,
        );

    return InkWellWidget(
      onTap: widget.enabled ? _showCountryCodePicker : null,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing12),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _selectedCountryCode,
              style: context.templateStyle.text.bodyLargeMedium,
            ),
            SizedBox(width: UiConstants.spacing8),
            ThemedImage.asset(
              'arrow_triangle_down',
              size: UiConstants.iconSize10,
              followTheme: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget? _buildSuffixIcon() {
    if (!widget.showClearButton || _controller.text.isEmpty) {
      return null;
    }

    return Padding(
      padding: EdgeInsets.only(right: UiConstants.spacing12),
      child: InkWellWidget(
        onTap: _clearText,
        child: Icon(
          RemixIcons.close_circle_fill,
          size: UiConstants.iconSize16,
          color: context.templateColors.textTertiary,
        ),
      ),
    );
  }

  String? _getEffectiveErrorText() {
    return widget.errorText ?? _validationError;
  }

  Color? _getEffectiveBorderColor() {
    if (!widget.showErrorBorder) return widget.borderColor;

    final errorText = _getEffectiveErrorText();
    if (errorText != null) {
      return context.templateColors.error;
    }
    return widget.borderColor;
  }

  @override
  Widget build(BuildContext context) {
    return TextFieldWidget(
      controller: _controller,
      hintText: widget.hintText ?? '请输入手机号码',
      labelText: widget.labelText,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      autofocus: widget.autofocus,
      height: widget.height ?? UiConstants.spacing48 + UiConstants.spacing2,
      width: widget.width,
      textStyle: widget.textStyle ?? context.templateStyle.text.inputText,
      hintStyle:
          widget.hintStyle ?? context.templateStyle.text.inputPlaceholder,
      labelStyle: widget.labelStyle ?? context.templateStyle.text.inputLabel,
      fillColor: widget.fillColor ?? context.templateColors.inputBackground,
      borderColor: _getEffectiveBorderColor(),
      focusBorderColor:
          widget.focusBorderColor ?? context.templateColors.inputFocusedBorder,
      radius: widget.borderRadius,
      contentPadding: widget.contentPadding,
      margin: widget.margin,
      onSubmitted: widget.onSubmitted,
      onEditingComplete: widget.onEditingComplete,
      onTap: widget.onTap,
      focusNode: widget.focusNode,
      keyboardType: TextInputType.phone,
      textInputAction: widget.textInputAction ?? TextInputAction.next,
      autofillHints: const [AutofillHints.telephoneNumber],
      prefixIcon: _buildPrefixIcon(),
      suffixIcon: _buildSuffixIcon(),
      errorText: _getEffectiveErrorText(),
      suffixIconConstraints: BoxConstraints(maxWidth: 80),
      prefixIconConstraints: BoxConstraints(maxWidth: 90),
      inputFormatters: [
        // 手机号格式化器
        PhoneNumberFormatter(_selectedCountryCode),
      ],
    );
  }
}

/// 手机号格式化器
class PhoneNumberFormatter extends TextInputFormatter {
  final String countryCode;

  PhoneNumberFormatter(this.countryCode);

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // 根据国家代码进行不同的格式化
    switch (countryCode) {
      case '+86':
        return _formatChinaPhone(oldValue, newValue);
      case '+1':
        return _formatUSPhone(oldValue, newValue);
      default:
        return newValue;
    }
  }

  /// 格式化中国手机号：138 1234 5678
  TextEditingValue _formatChinaPhone(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text.replaceAll(RegExp(r'[^\d]'), '');

    if (text.length > 11) {
      return oldValue;
    }

    String formatted = '';
    for (int i = 0; i < text.length; i++) {
      if (i == 3 || i == 7) {
        formatted += ' ';
      }
      formatted += text[i];
    }

    return newValue.copyWith(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }

  /// 格式化美国手机号：(*************
  TextEditingValue _formatUSPhone(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text.replaceAll(RegExp(r'[^\d]'), '');

    if (text.length > 10) {
      return oldValue;
    }

    String formatted = '';
    for (int i = 0; i < text.length; i++) {
      if (i == 0) {
        formatted += '(';
      } else if (i == 3) {
        formatted += ') ';
      } else if (i == 6) {
        formatted += '-';
      }
      formatted += text[i];
    }

    return newValue.copyWith(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
