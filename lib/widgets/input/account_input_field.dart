/*
 * 账户输入框组件
 *
 * 功能特性：
 * - 支持邮箱和手机号两种输入模式
 * - 自动检测输入类型或手动指定
 * - 整合邮箱和手机号的所有验证功能
 * - 支持国际区号选择（手机号模式）
 * - 支持自动填充和清除按钮
 * - 错误状态显示和自定义验证
 * - 适配模板主题系统
 */

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/routes/index.dart';
import 'package:qubic_exchange/data/country_code_data.dart' as country_data;
import 'package:remixicon/remixicon.dart';
import '../../core/index.dart';

/// 账户输入类型枚举
enum AccountInputType {
  auto, // 自动检测
  email, // 邮箱模式
  phone, // 手机号模式
}

class AccountInputField extends StatefulWidget {
  /// 输入框控制器
  final TextEditingController? controller;

  /// 输入类型（自动检测、邮箱、手机号）
  final AccountInputType inputType;

  /// 占位符文本
  final String? hintText;

  /// 标签文本
  final String? labelText;

  /// 是否启用
  final bool enabled;

  /// 是否只读
  final bool readOnly;

  /// 是否自动获取焦点
  final bool autofocus;

  /// 输入框高度
  final double? height;

  /// 输入框宽度
  final double? width;

  /// 文本样式
  final TextStyle? textStyle;

  /// 提示文本样式
  final TextStyle? hintStyle;

  /// 标签样式
  final TextStyle? labelStyle;

  /// 背景颜色
  final Color? fillColor;

  /// 边框颜色
  final Color? borderColor;

  /// 聚焦时边框颜色
  final Color? focusBorderColor;

  /// 圆角半径
  final BorderRadius? borderRadius;

  /// 内容边距
  final EdgeInsets? contentPadding;

  /// 外边距
  final EdgeInsets? margin;

  /// 内容变化回调
  final ValueChanged<String>? onChanged;

  /// 提交回调
  final ValueChanged<String>? onSubmitted;

  /// 编辑完成回调
  final VoidCallback? onEditingComplete;

  /// 点击回调
  final VoidCallback? onTap;

  /// 焦点节点
  final FocusNode? focusNode;

  /// 文本输入动作
  final TextInputAction? textInputAction;

  /// 是否显示清除按钮
  final bool showClearButton;

  /// 是否启用自动验证
  final bool enableValidation;

  /// 自定义验证函数
  final String? Function(String?)? validator;

  /// 错误文本
  final String? errorText;

  /// 是否显示错误状态的边框
  final bool showErrorBorder;

  /// 是否显示国家代码选择器（手机号模式）
  final bool showCountryCode;

  /// 默认国家代码
  final String defaultCountryCode;

  /// 国家代码变化回调
  final ValueChanged<String>? onCountryCodeChanged;

  /// 支持的国家代码列表
  final List<CountryCode>? supportedCountries;

  /// 输入类型变化回调（当自动检测到类型变化时）
  final ValueChanged<AccountInputType>? onInputTypeChanged;

  const AccountInputField({
    super.key,
    this.controller,
    this.inputType = AccountInputType.auto,
    this.hintText,
    this.labelText,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.height,
    this.width,
    this.textStyle,
    this.hintStyle,
    this.labelStyle,
    this.fillColor,
    this.borderColor,
    this.focusBorderColor,
    this.borderRadius,
    this.contentPadding,
    this.margin,
    this.onChanged,
    this.onSubmitted,
    this.onEditingComplete,
    this.onTap,
    this.focusNode,
    this.textInputAction,
    this.showClearButton = true,
    this.enableValidation = true,
    this.validator,
    this.errorText,
    this.showErrorBorder = true,
    this.showCountryCode = true,
    this.defaultCountryCode = '+86',
    this.onCountryCodeChanged,
    this.supportedCountries,
    this.onInputTypeChanged,
  });

  @override
  State<AccountInputField> createState() => _AccountInputFieldState();
}

class _AccountInputFieldState extends State<AccountInputField> {
  late TextEditingController _controller;
  late String _selectedCountryCode;
  String? _validationError;
  AccountInputType _detectedType = AccountInputType.auto;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _selectedCountryCode = widget.defaultCountryCode;
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_onTextChanged);
    }
    super.dispose();
  }

  void _onTextChanged() {
    _detectInputType();
    if (widget.enableValidation) {
      _validateInput();
    }
    widget.onChanged?.call(_controller.text);
    setState(() {});
  }

  /// 自动检测输入类型
  void _detectInputType() {
    if (widget.inputType != AccountInputType.auto) {
      _detectedType = widget.inputType;
      return;
    }

    final text = _controller.text.trim();
    if (text.isEmpty) {
      _detectedType = AccountInputType.auto;
      return;
    }

    final previousType = _detectedType;

    // 检测邮箱格式：包含@符号
    if (text.contains('@')) {
      _detectedType = AccountInputType.email;
    }
    // 检测手机号格式：纯数字或以+开头
    else if (RegExp(r'^[\+\d\s\-\(\)]+$').hasMatch(text)) {
      _detectedType = AccountInputType.phone;
    }
    // 默认为自动检测状态
    else {
      _detectedType = AccountInputType.auto;
    }

    // 如果类型发生变化，通知外部
    if (previousType != _detectedType) {
      widget.onInputTypeChanged?.call(_detectedType);
    }
  }

  /// 获取当前有效的输入类型
  AccountInputType _getEffectiveInputType() {
    return widget.inputType == AccountInputType.auto
        ? _detectedType
        : widget.inputType;
  }

  void _validateInput() {
    final text = _controller.text.trim();
    if (text.isEmpty) {
      _validationError = null;
      return;
    }

    // 使用自定义验证器
    if (widget.validator != null) {
      _validationError = widget.validator!(text);
      return;
    }

    final effectiveType = _getEffectiveInputType();

    switch (effectiveType) {
      case AccountInputType.email:
        _validationError = _validateEmail(text);
        break;
      case AccountInputType.phone:
        _validationError = _validatePhone(text);
        break;
      case AccountInputType.auto:
        // 自动模式下，尝试两种验证
        final emailError = _validateEmail(text);
        final phoneError = _validatePhone(text);

        // 如果两种都无效，显示通用错误
        if (emailError != null && phoneError != null) {
          _validationError = '请输入有效的邮箱地址或手机号码';
        } else {
          _validationError = null;
        }
        break;
    }
  }

  String? _validateEmail(String email) {
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      return '请输入有效的邮箱地址';
    }
    return null;
  }

  String? _validatePhone(String phone) {
    // 移除所有非数字字符
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');

    // 根据国家代码验证
    switch (_selectedCountryCode) {
      case '+86':
        // 中国手机号：11位，以1开头
        if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(cleanPhone)) {
          return '请输入有效的手机号码';
        }
        break;
      case '+1':
        // 美国手机号：10位
        if (cleanPhone.length != 10) {
          return '请输入有效的手机号码';
        }
        break;
      case '+44':
        // 英国手机号：10-11位
        if (cleanPhone.length < 10 || cleanPhone.length > 11) {
          return '请输入有效的手机号码';
        }
        break;
      default:
        // 其他国家：6-15位数字
        if (cleanPhone.length < 6 || cleanPhone.length > 15) {
          return '请输入有效的手机号码';
        }
    }
    return null;
  }

  void _clearText() {
    _controller.clear();
    _validationError = null;
    _detectedType = AccountInputType.auto;
    widget.onChanged?.call('');
    setState(() {});
  }

  void _showCountryCodePicker() async {
    // 跳转到国家代码选择页面
    final result = await NavigationService().navigateTo(AppRoutes.countryCode);

    // 如果用户选择了国家代码，更新当前选择
    if (result != null && result is country_data.CountryCode) {
      setState(() {
        _selectedCountryCode = result.dialCode;
      });
      widget.onCountryCodeChanged?.call(result.dialCode);
    }
  }

  Widget? _buildPrefixIcon() {
    final effectiveType = _getEffectiveInputType();

    // 只在手机号模式下显示国家代码选择器
    if (effectiveType != AccountInputType.phone || !widget.showCountryCode) {
      return null;
    }

    final selectedCountry =
        (widget.supportedCountries ?? CountryCode.defaultCountries).firstWhere(
          (country) => country.code == _selectedCountryCode,
          orElse: () => CountryCode.defaultCountries.first,
        );

    return GestureDetector(
      onTap: widget.enabled ? _showCountryCodePicker : null,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing12),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(selectedCountry.flag, style: const TextStyle(fontSize: 20)),
            SizedBox(width: UiConstants.spacing4),
            Text(
              _selectedCountryCode,
              style: context.templateStyle.text.bodyTextMedium,
            ),
            SizedBox(width: UiConstants.spacing4),
            Icon(
              Icons.arrow_drop_down,
              size: UiConstants.iconSize16,
              color: context.templateColors.textSecondary,
            ),
          ],
        ),
      ),
    );
  }

  Widget? _buildSuffixIcon() {
    if (!widget.showClearButton || _controller.text.isEmpty) {
      return null;
    }

    return Padding(
      padding: EdgeInsets.only(right: UiConstants.spacing12),
      child: IconButtonWidget(
        icon: RemixIcons.close_circle_fill,
        size: UiConstants.iconSize16,
        iconColor: context.templateColors.textTertiary,
        onTap: _clearText,
      ),
    );
  }

  String? _getEffectiveErrorText() {
    return widget.errorText ?? _validationError;
  }

  Color? _getEffectiveBorderColor() {
    if (!widget.showErrorBorder) return widget.borderColor;

    final errorText = _getEffectiveErrorText();
    if (errorText != null) {
      return context.templateColors.error;
    }
    return widget.borderColor;
  }

  /// 获取键盘类型
  TextInputType _getKeyboardType() {
    final effectiveType = _getEffectiveInputType();
    switch (effectiveType) {
      case AccountInputType.email:
        return TextInputType.emailAddress;
      case AccountInputType.phone:
        return TextInputType.phone;
      case AccountInputType.auto:
        return TextInputType.text;
    }
  }

  /// 获取自动填充提示
  List<String> _getAutofillHints() {
    final effectiveType = _getEffectiveInputType();
    switch (effectiveType) {
      case AccountInputType.email:
        return [AutofillHints.email];
      case AccountInputType.phone:
        return [AutofillHints.telephoneNumber];
      case AccountInputType.auto:
        return [AutofillHints.email, AutofillHints.telephoneNumber];
    }
  }

  /// 获取占位符文本
  String _getEffectiveHintText() {
    if (widget.hintText != null) return widget.hintText!;

    final effectiveType = _getEffectiveInputType();
    switch (effectiveType) {
      case AccountInputType.email:
        return '请输入邮箱地址';
      case AccountInputType.phone:
        return '请输入手机号码';
      case AccountInputType.auto:
        return '请输入邮箱地址或手机号码';
    }
  }

  /// 获取输入格式化器
  List<TextInputFormatter> _getInputFormatters() {
    final effectiveType = _getEffectiveInputType();
    switch (effectiveType) {
      case AccountInputType.email:
        return [
          // 移除空格
          // FilteringTextInputFormatter.deny(RegExp(r'\s')),
          // 转换为小写
          LowerCaseTextFormatter(),
        ];
      case AccountInputType.phone:
        return [
          // 只允许数字、空格、短横线、括号
          // FilteringTextInputFormatter.allow(RegExp(r'[\d\s\-\(\)]')),
          // 手机号格式化器
          PhoneNumberFormatter(_selectedCountryCode),
        ];
      case AccountInputType.auto:
        return [
          // 自动模式下不做特殊格式化，保持用户输入
        ];
    }
  }

  @override
  Widget build(BuildContext context) {
    return TextFieldWidget(
      controller: _controller,
      hintText: _getEffectiveHintText(),
      labelText: widget.labelText,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      autofocus: widget.autofocus,
      height: widget.height ?? UiConstants.spacing48 + UiConstants.spacing2,
      width: widget.width,
      textStyle: widget.textStyle ?? context.templateStyle.text.inputText,
      hintStyle:
          widget.hintStyle ?? context.templateStyle.text.inputPlaceholder,
      labelStyle: widget.labelStyle ?? context.templateStyle.text.inputLabel,
      fillColor: widget.fillColor ?? context.templateColors.inputBackground,
      borderColor: _getEffectiveBorderColor(),
      focusBorderColor:
          widget.focusBorderColor ?? context.templateColors.inputFocusedBorder,
      radius: widget.borderRadius,
      contentPadding: widget.contentPadding,
      margin: widget.margin,
      onSubmitted: widget.onSubmitted,
      onEditingComplete: widget.onEditingComplete,
      onTap: widget.onTap,
      focusNode: widget.focusNode,
      keyboardType: _getKeyboardType(),
      textInputAction: widget.textInputAction ?? TextInputAction.next,
      autofillHints: _getAutofillHints(),
      prefixIcon: _buildPrefixIcon(),
      suffixIcon: _buildSuffixIcon(),
      errorText: _getEffectiveErrorText(),
      inputFormatters: _getInputFormatters(),
    );
  }
}
