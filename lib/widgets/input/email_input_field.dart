/*
 * 邮箱输入框组件
 *
 * 功能特性：
 * - 自动邮箱格式验证
 * - 支持自动填充
 * - 内置清除按钮
 * - 错误状态显示
 * - 适配模板主题系统
 */

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';
import '../../core/index.dart';

class EmailInputField extends StatefulWidget {
  /// 输入框控制器
  final TextEditingController? controller;

  /// 占位符文本
  final String? hintText;

  /// 标签文本
  final String? labelText;

  /// 是否启用
  final bool enabled;

  /// 是否只读
  final bool readOnly;

  /// 是否自动获取焦点
  final bool autofocus;

  /// 输入框高度
  final double? height;

  /// 输入框宽度
  final double? width;

  /// 文本样式
  final TextStyle? textStyle;

  /// 提示文本样式
  final TextStyle? hintStyle;

  /// 标签样式
  final TextStyle? labelStyle;

  /// 背景颜色
  final Color? fillColor;

  /// 边框颜色
  final Color? borderColor;

  /// 聚焦时边框颜色
  final Color? focusBorderColor;

  /// 圆角半径
  final BorderRadius? borderRadius;

  /// 内容边距
  final EdgeInsets? contentPadding;

  /// 外边距
  final EdgeInsets? margin;

  /// 内容变化回调
  final ValueChanged<String>? onChanged;

  /// 提交回调
  final ValueChanged<String>? onSubmitted;

  /// 编辑完成回调
  final VoidCallback? onEditingComplete;

  /// 点击回调
  final VoidCallback? onTap;

  /// 焦点节点
  final FocusNode? focusNode;

  /// 文本输入动作
  final TextInputAction? textInputAction;

  /// 是否显示清除按钮
  final bool showClearButton;

  /// 是否启用自动验证
  final bool enableValidation;

  /// 自定义验证函数
  final String? Function(String?)? validator;

  /// 错误文本
  final String? errorText;

  /// 是否显示错误状态的边框
  final bool showErrorBorder;

  const EmailInputField({
    super.key,
    this.controller,
    this.hintText,
    this.labelText,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.height,
    this.width,
    this.textStyle,
    this.hintStyle,
    this.labelStyle,
    this.fillColor,
    this.borderColor,
    this.focusBorderColor,
    this.borderRadius,
    this.contentPadding,
    this.margin,
    this.onChanged,
    this.onSubmitted,
    this.onEditingComplete,
    this.onTap,
    this.focusNode,
    this.textInputAction,
    this.showClearButton = true,
    this.enableValidation = true,
    this.validator,
    this.errorText,
    this.showErrorBorder = true,
  });

  @override
  State<EmailInputField> createState() => _EmailInputFieldState();
}

class _EmailInputFieldState extends State<EmailInputField> {
  late TextEditingController _controller;
  String? _validationError;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_onTextChanged);
    }
    super.dispose();
  }

  void _onTextChanged() {
    if (widget.enableValidation) {
      _validateEmail();
    }
    widget.onChanged?.call(_controller.text);
    setState(() {});
  }

  void _validateEmail() {
    final text = _controller.text.trim();
    if (text.isEmpty) {
      _validationError = null;
      return;
    }

    // 使用自定义验证器
    if (widget.validator != null) {
      _validationError = widget.validator!(text);
      return;
    }

    // 默认邮箱格式验证
    if (!_isValidEmail(text)) {
      _validationError = '请输入有效的邮箱地址';
    } else {
      _validationError = null;
    }
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  void _clearText() {
    _controller.clear();
    _validationError = null;
    widget.onChanged?.call('');
    setState(() {});
  }

  Widget? _buildSuffixIcon() {
    if (!widget.showClearButton || _controller.text.isEmpty) {
      return null;
    }

    return Padding(
      padding: EdgeInsets.only(right: UiConstants.spacing12),
      child: IconButtonWidget(
        icon: RemixIcons.close_circle_fill,
        size: UiConstants.iconSize16,
        iconColor: context.templateColors.textTertiary,
        onTap: _clearText,
      ),
    );
  }

  String? _getEffectiveErrorText() {
    return widget.errorText ?? _validationError;
  }

  Color? _getEffectiveBorderColor() {
    if (!widget.showErrorBorder) return widget.borderColor;

    final errorText = _getEffectiveErrorText();
    if (errorText != null) {
      return context.templateColors.error;
    }
    return widget.borderColor;
  }

  @override
  Widget build(BuildContext context) {
    return TextFieldWidget(
      controller: _controller,
      hintText: widget.hintText ?? '请输入邮箱地址',
      labelText: widget.labelText,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      autofocus: widget.autofocus,
      height: widget.height ?? UiConstants.spacing48 + UiConstants.spacing2,
      width: widget.width,
      textStyle: widget.textStyle ?? context.templateStyle.text.inputText,
      hintStyle:
          widget.hintStyle ?? context.templateStyle.text.inputPlaceholder,
      labelStyle: widget.labelStyle ?? context.templateStyle.text.inputLabel,
      fillColor: widget.fillColor ?? context.templateColors.inputBackground,
      borderColor: _getEffectiveBorderColor(),
      focusBorderColor:
          widget.focusBorderColor ?? context.templateColors.inputFocusedBorder,
      radius: widget.borderRadius,
      contentPadding: widget.contentPadding,
      margin: widget.margin,
      onSubmitted: widget.onSubmitted,
      onEditingComplete: widget.onEditingComplete,
      onTap: widget.onTap,
      focusNode: widget.focusNode,
      keyboardType: TextInputType.emailAddress,
      textInputAction: widget.textInputAction ?? TextInputAction.next,
      autofillHints: const [AutofillHints.email],
      suffixIcon: _buildSuffixIcon(),
      errorText: _getEffectiveErrorText(),
      inputFormatters: [
        // 移除空格
        // FilteringTextInputFormatter.deny(RegExp(r'\s')),
        // 转换为小写
        LowerCaseTextFormatter(),
      ],
    );
  }
}

/// 小写文本格式化器
class LowerCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    return newValue.copyWith(text: newValue.text.toLowerCase());
  }
}
