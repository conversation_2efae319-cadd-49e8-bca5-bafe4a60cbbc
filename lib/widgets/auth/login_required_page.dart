import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/index.dart';
import '../../providers/auth_provider.dart';
import '../../services/pages/auth/index.dart';

import '../index.dart';

/// 需要登录的页面装饰器
///
/// 用于包装需要登录才能访问的页面
class LoginRequiredPage extends StatelessWidget {
  final Widget child;
  final String? title;
  final String? message;
  final Widget? customLoginPrompt;
  final bool showAppBar;
  final String? redirectRoute;
  final Map<String, dynamic>? redirectArguments;

  const LoginRequiredPage({
    super.key,
    required this.child,
    this.title,
    this.message,
    this.customLoginPrompt,
    this.showAppBar = true,
    this.redirectRoute,
    this.redirectArguments,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        if (LoginStatusService.isLoggedInStatic(authProvider)) {
          return child;
        }

        // 未登录时显示登录提示页面
        return _buildLoginPromptPage(context);
      },
    );
  }

  /// 构建登录提示页面
  Widget _buildLoginPromptPage(BuildContext context) {
    if (customLoginPrompt != null) {
      return customLoginPrompt!;
    }

    return Scaffold(
      backgroundColor: context.templateColors.surface,
      appBar:
          showAppBar
              ? AppBarWidget(title: title ?? '需要登录', showBackButton: true)
              : null,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(UiConstants.spacing24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 登录图标
              Icon(
                Icons.lock_outline,
                size: 80,
                color: context.templateColors.textSecondary,
              ),

              SizedBox(height: UiConstants.spacing24),

              // 标题
              Text(
                title ?? '需要登录',
                style: context.templateStyle.text.h2,
                textAlign: TextAlign.center,
              ),

              SizedBox(height: UiConstants.spacing16),

              // 提示信息
              Text(
                message ?? '此页面需要登录后才能访问\n请先登录您的账户',
                style: context.templateStyle.text.bodyText.copyWith(
                  color: context.templateColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: UiConstants.spacing32),

              // 登录按钮
              CommonButton.primary(
                '立即登录',
                width: double.infinity,
                height: UiConstants.buttonHeightMedium,
                onPressed: () => _handleLoginTap(context),
              ),

              SizedBox(height: UiConstants.spacing16),

              // 返回按钮
              CommonButton.secondary(
                '返回',
                width: double.infinity,
                height: UiConstants.buttonHeightMedium,
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理登录按钮点击
  void _handleLoginTap(BuildContext context) {
    LoginStatusService.requireLogin(
      context,
      redirectRoute: redirectRoute,
      redirectArguments: redirectArguments,
    );
  }
}

/// 需要登录的功能按钮
///
/// 点击时检查登录状态，未登录则提示登录
class LoginRequiredButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final String? loginPromptMessage;
  final CommonButtonType type;
  final double? width;
  final double? height;
  final bool enabled;

  const LoginRequiredButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.loginPromptMessage,
    this.type = CommonButtonType.primary,
    this.width,
    this.height,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return CommonButton(
      text: text,
      type: type,
      width: width,
      height: height,
      disabled: !enabled,
      onPressed: enabled ? () => _handleTap(context) : null,
    );
  }

  void _handleTap(BuildContext context) {
    if (LoginStatusService.isLoggedIn(context)) {
      onPressed();
    } else {
      _showLoginPrompt(context);
    }
  }

  void _showLoginPrompt(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('需要登录'),
          content: Text(loginPromptMessage ?? '此功能需要登录后才能使用，是否前往登录？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                LoginStatusService.requireLogin(context);
              },
              child: const Text('去登录'),
            ),
          ],
        );
      },
    );
  }
}
