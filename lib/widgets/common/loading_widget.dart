import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:qubic_exchange/core/index.dart';

// 加载组件
class LoadingWidget extends StatelessWidget {
  final double size;
  final Color? color;
  final bool showSkeleton;

  const LoadingWidget({
    super.key,
    this.size = 24,
    this.color,
    this.showSkeleton = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget loadingWidget = SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor:
            color != null ? AlwaysStoppedAnimation<Color>(color!) : null,
      ),
    );

    if (showSkeleton) {
      loadingWidget = Shimmer.fromColors(
        baseColor: context.templateColors.skeletonBase,
        highlightColor: context.templateColors.skeletonHighlight,
        child: Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: context.templateColors.skeletonBase,
            shape: BoxShape.circle,
          ),
        ),
      );
    }

    return loadingWidget;
  }
}
