/*
*  合约公用吸顶代理
*/

import 'package:flutter/material.dart';

/// 合约页面通用吸顶代理
///
/// 用于创建可吸顶的头部组件，支持：
/// - 固定高度吸顶
/// - 可变高度吸顶（支持展开/收缩）
/// - 自定义背景色和阴影
/// - 渐变透明度效果
class StickyDelegate extends SliverPersistentHeaderDelegate {
  /// 子组件
  final Widget child;

  /// 最大高度
  final double maxHeight;

  /// 最小高度（吸顶时的高度）
  final double minHeight;

  /// 背景色
  final Color? backgroundColor;

  /// 是否显示阴影
  final bool showShadow;

  /// 是否启用渐变透明度
  final bool enableFadeEffect;

  /// 阴影颜色
  final Color? shadowColor;

  /// 阴影高度
  final double shadowElevation;

  const StickyDelegate({
    required this.child,
    required this.maxHeight,
    double? minHeight,
    this.backgroundColor,
    this.showShadow = false,
    this.enableFadeEffect = false,
    this.shadowColor,
    this.shadowElevation = 2.0,
  }) : minHeight = minHeight ?? maxHeight;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    // 计算收缩比例 (0.0 - 1.0)
    final shrinkRatio = (shrinkOffset / (maxHeight - minHeight)).clamp(
      0.0,
      1.0,
    );

    // 计算当前高度
    final currentHeight =
        maxHeight - shrinkOffset.clamp(0.0, maxHeight - minHeight);

    Widget content = SizedBox(height: currentHeight, child: child);

    // 添加背景色
    if (backgroundColor != null) {
      content = Container(
        height: currentHeight,
        color: backgroundColor,
        child: child,
      );
    }

    // 添加渐变透明度效果
    if (enableFadeEffect) {
      content = Opacity(
        opacity: 1.0 - (shrinkRatio * 0.3), // 最多减少30%透明度
        child: content,
      );
    }

    // 添加阴影效果
    if (showShadow && shrinkRatio > 0.1) {
      content = Material(
        elevation: shadowElevation * shrinkRatio,
        shadowColor: shadowColor ?? Colors.black.withValues(alpha: 0.1),
        child: content,
      );
    }

    return content;
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    if (oldDelegate is! StickyDelegate) return true;

    return oldDelegate.child != child ||
        oldDelegate.maxHeight != maxHeight ||
        oldDelegate.minHeight != minHeight ||
        oldDelegate.backgroundColor != backgroundColor ||
        oldDelegate.showShadow != showShadow ||
        oldDelegate.enableFadeEffect != enableFadeEffect ||
        oldDelegate.shadowColor != shadowColor ||
        oldDelegate.shadowElevation != shadowElevation;
  }
}

/// 简化版吸顶代理（固定高度）
///
/// 用于简单的固定高度吸顶场景
class SimpleStickyDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;
  final Color? backgroundColor;
  final bool showShadow;

  const SimpleStickyDelegate({
    required this.child,
    required this.height,
    this.backgroundColor,
    this.showShadow = false,
  });

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    Widget content = SizedBox(height: height, child: child);

    if (backgroundColor != null) {
      content = Container(height: height, color: backgroundColor, child: child);
    }

    if (showShadow) {
      content = Material(
        elevation: 2.0,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        child: content,
      );
    }

    return content;
  }

  @override
  double get maxExtent => height;

  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    if (oldDelegate is! SimpleStickyDelegate) return true;

    return oldDelegate.child != child ||
        oldDelegate.height != height ||
        oldDelegate.backgroundColor != backgroundColor ||
        oldDelegate.showShadow != showShadow;
  }
}
