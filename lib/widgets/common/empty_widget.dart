/*

  空状态
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class EmptyWidget extends StatefulWidget {
  final String imageName;
  final String text;
  final bool? showButton;
  final String buttonText;
  final VoidCallback? onButtonTap;
  final EdgeInsets? padding;
  final double? imageSize;
  final TextStyle? textStyle;
  final Widget? buttonGroup;
  final ImageFormat format;
  final BoxFit? fit;

  const EmptyWidget({
    super.key,
    this.imageName = 'noData',
    this.text = '没有数据',
    this.showButton = false,
    this.buttonText = '重试',
    this.onButtonTap,
    this.padding,
    this.imageSize = 50,
    this.textStyle,
    this.buttonGroup,
    this.format = ImageFormat.png,
    this.fit,
  });

  @override
  State<EmptyWidget> createState() => _EmptyWidgetState();
}

class _EmptyWidgetState extends State<EmptyWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ThemedImage(
              name: widget.imageName,
              followTheme: true,
              size: widget.imageSize,
              format: widget.format,
              fit: widget.fit ?? BoxFit.cover,
            ),
            SizedBox(height: UiConstants.spacing8),
            TextWidget(
              text: widget.text,
              style: context.templateStyle.text.descriptionText.copyWith(
                fontSize: UiConstants.fontSize13,
                color: context.templateColors.textTertiary,
              ),
            ),
            SizedBox(height: UiConstants.spacing16),
            if (widget.showButton!) ...[
              CommonButton.primary(
                widget.buttonText,
                textStyle: widget.textStyle,
                size: CommonButtonSize.small,
                onPressed: widget.onButtonTap,
                padding: EdgeInsetsGeometry.symmetric(
                  horizontal: UiConstants.spacing20,
                ),
              ),
            ],
            if (widget.buttonGroup != null) widget.buttonGroup!,
          ],
        ),
      ),
    );
  }
}
