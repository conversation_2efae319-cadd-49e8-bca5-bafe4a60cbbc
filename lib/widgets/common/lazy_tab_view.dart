import 'package:flutter/material.dart';

/// 懒加载Tab视图组件
/// 只有在真正需要显示时才构建内容
class LazyTabView extends StatefulWidget {
  final Widget Function() builder;
  
  const LazyTabView({
    super.key, 
    required this.builder,
  });
  
  @override
  State<LazyTabView> createState() => _LazyTabViewState();
}

class _LazyTabViewState extends State<LazyTabView> with AutomaticKeepAliveClientMixin {
  Widget? _child;
  
  @override
  bool get wantKeepAlive => true;
  
  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    // 只有第一次build时才调用builder构建真实内容
    _child ??= widget.builder();
    return _child!;
  }
}
