/*
*  倒计时组件
*  
*  功能：
*  - 支持显示天数、小时、分钟、秒的倒计时
*  - 可自定义样式和分隔符
*  - 支持倒计时结束回调
*  - 自动更新显示
*/

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';

/// 倒计时显示格式枚举
enum CountdownFormat {
  /// 天:时:分:秒
  dayHourMinuteSecond,

  /// 时:分:秒
  hourMinuteSecond,

  /// 分:秒
  minuteSecond,

  /// 仅秒
  secondOnly,
}

/// 倒计时组件
class CountdownWidget extends StatefulWidget {
  /// 目标时间（倒计时结束时间）
  final DateTime? targetTime;

  /// 倒计时总秒数（与targetTime二选一）
  final int? totalSeconds;

  /// 显示格式
  final CountdownFormat format;

  /// 文本样式
  final TextStyle? textStyle;

  /// 分隔符
  final String separator;

  /// 倒计时结束回调
  final VoidCallback? onFinished;

  /// 倒计时进度回调 (剩余秒数, 进度值0.0-1.0)
  final Function(int remainingSeconds, double progress)? onProgress;

  /// 是否自动开始
  final bool autoStart;

  /// 数字容器装饰
  final BoxDecoration? numberDecoration;

  /// 数字容器内边距
  final EdgeInsets? numberPadding;

  /// 数字容器间距
  final double spacing;

  /// 是否显示单位标签（天、时、分、秒）
  final bool showLabels;

  /// 单位标签样式
  final TextStyle? labelStyle;

  /// 单位标签文本
  final Map<String, String>? labelTexts;

  const CountdownWidget({
    super.key,
    this.targetTime,
    this.totalSeconds,
    this.format = CountdownFormat.dayHourMinuteSecond,
    this.textStyle,
    this.separator = ':',
    this.onFinished,
    this.onProgress,
    this.autoStart = true,
    this.numberDecoration,
    this.numberPadding,
    this.spacing = 4.0,
    this.showLabels = false,
    this.labelStyle,
    this.labelTexts,
  }) : assert(
         targetTime != null || totalSeconds != null,
         '必须提供 targetTime 或 totalSeconds 其中之一',
       );

  @override
  State<CountdownWidget> createState() => _CountdownWidgetState();
}

class _CountdownWidgetState extends State<CountdownWidget> {
  Timer? _timer;
  late int _remainingSeconds;
  late int _totalSeconds;
  bool _isFinished = false;

  @override
  void initState() {
    super.initState();
    _initializeCountdown();
    if (widget.autoStart) {
      _startCountdown();
    }
  }

  @override
  void didUpdateWidget(CountdownWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.targetTime != oldWidget.targetTime ||
        widget.totalSeconds != oldWidget.totalSeconds) {
      _initializeCountdown();
      if (widget.autoStart) {
        _startCountdown();
      }
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  /// 初始化倒计时
  void _initializeCountdown() {
    if (widget.targetTime != null) {
      final now = DateTime.now();
      final difference = widget.targetTime!.difference(now);
      _remainingSeconds = difference.inSeconds > 0 ? difference.inSeconds : 0;
      _totalSeconds = _remainingSeconds;
    } else if (widget.totalSeconds != null) {
      _remainingSeconds = widget.totalSeconds! > 0 ? widget.totalSeconds! : 0;
      _totalSeconds = _remainingSeconds;
    }
    _isFinished = _remainingSeconds <= 0;

    // 延迟初始进度回调，避免在构建过程中触发
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _notifyProgress();
    });
  }

  /// 开始倒计时
  void _startCountdown() {
    _timer?.cancel();
    if (_remainingSeconds <= 0) {
      _onCountdownFinished();
      return;
    }

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _remainingSeconds--;
          if (_remainingSeconds <= 0) {
            _onCountdownFinished();
          }
        });

        // 延迟进度回调，避免在构建过程中触发
        if (_remainingSeconds > 0) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _notifyProgress();
          });
        }
      }
    });
  }

  /// 倒计时结束处理
  void _onCountdownFinished() {
    _timer?.cancel();
    _isFinished = true;
    widget.onFinished?.call();
  }

  /// 通知进度变化
  void _notifyProgress() {
    if (widget.onProgress != null && _totalSeconds > 0) {
      // 倒计时进度：从1.0（100%）开始，逐渐减少到0.0（0%）
      final progress = _remainingSeconds / _totalSeconds;
      widget.onProgress!(_remainingSeconds, progress.clamp(0.0, 1.0));
    }
  }

  /// 格式化时间显示
  Map<String, String> _formatTime() {
    if (_remainingSeconds <= 0) {
      return _getZeroTime();
    }

    final days = _remainingSeconds ~/ 86400;
    final hours = (_remainingSeconds % 86400) ~/ 3600;
    final minutes = (_remainingSeconds % 3600) ~/ 60;
    final seconds = _remainingSeconds % 60;

    return {
      'days': days.toString().padLeft(2, '0'),
      'hours': hours.toString().padLeft(2, '0'),
      'minutes': minutes.toString().padLeft(2, '0'),
      'seconds': seconds.toString().padLeft(2, '0'),
    };
  }

  /// 获取零时间显示
  Map<String, String> _getZeroTime() {
    return {'days': '00', 'hours': '00', 'minutes': '00', 'seconds': '00'};
  }

  /// 构建数字容器
  Widget _buildNumberContainer(String number) {
    if (widget.numberDecoration != null) {
      return Container(
        padding:
            widget.numberPadding ??
            EdgeInsets.symmetric(
              horizontal: UiConstants.spacing4,
              vertical: UiConstants.spacing2,
            ),
        decoration: widget.numberDecoration,
        child: Text(
          number,
          style: widget.textStyle ?? context.templateStyle.text.bodyText,
        ),
      );
    }

    return Text(
      number,
      style: widget.textStyle ?? context.templateStyle.text.bodyText,
    );
  }

  /// 构建分隔符
  Widget _buildSeparator() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: widget.spacing),
      child: Text(
        widget.separator,
        style: widget.textStyle ?? context.templateStyle.text.bodyText,
      ),
    );
  }

  /// 构建分隔符或间距
  /// 如果前后两个时间单位都有标签显示，则使用间距
  /// 如果任一时间单位没有标签显示，则使用冒号分隔符
  Widget _buildSeparatorOrSpacing(String prevKey, String nextKey) {
    final prevHasLabel =
        widget.showLabels &&
        widget.labelTexts != null &&
        widget.labelTexts!.containsKey(prevKey);
    final nextHasLabel =
        widget.showLabels &&
        widget.labelTexts != null &&
        widget.labelTexts!.containsKey(nextKey);

    // 如果前后都有标签，使用间距；否则使用分隔符
    if (prevHasLabel && nextHasLabel) {
      return SizedBox(width: widget.spacing);
    } else {
      return _buildSeparator();
    }
  }

  /// 构建带标签的时间单元
  Widget _buildTimeUnitWithLabel(String number, String labelKey) {
    final shouldShowLabel =
        widget.showLabels &&
        widget.labelTexts != null &&
        widget.labelTexts!.containsKey(labelKey);

    if (shouldShowLabel) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildNumberContainer(number),
          SizedBox(width: widget.spacing),
          Text(
            widget.labelTexts![labelKey]!,
            style:
                widget.labelStyle ??
                (widget.textStyle ?? context.templateStyle.text.bodyText)
                    .copyWith(fontSize: 12),
          ),
        ],
      );
    }
    return _buildNumberContainer(number);
  }

  @override
  Widget build(BuildContext context) {
    final timeMap = _formatTime();
    final List<Widget> children = [];

    switch (widget.format) {
      case CountdownFormat.dayHourMinuteSecond:
        children.addAll([
          _buildTimeUnitWithLabel(timeMap['days']!, 'days'),
          _buildSeparatorOrSpacing('days', 'hours'),
          _buildTimeUnitWithLabel(timeMap['hours']!, 'hours'),
          _buildSeparatorOrSpacing('hours', 'minutes'),
          _buildTimeUnitWithLabel(timeMap['minutes']!, 'minutes'),
          _buildSeparatorOrSpacing('minutes', 'seconds'),
          _buildTimeUnitWithLabel(timeMap['seconds']!, 'seconds'),
        ]);
        break;

      case CountdownFormat.hourMinuteSecond:
        final totalHours = (_remainingSeconds ~/ 3600).toString().padLeft(
          2,
          '0',
        );
        children.addAll([
          _buildTimeUnitWithLabel(totalHours, 'hours'),
          _buildSeparatorOrSpacing('hours', 'minutes'),
          _buildTimeUnitWithLabel(timeMap['minutes']!, 'minutes'),
          _buildSeparatorOrSpacing('minutes', 'seconds'),
          _buildTimeUnitWithLabel(timeMap['seconds']!, 'seconds'),
        ]);
        break;

      case CountdownFormat.minuteSecond:
        final totalMinutes = (_remainingSeconds ~/ 60).toString().padLeft(
          2,
          '0',
        );
        children.addAll([
          _buildTimeUnitWithLabel(totalMinutes, 'minutes'),
          _buildSeparatorOrSpacing('minutes', 'seconds'),
          _buildTimeUnitWithLabel(timeMap['seconds']!, 'seconds'),
        ]);
        break;

      case CountdownFormat.secondOnly:
        children.add(
          _buildTimeUnitWithLabel(_remainingSeconds.toString(), 'seconds'),
        );
        break;
    }

    return Row(mainAxisSize: MainAxisSize.min, children: children);
  }

  /// 手动开始倒计时
  void start() {
    if (!_isFinished) {
      _startCountdown();
    }
  }

  /// 暂停倒计时
  void pause() {
    _timer?.cancel();
  }

  /// 重置倒计时
  void reset() {
    _timer?.cancel();
    _initializeCountdown();
    if (mounted) {
      setState(() {});
    }
  }

  /// 获取剩余秒数
  int get remainingSeconds => _remainingSeconds;

  /// 是否已结束
  bool get isFinished => _isFinished;
}
