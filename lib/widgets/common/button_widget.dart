import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:qubic_exchange/core/index.dart';

// 按钮类型枚举
enum ButtonType { primary, secondary, outlined, text, buy, sell }

// 按钮尺寸枚举
enum ButtonSize { small, medium, large }

/*
 * 通用按钮组件
 *
 * ✅ 已适配新的模板主题系统：
 * - 使用 context.templateColors 替代 theme.xxx(context)
 * - 使用 context.templateStyle.button.xxx 替代 themeStyles.xxx(context)
 * - 支持多种主题模板（Base、OKX、Bitget）
 * - 自动适应深色/浅色模式
 *
 * 支持多种使用方式：
 * 1. 纯文本按钮：传入 text 参数
 * 2. 图标按钮：传入 icon 参数
 * 3. 图标+文本按钮：同时传入 icon 和 text 参数
 * 4. 自定义内容按钮：传入 child 参数（优先级最高）
 *
 * 示例：
 * ```dart
 * // 自定义内容按钮
 * ButtonWidget(
 *   child: Row(
 *     mainAxisSize: MainAxisSize.min,
 *     children: [
 *       Icon(Icons.star),
 *       SizedBox(width: 4),
 *       Text('自定义'),
 *     ],
 *   ),
 *   onPressed: () {},
 * )
 * ```
 */
class ButtonWidget extends StatefulWidget {
  final bool isLoading;
  final bool showSkeleton;
  final String text;
  final ButtonType type;
  final ButtonSize size;
  final VoidCallback? onPressed;
  final Widget? icon;
  final bool enabled;
  final double? width;
  final double? height;

  /// 自定义按钮内容，优先级高于 text、icon 参数
  final Widget? child;

  /// 自定义背景色
  final Color? backgroundColor;

  /// 自定义文本样式
  final TextStyle? textStyle;

  /// 自定义按钮圆角
  final double? borderRadius;

  const ButtonWidget({
    super.key,
    this.isLoading = false,
    this.showSkeleton = false,
    this.text = '',
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.onPressed,
    this.icon,
    this.enabled = true,
    this.width,
    this.height,
    this.child,
    this.backgroundColor,
    this.textStyle,
    this.borderRadius,
  });

  @override
  State<ButtonWidget> createState() => _ButtonWidgetState();
}

class _ButtonWidgetState extends State<ButtonWidget> {
  @override
  Widget build(BuildContext context) {
    // 构建按钮内容
    Widget buttonChild = widget.isLoading ? _buildLoading() : _buildContent();

    // 构建按钮
    Widget button = _buildButton(_getButtonStyle(), buttonChild);

    // 应用自定义尺寸
    if (widget.width != null || widget.height != null) {
      button = SizedBox(
        width: widget.width,
        height: widget.height,
        child: button,
      );
    }

    // 应用骨架屏效果
    if (widget.showSkeleton) {
      final colors = context.templateColors;
      button = Shimmer.fromColors(
        baseColor: colors.skeletonBase,
        highlightColor: colors.skeletonHighlight,
        child: Container(
          width: widget.width,
          height: widget.height ?? UiConstants.spacing48,
          color: colors.primary,
        ),
      );
    }

    return button;
  }

  // ========== 按钮构建方法 ==========

  /// 构建按钮
  Widget _buildButton(ButtonStyle style, Widget child) {
    return ElevatedButton(
      onPressed: _getOnPressed(),
      style: style,
      child: child,
    );
  }

  /// 构建按钮内容
  Widget _buildContent() {
    // 优先使用自定义传入的 widget
    if (widget.child != null) {
      return widget.child!;
    }

    if (widget.icon != null && widget.text.isNotEmpty) {
      // 图标 + 文本
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          widget.icon!,
          SizedBox(width: UiConstants.spacing8),
          _buildText(),
        ],
      );
    } else if (widget.icon != null) {
      // 仅图标
      return widget.icon!;
    } else {
      // 仅文本
      return _buildText();
    }
  }

  // 构建按钮文本
  Widget _buildText() {
    if (widget.text.isEmpty) return const SizedBox.shrink();

    return Text(
      widget.text,
      textAlign: TextAlign.center,
      style: widget.textStyle, // 使用自定义文本样式
    );
  }

  // 构建加载指示器
  Widget _buildLoading() {
    Color loadingColor = _getLoadingColor();

    return SizedBox(
      width: 20,
      height: 20,
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(loadingColor),
      ),
    );
  }

  // 获取按钮样式
  ButtonStyle _getButtonStyle() {
    ButtonStyle baseStyle;

    switch (widget.type) {
      case ButtonType.primary:
        baseStyle = context.templateStyle.button.primary;
        break;
      case ButtonType.secondary:
        baseStyle = context.templateStyle.button.secondary;
        break;
      case ButtonType.outlined:
        baseStyle = context.templateStyle.button.outlined;
        break;
      case ButtonType.text:
        baseStyle = context.templateStyle.button.text;
        break;
      case ButtonType.buy:
        baseStyle = context.templateStyle.button.buy;
        break;
      case ButtonType.sell:
        baseStyle = context.templateStyle.button.sell;
        break;
    }

    // 根据尺寸调整样式
    baseStyle = _adjustStyleForSize(baseStyle);

    // 应用自定义样式
    baseStyle = _applyCustomStyles(baseStyle);

    return baseStyle;
  }

  // 获取加载指示器颜色
  Color _getLoadingColor() {
    switch (widget.type) {
      case ButtonType.outlined:
      case ButtonType.text:
        return context.templateColors.primary;
      default:
        return Colors.white;
    }
  }

  // 获取点击回调
  VoidCallback? _getOnPressed() {
    if (!widget.enabled || widget.isLoading) {
      return null;
    }
    return widget.onPressed;
  }

  // ========== 按钮样式方法 ==========

  /// 应用自定义样式
  ButtonStyle _applyCustomStyles(ButtonStyle baseStyle) {
    // 如果没有自定义样式，直接返回原样式
    if (widget.backgroundColor == null &&
        widget.textStyle == null &&
        widget.borderRadius == null) {
      return baseStyle;
    }

    return baseStyle.copyWith(
      // 自定义背景色
      backgroundColor:
          widget.backgroundColor != null
              ? WidgetStateProperty.all(widget.backgroundColor!)
              : baseStyle.backgroundColor,

      // 自定义文本样式
      textStyle:
          widget.textStyle != null
              ? WidgetStateProperty.all(widget.textStyle!)
              : baseStyle.textStyle,

      // 自定义圆角
      shape:
          widget.borderRadius != null
              ? WidgetStateProperty.all(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(widget.borderRadius!),
                ),
              )
              : baseStyle.shape,
    );
  }

  /// 根据尺寸调整样式
  ButtonStyle _adjustStyleForSize(ButtonStyle baseStyle) {
    final styles = context.templateStyles;

    switch (widget.size) {
      case ButtonSize.small:
        return baseStyle.copyWith(
          padding: WidgetStateProperty.all(
            EdgeInsets.symmetric(
              horizontal: styles.spacingSmall,
              vertical: widget.height != null ? 0 : styles.spacingSmall / 2,
            ),
          ),
          minimumSize: WidgetStateProperty.all(const Size(0, 32)),
        );
      case ButtonSize.large:
        return baseStyle.copyWith(
          padding: WidgetStateProperty.all(
            EdgeInsets.symmetric(
              horizontal: styles.spacingLarge,
              vertical: widget.height != null ? 0 : styles.spacingMedium,
            ),
          ),
          minimumSize: WidgetStateProperty.all(const Size(0, 56)),
        );
      case ButtonSize.medium:
        // 如果传入了自定义高度，则将上下边距设为0
        if (widget.height != null) {
          return baseStyle.copyWith(
            padding: WidgetStateProperty.all(
              EdgeInsets.symmetric(
                horizontal: styles.spacingMedium,
                vertical: 0,
              ),
            ),
          );
        }
        return baseStyle; // 使用默认的中等尺寸
    }
  }
}
