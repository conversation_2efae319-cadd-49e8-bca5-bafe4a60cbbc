/*
*  步骤进度条组件
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';

/// 步骤进度条组件
class StepProgressWidget extends StatelessWidget {
  /// 步骤列表
  final List<String> steps;

  /// 当前步骤索引（从0开始）
  /// 进度会显示到当前步骤的中间位置
  final int currentStep;

  /// 步骤点的大小
  final double stepSize;

  /// 连接线的高度
  final double lineHeight;

  /// 节点和线段之间的间距
  final double nodeSpacing;

  /// 文字样式
  final TextStyle? textStyle;

  /// 已完成步骤的颜色
  final Color? completedColor;

  /// 当前步骤的颜色
  final Color? currentColor;

  /// 未完成步骤的颜色
  final Color? inactiveColor;

  const StepProgressWidget({
    super.key,
    required this.steps,
    required this.currentStep,
    this.stepSize = 8.0, // 菱形节点稍小一些
    this.lineHeight = 4.0, // 增加线条粗细
    this.nodeSpacing = 4.0, // 节点和线段之间的间距
    this.textStyle,
    this.completedColor,
    this.currentColor,
    this.inactiveColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing8),
      child: Column(
        children: [
          // 进度条
          _buildProgressBar(context),
          SizedBox(height: UiConstants.spacing12), // 增加进度条和文字的间距
          // 步骤文字
          _buildStepLabels(context),
        ],
      ),
    );
  }

  /// 构建进度条
  Widget _buildProgressBar(BuildContext context) {
    return Stack(
      alignment: Alignment.center, // Stack 整体居中对齐
      children: [
        // 背景连接线层
        _buildBackgroundLines(context),
        // 节点层
        _buildStepDots(context),
      ],
    );
  }

  /// 构建背景连接线层
  Widget _buildBackgroundLines(BuildContext context) {
    return Container(
      height: stepSize * 1.2, // 设置固定高度，与菱形容器高度一致
      alignment: Alignment.center, // 确保整体居中
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center, // 横向居中
        crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中对齐
        children: List.generate(steps.length * 2 - 1, (index) {
          if (index.isEven) {
            // 节点位置，用透明容器占位，包含间距
            return SizedBox(
              width: stepSize * 1.2 + nodeSpacing * 2, // 左右各加间距
              height: stepSize * 1.2,
            );
          } else {
            // 连接线，确保与菱形水平居中
            final lineIndex = index ~/ 2;
            return _buildConnectingLineWithSpacing(context, lineIndex);
          }
        }),
      ),
    );
  }

  /// 构建步骤节点层
  Widget _buildStepDots(BuildContext context) {
    return Container(
      height: stepSize * 1.2, // 设置固定高度，与连接线层高度一致
      alignment: Alignment.center, // 确保整体居中
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center, // 横向居中
        crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中对齐
        children: List.generate(steps.length * 2 - 1, (index) {
          if (index.isEven) {
            // 步骤点，包含间距
            final stepIndex = index ~/ 2;
            return Container(
              width: stepSize * 1.2 + nodeSpacing * 2, // 与连接线层保持一致
              alignment: Alignment.center,
              child: _buildStepDot(context, stepIndex),
            );
          } else {
            // 连接线位置，用 Expanded 占位，包含间距
            return Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: nodeSpacing),
                child: const SizedBox(),
              ),
            );
          }
        }),
      ),
    );
  }

  /// 构建步骤点（菱形）
  Widget _buildStepDot(BuildContext context, int stepIndex) {
    // 步骤点的激活逻辑：当前步骤对应的点位激活
    // currentStep = 1 -> 激活第0个点
    // currentStep = 2 -> 激活第0、1个点
    // currentStep = 3 -> 激活第0、1、2个点
    final isActive = stepIndex < currentStep;

    Color dotColor;
    if (isActive) {
      // 使用黑色表示已完成步骤
      dotColor = completedColor ?? context.templateColors.textPrimary;
    } else {
      // 使用浅灰色表示未完成步骤
      dotColor = inactiveColor ?? context.templateColors.tabbarActive;
    }

    return Container(
      width: stepSize * 1.2, // 稍微增加容器宽度，确保菱形不被裁剪
      height: stepSize * 1.2,
      alignment: Alignment.center,
      child: Transform.rotate(
        angle: 0.785398, // 45度，π/4 弧度
        child: Container(
          width: stepSize,
          height: stepSize,
          decoration: BoxDecoration(
            color: dotColor,
            borderRadius: BorderRadius.circular(2), // 增加圆角，让菱形更圆润
          ),
        ),
      ),
    );
  }

  /// 构建带间距的连接线
  Widget _buildConnectingLineWithSpacing(BuildContext context, int lineIndex) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: nodeSpacing), // 左右间距
        child: _buildConnectingLineContent(context, lineIndex),
      ),
    );
  }

  /// 构建连接线内容
  Widget _buildConnectingLineContent(BuildContext context, int lineIndex) {
    // 连接线的填充逻辑：
    // currentStep = 1 -> 第0段线填充一半
    // currentStep = 2 -> 第0段线全部填充 + 第1段线填充一半
    // currentStep = 3 -> 第0、1段线全部填充 + 第2段线填充一半

    final activeColor = completedColor ?? context.templateColors.textPrimary;
    final inactiveColor =
        this.inactiveColor ?? context.templateColors.tabbarActive;

    if (lineIndex < currentStep - 1) {
      // 完全填充的线段
      return Container(
        alignment: Alignment.center, // 垂直居中
        child: Container(height: lineHeight, color: activeColor),
      );
    } else if (lineIndex == currentStep - 1) {
      // 当前步骤对应的线段，填充一半
      return Container(
        alignment: Alignment.center, // 垂直居中
        child: Row(
          children: [
            Expanded(child: Container(height: lineHeight, color: activeColor)),
            Expanded(
              child: Container(height: lineHeight, color: inactiveColor),
            ),
          ],
        ),
      );
    } else {
      // 未填充的线段
      return Container(
        alignment: Alignment.center, // 垂直居中
        child: Container(height: lineHeight, color: inactiveColor),
      );
    }
  }

  /// 构建步骤标签
  Widget _buildStepLabels(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween, // 两端对齐
      crossAxisAlignment: CrossAxisAlignment.center,
      children: List.generate(steps.length, (index) {
        // 文字标签的激活逻辑：与步骤点保持一致
        final isActive = index < currentStep;
        final isCurrent = index == currentStep - 1; // 当前激活的步骤

        Color textColor;
        if (isActive) {
          // 使用黑色表示已完成步骤
          textColor = completedColor ?? context.templateColors.textPrimary;
        } else {
          // 使用浅灰色表示未完成步骤
          textColor = inactiveColor ?? context.templateColors.textSecondary;
        }

        // 根据位置确定文本对齐方式
        TextAlign textAlign;
        if (index == 0) {
          textAlign = TextAlign.left; // 第一个文本左对齐
        } else if (index == steps.length - 1) {
          textAlign = TextAlign.right; // 最后一个文本右对齐
        } else {
          textAlign = TextAlign.center; // 中间的文本居中对齐
        }

        return Expanded(
          child: Text(
            steps[index],
            textAlign: textAlign,
            style: (textStyle ?? context.templateStyle.text.bodyText).copyWith(
              color: textColor,
              fontWeight: isCurrent ? FontWeight.w600 : FontWeight.normal,
              fontSize: 12, // 设置合适的字体大小
            ),
          ),
        );
      }),
    );
  }
}

/// 预定义的交易进度组件
class TradingProgressWidget extends StatelessWidget {
  /// 当前步骤：0-身份认证, 1-充值, 2-开始交易
  final int currentStep;

  const TradingProgressWidget({super.key, required this.currentStep});

  @override
  Widget build(BuildContext context) {
    return StepProgressWidget(
      steps: const ['身份认证', '充值', '开始交易'],
      currentStep: currentStep,
      stepSize: 8.0, // 菱形节点大小（与您的修改保持一致）
      lineHeight: 4.0, // 使用更粗的线条
      nodeSpacing: 4.0, // 节点和线段之间的间距
    );
  }
}

/// 便捷的构造方法
extension TradingProgressExtension on TradingProgressWidget {
  /// 身份认证阶段（进度到第一步中间）
  static TradingProgressWidget identity() {
    return const TradingProgressWidget(currentStep: 0);
  }

  /// 充值阶段（进度到第二步中间）
  static TradingProgressWidget deposit() {
    return const TradingProgressWidget(currentStep: 1);
  }

  /// 开始交易阶段（进度到第三步中间）
  static TradingProgressWidget trading() {
    return const TradingProgressWidget(currentStep: 2);
  }
}
