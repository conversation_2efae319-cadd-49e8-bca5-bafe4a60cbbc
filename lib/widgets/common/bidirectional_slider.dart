/*
 * 双向滑块组件
 *
 * 功能：
 * - 支持左滑显示红色，右滑显示蓝色
 * - 中心位置为默认状态
 * - 可自定义颜色、尺寸和回调
 * - 支持主题感知
 */

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qubic_exchange/core/index.dart';

/// 滑块方向枚举
enum SliderDirection {
  center, // 中心位置
  left, // 左滑
  right, // 右滑
}

/// 双向滑块组件
class BidirectionalSlider extends StatefulWidget {
  /// 滑块宽度
  final double width;

  /// 滑块高度
  final double height;

  /// 滑块圆角
  final double borderRadius;

  /// 左滑颜色（红色）
  final Color? leftColor;

  /// 右滑颜色（蓝色）
  final Color? rightColor;

  /// 中心颜色
  final Color? centerColor;

  /// 滑块按钮颜色
  final Color? thumbColor;

  /// 滑块按钮大小
  final double thumbSize;

  /// 初始值 (-1.0 到 1.0，0为中心)
  final double initialValue;

  /// 值变化回调
  final ValueChanged<double>? onChanged;

  /// 滑动结束回调
  final ValueChanged<double>? onChangeEnd;

  /// 方向变化回调
  final ValueChanged<SliderDirection>? onDirectionChanged;

  /// 是否启用触觉反馈
  final bool enableHapticFeedback;

  /// 左侧文本
  final String? leftText;

  /// 右侧文本
  final String? rightText;

  /// 中心文本
  final String? centerText;

  const BidirectionalSlider({
    super.key,
    this.width = 300,
    this.height = 50,
    this.borderRadius = 25,
    this.leftColor,
    this.rightColor,
    this.centerColor,
    this.thumbColor,
    this.thumbSize = 40,
    this.initialValue = 0.0,
    this.onChanged,
    this.onChangeEnd,
    this.onDirectionChanged,
    this.enableHapticFeedback = true,
    this.leftText,
    this.rightText,
    this.centerText,
  });

  @override
  State<BidirectionalSlider> createState() => _BidirectionalSliderState();
}

class _BidirectionalSliderState extends State<BidirectionalSlider>
    with TickerProviderStateMixin {
  late double _currentValue;
  late AnimationController _animationController;
  late Animation<double> _animation;
  SliderDirection _currentDirection = SliderDirection.center;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.initialValue.clamp(-1.0, 1.0);
    _updateDirection(_currentValue);

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _updateDirection(double value) {
    SliderDirection newDirection;
    if (value < -0.1) {
      newDirection = SliderDirection.left;
    } else if (value > 0.1) {
      newDirection = SliderDirection.right;
    } else {
      newDirection = SliderDirection.center;
    }

    if (newDirection != _currentDirection) {
      _currentDirection = newDirection;
      widget.onDirectionChanged?.call(_currentDirection);

      if (widget.enableHapticFeedback) {
        HapticFeedback.lightImpact();
      }
    }
  }

  Color _getBackgroundColor() {
    final colors = context.templateColors;

    if (_currentValue < -0.1) {
      return widget.leftColor ?? colors.tradeSell;
    } else if (_currentValue > 0.1) {
      return widget.rightColor ?? colors.tradeBuy;
    } else {
      return widget.centerColor ?? colors.cardBackground;
    }
  }

  String _getCurrentText() {
    if (_currentValue < -0.1 && widget.leftText != null) {
      return widget.leftText!;
    } else if (_currentValue > 0.1 && widget.rightText != null) {
      return widget.rightText!;
    } else if (widget.centerText != null) {
      return widget.centerText!;
    }
    return '';
  }

  @override
  Widget build(BuildContext context) {
    final colors = context.templateColors;
    final textStyles = context.templateStyle.text;

    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: Stack(
        children: [
          // 背景轨道
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            width: widget.width,
            height: widget.height,
            decoration: BoxDecoration(
              color: _getBackgroundColor(),
              borderRadius: BorderRadius.circular(widget.borderRadius),
              border: Border.all(color: colors.border, width: 1),
            ),
            child: Center(
              child: AnimatedOpacity(
                duration: const Duration(milliseconds: 200),
                opacity: _getCurrentText().isNotEmpty ? 1.0 : 0.0,
                child: Text(
                  _getCurrentText(),
                  style: textStyles.bodyText.copyWith(
                    color: colors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),

          // 滑块按钮
          Positioned(
            left: (_currentValue + 1) * (widget.width - widget.thumbSize) / 2,
            top: (widget.height - widget.thumbSize) / 2,
            child: GestureDetector(
              onPanUpdate: (details) {
                final RenderBox renderBox =
                    context.findRenderObject() as RenderBox;
                final localPosition = renderBox.globalToLocal(
                  details.globalPosition,
                );
                final newValue =
                    ((localPosition.dx - widget.thumbSize / 2) /
                            (widget.width - widget.thumbSize)) *
                        2 -
                    1;

                setState(() {
                  _currentValue = newValue.clamp(-1.0, 1.0);
                  _updateDirection(_currentValue);
                });

                widget.onChanged?.call(_currentValue);
              },
              onPanEnd: (details) {
                widget.onChangeEnd?.call(_currentValue);
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 100),
                width: widget.thumbSize,
                height: widget.thumbSize,
                decoration: BoxDecoration(
                  color: widget.thumbColor ?? colors.surface,
                  shape: BoxShape.circle,
                  border: Border.all(color: colors.border, width: 2),
                  boxShadow: [
                    BoxShadow(
                      color: colors.border.withValues(alpha: 0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.drag_indicator,
                  color: colors.textSecondary,
                  size: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
