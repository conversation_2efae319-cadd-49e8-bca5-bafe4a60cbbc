import 'package:flutter/material.dart';

class InkWellWidget extends StatefulWidget {
  final Widget? child;
  final VoidCallback? onTap;

  const InkWellWidget({super.key, this.child, this.onTap});

  @override
  State<InkWellWidget> createState() => _InkWellWidgetState();
}

class _InkWellWidgetState extends State<InkWellWidget> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap,
      splashFactory: NoSplash.splashFactory,
      overlayColor: WidgetStateProperty.all(Colors.transparent),
      highlightColor: Colors.transparent,
      hoverColor: Colors.transparent,
      focusColor: Colors.transparent,
      child: widget.child,
    );
  }
}
