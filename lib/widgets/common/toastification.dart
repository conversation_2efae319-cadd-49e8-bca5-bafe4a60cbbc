/*
*  Toast组件 - 基于toastification包的封装
*  只提供自定义样式功能
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:toastification/toastification.dart';

class Toastification {
  // 用于跟踪已显示的消息，防止重复显示
  static final Set<String> _shownMessages = <String>{};

  /// 显示自定义提示
  static void show(
    BuildContext context, {
    required String message,
    ToastificationType type = ToastificationType.info,
    Duration duration = const Duration(seconds: 2),
    Color? backgroundColor,
    Color? foregroundColor,
    Alignment alignment = Alignment.center,
    ToastificationStyle style = ToastificationStyle.simple,
    bool showProgressBar = false,
    bool closeOnClick = false,
    bool dragToClose = false,
    Widget? icon,
    EdgeInsets? padding = const EdgeInsets.symmetric(
      horizontal: 8,
      vertical: 2,
    ),
    EdgeInsets? margin,
    BorderRadius? borderRadius,
    BorderSide? borderSide = BorderSide.none,
    bool enableAnimation = false,
    bool allowDuplicate = false,
  }) {
    // 检查是否允许重复显示
    if (!allowDuplicate) {
      if (_shownMessages.contains(message)) {
        return; // 消息已显示过，直接返回
      }
      _shownMessages.add(message);

      // 设置定时器清除消息记录，避免内存泄漏
      Future.delayed(duration + const Duration(milliseconds: 500), () {
        _shownMessages.remove(message);
      });
    }

    toastification.show(
      context: context,
      title: Text(message),
      type: type,
      style: style,
      autoCloseDuration: duration,
      alignment: alignment,
      backgroundColor: backgroundColor ?? context.templateColors.textPrimary,
      foregroundColor: foregroundColor ?? context.templateColors.surface,
      showProgressBar: showProgressBar,
      closeOnClick: closeOnClick,
      dragToClose: dragToClose,
      icon: icon,
      padding: padding,
      margin: margin,
      borderRadius:
          borderRadius ?? BorderRadius.circular(UiConstants.borderRadius6),
      borderSide: borderSide,
      showIcon: false,
      closeButton: ToastCloseButton(showType: CloseButtonShowType.none),
      // 动画控制
      animationDuration:
          enableAnimation ? const Duration(milliseconds: 300) : Duration.zero,
      animationBuilder:
          enableAnimation
              ? null // 使用默认动画
              : (context, animation, alignment, child) {
                // 禁用动画，直接返回子组件
                return child;
              },
    );
  }

  /// 清除所有已显示消息的记录
  static void clearShownMessages() {
    _shownMessages.clear();
  }

  /// 清除特定消息的记录
  static void clearMessage(String message) {
    _shownMessages.remove(message);
  }
}
