/*
* 动态高度 TabView 组件
* 解决 TabBarView 无法根据内容自动调整高度的问题
*/

import 'package:flutter/material.dart';

/// 标签切换过渡效果类型
enum TabTransitionType {
  /// 淡入淡出 + 轻微滑动
  fadeSlide,

  /// 缩放 + 淡入淡出
  scaleFade,

  /// 仅淡入淡出
  fade,

  /// 滑动切换（类似原生 TabBarView）
  slide,

  /// 无动画
  none,
}

/// 动态高度 TabView 组件
/// 使用 AnimatedSwitcher 替代 TabBarView 来实现内容高度自适应
class DynamicHeightTabView extends StatefulWidget {
  /// Tab 控制器
  final TabController controller;

  /// Tab 页面内容列表
  final List<Widget> children;

  /// 是否启用滑动切换
  final bool enableSwipe;

  /// 滑动切换的最小速度阈值（像素/秒）
  final double swipeVelocityThreshold;

  /// 过渡效果类型
  final TabTransitionType transitionType;

  /// 动画持续时间
  final Duration animationDuration;

  const DynamicHeightTabView({
    super.key,
    required this.controller,
    required this.children,
    this.enableSwipe = true,
    this.swipeVelocityThreshold = 300.0,
    this.transitionType = TabTransitionType.fadeSlide,
    this.animationDuration = const Duration(milliseconds: 250),
  });

  @override
  State<DynamicHeightTabView> createState() => _DynamicHeightTabViewState();
}

class _DynamicHeightTabViewState extends State<DynamicHeightTabView> {
  int _previousIndex = 0;

  @override
  void initState() {
    super.initState();
    _previousIndex = widget.controller.index;
    widget.controller.addListener(_handleTabChange);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_handleTabChange);
    super.dispose();
  }

  /// 处理标签切换
  void _handleTabChange() {
    if (mounted) {
      final newIndex = widget.controller.index;
      if (newIndex != _previousIndex) {
        setState(() {
          // 更新前一个索引为当前索引
          _previousIndex = newIndex;
        });
      }
    }
  }

  /// 处理水平滑动手势
  void _handleHorizontalDrag(DragEndDetails details) {
    if (!widget.enableSwipe) return;

    final velocity = details.primaryVelocity ?? 0.0;

    // 检查是否满足速度阈值
    if (velocity.abs() < widget.swipeVelocityThreshold) return;

    final currentIndex = widget.controller.index;

    if (velocity < 0) {
      // 向左滑动，切换到下一个标签（如果有）
      if (currentIndex < widget.children.length - 1) {
        widget.controller.animateTo(currentIndex + 1);
      }
    } else {
      // 向右滑动，切换到上一个标签（如果有）
      if (currentIndex > 0) {
        widget.controller.animateTo(currentIndex - 1);
      }
    }
  }

  /// 构建过渡效果
  Widget _buildTransition(Widget child, Animation<double> animation) {
    switch (widget.transitionType) {
      case TabTransitionType.fadeSlide:
        return FadeTransition(
          opacity: animation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0.08, 0.0),
              end: Offset.zero,
            ).animate(
              CurvedAnimation(parent: animation, curve: Curves.easeOutCubic),
            ),
            child: child,
          ),
        );

      case TabTransitionType.scaleFade:
        return Transform.scale(
          scale: 0.96 + (0.04 * animation.value),
          child: FadeTransition(opacity: animation, child: child),
        );

      case TabTransitionType.fade:
        return FadeTransition(opacity: animation, child: child);

      case TabTransitionType.slide:
        // 根据切换方向决定滑动方向
        final currentIndex = widget.controller.index;
        final isForward = currentIndex > _previousIndex;
        return SlideTransition(
          position: Tween<Offset>(
            begin: Offset(isForward ? 1.0 : -1.0, 0.0),
            end: Offset.zero,
          ).animate(
            CurvedAnimation(parent: animation, curve: Curves.easeOutCubic),
          ),
          child: child,
        );

      case TabTransitionType.none:
        return child;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onHorizontalDragEnd: widget.enableSwipe ? _handleHorizontalDrag : null,
      child: AnimatedSwitcher(
        duration:
            widget.transitionType == TabTransitionType.none
                ? Duration.zero
                : widget.animationDuration,
        transitionBuilder: (Widget child, Animation<double> animation) {
          return _buildTransition(child, animation);
        },
        child: Container(
          key: ValueKey<int>(widget.controller.index),
          child: widget.children[widget.controller.index],
        ),
      ),
    );
  }
}
