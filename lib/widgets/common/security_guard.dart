/*
* 安全守护
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

/// 安全守护项数据模型
class SecurityGuardItem {
  final String title;
  final String imagePath;
  final VoidCallback? onTap;

  const SecurityGuardItem({
    required this.title,
    required this.imagePath,
    this.onTap,
  });
}

class SecurityGuard extends StatefulWidget {
  // 标题
  final String title;
  // 项目列表
  final List<SecurityGuardItem> items;

  const SecurityGuard({
    super.key,
    this.title = '您的资产安全，我们来守护',
    this.items = const [],
  });

  @override
  State<SecurityGuard> createState() => _SecurityGuardState();
}

class _SecurityGuardState extends State<SecurityGuard> {
  /// 获取默认守护项列表
  List<SecurityGuardItem> get _defaultItems => [
    SecurityGuardItem(
      title: '7.651亿美元的保护基金，您的资产安全是我们的立身之本',
      imagePath: 'security_fund',
      onTap: () {
        // TODO: 处理保护基金点击
      },
    ),
    SecurityGuardItem(
      title: '银行级安全防护，多重加密保障您的数字资产',
      imagePath: 'security_protection',
      onTap: () {
        // TODO: 处理安全防护点击
      },
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final items = widget.items.isNotEmpty ? widget.items : _defaultItems;

    return Padding(
      padding: EdgeInsets.all(UiConstants.spacing18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(
              bottom: UiConstants.spacing32,
              top: UiConstants.spacing18,
            ),
            child: Text(widget.title, style: context.templateStyle.text.h3),
          ),
          ...items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return Column(
              children: [
                if (index > 0) SizedBox(height: UiConstants.spacing24),
                _buildItem(item: item),
              ],
            );
          }).toList(),
        ],
      ),
    );
  }

  // 构建守护项
  Widget _buildItem({required SecurityGuardItem item}) {
    return GestureDetector(
      onTap: item.onTap,
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.title,
                  style: context.templateStyle.text.bodyTextMedium,
                ),
                SizedBox(height: UiConstants.spacing10),
                ViewMoreButton(
                  text: '了解更多',
                  showArrow: false,
                  color: context.templateColors.primary,
                ),
              ],
            ),
          ),
          SizedBox(width: UiConstants.spacing14),
          ThemedImage(name: item.imagePath, width: 70, height: 70),
        ],
      ),
    );
  }
}
