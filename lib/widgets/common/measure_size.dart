/*
  === 尺寸测量组件 ===
  
  功能：
  - 测量子组件的尺寸变化
  - 当尺寸发生变化时触发回调
  - 优化性能，避免重复调用
*/

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

/// 尺寸变化回调函数类型
typedef OnWidgetSizeChange = void Function(Size size);

/// 尺寸测量组件
/// 
/// 用于测量子组件的尺寸，并在尺寸发生变化时触发回调
class MeasureSize extends StatefulWidget {
  /// 子组件
  final Widget child;
  
  /// 尺寸变化回调
  final OnWidgetSizeChange onChange;

  const MeasureSize({
    super.key, 
    required this.child, 
    required this.onChange,
  });

  @override
  State<MeasureSize> createState() => _MeasureSizeState();
}

class _MeasureSizeState extends State<MeasureSize> {
  /// 上一次的尺寸
  Size _oldSize = Size.zero;
  
  /// 是否已经添加了回调
  bool _hasAddedCallback = false;

  @override
  Widget build(BuildContext context) {
    // 只在需要时添加回调，避免重复添加
    if (!_hasAddedCallback) {
      _hasAddedCallback = true;
      SchedulerBinding.instance.addPostFrameCallback((_) => _measureSize());
    }

    return widget.child;
  }

  /// 测量尺寸
  void _measureSize() {
    if (!mounted) return;
    
    final renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final newSize = renderBox.size;
      if (_oldSize != newSize) {
        _oldSize = newSize;
        widget.onChange(newSize);
      }
    }
    
    // 重置标志，以便下次build时可以再次添加回调
    _hasAddedCallback = false;
  }

  @override
  void didUpdateWidget(MeasureSize oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当组件更新时，重新测量尺寸
    SchedulerBinding.instance.addPostFrameCallback((_) => _measureSize());
  }
}
