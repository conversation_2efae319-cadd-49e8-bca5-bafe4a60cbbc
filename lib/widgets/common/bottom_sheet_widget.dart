/*
 * 底部弹窗组件
 *
 * 功能特性：
 * - 高度自适应内容，支持最大高度限制
 * - 可配置头部、拖拽指示器、关闭按钮
 * - 支持自定义内容区域和底部按钮
 * - 响应式设计，适配不同屏幕尺寸
 * - 支持安全区域适配
 */

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

/*
 * 底部弹窗组件
 *
 * 提供统一的底部弹窗UI组件，高度自适应内容，支持参数实时更新
 */
class BottomSheetWidget extends StatefulWidget {
  /* 弹窗标题 */
  final String? title;

  /* 标题文本样式 */
  final TextStyle? titleStyle;

  /* 标题对齐方式 */
  final TextAlign titleAlign;

  /* 主要内容区域 */
  final Widget child;

  /* 底部区域内容 */
  final Widget? footer;

  /* 自定义底部按钮 */
  final Widget? customBottomButton;

  /* 是否显示底部按钮区域 */
  final bool showBottomButton;

  /* 是否显示头部区域 */
  final bool showHeader;

  /* 是否显示关闭按钮 */
  final bool showCloseButton;

  /* 是否显示拖拽指示器 */
  final bool showDragHandle;

  /* 是否可通过手势关闭 */
  final bool isDismissible;

  /* 关闭回调函数 */
  final VoidCallback? onClose;

  /* 整体内边距 */
  final EdgeInsets? padding;

  /* 内容区域内边距 */
  final EdgeInsets? contentPadding;

  /* 最大高度限制 */
  final double? maxHeight;

  /* 固定高度 */
  final double? height;

  /* 头部区域高度 */
  final double? headerHeight;

  /* 是否显示头部底部边框 */
  final bool showHeaderBorder;

  /* 是否适配底部安全区域（如 iPhone 的 Home Indicator 区域） */
  final bool useSafeArea;

  const BottomSheetWidget({
    super.key,
    this.title,
    this.titleStyle,
    this.titleAlign = TextAlign.center,
    required this.child,
    this.footer,
    this.customBottomButton,
    this.showBottomButton = false,
    this.showHeader = true,
    this.showCloseButton = true,
    this.showDragHandle = true,
    this.isDismissible = true,
    this.onClose,
    this.padding,
    this.contentPadding,
    this.maxHeight,
    this.height,
    this.headerHeight,
    this.showHeaderBorder = false,
    this.useSafeArea = false,
  });

  @override
  State<BottomSheetWidget> createState() => _BottomSheetWidgetState();

  /*
   * 显示底部弹窗的静态方法
   */
  static Future<T?> show<T>({
    required BuildContext context,
    String? title,
    TextStyle? titleStyle,
    TextAlign titleAlign = TextAlign.center,
    required Widget child,
    Widget? footer,
    Widget? customBottomButton,
    bool showBottomButton = true,
    bool showHeader = true,
    bool showCloseButton = true,
    bool showDragHandle = true,
    bool isDismissible = true,
    VoidCallback? onClose,
    EdgeInsets? padding,
    EdgeInsets? contentPadding,
    double? maxHeight,
    double? height,
    double? headerHeight,
    bool showHeaderBorder = true,
    bool useSafeArea = false,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: true,
      isDismissible: isDismissible,
      enableDrag: isDismissible,
      backgroundColor: Colors.transparent,
      useSafeArea: useSafeArea, // 控制是否使用系统安全区域
      builder: (context) {
        final bottomSheetWidget = BottomSheetWidget(
          title: title,
          titleStyle: titleStyle,
          titleAlign: titleAlign,
          showHeader: showHeader,
          showCloseButton: showCloseButton,
          showDragHandle: showDragHandle,
          isDismissible: isDismissible,
          onClose: onClose,
          padding: padding,
          contentPadding: contentPadding,
          maxHeight: maxHeight,
          height: height,
          headerHeight: headerHeight,
          showHeaderBorder: showHeaderBorder,
          useSafeArea: useSafeArea,
          footer: footer,
          customBottomButton: customBottomButton,
          showBottomButton: showBottomButton,
          child: child,
        );

        // 键盘适配：弹窗保持原位置，不被键盘推挤
        return bottomSheetWidget;
      },
    );
  }
}

/*
 * BottomSheetWidget 的状态类
 */
class _BottomSheetWidgetState extends State<BottomSheetWidget> {
  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final safeAreaBottom = mediaQuery.padding.bottom;
    final screenHeight = mediaQuery.size.height;

    return Container(
      height: widget.height,
      constraints: _buildConstraints(screenHeight),
      decoration: _buildDecoration(context),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /* 拖拽指示器 */
          if (widget.showDragHandle) _buildDragHandle(context),

          /* 头部区域 */
          if (widget.showHeader) _buildHeader(context),

          /* 内容区域 */
          Flexible(
            child: Container(
              width: double.infinity,
              padding: _getContentPadding(),
              child: SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: widget.child,
              ),
            ),
          ),

          /* 底部区域 */
          ..._buildBottomSection(context),

          /* 底部安全区域 */
          SizedBox(
            height:
                (widget.useSafeArea ? safeAreaBottom : 0) +
                UiConstants.spacing16,
          ),
        ],
      ),
    );
  }

  /*
   * 构建容器约束条件
   */
  BoxConstraints _buildConstraints(double screenHeight) {
    return BoxConstraints(
      maxHeight: widget.maxHeight ?? screenHeight * 0.85,
      maxWidth: double.infinity,
    );
  }

  /*
   * 构建装饰样式
   */
  BoxDecoration _buildDecoration(BuildContext context) {
    return BoxDecoration(
      color: context.templateColors.popupBackground,
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(context.templateStyles.borderRadiusLarge),
        topRight: Radius.circular(context.templateStyles.borderRadiusLarge),
      ),
    );
  }

  /*
   * 获取内容区域内边距
   */
  EdgeInsets _getContentPadding() {
    return widget.contentPadding ??
        widget.padding ??
        EdgeInsets.symmetric(horizontal: UiConstants.spacing16);
  }

  /*
   * 构建底部区域
   */
  List<Widget> _buildBottomSection(BuildContext context) {
    // 如果不显示底部按钮，直接返回空列表
    if (!widget.showBottomButton) {
      return [];
    }

    // 优先显示自定义 footer
    if (widget.footer != null) {
      return [SizedBox(height: UiConstants.spacing16), widget.footer!];
    }

    // 其次显示自定义底部按钮
    if (widget.customBottomButton != null) {
      return [
        SizedBox(height: UiConstants.spacing16),
        widget.customBottomButton!,
      ];
    }

    // 如果没有头部且需要显示底部按钮，显示取消按钮
    if (widget.showBottomButton) {
      return [
        SizedBox(height: UiConstants.spacing16),
        _buildCancelButton(context),
      ];
    }

    return [];
  }

  /*
   * 构建拖拽指示器
   */
  Widget _buildDragHandle(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: UiConstants.spacing10),
      child: Center(
        child: Container(
          width: 60,
          height: 4,
          decoration: BoxDecoration(
            color: context.templateColors.dragIndicator,
            borderRadius: BorderRadius.circular(UiConstants.borderRadius12),
          ),
        ),
      ),
    );
  }

  /*
   * 构建头部区域
   */
  Widget _buildHeader(BuildContext context) {
    return Container(
      width: double.infinity,
      height: widget.headerHeight,
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing16,
        vertical: widget.headerHeight != null ? 0 : UiConstants.spacing12,
      ),
      decoration: BoxDecoration(
        border:
            widget.showHeaderBorder
                ? Border(
                  bottom: BorderSide(
                    color: context.templateColors.divider,
                    width: 1,
                  ),
                )
                : null,
      ),
      child: _buildHeaderContent(context),
    );
  }

  /*
   * 构建头部内容
   */
  Widget _buildHeaderContent(BuildContext context) {
    if (widget.title?.isEmpty ?? true) {
      // 如果没有标题，只显示关闭按钮（如果需要）
      return Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [if (widget.showCloseButton) _buildCloseButton(context)],
      );
    }

    // 根据标题对齐方式选择不同的布局
    switch (widget.titleAlign) {
      case TextAlign.left:
      case TextAlign.start:
        return _buildLeftAlignedHeader(context);
      case TextAlign.center:
      default:
        return _buildCenteredHeader(context);
    }
  }

  /*
   * 构建居中对齐的头部
   */
  Widget _buildCenteredHeader(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        /* 标题文本 - 居中显示 */
        Text(
          widget.title!,
          style: widget.titleStyle ?? context.templateStyle.text.h4,
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),

        /* 关闭按钮 - 右侧浮动 */
        if (widget.showCloseButton)
          Positioned(right: 0, child: _buildCloseButton(context)),
      ],
    );
  }

  /*
   * 构建左对齐的头部
   */
  Widget _buildLeftAlignedHeader(BuildContext context) {
    return Row(
      children: [
        /* 标题文本 - 左侧显示 */
        Expanded(
          child: Text(
            widget.title!,
            style: widget.titleStyle ?? context.templateStyle.text.h4,
            textAlign: TextAlign.left,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),

        /* 关闭按钮 - 右侧显示 */
        if (widget.showCloseButton) _buildCloseButton(context),
      ],
    );
  }

  /*
   * 构建关闭按钮
   */
  Widget _buildCloseButton(BuildContext context) {
    return IconButtonWidget(
      onTap: () => _handleClose(context),
      icon: Icons.close,
    );
  }

  /*
   * 处理关闭操作
   */
  void _handleClose(BuildContext context) {
    widget.onClose?.call();
    /* 使用rootNavigator确保能正确关闭弹窗 */
    Navigator.of(context, rootNavigator: true).pop();
  }

  /*
   * 构建取消按钮
   */
  Widget _buildCancelButton(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing16),
      child: TextButton(
        onPressed: () => _handleClose(context),
        style: TextButton.styleFrom(
          backgroundColor: context.templateColors.background,
          foregroundColor: context.templateColors.textTertiary,
          padding: EdgeInsets.symmetric(vertical: UiConstants.spacing12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(UiConstants.borderRadius12),
            side: BorderSide(
              color: context.templateColors.primary.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
        ),
        child: Text('取消'),
      ),
    );
  }
}
