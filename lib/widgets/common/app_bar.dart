/*
  顶部导航组件
*/

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qubic_exchange/core/index.dart';

class AppBarWidget extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final Widget? leading;
  final double? leadingWidth;
  final List<Widget>? actions;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final double height;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final bool centerTitle;
  final double elevation;
  final Widget? bottom;
  final double? bottomHeight;
  final SystemUiOverlayStyle? systemOverlayStyle;

  const AppBarWidget({
    super.key,
    this.title,
    this.titleWidget,
    this.leading,
    this.leadingWidth,
    this.actions,
    this.showBackButton = false,
    this.onBackPressed,
    this.height = 56.0,
    this.backgroundColor,
    this.foregroundColor,
    this.centerTitle = true,
    this.elevation = 0,
    this.bottom,
    this.bottomHeight,
    this.systemOverlayStyle,
  });

  @override
  Size get preferredSize => Size.fromHeight(height + (bottomHeight ?? 0));

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title:
          titleWidget ??
          (title != null
              ? Text(title!, style: context.templateStyle.text.h4)
              : null),
      leading: _buildLeading(context),
      leadingWidth: leadingWidth,
      actions: actions,
      backgroundColor: backgroundColor ?? context.templateColors.surface,
      foregroundColor: foregroundColor ?? context.templateColors.primary,
      centerTitle: centerTitle,
      elevation: elevation,
      scrolledUnderElevation: elevation,
      toolbarHeight: height,
      bottom:
          bottom != null
              ? PreferredSize(
                preferredSize: Size.fromHeight(bottomHeight ?? 0),
                child: bottom!,
              )
              : null,
      titleTextStyle: context.templateStyle.text.h4,
      systemOverlayStyle:
          systemOverlayStyle ??
          SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness:
                Theme.of(context).brightness == Brightness.dark
                    ? Brightness.light
                    : Brightness.dark,
            statusBarBrightness:
                Theme.of(context).brightness == Brightness.dark
                    ? Brightness.dark
                    : Brightness.light,
          ),
    );
  }

  Widget? _buildLeading(BuildContext context) {
    if (leading != null) {
      // 为自定义leading添加溢出保护
      return ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.4,
        ),
        child: leading!,
      );
    }

    if (showBackButton) {
      return IconButton(
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
        icon: Icon(
          Icons.arrow_back,
          size: UiConstants.fontSize24,
          color: context.templateColors.textPrimary,
        ),
        style: IconButton.styleFrom(
          padding: EdgeInsets.zero,
          minimumSize: Size(44, 44),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          splashFactory: NoSplash.splashFactory,
          overlayColor: Colors.transparent,
        ),
      );
    }

    return null;
  }
}
