/*
  === 自定义输入框组件 ===

  ✅ 已适配新的模板主题系统：
  - 使用 context.templateColors 替代 theme.xxx(context)
  - 使用 context.templateStyle.text.xxx 替代 themeStyles.xxx(context)
  - 支持多种主题模板（Base、OKX、Bitget）
  - 自动适应深色/浅色模式

  功能：
  - 支持自定义样式、高度、前缀后缀等功能
  - 使用 TextField 原生的 labelText 功能
  - 支持聚焦时边框高亮效果
  - 提供搜索输入框工厂构造函数
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/core/managers/focus_manager.dart';

/// 自定义输入框组件
///
/// 支持自定义样式、高度、前缀后缀等功能
class InputWidget extends StatefulWidget {
  /// 标签文本
  final String? labelText;

  /// 提示文本
  final String? hintText;

  /// 边框圆角半径
  final double? borderRadius;

  /// 边框颜色
  final Color? borderColor;

  /// 填充颜色
  final Color? fillColor;

  /// 标签文本样式
  final TextStyle? labelTextStyle;

  /// 提示文本样式
  final TextStyle? hitTextStyle;

  /// 输入文本样式
  final TextStyle? textStyle;

  /// 内容内边距
  final EdgeInsetsGeometry? contentPadding;

  /// 输入框高度
  final double? height;

  /// 文本控制器
  final TextEditingController? controller;

  /// 输入变化回调
  final ValueChanged<String>? onChanged;

  /// 提交回调
  final ValueChanged<String>? onSubmitted;

  /// 前缀图标或组件
  final Widget? prefixIcon;

  /// 后缀图标或组件
  final Widget? suffixIcon;

  /// 前缀图标约束
  final BoxConstraints? prefixIconConstraints;

  /// 后缀图标约束
  final BoxConstraints? suffixIconConstraints;

  /// 前缀图标内边距
  final EdgeInsetsGeometry? prefixIconPadding;

  /// 后缀图标内边距
  final EdgeInsetsGeometry? suffixIconPadding;

  /// 是否移除前缀图标默认边距
  final bool removePrefixIconPadding;

  /// 是否移除后缀图标默认边距
  final bool removeSuffixIconPadding;

  /// 是否自动获取焦点
  final bool autofocus;

  /// 焦点变化回调
  final ValueChanged<bool>? onFocusChanged;

  /// 聚焦时是否隐藏后缀图标
  final bool hideSuffixOnFocus;

  const InputWidget({
    super.key,
    this.labelText,
    this.hintText,
    this.borderRadius,
    this.borderColor,
    this.fillColor,
    this.contentPadding,
    this.height,
    this.controller,
    this.onChanged,
    this.onSubmitted,
    this.hitTextStyle,
    this.labelTextStyle,
    this.prefixIcon,
    this.suffixIcon,
    this.prefixIconConstraints,
    this.suffixIconConstraints,
    this.prefixIconPadding,
    this.suffixIconPadding,
    this.removePrefixIconPadding = false,
    this.removeSuffixIconPadding = false,
    this.autofocus = false,
    this.onFocusChanged,
    this.textStyle,
    this.hideSuffixOnFocus = false,
  });

  /// 创建搜索输入框
  const InputWidget.search({
    super.key,
    this.hintText = '搜索...',
    this.controller,
    this.onChanged,
    this.onSubmitted,
    this.autofocus = false,
    this.onFocusChanged,
    this.textStyle,
  }) : labelText = null,
       borderRadius = null,
       borderColor = null,
       fillColor = null,
       contentPadding = null,
       height = null,
       hitTextStyle = null,
       labelTextStyle = null,
       prefixIcon = null,
       suffixIcon = null,
       prefixIconConstraints = null,
       suffixIconConstraints = null,
       prefixIconPadding = null,
       suffixIconPadding = null,
       removePrefixIconPadding = false,
       removeSuffixIconPadding = false,
       hideSuffixOnFocus = false;

  @override
  State<InputWidget> createState() => _InputWidgetState();
}

class _InputWidgetState extends State<InputWidget> {
  late FocusNode _focusNode;
  late TextEditingController _effectiveController;
  final GlobalFocusManager _globalFocusManager = GlobalFocusManager.instance;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _effectiveController = widget.controller ?? TextEditingController();

    // 注册到全局焦点管理器
    _globalFocusManager.registerFocusNode(_focusNode);

    // 监听焦点变化
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChanged);

    // 从全局焦点管理器注销
    _globalFocusManager.unregisterFocusNode(_focusNode);

    _focusNode.dispose();

    // 只有当控制器是内部创建的时候才释放
    if (widget.controller == null) {
      _effectiveController.dispose();
    }

    super.dispose();
  }

  // 焦点变化处理
  void _onFocusChanged() {
    if (mounted) {
      setState(() {}); // 更新UI
    }

    // 调用外部回调
    widget.onFocusChanged?.call(_focusNode.hasFocus);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      padding: _getContainerPadding(),
      decoration: _getContainerDecoration(),
      child: TextField(
        controller: _effectiveController,
        focusNode: _focusNode,
        autofocus: widget.autofocus,
        cursorHeight: UiConstants.fontSize14,
        style: _getTextStyle(),
        decoration: _getInputDecoration(),
        onChanged: (value) {
          // 确保在文本变化时更新UI
          if (mounted) {
            setState(() {});
          }
          // 调用原始的onChanged回调
          widget.onChanged?.call(value);
        },
        onSubmitted: widget.onSubmitted,
      ),
    );
  }

  // 获取容器内边距
  EdgeInsetsGeometry? _getContainerPadding() {
    // 只有在有 labelText 时才应用聚焦 padding 变化
    if (widget.labelText == null || widget.labelText!.isEmpty) {
      return EdgeInsets.zero;
    }

    // 当获得焦点或输入框有值时，应用相同的底部padding
    final hasValue = _effectiveController.text.isNotEmpty;
    final shouldUseFocusPadding = _focusNode.hasFocus || hasValue;

    return shouldUseFocusPadding
        ? EdgeInsets.only(bottom: UiConstants.spacing10)
        : EdgeInsets.zero;
  }

  // 获取容器装饰
  BoxDecoration _getContainerDecoration() {
    final borderRadius = widget.borderRadius ?? UiConstants.borderRadius8;
    final fillColor =
        widget.fillColor ?? context.templateColors.inputBackground;

    return BoxDecoration(
      color: fillColor,
      borderRadius: BorderRadius.circular(borderRadius),
      border: Border.all(
        color: _getBorderColor(),
        width: UiConstants.borderWidth1,
      ),
    );
  }

  // 获取输入框装饰
  InputDecoration _getInputDecoration() {
    final contentPadding =
        widget.contentPadding ??
        EdgeInsets.symmetric(
          horizontal: UiConstants.spacing8,
          vertical: UiConstants.spacing4,
        );

    // 根据 hideSuffixOnFocus 参数决定是否显示后缀
    Widget? effectiveSuffixIcon = IntrinsicWidth(child: widget.suffixIcon);
    if (widget.hideSuffixOnFocus && _focusNode.hasFocus) {
      effectiveSuffixIcon = null;
    }

    return InputDecoration(
      filled: true,
      fillColor: Colors.transparent, // 背景色由外层Container控制
      labelText:
          (widget.labelText != null && widget.labelText!.isNotEmpty)
              ? widget.labelText
              : null, // 只有在有 labelText 时才显示
      hintText: widget.hintText,
      labelStyle: _getLabelTextStyle(),
      hintStyle: _getHintTextStyle(),
      floatingLabelBehavior: FloatingLabelBehavior.auto,
      contentPadding: contentPadding,
      prefixIcon: widget.prefixIcon,
      prefixIconConstraints: BoxConstraints(
        minWidth: widget.removePrefixIconPadding ? 0 : 40,
        minHeight: widget.removePrefixIconPadding ? 0 : 40,
      ),
      suffixIcon: effectiveSuffixIcon,
      border: InputBorder.none,
      enabledBorder: InputBorder.none,
      focusedBorder: InputBorder.none,
    );
  }

  // ========== 样式获取方法 ==========

  // 获取文本样式
  TextStyle _getTextStyle() {
    return widget.textStyle ?? context.templateStyle.text.bodyLarge;
  }

  // 获取标签文本样式
  TextStyle _getLabelTextStyle() {
    return widget.labelTextStyle ?? context.templateStyle.text.descriptionText;
  }

  // 获取提示文本样式
  TextStyle _getHintTextStyle() {
    return widget.hitTextStyle ??
        context.templateStyle.text.hintText.copyWith(
          color: context.templateColors.textTertiary,
        );
  }

  // 获取边框颜色
  Color _getBorderColor() {
    return _focusNode.hasFocus
        ? (widget.borderColor ??
            context.templateColors.inputFocusedBorder) // 聚焦时高亮
        : (widget.borderColor ?? context.templateColors.inputBorder); // 默认边框
  }
}
