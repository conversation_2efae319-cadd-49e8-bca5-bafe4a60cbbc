/*
 * 交易滑块组件
 *
 * 基于 BidirectionalSlider 封装的交易专用滑块
 * 适用于快速买卖操作
 */

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/common/bidirectional_slider.dart';

/// 交易类型
enum TradeType {
  buy, // 买入
  sell, // 卖出
  none, // 无操作
}

/// 交易滑块组件
class TradeSliderWidget extends StatefulWidget {
  /// 交易对名称
  final String symbol;

  /// 当前价格
  final String currentPrice;

  /// 滑块宽度
  final double? width;

  /// 滑块高度
  final double? height;

  /// 交易回调
  final Function(TradeType tradeType)? onTrade;

  /// 是否启用
  final bool enabled;

  const TradeSliderWidget({
    super.key,
    required this.symbol,
    required this.currentPrice,
    this.width,
    this.height,
    this.onTrade,
    this.enabled = true,
  });

  @override
  State<TradeSliderWidget> createState() => _TradeSliderWidgetState();
}

class _TradeSliderWidgetState extends State<TradeSliderWidget> {
  double _currentValue = 0.0;
  bool _isTrading = false;

  @override
  Widget build(BuildContext context) {
    final colors = context.templateColors;
    final textStyles = context.templateStyle.text;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colors.border),
      ),
      child: Column(
        children: [
          // 交易对信息
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.symbol,
                style: textStyles.h4.copyWith(
                  color: colors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                widget.currentPrice,
                style: textStyles.h4.copyWith(
                  color: colors.tradeBuy,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 交易滑块
          Opacity(
            opacity: widget.enabled ? 1.0 : 0.5,
            child: BidirectionalSlider(
              width: widget.width ?? double.infinity,
              height: widget.height ?? 50,
              leftText: '卖出 ${_getBaseCurrency()}',
              rightText: '买入 ${_getBaseCurrency()}',
              centerText: _isTrading ? '处理中...' : '滑动交易',
              enableHapticFeedback: widget.enabled,
              onChanged:
                  widget.enabled
                      ? (value) {
                        setState(() {
                          _currentValue = value;
                        });
                      }
                      : null,
              onDirectionChanged:
                  widget.enabled
                      ? (direction) {
                        if (!_isTrading) {
                          _handleTradeDirection(direction);
                        }
                      }
                      : null,
            ),
          ),

          const SizedBox(height: 12),

          // 提示文本
          Text(
            _getHintText(),
            style: textStyles.hintText.copyWith(color: colors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 获取基础货币名称
  String _getBaseCurrency() {
    return widget.symbol.split('/').first;
  }

  /// 获取提示文本
  String _getHintText() {
    if (!widget.enabled) {
      return '交易功能暂时不可用';
    }

    if (_isTrading) {
      return '正在处理交易请求...';
    }

    if (_currentValue < -0.1) {
      return '向左滑动完成卖出操作';
    } else if (_currentValue > 0.1) {
      return '向右滑动完成买入操作';
    } else {
      return '向左滑动卖出，向右滑动买入';
    }
  }

  /// 处理交易方向
  void _handleTradeDirection(SliderDirection direction) {
    TradeType tradeType = TradeType.none;

    switch (direction) {
      case SliderDirection.left:
        tradeType = TradeType.sell;
        break;
      case SliderDirection.right:
        tradeType = TradeType.buy;
        break;
      case SliderDirection.center:
        tradeType = TradeType.none;
        break;
    }

    if (tradeType != TradeType.none) {
      _executeTrade(tradeType);
    }
  }

  /// 执行交易
  void _executeTrade(TradeType tradeType) async {
    setState(() {
      _isTrading = true;
    });

    // 调用交易回调
    widget.onTrade?.call(tradeType);

    // 模拟交易处理时间
    await Future.delayed(const Duration(milliseconds: 1500));

    // 重置状态
    if (mounted) {
      setState(() {
        _isTrading = false;
        _currentValue = 0.0;
      });
    }
  }
}

/// 简化版交易滑块
class SimpleTradeSlider extends StatelessWidget {
  final Function(TradeType)? onTrade;
  final bool enabled;

  const SimpleTradeSlider({super.key, this.onTrade, this.enabled = true});

  @override
  Widget build(BuildContext context) {
    return BidirectionalSlider(
      width: double.infinity,
      height: 50,
      leftText: '卖出',
      rightText: '买入',
      centerText: '滑动交易',
      enableHapticFeedback: enabled,
      onDirectionChanged:
          enabled
              ? (direction) {
                TradeType tradeType = TradeType.none;

                switch (direction) {
                  case SliderDirection.left:
                    tradeType = TradeType.sell;
                    break;
                  case SliderDirection.right:
                    tradeType = TradeType.buy;
                    break;
                  case SliderDirection.center:
                    tradeType = TradeType.none;
                    break;
                }

                if (tradeType != TradeType.none) {
                  onTrade?.call(tradeType);
                }
              }
              : null,
    );
  }
}
