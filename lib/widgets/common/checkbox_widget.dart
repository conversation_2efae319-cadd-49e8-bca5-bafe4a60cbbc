/*
  === 自定义复选框组件 ===

  功能：
  - 支持自定义样式、大小、颜色等功能
  - 支持选中/未选中状态切换
  - 支持动画过渡效果
  - 支持自定义图标和文本
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/widgets/display/image_widget.dart';

/// 复选框尺寸枚举
enum CheckboxSize { small, medium, large }

/// 复选框形状枚举
enum CheckboxShape { square, rounded, circle }

/// 自定义复选框组件
///
/// 支持自定义样式、大小、颜色等功能
class CheckboxWidget extends StatefulWidget {
  /// 是否选中
  final bool value;

  /// 选中状态变化回调
  final ValueChanged<bool>? onChanged;

  /// 复选框大小
  final CheckboxSize size;

  /// 自定义尺寸（优先级高于size）
  final double? customSize;

  /// 复选框形状
  final CheckboxShape shape;

  /// 自定义圆角半径（仅在shape为rounded时生效）
  final double? borderRadius;

  /// 边框宽度
  final double? borderWidth;

  /// 选中时的颜色
  final Color? activeColor;

  /// 未选中时的边框颜色
  final Color? inactiveColor;

  /// 选中时的填充色
  final Color? activeFillColor;

  /// 未选中时的填充色
  final Color? inactiveFillColor;

  /// 选中时的图标颜色
  final Color? checkColor;

  /// 自定义选中图标
  final String? checkIcon;

  /// 自定义未选中图标
  final String? uncheckIcon;

  /// 图标大小
  final double? iconSize;

  /// 标签文本
  final String? label;

  /// 标签文本样式
  final TextStyle? labelStyle;

  /// 标签与复选框的间距
  final double? spacing;

  /// 标签位置（左侧或右侧）
  final bool labelOnLeft;

  /// 是否启用
  final bool enabled;

  /// 内边距
  final EdgeInsetsGeometry? padding;

  /// 点击区域扩展
  final EdgeInsetsGeometry? hitTestPadding;

  const CheckboxWidget({
    super.key,
    required this.value,
    this.onChanged,
    this.size = CheckboxSize.medium,
    this.customSize,
    this.shape = CheckboxShape.rounded,
    this.borderRadius,
    this.borderWidth,
    this.activeColor,
    this.inactiveColor,
    this.activeFillColor,
    this.inactiveFillColor,
    this.checkColor,
    this.checkIcon,
    this.uncheckIcon,
    this.iconSize,
    this.label,
    this.labelStyle,
    this.spacing,
    this.labelOnLeft = false,
    this.enabled = true,
    this.padding,
    this.hitTestPadding,
  });

  @override
  State<CheckboxWidget> createState() => _CheckboxWidgetState();
}

class _CheckboxWidgetState extends State<CheckboxWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: UiConstants.animationDurationShort,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // 根据初始值设置动画状态
    if (widget.value) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(CheckboxWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      if (widget.value) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final checkbox = _buildCheckbox();

    if (widget.label != null) {
      return _buildWithLabel(checkbox);
    }

    return checkbox;
  }

  // ========== 构建方法 ==========

  /// 构建复选框
  Widget _buildCheckbox() {
    final size = _getEffectiveSize();
    final padding = widget.padding ?? EdgeInsets.all(UiConstants.spacing2);
    final hitTestPadding = widget.hitTestPadding ?? EdgeInsets.zero;

    return InkWellWidget(
      onTap: widget.enabled ? _handleTap : null,
      child: Padding(
        padding: hitTestPadding,
        child: Container(
          padding: padding,
          child: AnimatedContainer(
            duration: UiConstants.animationDurationShort,
            width: size,
            height: size,
            decoration: _getBoxDecoration(),
            child: _buildCheckIcon(),
          ),
        ),
      ),
    );
  }

  /// 构建带标签的复选框
  Widget _buildWithLabel(Widget checkbox) {
    final spacing = widget.spacing ?? 8.0;
    final label = _buildLabel();

    return InkWellWidget(
      onTap: widget.enabled ? _handleTap : null,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children:
            widget.labelOnLeft
                ? [label, SizedBox(width: spacing), checkbox]
                : [checkbox, SizedBox(width: spacing), label],
      ),
    );
  }

  /// 构建标签
  Widget _buildLabel() {
    if (widget.label == null) return const SizedBox.shrink();

    return Text(widget.label!, style: _getLabelStyle());
  }

  /// 构建图标（选中或未选中）
  Widget _buildCheckIcon() {
    final iconSize = _getEffectiveIconSize();

    // 根据状态选择图标
    String? iconName;
    if (widget.value) {
      iconName = widget.checkIcon ?? 'icon_checked';
    } else if (widget.uncheckIcon != null) {
      iconName = widget.uncheckIcon;
    }

    // 如果没有图标则不显示
    if (iconName == null) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        // 选中状态有缩放动画，未选中状态直接显示
        if (widget.value) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Opacity(
              opacity: _opacityAnimation.value,
              child: ThemedImage(
                name: iconName!,
                width: iconSize,
                height: iconSize,
                followTheme: true,
              ),
            ),
          );
        } else {
          // 未选中状态直接显示图标
          return ThemedImage(
            name: iconName!,
            width: iconSize,
            height: iconSize,
            followTheme: true,
            showSkeleton: false,
          );
        }
      },
    );
  }

  // ========== 事件处理 ==========

  /// 处理点击事件
  void _handleTap() {
    if (widget.onChanged != null) {
      widget.onChanged!(!widget.value);
    }
  }

  // ========== 样式获取方法 ==========

  /// 获取有效尺寸
  double _getEffectiveSize() {
    if (widget.customSize != null) {
      return widget.customSize!;
    }

    switch (widget.size) {
      case CheckboxSize.small:
        return UiConstants.iconSize16;
      case CheckboxSize.medium:
        return UiConstants.iconSize20;
      case CheckboxSize.large:
        return UiConstants.iconSize24;
    }
  }

  /// 获取有效图标尺寸
  double _getEffectiveIconSize() {
    if (widget.iconSize != null) {
      return widget.iconSize!;
    }

    final size = _getEffectiveSize();
    return size * 0.7; // 图标大小为复选框的70%
  }

  /// 获取盒子装饰
  BoxDecoration _getBoxDecoration() {
    final borderRadius = _getBorderRadius();
    final borderWidth = widget.borderWidth ?? UiConstants.borderWidth0_5;

    return BoxDecoration(
      color: widget.value ? _getActiveFillColor() : _getInactiveFillColor(),
      borderRadius: borderRadius,
      border: Border.all(
        color: widget.value ? _getActiveColor() : _getInactiveColor(),
        width: borderWidth,
      ),
    );
  }

  /// 获取边框圆角
  BorderRadius? _getBorderRadius() {
    switch (widget.shape) {
      case CheckboxShape.square:
        return BorderRadius.zero;
      case CheckboxShape.rounded:
        final radius = widget.borderRadius ?? UiConstants.borderRadius4;
        return BorderRadius.circular(radius);
      case CheckboxShape.circle:
        final size = _getEffectiveSize();
        return BorderRadius.circular(size / 2);
    }
  }

  /// 获取激活颜色
  Color _getActiveColor() {
    if (!widget.enabled) {
      return context.templateColors.textTertiary;
    }
    return widget.activeColor ?? context.templateColors.primary;
  }

  /// 获取非激活颜色
  Color _getInactiveColor() {
    if (!widget.enabled) {
      return context.templateColors.textTertiary;
    }
    return widget.inactiveColor ?? context.templateColors.textTertiary;
  }

  /// 获取激活填充颜色
  Color _getActiveFillColor() {
    if (!widget.enabled) {
      return context.templateColors.textTertiary;
    }
    return widget.activeFillColor ?? _getActiveColor();
  }

  /// 获取非激活填充颜色
  Color _getInactiveFillColor() {
    if (!widget.enabled) {
      return Colors.transparent;
    }
    return widget.inactiveFillColor ?? Colors.transparent;
  }

  /// 获取标签样式
  TextStyle _getLabelStyle() {
    final baseStyle =
        widget.labelStyle ?? context.templateStyle.text.bodyLargeMedium;

    if (!widget.enabled) {
      return baseStyle.copyWith(color: context.templateColors.textTertiary);
    }

    return baseStyle;
  }
}
