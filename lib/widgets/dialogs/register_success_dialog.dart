/*
*  注册成功底部弹窗
*/

import 'package:flutter/material.dart';
import 'package:remixicon/remixicon.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class RegisterSuccessDialog {
  // 显示注册成功底部弹窗
  static Future<void> show(BuildContext context) async {
    return await BottomSheetWidget.show<void>(
      context: context,
      showCloseButton: false,
      showHeader: false,
      showDragHandle: false,
      showHeaderBorder: false,
      showBottomButton: false,
      useSafeArea: false,
      child: _RegisterSuccessContent(),
    );
  }
}

class _RegisterSuccessContent extends StatefulWidget {
  const _RegisterSuccessContent();

  @override
  State<_RegisterSuccessContent> createState() =>
      _RegisterSuccessContentState();
}

class _RegisterSuccessContentState extends State<_RegisterSuccessContent> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(UiConstants.spacing18),
      child: Stack(
        children: [
          // 关闭按钮
          IconButtonWidget(
            icon: RemixIcons.close_circle_fill,
            size: UiConstants.iconSize20,
            iconColor: context.templateColors.textTertiary,
            onTap: () => Navigator.of(context).pop(),
          ),

          // 弹窗内容
          Container(
            child: Column(
              children: [_buildHeader(), _buildCard(), _buildButtons()],
            ),
          ),
        ],
      ),
    );
  }

  // 构建顶部内容
  Widget _buildHeader() {
    return Column(
      children: [
        ThemedImage(name: 'icon_allow_logo', size: 80, followTheme: true),
        Padding(
          padding: EdgeInsets.symmetric(vertical: UiConstants.spacing10),
          child: Column(
            children: [
              Text('账户注册成功', style: context.templateStyle.text.h4),
              SizedBox(height: UiConstants.spacing8),
              Text(
                '想要使用 CP Exchange 全部功能，请先完成身份认证，否则以下功能将被禁止使用。',
                style: context.templateStyle.text.bodyTextMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 构建卡片内容
  Widget _buildCard() {
    /// 内容项
    Widget buildInfoCell({
      required String title,
      bool isAvailable = false,
      bool isHeader = false,
    }) {
      return Padding(
        padding: EdgeInsets.symmetric(vertical: UiConstants.spacing10),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                title,
                style:
                    isHeader
                        ? context.templateStyle.text.bodyTextMedium
                        : context.templateStyle.text.descriptionText,
              ),
            ),
            Expanded(
              child:
                  isHeader
                      ? Text(
                        '认证前',
                        style: context.templateStyle.text.bodyTextMedium,
                      )
                      : Icon(RemixIcons.compass_4_line),
            ),
            Expanded(
              child:
                  isHeader
                      ? Text(
                        '认证后',
                        style: context.templateStyle.text.bodyTextMedium,
                      )
                      : Icon(RemixIcons.checkbox_circle_line),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(UiConstants.borderRadius10),
      decoration: BoxDecoration(
        color: context.templateColors.cardBackground,
        borderRadius: BorderRadius.circular(UiConstants.borderRadius16),
      ),
      child: Column(
        children: [
          buildInfoCell(title: '账户礼遇', isHeader: true),
          buildInfoCell(title: '加密货币充值', isHeader: false),
          buildInfoCell(title: '加密货币提现', isHeader: false),
          buildInfoCell(title: '交易等其他服务', isHeader: false),
        ],
      ),
    );
  }

  // 构建按钮组
  Widget _buildButtons() {
    return Column(
      children: [
        CommonButton.primary(
          '立即认证',
          width: double.infinity,
          height: UiConstants.buttonHeightMedium,
          borderRadius: UiConstants.borderRadius20,
          onPressed: () => {},
        ),
        SizedBox(height: UiConstants.spacing16),
        CommonButton(
          text: '稍后认证',
          width: double.infinity,
          height: UiConstants.buttonHeightMedium,
          type: CommonButtonType.text,
          onPressed: () => Navigator.of(context).pop(),
        ),
      ],
    );
  }
}
