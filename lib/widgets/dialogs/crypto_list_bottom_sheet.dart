/*
*  数字货币列表底部弹窗
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/utils/index.dart';

import 'package:qubic_exchange/services/market/market_service.dart';
import 'package:qubic_exchange/services/market/models/main_tab_model.dart';
import 'package:qubic_exchange/services/market/models/currency_model.dart';
import 'package:qubic_exchange/services/common/common_service.dart';
import 'package:qubic_exchange/services/storage/storage_service.dart';
import 'package:qubic_exchange/l10n/managers/language_manager.dart';

// 数字货币列表项模型
class CryptoListItem {
  final int? currencyId; // 币种ID
  final String baseCurrency; // 基础货币
  final String quoteCurrency; // 计价货币
  final double price; // 最新价
  final double changePercent; // 涨跌幅百分比
  final double changeValue; // 涨跌额
  final double volume; // 成交额
  final bool isFavorite; // 是否收藏
  final String category; // 分类标签
  final String? type; // 类型
  final bool isNew; // 是否为新上线代币
  final bool hasCandyBomb; // 是否有糖果炸弹/空投活动
  final int? pricePrecision; // 价格精度

  const CryptoListItem({
    this.currencyId,
    required this.baseCurrency,
    required this.quoteCurrency,
    required this.price,
    required this.changePercent,
    required this.changeValue,
    required this.volume,
    required this.isFavorite,
    required this.category,
    this.type,
    this.isNew = false,
    this.hasCandyBomb = false,
    this.pricePrecision,
  });

  // 获取交易对符号
  String get symbol => '$baseCurrency$quoteCurrency';

  // 获取显示名称（用于现货模式）
  String get displayName => '$baseCurrency / $quoteCurrency';

  // 获取格式化的价格
  String get formattedPrice {
    // 对于小价格，自动调整精度以避免显示为0
    int precision = pricePrecision ?? 2;

    // 如果价格小于0.01且使用默认精度，则增加精度
    if (price < 0.01 && precision <= 2) {
      if (price < 0.000001) {
        precision = 8; // 非常小的价格使用8位精度
      } else if (price < 0.0001) {
        precision = 6; // 小价格使用6位精度
      } else {
        precision = 4; // 中等小价格使用4位精度
      }
    }

    return NumberFormatUtil.formatWithComma(price, decimalDigits: precision);
  }

  // 获取格式化的涨跌幅
  String get formattedChangePercent {
    final sign = changePercent >= 0 ? '+' : '';
    return '$sign${NumberFormatUtil.formatWithComma(changePercent, decimalDigits: 2)}%';
  }
}

class CryptoListBottomSheet {
  // 显示数字货币列表底部弹窗
  static Future<String?> show(
    BuildContext context, {
    bool showHeader = true,
    bool showCloseButton = false,
    bool showDragHandle = true,
    bool showHeaderBorder = false,
    double? maxHeight,
    double? headerHeight = 66,
    bool useSafeArea = false,
    bool isContract = false,
    TextAlign titleAlign = TextAlign.start,
    int marketType = 1, // 市场类型：1=现货，5=合约
    String? field, // 字段参数
    Function(CryptoListItem)? onItemSelected, // 选择回调
    Function(CryptoListItem, bool)? onFavoriteChanged, // 收藏状态变化回调
  }) async {
    return await BottomSheetWidget.show<String>(
      context: context,
      title: '货币列表',
      titleAlign: titleAlign,
      showCloseButton: showCloseButton,
      showHeader: showHeader,
      showDragHandle: showDragHandle,
      headerHeight: headerHeight,
      showHeaderBorder: showHeaderBorder,
      maxHeight:
          maxHeight ?? ScreenUtil.getScreenInfo(context)['screenHeight'] * 0.85,
      showBottomButton: false,
      useSafeArea: useSafeArea,
      child: _CryptoListContent(
        isContract: isContract,
        marketType: marketType,
        field: field,
        onItemSelected: onItemSelected,
        onFavoriteChanged: onFavoriteChanged,
      ),
    );
  }
}

class _CryptoListContent extends StatefulWidget {
  final bool isContract;
  final int marketType;
  final String? field;
  final Function(CryptoListItem)? onItemSelected;
  final Function(CryptoListItem, bool)? onFavoriteChanged;

  const _CryptoListContent({
    this.isContract = false,
    this.marketType = 1,
    this.field,
    this.onItemSelected,
    this.onFavoriteChanged,
  });

  @override
  State<_CryptoListContent> createState() => _CryptoListContentState();
}

class _CryptoListContentState extends State<_CryptoListContent>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  // 标签控制器
  late TabController _mainController;
  late TabController _subController;

  // 动态标签数据
  List<TabItem> _mainTabTitles = [];
  List<TabItem> _subTabTitles = [];

  // 缓存的自选币种数据Future
  Future<List<CryptoListItem>>? _favoriteCoinsFuture;

  // 当前选中的标签索引
  int _currentMainTabIndex = 0;
  int _currentSubTabIndex = 0;



  @override
  bool get wantKeepAlive => true;

  /// 获取当前语言代码
  String _getCurrentLanguageCode() {
    final languageManager = LanguageManager();
    final currentLocale = languageManager.currentLocale;

    if (currentLocale.countryCode != null) {
      return '${currentLocale.languageCode}_${currentLocale.countryCode}';
    } else {
      return currentLocale.languageCode;
    }
  }

  /// 获取自选币种列表
  Future<List<CryptoListItem>> _getFavoriteCoins() async {
    final storage = StorageService.instance;
    final cacheKey = 'favorites_market_${widget.marketType}';

    final favoriteIds = storage.getList(cacheKey);
    if (favoriteIds == null || favoriteIds.isEmpty) {
      return [];
    }

    final marketService = MarketService.instance;
    final favoriteCoins = <CryptoListItem>[];

    for (final idData in favoriteIds) {
      final currencyId = idData is int ? idData : int.tryParse(idData.toString());
      if (currencyId == null) continue;

      try {
        final currency = marketService.currencyModels.firstWhere(
          (c) => c.id == currencyId,
        );

        final ticker = currency.tickers[widget.marketType.toString()];

        favoriteCoins.add(CryptoListItem(
          currencyId: currency.id,
          baseCurrency: currency.baseAsset,
          quoteCurrency: currency.quoteAsset,
          price: ticker?.lastPrice ?? 0.0,
          changePercent: ticker?.priceChangeP ?? 0.0,
          changeValue: ticker?.priceChange ?? 0.0,
          volume: ticker?.volume ?? 0.0,
          isFavorite: true,
          category: '',
          pricePrecision: currency.mPricePrecision,
        ));
      } catch (e) {
        // 币种不存在时跳过
        continue;
      }
    }

    return favoriteCoins;
  }

  /// 根据主标签筛选币种数据
  List<CryptoListItem> _getFilteredCoins(int mainTabIndex) {
    if (mainTabIndex == 0 || _mainTabTitles.isEmpty || mainTabIndex >= _mainTabTitles.length) {
      return [];
    }

    final currentMainTabTitle = _mainTabTitles[mainTabIndex].title;
    final marketService = MarketService.instance;
    final mainTabModels = marketService.mainTabModels;
    final currentLang = _getCurrentLanguageCode();

    // 找到对应的主标签模型
    for (final mainTab in mainTabModels) {
      final mainTabLangList = mainTab.title.map((text) => {
        'lang': text.lang,
        'text': text.text,
      }).toList();

      final mainTabTitle = CommonService.getLocalizedText(mainTabLangList, targetLang: currentLang, fallbackLang: 'en');

      if (mainTabTitle == currentMainTabTitle) {
        // 获取筛选条件
        final filterCondition = _getFilterCondition(mainTab.sub);
        if (filterCondition != null) {
          return _filterCurrencies(filterCondition);
        }
        break;
      }
    }

    return [];
  }

  /// 获取筛选条件
  Map<String, dynamic>? _getFilterCondition(List<SubTabModel> subTabs) {
    if (subTabs.isEmpty) return null;

    // 如果是List，取第一个元素
    final firstSub = subTabs.first;

    return {
      'source': firstSub.source,
      'market_type': firstSub.marketType,
      'field': firstSub.field,
      'sort_field': firstSub.sortField,
      'sort': firstSub.sort,
    };
  }

  /// 根据条件筛选币种
  List<CryptoListItem> _filterCurrencies(Map<String, dynamic> condition) {
    final marketService = MarketService.instance;
    final currencies = marketService.currencyModels;
    final filteredCurrencies = <CurrencyModel>[];

    for (final currency in currencies) {
      // 检查 market_type 匹配
      if (condition['market_type'] != widget.marketType) continue;

      // 检查 field 字段条件
      final field = condition['field'] as String;
      if (field.isNotEmpty) {
        bool fieldValue = false;
        switch (field) {
          case 'is_spotTrade':
            fieldValue = currency.isSpotTrade == 1;
            break;
          case 'is_marginTrade':
            fieldValue = currency.isMarginTrade == 1;
            break;
          default:
            continue;
        }
        if (!fieldValue) continue;
      }

      // 检查ticker数据是否存在
      final ticker = currency.tickers[widget.marketType.toString()];
      if (ticker == null) continue;

      filteredCurrencies.add(currency);
    }

    // 排序
    _sortCoins(filteredCurrencies, condition);

    // 转换为CryptoListItem
    final filteredCoins = <CryptoListItem>[];
    for (final currency in filteredCurrencies) {
      final ticker = currency.tickers[widget.marketType.toString()]!;

      filteredCoins.add(CryptoListItem(
        currencyId: currency.id,
        baseCurrency: currency.baseAsset,
        quoteCurrency: currency.quoteAsset,
        price: ticker.lastPrice,
        changePercent: ticker.priceChangeP,
        changeValue: ticker.priceChange,
        volume: ticker.volume,
        isFavorite: CommonService.isFavoriteCoin(currency.id, widget.marketType),
        category: '',
        pricePrecision: currency.mPricePrecision,
      ));
    }

    return filteredCoins;
  }

  /// 排序币种列表
  void _sortCoins(List<CurrencyModel> currencies, Map<String, dynamic> condition) {
    final sortField = condition['sort_field'] as String;
    final sortOrder = condition['sort'] as int; // 0=asc, 1=desc

    currencies.sort((a, b) {
      int comparison = 0;

      if (sortField.startsWith('tickers.')) {
        // 解析 tickers.{marketType}.{field} 格式
        final parts = sortField.split('.');
        if (parts.length == 3) {
          final marketType = parts[1];
          final field = parts[2];

          try {
            final tickerA = a.tickers[marketType];
            final tickerB = b.tickers[marketType];

            if (tickerA != null && tickerB != null) {
              double valueA = 0.0;
              double valueB = 0.0;

              switch (field) {
                case 'price_changeP':
                  valueA = tickerA.priceChangeP;
                  valueB = tickerB.priceChangeP;
                  break;
                case 'last_price':
                  valueA = tickerA.lastPrice;
                  valueB = tickerB.lastPrice;
                  break;
                case 'volume':
                  valueA = tickerA.volume;
                  valueB = tickerB.volume;
                  break;
                case 'price_change':
                  valueA = tickerA.priceChange;
                  valueB = tickerB.priceChange;
                  break;
                default:
                  valueA = tickerA.lastPrice;
                  valueB = tickerB.lastPrice;
              }

              comparison = valueA.compareTo(valueB);
            } else {
              // 如果ticker数据不存在，按ID排序
              comparison = a.id.compareTo(b.id);
            }
          } catch (e) {
            // 如果获取ticker数据失败，按ID排序
            comparison = a.id.compareTo(b.id);
          }
        }
      } else {
        // 直接字段排序
        switch (sortField) {
          case 'id':
            comparison = a.id.compareTo(b.id);
            break;
          case 'created_at':
            // 按ID排序作为创建时间的替代
            comparison = a.id.compareTo(b.id);
            break;
          default:
            comparison = a.id.compareTo(b.id);
        }
      }

      return sortOrder == 0 ? comparison : -comparison;
    });
  }

  /// 应用子标签栏目筛选
  List<CryptoListItem> _applyCategoryFilter(List<CryptoListItem> coins, int subTabIndex) {
    if (subTabIndex == 0 || _subTabTitles.isEmpty || subTabIndex >= _subTabTitles.length) {
      // 第一个子标签"全部"，不筛选栏目
      return coins;
    }

    final currentSubTabTitle = _subTabTitles[subTabIndex].title;
    final marketService = MarketService.instance;
    final categoryModels = marketService.categoryModels;
    final currentLang = _getCurrentLanguageCode();

    // 找到对应的栏目ID
    int? categoryId;
    for (final category in categoryModels) {
      final categoryName = category.getName(targetLang: currentLang, fallbackLang: 'en');
      if (categoryName == currentSubTabTitle) {
        categoryId = category.id;
        break;
      }
    }

    if (categoryId == null) return coins;

    // 筛选符合栏目的币种：检查币种的cateIds是否包含当前栏目ID
    return coins.where((coin) {
      if (coin.currencyId == null) return false;

      try {
        final currency = marketService.currencyModels.firstWhere(
          (c) => c.id == coin.currencyId,
          orElse: () => throw StateError('Currency not found'),
        );

        // 检查币种的cateIds列表是否包含当前栏目ID
        return currency.cateIds.contains(categoryId);
      } catch (e) {
        return false;
      }
    }).toList();
  }





  // 初始化
  @override
  void initState() {
    super.initState();

    _initializeData();
  }

  void _initializeData() async {
    final marketService = MarketService.instance;

    // 如果没有主标签数据，先尝试获取
    if (marketService.mainTabModels.isEmpty) {
      await marketService.fetchMainTabData();
    }

    _generateMainTabs();
    _generateSubTabs();

    _mainController = TabController(length: _mainTabTitles.length, vsync: this);
    _subController = TabController(length: _subTabTitles.length, vsync: this);

    _mainController.addListener(_onMainTabChanged);
    _subController.addListener(_onSubTabChanged);

    // 初始化当前标签索引
    _currentMainTabIndex = _mainController.index;
    _currentSubTabIndex = _subController.index;

    // 预加载自选币种数据
    _favoriteCoinsFuture = _getFavoriteCoins();

    if (mounted) {
      setState(() {});
    }
  }

  void _onMainTabChanged() {
    if (_mainController.indexIsChanging) {
      _currentMainTabIndex = _mainController.index;

      // 如果切换到自选标签，重新加载自选数据
      if (_currentMainTabIndex == 0) {
        _favoriteCoinsFuture = _getFavoriteCoins();
      }

      final oldSubTabsLength = _subTabTitles.length;
      _generateSubTabs(mainTabIndex: _currentMainTabIndex);

      // 重置子标签索引为0（第一个"全部"）
      _currentSubTabIndex = 0;

      // 只有在子标签数量发生变化时才重建控制器
      if (oldSubTabsLength != _subTabTitles.length) {
        _subController.removeListener(_onSubTabChanged);
        _subController.dispose();
        _subController = TabController(length: _subTabTitles.length, vsync: this);
        _subController.addListener(_onSubTabChanged);

        // 使用 WidgetsBinding 延迟更新，避免在构建过程中调用 setState
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {});
          }
        });
      } else {
        // 子标签数量没变化，但需要重置子标签控制器索引
        if (_subController.length > 0) {
          _subController.animateTo(0);
        }
        if (mounted) {
          setState(() {});
        }
      }
    }
  }

  void _onSubTabChanged() {
    if (_subController.indexIsChanging) {
      _currentSubTabIndex = _subController.index;

      if (mounted) {
        setState(() {});
      }
    }
  }

  /// 根据 marketType 生成主标签
  void _generateMainTabs() {
    _mainTabTitles = [TabItem(title: '自选')];

    final marketService = MarketService.instance;
    final mainTabModels = marketService.mainTabModels;
    final currentLang = _getCurrentLanguageCode();

    for (final mainTab in mainTabModels) {
      final hasMatchingMarketType = mainTab.sub.any((subTab) => subTab.marketType == widget.marketType);

      if (hasMatchingMarketType) {
        final langList = mainTab.title.map((text) => {
          'lang': text.lang,
          'text': text.text,
        }).toList();

        final title = CommonService.getLocalizedText(langList, targetLang: currentLang, fallbackLang: 'en');
        if (title.isNotEmpty) {
          _mainTabTitles.add(TabItem(title: title));
        }
      }
    }
  }

  /// 根据当前主标签生成子标签
  void _generateSubTabs({int mainTabIndex = 0}) {
    _subTabTitles = [];

    // 自选标签没有子标签
    if (mainTabIndex == 0) {
      return;
    }

    if (_mainTabTitles.isEmpty || mainTabIndex >= _mainTabTitles.length) {
      return;
    }

    // 使用币种栏目数据生成子标签
    final marketService = MarketService.instance;
    final categoryModels = marketService.categoryModels;
    final currentLang = _getCurrentLanguageCode();

    // 第一个固定为"全部"
    _subTabTitles.add(TabItem(title: '全部'));

    // 添加币种栏目数据
    for (final category in categoryModels) {
      if (category.status == 1) { // 只显示有效的栏目
        final categoryName = category.getName(targetLang: currentLang, fallbackLang: 'en');
        if (categoryName.isNotEmpty) {
          _subTabTitles.add(TabItem(title: categoryName));
        }
      }
    }
  }

  // 销毁
  @override
  void dispose() {
    _mainController.removeListener(_onMainTabChanged);
    _subController.removeListener(_onSubTabChanged);
    _mainController.dispose();
    _subController.dispose();
    super.dispose();
  }

  // 构建 UI 内容
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Column(
      children: [
        // 搜索
        _buildSearch(),

        // 主标签栏
        _buildMainTabbar(),

        // 主标签内容
        _buildMainTabview(),
      ],
    );
  }

  // 构建加密数字货币搜索
  Widget _buildSearch() {
    return TextFieldWidget(
      height: 36,
      hintText: '搜索',
      prefixIcon: Padding(
        padding: EdgeInsets.only(left: UiConstants.spacing12),
        child: ThemedImage.asset('icon_search', width: 18, height: 18),
      ),
      focusBorderColor: context.templateColors.textPrimary,
    );
  }

  // 构建加密数字货币主标签栏
  Widget _buildMainTabbar() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing12),
      child: TabbarWidget(
        controller: _mainController,
        tabs: _mainTabTitles,
        height: 38,
        labelStyle: context.templateStyle.text.tabTextSmall,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: true,
        indicatorSize: TabBarIndicatorSize.label,
        labelPadding: EdgeInsets.only(right: UiConstants.spacing18),
      ),
    );
  }

  // 构建加密数字货币主标签内容
  Widget _buildMainTabview() {
    return SizedBox(
      height: ScreenUtil.screenHeight(context) * 0.7,
      child: TabBarView(
        controller: _mainController,
        children: _mainTabTitles.asMap().entries.map((entry) {
          final index = entry.key;
          final tabTitle = entry.value.title;

          // 自选标签不显示子标签栏
          if (tabTitle == '自选') {
            return Column(
              children: [
                _buildCryptoSort(),
                Expanded(child: _buildCryptoList()),
              ],
            );
          } else {
            // 其他主标签显示子标签栏（如果有子标签的话）
            if (_subTabTitles.isNotEmpty) {
              return Column(
                children: [_buildSubTabbar(index), _buildSubTabview(index)],
              );
            } else {
              return Column(
                children: [
                  _buildCryptoSort(),
                  Expanded(child: _buildCryptoList()),
                ],
              );
            }
          }
        }).toList(),
      ),
    );
  }

  // 构建加密数字货币子标签栏
  Widget _buildSubTabbar(int mainTabIndex) {
    if (_subTabTitles.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing4),
      child: TabbarWidget(
        controller: _subController,
        tabs: _subTabTitles,
        height: 24,
        labelStyle: context.templateStyle.text.hintTextMedium,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: true,
        indicatorStyle: TabBarIndicatorStyle.filled,
        labelPadding: EdgeInsets.zero,
        tabPadding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
      ),
    );
  }

  // 构建加密数字货币子标签内容
  Widget _buildSubTabview(int mainTabIndex) {
    if (_subTabTitles.isEmpty) {
      return const SizedBox.shrink();
    }

    return Expanded(
      child: TabBarView(
        controller: _subController,
        children: _subTabTitles.asMap().entries.map((entry) {
          return _AutomaticKeepAlive(
            child: Column(
              children: [
                _buildCryptoSort(),
                Expanded(child: _buildCryptoList()),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  // 构建加密数字货币排序
  Widget _buildCryptoSort() {
    /// 排序组
    Widget buildSortGroup({
      required String title,
      required String sortKey,
      required String subTitle,
      required String sortSubKey,
    }) {
      return Row(
        children: [
          InkWellWidget(
            child: Row(
              children: [
                Text(title, style: context.templateStyle.text.hintText),
                ThemedImage(
                  name: 'sort_normal',
                  size: UiConstants.iconSize14,
                  followTheme: true,
                  margin: EdgeInsets.only(left: UiConstants.spacing4),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing6),
            child: Text('/', style: context.templateStyle.text.hintText),
          ),
          InkWellWidget(
            child: Row(
              children: [
                Text(subTitle, style: context.templateStyle.text.hintText),
                ThemedImage(
                  name: 'sort_normal',
                  size: UiConstants.iconSize14,
                  followTheme: true,
                  margin: EdgeInsets.only(left: UiConstants.spacing4),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          /// 名称 & 成交额
          buildSortGroup(
            title: '名称',
            sortKey: 'symbol',
            subTitle: '成交额',
            sortSubKey: 'volume',
          ),

          /// 最新价 & 涨跌幅
          buildSortGroup(
            title: '最新价',
            sortKey: 'price',
            subTitle: '涨跌幅',
            sortSubKey: 'changePercent',
          ),
        ],
      ),
    );
  }

  /// 处理收藏状态切换
  Future<void> _handleFavoriteToggle(CryptoListItem item) async {
    if (item.currencyId == null) return;

    final currencyId = item.currencyId!;
    final newFavoriteState = !item.isFavorite;

    try {
      bool success;
      if (newFavoriteState) {
        // 添加到自选
        success = await CommonService.addFavoriteCoin(currencyId, widget.marketType);
      } else {
        // 从自选移除
        success = await CommonService.removeFavoriteCoin(currencyId, widget.marketType);
      }

      if (success) {
        // 调用外部回调
        widget.onFavoriteChanged?.call(item, newFavoriteState);

        // 如果当前在自选标签，重新加载自选数据
        if (_currentMainTabIndex == 0) {
          _favoriteCoinsFuture = _getFavoriteCoins();
        }

        // 刷新UI - 这会重新构建列表，确保收藏状态同步
        if (mounted) {
          setState(() {});
        }
      }
    } catch (e) {
      // 可以添加错误提示
      debugPrint('收藏状态切换失败: $e');
    }
  }

  // 构建加密数字货币列表
  Widget _buildCryptoList() {
    if (_currentMainTabIndex == 0) {
      // 自选标签：显示自选币种
      return FutureBuilder<List<CryptoListItem>>(
        future: _favoriteCoinsFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(child: Text('加载失败: ${snapshot.error}'));
          }

          final favoriteCoins = snapshot.data ?? [];

          if (favoriteCoins.isEmpty) {
            return const Center(child: Text('暂无自选币种'));
          }

          return _buildCryptoListView(favoriteCoins);
        },
      );
    } else {
      // 其他标签：使用主标签筛选
      List<CryptoListItem> cryptoList = _getFilteredCoins(_currentMainTabIndex);

      // 应用子标签栏目筛选
      cryptoList = _applyCategoryFilter(cryptoList, _currentSubTabIndex);

      return _buildCryptoListView(cryptoList);
    }
  }

  // 构建币种列表视图
  Widget _buildCryptoListView(List<CryptoListItem> cryptoItems) {

    /// 加密数字货币列表项
    Widget buildCryptoItem(CryptoListItem item) {
      return InkWellWidget(
        onTap: () {
          widget.onItemSelected?.call(item);
        },
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: UiConstants.spacing10),
          child: Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.isContract)
                    Column(
                      children: [
                        Row(
                          children: [
                            Text(
                              item.baseCurrency,
                              style: context.templateStyle.text.bodyLargeMedium,
                            ),
                            if (item.hasCandyBomb)
                              TagWidget(
                                text: 'CandyBomb',
                                borderColor: context.templateColors.primary,
                                padding: EdgeInsets.symmetric(
                                  horizontal: UiConstants.spacing4,
                                ),
                              ),
                            if (item.isNew)
                              TagWidget(
                                text: 'New',
                                borderColor: context.templateColors.primary,
                                padding: EdgeInsets.symmetric(
                                  horizontal: UiConstants.spacing4,
                                ),
                              ),
                          ],
                        ),
                        if (item.type != null)
                          Text(
                            item.type!,
                            style: context.templateStyle.text.descriptionSmall,
                          ),
                      ],
                    )
                  else
                    Text.rich(
                      TextSpan(
                        style: context.templateStyle.text.bodyLargeMedium,
                        children: [
                          TextSpan(text: item.baseCurrency),
                          TextSpan(
                            text: ' / ${item.quoteCurrency}',
                            style: context.templateStyle.text.descriptionSmall,
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              Spacer(),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    item.formattedPrice,
                    style: context.templateStyle.text.bodyLargeMedium,
                  ),
                  Text(
                    item.formattedChangePercent,
                    style: context.templateStyle.text.descriptionSmall.copyWith(
                      color:
                          item.changePercent >= 0
                              ? context.templateColors.tradeBuy
                              : context.templateColors.tradeSell,
                    ),
                  ),
                ],
              ),
              InkWellWidget(
                onTap: () {
                  _handleFavoriteToggle(item);
                },
                child: Padding(
                  padding: EdgeInsets.only(left: UiConstants.spacing10),
                  child: ThemedImage.asset(
                    item.isFavorite ? 'icon_favorite' : 'icon_favorite_off',
                    size: UiConstants.iconSize20,
                    followTheme: true,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // 如果没有数据，显示空状态
    if (cryptoItems.isEmpty) {
      return EmptyWidget(text: '暂无货币', imageName: 'noData', imageSize: 50);
    }

    return ListView.builder(
      itemCount: cryptoItems.length,
      itemBuilder: (context, index) {
        final item = cryptoItems[index];
        return buildCryptoItem(item);
      },
    );
  }
}

// 自动保持状态的包装器
class _AutomaticKeepAlive extends StatefulWidget {
  final Widget child;

  const _AutomaticKeepAlive({required this.child});

  @override
  State<_AutomaticKeepAlive> createState() => _AutomaticKeepAliveState();
}

class _AutomaticKeepAliveState extends State<_AutomaticKeepAlive>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => true;
}
