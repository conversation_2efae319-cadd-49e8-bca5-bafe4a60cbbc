/*
*  功能选择底部弹窗
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/utils/device/screen_util.dart';
import 'package:qubic_exchange/widgets/index.dart';

// 功能项模型
class FunctionItem {
  final String title;
  final String imageName;
  final VoidCallback onTap;
  final ImageFormat format;

  const FunctionItem({
    required this.title,
    required this.imageName,
    required this.onTap,
    this.format = ImageFormat.webp,
  });
}

class FunctionBottomSheet {
  // 显示功能选择底部弹窗
  static Future<void> show(
    BuildContext context, {
    bool showHeader = false,
    bool showCloseButton = false,
    bool showDragHandle = true,
    bool showHeaderBorder = false,
    double? maxHeight,
    double? headerHeight,
    bool useSafeArea = false,
  }) async {
    return await BottomSheetWidget.show<void>(
      context: context,
      showCloseButton: showCloseButton,
      showHeader: showHeader,
      showDragHandle: showDragHandle,
      headerHeight: headerHeight ?? 58,
      showHeaderBorder: showHeaderBorder,
      maxHeight: maxHeight ?? MediaQuery.of(context).size.height * 0.8,
      showBottomButton: false,
      useSafeArea: useSafeArea,
      child: _FunctionSelectionContent(),
    );
  }
}

class _FunctionSelectionContent extends StatefulWidget {
  const _FunctionSelectionContent();

  @override
  State<_FunctionSelectionContent> createState() =>
      __FunctionSelectionContentState();
}

class __FunctionSelectionContentState extends State<_FunctionSelectionContent>
    with TickerProviderStateMixin {
  // 功能标签控制器
  late TabController _tabController;

  // 功能标签列表
  final List<TabItem> _functionTabs = [
    TabItem(title: '精选功能'),
    TabItem(title: '新手学习'),
    TabItem(title: '公告中心'),
  ];

  // 功能列表
  final List<FunctionItem> _functionList = [
    FunctionItem(
      title: '现货设置',
      imageName: 'icon_futures_more_entry_setting',
      onTap: () => {},
    ),
    FunctionItem(
      title: '资金划转',
      imageName: 'icon_futures_guide_entry_transfer',
      onTap: () => {},
    ),
    FunctionItem(
      title: '充值',
      imageName: 'icon_deposit',
      format: ImageFormat.png,
      onTap: () => {},
    ),
    FunctionItem(
      title: '财务记录',
      imageName: 'icon_futures_more_entry_finance_record',
      onTap: () => {},
    ),
    FunctionItem(
      title: '现货信息',
      imageName: 'icon_futures_more_entry_contract_information',
      onTap: () => {},
    ),
    FunctionItem(
      title: '卡券中心',
      imageName: 'icon_futures_more_entry_coupon_center',
      onTap: () => {},
    ),
    FunctionItem(
      title: '现货返佣',
      imageName: 'ic_trade_more_setting_rebate',
      onTap: () => {},
    ),
    FunctionItem(
      title: 'PIN 码设置',
      imageName: 'flutter_on_chain_security_settings',
      onTap: () => {},
    ),
    FunctionItem(
      title: '自选',
      imageName: 'icon_futures_more_entry_favorite_no',
      onTap: () => {},
    ),
  ];

  // 初始化
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _functionTabs.length, vsync: this);
  }

  // 销毁
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: ScreenUtil.screenHeight(context) * 0.55,
      child: Column(
        children: [
          // 功能标签栏
          _buildTabbar(),

          // 功能标签内容
          _buildTabview(),
        ],
      ),
    );
  }

  // 构建功能标签栏
  Widget _buildTabbar() {
    return Container(
      padding: EdgeInsets.only(top: UiConstants.spacing14),
      child: TabbarWidget(
        controller: _tabController,
        tabs: _functionTabs,
        height: 48,
        labelStyle: context.templateStyle.text.tabText,
        selectedColor: context.templateColors.textPrimary,
        unselectedColor: context.templateColors.textSecondary,
        showIndicator: true,
        indicatorSize: TabBarIndicatorSize.label,
        labelPadding: EdgeInsets.only(right: UiConstants.spacing18),
      ),
    );
  }

  // 构建功能标签内容
  Widget _buildTabview() {
    return Expanded(
      child: TabBarView(
        controller: _tabController,
        children: [
          /// 精选功能
          _buildFunctionList(),

          /// 新手学习
          Container(),

          /// 公告中心
          Container(),
        ],
      ),
    );
  }

  // 构建功能列表
  Widget _buildFunctionList() {
    /// 功能项
    Widget buildFunctionItem({
      required String title,
      required String imageName,
      required VoidCallback onTap,
      ImageFormat format = ImageFormat.webp,
    }) {
      return InkWellWidget(
        onTap: onTap,
        child: Container(
          width:
              (ScreenUtil.screenWidth(context) - UiConstants.spacing16 * 5) / 4,
          padding: EdgeInsets.symmetric(vertical: UiConstants.spacing16),
          child: Column(
            children: [
              ThemedImage.asset(
                imageName,
                size: UiConstants.iconSize24,
                followTheme: true,
                format: format,
                margin: EdgeInsets.only(bottom: UiConstants.spacing8),
              ),
              Text(title, style: context.templateStyle.text.bodySmall),
            ],
          ),
        ),
      );
    }

    return Padding(
      padding: EdgeInsets.only(top: UiConstants.spacing10),
      child: Wrap(
        spacing: UiConstants.spacing16,
        children:
            _functionList
                .map(
                  (key) => buildFunctionItem(
                    title: key.title,
                    imageName: key.imageName,
                    onTap: key.onTap,
                  ),
                )
                .toList(),
      ),
    );
  }
}
