/*
*  字段介绍底部弹窗
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

/// 字段描述数据模型
class FieldDescription {
  /// 字段标题
  final String title;

  /// 字段描述内容
  final String description;

  /// 可选的示例文本
  final String? example;

  /// 可选的注意事项
  final String? note;

  /// 可选的图标
  final IconData? icon;

  /// 可选的图片资源
  final String? imageName;

  const FieldDescription({
    required this.title,
    required this.description,
    this.example,
    this.note,
    this.icon,
    this.imageName,
  });
}

/// 字段介绍底部弹窗
class FieldDescriptionBottomSheet {
  /// 显示字段介绍底部弹窗
  static Future<void> show(
    BuildContext context, {
    required FieldDescription fieldDescription,
    String? customTitle,
    bool showHeader = true,
    bool showCloseButton = true,
    bool showDragHandle = true,
    bool showHeaderBorder = false,
    double? maxHeight,
    double? headerHeight,
    bool useSafeArea = false,
    TextAlign titleAlign = TextAlign.center,
  }) async {
    return await BottomSheetWidget.show<void>(
      context: context,
      title: customTitle ?? fieldDescription.title,
      titleAlign: titleAlign,
      showCloseButton: showCloseButton,
      showHeader: showHeader,
      showDragHandle: showDragHandle,
      headerHeight: headerHeight ?? 58,
      showHeaderBorder: showHeaderBorder,
      maxHeight: maxHeight ?? MediaQuery.of(context).size.height * 0.7,
      showBottomButton: false,
      useSafeArea: useSafeArea,
      child: _FieldDescriptionContent(fieldDescription: fieldDescription),
    );
  }
}

/// 字段介绍内容组件
class _FieldDescriptionContent extends StatelessWidget {
  final FieldDescription fieldDescription;

  const _FieldDescriptionContent({required this.fieldDescription});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: UiConstants.spacing18,
        vertical: UiConstants.spacing16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 图标或图片（如果有）
          if (fieldDescription.icon != null ||
              fieldDescription.imageName != null)
            _buildIconSection(context),

          // 描述内容
          _buildDescriptionSection(context),

          // 示例（如果有）
          if (fieldDescription.example != null) _buildExampleSection(context),

          // 注意事项（如果有）
          if (fieldDescription.note != null) _buildNoteSection(context),

          // 底部间距
          SizedBox(height: UiConstants.spacing16),
        ],
      ),
    );
  }

  /// 构建图标部分
  Widget _buildIconSection(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: UiConstants.spacing16),
      child: Center(
        child:
            fieldDescription.imageName != null
                ? ThemedImage.asset(
                  fieldDescription.imageName!,
                  size: UiConstants.iconSize48,
                  followTheme: true,
                )
                : Icon(
                  fieldDescription.icon!,
                  size: UiConstants.iconSize48,
                  color: context.templateColors.primary,
                ),
      ),
    );
  }

  /// 构建描述部分
  Widget _buildDescriptionSection(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: UiConstants.spacing16),
      child: Text(
        fieldDescription.description,
        style: context.templateStyle.text.bodyText.copyWith(height: 1.5),
      ),
    );
  }

  /// 构建示例部分
  Widget _buildExampleSection(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: UiConstants.spacing16),
      padding: EdgeInsets.all(UiConstants.spacing12),
      decoration: BoxDecoration(
        color: context.templateColors.inputBackground,
        borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
        border: Border.all(color: context.templateColors.border, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '示例',
            style: context.templateStyle.text.bodySmallMedium.copyWith(
              color: context.templateColors.textSecondary,
            ),
          ),
          SizedBox(height: UiConstants.spacing8),
          Text(
            fieldDescription.example!,
            style: context.templateStyle.text.bodyText.copyWith(
              fontFamily: 'monospace', // 使用等宽字体显示示例
            ),
          ),
        ],
      ),
    );
  }

  /// 构建注意事项部分
  Widget _buildNoteSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(UiConstants.spacing12),
      decoration: BoxDecoration(
        color: context.templateColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
        border: Border.all(
          color: context.templateColors.warning.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.info_outline,
            size: UiConstants.iconSize16,
            color: context.templateColors.warning,
          ),
          SizedBox(width: UiConstants.spacing8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '注意事项',
                  style: context.templateStyle.text.bodySmallMedium.copyWith(
                    color: context.templateColors.warning,
                  ),
                ),
                SizedBox(height: UiConstants.spacing4),
                Text(
                  fieldDescription.note!,
                  style: context.templateStyle.text.bodySmall.copyWith(
                    color: context.templateColors.textSecondary,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
