/*
 * 委托类型选择弹窗
 */

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class OrderTypeSelectorDialog {
  /// 显示委托类型选择弹窗
  static Future<String?> show({
    required BuildContext context,
    required String currentType,
    List<String>? orderTypes,
  }) {
    final List<String> defaultOrderTypes = [
      '全部委托',
      '限价委托',
      '市价委托',
      '止盈止损',
      '计划委托',
    ];

    return showModalBottomSheet<String>(
      context: context,
      backgroundColor: context.templateColors.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(UiConstants.borderRadius8),
        ),
      ),
      builder: (context) {
        return _OrderTypeSelectorContent(
          currentType: currentType,
          orderTypes: orderTypes ?? defaultOrderTypes,
        );
      },
    );
  }
}

class _OrderTypeSelectorContent extends StatelessWidget {
  final String currentType;
  final List<String> orderTypes;

  const _OrderTypeSelectorContent({
    required this.currentType,
    required this.orderTypes,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: UiConstants.spacing16,
        horizontal: UiConstants.spacing18,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Center(child: Text('委托类型', style: context.templateStyle.text.h4)),
          SizedBox(height: UiConstants.spacing24),

          // 委托类型列表
          ...orderTypes.map((type) {
            final bool isSelected = type == currentType;
            return InkWellWidget(
              onTap: () {
                Navigator.pop(context, type);
              },
              child: Container(
                padding: EdgeInsets.symmetric(vertical: UiConstants.spacing12),
                child: Row(
                  children: [
                    Text(
                      type,
                      style: context.templateStyle.text.bodyLargeMedium
                          .copyWith(
                            color:
                                isSelected
                                    ? context.templateColors.primary
                                    : context.templateColors.textPrimary,
                          ),
                    ),
                    Spacer(),
                    if (isSelected)
                      Icon(
                        Icons.check,
                        color: context.templateColors.primary,
                        size: 20,
                      ),
                  ],
                ),
              ),
            );
          }),

          SizedBox(height: UiConstants.spacing16),
        ],
      ),
    );
  }
}
