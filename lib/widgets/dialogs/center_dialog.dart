/*
* 居中弹窗
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/utils/device/screen_util.dart';
import 'package:qubic_exchange/widgets/index.dart';

/// 居中对话框配置
class CenterDialogConfig {
  /// 标题
  final String? title;

  /// 内容文本
  final String? content;

  /// 自定义内容组件
  final Widget? customContent;

  /// 警告图标
  final bool showWarningIcon;

  /// 取消按钮文本
  final String cancelText;

  /// 确认按钮文本
  final String confirmText;

  /// 确认按钮回调
  final VoidCallback? onConfirm;

  /// 取消按钮回调
  final VoidCallback? onCancel;

  /// 是否显示"不再提示"选项
  final bool showDontAskAgain;

  /// "不再提示"初始状态
  final bool dontAskAgainInitial;

  /// "不再提示"状态变化回调
  final ValueChanged<bool>? onDontAskAgainChanged;

  const CenterDialogConfig({
    this.title,
    this.content,
    this.customContent,
    this.showWarningIcon = false,
    this.cancelText = '取消',
    this.confirmText = '确认',
    this.onConfirm,
    this.onCancel,
    this.showDontAskAgain = false,
    this.dontAskAgainInitial = false,
    this.onDontAskAgainChanged,
  });
}

class CenterDialog {
  /// 显示基础居中弹窗
  static Future<T?> show<T>(
    BuildContext context, {
    required Widget child,
    bool barrierDismissible = true,
    Color? barrierColor,
    bool useSafeArea = true,
  }) async {
    return await showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
      useSafeArea: useSafeArea,
      builder: (context) {
        return Center(child: child);
      },
    );
  }

  /// 显示确认对话框
  static Future<bool?> showConfirm(
    BuildContext context, {
    required CenterDialogConfig config,
    bool barrierDismissible = true,
    double? width,
  }) async {
    return await show<bool>(
      context,
      barrierDismissible: barrierDismissible,
      child: _ConfirmDialogContent(config: config, width: width),
    );
  }

  /// 显示全部撤销确认对话框（根据截图样式）
  static Future<bool?> showCancelAllConfirm(
    BuildContext context, {
    bool showDontAskAgain = true,
    bool dontAskAgainInitial = false,
    ValueChanged<bool>? onDontAskAgainChanged,
  }) async {
    return await showConfirm(
      context,
      config: CenterDialogConfig(
        title: '全部撤销确认',
        content: '您确认要撤销全部委托订单吗？包含：限价｜市价、计划委托、OCO、止盈止损、追踪委托、冰山委托、分时委托',
        showWarningIcon: true,
        cancelText: '取消',
        confirmText: '确认',
        showDontAskAgain: showDontAskAgain,
        dontAskAgainInitial: dontAskAgainInitial,
        onDontAskAgainChanged: onDontAskAgainChanged,
      ),
    );
  }
}

/// 确认对话框内容组件
class _ConfirmDialogContent extends StatefulWidget {
  final CenterDialogConfig config;
  final double? width;

  const _ConfirmDialogContent({required this.config, this.width});

  @override
  State<_ConfirmDialogContent> createState() => _ConfirmDialogContentState();
}

class _ConfirmDialogContentState extends State<_ConfirmDialogContent> {
  late bool _dontAskAgain;

  @override
  void initState() {
    super.initState();
    _dontAskAgain = widget.config.dontAskAgainInitial;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        width: widget.width ?? ScreenUtil.screenWidth(context) * 0.8,
        margin: EdgeInsets.symmetric(horizontal: UiConstants.spacing24),
        decoration: BoxDecoration(
          color: context.templateColors.surface,
          borderRadius: BorderRadius.circular(UiConstants.borderRadius12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 警告图标和标题
            _buildHeader(context),

            // 内容区域
            _buildContent(context),

            // 不再提示选项
            if (widget.config.showDontAskAgain)
              _buildDontAskAgainOption(context),

            // 按钮区域
            _buildButtons(context),
          ],
        ),
      ),
    );
  }

  /// 构建头部（图标 + 标题）
  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        top: UiConstants.spacing24,
        left: UiConstants.spacing24,
        right: UiConstants.spacing24,
        bottom: UiConstants.spacing16,
      ),
      child: Column(
        children: [
          // 警告图标
          if (widget.config.showWarningIcon)
            Container(
              width: 48,
              height: 48,
              margin: EdgeInsets.only(bottom: UiConstants.spacing16),
              decoration: BoxDecoration(
                color: context.templateColors.warning.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.warning_rounded,
                color: context.templateColors.warning,
                size: 24,
              ),
            ),

          // 标题
          if (widget.config.title != null)
            Text(
              widget.config.title!,
              style: context.templateStyle.text.h4.copyWith(
                color: context.templateColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing24),
      child:
          widget.config.customContent ??
          (widget.config.content != null
              ? Text(
                widget.config.content!,
                style: context.templateStyle.text.bodyText.copyWith(
                  color: context.templateColors.textSecondary,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              )
              : const SizedBox.shrink()),
    );
  }

  /// 构建"不再提示"选项
  Widget _buildDontAskAgainOption(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: UiConstants.spacing16,
        right: UiConstants.spacing24,
        top: UiConstants.spacing20,
      ),
      child: Row(
        children: [
          CheckboxWidget(
            value: _dontAskAgain,
            customSize: UiConstants.iconSize14,
            onChanged: (value) {
              setState(() {
                _dontAskAgain = value;
              });
              widget.config.onDontAskAgainChanged?.call(_dontAskAgain);
            },
            activeColor: context.templateColors.textPrimary,
          ),
          SizedBox(width: UiConstants.spacing8),
          Expanded(
            child: Text(
              '不再提示，可在"现货设置"中再次打开',
              style: context.templateStyle.text.bodySmall.copyWith(
                color: context.templateColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建按钮区域
  Widget _buildButtons(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(UiConstants.spacing24),
      child: Row(
        children: [
          // 取消按钮
          Expanded(
            child: CommonButton.secondary(
              widget.config.cancelText,
              height: 40,
              size: CommonButtonSize.medium,
              onPressed: () {
                widget.config.onCancel?.call();
                Navigator.of(context).pop(false);
              },
            ),
          ),

          SizedBox(width: UiConstants.spacing12),

          // 确认按钮
          Expanded(
            child: CommonButton.primary(
              widget.config.confirmText,
              size: CommonButtonSize.medium,
              height: 40,
              onPressed: () {
                widget.config.onConfirm?.call();
                Navigator.of(context).pop(true);
              },
            ),
          ),
        ],
      ),
    );
  }
}
