// 图标按钮

import 'package:flutter/material.dart';
import 'package:qubic_exchange/widgets/index.dart';

class IconButtonWidget extends StatefulWidget {
  final VoidCallback? onTap;
  final IconData icon;
  final double? size;
  final Color? fillColor;
  final Color? iconColor;
  final double? iconSize;
  final double? radius;
  final EdgeInsets padding;
  final EdgeInsets margin;

  const IconButtonWidget({
    super.key,
    required this.icon,
    this.size,
    this.fillColor,
    this.iconColor,
    this.iconSize,
    this.radius,
    this.padding = EdgeInsets.zero,
    this.margin = EdgeInsets.zero,
    this.onTap,
  });

  @override
  State<IconButtonWidget> createState() => _IconButtonWidgetState();
}

class _IconButtonWidgetState extends State<IconButtonWidget> {
  @override
  Widget build(BuildContext context) {
    return InkWellWidget(
      onTap: widget.onTap,
      child: Container(
        width: widget.size,
        height: widget.size,
        padding: widget.padding,
        margin: widget.margin,
        decoration: BoxDecoration(
          color: widget.fillColor,
          borderRadius: BorderRadius.circular(widget.radius ?? 0.0),
        ),
        child: Icon(
          widget.icon,
          size: widget.iconSize,
          color: widget.iconColor,
        ),
      ),
    );
  }
}
