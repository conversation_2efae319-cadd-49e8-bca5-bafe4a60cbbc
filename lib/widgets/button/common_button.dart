/*
*  通用按钮组件
      - 支持多种按钮类型和样式
      - 自动适配模板主题
      - 支持加载状态和禁用状态
      - 支持图标和文本组合
      - 🆕 支持自定义高度、圆角、文本样式
      - 🆕 支持自定义背景色、前景色、边框

使用示例：
// 基础用法
CommonButton.primary('确认', onPressed: () {})
CommonButton.buy('买入', onPressed: () {}, loading: true)

// 自定义样式
CommonButton.custom(
  '自定义按钮',
  height: 50,
  borderRadius: 25,
  backgroundColor: Colors.purple,
  textStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
  onPressed: () {},
)

// 圆形按钮
CommonButton.circular('', icon: Icons.add, size: 56, onPressed: () {})
*/

import 'package:flutter/material.dart';
import '../../core/index.dart';

/// 按钮类型枚举
enum CommonButtonType {
  primary, // 主要按钮
  secondary, // 次要按钮
  outlined, // 边框按钮
  text, // 文本按钮
  buy, // 买入按钮
  sell, // 卖出按钮
  buyOutlined, // 买入边框按钮
  sellOutlined, // 卖出边框按钮
  success, // 成功按钮
  warning, // 警告按钮
  error, // 错误按钮
  info, // 信息按钮
}

/// 按钮尺寸枚举
enum CommonButtonSize {
  small, // 小尺寸
  medium, // 中等尺寸（默认）
  large, // 大尺寸
}

/// 通用按钮组件
class CommonButton extends StatefulWidget {
  /// 按钮文本
  final String? text;

  /// 按钮类型
  final CommonButtonType type;

  /// 按钮尺寸
  final CommonButtonSize size;

  /// 点击回调
  final VoidCallback? onPressed;

  /// 长按回调
  final VoidCallback? onLongPress;

  /// 是否加载中
  final bool loading;

  /// 是否禁用
  final bool disabled;

  /// 是否展开填充宽度
  final bool expanded;

  /// 自定义宽度
  final double? width;

  /// 自定义高度
  final double? height;

  /// 前置图标
  final IconData? prefixIcon;

  /// 后置图标
  final IconData? suffixIcon;

  /// 图标大小
  final double? iconSize;

  /// 图标和文本间距
  final double? iconSpacing;

  /// 自定义子组件（优先级高于text）
  final Widget? child;

  /// 自定义样式（会覆盖默认样式）
  final ButtonStyle? customStyle;

  /// 自定义圆角半径
  final double? borderRadius;

  /// 自定义文本样式
  final TextStyle? textStyle;

  /// 自定义背景颜色
  final Color? backgroundColor;

  /// 自定义前景色（文本和图标颜色）
  final Color? foregroundColor;

  /// 自定义边框颜色
  final Color? borderColor;

  /// 自定义边框宽度
  final double? borderWidth;

  /// 自定义内边距
  final EdgeInsetsGeometry? padding;

  /// 外边距
  final EdgeInsetsGeometry? margin;

  const CommonButton({
    super.key,
    this.text,
    this.type = CommonButtonType.primary,
    this.size = CommonButtonSize.medium,
    this.onPressed,
    this.onLongPress,
    this.loading = false,
    this.disabled = false,
    this.expanded = false,
    this.width,
    this.height,
    this.prefixIcon,
    this.suffixIcon,
    this.iconSize,
    this.iconSpacing,
    this.child,
    this.customStyle,
    this.borderRadius,
    this.textStyle,
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.borderWidth,
    this.padding,
    this.margin,
  });

  /// 创建主要按钮
  const CommonButton.primary(
    String text, {
    Key? key,
    CommonButtonSize size = CommonButtonSize.medium,
    VoidCallback? onPressed,
    VoidCallback? onLongPress,
    bool loading = false,
    bool disabled = false,
    bool expanded = false,
    double? width,
    double? height,
    IconData? prefixIcon,
    IconData? suffixIcon,
    double? iconSize,
    double? iconSpacing,
    ButtonStyle? customStyle,
    double? borderRadius,
    TextStyle? textStyle,
    Color? backgroundColor,
    Color? foregroundColor,
    Color? borderColor,
    double? borderWidth,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
  }) : this(
         key: key,
         text: text,
         type: CommonButtonType.primary,
         size: size,
         onPressed: onPressed,
         onLongPress: onLongPress,
         loading: loading,
         disabled: disabled,
         expanded: expanded,
         width: width,
         height: height,
         prefixIcon: prefixIcon,
         suffixIcon: suffixIcon,
         iconSize: iconSize,
         iconSpacing: iconSpacing,
         customStyle: customStyle,
         borderRadius: borderRadius,
         textStyle: textStyle,
         backgroundColor: backgroundColor,
         foregroundColor: foregroundColor,
         borderColor: borderColor,
         borderWidth: borderWidth,
         padding: padding,
         margin: margin,
       );

  /// 创建次要按钮
  const CommonButton.secondary(
    String text, {
    Key? key,
    CommonButtonSize size = CommonButtonSize.medium,
    VoidCallback? onPressed,
    VoidCallback? onLongPress,
    bool loading = false,
    bool disabled = false,
    bool expanded = false,
    double? width,
    double? height,
    IconData? prefixIcon,
    IconData? suffixIcon,
    double? iconSize,
    double? iconSpacing,
    ButtonStyle? customStyle,
    EdgeInsetsGeometry? margin,
  }) : this(
         key: key,
         text: text,
         type: CommonButtonType.secondary,
         size: size,
         onPressed: onPressed,
         onLongPress: onLongPress,
         loading: loading,
         disabled: disabled,
         expanded: expanded,
         width: width,
         height: height,
         prefixIcon: prefixIcon,
         suffixIcon: suffixIcon,
         iconSize: iconSize,
         iconSpacing: iconSpacing,
         customStyle: customStyle,
         margin: margin,
       );

  /// 创建边框按钮
  const CommonButton.outlined(
    String text, {
    Key? key,
    CommonButtonSize size = CommonButtonSize.medium,
    VoidCallback? onPressed,
    VoidCallback? onLongPress,
    bool loading = false,
    bool disabled = false,
    bool expanded = false,
    double? width,
    double? height,
    IconData? prefixIcon,
    IconData? suffixIcon,
    double? iconSize,
    double? iconSpacing,
    ButtonStyle? customStyle,
    EdgeInsetsGeometry? margin,
  }) : this(
         key: key,
         text: text,
         type: CommonButtonType.outlined,
         size: size,
         onPressed: onPressed,
         onLongPress: onLongPress,
         loading: loading,
         disabled: disabled,
         expanded: expanded,
         width: width,
         height: height,
         prefixIcon: prefixIcon,
         suffixIcon: suffixIcon,
         iconSize: iconSize,
         iconSpacing: iconSpacing,
         customStyle: customStyle,
         margin: margin,
       );

  /// 创建买入按钮
  const CommonButton.buy(
    String text, {
    Key? key,
    CommonButtonSize size = CommonButtonSize.medium,
    VoidCallback? onPressed,
    VoidCallback? onLongPress,
    bool loading = false,
    bool disabled = false,
    bool expanded = false,
    double? width,
    double? height,
    IconData? prefixIcon,
    IconData? suffixIcon,
    double? iconSize,
    double? iconSpacing,
    ButtonStyle? customStyle,
    double? borderRadius,
    TextStyle? textStyle,
    Color? backgroundColor,
    Color? foregroundColor,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
  }) : this(
         key: key,
         text: text,
         type: CommonButtonType.buy,
         size: size,
         onPressed: onPressed,
         onLongPress: onLongPress,
         loading: loading,
         disabled: disabled,
         expanded: expanded,
         width: width,
         height: height,
         prefixIcon: prefixIcon,
         suffixIcon: suffixIcon,
         iconSize: iconSize,
         iconSpacing: iconSpacing,
         customStyle: customStyle,
         borderRadius: borderRadius,
         textStyle: textStyle,
         backgroundColor: backgroundColor,
         foregroundColor: foregroundColor,
         padding: padding,
         margin: margin,
       );

  /// 创建卖出按钮
  const CommonButton.sell(
    String text, {
    Key? key,
    CommonButtonSize size = CommonButtonSize.medium,
    VoidCallback? onPressed,
    VoidCallback? onLongPress,
    bool loading = false,
    bool disabled = false,
    bool expanded = false,
    double? width,
    double? height,
    IconData? prefixIcon,
    IconData? suffixIcon,
    double? iconSize,
    double? iconSpacing,
    ButtonStyle? customStyle,
    EdgeInsetsGeometry? margin,
  }) : this(
         key: key,
         text: text,
         type: CommonButtonType.sell,
         size: size,
         onPressed: onPressed,
         onLongPress: onLongPress,
         loading: loading,
         disabled: disabled,
         expanded: expanded,
         width: width,
         height: height,
         prefixIcon: prefixIcon,
         suffixIcon: suffixIcon,
         iconSize: iconSize,
         iconSpacing: iconSpacing,
         customStyle: customStyle,
         margin: margin,
       );

  /// 创建自定义样式按钮
  const CommonButton.custom(
    String text, {
    Key? key,
    CommonButtonType type = CommonButtonType.primary,
    CommonButtonSize size = CommonButtonSize.medium,
    VoidCallback? onPressed,
    VoidCallback? onLongPress,
    bool loading = false,
    bool disabled = false,
    bool expanded = false,
    double? width,
    double? height,
    IconData? prefixIcon,
    IconData? suffixIcon,
    double? iconSize,
    double? iconSpacing,
    double? borderRadius,
    TextStyle? textStyle,
    Color? backgroundColor,
    Color? foregroundColor,
    Color? borderColor,
    double? borderWidth,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
  }) : this(
         key: key,
         text: text,
         type: type,
         size: size,
         onPressed: onPressed,
         onLongPress: onLongPress,
         loading: loading,
         disabled: disabled,
         expanded: expanded,
         width: width,
         height: height,
         prefixIcon: prefixIcon,
         suffixIcon: suffixIcon,
         iconSize: iconSize,
         iconSpacing: iconSpacing,
         borderRadius: borderRadius,
         textStyle: textStyle,
         backgroundColor: backgroundColor,
         foregroundColor: foregroundColor,
         borderColor: borderColor,
         borderWidth: borderWidth,
         padding: padding,
         margin: margin,
       );

  /// 创建圆形按钮
  const CommonButton.circular(
    String text, {
    Key? key,
    CommonButtonType type = CommonButtonType.primary,
    VoidCallback? onPressed,
    VoidCallback? onLongPress,
    bool loading = false,
    bool disabled = false,
    double? size,
    IconData? icon,
    TextStyle? textStyle,
    Color? backgroundColor,
    Color? foregroundColor,
    EdgeInsetsGeometry? margin,
  }) : this(
         key: key,
         text: text,
         type: type,
         size: CommonButtonSize.medium,
         onPressed: onPressed,
         onLongPress: onLongPress,
         loading: loading,
         disabled: disabled,
         width: size ?? 48,
         height: size ?? 48,
         prefixIcon: icon,
         borderRadius: (size ?? 48) / 2,
         textStyle: textStyle,
         backgroundColor: backgroundColor,
         foregroundColor: foregroundColor,
         padding: EdgeInsets.zero,
         margin: margin,
       );

  @override
  State<CommonButton> createState() => _CommonButtonState();
}

class _CommonButtonState extends State<CommonButton> {
  @override
  Widget build(BuildContext context) {
    // 构建按钮内容
    Widget buttonChild = _buildButtonContent();

    // 构建按钮
    Widget button = _buildButton(buttonChild);

    // 应用外边距
    if (widget.margin != null) {
      button = Padding(padding: widget.margin!, child: button);
    }

    // 应用展开
    if (widget.expanded) {
      button = SizedBox(width: double.infinity, child: button);
    }

    // 应用自定义尺寸
    if (widget.width != null || widget.height != null) {
      button = SizedBox(
        width: widget.width,
        height: widget.height,
        child: button,
      );
    }

    return button;
  }

  /// 构建按钮内容
  Widget _buildButtonContent() {
    // 如果有自定义子组件，优先使用
    if (widget.child != null) {
      return widget.child!;
    }

    // 如果正在加载，显示加载指示器
    if (widget.loading) {
      return _buildLoadingContent();
    }

    // 构建文本和图标内容
    return _buildTextAndIconContent();
  }

  /// 构建加载内容
  Widget _buildLoadingContent() {
    final iconSize = widget.iconSize ?? _getDefaultIconSize();

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: iconSize,
          height: iconSize,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              _getLoadingIndicatorColor(),
            ),
          ),
        ),
        if (widget.text != null && widget.text!.isNotEmpty) ...[
          SizedBox(width: widget.iconSpacing ?? 8),
          Text(widget.text!),
        ],
      ],
    );
  }

  /// 构建文本和图标内容
  Widget _buildTextAndIconContent() {
    final List<Widget> children = [];

    // 前置图标
    if (widget.prefixIcon != null) {
      children.add(
        Icon(widget.prefixIcon, size: widget.iconSize ?? _getDefaultIconSize()),
      );

      if (widget.text != null && widget.text!.isNotEmpty) {
        children.add(SizedBox(width: widget.iconSpacing ?? 8));
      }
    }

    // 文本
    if (widget.text != null && widget.text!.isNotEmpty) {
      children.add(Text(widget.text!));
    }

    // 后置图标
    if (widget.suffixIcon != null) {
      if (widget.text != null && widget.text!.isNotEmpty) {
        children.add(SizedBox(width: widget.iconSpacing ?? 8));
      }

      children.add(
        Icon(widget.suffixIcon, size: widget.iconSize ?? _getDefaultIconSize()),
      );
    }

    if (children.isEmpty) {
      return const SizedBox.shrink();
    }

    if (children.length == 1) {
      return children.first;
    }

    return Row(mainAxisSize: MainAxisSize.min, children: children);
  }

  /// 构建按钮
  Widget _buildButton(Widget child) {
    final buttonStyle = _getButtonStyle();
    final isEnabled = _isButtonEnabled();

    // 根据按钮类型选择合适的按钮组件
    switch (widget.type) {
      case CommonButtonType.outlined:
      case CommonButtonType.buyOutlined:
      case CommonButtonType.sellOutlined:
        return OutlinedButton(
          onPressed: isEnabled ? widget.onPressed : null,
          onLongPress: isEnabled ? widget.onLongPress : null,
          style: buttonStyle,
          child: child,
        );

      case CommonButtonType.text:
        return TextButton(
          onPressed: isEnabled ? widget.onPressed : null,
          onLongPress: isEnabled ? widget.onLongPress : null,
          style: buttonStyle,
          child: child,
        );

      default:
        return ElevatedButton(
          onPressed: isEnabled ? widget.onPressed : null,
          onLongPress: isEnabled ? widget.onLongPress : null,
          style: buttonStyle,
          child: child,
        );
    }
  }

  /// 获取按钮样式
  ButtonStyle _getButtonStyle() {
    ButtonStyle baseStyle;

    // 根据类型获取基础样式
    switch (widget.type) {
      case CommonButtonType.primary:
        baseStyle = context.templateStyle.button.primary;
        break;
      case CommonButtonType.secondary:
        baseStyle = context.templateStyle.button.secondary;
        break;
      case CommonButtonType.outlined:
        baseStyle = context.templateStyle.button.outlined;
        break;
      case CommonButtonType.text:
        baseStyle = context.templateStyle.button.text;
        break;
      case CommonButtonType.buy:
        baseStyle = context.templateStyle.button.buy;
        break;
      case CommonButtonType.sell:
        baseStyle = context.templateStyle.button.sell;
        break;
      case CommonButtonType.buyOutlined:
        baseStyle = context.templateStyle.button.buyOutlined;
        break;
      case CommonButtonType.sellOutlined:
        baseStyle = context.templateStyle.button.sellOutlined;
        break;
      case CommonButtonType.success:
        baseStyle = context.templateStyle.button.success;
        break;
      case CommonButtonType.warning:
        baseStyle = context.templateStyle.button.warning;
        break;
      case CommonButtonType.error:
        baseStyle = context.templateStyle.button.error;
        break;
      case CommonButtonType.info:
        // info 样式使用 primary 样式，但颜色为 info 色
        baseStyle = context.templateStyle.button.primary.copyWith(
          backgroundColor: WidgetStateProperty.all(context.templateColors.info),
        );
        break;
    }

    // 根据尺寸调整样式
    baseStyle = _applySizeToStyle(baseStyle);

    // 应用自定义属性
    baseStyle = _applyCustomProperties(baseStyle);

    // 应用自定义样式（优先级最高）
    if (widget.customStyle != null) {
      baseStyle = baseStyle.merge(widget.customStyle);
    }

    return baseStyle;
  }

  /// 根据尺寸调整样式
  ButtonStyle _applySizeToStyle(ButtonStyle baseStyle) {
    switch (widget.size) {
      case CommonButtonSize.small:
        return baseStyle.copyWith(
          padding: WidgetStateProperty.all(
            widget.padding ??
                EdgeInsets.symmetric(
                  horizontal: context.templateStyles.spacingSmall,
                  vertical: context.templateStyles.spacingSmall / 2,
                ),
          ),
          minimumSize: WidgetStateProperty.all(const Size(0, 32)),
          textStyle: WidgetStateProperty.all(
            widget.textStyle ?? context.templateStyle.text.bodySmallMedium,
          ),
        );

      case CommonButtonSize.large:
        return baseStyle.copyWith(
          padding: WidgetStateProperty.all(
            widget.padding ??
                EdgeInsets.symmetric(
                  horizontal: context.templateStyles.spacingLarge,
                  vertical: context.templateStyles.spacingMedium,
                ),
          ),
          minimumSize: WidgetStateProperty.all(const Size(0, 56)),
          textStyle: WidgetStateProperty.all(
            widget.textStyle ?? context.templateStyle.text.bodyLargeMedium,
          ),
        );

      case CommonButtonSize.medium:
        return baseStyle.copyWith(
          padding:
              widget.padding != null
                  ? WidgetStateProperty.all(widget.padding)
                  : baseStyle.padding,
          textStyle: WidgetStateProperty.all(
            widget.textStyle ?? context.templateStyle.text.bodyLargeMedium,
          ),
        );
    }
  }

  /// 应用自定义属性
  ButtonStyle _applyCustomProperties(ButtonStyle baseStyle) {
    return baseStyle.copyWith(
      // 自定义背景颜色
      backgroundColor:
          widget.backgroundColor != null
              ? WidgetStateProperty.all(widget.backgroundColor)
              : baseStyle.backgroundColor,

      // 自定义前景色
      foregroundColor:
          widget.foregroundColor != null
              ? WidgetStateProperty.all(widget.foregroundColor)
              : baseStyle.foregroundColor,

      // 自定义圆角
      shape:
          widget.borderRadius != null ||
                  widget.borderColor != null ||
                  widget.borderWidth != null
              ? WidgetStateProperty.all(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    widget.borderRadius ??
                        context.templateStyles.borderRadiusMedium,
                  ),
                  side: BorderSide(
                    color: widget.borderColor ?? Colors.transparent,
                    width: widget.borderWidth ?? 0,
                  ),
                ),
              )
              : baseStyle.shape,
    );
  }

  /// 判断按钮是否可用
  bool _isButtonEnabled() {
    return !widget.disabled && !widget.loading && widget.onPressed != null;
  }

  /// 获取默认图标大小
  double _getDefaultIconSize() {
    switch (widget.size) {
      case CommonButtonSize.small:
        return 16;
      case CommonButtonSize.large:
        return 20;
      case CommonButtonSize.medium:
        return 18;
    }
  }

  /// 获取加载指示器颜色
  Color _getLoadingIndicatorColor() {
    switch (widget.type) {
      case CommonButtonType.outlined:
      case CommonButtonType.buyOutlined:
      case CommonButtonType.sellOutlined:
      case CommonButtonType.text:
        return context.templateColors.primary;

      case CommonButtonType.buy:
        return context.templateColors.tradeBuy;

      case CommonButtonType.sell:
        return context.templateColors.tradeSell;

      case CommonButtonType.success:
        return context.templateColors.success;

      case CommonButtonType.warning:
        return context.templateColors.warning;

      case CommonButtonType.error:
        return context.templateColors.error;

      case CommonButtonType.info:
        return context.templateColors.info;

      default:
        return context.templateColors.buttonText;
    }
  }
}
