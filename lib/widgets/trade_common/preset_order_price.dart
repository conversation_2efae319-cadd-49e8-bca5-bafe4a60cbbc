/*
* 预设委托价 or 价格
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class PresetOrderPrice extends StatefulWidget {
  final String labelText;
  final bool showIncrementButtons;
  final ValueChanged<double?>? onChanged;
  final TextEditingController? controller;
  final bool enabled;
  final ValueChanged<bool>? onOrderTypeChanged;

  const PresetOrderPrice({
    super.key,
    this.showIncrementButtons = false,
    this.labelText = '预设委托价',
    this.onChanged,
    this.controller,
    this.enabled = true,
    this.onOrderTypeChanged,
  });

  @override
  State<PresetOrderPrice> createState() => _PresetOrderPriceState();
}

class _PresetOrderPriceState extends State<PresetOrderPrice> {
  // 是否为市价
  bool isMarketPrice = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          top: -60,
          child: Container(width: 40, height: 60, color: Colors.red),
        ),
        SizedBox(
          child: Row(
            children: [
              // 价格输入
              Expanded(
                child:
                    isMarketPrice ? _buildCounterOrder() : _buildPriceInput(),
              ),
              SizedBox(width: UiConstants.spacing8),
              // 切换按钮
              _buildSwitchButton(),
            ],
          ),
        ),
      ],
    );
  }

  // 构建价格输入
  Widget _buildPriceInput() {
    return SizedBox(
      child: TextFieldWidget(
        controller: widget.controller,
        enabled: widget.enabled,
        height: 42,
        labelText: widget.labelText,
        cursorHeight: 12,
        contentPadding: EdgeInsets.only(
          left: UiConstants.spacing10,
          top: UiConstants.spacing4,
          bottom: UiConstants.spacing4,
        ),
        padding: EdgeInsets.zero,
        radius: BorderRadius.circular(UiConstants.borderRadius6),
        keyboardType: TextInputType.numberWithOptions(decimal: true),
        onChanged: (value) {
          final price = double.tryParse(value);
          widget.onChanged?.call(price);
        },
      ),
    );
  }

  // 构建市价显示
  Widget _buildCounterOrder() {
    return Container(
      width: double.infinity,
      height: 42,
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        color: context.templateColors.inputBackground,
        borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
        border: Border.all(width: 1, color: context.templateColors.divider),
      ),
      child: Text(
        '市价成交',
        style: context.templateStyle.text.bodyTextMedium.copyWith(
          color: context.templateColors.textTertiary,
        ),
      ),
    );
  }

  // 构建切换按钮
  Widget _buildSwitchButton() {
    return InkWellWidget(
      onTap: () {
        setState(() {
          isMarketPrice = !isMarketPrice;
        });
        widget.onOrderTypeChanged?.call(isMarketPrice);
      },
      child: Container(
        height: 42,
        padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing14),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: context.templateColors.tabbarBackground,
          borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
          border: Border.all(
            width: 1,
            color:
                isMarketPrice
                    ? context.templateColors.textPrimary
                    : Colors.transparent,
          ),
        ),
        child: Text('市价', style: context.templateStyle.text.bodyTextMedium),
      ),
    );
  }
}
