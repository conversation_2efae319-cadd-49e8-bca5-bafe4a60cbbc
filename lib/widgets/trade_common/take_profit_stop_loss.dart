/*
*  止损止盈
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/pages/trade/widgets/edit_order/edit_order_dialog.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/models/trade/spot_order_model.dart';

class TakeProfitStopLoss extends StatefulWidget {
  // 是否显示高级
  final bool showAdvanced;
  // 止盈文本
  final String takeProfitText;
  // 止损文本
  final String stopLossText;
  // 止盈后缀
  final Widget takeProfitSuffix;
  //止损后缀
  final Widget stopLossSuffix;
  // 是否为合约
  final bool isContract;
  // 展开/收缩状态变化回调
  final Function(bool isExpanded)? onExpandChanged;

  const TakeProfitStopLoss({
    super.key,
    this.showAdvanced = true,
    this.isContract = false,
    this.onExpandChanged,
    this.takeProfitText = '止盈',
    this.stopLossText = '止损',
    this.takeProfitSuffix = const SizedBox(),
    this.stopLossSuffix = const SizedBox(),
  });

  @override
  State<TakeProfitStopLoss> createState() => _TakeProfitStopLossState();
}

class _TakeProfitStopLossState extends State<TakeProfitStopLoss> {
  // 止盈止损开关状态
  bool _isStopLossEnabled = false;

  // 焦点节点
  final FocusNode _percentageFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _percentageFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 止盈止损控制器
        _buildController(),
        // 止盈止损输入框
        if (_isStopLossEnabled)
          Padding(
            padding: EdgeInsets.only(bottom: UiConstants.spacing8),
            child:
                widget.isContract ? _buildContractInput() : _buildCommonInput(),
          ),
      ],
    );
  }

  // 构建止盈止损控制器
  Widget _buildController() {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing8),
      child: Row(
        children: [
          CheckboxWidget(
            value: _isStopLossEnabled,
            customSize: UiConstants.iconSize14,
            activeColor: context.templateColors.textPrimary,
            activeFillColor: context.templateColors.textPrimary,
            onChanged: (value) {
              setState(() {
                _isStopLossEnabled = !_isStopLossEnabled;
              });
              // 通知父组件展开/收缩状态变化
              widget.onExpandChanged?.call(_isStopLossEnabled);
            },
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing4),
            child: Text(
              '止盈止损',
              style: context.templateStyle.text.hintText.copyWith(
                fontSize: UiConstants.fontSize12 + 1,
                color: context.templateColors.textPrimary,
                decorationStyle: TextDecorationStyle.dashed,
                decoration: TextDecoration.underline,
                decorationColor: context.templateColors.textSecondary,
              ),
            ),
          ),
          Spacer(),
          if (_isStopLossEnabled && widget.showAdvanced)
            InkWellWidget(
              onTap:
                  () => {
                    EditOrderDialog.showStopProfitLoss(
                      context,
                      orderData: SpotOrderModel.mock(),
                    ),
                  },
              child: Text(
                '高级',
                style: context.templateStyle.text.hintText.copyWith(
                  fontSize: UiConstants.fontSize13,
                  color: context.templateColors.textPrimary,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // 构建共用止盈止损输入框
  Widget _buildCommonInput() {
    return Column(
      children: [
        // 止盈
        _buildCommonInputRow(
          labelText: widget.takeProfitText,
          unit: '%',
          suffixIcon: widget.takeProfitSuffix,
        ),
        SizedBox(height: UiConstants.spacing8),
        // 止损
        _buildCommonInputRow(
          labelText: widget.stopLossText,
          unit: '%',
          suffixIcon: widget.takeProfitSuffix,
        ),
      ],
    );
  }

  // 构建共用输入行
  Widget _buildCommonInputRow({
    required String labelText,
    required String unit,
    Widget? suffixIcon,
  }) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: TextFieldWidget(
            labelText: labelText,
            height: 42,
            cursorHeight: 12,
            contentPadding: EdgeInsets.only(
              left: UiConstants.spacing10,
              top: UiConstants.spacing4,
              bottom: UiConstants.spacing4,
            ),
            padding: EdgeInsets.zero,
            radius: BorderRadius.circular(UiConstants.borderRadius6),
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            suffixIcon: suffixIcon,
            suffixIconConstraints: BoxConstraints(maxWidth: 70),
          ),
        ),
        SizedBox(width: UiConstants.spacing8),
        Expanded(
          flex: 2,
          child: AnimatedBuilder(
            animation: _percentageFocusNode,
            builder: (context, child) {
              return TextFieldWidget(
                height: 42,
                cursorHeight: 12,
                contentPadding: EdgeInsets.only(
                  left: UiConstants.spacing10,
                  top: UiConstants.spacing4,
                  bottom: UiConstants.spacing4,
                ),
                padding: EdgeInsets.zero,
                radius: BorderRadius.circular(UiConstants.borderRadius6),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                labelText: _percentageFocusNode.hasFocus ? unit : null,
                focusNode: _percentageFocusNode,
                suffixIcon:
                    _percentageFocusNode.hasFocus
                        ? null
                        : Padding(
                          padding: EdgeInsets.only(
                            right: UiConstants.spacing10,
                          ),
                          child: Text(
                            unit,
                            style: context.templateStyle.text.descriptionText,
                          ),
                        ),
              );
            },
          ),
        ),
      ],
    );
  }

  // 构建合约止盈止损输入框
  Widget _buildContractInput() {
    return Column(
      children: [
        // 止盈
        _buildContractInputRow(
          labelText: widget.takeProfitText,
          unit: 'USDT',
          onTap: () => {},
        ),
        SizedBox(height: UiConstants.spacing8),
        // 止损
        _buildContractInputRow(
          labelText: widget.stopLossText,
          unit: 'USDT',
          onTap: () => {},
        ),
      ],
    );
  }

  // 构建合约止盈止损输入行
  Widget _buildContractInputRow({
    required String labelText,
    required String unit,
    String value = '',
    VoidCallback? onTap,
  }) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: TextFieldWidget(
            labelText: '$labelText($unit)',
            height: 42,
            cursorHeight: 12,
            contentPadding: EdgeInsets.only(
              left: UiConstants.spacing10,
              top: UiConstants.spacing4,
              bottom: UiConstants.spacing4,
            ),
            padding: EdgeInsets.zero,
            radius: BorderRadius.circular(UiConstants.borderRadius6),
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            suffixIcon: Padding(
              padding: EdgeInsets.only(right: UiConstants.spacing10),
              child: InkWellWidget(
                onTap: onTap,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      value,
                      style: context.templateStyle.text.bodyTextMedium,
                    ),
                    SizedBox(width: UiConstants.spacing4),
                    ThemedImage(
                      name: 'arrow_triangle_down',
                      size: 14,
                      followTheme: true,
                    ),
                  ],
                ),
              ),
            ),
            suffixIconConstraints: BoxConstraints(maxWidth: 70),
          ),
        ),
      ],
    );
  }
}
