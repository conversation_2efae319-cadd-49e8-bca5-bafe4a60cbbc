/*
*  最低价
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class LowestPrice extends StatefulWidget {
  const LowestPrice({super.key});

  @override
  State<LowestPrice> createState() => _LowestPriceState();
}

class _LowestPriceState extends State<LowestPrice> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: UiConstants.spacing8),
      child: TextFieldWidget(
        height: 42,
        labelText: '最低价格',
        cursorHeight: 12,
        contentPadding: EdgeInsets.only(
          left: UiConstants.spacing10,
          top: UiConstants.spacing4,
          bottom: UiConstants.spacing4,
        ),
        padding: EdgeInsets.zero,
        radius: BorderRadius.circular(UiConstants.borderRadius6),
        keyboardType: TextInputType.numberWithOptions(decimal: true),
        suffixIcon: Padding(
          padding: EdgeInsets.only(right: UiConstants.spacing8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                'USDT',
                style: context.templateStyle.text.descriptionTextMedium,
              ),
            ],
          ),
        ),
        suffixIconConstraints: BoxConstraints(maxWidth: 58),
      ),
    );
  }
}
