/*
*  杠杆滑块选择器
*
*  功能：
*  - 使用 Syncfusion 滑块组件
*  - 圆形节点设计
*  - 显示百分比值
*  - 支持自定义杠杆倍数范围
*  - 与图片样式保持一致
*/

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:qubic_exchange/core/index.dart';

class LeverageSelector extends StatefulWidget {
  /// 最小杠杆倍数
  final double minLeverage;

  /// 最大杠杆倍数
  final double maxLeverage;

  /// 当前杠杆倍数
  final double currentLeverage;

  /// 杠杆变化回调
  final ValueChanged<double>? onChanged;

  /// 杠杆变化结束回调
  final ValueChanged<double>? onChangeEnd;

  const LeverageSelector({
    super.key,
    this.minLeverage = 0.0,
    this.maxLeverage = 100.0,
    this.currentLeverage = 0.0,
    this.onChanged,
    this.onChangeEnd,
  });

  @override
  State<LeverageSelector> createState() => _LeverageSelectorState();
}

class _LeverageSelectorState extends State<LeverageSelector> {
  late double _currentValue;
  late double _previousValue;

  // 分割点列表
  final List<double> _dividerPoints = [0.0, 25.0, 50.0, 75.0, 100.0];

  @override
  void initState() {
    super.initState();
    _currentValue = widget.currentLeverage;
    _previousValue = widget.currentLeverage;
  }

  @override
  void didUpdateWidget(LeverageSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentLeverage != widget.currentLeverage) {
      _currentValue = widget.currentLeverage;
      _previousValue = widget.currentLeverage;
    }
  }

  /// 检查是否达到分割点并触发震动
  void _checkDividerPointAndVibrate(double newValue) {
    // 检查是否跨越了任何分割点
    for (double dividerPoint in _dividerPoints) {
      // 检查是否跨越了分割点（从一侧移动到另一侧）
      bool crossedDivider =
          (_previousValue < dividerPoint && newValue >= dividerPoint) ||
          (_previousValue > dividerPoint && newValue <= dividerPoint);

      if (crossedDivider) {
        // 触发强震动
        HapticFeedback.heavyImpact();
      }
    }
    _previousValue = newValue;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing8),
      child: _buildSlider(),
    );
  }

  /// 构建滑块组件
  Widget _buildSlider() {
    return SfSliderTheme(
      data: SfSliderThemeData(
        activeTrackHeight: 2.0,
        inactiveTrackHeight: 2.0,
        trackCornerRadius: 1.0,
        activeTrackColor: context.templateColors.textPrimary,
        inactiveTrackColor: context.templateColors.divider,
        thumbRadius: 5.0,
        thumbColor: context.templateColors.textPrimary,
        overlayRadius: 0.0,
        overlayColor: Colors.transparent,
        activeDividerColor: context.templateColors.textPrimary,
        inactiveDividerColor: context.templateColors.divider,
        activeDividerRadius: 3.0,
        inactiveDividerRadius: 3.0,
      ),
      child: SfSlider(
        min: widget.minLeverage,
        max: widget.maxLeverage,
        value: _currentValue,
        interval: 25.0,
        showDividers: true,
        showTicks: false,
        showLabels: false,
        enableTooltip: false,
        onChanged: (dynamic value) {
          // 检查分割点并触发震动
          _checkDividerPointAndVibrate(value);

          setState(() {
            _currentValue = value;
          });
          widget.onChanged?.call(value);
        },
        onChangeEnd: (dynamic value) {
          widget.onChangeEnd?.call(value);
        },
      ),
    );
  }
}
