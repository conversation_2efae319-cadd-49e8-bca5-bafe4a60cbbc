/*
*  可用余额信息
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/routes/index.dart';
import 'package:qubic_exchange/services/assets/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';
import 'package:qubic_exchange/utils/formatting/number_format_util.dart';

class AvailableInfo extends StatefulWidget {
  // 是否登录
  final bool isLogin;
  // 外边距
  final EdgeInsets? margin;

  final int direction;

  final CurrencyBalance? baseBalance ;
  final CurrencyBalance? quoteBalance ;

  final String? symbol;
  final String? quoteSymbol;

  const AvailableInfo(
    {
      super.key, 
      this.margin,
     this.isLogin = false,
     this.direction = 1,
     this.baseBalance,
     this.quoteBalance,
     this.symbol,
     this.quoteSymbol
    });

  @override
  State<AvailableInfo> createState() => _AvailableInfoState();
}

void _navigateToLogin() {
  NavigationService().navigateTo(
    AppRoutes.login,
    arguments: {'transitionType': RouteTransitionType.slideUp},
  );
}

class _AvailableInfoState extends State<AvailableInfo> {

  String buildFormatBalance(dynamic value){
    return NumberFormatUtil.formatWithComma(value,decimalDigits: widget.direction == 1 ? 2 : 8);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin,
      height: 24,
      child: Row(
        children: [
          Text(
            '可用',
            style: context.templateStyle.text.hintText.copyWith(
              fontSize: UiConstants.fontSize12 + 1,
              color: context.templateColors.textSecondary,
              decorationStyle: TextDecorationStyle.dashed,
              decoration: TextDecoration.underline,
              decorationColor: context.templateColors.textTertiary,
            ),
          ),
          Spacer(),
          InkWellWidget(
            onTap: widget.isLogin ? null : _navigateToLogin,
            child: Row(
              children: [
                Text(
                  '${buildFormatBalance(widget.direction == 1 ? widget.quoteBalance?.availableAmount : widget.baseBalance?.availableAmount)} ${widget.direction == 1 ?widget.quoteSymbol:widget.symbol}',
                  style: context.templateStyle.text.hintText.copyWith(
                    color: context.templateColors.textPrimary,
                    fontSize: UiConstants.fontSize12 + 1,
                  ),
                ),
                SizedBox(width: UiConstants.spacing4),
                Icon(
                  RemixIcons.add_box_fill,
                  size: 16,
                  color: context.templateColors.textPrimary,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
