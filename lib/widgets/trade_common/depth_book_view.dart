/*
* 盘口数据
*/

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';
import 'package:qubic_exchange/pages/trade/widgets/orderbook_depth_bottom_sheet.dart';
import 'package:qubic_exchange/services/network/websocket_service.dart';

// 盘口显示模式枚举
enum DepthViewMode {
  both, // 显示买卖盘
  sellOnly, // 只显示卖盘
  buyOnly, // 只显示买盘
}

// 盘口数据项模型
class DepthBookItem {
  double price;
  double amount;
  double percentage;
  double previousPercentage;

  DepthBookItem({
    required this.price,
    required this.amount,
    required this.percentage,
  }) : previousPercentage = percentage;

  // 更新百分比并保存之前的值
  void updatePercentage(double newPercentage) {
    previousPercentage = percentage;
    percentage = newPercentage;
  }
}

class DepthBookView extends StatefulWidget {
  // 是否为合约
  final bool isContract;
  // 高度
  final double? height;
  // 市场类型
  final int marketType;
  // 交易对符号
  final String symbol;
  // 价格点击回调
  final ValueChanged<double>? onPriceSelected;

  const DepthBookView({
    super.key,
    this.height,
    this.isContract = false,
    this.marketType = 1,
    required this.symbol,
    this.onPriceSelected,
  });

  @override
  State<DepthBookView> createState() => _DepthBookViewState();
}

class _DepthBookViewState extends State<DepthBookView>
    with TickerProviderStateMixin {
  // 当前选中的小数点
  late String _selectedDepth = '0.001';

  // 买卖盘价格项高度
  static const double _priceItemHeight = 21.0;

  // 最新价高度
  static const double _currentPriceHeight = 56.0;

  // 当前显示模式
  DepthViewMode _currentViewMode = DepthViewMode.both;

  // 买卖比例数据
  double get bullishRatio => 65.5;
  double get bearishRatio => 34.5;



  // 模拟盘口数据
  List<DepthBookItem> sellOrders = [];
  List<DepthBookItem> buyOrders = [];

  // 当前价格数据
  double? currentPrice;
  double? indexPrice;

  // WebSocket订阅
  StreamSubscription<Map<String, dynamic>>? _depthSubscription;
  StreamSubscription<Map<String, dynamic>>? _marketPriceSubscription;

  @override
  void initState() {
    super.initState();
    _initMockData();

    // 订阅WebSocket深度数据
    _initWebSocketConnection();
  }

  @override
  void dispose() {
    _depthSubscription?.cancel();
    _marketPriceSubscription?.cancel();
    _unsubscribeFromDepthData();
    super.dispose();
  }

  /// 初始化WebSocket连接
  Future<void> _initWebSocketConnection() async {
    try {
      if (!WebSocketService.instance.isConnected) {
        await WebSocketService.instance.connect();
        await Future.delayed(const Duration(milliseconds: 500));
      }

      if (WebSocketService.instance.isConnected) {
        _subscribeToDepthData();
      }
    } catch (e) {
      debugPrint('❌ 初始化WebSocket连接失败: $e');
    }
  }

  /// 订阅WebSocket深度数据
  void _subscribeToDepthData() {
    try {
      // 订阅深度数据
      final depthSubscribeMessage = {
        "action": "subscribe",
        "type": "depth",
        "market_type": widget.marketType,
        "symbol": widget.symbol
      };

      _depthSubscription = WebSocketService.instance
          .subscribe('depth', depthSubscribeMessage)
          .listen(
            (data) {
              // 检查是否是当前交易对的数据
              if (data['symbol'] == widget.symbol && data['type'] == 'depth') {
                _handleDepthData(data);
              }
            },
            onError: (error) {
              debugPrint('❌ 深度数据订阅错误: $error');
            },
          );

      // 订阅市场价格数据
      final marketPriceSubscribeMessage = {
        "action": "subscribe",
        "type": "marketPrice",
        "market_type": widget.marketType,
        "symbol": widget.symbol
      };

      _marketPriceSubscription = WebSocketService.instance
          .subscribe('marketPrice', marketPriceSubscribeMessage)
          .listen(
            (data) {
              // 检查是否是当前交易对的数据
              if (data['symbol'] == widget.symbol && data['type'] == 'marketPrice') {
                _handleMarketPriceData(data);
              }
            },
            onError: (error) {
              debugPrint('❌ 市场价格数据订阅错误: $error');
            },
          );

    } catch (e) {
      debugPrint('❌ 订阅深度数据失败: $e');
    }
  }

  /// 取消订阅WebSocket深度数据
  void _unsubscribeFromDepthData() {
    try {
      // 取消订阅深度数据
      final depthUnsubscribeMessage = {
        "action": "unsubscribe",
        "type": "depth",
        "market_type": widget.marketType,
        "symbol": widget.symbol
      };

      WebSocketService.instance.sendMessage(depthUnsubscribeMessage);

      // 取消订阅市场价格数据
      final marketPriceUnsubscribeMessage = {
        "action": "unsubscribe",
        "type": "marketPrice",
        "market_type": widget.marketType,
        "symbol": widget.symbol
      };

      WebSocketService.instance.sendMessage(marketPriceUnsubscribeMessage);
    } catch (e) {
      debugPrint('取消订阅数据失败: $e');
    }
  }

  /// 处理接收到的市场价格数据
  void _handleMarketPriceData(Map<String, dynamic> data) {
    try {
      // 解析价格数据
      final priceData = data['data'] as Map<String, dynamic>?;
      if (priceData != null) {
        final price = priceData['market_price'];
        
        if (price != null) {
          if (mounted) {
            setState(() {
              currentPrice = price is double ? price : double.tryParse(price.toString());
              indexPrice = priceData['index_price'] is double ?  priceData['index_price'] : double.tryParse( priceData['index_price']);
            });
          }
          widget.onPriceSelected?.call(currentPrice!);
        }
      }
    } catch (e) {
      //print('❌ 处理marketPrice数据错误: $e');
    }
  }

  /// 处理接收到的深度数据
  void _handleDepthData(Map<String, dynamic> data) {
    try {
      final depthData = data['data'] as Map<String, dynamic>?;
      if (depthData == null) return;

      final asks = depthData['asks'] as List<dynamic>?;
      final bids = depthData['bids'] as List<dynamic>?;

      if (asks != null && bids != null) {
        // 取最优的10档数据
        const int maxDepth = 10;

        // 解析并排序卖盘数据
        final asksList = <List<double>>[];
        for (final ask in asks) {
          if (ask is List && ask.length >= 2) {
            final price = double.tryParse(ask[0].toString()) ?? 0.0;
            final amount = double.tryParse(ask[1].toString()) ?? 0.0;
            if (price > 0 && amount > 0) {
              asksList.add([price, amount]);
            }
          }
        }

        // 解析并排序买盘数据
        final bidsList = <List<double>>[];
        for (final bid in bids) {
          if (bid is List && bid.length >= 2) {
            final price = double.tryParse(bid[0].toString()) ?? 0.0;
            final amount = double.tryParse(bid[1].toString()) ?? 0.0;
            if (price > 0 && amount > 0) {
              bidsList.add([price, amount]);
            }
          }
        }

        // 确保排序正确
        asksList.sort((a, b) => a[0].compareTo(b[0])); // 卖盘：价格从低到高
        bidsList.sort((a, b) => b[0].compareTo(a[0])); // 买盘：价格从高到低

        // 取最优的10档数据
        // 卖盘：取最后10档（最接近当前价格的最低卖价）
        final topAsks = asksList.length > maxDepth
            ? asksList.skip(asksList.length - maxDepth).toList()
            : asksList;
        // 买盘：取前10档（最接近当前价格的最高买价）
        final topBids = bidsList.take(maxDepth).toList();

        // 计算总量用于百分比计算
        double totalSellAmount = 0.0;
        double totalBuyAmount = 0.0;

        for (final ask in topAsks) {
          totalSellAmount += ask[1]; // amount
        }
        for (final bid in topBids) {
          totalBuyAmount += bid[1]; // amount
        }

        // 解析卖单数据 (asks) - 价格从低到高，需要反转显示为从高到低
        final newSellOrders = <DepthBookItem>[];
        for (final ask in topAsks) {
          final price = ask[0];
          final amount = ask[1];
          final percentage = totalSellAmount > 0 ? (amount / totalSellAmount) * 100 : 0.0;
          newSellOrders.add(DepthBookItem(
            price: price,
            amount: amount,
            percentage: percentage
          ));
        }

        // 解析买单数据 (bids) - 价格已经从高到低排序
        final newBuyOrders = <DepthBookItem>[];
        for (final bid in topBids) {
          final price = bid[0];
          final amount = bid[1];
          final percentage = totalBuyAmount > 0 ? (amount / totalBuyAmount) * 100 : 0.0;
          newBuyOrders.add(DepthBookItem(
            price: price,
            amount: amount,
            percentage: percentage,
          ));
        }

        // 更新UI
        if (mounted) {
          setState(() {
            // 卖单：原始数据是价格从低到高，反转后显示为从高到低
            // 最高价格（远离当前价格）显示在最顶部
            // 最低价格（最接近当前价格）显示在最底部
            sellOrders = newSellOrders.reversed.toList();

            // 买单：原始数据是价格从高到低，直接使用
            // 最高价格（最接近当前价格）显示在最顶部
            // 最低价格（远离当前价格）显示在最底部
            buyOrders = newBuyOrders;
          });
        }
      }
    } catch (e) {
      debugPrint('解析深度数据失败: $e');
    }
  }

  // 初始化空数据，等待WebSocket数据
  void _initMockData() {
    sellOrders = [];
    buyOrders = [];
  }



  // 切换视图模式
  void _toggleViewMode() {
    setState(() {
      switch (_currentViewMode) {
        case DepthViewMode.both:
          _currentViewMode = DepthViewMode.sellOnly;
          break;
        case DepthViewMode.sellOnly:
          _currentViewMode = DepthViewMode.buyOnly;
          break;
        case DepthViewMode.buyOnly:
          _currentViewMode = DepthViewMode.both;
          break;
      }
    });
  }

  // 根据当前视图模式获取对应的图标名称
  String _getViewModeIconName() {
    switch (_currentViewMode) {
      case DepthViewMode.both:
        return 'icon_contract_order_book_both_left';
      case DepthViewMode.sellOnly:
        return 'icon_contract_order_book_sell_left'; // 假设有卖盘图标
      case DepthViewMode.buyOnly:
        return 'icon_contract_order_book_buy_left'; // 假设有买盘图标
    }
  }

  @override
  Widget build(BuildContext context) {
    // 确保高度不为负值，设置最小高度
    final containerHeight =
        widget.height != null && widget.height! > 10
            ? widget.height! - 0
            : 300.0; // 默认高度

    return Container(
      height: containerHeight,
      width: double.infinity,
      color: context.templateColors.surface,
      child: Column(
        children: [
          // 头部
          _buildHeader(),

          // 卖盘
          if (_currentViewMode != DepthViewMode.buyOnly) _buildSellOrders(),

          // 最新价
          _buildCurrentPrice(),

          // 买盘
          if (_currentViewMode != DepthViewMode.sellOnly) _buildBuyOrders(),

          // 买卖比例条
          _buildRatioBar(),

          // 视图 & 小数点控制器
          _buildViewAndDecimalController(),
        ],
      ),
    );
  }

  // 构建头部
  Widget _buildHeader() {
    final textStyle = context.templateStyle.text.hintText.copyWith(
      color: context.templateColors.textSecondary,
    );
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // 资金费率 & 倒计时
          if (widget.isContract)
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text('资金费率 / 倒计时', style: textStyle),
                Text.rich(
                  TextSpan(
                    style: textStyle,
                    children: [
                      // 资金费率
                      TextSpan(text: '0.0100%'),
                      TextSpan(text: ' / '),
                      // 倒计时
                      TextSpan(text: '03:03:23'),
                    ],
                  ),
                ),
              ],
            ),

          // 标题
          Padding(
            padding: EdgeInsets.symmetric(vertical: UiConstants.spacing4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('价格\n(USDT)', style: textStyle),
                Text('数量\n(BTC)', style: textStyle),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建买盘
  Widget _buildBuyOrders() {
    return Expanded(
      child: ListView.builder(
        physics: NeverScrollableScrollPhysics(),
        itemCount: buyOrders.length,
        itemBuilder: (context, index) {
          return _buildPriceItem(item: buyOrders[index], isSell: false);
        },
      ),
    );
  }

  // 构建卖盘
  Widget _buildSellOrders() {
    return Expanded(
      child: ListView.builder(
        physics: NeverScrollableScrollPhysics(),
        itemCount: sellOrders.length,
        itemBuilder: (context, index) {
          return _buildPriceItem(item: sellOrders[index], isSell: true);
        },
      ),
    );
  }

  // 构建买卖盘项
  Widget _buildPriceItem({required DepthBookItem item, bool isSell = false}) {
    final backgroundColor =
        isSell
            ? context.templateColors.tradeSell.withValues(alpha: 0.1)
            : context.templateColors.tradeBuy.withValues(alpha: 0.1);

    return SizedBox(
      height: _priceItemHeight,
      child: Stack(
        children: [
          // 背景动画条 - 使用 TweenAnimationBuilder 实现平滑过渡
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 300),
            tween: Tween<double>(
              begin: item.previousPercentage / 100,
              end: item.percentage / 10,
            ),
            curve: Curves.easeInOut,
            builder: (context, animatedPercentage, child) {
              return Align(
                alignment: Alignment.centerRight,
                child: FractionallySizedBox(
                  widthFactor: animatedPercentage.clamp(0.0, 1.0),
                  child: Container(
                    height: _priceItemHeight,
                    decoration: BoxDecoration(color: backgroundColor),
                  ),
                ),
              );
            },
          ),

          // 前景内容
          InkWell(
            onTap: () {
              widget.onPriceSelected?.call(item.price);
            },
            child: SizedBox(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    item.price.toStringAsFixed(2),
                    style: context.templateStyle.text.hintText.copyWith(
                      color:
                          isSell
                              ? context.templateColors.tradeSell
                              : context.templateColors.tradeBuy,
                    ),
                  ),
                  SizedBox(width: UiConstants.spacing10),
                  Text(
                    item.amount.toStringAsFixed(2),
                    style: context.templateStyle.text.hintText.copyWith(
                      color: context.templateColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建最新价 & 标记价
  Widget _buildCurrentPrice() {
    return Container(
      height: _currentPriceHeight,
      alignment: Alignment.centerLeft,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  currentPrice.toString(),
                  style: context.templateStyle.text.h4
                ),
                Text(
                  indexPrice.toString(),
                  style: context.templateStyle.text.hintText.copyWith(
                    color: context.templateColors.textSecondary,
                    decorationStyle: TextDecorationStyle.dashed,
                    decoration: TextDecoration.underline,
                    decorationColor: context.templateColors.textTertiary,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            RemixIcons.arrow_right_s_line,
            size: 18,
            color: context.templateColors.textSecondary,
          ),
        ],
      ),
    );
  }

  // 构建买卖比例条
  Widget _buildRatioBar() {
    return Container(
      height: 15,
      margin: EdgeInsets.symmetric(vertical: UiConstants.spacing4),
      child: RatioProgressBar(
        layout: RatioBarLayout.horizontal,
        height: 2,
        bullishRatio: bullishRatio,
        bearishRatio: bearishRatio,
        bullishText: 'B',
        bearishText: 'S',
        labelStyle: context.templateStyle.text.hintText,
        showDecimal: false,
      ),
    );
  }

  // 构建视图 & 小数点控制器
  Widget _buildViewAndDecimalController() {
    return SizedBox(
      height: 30,
      child: Row(
        children: [
          // 买卖盘视图显示
          InkWellWidget(
            onTap: _toggleViewMode,
            child: Padding(
              padding: EdgeInsets.only(right: UiConstants.spacing14),
              child: ThemedImage(
                name: _getViewModeIconName(),
                size: 20,
                followTheme: true,
              ),
            ),
          ),

          // 小数点控制
          Expanded(
            child: InkWellWidget(
              onTap:
                  () => {
                    OrderbookDepthBottomSheet.show(
                      context,
                      depths: ['0.001', '0.01', '0.1', '1.0'],
                      selectedDepth: '0.001',
                      onSelected: (depth) {
                        setState(() {
                          _selectedDepth = depth;
                        });
                      },
                    ),
                  },
              child: Container(
                height: 24,
                padding: EdgeInsets.symmetric(
                  horizontal: UiConstants.spacing10,
                ),
                decoration: BoxDecoration(
                  color: context.templateColors.inputBackground,
                  borderRadius: BorderRadius.circular(
                    UiConstants.borderRadius6,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _selectedDepth,
                      style: context.templateStyle.text.hintText.copyWith(
                        color: context.templateColors.textPrimary,
                      ),
                    ),
                    ThemedImage(
                      name: 'arrow_triangle_down',
                      size: UiConstants.iconSize10,
                      followTheme: true,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
