/*
*  杠杆模式选择器
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class MarginModeSelector extends StatefulWidget {
  const MarginModeSelector({super.key});

  @override
  State<MarginModeSelector> createState() => _MarginModeSelectorState();
}

class _MarginModeSelectorState extends State<MarginModeSelector> {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // 杠杆模式
        _buildButtonItem(flex: 4, text: '逐仓', onTap: () {}),
        SizedBox(width: UiConstants.spacing4),

        // 杠杆倍数
        _buildButtonItem(flex: 4, text: '10x', onTap: () {}),
        SizedBox(width: UiConstants.spacing4),

        // 借/还
        _buildButtonItem(flex: 3, text: '借/还', onTap: () {}),
      ],
    );
  }

  // 构建按钮项
  Widget _buildButtonItem({
    int flex = 2,
    required String text,
    required VoidCallback onTap,
  }) {
    return Expanded(
      flex: flex,
      child: InkWellWidget(
        child: Container(
          height: 22,
          padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: context.templateColors.inputBackground,
            borderRadius: BorderRadius.circular(UiConstants.borderRadius4),
          ),
          child: Text(
            text,
            style: context.templateStyle.text.hintText.copyWith(
              color: context.templateColors.textPrimary,
            ),
          ),
        ),
      ),
    );
  }
}
