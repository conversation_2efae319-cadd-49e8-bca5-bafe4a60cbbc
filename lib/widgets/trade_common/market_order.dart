/*
*  市价单
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';

class MarketOrder extends StatefulWidget {
  const MarketOrder({super.key});

  @override
  State<MarketOrder> createState() => _MarketOrderState();
}

class _MarketOrderState extends State<MarketOrder> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 42,
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
      margin: EdgeInsets.symmetric(vertical: UiConstants.spacing8),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        color: context.templateColors.inputBackground,
        borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
        border: Border.all(width: 1, color: context.templateColors.divider),
      ),
      child: Text(
        '市价成交',
        style: context.templateStyle.text.bodyTextMedium.copyWith(
          color: context.templateColors.textTertiary,
        ),
      ),
    );
  }
}
