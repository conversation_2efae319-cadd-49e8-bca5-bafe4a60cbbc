/*
*  限价输入框
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class LimitPriceInput extends StatefulWidget {
  final EdgeInsets? padding;
  const LimitPriceInput({super.key, this.padding});

  @override
  State<LimitPriceInput> createState() => _LimitPriceInputState();
}

class _LimitPriceInputState extends State<LimitPriceInput> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.padding ?? EdgeInsets.only(bottom: UiConstants.spacing8),
      child: TextFieldWidget(
        height: 42,
        labelText: '限价',
        cursorHeight: 12,
        contentPadding: EdgeInsets.only(
          left: UiConstants.spacing10,
          top: UiConstants.spacing4,
          bottom: UiConstants.spacing4,
        ),
        padding: EdgeInsets.zero,
        radius: BorderRadius.circular(UiConstants.borderRadius6),
        keyboardType: TextInputType.numberWithOptions(decimal: true),
        suffixIcon: Padding(
          padding: EdgeInsets.only(right: UiConstants.spacing10),
          child: Text(
            'ETH',
            style: context.templateStyle.text.descriptionTextMedium,
          ),
        ),
        suffixIconConstraints: BoxConstraints(maxWidth: 80),
      ),
    );
  }
}
