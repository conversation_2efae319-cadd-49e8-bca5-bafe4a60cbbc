/*
*  订单策略选择底部弹窗
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/utils/index.dart';

/// 订单策略选项数据结构
class OrderStrategyOption {
  /// 显示文本
  final String text;

  /// 描述信息
  final String description;

  /// 选项索引
  final int index;

  /// 是否启用
  final bool enabled;

  const OrderStrategyOption({
    required this.text,
    required this.description,
    required this.index,
    this.enabled = true,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OrderStrategyOption &&
          runtimeType == other.runtimeType &&
          text == other.text &&
          index == other.index;

  @override
  int get hashCode => text.hashCode ^ index.hashCode;

  @override
  String toString() =>
      'OrderStrategyOption(text: $text, description: $description, index: $index)';
}

class OrderStrategyDialog {
  // 显示订单策略选择底部弹窗
  static Future<OrderStrategyOption?> show(
    BuildContext context, {
    required List<OrderStrategyOption> options,
    OrderStrategyOption? selectedOption,
    String title = '选择订单策略',
    bool showHeader = true,
    bool showCloseButton = true,
    bool showDragHandle = true,
    bool showHeaderBorder = false,
    double? maxHeight,
    double? headerHeight,
    bool useSafeArea = false,
  }) {
    return BottomSheetWidget.show<OrderStrategyOption>(
      context: context,
      title: title,
      showCloseButton: showCloseButton,
      showHeader: showHeader,
      showDragHandle: showDragHandle,
      headerHeight: headerHeight ?? 58,
      showHeaderBorder: showHeaderBorder,
      maxHeight: maxHeight ?? MediaQuery.of(context).size.height * 0.8,
      showBottomButton: false,
      useSafeArea: useSafeArea,
      child: _OrderStrategyDialogContent(
        options: options,
        selectedOption: selectedOption,
      ),
    );
  }
}

class _OrderStrategyDialogContent extends StatefulWidget {
  final List<OrderStrategyOption> options;
  final OrderStrategyOption? selectedOption;

  const _OrderStrategyDialogContent({
    required this.options,
    this.selectedOption,
  });

  @override
  State<_OrderStrategyDialogContent> createState() =>
      _OrderStrategyDialogContentState();
}

class _OrderStrategyDialogContentState
    extends State<_OrderStrategyDialogContent> {
  OrderStrategyOption? _selectedOption;

  @override
  void initState() {
    super.initState();
    // 初始化当前选中项，如果没有传入选中项则默认选中第一项
    _selectedOption =
        widget.selectedOption ??
        (widget.options.isNotEmpty ? widget.options.first : null);
  }

  /// 处理选项点击
  void _handleOptionTap(OrderStrategyOption option) {
    setState(() {
      _selectedOption = option;
    });

    // 短暂延迟让用户看到选中效果，然后关闭弹窗并返回结果
    Future.delayed(const Duration(milliseconds: 50), () {
      if (mounted) {
        Navigator.of(context).pop(option);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: UiConstants.spacing20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 订单策略选项列表
          ...widget.options.map((option) {
            final bool isSelected = option == _selectedOption;
            return InkWellWidget(
              onTap: () => _handleOptionTap(option),
              child: _buildCheckItem(option, isSelected),
            );
          }),

          // 底部安全区域
          SizedBox(height: ScreenUtil.bottomSafeHeight(context)),
        ],
      ),
    );
  }

  // 构建选择项
  Widget _buildCheckItem(OrderStrategyOption option, bool isSelected) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(UiConstants.spacing16),
      margin: EdgeInsets.only(bottom: UiConstants.spacing8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
        border: Border.all(
          width: 1,
          color:
              isSelected
                  ? context.templateColors.textPrimary
                  : context.templateColors.divider,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 策略名称
          Text(option.text, style: context.templateStyle.text.bodyTextMedium),
          if (option.description.isNotEmpty) ...[
            SizedBox(height: UiConstants.spacing4),
            Text(
              option.description,
              style: context.templateStyle.text.hintText.copyWith(
                color: context.templateColors.textSecondary,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
