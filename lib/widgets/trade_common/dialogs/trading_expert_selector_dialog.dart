/*
* 交易专家选择底部弹窗
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/utils/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:remixicon/remixicon.dart';

/// 交易专家选项数据模型
class TradingExpertOption {
  final String key;
  final String text;

  const TradingExpertOption({required this.key, required this.text});
}

class TradingExpertSelector {
  // 显示交易专家选择底部弹窗
  static Future<String?> show(
    BuildContext context, {
    required List<TradingExpertOption> options,
    String? selectedKey,
    Function(String)? onSelected,
    bool showHeader = true,
    bool showCloseButton = true,
    bool showDragHandle = true,
    bool showHeaderBorder = false,
    double? maxHeight,
    double? headerHeight,
    bool useSafeArea = false,
  }) async {
    return await BottomSheetWidget.show<String>(
      context: context,
      title: '交易专家',
      showCloseButton: showCloseButton,
      showHeader: showHeader,
      showDragHandle: showDragHandle,
      headerHeight: headerHeight ?? 58,
      showHeaderBorder: showHeaderBorder,
      maxHeight: maxHeight ?? MediaQuery.of(context).size.height * 0.8,
      showBottomButton: false,
      useSafeArea: useSafeArea,
      child: _TradingExpertSelectorContent(
        options: options,
        selectedKey:
            selectedKey ?? (options.isNotEmpty ? options.first.key : null),
        onSelected: onSelected,
      ),
    );
  }
}

class _TradingExpertSelectorContent extends StatefulWidget {
  final List<TradingExpertOption> options;
  final String? selectedKey;
  final Function(String)? onSelected;

  const _TradingExpertSelectorContent({
    required this.options,
    this.selectedKey,
    this.onSelected,
  });

  @override
  State<_TradingExpertSelectorContent> createState() =>
      _TradingExpertSelectorContentState();
}

class _TradingExpertSelectorContentState
    extends State<_TradingExpertSelectorContent> {
  late String? _selectedKey;

  @override
  void initState() {
    super.initState();
    // 初始化选中状态，默认选中第一个选项
    _selectedKey =
        widget.selectedKey ??
        (widget.options.isNotEmpty ? widget.options.first.key : null);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing28),
      child: _buildContent(),
    );
  }

  // 构建选项列表
  Widget _buildContent() {
    return SizedBox(
      height: ScreenUtil.screenHeight(context) * 0.55,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 添加顶部间距
          SizedBox(height: UiConstants.spacing8),

          // 渲染选项列表
          ...widget.options.map(
            (option) => _buildSelectItem(
              onTap: () => _handleOptionSelected(option.key),
              isSelected: _selectedKey == option.key,
              text: option.text,
            ),
          ),

          // 添加底部间距
          SizedBox(height: UiConstants.spacing16),
        ],
      ),
    );
  }

  // 处理选项选择
  void _handleOptionSelected(String key) {
    setState(() {
      _selectedKey = key;
    });

    // 调用回调函数
    widget.onSelected?.call(key);

    // 关闭弹窗并返回选中的key
    Navigator.of(context).pop(key);
  }

  // 构建选择项
  Widget _buildSelectItem({
    required VoidCallback onTap,
    required bool isSelected,
    required String text,
  }) {
    return InkWellWidget(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: UiConstants.spacing12),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(text, style: context.templateStyle.text.bodyLargeMedium),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                RemixIcons.check_line,
                size: 20,
                color: context.templateColors.textPrimary,
              ),
          ],
        ),
      ),
    );
  }
}
