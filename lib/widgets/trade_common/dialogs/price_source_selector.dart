/*
*   价格来源选择器
  */

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

/// 价格来源选项数据结构
class PriceSourceOption {
  /// 显示文本
  final String text;

  /// 选项索引
  final int index;

  /// 是否启用
  final bool enabled;

  const PriceSourceOption({
    required this.text,
    required this.index,
    this.enabled = true,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PriceSourceOption &&
          runtimeType == other.runtimeType &&
          text == other.text &&
          index == other.index;

  @override
  int get hashCode => text.hashCode ^ index.hashCode;

  @override
  String toString() => 'PriceSourceOption(text: $text, index: $index)';
}

/// 价格来源选择器底部弹窗
class PriceSourceSelector {
  /// 显示价格来源选择底部弹窗
  static Future<PriceSourceOption?> show(
    BuildContext context, {
    required List<PriceSourceOption> options,
    PriceSourceOption? selectedOption,
    String title = '选择价格来源',
    bool showHeader = true,
    bool showCloseButton = true,
    bool showDragHandle = true,
    bool showHeaderBorder = false,
    double? maxHeight,
    double? headerHeight,
    bool useSafeArea = false,
  }) {
    // 参数验证
    assert(options.isNotEmpty, '选项列表不能为空');

    return BottomSheetWidget.show<PriceSourceOption>(
      context: context,
      title: title,
      showCloseButton: showCloseButton,
      showHeader: showHeader,
      showDragHandle: showDragHandle,
      headerHeight: headerHeight ?? 58,
      showHeaderBorder: showHeaderBorder,
      maxHeight: maxHeight ?? MediaQuery.of(context).size.height * 0.8,
      showBottomButton: false,
      useSafeArea: useSafeArea,
      child: _PriceSourceSelectorContent(
        selectedOption: selectedOption,
        options: options,
      ),
    );
  }
}

class _PriceSourceSelectorContent extends StatefulWidget {
  final PriceSourceOption? selectedOption;
  final List<PriceSourceOption> options;

  const _PriceSourceSelectorContent({
    required this.selectedOption,
    required this.options,
  });

  @override
  State<_PriceSourceSelectorContent> createState() =>
      _PriceSourceSelectorContentState();
}

class _PriceSourceSelectorContentState
    extends State<_PriceSourceSelectorContent> {
  PriceSourceOption? _currentSelected;

  @override
  void initState() {
    super.initState();
    // 初始化当前选中项
    _currentSelected = widget.selectedOption;
  }

  @override
  Widget build(BuildContext context) {
    // 选择器内容区域
    return Column(
      children: [
        ...widget.options.map((option) {
          final isSelected = _currentSelected == option;
          return _buildSelectItem(
            option: option,
            isSelected: isSelected,
            onTap: () => _handleOptionTap(option),
          );
        }),
        SizedBox(height: MediaQuery.of(context).padding.bottom),
      ],
    );
  }

  /// 处理选项点击
  void _handleOptionTap(PriceSourceOption option) {
    setState(() {
      _currentSelected = option;
    });

    // 短暂延迟让用户看到选中效果，然后关闭弹窗并返回结果
    Future.delayed(const Duration(milliseconds: 50), () {
      if (mounted) {
        Navigator.of(context).pop(option);
      }
    });
  }

  /// 构建选择项
  Widget _buildSelectItem({
    required PriceSourceOption option,
    required bool isSelected,
    VoidCallback? onTap,
  }) {
    return InkWellWidget(
      onTap: option.enabled ? onTap : null,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: UiConstants.spacing16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              width: 1,
              color: context.templateColors.divider.withValues(alpha: 0.2),
            ),
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                option.text,
                style: context.templateStyle.text.bodyTextMedium.copyWith(
                  color:
                      option.enabled
                          ? (isSelected
                              ? context.templateColors.primary
                              : context.templateColors.textPrimary)
                          : context.templateColors.textTertiary,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                RemixIcons.check_line,
                color: context.templateColors.primary,
                size: 18,
              ),
          ],
        ),
      ),
    );
  }
}
