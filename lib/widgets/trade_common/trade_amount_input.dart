/*
*  交易额输入
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class TradeAmountInput extends StatefulWidget {
  final bool isButton;
  final ValueChanged<double?>? onChanged;
  final TextEditingController? controller;
  const TradeAmountInput({super.key, this.isButton = true, this.onChanged, this.controller});

  @override
  State<TradeAmountInput> createState() => _TradeAmountInputState();
}

class _TradeAmountInputState extends State<TradeAmountInput> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing8),
      child: TextFieldWidget(
        controller: widget.controller,
        height: 42,
        labelText: '交易额',
        cursorHeight: 12,
        contentPadding: EdgeInsets.only(
          left: UiConstants.spacing10,
          top: UiConstants.spacing4,
          bottom: UiConstants.spacing4,
        ),
        padding: EdgeInsets.zero,
        radius: BorderRadius.circular(UiConstants.borderRadius6),
        keyboardType: TextInputType.numberWithOptions(decimal: true),
        onChanged: (value) {
          final amount = double.tryParse(value);
          widget.onChanged?.call(amount);
        },
        suffixIcon: Padding(
          padding: EdgeInsets.only(right: UiConstants.spacing10),
          child: InkWellWidget(
            onTap: widget.isButton ? () => {} : null,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  'USDT',
                  style:
                      widget.isButton
                          ? context.templateStyle.text.bodyTextMedium
                          : context.templateStyle.text.descriptionTextMedium,
                ),
                if (widget.isButton) ...[
                  SizedBox(width: UiConstants.spacing4),
                  ThemedImage(
                    name: 'arrow_triangle_down',
                    size: 14,
                    followTheme: true,
                  ),
                ],
              ],
            ),
          ),
        ),
        suffixIconConstraints: BoxConstraints(maxWidth: 80),
      ),
    );
  }
}
