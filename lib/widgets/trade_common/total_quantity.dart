/*
*  总数量
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class TotalQuantity extends StatefulWidget {
  const TotalQuantity({super.key});

  @override
  State<TotalQuantity> createState() => _TotalQuantityState();
}

class _TotalQuantityState extends State<TotalQuantity> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: UiConstants.spacing8),
      child: TextFieldWidget(
        height: 42,
        labelText: '总数量',
        cursorHeight: 12,
        contentPadding: EdgeInsets.only(
          left: UiConstants.spacing10,
          top: UiConstants.spacing4,
          bottom: UiConstants.spacing4,
        ),
        padding: EdgeInsets.zero,
        radius: BorderRadius.circular(UiConstants.borderRadius6),
        keyboardType: TextInputType.numberWithOptions(decimal: true),
        suffixIcon: Padding(
          padding: EdgeInsets.only(right: UiConstants.spacing8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                'ETH',
                style: context.templateStyle.text.descriptionTextMedium,
              ),
            ],
          ),
        ),
        suffixIconConstraints: BoxConstraints(maxWidth: 58),
      ),
    );
  }
}
