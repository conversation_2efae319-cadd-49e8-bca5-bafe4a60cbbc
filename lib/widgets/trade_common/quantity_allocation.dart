/*
*  数量分配
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class QuantityAllocation extends StatefulWidget {
  const QuantityAllocation({super.key});

  @override
  State<QuantityAllocation> createState() => _QuantityAllocationState();
}

class _QuantityAllocationState extends State<QuantityAllocation> {
  // 选项
  final List<String> options = ['平均', '递增', '递减'];

  // 当前选中的选项索引
  int? _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing8),
      child: Column(
        children: [
          //  头部
          _buildHeader(),

          // 选项
          _buildOptions(),
        ],
      ),
    );
  }

  // 构建头部
  Widget _buildHeader() {
    return Row(
      children: [
        Text(
          '数量分配',
          style: context.templateStyle.text.hintText.copyWith(
            color: context.templateColors.textPrimary,
          ),
        ),
        SizedBox(width: UiConstants.spacing4),
        Icon(
          RemixIcons.information_fill,
          size: 14,
          color: context.templateColors.textSecondary,
        ),
        Spacer(),
        InkWellWidget(
          child: Row(
            children: [
              Text('预览', style: context.templateStyle.text.hintText),
              Icon(
                RemixIcons.arrow_right_s_line,
                size: 14,
                color: context.templateColors.textTertiary,
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 构建选项
  Widget _buildOptions() {
    return Padding(
      padding: EdgeInsets.only(top: UiConstants.spacing8),
      child: Row(
        children:
            options
                .asMap()
                .entries
                .map(
                  (entry) => Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(
                        right:
                            entry.key < options.length - 1
                                ? UiConstants.spacing8
                                : 0,
                      ),
                      child: _buildItem(
                        text: entry.value,
                        index: entry.key,
                        selected: _selectedIndex == entry.key,
                      ),
                    ),
                  ),
                )
                .toList(),
      ),
    );
  }

  // 构建单项
  Widget _buildItem({
    required String text,
    required bool selected,
    required int index,
  }) {
    return InkWellWidget(
      onTap: () {
        setState(() {
          _selectedIndex = index;
        });
      },
      child: Container(
        height: 26,
        padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing8),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: context.templateColors.inputBackground,
          borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
          border: Border.all(
            width: 1,
            color:
                selected
                    ? context.templateColors.textPrimary
                    : Colors.transparent,
          ),
        ),
        child: Text(
          text,
          style: context.templateStyle.text.hintTextMedium.copyWith(
            color:
                selected
                    ? context.templateColors.textPrimary
                    : context.templateColors.textSecondary,
          ),
        ),
      ),
    );
  }
}
