/*
*  平均委托价格
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';

class AverageOrderPrice extends StatefulWidget {
  const AverageOrderPrice({super.key});

  @override
  State<AverageOrderPrice> createState() => _AverageOrderPriceState();
}

class _AverageOrderPriceState extends State<AverageOrderPrice> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 26,
      padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
      margin: EdgeInsets.only(bottom: UiConstants.spacing8),
      decoration: BoxDecoration(
        color: context.templateColors.inputBackground,
        borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
      ),
      child: Row(
        children: [
          Text(
            '委托均价',
            style: context.templateStyle.text.hintText.copyWith(
              color: context.templateColors.textSecondary,
              decorationStyle: TextDecorationStyle.dashed,
              decoration: TextDecoration.underline,
              decorationColor: context.templateColors.textTertiary,
            ),
          ),
          Spacer(),
          Text(
            '--',
            style: context.templateStyle.text.hintText.copyWith(
              color: context.templateColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }
}
