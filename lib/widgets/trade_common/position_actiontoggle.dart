/*
* 仓位模式切换
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class PositionActiontoggle extends StatefulWidget {
  // 选项内容
  final List<String> tabKeys;

  // 选中颜色
  final Color? selectedColor;

  // 选中指示器颜色
  final Color? indicatorColor;

  /// 选择变化回调，返回选中的索引
  final ValueChanged<int>? onChanged;

  /// 初始选中的索引
  final int initialIndex;

  const PositionActiontoggle({
    super.key,
    this.tabKeys = const ['开仓', '平仓'],
    this.selectedColor,
    this.indicatorColor,
    this.onChanged,
    this.initialIndex = 0,
  });

  @override
  State<PositionActiontoggle> createState() => _PositionActiontoggleState();
}

class _PositionActiontoggleState extends State<PositionActiontoggle>
    with TickerProviderStateMixin {
  // 创建标签栏控制器
  late TabController _tabController;

  // 初始化
  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.tabKeys.length,
      vsync: this,
      initialIndex: widget.initialIndex,
    );
  }

  // 销毁
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing8),
      child: Container(
        padding: EdgeInsets.all(UiConstants.spacing2),
        decoration: BoxDecoration(
          color: context.templateColors.tabbarBackground,
          borderRadius: BorderRadius.circular(UiConstants.borderRadius8),
        ),
        child: TabbarWidget(
          height: 26,
          controller: _tabController,
          tabs: widget.tabKeys.map((key) => TabItem(title: key)).toList(),
          indicatorStyle: TabBarIndicatorStyle.filled,
          isScrollable: false,
          tabAlignment: TabAlignment.fill,
          indicatorColor:
              widget.indicatorColor ?? context.templateColors.surface,
          indicatorRadius: UiConstants.borderRadius6,
          labelStyle: context.templateStyle.text.bodyTextMedium,
          selectedColor:
              widget.selectedColor ?? context.templateColors.textPrimary,
          unselectedColor: context.templateColors.textSecondary,
          labelPadding: EdgeInsets.only(top: 3),
          onTap: (index) {
            widget.onChanged?.call(index);
          },
        ),
      ),
    );
  }
}
