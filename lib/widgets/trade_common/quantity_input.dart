/*
*  数量输入框
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class QuantityInput extends StatefulWidget {
  final bool isButton;
  final EdgeInsets? padding;
  final ValueChanged<double?>? onChanged;
  final TextEditingController? controller;
  final String symbol;
  const QuantityInput({super.key, this.isButton = true, this.padding, this.onChanged, this.controller,this.symbol = 'BTC'});

  @override
  State<QuantityInput> createState() => _QuantityInputState();
}

class _QuantityInputState extends State<QuantityInput> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.padding ?? EdgeInsets.only(bottom: UiConstants.spacing8),
      child: TextFieldWidget(
        controller: widget.controller,
        height: 42,
        labelText: '数量',
        cursorHeight: 12,
        contentPadding: EdgeInsets.only(
          left: UiConstants.spacing10,
          top: UiConstants.spacing4,
          bottom: UiConstants.spacing4,
        ),
        padding: EdgeInsets.zero,
        radius: BorderRadius.circular(UiConstants.borderRadius6),
        keyboardType: TextInputType.numberWithOptions(decimal: true),
        onChanged: (value) {
          final quantity = double.tryParse(value);
          widget.onChanged?.call(quantity);
        },
        suffixIcon: Padding(
          padding: EdgeInsets.only(right: UiConstants.spacing10),
          child: InkWellWidget(
            onTap: widget.isButton ? () => {} : null,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  widget.symbol,
                  style:
                      widget.isButton
                          ? context.templateStyle.text.bodyTextMedium
                          : context.templateStyle.text.descriptionTextMedium,
                ),
                if (widget.isButton) ...[
                  SizedBox(width: UiConstants.spacing4),
                  ThemedImage(
                    name: 'arrow_triangle_down',
                    size: 14,
                    followTheme: true,
                  ),
                ],
              ],
            ),
          ),
        ),
        suffixIconConstraints: BoxConstraints(maxWidth: 80),
      ),
    );
  }
}
