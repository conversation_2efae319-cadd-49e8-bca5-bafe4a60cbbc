/*
*  可开/可平仓数量信息
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';

class OpenCloseInfo extends StatefulWidget {
  final String text;
  final EdgeInsets? margin;

  const OpenCloseInfo({super.key, this.margin, this.text = '可开'});

  @override
  State<OpenCloseInfo> createState() => _OpenCloseInfoState();
}

class _OpenCloseInfoState extends State<OpenCloseInfo> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin,
      height: 24,
      child: Row(
        children: [
          Text(
            widget.text,
            style: context.templateStyle.text.hintText.copyWith(
              fontSize: UiConstants.fontSize13,
              color: context.templateColors.textSecondary,
              decorationStyle: TextDecorationStyle.dashed,
              decoration: TextDecoration.underline,
              decorationColor: context.templateColors.textTertiary,
            ),
          ),
          Spacer(),
          Text(
            '0.00 USDT',
            style: context.templateStyle.text.hintText.copyWith(
              color: context.templateColors.textPrimary,
              fontSize: UiConstants.fontSize13,
            ),
          ),
        ],
      ),
    );
  }
}
