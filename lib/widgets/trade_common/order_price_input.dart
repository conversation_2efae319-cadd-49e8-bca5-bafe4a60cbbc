/*
*  委托价输入框
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class OrderPriceInput extends StatefulWidget {
  final EdgeInsets? padding;
  final ValueChanged<double?>? onChanged;
  const OrderPriceInput({super.key, this.padding, this.onChanged});

  @override
  State<OrderPriceInput> createState() => _OrderPriceInputState();
}

class _OrderPriceInputState extends State<OrderPriceInput> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.padding ?? EdgeInsets.only(bottom: UiConstants.spacing8),
      child: TextFieldWidget(
        height: 42,
        labelText: '委托价',
        cursorHeight: 12,
        contentPadding: EdgeInsets.only(
          left: UiConstants.spacing10,
          top: UiConstants.spacing4,
          bottom: UiConstants.spacing4,
        ),
        padding: EdgeInsets.zero,
        radius: BorderRadius.circular(UiConstants.borderRadius6),
        keyboardType: TextInputType.numberWithOptions(decimal: true),
        onChanged: (value) {
          final price = double.tryParse(value);
          widget.onChanged?.call(price);
        },
        suffixIcon: Padding(
          padding: EdgeInsets.only(right: UiConstants.spacing10),
          child: Text(
            'ETH',
            style: context.templateStyle.text.descriptionTextMedium,
          ),
        ),
        suffixIconConstraints: BoxConstraints(maxWidth: 80),
      ),
    );
  }
}
