/*
* 价格输入框
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';

class PriceInput extends StatefulWidget {
  final bool showBBO;
  final bool showIncrementButtons;
  final ValueChanged<double?>? onChanged;
  const PriceInput({
    super.key,
    this.showIncrementButtons = false,
    this.showBBO = true,
    this.onChanged,
  });

  @override
  State<PriceInput> createState() => _PriceInputState();
}

class _PriceInputState extends State<PriceInput> {
  // 是否为对手价
  bool isCounterpartyPrice = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          top: -60,
          child: Container(width: 40, height: 60, color: Colors.red),
        ),
        Container(
          padding: EdgeInsets.symmetric(vertical: UiConstants.spacing8),
          child: Row(
            children: [
              // 价格输入
              Expanded(
                child:
                    isCounterpartyPrice
                        ? _buildCounterOrder()
                        : _buildPriceInput(),
              ),
              if (widget.showBBO) ...[
                SizedBox(width: UiConstants.spacing8),
                // 切换按钮
                _buildSwitchButton(),
              ],
            ],
          ),
        ),
      ],
    );
  }

  // 构建价格输入
  Widget _buildPriceInput() {
    return SizedBox(
      child: TextFieldWidget(
        labelText: '价格',
        cursorHeight: 14,
        height: 42,
        textStyle: context.templateStyle.text.bodyTextMedium,
        textAlignVertical: TextAlignVertical.top,
        contentPadding: EdgeInsets.only(
          left: UiConstants.spacing10,
          top: UiConstants.spacing6,
          right: UiConstants.spacing10,
          bottom: UiConstants.spacing6,
        ),
        padding: EdgeInsets.zero,
        radius: BorderRadius.circular(UiConstants.borderRadius6),
        keyboardType: TextInputType.numberWithOptions(decimal: true),
        onChanged: (value) {
          final price = double.tryParse(value);
          widget.onChanged?.call(price);
        },
        suffixIcon:
            widget.showIncrementButtons
                ? Padding(
                  padding: EdgeInsets.only(right: UiConstants.spacing8),
                  child: Row(
                    children: [
                      InkWellWidget(
                        onTap: () => {},
                        child: Icon(RemixIcons.subtract_line),
                      ),
                      SizedBox(width: UiConstants.spacing4),
                      InkWellWidget(
                        onTap: () => {},
                        child: Icon(RemixIcons.add_line),
                      ),
                    ],
                  ),
                )
                : null,
        suffixIconConstraints: BoxConstraints(
          maxWidth: widget.showIncrementButtons ? 48 : 0.0,
        ),
      ),
    );
  }

  // 构建市价显示
  Widget _buildCounterOrder() {
    return InkWellWidget(
      child: Container(
        height: 42,
        padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing10),
        decoration: BoxDecoration(
          color: context.templateColors.inputBackground,
          borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
        ),
        child: Row(
          children: [
            Text('对手价1', style: context.templateStyle.text.bodyTextMedium),
            Spacer(),
            ThemedImage(
              name: 'arrow_triangle_down',
              size: 14,
              followTheme: true,
            ),
          ],
        ),
      ),
    );
  }

  // 构建切换按钮
  Widget _buildSwitchButton() {
    return InkWellWidget(
      onTap:
          () => {
            setState(() {
              isCounterpartyPrice = !isCounterpartyPrice;
            }),
          },
      child: Container(
        height: 42,
        padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing14),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: context.templateColors.tabbarBackground,
          borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
          border: Border.all(
            width: 1,
            color:
                isCounterpartyPrice
                    ? context.templateColors.textPrimary
                    : Colors.transparent,
          ),
        ),
        child: Text('BBO', style: context.templateStyle.text.bodyTextMedium),
      ),
    );
  }
}
