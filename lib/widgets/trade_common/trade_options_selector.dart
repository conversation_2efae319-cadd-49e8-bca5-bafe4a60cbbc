/*
*  交易选项选择器
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/routes/index.dart';

class TradeOptionsSelector extends StatefulWidget {
  // 是否登录
  final bool isLoggedIn;

  const TradeOptionsSelector({super.key, this.isLoggedIn = false});

  @override
  State<TradeOptionsSelector> createState() => _TradeOptionsSelectorState();
}

// 跳转至登录
void _navigateToLogin() {
  NavigationService().navigateTo(
    AppRoutes.login,
    arguments: {'transitionType': RouteTransitionType.slideUp},
  );
}

class _TradeOptionsSelectorState extends State<TradeOptionsSelector> {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // 仓位模式
        _buildOptionItem(
          flex: 2,
          text: '全仓',
          onTap:
              () => {
                if (!widget.isLoggedIn) {_navigateToLogin()},
              },
        ),
        SizedBox(width: UiConstants.spacing8),

        // 杠杆倍数
        _buildOptionItem(
          flex: 2,
          text: '10x',
          onTap:
              () => {
                if (!widget.isLoggedIn) {_navigateToLogin()},
              },
        ),
        SizedBox(width: UiConstants.spacing8),

        // 交易方向
        _buildOptionItem(
          text: '单',
          onTap:
              () => {
                if (!widget.isLoggedIn) {_navigateToLogin()},
              },
        ),
      ],
    );
  }

  // 构建选择项
  Widget _buildOptionItem({
    required String text,
    int flex = 1,
    VoidCallback? onTap,
  }) {
    return Expanded(
      flex: flex,
      child: InkWellWidget(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: UiConstants.spacing8,
            vertical: UiConstants.spacing2,
          ),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: context.templateColors.tabbarBackground,
            borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
          ),
          child: Text(text, style: context.templateStyle.text.bodyText),
        ),
      ),
    );
  }
}
