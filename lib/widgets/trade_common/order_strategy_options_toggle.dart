/*
*  订单策略选项切换
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import './dialogs/order_strategy_dialog.dart';

class OrderStrategyOptionsToggle extends StatefulWidget {
  final List<OrderStrategyOption> options;
  final OrderStrategyOption? selectedOption;
  final ValueChanged<OrderStrategyOption> onChanged;

  const OrderStrategyOptionsToggle({
    super.key,
    required this.options,
    this.selectedOption,
    required this.onChanged,
  });

  @override
  State<OrderStrategyOptionsToggle> createState() =>
      _OrderStrategyOptionsToggleState();
}

class _OrderStrategyOptionsToggleState
    extends State<OrderStrategyOptionsToggle> {
  /// 处理弹窗点击
  Future<void> _handleTap() async {
    final result = await OrderStrategyDialog.show(
      context,
      options: widget.options,
      selectedOption: widget.selectedOption,
    );

    // 如果用户选择了选项且与当前选项不同，则触发回调
    if (result != null && result != widget.selectedOption) {
      widget.onChanged.call(result);
    }
  }

  @override
  Widget build(BuildContext context) {
    // 显示当前选中的选项，如果没有选中则显示第一项
    final displayText = widget.selectedOption?.text ?? widget.options[0].text;

    return InkWellWidget(
      onTap: _handleTap,
      child: Container(
        margin: EdgeInsets.only(top: UiConstants.spacing8),
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing10,
          vertical: UiConstants.spacing4,
        ),
        decoration: BoxDecoration(
          color: context.templateColors.tabbarBackground,
          borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
        ),
        child: Row(
          children: [
            Text(displayText, style: context.templateStyle.text.bodyTextMedium),
            Spacer(),
            ThemedImage(
              name: 'arrow_triangle_down',
              size: 14,
              followTheme: true,
            ),
          ],
        ),
      ),
    );
  }
}
