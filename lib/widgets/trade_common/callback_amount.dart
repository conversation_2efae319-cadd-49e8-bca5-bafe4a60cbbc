/*
*  回调幅度
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class CallbackAmount extends StatefulWidget {
  final ValueChanged<String>? onChanged;
  final TextEditingController? controller;

  const CallbackAmount({super.key, this.onChanged, this.controller});

  @override
  State<CallbackAmount> createState() => _CallbackAmountState();
}

class _CallbackAmountState extends State<CallbackAmount> {
  // 回调幅度选项
  final List<String> _callbackAmountOptions = ['1%', '5%', '10%'];

  // 输入框控制器
  late final TextEditingController _controller;

  // 当前选中的选项索引
  int? _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    // 使用传入的控制器或创建新的
    _controller = widget.controller ?? TextEditingController();
    // 默认选中第一个选项并设置到输入框
    if (_callbackAmountOptions.isNotEmpty && _controller.text.isEmpty) {
      _controller.text = _callbackAmountOptions[0];
    }
  }

  @override
  void dispose() {
    // 只有在自己创建的控制器时才释放
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Column(
        children: [
          // 输入框
          _buildCallbackAmountInput(),

          // 选项
          _buildCallbackAmountOptions(),
        ],
      ),
    );
  }

  // 构建回调幅度输入框
  Widget _buildCallbackAmountInput() {
    return TextFieldWidget(
      controller: _controller,
      height: 42,
      labelText: '回调幅度',
      cursorHeight: 12,
      contentPadding: EdgeInsets.only(
        left: UiConstants.spacing10,
        top: UiConstants.spacing4,
        bottom: UiConstants.spacing4,
      ),
      padding: EdgeInsets.zero,
      radius: BorderRadius.circular(UiConstants.borderRadius6),
      keyboardType: TextInputType.numberWithOptions(decimal: true),
      onChanged: (value) {
        widget.onChanged?.call(value);
      },
    );
  }

  // 构件回调幅度选项
  Widget _buildCallbackAmountOptions() {
    return Container(
      padding: EdgeInsets.only(
        top: UiConstants.spacing4,
        bottom: UiConstants.spacing8,
      ),
      child: Row(
        children:
            _callbackAmountOptions
                .asMap()
                .entries
                .map(
                  (entry) => Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(
                        right:
                            entry.key < _callbackAmountOptions.length - 1
                                ? UiConstants.spacing8
                                : 0,
                      ),
                      child: _buildItem(
                        key: entry.value,
                        index: entry.key,
                        selected: _selectedIndex == entry.key,
                      ),
                    ),
                  ),
                )
                .toList(),
      ),
    );
  }

  // 构建单项
  Widget _buildItem({
    required String key,
    required int index,
    bool selected = false,
  }) {
    return InkWellWidget(
      onTap: () {
        setState(() {
          _selectedIndex = index;
          _controller.text = key;
        });
        widget.onChanged?.call(key);
      },
      child: Container(
        height: 20,
        padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing8),
        decoration: BoxDecoration(
          color: context.templateColors.inputBackground,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            width: 1,
            color:
                selected
                    ? context.templateColors.textPrimary
                    : Colors.transparent,
          ),
        ),
        child: Text(
          key,
          style: context.templateStyle.text.hintTextMedium.copyWith(
            color: context.templateColors.textPrimary,
          ),
        ),
      ),
    );
  }
}
