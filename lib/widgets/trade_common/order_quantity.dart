/*
*  订单数量
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class OrderQuantity extends StatefulWidget {
  const OrderQuantity({super.key});

  @override
  State<OrderQuantity> createState() => _OrderQuantityState();
}

class _OrderQuantityState extends State<OrderQuantity> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: UiConstants.spacing8),
      child: TextFieldWidget(
        height: 42,
        labelText: '订单数量(2-50)',
        cursorHeight: 12,
        contentPadding: EdgeInsets.only(
          left: UiConstants.spacing10,
          top: UiConstants.spacing4,
          bottom: UiConstants.spacing4,
        ),
        padding: EdgeInsets.zero,
        radius: BorderRadius.circular(UiConstants.borderRadius6),
        keyboardType: TextInputType.numberWithOptions(decimal: true),
      ),
    );
  }
}
