/*
*  执行价格 
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';

class ExecutionPrice extends StatefulWidget {
  const ExecutionPrice({super.key});

  @override
  State<ExecutionPrice> createState() => _ExecutionPriceState();
}

class _ExecutionPriceState extends State<ExecutionPrice> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: UiConstants.spacing8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: TextFieldWidget(
              labelText: '执行价格(USDT)',
              height: 42,
              cursorHeight: 12,
              contentPadding: EdgeInsets.only(
                left: UiConstants.spacing10,
                top: UiConstants.spacing4,
                bottom: UiConstants.spacing4,
              ),
              padding: EdgeInsets.zero,
              radius: BorderRadius.circular(UiConstants.borderRadius6),
              keyboardType: TextInputType.numberWithOptions(decimal: true),
            ),
          ),
          SizedBox(width: UiConstants.spacing8),
          Expanded(
            child: InkWellWidget(
              child: Container(
                height: 42,
                padding: EdgeInsets.symmetric(
                  horizontal: UiConstants.spacing14,
                ),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: context.templateColors.tabbarBackground,
                  borderRadius: BorderRadius.circular(
                    UiConstants.borderRadius6,
                  ),
                  border: Border.all(width: 1, color: Colors.transparent),
                ),
                child: Text(
                  '市价',
                  style: context.templateStyle.text.bodyTextMedium,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
