/*
*  订单类型切换组件
*
*  功能特性：
*  - 支持多种订单类型选择
*  - 底部弹窗选择器
*  - 状态管理和回调
*  - 自定义样式支持
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:remixicon/remixicon.dart';
import 'dialogs/order_type_picker.dart';

/// 订单类型切换组件
class OrderTypeToggle extends StatefulWidget {
  /// 订单类型列表（必需参数）
  final List<OrderTypeOption> orderTypes;

  /// 当前选中的订单类型（由外部控制）
  final OrderTypeOption? selectedOption;

  /// 选择改变回调（必须实现以接收选中结果）
  final ValueChanged<OrderTypeOption> onChanged;

  /// 弹窗标题
  final String title;

  /// 是否显示信息图标
  final bool showInfoIcon;

  /// 自定义样式
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;

  const OrderTypeToggle({
    super.key,
    required this.orderTypes,
    this.selectedOption,
    required this.onChanged,
    this.title = '选择订单类型',
    this.showInfoIcon = true,
    this.padding,
    this.borderRadius,
  });

  @override
  State<OrderTypeToggle> createState() => _OrderTypeToggleState();
}

class _OrderTypeToggleState extends State<OrderTypeToggle> {
  /// 获取订单类型列表
  List<OrderTypeOption> get _orderTypes => widget.orderTypes;

  /// 获取当前选中的订单类型
  OrderTypeOption? get _selectedOrderType => widget.selectedOption;

  /// 显示订单类型选择器
  void _showOrderTypePicker() async {
    final result = await OrderTypePicker.show(
      context,
      options: _orderTypes,
      selectedOption: _selectedOrderType,
      title: widget.title,
    );

    if (result != null) {
      // 直接触发回调，让外部处理状态更新
      widget.onChanged.call(result);
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWellWidget(
      onTap: _showOrderTypePicker,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: UiConstants.spacing10,
          vertical: UiConstants.spacing4,
        ),
        decoration: BoxDecoration(
          color: context.templateColors.tabbarBackground,
          borderRadius: BorderRadius.circular(UiConstants.borderRadius6),
        ),
        child: Row(
          children: [
            InkWellWidget(
              child: Icon(
                RemixIcons.information_fill,
                size: UiConstants.iconSize14,
                color: context.templateColors.textTertiary,
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: UiConstants.spacing8),
              child: Text(
                _selectedOrderType?.text ?? widget.orderTypes.first.text,
                style: context.templateStyle.text.bodyTextMedium,
              ),
            ),
            Spacer(),
            ThemedImage(
              name: 'arrow_triangle_down',
              size: UiConstants.iconSize10,
              followTheme: true,
            ),
          ],
        ),
      ),
    );
  }
}
