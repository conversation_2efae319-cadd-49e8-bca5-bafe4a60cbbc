/*
*  触发价格
*/

import 'package:flutter/material.dart';
import 'package:qubic_exchange/core/index.dart';
import 'package:qubic_exchange/widgets/index.dart';
import 'package:qubic_exchange/widgets/trade_common/dialogs/price_source_selector.dart';

class TriggerPrice extends StatefulWidget {
  /// 是否为合约
  final bool isContract;

  /// 价格来源变更回调
  final ValueChanged<PriceSourceOption>? onPriceSourceChanged;

  /// 价格输入变更回调
  final ValueChanged<String>? onPriceChanged;

  /// 初始选中的价格来源索引
  final int? initialPriceSourceIndex;

  /// 输入框控制器
  final TextEditingController? controller;

  const TriggerPrice({
    super.key,
    this.isContract = true,
    this.onPriceSourceChanged,
    this.onPriceChanged,
    this.initialPriceSourceIndex,
    this.controller,
  });

  @override
  State<TriggerPrice> createState() => _TriggerPriceState();
}

class _TriggerPriceState extends State<TriggerPrice> {
  // 价格来源选项列表
  final List<PriceSourceOption> _priceSourceOptions = [
    PriceSourceOption(text: '市场', index: 0),
    PriceSourceOption(text: '标记', index: 1),
  ];

  // 当前选中的价格来源，默认选中第一项
  PriceSourceOption? _selectedPriceSource;

  @override
  void initState() {
    super.initState();
    // 根据传入的初始索引选择，否则默认选中第一项
    if (widget.initialPriceSourceIndex != null &&
        widget.initialPriceSourceIndex! < _priceSourceOptions.length) {
      _selectedPriceSource =
          _priceSourceOptions[widget.initialPriceSourceIndex!];
    } else {
      _selectedPriceSource = _priceSourceOptions.first;
    }
  }

  /// 显示价格来源选择器
  void _showPriceSourceSelector() async {
    final result = await PriceSourceSelector.show(
      context,
      options: _priceSourceOptions,
      selectedOption: _selectedPriceSource,
      title: '选择价格来源',
    );

    if (result != null) {
      setState(() {
        _selectedPriceSource = result;
      });

      // 回调处理
      _handlePriceSourceChanged(result);
    }
  }

  /// 处理价格来源变更回调
  void _handlePriceSourceChanged(PriceSourceOption option) {
    // 触发外部回调
    widget.onPriceSourceChanged?.call(option);

    // 根据索引处理不同的逻辑
    switch (option.index) {
      case 0:
        // 市场价格逻辑
        // 可以在这里添加特定的市场价格处理逻辑
        break;
      case 1:
        // 标记价格逻辑
        // 可以在这里添加特定的标记价格处理逻辑
        break;
      default:
        break;
    }
  }

  /// 获取当前显示的文本
  String get _displayText {
    return _selectedPriceSource?.text ?? '市场';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: UiConstants.spacing8),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: TextFieldWidget(
              controller: widget.controller,
              labelText: '触发价格',
              height: 42,
              cursorHeight: 12,
              contentPadding: EdgeInsets.only(
                left: UiConstants.spacing10,
                top: UiConstants.spacing4,
                bottom: UiConstants.spacing4,
              ),
              padding: EdgeInsets.zero,
              radius: BorderRadius.circular(UiConstants.borderRadius6),
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              onChanged: widget.onPriceChanged,
              suffixIcon: Padding(
                padding: EdgeInsets.only(right: UiConstants.spacing10),
                child: InkWellWidget(
                  onTap: widget.isContract ? _showPriceSourceSelector : null,
                  child:
                      widget.isContract
                          ? Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Text(
                                _displayText,
                                style:
                                    context.templateStyle.text.bodyTextMedium,
                              ),
                              SizedBox(width: UiConstants.spacing4),
                              ThemedImage(
                                name: 'arrow_triangle_down',
                                size: 14,
                                followTheme: true,
                              ),
                            ],
                          )
                          : Text(
                            'USDT',
                            style:
                                context
                                    .templateStyle
                                    .text
                                    .descriptionTextMedium,
                          ),
                ),
              ),
              suffixIconConstraints: BoxConstraints(maxWidth: 70),
            ),
          ),
        ],
      ),
    );
  }
}
