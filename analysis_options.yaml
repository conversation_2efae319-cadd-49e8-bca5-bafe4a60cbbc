# Dart代码静态分析配置文件
# 用于检查代码错误、警告和代码规范问题
# 分析结果会在支持Dart的IDE中显示，也可通过命令行运行 `flutter analyze` 查看

# 引入Flutter推荐的代码规范规则集
include: package:flutter_lints/flutter.yaml

# 代码检查规则配置
linter:
  # 可以在下面的rules部分自定义规则
  # 禁用某些规则或启用额外的规则
  # 完整的规则列表和文档：https://dart.dev/lints
  #
  # 也可以在特定代码行或文件中使用以下语法忽略规则：
  # // ignore: rule_name (忽略单行)
  # // ignore_for_file: rule_name (忽略整个文件)
  rules:
    # avoid_print: false  # 取消注释以禁用avoid_print规则
    # prefer_single_quotes: true  # 取消注释以启用prefer_single_quotes规则
