<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>192826299005-2s6roej8ihor4c0qn423igcaidf8dgb4.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.192826299005-2s6roej8ihor4c0qn423igcaidf8dgb4</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>192826299005-6ghnlh85oh4omtuc21jnlgaij5rs9lmq.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyA__X349QBuPedG62515qfmT-C8pSP4CPM</string>
	<key>GCM_SENDER_ID</key>
	<string>192826299005</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>org.algoquant.cpxios</string>
	<key>PROJECT_ID</key>
	<string>mflh-app</string>
	<key>STORAGE_BUCKET</key>
	<string>mflh-app.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:192826299005:ios:a4712bed7810dd38221714</string>
</dict>
</plist>